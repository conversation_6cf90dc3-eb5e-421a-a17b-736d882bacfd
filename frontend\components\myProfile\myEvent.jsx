import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import { CiLocationOn } from "react-icons/ci";
import { LuCalendarDays } from "react-icons/lu";
import { getMyEventApi } from "@/services/webflowServices";
import { format } from "date-fns";
import dynamic from "next/dynamic";

const WriteReview = dynamic(() => import("../review/writeReview"), {
  ssr: false,
});

const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});
const Bookingdetailspopup = dynamic(
  () => import("../model/bookingdetailspopup"),
  { ssr: false }
);
const Cancelpopup = dynamic(() => import("../model/cancelpopup"), {
  ssr: false,
});

const MyEvent = () => {
  const isFirstRender = useRef(null);
  const [eventData, setEventData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openReview, setOpenReview] = useState(false);
  const [openBookingdetailspopup, setopenBookingdetailspopup] = useState(false);
  const handleCloseBookingdetailspopup = () =>
    setopenBookingdetailspopup(false);
  const [openCancelpopup, setopenCancelpopup] = useState(false);
  const handleCloseCancelpopup = () => setopenCancelpopup(false);

  useEffect(() => {
    const fetchMyEventData = async () => {
      setLoading(true);
      try {
        const response = await getMyEventApi();

        setEventData(response?.data?.data || []);
      } catch (error) {
        console.error("Error fetching stay data:", error);
      } finally {
        setLoading(false);
      }
    };
    if (!isFirstRender.current) {
      fetchMyEventData();
    } else {
      isFirstRender.current = false;
    }
  }, []);

  const handleOpen = () => {
    setOpenReview(true);
  };

  const handleClose = () => {
    setOpenReview(false);
  };

  return (
    <>
      <Loader open={loading} />
      <h2 className='text-[#40E0D0] flex gap-1 text-2xl font-bold mb-6'>
        My
        <span className='text-black ml-0.5'> Event</span>
      </h2>

      <div className='flex flex-col gap-5'>
        {eventData.length > 0
          ? eventData.map((booking, index) => (
              <div
                key={index}
                className='flex items-center border rounded-3xl relative overflow-hidden'
              >
                <Image
                  src={booking?.event?.attachment?.[0]?.url}
                  width={317}
                  height={204}
                  alt='event'
                  className='object-cover'
                  loading='lazy'
                />

                <div className='py-2 px-4'>
                  <h2 className='text-xl font-bold mb-3'>
                    {booking?.event?.title}
                  </h2>
                  <p className='flex items-center gap-1 text-sm mb-2'>
                    <CiLocationOn className='text-[#40E0D0]' />
                    {booking?.event?.address}
                  </p>
                  <p className='flex items-center gap-1 text-sm mb-5'>
                    <LuCalendarDays className='text-[#40E0D0]' />
                    {format(new Date(booking?.event?.startDate), "dd MMM yyyy")}
                  </p>
                </div>
                <div className='ml-auto pr-1 grid gap-1'>
                  {/* <button
                    className="bg-white text-primary-blue text-sm font-bold text-center"
                    onClick={handleOpenBookingdetailspopup}
                  >
                    View Booking
                  </button>
                  <button
                    className="bg-red-600 text-white px-4 py-2 rounded-full text-sm"
                    onClick={handleOpenCancelpopup}
                  >
                    Cancel
                  </button> */}
                  <button
                    className='bg-black text-white px-4 py-2 rounded-full text-sm'
                    onClick={() => handleOpen(booking?.event?._id)}
                  >
                    Leave Review
                  </button>
                </div>

                <WriteReview
                  open={openReview}
                  close={handleClose}
                  id={booking?.event?._id}
                  isEventReview
                />
              </div>
            ))
          : !loading && <p>No events available.</p>}
      </div>
      <Bookingdetailspopup
        openBookingdetailspopup={openBookingdetailspopup}
        handleCloseBookingdetailspopup={handleCloseBookingdetailspopup}
      />
      <Cancelpopup
        openCancelpopup={openCancelpopup}
        handleCloseCancelpopup={handleCloseCancelpopup}
      />
    </>
  );
};

export default MyEvent;
