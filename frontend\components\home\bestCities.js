import React, { useState, useEffect } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/autoplay';
import { getCityListApi } from '@/services/webflowServices';
import Loader from '../loader/loader';

const BestCitiesSlider = ({country, onCitySelect}) => {
  const [cities, setCities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCity, setSelectedCity] = useState(null);

  useEffect(() => {
    const fetchCities = async () => {
      setLoading(true)
      try {
        const response = await getCityListApi(country);
        if (response.data.success) {
          setCities(response.data.data);
          // Set the first city as selected by default
          if (response.data.data.length > 0) {
            setSelectedCity(response.data.data[0]);
            onCitySelect(response.data.data[0]);
          }
        }
      } catch (error) {
        console.error('Error fetching cities:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCities();
  }, [country]);

  const handleCityClick = (city) => {
    setSelectedCity(city);
    onCitySelect(city);
  };

  return (
    <>
      <Loader open={loading} />
      <div className="container mt-3 px-4 lg:px-14">
        <h2 className="text-white text-sm font-manrope font-bold mb-1 ">
          {country}&apos;s Must-Visit <span className="text-primary-blue">Gems</span>
        </h2>
        <Swiper
          spaceBetween={10}
          slidesPerView="auto"
          autoplay={{ delay: 2000, disableOnInteraction: false }}
          loop
          modules={[Autoplay]}
          className="w-full"
        >
          {cities.map((city, index) => (
            <SwiperSlide key={index} style={{ width: 'auto' }}>
              <button
                onClick={() => handleCityClick(city)}
                className={`min-w-[110px] px-3 h-[40px] text-sm font-manrope font-semibold rounded-full whitespace-nowrap ${
                  city === selectedCity ? 'text-primary-blue border border-primary-blue' : 'text-white border border-white hover:text-primary-blue hover:border hover:border-primary-blue'
                }`}
              >
                {city}
              </button>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </>
  );
};

export default BestCitiesSlider;
