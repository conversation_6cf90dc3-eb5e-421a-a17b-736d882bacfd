import React, { useState, Fragment } from "react";
import dynamic from "next/dynamic";
import { Dialog, Transition } from "@headlessui/react";
import AddRate from "@/components/ownerFlow/dashboard/addRate";
import EditRate from "@/components/ownerFlow/dashboard/editRate";
// import Link from "next/link";
// import StopSell from "@/components/ownerFlow/dashboard/stopSell";
const Calendars = dynamic(
  () => import("@/components/ownerFlow/dashboard/calendar"),
  {
    ssr: false,
  }
);

const Calendar = () => {
  const [addrateOpen, setAddrateOpen] = useState(false);
  const [iseditOpen, setIseditOpen] = useState(false);
 
  // const openaddModal = () => setAddrateOpen(true);
  const closeaddModal = () => setAddrateOpen(false);
  // const openeditModal = () => setIseditOpen(true);
  const closeeditModal = () => setIseditOpen(false);


  return (
    <>
      <section className="w-full ">
        <div className="flex items-center justify-between">
          <h2 className="page-title">Calendar</h2>
          <div className="flex items-center gap-x-3.5">
            <button  className="flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-gray-300 font-medium cursor-not-allowed"
            
          >
              Stop Sell
            </button>
            <button
              className="flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-gray-300 font-medium cursor-not-allowed"
              // onClick={openaddModal}
            >
              Add Rate
            </button>
            <button
              className="flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-gray-300 font-medium cursor-not-allowed"
              // onClick={openeditModal}
            >
              Edit Rate
            </button>
          </div>
        </div>
        <Calendars />
      </section>

      <Transition show={addrateOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeaddModal}>
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="lg:w-[42.5%] md:w-[60%] w-[95%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <h2 className="page-title">Add Rate</h2>
                    <button
                      onClick={closeaddModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <AddRate closeaddModal={closeaddModal} />
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      <Transition show={iseditOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeeditModal}>
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="lg:w-[42.5%] md:w-[60%] w-[95%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <h2 className="page-title">Edit Rate</h2>
                    <button
                      onClick={closeeditModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <EditRate closeeditModal={closeeditModal} />
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

     
    </>
  );
};

export default Calendar;
