import React, { useEffect, useRef, useState, Fragment } from "react";
import Image from "next/image";
import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/react";
import { Trash, MoreVertical, Pencil, Eye } from "lucide-react";
import {
  DeletePaymentApi,
  PaymentAccountDataApi,
  PaymentListApi,
} from "@/services/ownerflowServices";
import { getItemLocalStorage } from "@/utils/browserSetting";
import dynamic from "next/dynamic";
import Addpayment from "@/components/ownerFlow/dashboard/addpayment";
import toast from "react-hot-toast";
import Editpayment from "@/components/ownerFlow/dashboard/editPayment";
import Viewpayment from "@/components/ownerFlow/dashboard/viewPayment";
import countries from "world-countries";
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
  Transition,
} from "@headlessui/react";
import Head from "next/head";
import Pagination from "@/components/common/commonPagination";

const Filter = dynamic(() => import("../../../components/model/filter"), {
  ssr: false,
});

const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});
const headers = [
  "Reservation ID",
  "Name",
  "Date",
  "Type",
  "Amount Paid",
  "Status",
  "Actions",
];

const Payment = () => {
  // Filter Modal
  const [isModalOpen, setIsModalOpen] = useState(false);
  const closefilterModal = () => setIsModalOpen(false);
  const [paymentAccountData, setPaymentAccountData] = useState([]);
  const [paymentList, setPaymentList] = useState([]);

  const [loading, setLoading] = useState(false);
  const [openAddBooking, setOpenAddBooking] = useState(false);
  // const openbookingModal = () => setOpenAddBooking(true);
  const closebookingModal = () => setOpenAddBooking(false);
  const [openEditBooking, setOpenEditBooking] = useState(false);
  const closeeditbookingModal = () => setOpenEditBooking(false);
  const [currencyData, setCurrencyData] = useState({});
  const [editId, setEditId] = useState(null);
  const [openViewBooking, setOpenViewBooking] = useState(false);
  const closeviewbookingModal = () => setOpenViewBooking(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalData, setTotalData] = useState();

  const isFirstRender = useRef(null);
  const storedId = getItemLocalStorage("hopid");

  const fetchPaymentsAccount = async (id) => {
    setLoading(true);
    try {
      const response = await PaymentAccountDataApi(id);
      setPaymentAccountData(response?.data?.data);
    } catch (error) {
      console.error("Error fetching payment data:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchPaymentsList = async (id) => {
    setLoading(true);
    try {
      const response = await PaymentListApi(id, currentPage, itemsPerPage);
      setPaymentList(response?.data?.data?.payments);
      setTotalPages(response?.data?.data?.pages || 1);
      setTotalData(response?.data?.data?.total);
    } catch (error) {
      console.error("Error fetching payment data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!isFirstRender.current) {
      fetchPaymentsAccount(storedId);
      fetchPaymentsList(storedId);
    } else {
      isFirstRender.current = false;
    }
  }, [storedId, currentPage, itemsPerPage]);

  const getStatusClassName = (status, isDeleted) => {
    if (isDeleted) {
      return "bg-red-100 text-red-500";
    }

    switch (status) {
      case "pending":
        return "bg-[#F9A63A33] text-[#F9A63A]";
      case "Paid":
        return "bg-[#41C58833] text-[#41C588]";
      case "fail":
        return "bg-[#F369601A] text-[#F36960]";
      default:
        return "";
    }
  };

  const updatePaymentList = async () => {
    await fetchPaymentsAccount(storedId);
    await fetchPaymentsList(storedId);
  };

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);
      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);

  const getCurrencySymbol = (currencyCode) => {
    return currencyData[currencyCode] || currencyCode;
  };

  const handleDelete = async (id) => {
    setLoading(true);
    try {
      const response = await DeletePaymentApi(id);
      if (response?.data?.status) {
        toast.success(response?.data?.message);
        await fetchPaymentsAccount(storedId);
        await fetchPaymentsList(storedId);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  return (
    <div>
      <Head>
        <title>Payment Overview | Mixdorm</title>
      </Head>
      <Loader open={loading} />
      {loading ? (
        <section className="w-full">
          <div className="flex justify-between items-center mb-4">
            <h1 className="page-title h-8 w-48 bg-gray-200 rounded animate-pulse"></h1>
            <div className="flex sm:gap-2 gap-1">
              <button className="sm:flex block items-center text-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs rounded-lg w-24 h-10 bg-gray-200 animate-pulse"></button>
              <button className="flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs rounded-lg w-24 h-10 bg-gray-200 animate-pulse"></button>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-4xl">
            <div className="grid lg:grid-cols-[repeat(3,_1fr)_1fr] md:grid-cols-3 sm:grid-cols-3 grid-cols-1 gap-2.5 mb-4">
              {[1, 2, 3].map((item) => (
                <div
                  key={item}
                  className="bg-gray-200 rounded-lg px-4 py-6 flex items-center animate-pulse"
                >
                  <div className="w-full">
                    <h4 className="h-5 w-32 bg-gray-300 rounded mb-5 sm:mb-6"></h4>
                    <h5 className="h-7 w-24 bg-gray-300 rounded"></h5>
                  </div>
                  <div className="ml-auto w-10 h-10 bg-gray-300 rounded-full"></div>
                </div>
              ))}
            </div>

            <div className="w-full mt-5 text-nowrap overflow-x-auto rounded-lg">
              <table className="w-full border border-gray-340 rounded-lg font-inter">
                <thead className="rounded-t-lg">
                  <tr className="rounded-t-lg">
                    {[1, 2, 3, 4, 5, 6, 7].map((header) => (
                      <th
                        key={header}
                        className="first:pl-6 py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-left first:rounded-tl-lg last:rounded-tr-lg"
                      >
                        <div className="h-6 w-20 bg-gray-300 rounded animate-pulse"></div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {[1, 2, 3, 4].map((row) => (
                    <tr key={row} className="border-b">
                      {[1, 2, 3, 4, 5, 6, 7].map((cell) => (
                        <td
                          key={cell}
                          className="text-xs font-medium pl-6 py-3.5 px-4 border-b"
                        >
                          <div className="h-5 w-full bg-gray-200 rounded animate-pulse"></div>
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </section>
      ) : (
        <section className="w-full">
          <div className="flex justify-between items-center mb-4">
            <h1 className="page-title">Payment Management</h1>
            <div className="flex sm:gap-2 gap-1">
              <button
                className="sm:flex block items-center text-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-gray-300 cursor-not-allowed font-medium"
                // onClick={() => setIsModalOpen(true)}
              >
                <Image
                  className="sm:mr-2 mx-auto"
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/filter.svg`}
                  width={20}
                  height={20}
                />
                Filter
              </button>
              <button
                className="flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-gray-300 cursor-not-allowed  font-medium"
                // onClick={openbookingModal}
              >
                Add Payment
              </button>
              {/* <button
              className='flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-primary-blue font-medium' onClick={openeditbookingModal} >
              Edit Payment
            </button> */}
            </div>
          </div>
          {/* {!openAddBooking && !editOpen ? ( */}
          <div className="bg-white rounded-lg shadow-4xl">
            <div className="grid lg:grid-cols-[repeat(3,_1fr)_1fr] md:grid-cols-3 sm:grid-cols-3 grid-cols-1 gap-2.5 mb-4">
              <div className="bg-[#43C666] rounded-lg px-4 py-5 flex items-center">
                <div>
                  <h4 className="text-white sm:text-xl text-lg mb-5 sm:mb-6">
                    Total Revenue
                  </h4>
                  <h5 className="text-xl sm:text-[26px] font-semibold text-white">
                    {paymentAccountData?.totalRevenue}
                  </h5>
                </div>
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/moneyrevenue.svg`}
                  width={40}
                  height={40}
                  alt="BgImg"
                  className="ml-auto"
                  loading="lazy"
                />
              </div>
              <div className="bg-[#FF72AD] rounded-lg px-4 py-5 flex items-center">
                <div>
                  <h4 className="text-white sm:text-xl text-lg mb-5 sm:mb-6">
                    Total Received
                  </h4>
                  <h5 className="text-xl sm:text-[26px] font-semibold text-white">
                    {paymentAccountData?.totalReceived}
                  </h5>
                </div>
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/receivedMoney.svg`}
                  width={40}
                  height={40}
                  alt="BgImg"
                  className="ml-auto"
                  loading="lazy"
                />
              </div>
              <div className="bg-[#D092F5] rounded-lg px-4 py-5 flex items-center">
                <div>
                  <h4 className="text-white sm:text-xl text-lg mb-5 sm:mb-6">
                    Pending Payments
                  </h4>
                  <h5 className="text-xl sm:text-[26px] font-semibold text-white">
                    {paymentAccountData?.totalPending}
                  </h5>
                </div>
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/pendingPayment.svg`}
                  width={40}
                  height={40}
                  alt="BgImg"
                  className="ml-auto"
                  loading="lazy"
                />
              </div>
            </div>
            {/* <div className='flex flex-wrap items-center justify-between mb-4 gap-y-2'>
              <h3 className='text-lg font-medium'>Payment History</h3>
              <button className='flex items-center px-4 py-2 text-sm text-white bg-blue-500 rounded-lg'>
                Daily <ExpandMoreIcon className='ml-1' />
              </button>
            </div> */}
            <div className="w-full mt-5 text-nowrap overflow-x-auto rounded-lg">
              <table className="w-full border border-gray-340 rounded-lg font-inter">
                <thead className="rounded-t-lg">
                  <tr className="rounded-t-lg">
                    {headers.map((header, index) => (
                      <th
                        key={index}
                        className="first:pl-6 py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left first:rounded-tl-lg last:rounded-tr-lg"
                      >
                        {header}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {paymentList?.map((item, index) => (
                    <tr
                      className={`border-b ${
                        item.isDeleted ? "bg-red-200" : "" // Add red background if isDeleted is true
                      }`}
                      key={index}
                    >
                      <td
                        className="text-xs font-medium pl-6 py-3.5 px-4 border-b text-black"
                        title={item._id}
                      >
                        {item._id}
                      </td>
                      <td
                        className="text-xs font-medium py-3.5 px-4 border-b text-black"
                        title={item.gusetUser}
                      >
                        {item.gusetUser}
                      </td>
                      <td
                        className="text-xs font-medium py-3.5 px-4 border-b text-black"
                        title={item.paymentDate}
                      >
                        {`${new Date(item?.paymentDate || item?.createdAt)
                          .getDate()
                          .toString()
                          .padStart(2, "0")}-${(
                          new Date(
                            item.paymentDate || item?.createdAt
                          ).getMonth() + 1
                        )
                          .toString()
                          .padStart(2, "0")}-${new Date(
                          item.paymentDate || item?.createdAt
                        ).getFullYear()}`}{" "}
                        {/* -{" "}
                        {`${new Date(item.checkOut)
                          .getDate()
                          .toString()
                          .padStart(2, "0")}-${(
                          new Date(item.checkOut).getMonth() + 1
                        )
                          .toString()
                          .padStart(2, "0")}-${new Date(
                          item.checkOut
                        ).getFullYear()}`} */}
                      </td>
                      <td
                        className="text-xs font-medium py-3.5 px-4 border-b text-black"
                        title={item.paymentType}
                      >
                        {item.paymentType}
                      </td>
                      <td
                        className="text-xs font-medium py-3.5 px-4 border-b text-black"
                        title={item.amount}
                      >
                        {getCurrencySymbol(item?.currency)} {item.amount}
                      </td>
                      <td
                        className="text-xs font-medium py-3.5 px-4 border-b text-black"
                        title={item.status}
                      >
                        <span
                          className={`inline-block px-2 py-1 text-xs whitespace-nowrap ${getStatusClassName(
                            item.status,
                            item.isDeleted
                          )} rounded-full`}
                        >
                          {item.isDeleted ? "Deleted" : item.status}
                        </span>
                      </td>
                      <td className="p-4" title="Actions">
                        <Menu
                          as="div"
                          className="relative inline-block text-left"
                        >
                          <div>
                            <MenuButton>
                              <MoreVertical
                                aria-hidden="true"
                                size={16}
                              ></MoreVertical>
                            </MenuButton>
                          </div>

                          <MenuItems
                            transition
                            // ref={(el) => {
                            //   if (el) {
                            //     const rect = el.getBoundingClientRect();
                            //     const windowHeight = window.innerHeight;
                            //     if (rect.bottom > windowHeight) {
                            //       el.classList.add("bottom-full", "mb-2"); // Open upwards
                            //     } else {
                            //       el.classList.remove("bottom-full", "mb-2"); // Default downwards
                            //     }
                            //   }
                            // }}
                            ref={(el) => {
                              if (el) {
                                const rect = el.getBoundingClientRect();
                                const windowHeight = window.innerHeight;
                                const isLastItem =
                                  index === paymentList.length - 1;
                                const isSecondLastItem =
                                  index === paymentList.length - 2;
                                const isOnlyItem = paymentList.length === 1;
                                const hasMoreThanTwoItems =
                                  paymentList.length > 2;

                                // Clear previous classes
                                el.classList.remove(
                                  "bottom-full",
                                  "mb-2",
                                  "mt-2",
                                  "top-1/2",
                                  "-translate-y-1/2"
                                );

                                if (isOnlyItem) {
                                  el.classList.add(
                                    "top-1/2",
                                    "-translate-y-1/2"
                                  );
                                } else if (
                                  isLastItem ||
                                  (hasMoreThanTwoItems && isSecondLastItem) ||
                                  rect.bottom > windowHeight
                                ) {
                                  el.classList.add("bottom-full", "mb-2");
                                } else {
                                  el.classList.add("mt-2");
                                }
                              }
                            }}
                            className="absolute right-0 z-10 mt-2 w-max origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
                          >
                            <div>
                              <MenuItem>
                                <button
                                  href="#"
                                  className="px-4 py-2 text-sm w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-t-md"
                                  onClick={() => {
                                    setOpenViewBooking(true);

                                    setEditId(item?._id);
                                  }}
                                >
                                  <Eye size={16}></Eye>
                                  View
                                </button>
                              </MenuItem>
                              {!item.isDeleted &&
                                item.paymentType === "Cash" && (
                                  <MenuItem>
                                    <button
                                      href="#"
                                      className="px-4 py-2 text-sm w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-t-md"
                                      onClick={() => {
                                        setOpenEditBooking(true);

                                        setEditId(item?._id);
                                      }}
                                    >
                                      <Pencil size={16}></Pencil>
                                      Edit
                                    </button>
                                  </MenuItem>
                                )}
                              {item.paymentType === "Cash" &&
                                !item.isDeleted && (
                                  <MenuItem>
                                    <button
                                      onClick={() => {
                                        handleDelete(item?._id);
                                      }}
                                      className="px-4 py-2 text-sm text-red-600 w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-b-md"
                                    >
                                      <Trash
                                        size={16}
                                        className="text-red-600"
                                      />{" "}
                                      <span>Delete</span>
                                    </button>
                                  </MenuItem>
                                )}
                            </div>
                          </MenuItems>
                        </Menu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          {/* ) : (
          editOpen && (
            <Editpayment
              setOpenAddBooking={setEditOpen}
              updatePaymentList={updatePaymentList}
              editId={editId}
            />
          )
        )} */}
        </section>
      )}

      {isModalOpen && (
        <Dialog
          open={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          className="relative z-50"
        >
          <DialogBackdrop
            transition
            className="fixed inset-0 bg-[#000000B2] transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
          />

          <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
            <div className="flex min-h-full justify-center p-4 text-center items-center sm:p-0">
              <DialogPanel
                transition
                className="relative transform overflow-hidden rounded-2xl bg-white text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-full sm:max-w-[490px] max-w-full w-full data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95"
              >
                <div className="bg-white sm:px-7 sm:pb-7 pt-3 p-3 pb-3">
                  <DialogTitle>
                    <h3 className="text-center text-black font-bold sm:text-lg text-sm">
                      Filter
                    </h3>
                  </DialogTitle>
                  <Filter closefilterModal={closefilterModal} />
                </div>
              </DialogPanel>
            </div>
          </div>
        </Dialog>
      )}

      <Transition show={openAddBooking} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closebookingModal}>
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <h2 className="page-title">Add Payment</h2>
                    <button
                      onClick={closebookingModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <Addpayment
                      closebookingModal={closebookingModal}
                      updatePaymentList={updatePaymentList}
                    ></Addpayment>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
      <Transition show={openEditBooking} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-50"
          onClose={closeeditbookingModal}
        >
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm">
                    <h2 className="page-title">Edit Payment</h2>
                    <button
                      onClick={closeeditbookingModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <Editpayment
                      closeeditbookingModal={closeeditbookingModal}
                      updatePaymentList={updatePaymentList}
                      editId={editId}
                    ></Editpayment>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
      <Transition show={openViewBooking} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-50"
          onClose={closeviewbookingModal}
        >
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm">
                    <h2 className="page-title">View Payment</h2>
                    <button
                      onClick={closeviewbookingModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <Viewpayment
                      updatePaymentList={updatePaymentList}
                      editId={editId}
                    ></Viewpayment>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
      {paymentList?.length > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalData || 0}
          itemsPerPage={itemsPerPage}
          onPageChange={handlePageChange}
          onItemsPerPageChange={handleItemsPerPageChange}
        />
      )}
    </div>
  );
};

export default Payment;
