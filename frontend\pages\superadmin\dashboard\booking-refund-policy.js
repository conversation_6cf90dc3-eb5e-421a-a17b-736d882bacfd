/* eslint-disable react/no-unescaped-entities */
 
"use client";
import { Plus } from "lucide-react";
import Link from "next/link";
import { FaRegTrashCan } from "react-icons/fa6";
import { FiEye } from "react-icons/fi";
import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight,
} from "react-icons/md";
import { TfiPencilAlt } from "react-icons/tfi";






const BookingRefundPolicy = () => {

  const refundData = [
    {
      id: 1,
      name: (
        <div className="">
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            General terms
          </span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            and conditions
          </span>
        </div>
      ),
      feedback: (
        <div>
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • All guests must be at least 18 years of age to check-in at
            MixDorm.{" "}
          </p>
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • Valid photo identification is mandatory at the time of check-in.
            Acceptable IDs <br/> include Passport, Aadhar Card, Driving License, and
            Voter ID card. PAN Card is not <br/> accepted. Guests with local IDs will
            not be admitted.{" "}
          </p>{" "}
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • MixDorm is designed for solo travelers and young backpackers;
            families are <br/> encouraged to consider more family-oriented
            accommodations.{" "}
          </p>{" "}
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • Advance payments made at the time of reservation are
            non-refundable.{" "}
          </p>{" "}
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • Full prepayment is required at the time of check-in.{" "}
          </p>{" "}
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • Non-resident guests are not allowed inside the rooms; they can be
            met in the <br/> common areas.{" "}
          </p>{" "}
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • For groups of 3 or 4, accommodation in the same dorm room is not
            guaranteed.{" "}
          </p>{" "}
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • Different destinations and properties may have specific policies
            during certain times <br/> of the year.{" "}
          </p>{" "}
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • Guests are responsible for their personal belongings.{" "}
          </p>{" "}
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • Outside pets are not allowed.{" "}
          </p>{" "}
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • Breakfast is not included in the room rate and is charged
            separately at <br/> the property.{" "}
          </p>
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • The hostel reserves the right to take appropriate action against
            any guest whose <br/> behavior is deemed inappropriate.{" "}
          </p>{" "}
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • Right to admission is reserved.{" "}
          </p>{" "}
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • Certain policies are specific to individual bookings and will be
            notified at the time of <br/> booking.{" "}
          </p>{" "}
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • Policies apply unless otherwise specified on the individual
            destination's booking <br/> page.{" "}
          </p>
        </div>
      ),
    },
    {
      id: 2,
      name: (
        <div className="">
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Booking</span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">extantion</span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">policy</span>
        </div>
      ),
      feedback: (
        <div>
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • Extensions of stay are subject to current room rates and
            availability.
          </p>

          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • Current room rates may differ from the rates at which the rooms
            were originally <br/> booked.
          </p>
        </div>
      ),
    },
    {
      id: 3,
      name: (
        <div>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            Cancellation
          </span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Policy</span>
        </div>
      ),
      feedback: (
        <div>
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • Advance payments made at the time of reservation are
            non-refundable.
          </p>

          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • Full prepayment of the remaining amount is required at check-in.
          </p>

          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • Policies apply unless otherwise specified on the individual
            destination's booking <br/> page.
          </p>
        </div>
      ),
    },
  ];

  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <div className="flex items-center justify-between w-full">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Booking Refund Policy
        </h2>
        <div className="w-[50%] gap-x-5 flex justify-end items-center">
          <Link
            href={"/superadmin/dashboard/booking-refund-policy-add"}
            className="px-4 py-2 text-sm font-medium font-poppins text-white rounded flex items-center bg-sky-blue-650"
          >
            <Plus size={18} className="mr-1" /> Booking Refund Policy
          </Link>
        </div>
      </div>

      <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border dark:border-none">
        <table className="w-full divide-y bg-white rounded-xl divide-gray-200 dark:bg-black">
          <thead>
            <tr className="w-full items-center justify-between">
              <th className="pr-8 py-6 bg-white text-center text-sm font-semibold font-poppins text-black uppercase dark:bg-black dark:text-[#B6B6B6]">
                TITLE TEXT
              </th>
              <th className="py-6 pr-10 md:pr-8 lg:pr-10 bg-white text-sm font-semibold text-center font-poppins text-black uppercase dark:bg-black dark:text-[#B6B6B6]">
                SUB TEXT
              </th>
              <th className="py-6  bg-white text-center text-sm font-poppins font-semibold text-black uppercase dark:bg-black dark:text-[#B6B6B6]">
                ACTION
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 border-x border-y dark:border-x-0 text-black/70 dark:text-[#757575]">
            {refundData.map((refundItem) => (
              <tr key={refundItem.id} className="w-full">
                <td className="whitespace-nowrap text-gray-500 text-sm font-medium font-poppins px-6 py-8 dark:text-[#757575]">
                  {refundItem.name}
                </td>
                <td className="pl-6 lg:pl-32 px-5 text-sm text-gray-500 font-medium font-poppins py-4 dark:text-[#757575]">
                  {refundItem.feedback}
                </td>
                <td className="py-6 md:py-6 lg:py-10 flex justify-end px-6">
                  <Link
                    href={"/superadmin/dashboard/booking-refund-policy-details"}
                    className="border p-2 rounded-l-lg text-black/75 hover:text-blue-700 dark:text-[#757575] dark:hover:text-blue-700"
                  >
                    <FiEye />
                  </Link>
                  <Link
                    href={"/superadmin/dashboard/booking-refund-policy-edit"}
                    className="border p-2 text-black/75 hover:text-yellow-400 dark:text-[#757575] dark:hover:text-yellow-400"
                  >
                    <TfiPencilAlt />
                  </Link>
                  <button className="p-2 border rounded-r-lg text-red-600">
                    <FaRegTrashCan />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
 <div className="flex justify-between items-center mt-5">
              <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
              <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
                <a
                  href="#"
                  className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                >
                  <span className="sr-only">Next Page</span>
                  <MdOutlineKeyboardArrowLeft />
                </a>
      
                <a
                  href="#"
                  className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                >
                  <span className="sr-only">Next Page</span>
                  <MdOutlineKeyboardArrowRight />
                </a>
              </div>
            </div>
    </div>
  );
};

export default BookingRefundPolicy;
