import Response from "../utills/response.js";
import {
    getData, getUserDataByEmail, addUserData, updateUserDataByEmail, updateUserDataById,
    getUserData, changePasswordService, updateLastLogin,
    getAllAdminAndSubAdmin, deleteUserById, getUserDataByIdService,
    getAllUsers,
    getUserDataByEmailOrOtaId
} from "../services/auth.js";
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import generator from 'generate-password'
import { v4 as uuidv4 } from 'uuid';
import sendEmail from "../commons/mail.js";
import userModel from "../models/auth.js";
import axios from 'axios';
import { cloudBedsBookingSend } from "../utills/chanelManager.js";
import hostelLoginModel from "../models/hostelsLogin.js";

// Function to send WhatsApp message via Meta API
async function sendWhatsAppOTP(recipientNumber, otpCode) {
    const token = '478816847864259|wyXHu1aKJStkgT79CNpPqWUEcmw'; // Replace with your permanent access token
    const phoneNumberID = 'YOUR_PHONE_NUMBER_ID'; // Replace with the ID of your connected WhatsApp phone number
    const apiURL = `https://graph.facebook.com/v16.0/${phoneNumberID}/messages`;

    // Prepare the message body
    const messageData = {
        messaging_product: 'whatsapp',
        to: recipientNumber,
        type: 'template',
        template: {
            name: 'your_template_name', // Replace with your template name created in WhatsApp Manager
            language: {
                code: 'en_US'
            },
            components: [{
                type: 'body',
                parameters: [{
                    type: 'text',
                    text: otpCode
                }]
            }]
        }
    };

    try {
        const response = await axios.post(apiURL, messageData, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        console.log('Message sent successfully:', response.data);
    } catch (error) {
        console.error('Error sending message:', error.response ? error.response.data : error.message);
    }
}
const watiApiEndpoint = 'https://live-mt-server.wati.io/370421';
const apiKey = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6akvlXiJoVwleiZrBlGikgBb8a7F-uebMccsXSvEeTo';

async function sendOTP(phoneNumber, otp) {
    let data = JSON.stringify({
        "template_name": "login_code",
        "broadcast_name": "login_code",
        "parameters": [
            {
                "name": "1",
                "value": otp
            }
        ]
    });

    let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://live-mt-server.wati.io/370421/api/v1/sendTemplateMessage?whatsappNumber=${phoneNumber}`,
        headers: {
            'Content-Type': 'application/json',
            'Authorization': apiKey
        },
        data: data
    };

    axios.request(config)
        .then((response) => {
            console.log(JSON.stringify(response.data));
            return response.data
        })
        .catch((error) => {
            console.log(error);
        });

}

const otp = Math.floor(100000 + Math.random() * 900000).toString();
// sendWhatsAppOTP('recipient_number_including_country_code', otp); // e.g., 'recipient_number_including_country_code'

const login = async (req, res) => {
    try {
        const { email, password, role, id } = req.body;
        let user
        if (role == 'hostel_owner') {
            user = await getUserDataByEmailOrOtaId(id, role, email);
        } else {
            user = await getUserDataByEmail(email)
        }

        if (!user) {
            return Response.NotFound(res, null, 'User Not Found');
        }
        if (!user.password) {
            return Response.NotFound(res, null, 'Your account may be registered with a social login. Please reset your password first to log in.');

        }
        const isPasswordValid = await bcrypt.compare(password, user.password);

        if (!isPasswordValid) {
            return Response.Unauthorized(res, null, 'Invalid email or password');
        }
        if (!user.isActive) {
            return Response.Conflict(res, null, 'Your Account is Not Active.');
        }
        if (email) {
            if (!user.isEmailVerified) {
                return Response.Forbidden(res, null, 'Email is not verified');
            }
        }
        // Check if role matches
        if (user.role !== role) {
            return Response.NotAcceptable(res, null, `Access restricted to ${role} role`);
        }

        const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET);
        const userToSend = { ...user.toObject() };
        return Response.OK(res, { user: userToSend, token }, 'Logged in successfully');

    } catch (error) {
        console.error('Login error:', error);
        return Response.InternalServerError(res, null, error.message);
    }
};


const verifyEmail = async (req, res) => {
    try {
        const { email } = req.body;
        const user = await getUserDataByEmail(email);
        if (!user) {
            return Response.NotFound(res, null, 'User Not Found');
        }
        const otp = Math.floor(100000 + Math.random() * 900000).toString();
        const otpExpires = Date.now() + 300000; // OTP expires in 5 minutes

        await updateUserDataByEmail(req.body.email, { otp, otpExpires });
        await sendEmail("userOtpSignUp", { email: req.body.email, otp, name: user.name.first });
        return Response.Created(res, null, 'Email Verification Sent');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};
const verifyOtp = async (req, res) => {
    try {
        const { email, otp } = req.body;
        const user = await getUserDataByEmail(email);
        if (!user) {
            return Response.NotFound(res, null, 'User Not Found');
        }
        if (!user || user.otp !== Number(otp) || user.otpExpires < Date.now()) {
            return Response.NotFound(res, null, 'Invalid OTP');
        }
        let role
        if (user.role == 'user') {
            role = 'user'
        } else {
            role = 'hostel'
        }
        await updateUserDataByEmail(email, { otp: null, otpExpires: null, isEmailVerified: true }, role);

        // if (user.role === 'user') {
        //     await sendEmail("userOtpVerificationSuccess", { email: req.body.email, name: user.name.first });
        // }

        const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET);
        const userToSend = { ...user.toObject() };
        return Response.Created(res, { user: userToSend, token }, 'Logged in successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};
const users = async (req, res) => {
    try {
        const data = await getData();
        return Response.OK(res, data)
    } catch (error) {
        throw error
    }
}
const register = async (req, res) => {
    try {
        const { name, email, password, contact } = req.body;
        if (!name || !email || !password) {
            return Response.NotFound(res, null, 'Name, Email, and Password are mandatory');
        }

        const existingUser = await getUserDataByEmail(email);
        if (existingUser) {
            return Response.Conflict(res, undefined, 'Email Already Exists');
        }

        const hashedPassword = await bcrypt.hash(password, 10);

        const user = await addUserData({
            "name.first": name,
            email,
            role: 'user',
            password: hashedPassword,
            contact
        });
        console.log("user added")
        const otp = Math.floor(100000 + Math.random() * 900000).toString();
        const otpExpires = Date.now() + 600000;
        await updateUserDataByEmail(email, { otp, otpExpires }, 'user');
        console.log("OTP Updated")
        // sendOTP(contact, otp).then(result => {
        //     console.log('OTP sent successfully:', result);
        // }).catch(error => {
        //     console.error('Error sending OTP:', error);
        // });
        // await sendOTPWati('+919244770117',otp)
        await sendEmail("userOtpSignUp", { email: req.body.email, otp, name: user.name.first });

        return Response.Created(res, null, 'User created and email verification sent successfully!');
    } catch (error) {
        console.error('Registration error:', error);
        return Response.InternalServerError(res, null, error.message);
    }
};


const registerUser = async (req, res) => {
    try {
        const { name, email, password, contact, dob,
            profileImage,
            profileHeading,
            countryCode,
            country,
            currency } = req.body;
        if (!name || !email || !password) {
            return Response.NotFound(res, null, 'Name,Email and Password is manadatory');
        }
        // Check if email already exists
        const existingUser = await getUserDataByEmail(email);
        if (existingUser) {
            return Response.Conflict(res, undefined, 'EMAIL_EXISTS')
        }

        const hashedPassword = await bcrypt.hash(password, 10);


        const user = await addUserData({
            name,
            email,
            role: 'user',
            password: hashedPassword,
            contact,
            dob,
            profileImage,
            profileHeading,
            countryCode,
            country,
            currency
        });

        const otp = Math.floor(100000 + Math.random() * 900000).toString();
        const otpExpires = Date.now() + 300000;

        await updateUserDataByEmail(email, { otp, otpExpires });
        return Response.Created(res, null, 'User created and email verification sent succesfully!');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message)
    }
}

const loginUser = async (req, res) => {
    try {
        const { email, password } = req.body;
        const user = await getUserDataByEmail(email);
        if (!user) {
            return Response.NotFound(res, null, 'User Not Found');
        }
        const isPasswordValid = await bcrypt.compare(password, user.password);

        if (!isPasswordValid) {
            return Response.NotFound(res, null, 'Invalid email or password');
        }
        const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET);
        const userToSend = { ...user.toObject() };
        return Response.OK(res, { user: userToSend, token }, 'Logged in successfully');

    } catch (error) {
        console.error('Login error:', error);
        return Response.InternalServerError(res, null, error.message);
    }
}

const forgotPassword = async (req, res) => {
    try {
        const { email, reset_link } = req.body;

        const user = await getUserDataByEmail(email);

        if (!user) {
            return Response.NotFound(res, null, 'User Not Found')
        }

        // Generate a unique reset token
        const resetToken = uuidv4().substr(0, 10);

        // Save the reset token and expiration time to the user in the database
        const resetExpires = Date.now() + 3600000; // Token expires in 1 hour

        // Update the user document with the new reset token and expiration time
        await updateUserDataByEmail(email, { resetPasswordToken: resetToken, resetPasswordExpires: resetExpires })

        const url = `${reset_link}?id=${resetToken}`
        const link = `<a href="${url}">${url}</a><br>`;

        let fullName = (user.name.first + ' ' + (user.name.middle || '') + ' ' + (user.name.last || ''))
        await sendEmail("forgotPassword", { name: fullName, email: email, reset_link: url });

        return Response.OK(res, undefined, 'Reset Password link Sent to your email')
    } catch (error) {
    }
}

const resetPassword = async (req, res) => {

    try {
        const { newPassword, email, token } = req.body;

        let user = await getData({ email, resetPasswordToken: token }); // resetPasswordToken: req.body.resetToken
        user = user[0];

        if (!user) {
            return Response.NotFound(res, null, 'User not found'); //Reset token is Invalid
        }

        if (!user.isActive || user.isDeleted) {
            return res.status(401).json({ message: 'Your Account is deactive. Please contact admin' });
        }

        // if (user.resetPasswordExpires < Date.now()) {
        //     return Response.NotFound(res, null, 'Your Reset Password Link is Expired')
        // }

        const hashedPassword = await bcrypt.hash(newPassword, 10);

        await updateUserDataById(user._id, {
            password: hashedPassword,
            resetPasswordToken: null,
            resetPasswordExpires: null
        })
        return Response.OK(res, undefined, "Password Reset Successfully");

    } catch (error) {
        return Response.InternalServerError(res, null, error.message)
    }
}

const profile = async (req, res) => {
    try {
        let data
        if (req?.user?.role == 'user') {
            data = await getUserData(req.user._id, { createdAt: 0, updatedAt: 0, resetPasswordToken: 0, resetPasswordExpires: 0, __v: 0 });
        } else {
            data = await hostelLoginModel.findOne({ _id: req.user._id })
        }
        return Response.OK(res, data)
    } catch (error) {
        throw error
    }
}

const updateProfile = async (req, res) => {
    try {
        const updatedData = req.body;
        await updateUserDataById(req.user._id, updatedData);
        return Response.OK(res, null, 'User Profile Updated Successfully');
    } catch (error) {
        console.error("Error updating profile:", error);
        return Response.InternalServerError(res, { message: "Internal server error" });
    }
};

const changePassword = async (req, res) => {
    try {
        await changePasswordService(req.user, req.body)
        return Response.OK(res, {}, "PASSWORD_CHANGED");
    }
    catch (error) {
        return Response.handleError(res, error, error.statusCode)
    }
}

const listAllAdminAndSubAdmin = async (req, res) => {
    try {
        const admins = await getAllAdminAndSubAdmin();
        Response.OK(res, admins, "Admin List");
    } catch (error) {
        Response.InternalServerError(res, null, error.message);
    }
};

const addSubAdmin = async (req, res) => {
    try {
        const { name, email, password } = req.body;
        const hashedPassword = await bcrypt.hash(password, 10);
        const user = await addUserData({
            name: {
                first: name
            },
            email,
            role: 'sub_admin',
            password: hashedPassword,
        });
        // sent welcome email with verification Link
        return Response.Created(res, user, 'User Created Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const updateSubAdmin = async (req, res) => {
    try {
        const { id } = req.params;
        const updatedData = req.body;

        await updateUserDataById(id, updatedData);
        return Response.OK(res, null, 'Updated successfully');
    } catch (error) {
        console.error("Error updating sub-admin:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};

const deleteSubAdmin = async (req, res) => {
    try {
        const { id } = req.params;
        const response = await deleteUserById(id);
        Response.OK(res, response);
    } catch (error) {
        console.error("Error deleting sub-admin:", error.message);
        Response.InternalServerError(res, null, error.message);
    }
};

const addHostelOwner = async (req, res) => {
    try {
        const { name, lastname, email, password, contact } = req.body;
        const hashedPassword = await bcrypt.hash(password, 10);
        const user = await addUserData({
            name,
            email,
            role: 'hostel_owner',
            password: hashedPassword,
            contact,
        });

        const otp = Math.floor(100000 + Math.random() * 900000).toString();
        const otpExpires = Date.now() + 300000;

        await updateUserDataByEmail(email, { otp, otpExpires }, 'hostel');
        // sendOTP(contact, otp).then(result => {
        //     console.log('OTP sent successfully:', result);
        // }).catch(error => {
        //     console.error('Error sending OTP:', error);
        // });
        await sendEmail("userOtpSignUp", { email: req.body.email, otp, name: user.name.first });
        return Response.Created(res, user, 'Success');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const getUserDataById = async (req, res) => {
    try {
        const user = await getUserDataByIdService(req.params.id);
        return Response.OK(res, user)
    } catch (error) {
        throw error
    }
}

const getUsers = async (req, res) => {
    try {
        let { page, limit, filter } = req.query;

        // Convert page and limit to integers
        page = parseInt(page);
        limit = parseInt(limit);

        // If page or limit are not valid numbers, default them
        if (isNaN(page) || page <= 0) {
            page = 1;
        }
        if (isNaN(limit) || limit <= 0) {
            limit = 10;
        }

        const { users, totalUsers } = await getAllUsers(parseInt(page), parseInt(limit), filter);

        const totalPages = Math.ceil(totalUsers / limit);
        const pagination = {
            page,
            limit,
            totalPages,
            totalUsers
        };

        return Response.OK(res, { users, pagination }, 'Users listed');
    } catch (error) {
        console.error("Error listing users:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
}

export const socialLogin = async (req, res) => {
    try {
        const { email, name, contact } = req.body;
        let user = await getUserDataByEmail(email);
        if (!user) {
            if (!req.body.role) {
                return Response.NotFound(res, null, "User Not Exist, Please Register")
            }
            // Create a new user if they don't exist
            const newUser = {
                name,
                email,
                role: req.body.role,
                contact
            };
            user = await addUserData(newUser);
        }

        // Generate JWT token
        const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET);
        const userToSend = { ...user.toObject() };

        return Response.Created(res, { user: userToSend, token }, 'Login Successful');

    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};
export const usersList = async (req, res) => {
    try {
        const users = await userModel.find({ role: "user", isDeleted: false }, { name: 1 }).exec();
        return Response.OK(res, users, 'Users listed');
    } catch (error) {
        console.error("Error listing users:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
}

export const deleteSelfAccount = async (req, res) => {
    try {
        const { _id } = req.user;
        const response = await deleteUserById(_id, req.body);
        Response.OK(res, response);
    } catch (error) {
        console.error("Error processing user account action:", error.message);
        Response.InternalServerError(res, null, error.message);
    }
};

const loginOrRegister = async (req, res) => {
    try {
        const { email, password, name } = req.body;

        // Validate mandatory fields
        if (!email || !password) {
            return Response.BadRequest(res, null, 'Email and Password are mandatory');
        }

        // Check if user exists
        const user = await getUserDataByEmail(email);

        if (user) {
            // User exists, validate password
            const isPasswordValid = await bcrypt.compare(password, user.password);

            // Ensure role is user-specific
            if (user.role !== 'user') {
                return Response.Forbidden(res, null, 'Access restricted to user role');
            }

            // Generate token
            const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET);
            const userToSend = { ...user.toObject() };

            return Response.OK(res, { user: userToSend, token }, 'Login successful');
        }

        // User does not exist, create new account
        const hashedPassword = await bcrypt.hash(password, 10);

        // Set a default name if not provided
        const userName = name || 'Default User';

        // Create user with role 'user'
        const newUser = await addUserData({
            "name.first": userName,
            email,
            role: 'user',
            password: hashedPassword,
        });

        // Generate token for the new user
        const token = jwt.sign({ userId: newUser._id }, process.env.JWT_SECRET);
        const userToSend = { ...newUser.toObject() };

        return Response.Created(
            res,
            { user: userToSend, token },
            'User registered successfully and logged in'
        );

    } catch (error) {
        console.error('Login or Registration error:', error);
        return Response.InternalServerError(res, null, error.message);
    }
};

export const activteProfile = async (req, res) => {
    try {
        await userModel.updateOne(
            { _id: req.user._id },
            {
                $set: { isActive: true, deactivate_period: 0 },
                $unset: { deactivatedAt: "" }, // Remove the deactivatedAt field
            }
        );

        return Response.OK(res, null, 'User Profile Updated Successfully');
    } catch (error) {
        console.error("Error updating profile:", error);
        return Response.InternalServerError(res, { message: "Internal server error" });
    }
};

export const acticateAccount = async (req, res) => {
    try {
        const { email } = req.body;
        const data = {
            deactivate_period: 0,
            deactivatedAt: null,
            isActive: true
        }
        const response = await updateUserDataByEmail(email, data);
        Response.OK(res, response);
    } catch (error) {
        console.error("Error processing user account action:", error.message);
        Response.InternalServerError(res, null, error.message);
    }
};
export {
    users, login, register, forgotPassword, resetPassword, profile, updateProfile,
    changePassword, verifyEmail, addSubAdmin, listAllAdminAndSubAdmin,
    updateSubAdmin, deleteSubAdmin, addHostelOwner, verifyOtp, getUserDataById,
    registerUser, loginUser, getUsers, loginOrRegister
};
