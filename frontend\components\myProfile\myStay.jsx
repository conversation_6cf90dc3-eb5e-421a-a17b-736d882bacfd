import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";

import { LuCalendarDays } from "react-icons/lu";
import { getBookingDetailsApi, getMyStayApi } from "@/services/webflowServices";
import { format } from "date-fns";
import Link from "next/link";
import dynamic from "next/dynamic";
import toast from "react-hot-toast";
import Pagination from "../common/commonPagination";
import { FaRegBookmark } from "react-icons/fa";
import { SlLocationPin } from "react-icons/sl";
import { GiCash, GiTakeMyMoney } from "react-icons/gi";
import { TbCashRegister } from "react-icons/tb";

import { RiSecurePaymentLine } from "react-icons/ri";
import { FaClockRotateLeft } from "react-icons/fa6";
import { IoMdCheckboxOutline } from "react-icons/io";


// const CustomPagination = dynamic(
//   () => import("../customPagination/customPagination"),
//   {
//     ssr: false,
//   }
// );

const WriteReview = dynamic(() => import("../review/writeReview"), {
  ssr: false,
});

const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});
const Bookingdetailspopup = dynamic(
  () => import("../model/bookingdetailspopup"),
  { ssr: false }
);
const Cancelpopup = dynamic(() => import("../model/cancelpopup"), {
  ssr: false,
});

const MyStay = () => {
  const [stayData, setStayData] = useState([]);
  const [bookingDetailsData, setBookingDetailsData] = useState("");
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalData, setTotalData] = useState(0);
  const [openReview, setOpenReview] = useState(false);
  const [bookingId, setBookingId] = useState();
  const [isUpdate, setIsUpdateData] = useState(false);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const isFirstRender = useRef(null);
  const [openBookingdetailspopup, setopenBookingdetailspopup] = useState(false);
  // const handleOpenBookingdetailspopup = () => setopenBookingdetailspopup(true);
  const handleCloseBookingdetailspopup = () =>
    setopenBookingdetailspopup(false);
  const [openCancelpopup, setopenCancelpopup] = useState(false);
  // const handleOpenCancelpopup = () => setopenCancelpopup(true);
  const handleCloseCancelpopup = () => setopenCancelpopup(false);

  // const handleOpen = () => {
  //   setOpenReview(true);
  // };

  const handleClose = () => {
    setOpenReview(false);
  };

  useEffect(() => {
    const fetchMyStayData = async () => {
      setLoading(true);
      try {
        const response = await getMyStayApi(currentPage, itemsPerPage);
        console.log("stayData", response);

        setStayData(response?.data?.data?.bookings || []);
        setTotalPages(response?.data?.data?.pagination?.totalPages || 1);
        setTotalData(response?.data?.data?.pagination?.totalBookings || 0);
      } catch (error) {
        console.error("Error fetching stay data:", error);
      } finally {
        setLoading(false);
      }
    };
    if (!isFirstRender.current) {
      fetchMyStayData();
    } else {
      isFirstRender.current = false;
    }
  }, [currentPage, isUpdate, itemsPerPage]);

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  const handleOpenBookingdetailspopup = async (id) => {
    try {
      const response = await getBookingDetailsApi(id);
      console.log("response", response);
      if (response?.data?.status) {
        setopenBookingdetailspopup(true);
        setBookingDetailsData(response?.data?.data?.bookings?.[0] || "");
      } else {
        // setopenBookingdetailspopup();
        toast.error(
          response?.data?.message ||
            "There is some issue getting booking details..."
        );
      }
    } catch (error) {
      console.error("Failed to delete account:", error);
      // Optionally, display an error message to the user here
    }
  };

  const handleOpenCancelpopup = async (id) => {
    setBookingId(id);
    setopenCancelpopup(true);
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const targetDate = new Date().getTime() + 10 * 24 * 60 * 60 * 1000; // 10 days from now

  const [timeLeft, setTimeLeft] = useState({
    days: "00",
    hours: "00",
    minutes: "00",
    seconds: "00",
  });

  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date().getTime();
      const distance = targetDate - now;

      if (distance <= 0) {
        clearInterval(timer);
        setTimeLeft({ days: "00", hours: "00", minutes: "00", seconds: "00" });
      } else {
        const days = String(
          Math.floor(distance / (1000 * 60 * 60 * 24))
        ).padStart(2, "0");
        const hours = String(
          Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
        ).padStart(2, "0");
        const minutes = String(
          Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
        ).padStart(2, "0");
        const seconds = String(
          Math.floor((distance % (1000 * 60)) / 1000)
        ).padStart(2, "0");

        setTimeLeft({ days, hours, minutes, seconds });
      }
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <>
      <Loader open={loading} />
      <h2 className="text-[#40E0D0] flex gap-1 text-2xl font-bold mb-6">
        My
        <span className="text-black ml-0.5"> Stay</span>
      </h2>

      <div className="flex flex-col gap-5">
        {stayData.length > 0
          ? stayData.map((booking, index) => (
              <div
                key={index}
                className="sm:flex border rounded-3xl relative overflow-hidden  "
              >
                <Link
                  href={`/hostels-detail/${booking?.property?._id}`}
                  passHref
                  prefetch={false}
                >
                  <Image
                    // src={`https://${booking?.property?.photos?.[0]?.url}`}
                    src={booking?.property?.images?.[0]?.objectUrl}
                    alt="stay"
                    width={400}
                    height={250}
                    className="object-cover cursor-pointer sm:min-w-[200px] sm:w-[400px] w-full sm:min-h-[200px] h-full "
                  />
                  
                   <span className="absolute top-2 left-0 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs font-semibold px-3 py-1  rounded-r-md shadow-md">
                    Payment Due
                  </span> 
                    {/* <span className="absolute top-2 -left-1 bg-gradient-to-r from-green-500 to-green-700  text-white text-sm font-semibold px-4 py-1  rounded-r-lg shadow-md">
                    Paid
                  </span> */}
                </Link>

                <div className="md:flex block  justify-between w-full ">
                  <div className="py-4 sm:px-4 px-3 w-[70%]">
                    <Link
                      href={`/hostels-detail/${booking?.property?._id}`}
                      passHref
                      prefetch={false}
                    >
                      <h2 className='sm:text-xl text-lg font-bold mb-1 cursor-pointer hover:underline'>
                        {booking?.property?.name} <br/>
                      </h2>
                    </Link>
                    <h3 className=' font-bold mb-3'>{booking?.roomName} ({booking?.ratePlan?.name})</h3>
                    <p className='flex gap-1 text-xs mb-1'>
                      <SlLocationPin className='text-[#40E0D0]' size={12} />
                      {booking?.property?.address?.lineOne},{" "}
                      {booking?.property?.address?.state},{" "}
                      {booking?.property?.address?.country} 
                    </p>

                    <p className="flex items-center gap-1  text-xs mb-1">
                      <LuCalendarDays className="text-[#40E0D0]" />
                      {format(
                        new Date(booking.checkInDate),
                        "dd MMM yyyy"
                      )} -{" "}
                      {format(new Date(booking.checkOutDate), "dd MMM yyyy")}
                    </p>
                    <p className="flex gap-1  text-xs mb-1">
                      <FaRegBookmark className="text-[#40E0D0]" size={12} />
                      Booking ID - XYZ010101010
                    </p>
                    <p className="flex gap-1  text-xs mb-1">
                      <GiCash className="text-[#40E0D0]" size={12} />
                      Total Price - INR 5008{" "}
                      <span>(Inc all Fees and Taxes)</span>
                    </p>
                    <p className="flex gap-1  text-xs mb-1">
                      <TbCashRegister className="text-[#40E0D0]" size={12} />
                      Pay a Little Now -{" "}
                      <span className="text-xs font-bold text-green-600">
                        {" "}
                        INR 508/-
                      </span>
                      <IoMdCheckboxOutline
                        className="text-[#40E0D0]"
                        size={12}
                      />
                      Paid -{" "}
                      <span className="text-xs font-bold text-green-600">
                        {" "}
                        INR 508/-
                      </span>
                    </p>
                    <p className="flex gap-1  text-xs mb-2">
                      <FaClockRotateLeft className="text-[#40E0D0]" size={12} />
                      Pay the rest when you arrive -{" "}
                      <span className="text-xs font-bold text-black">
                        {" "}
                        INR 4500/-
                      </span>
                    </p>
                    {/* <p className="flex gap-1  text-sm font-bold text-green-600">
                      <RiCheckboxCircleFill
                        className="text-green-600 font-bold"
                        size={18}
                      />
                      Paid - 508/-
                    </p> */}
                    {/* <p className="flex gap-1  text-sm font-bold text-red-600 mb-4">
                      <FaClockRotateLeft
                        className="text-red-600 font-bold"
                        size={16}
                      />
                      Payable Now - INR 508/-
                    </p> */}
                    <div className="flex gap-x-1">
                      {" "}
                      <button className="bg-[#FFD93D] text-black  px-3 py-2 rounded-xl sm:text-sm text-xs flex items-center gap-x-1">
                        <RiSecurePaymentLine size={16} />{" "}
                        <span>Pay Now 508/-</span>
                      </button>
                      <button className="bg-blue-600 text-white  px-3 py-2 rounded-xl sm:text-sm text-xs flex items-center gap-x-1">
                        <GiTakeMyMoney size={16} />
                        <span>Pay Full Amount</span>
                      </button>{" "}
                    </div>
                    {/* Pass the open and close props to the WriteReview component */}
                    <WriteReview
                      open={openReview}
                      close={handleClose}
                      id={booking?.property?._id}
                    />
                  </div>

                  <div className="sm:px-6 px-3 py-3 flex md:flex-col flex-row-reverse  justify-between gap-1 w-[30%]">
                    <div className="flex justify-center items-center text-black py-1 rounded-xl mb-2">
                      <div className="flex items-center gap-1 text-sm font-mono leading-none">
                        <span className=" rounded-full p-1">
                          {timeLeft.days}d
                        </span>
                        <span>:</span>
                        <span>{timeLeft.hours}h</span>
                        <span>:</span>
                        <span>{timeLeft.minutes}m</span>
                        <span>:</span>
                        <span>{timeLeft.seconds}s</span>
                      </div>
                    </div>
                    <div className="px-4 pb-3 flex md:flex-col flex-row-reverse  justify-end gap-1 w-full">
                      {!booking?.isCancel ? (
                        <>
                          <button
                            className="bg-primary-blue text-white  px-3 py-2 rounded-xl sm:text-sm text-xs"
                            onClick={() =>
                              handleOpenBookingdetailspopup(booking?._id)
                            }
                          >
                            View Booking
                          </button>
                          {/* <button
                            className="bg-red-600 text-white  px-3 py-2 rounded-full sm:text-sm text-xs"
                            onClick={() => handleOpenCancelpopup(booking?._id)}
                          >
                            Cancel
                          </button> */}
                        </>
                      ) : (
                        <p className="bg-red-600 text-white sm:px-4 px-3 py-2 rounded-xl sm:text-sm text-xs font-semibold border border-gray-300">
                          Cancelled
                        </p>
                      )}
                      <button className="bg-gray-500 text-white px-3 py-2 rounded-xl sm:text-sm text-xs">
                        Modify Name
                      </button>
                      <button
                        className="bg-black text-white px-3 py-2 rounded-xl sm:text-sm text-xs"
                        // onClick={handleOpen}
                      >
                        Leave Review
                      </button>
                      <button
                        className="bg-red-600 text-white  px-3 py-2 rounded-xl sm:text-sm text-xs"
                        onClick={() => handleOpenCancelpopup(booking?._id)}
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))
          : !loading && <p>No stays available.</p>}
      </div>
      {totalPages >= 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalData || 0}
          itemsPerPage={itemsPerPage}
          onPageChange={handlePageChange}
          onItemsPerPageChange={handleItemsPerPageChange}
        />
      )}
      <Bookingdetailspopup
        openBookingdetailspopup={openBookingdetailspopup}
        handleCloseBookingdetailspopup={handleCloseBookingdetailspopup}
        bookingDetailsData={bookingDetailsData}
      />
      <Cancelpopup
        openCancelpopup={openCancelpopup}
        handleCloseCancelpopup={handleCloseCancelpopup}
        bookingId={bookingId}
        setIsUpdateData={setIsUpdateData}
        setLoading={setLoading}
      />
    </>
  );
};

export default MyStay;
