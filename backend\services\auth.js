import userModel from "../models/auth.js";
import mongoose from "mongoose"
import bcrypt from 'bcrypt';
let currentDate = new Date();
import hostelLoginModel from "../models/hostelsLogin.js"
const getData = async (find, project = { password: 0 }) => {
    return await userModel.find(find, project)
};
const getUserData = async (id, projection = { password: 0 }) => {

    return await userModel.findOne({ _id: new mongoose.Types.ObjectId(id), isDeleted: false }, projection)
};

const getUserDataByEmail = async (email, projection = {}) => {
    let user = await userModel.findOne({ email: email, isDeleted: false }, projection)
    if (!user) {
        user = await hostelLoginModel.findOne({ email: email, isDeleted: false }, projection)
    }
    return user
};
const addUserData = async (data) => {
    if (data?.role == 'user') {
        if (await userModel.isEmailTaken(data.email)) {
            throw new Error('User Already Exists');
        }
        return await userModel.create(data)
    } else {
        // if (await hostelLoginModel.isEmailTaken(data.email)) {
        //     throw new Error('User Already Exists');
        // }
        return await hostelLoginModel.create(data)
    }

}
const updateLastLogin = async (user) => {

    return await userModel.updateOne({ _id: user._id }, { $set: { lastAccess: currentDate } })

}
const updateUserDataById = async (id, newData) => {
    try {
        const user = await userModel.findOne({ _id: id });

        // Check if the user exists
        if (!user) {
            throw new Error('User Not Exists');
        }
        const updatedUser = await userModel.updateOne({ _id: id }, { $set: newData });
        return updatedUser;
    } catch (error) {
        // Handle errors
        console.error("Error updating user data:", error.message);
        throw error;
    }
};
const updateUserDataByEmail = async (email, newData, role) => {
    try {
        let updatedUser;
        if (role === 'hostel') {
            updatedUser = await hostelLoginModel.findOneAndUpdate(
                { email: email },
                newData,
                { new: true } // important to return the updated document
            );
        } else {
            updatedUser = await userModel.findOneAndUpdate(
                { email: email },
                newData,
                { new: true }
            );
        }

        console.log("updatedUser", updatedUser);

        if (!updatedUser) {
            throw new Error('User Not Found');
        }

        return updatedUser;
    } catch (error) {
        console.error("Error updating user data by email:", error.message);
        throw error;
    }
};

const getUserDataByIdService = async (id) => {
    try {
        const user = await userModel.findOne({ _id: new mongoose.Types.ObjectId(id) }, { password: 0, });
        if (!user) {
            throw new Error('User Not Found');
        }
        return user
    } catch (error) {
        // Handle errors
        console.error("Error updating user data:", error.message);
        throw error;
    }
};

const changePasswordService = async (user, body) => {
    try {

    }
    catch (error) {

        throw error
    }
}

// Get all admins and sub-admins
const getAllAdminAndSubAdmin = async () => {
    return await userModel.find({ role: { $in: ['admin', 'sub_admin'] }, isDeleted: false, isArchived: false }, { name: 1, email: 1, role: 1 });
};

const deleteUserById = async (id, data) => {
    try {
        const user = await userModel.findOne({ _id: id });

        if (!user) {
            throw new Error('USER_NOT_FOUND');
        }

        // Handle deactivate logic
        if (data?.type === "deactivate") {
            const deactivatePeriod = parseInt(data.deactivate_period, 10); // Convert period to a number
            if (isNaN(deactivatePeriod) || deactivatePeriod <= 0) {
                throw new Error('INVALID_DEACTIVATE_PERIOD');
            }

            // Calculate the deactivation period's end date
            const deactivatedAt = new Date();

            // Update the user document with the new deactivation data
            await userModel.updateOne(
                { _id: id },
                {
                    $set: {
                        isActive: false,
                        deactivate_period: deactivatePeriod,
                        deactivatedAt: deactivatedAt,
                    },
                }
            );

            return { message: `Account deactivated for ${deactivatePeriod} day(s)` };
        }

        // Handle delete logic
        await userModel.updateOne(
            { _id: id },
            {
                $set: {
                    isDeleted: true,
                    isActive: false,
                    deactivate_period: 0, // Reset the deactivation period
                    deactivatedAt: null,  // Reset the deactivation date
                },
            }
        );

        return { message: 'Account deleted successfully' };
    } catch (error) {
        console.error("Error processing user action:", error.message);
        throw error;
    }
};


// get all users (customers)
const getAllUsers = async (page, limit, filter) => {
    const skip = (page - 1) * limit;

    // const { status, country } = filter;

    const query = { role: 'user', ...filter, isDeleted: false, isActive: true }

    const users = await userModel.find(query).skip(skip).limit(limit).exec();


    const totalUsers = await userModel.countDocuments(query);

    return { users, totalUsers };
}
const getUserDataByEmailOrOtaId = async (id, role, email) => {
    const conditions = [];

    if (id && id.includes("@")) {
        conditions.push({ email: id });
    }

    if (!isNaN(id)) {
        conditions.push({ otaId: Number(id) });
    }


    const query = { $or: conditions };
    const model = role === "hostel_owner" ? hostelLoginModel : userModel;

    console.log("model", model.modelName); // Better debug output
    console.log("query", query);

    const property = await model.findOne(query); // ✅ FIXED LINE
    console.log(property);
    return property;
};


export {
    getData, getUserData, getUserDataByEmail, addUserData, updateUserDataByEmail,
    updateUserDataById, getUserDataByIdService, updateLastLogin, changePasswordService, getAllAdminAndSubAdmin, deleteUserById,
    getAllUsers, getUserDataByEmailOrOtaId
};
