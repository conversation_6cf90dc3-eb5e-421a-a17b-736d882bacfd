import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
import { ChevronRight } from "lucide-react";
import { Modal, Fade, Backdrop } from "@mui/material";
import { getNoticeApi } from "@/services/webflowServices";
import { getItemLocalStorage } from "@/utils/browserSetting";
import toast from "react-hot-toast";
import { FaCaretUp } from "react-icons/fa";
// import { BiSolidHeartCircle } from "react-icons/bi";
// import { MdOutlineChat } from "react-icons/md";
// import { FaCalendarDays } from "react-icons/fa6";
// import { PiMapPinLineFill } from "react-icons/pi";
// import { IoFilterSharp } from "react-icons/io5";

import { useRouter } from "next/router";
// import zIndex from "@mui/material/styles/zIndex";

// const notices = [
//   {
//     title: "Notice 1",
//     text: "Mad monkey updated new roof top cafe with edm vibes 5 min ago",
//   },
//   {
//     title: "Notice 2",
//     text: "Mad monkey updated new roof top cafe with edm vibes 5 min ago",
//   },
//   {
//     title: "Notice 3",
//     text: "Mad monkey updated new roof top cafe with edm vibes 5 min ago",
//   },
// ];

const NoticeBoardDetail = ({ open, close }) => {
  const style = {
    position: "fixed",
    top: "50%",
    left: "70%",
    transform: "translate(-50%, -50%)",
    width: "100%",
    bgcolor: "background.paper",
    boxShadow: 24,
    borderRadius: "20px",
  };

  // eslint-disable-next-line no-unused-vars
  const [age, setAge] = useState("");
  // eslint-disable-next-line no-unused-vars
  const [notices, setNotices] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [loading, setLoading] = useState(false);
  const isFirstRender = useRef(null);

  const router = useRouter();

  const handleOpenDetail = () => {
    router.push("/noticeboard-detail");
    close(); // Close the main modal
  };

  useEffect(() => {
    const fetchNotices = async () => {
      setLoading(true);
      try {
        const response = await getNoticeApi();
        setNotices(response?.data?.data);
      } catch (error) {
        console.error("Error fetching payment data:", error);
      } finally {
        setLoading(false);
      }
    };
    if (!isFirstRender.current && getItemLocalStorage("token") && open) {
      fetchNotices();
    } else if (open) {
      toast.error("Please Login !!!");
      close();
    } else {
      isFirstRender.current = false;
    }
  }, [open]);

  // eslint-disable-next-line no-unused-vars
  const handleChange = (event) => {
    setAge(event.target.value);
  };

  return (
    <>
      <Modal
        aria-labelledby="transition-modal-title"
        aria-describedby="transition-modal-description"
        open={open}
        onClose={close}
        closeAfterTransition
        slots={{ backdrop: Backdrop }}
        slotProps={{
          backdrop: {
            timeout: 500,
          },
        }}
      >
        <Fade in={open} sx={style}>
          <div className="bg-white rounded-2xl max-w-[450px] md:w-auto xs:w-[320px] w-[280px] lg:end-[10%] end-[5%] lg:mt-28 mt-24 float-right relative z-50">
            <div className="relative">
              <FaCaretUp className="xs:top-[-25px] top-[-22px] absolute lg:start-[40%] md:start-[70%] xs:start-[60%] start-[52%]" size={40} color="#40E0D0" />
              <div className="flex justify-between items-center pe-4 border-b bg-[#40E0D0] rounded-t-2xl">
                <h2 className="sm:text-[27px] text-xl leading-none rounded-t-2xl text-center font-bold text-black p-4">
                  <span className="text-[#fff]">Notice</span>board
                </h2>
                <button
                  onClick={handleOpenDetail}
                  className="bg-[#fff] xs:text-sm text-xs font-semibold rounded-lg border border-white hover:border-black transition-all flex items-center justify-center py-1 ps-2 pe-1"
                >
                  View All <ChevronRight size={18}/>
                </button>
              </div>
              {/* <FormControl
                sx={{ minWidth: 140, minHeight: 40 }}
                size="small"
                className="absolute -top-1 right-0"
              >
                <InputLabel
                  id="demo-simple-select-label"
                  className="flex items-center gap-x-2 text-sm border rounded-2xl px-2 py-1.5 text-black"
                >
                  <IoFilterSharp />
                  Hostel / City
                </InputLabel>
              </FormControl> */}
            </div>
            <div className="">
            {/* <div className="flex space-x-2 mt-4 px-1 flex-wrap md:flex-nowrap space-y-2 mr-0">
                <button className="bg-[#D9F9F6] w-[67px] xl:w-[60px] h-[51px] rounded-lg flex justify-center items-center hover:border-2 hover:border-[#66E6D9] text-[#56edde] font-semibold text-sm mt-2 ml-2.5 md:ml-0">
                  All
                </button>
                <button className="bg-[#FFE3EF] w-[145px] xl:w-[142] h-[51px] rounded-lg flex justify-center items-center hover:border-2 hover:border-pink-500 text-pink-500 font-semibold text-sm">
                  Activity Updates
                </button>
                <button className="bg-[#FFEFE3] w-[148px] md:w-[150px] xl:w-[146] h-[51px] rounded-lg flex justify-center items-center hover:border-2 hover:border-orange-400  text-orange-400  font-semibold text-sm">
                  Property Updates
                </button>
                <button className="bg-[#D9F9F6] w-[157px] md:w-[160px] xl:w-[144] h-[51px] rounded-lg flex justify-center items-center hover:border-2 hover:border-[#66E6D9] text-[#56edde] font-semibold text-sm">
                  Social Interactions
                </button>
              </div> */}

              <div className="xs:pb-0 pb-2">
                <div className="bg-white hover:bg-gray-100 flex justify-between sm:p-3 p-2 border-b last:border-0 w-full gap-2 rounded-0 last:rounded-b-2xl items-center">
                  <div className="flex flex-col justify-around">
                    <div className="flex gap-2 items-center">
                      <div>
                        <Image
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                          width={37}
                          height={37}
                          alt="Arlene McCoy"
                          className="min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full"
                        />
                      </div>
                      <div>
                        <button className="text-black bg-[#d8d4d4] w-auto px-2 h-[20px] xs:flex hidden items-center justify-center text-xs rounded-md">
                          Mix Mate
                        </button>
                        <p className="font-semibold text-gray-400 text-xs xs:mt-1 mt-0">
                          <span className="text-black text-xs xs:font-bold font-semibold">
                            {" "}
                            Priyanka
                          </span>{" "}
                          - You got your first like!
                        </p>
                      </div>
                    </div>
                  </div>
                  <p className="sm:text-sm text-xs text-gray-400 leading-none">5m</p>
                </div>

                <div className="bg-white hover:bg-gray-100 flex justify-between sm:p-3 p-2 border-b last:border-0 w-full gap-2 rounded-0 last:rounded-b-2xl items-center">
                  <div className="flex flex-col justify-evenly">
                    <div className="flex gap-2 items-center">
                      <div>
                        <Image
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                          width={37}
                          height={37}
                          alt="Arlene McCoy"
                          className="min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full"
                        />
                      </div>
                      <div>
                        <button className="text-black bg-[#d8d4d4] w-auto px-2 h-[20px] xs:flex hidden items-center justify-center text-xs rounded-md">
                          Mix Event
                        </button>
                        <p className="xs:font-semibold font-medium text-black text-xs xs:mt-1 mt-0">
                          <span className="text-black text-xs xs:font-bold font-semibold">
                            {" "}
                            Mad Monkey
                          </span>{" "}
                          - Created New Event - Club Dance
                        </p>
                      </div>
                    </div>
                  </div>
                  <p className="sm:text-sm text-xs text-gray-400 leading-none">5m</p>
                </div>

                <div className="bg-white hover:bg-gray-100 flex justify-between sm:p-3 p-2 border-b last:border-0 w-full gap-2 rounded-0 last:rounded-b-2xl items-center">
                  <div className="">
                    <div className="flex gap-2 items-center">
                      <div>
                        <Image
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                          width={37}
                          height={37}
                          alt="Arlene McCoy"
                          className="min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full"
                        />
                      </div>
                      <div className="">
                        <button className="text-black bg-[#d8d4d4] w-auto px-2 h-[20px] xs:flex hidden items-center justify-center text-xs rounded-md">
                          Mix Creator
                        </button>
                        <p className="xs:font-semibold font-medium text-black text-xs xs:mt-1 mt-0">
                          <span className="text-black text-xs xs:font-bold font-semibold">
                            {" "}
                            Mad Monkey - Mix
                          </span>{" "}
                          <span className="text-[#56edde]">Creator </span>
                          Sent offer request to Ayush Jain
                        </p>
                      </div>
                    </div>
                  </div>
                  <p className="sm:text-sm text-xs text-gray-400 leading-none">5m</p>
                </div>

                <div className="bg-white hover:bg-gray-100 flex justify-between sm:p-3 p-2 border-b last:border-0 w-full gap-2 rounded-0 last:rounded-b-2xl items-center">
                  <div className="flex flex-col justify-around">
                    <div className="flex gap-2 items-center">
                      <div>
                        <Image
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                          width={37}
                          height={37}
                          alt="Arlene McCoy"
                          className="min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full"
                        />
                      </div>
                      <div>
                        <button className="text-black bg-[#d8d4d4] w-auto px-2 h-[20px] xs:flex hidden items-center justify-center text-xs rounded-md">
                          Mix Mate
                        </button>
                        <p className="font-semibold text-gray-400 text-xs xs:mt-1 mt-0">
                          <span className="text-black text-xs xs:font-bold font-semibold">
                            {" "}
                            Priyanka
                          </span>{" "}
                          - You got your first like!
                        </p>
                      </div>
                    </div>
                  </div>
                  <p className="sm:text-sm text-xs text-gray-400 leading-none">5m</p>
                </div>

                <div className="bg-white hover:bg-gray-100 flex justify-between sm:p-3 p-2 border-b last:border-0 w-full gap-2 rounded-0 last:rounded-b-2xl items-center">
                  <div className="flex flex-col justify-evenly">
                    <div className="flex gap-2 items-center">
                      <div>
                        <Image
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                          width={37}
                          height={37}
                          alt="Arlene McCoy"
                          className="min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full"
                        />
                      </div>
                      <div>
                        <button className="text-black bg-[#d8d4d4] w-auto px-2 h-[20px] xs:flex hidden items-center justify-center text-xs rounded-md">
                          Mix Event
                        </button>
                        <p className="xs:font-semibold font-medium text-black text-xs xs:mt-1 mt-0">
                          <span className="text-black text-xs xs:font-bold font-semibold">
                            {" "}
                            Mad Monkey
                          </span>{" "}
                          - Created New Event - Club Dance
                        </p>
                      </div>
                    </div>
                  </div>
                  <p className="sm:text-sm text-xs text-gray-400 leading-none">5m</p>
                </div>

                {/* <div className="w-full flex items-center justify-center">
                  <button
                    onClick={handleOpenDetail}
                    className="bg-[#40E0D0] text-sm font-semibold w-full py-4 rounded-b-2xl hover:bg-sky-blue-750 hover:text-white transition-all flex items-center justify-center"
                  >
                    View All <MoveRight className="ml-3" size={22} />
                  </button>
                </div> */}
              </div>
            </div>
          </div>
        </Fade>
      </Modal>
    </>
  );
};

export default NoticeBoardDetail;
