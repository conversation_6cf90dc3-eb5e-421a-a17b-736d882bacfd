import Notification from '../models/notifications.js';
import bookingModel from '../models/bookings.js';
import Noticeboard from '../models/noticeboard.js'
import Response from '../utills/response.js';
import socialIntrectionCommentsModel from '../models/socialInteractionComments.js';
import socialIntrectionLikesModel from '../models/socialInteractionsLikes.js';
import mongoose from 'mongoose';
import socialIntrectionCommentLikesModel from '../models/socialInteractionCommentsLikes.js';

export const listNoticeboardController = async (req, res) => {
    try {
        const { type } = req.query;

        const matchQuery = {
            $or: [
                { userId: new mongoose.Types.ObjectId(req.user._id) },
                { isGlobal: true }
            ]
        };

        if (type) {
            matchQuery.type = type;
        }

        const notifications = await Noticeboard.aggregate([
            { $match: matchQuery },
            { $sort: { createdAt: -1 } },

            // Lookup user
            {
                $lookup: {
                    from: "users",
                    let: { userId: "$user" },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ["$_id", "$$userId"] }
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                name: 1,
                                email: 1
                                // add any other user fields you want here
                            }
                        }
                    ],
                    as: "user"
                }
            },
            {
                $unwind: {
                    path: "$user",
                    preserveNullAndEmptyArrays: true
                }
            },

            // Lookup actionBy
            {
                $lookup: {
                    from: "users",
                    let: { actionById: "$actionBy" },   // local field passed as variable
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ["$_id", "$$actionById"] }  // match user _id with actionBy
                            }
                        },
                        {
                            $project: {
                                _id: 1,
                                name: 1,
                                email: 1,
                                // include only the fields you want from users
                            }
                        }
                    ],
                    as: "actionBy"
                }
            },
            {
                $unwind: {
                    path: "$actionBy",
                    preserveNullAndEmptyArrays: true
                }
            },

            // Lookup likes and count how many
            {
                $lookup: {
                    from: 'socialinteractionslikes',
                    let: { interactionId: '$_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$socialIntrection', '$$interactionId'] },
                                        { $eq: ['$isDeleted', false] }
                                    ]
                                }
                            }
                        },

                    ],
                    as: 'likeInfo'
                }
            },

            // Add likeCount to social_interactions
            {
                $addFields: {
                    "social_interactions.likes": { $size: "$likeInfo" },
                    "isLiked": {
                        $gt: [
                            {
                                $size: {
                                    $filter: {
                                        input: "$likeInfo",
                                        as: "like",
                                        cond: {
                                            $and: [
                                                { $eq: ["$$like.user", new mongoose.Types.ObjectId(req.user._id)] },
                                                { $eq: ["$$like.isLike", true] },
                                                { $eq: ["$$like.isDeleted", false] }
                                            ]
                                        }
                                    }
                                }
                            },
                            0
                        ]
                    }
                }
            },

            // Final cleanup
            {
                $project: {
                    likeInfo: 0,
                    __v: 0,
                    'user.__v': 0,
                    'actionBy.__v': 0
                }
            }
        ]);

        return res.status(200).json({ success: true, data: notifications });

    } catch (error) {
        console.error("Error retrieving notifications:", error.message);
        return res.status(500).json({ success: false, message: 'Internal server error.' });
    }
};

export const getUnreadCount = async (req, res) => {
    try {
        const { type } = req.query;
        let notifications = [];
        const query = {
            $or: [
                { userId: req.user._id },
                { isGlobal: true }
            ],
            isRead: false
        };

        // Add type filter if provided in query
        if (type) {
            query.type = type;
        }
        notifications = await Notification.countDocuments(query)
        return res.status(200).json({ success: true, data: { counts: notifications } });
    } catch (error) {
        console.error("Error retrieving notifications:", error.message);
        return res.status(500).json({ success: false, message: 'Error retrieving notifications.' });
    }
};
export const updateRead = async (req, res) => {
    try {
        const { notificationIds } = req.body;

        if (!Array.isArray(notificationIds) || notificationIds.length === 0) {
            return Response.BadRequest(res, 'No notification IDs provided');
        }

        const objectIds = notificationIds.map(id => new mongoose.Types.ObjectId(id));

        await Notification.updateMany(
            {
                _id: { $in: objectIds },
            },
            { $set: { isRead: true } },
        );
        return Response.OK(res, null, 'NOTIFICATIONS_READ');
    } catch (error) {
        return Response.InternalServerError(res, error.message);
    }
};
export const noticeboardAdd = async (actionBy, message, type, title, isGlobal, dynamicId, dynamicData) => {
    try {
        await Noticeboard.create({ actionBy, message, type, title, isGlobal, dynamicId, dynamicData })
        return
    } catch (error) {
        console.log("err", error)
        return error
    }
};
export const addSocialInteraction = async (req, res) => {
    try {
        const { comment, videos, photos, experience } = req.body;
        const actionBy = req.user._id;

        // Ensure at least one social interaction exists
        if (!comment && (!videos || videos.length === 0) && (!photos || photos.length === 0) && !experience) {
            return res.status(400).json({ success: false, message: "At least one social interaction is required." });
        }

        const newNotice = new Noticeboard({
            social_interactions: {
                comment,
                videos,
                photos,
                experience,
            },
            isGlobal: true,
            title: comment,
            type: "social_interaction",
            user: req.user._id,
            actionBy
        });

        await newNotice.save();
        return res.status(201).json({ success: true, message: "Noticeboard entry created successfully.", data: newNotice });

    } catch (error) {
        console.error("Error adding noticeboard:", error);
        return res.status(500).json({ success: false, message: "Internal server error." });
    }
};
export const addSocialInteractionComment = async (req, res) => {
    try {
        const { socialIntrection, comment } = req.body;
        const user = req.user._id;

        const newComment = new socialIntrectionCommentsModel({
            user,
            socialIntrection,
            comment,
        });

        await newComment.save();

        return res.status(201).json({
            success: true,
            message: "Social interaction comment added successfully.",
            data: newComment
        });

    } catch (error) {
        console.error("Error adding social interaction comment:", error);
        return res.status(500).json({
            success: false,
            message: "Internal server error."
        });
    }
};
export const getSoccialInteractionComments = async (req, res) => {
    try {
        const { socialIntrectionId } = req.params;

        const comments = await socialIntrectionCommentsModel.find({
            socialIntrection: socialIntrectionId,
            isDeleted: false
        })
            .populate("user", "name profileImage") // adjust fields as needed
            .sort({ createdAt: -1 }); // sort by latest

        return res.status(200).json({
            success: true,
            message: "Comments fetched successfully.",
            data: comments
        });

    } catch (error) {
        console.error("Error fetching comments:", error);
        return res.status(500).json({
            success: false,
            message: "Internal server error."
        });
    }
};
export const getSocialInteractionLikes = async (req, res) => {
    try {
        const { socialIntrectionId } = req.params;

        const likes = await socialIntrectionLikesModel.find({
            socialIntrection: socialIntrectionId,
            isDeleted: false,
            isLike: true
        })
            .populate("user", "name profileImage") // optional: adjust as needed
            .sort({ createdAt: -1 });

        return res.status(200).json({
            success: true,
            message: "Likes fetched successfully.",
            data: likes
        });

    } catch (error) {
        console.error("Error fetching likes:", error);
        return res.status(500).json({
            success: false,
            message: "Internal server error."
        });
    }
};
export const addSocialInteractionLike = async (req, res) => {
    try {
        const { socialIntrection, isLike } = req.body;
        const user = req.user._id;

        const updatedLike = await socialIntrectionLikesModel.findOneAndUpdate(
            {
                user,
                socialIntrection,
                isDeleted: false
            },
            {
                $set: {
                    isLike,
                }
            },
            {
                new: true,
                upsert: true,
            }
        );

        return res.status(200).json({
            success: true,
            message: "Like status saved successfully.",
            data: updatedLike
        });

    } catch (error) {
        console.error("Error adding/updating like:", error);
        return res.status(500).json({
            success: false,
            message: "Internal server error."
        });
    }
};

export const likeSocialInteractionComment = async (req, res) => {
    try {
        const { commentId, isLike } = req.body;
        const userId = req.user._id;

        // Check if comment exists
        const comment = await socialIntrectionCommentsModel.findById(commentId);
        if (!comment) {
            return res.status(404).json({ success: false, message: "Comment not found." });
        }

        // Check for existing like record
        const existingLike = await socialIntrectionCommentLikesModel.findOne({
            user: userId,
            socialIntrectionComment: commentId,
        });

        if (isLike) {
            if (existingLike) {
                if (existingLike.isLike) {
                    return res.status(400).json({ success: false, message: "Comment already liked." });
                } else {
                    existingLike.isLike = true;
                    await existingLike.save();
                }
            } else {
                await socialIntrectionCommentLikesModel.create({
                    user: userId,
                    socialIntrectionComment: commentId,
                    isLike: true
                });
            }

            const likesCount = await socialIntrectionCommentLikesModel.countDocuments({
                socialIntrectionComment: commentId,
                isLike: true,
            });

            return res.status(201).json({
                success: true,
                liked: true,
                message: "Comment liked successfully.",
                likesCount
            });

        } else {
            if (!existingLike || !existingLike.isLike) {
                return res.status(400).json({ success: false, message: "Comment not liked yet." });
            }

            existingLike.isDeleted = true;
            await existingLike.save();

            const likesCount = await socialIntrectionCommentLikesModel.countDocuments({
                socialIntrectionComment: commentId,
                isLike: true,
                isDeleted: false
            });

            return res.status(200).json({
                success: true,
                liked: false,
                message: "Comment unliked successfully.",
                likesCount
            });
        }
    } catch (error) {
        console.error("Error in likeSocialInteractionComment:", error);
        return res.status(500).json({ success: false, message: "Internal server error." });
    }
};

export const replyToSocialInteractionComment = async (req, res) => {
    try {
        const { commentId } = req.body; // this is parent comment ID
        const { comment } = req.body;
        const user = req.user._id;

        const parent = await socialIntrectionCommentsModel.findById(commentId);

        if (!parent) {
            return res.status(404).json({ success: false, message: "Parent comment not found." });
        }

        const reply = new socialIntrectionCommentsModel({
            user,
            socialIntrection: parent.socialIntrection,
            comment,
            parentComment: commentId
        });

        await reply.save();

        return res.status(201).json({
            success: true,
            message: "Reply added successfully.",
            data: reply
        });
    } catch (error) {
        console.error("Error replying to comment:", error);
        return res.status(500).json({ success: false, message: "Internal server error." });
    }
};
export const getAllCommentsWithReplies = async (req, res) => {
    try {
        const { socialIntrectionId } = req.params;
        const userId = req.user._id;

        // Get top-level comments
        const comments = await socialIntrectionCommentsModel.find({
            socialIntrection: socialIntrectionId,
            parentComment: null,
            isDeleted: false
        })
            .populate('user', 'name profileImage')
            .sort({ createdAt: -1 });

        // For each comment, get its replies and like info
        const enrichedComments = await Promise.all(comments.map(async (comment) => {
            const replies = await socialIntrectionCommentsModel.find({
                parentComment: comment._id,
                isDeleted: false
            })
                .populate('user', 'name profileImage')
                .sort({ createdAt: 1 });

            const enrichedReplies = replies.map(reply => ({
                ...reply.toObject(),
                likesCount: reply.likedBy.length,
                hasUserLiked: reply.likedBy.includes(userId)
            }));

            // Step 4: Get like count and user like for parent comment
            const commentLikesCount = await socialIntrectionCommentLikesModel.countDocuments({
                socialIntrectionComment: comment._id,
                isLike: true
            });

            const commentHasUserLiked = await socialIntrectionCommentLikesModel.exists({
                socialIntrectionComment: comment._id,
                user: userId,
                isLike: true
            });

            return {
                ...comment.toObject(),
                likesCount: commentLikesCount,
                hasUserLiked: !!commentHasUserLiked,
                replies: enrichedReplies
            };
        }));

        return res.status(200).json({
            success: true,
            message: "Comments fetched successfully.",
            data: enrichedComments
        });
    } catch (error) {
        console.error("Error fetching comments:", error);
        return res.status(500).json({ success: false, message: "Internal server error." });
    }
};

