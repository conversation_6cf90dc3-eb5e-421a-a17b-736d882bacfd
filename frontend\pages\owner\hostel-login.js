import React, { useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { useNavbar } from "@/components/home/<USER>";
import { useRouter } from "next/router";
import toast from "react-hot-toast";
import {
  getItemLocalStorage,
  removeItemLocalStorage,
} from "@/utils/browserSetting";
import { removeFirebaseToken } from "@/services/ownerflowServices";
import Head from "next/head";

const Page = () => {
  const { token, role, hopid, updateUserStatus, updateUserRole, updateHopId } =
    useNavbar();

  const router = useRouter();

  useEffect(() => {
    const handleLogoutAndRemoveToken = async () => {
      if (token && role === "hostel_owner" && hopid) {
        toast.success("Already Login! Dashboard..");
        router.push("/owner/dashboard");
      } else {
        // Clear local storage and update states
        removeItemLocalStorage("token");
        removeItemLocalStorage("name");
        removeItemLocalStorage("id");
        removeItemLocalStorage("role");
        removeItemLocalStorage("hopid");
        removeItemLocalStorage("email");
        removeItemLocalStorage("contact");
        updateUserStatus("");
        updateUserRole("");
        updateHopId("");

        const payload = {
          token: getItemLocalStorage("FCT"),
          userId: getItemLocalStorage("uid"),
        };

        try {
          await removeFirebaseToken(payload); // Await here
          console.log("FCM token removed successfully.");
          removeItemLocalStorage("FCT");
        } catch (error) {
          console.error("Error removing FCM token:", error);
        }

        removeItemLocalStorage("uid");
      }
    };

    handleLogoutAndRemoveToken(); // Call the async function
  }, [token, role]);

  return (
    <>
      <Head>
        <title>Hostel Owner Login | Mixdorm</title>
        <meta
          name='description'
          content='Sign In or Create a Mixdorm Account to List Your Property and Manage Bookings Easily.'
        />
      </Head>
      <div className='grid lg:grid-cols-2 sm:grid-cols-1 h-[100vh]'>
        <div className='h-full bg-[#40E0D0] flex justify-center items-center relative'>
          <Link href={token && role !== "user" ? "" : "/"} prefetch={false}>
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/logoWhite.svg`}
              className='absolute left-10 top-10'
              width={140}
              height={25}
              loading='lazy'
            />
          </Link>
          <div className='mt-[70px] md:mt-[0] mb-3'>
            <h2 className='text-black font-semibold xs:text:lg md:text-xl mb-10 text-center'>
              👋 Join Our Community
            </h2>
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/propertyAC_img-bg1.svg`}
              alt=''
              className='mx-auto block mb-10'
              width={247}
              height={210}
              loading='lazy'
            />
            <Link
              href='/owner/signup'
              className='text-sm text-primary-blue bg-black rounded-3xl p-3 text-center max-w-64 w-full mx-auto block'
              prefetch={false}
            >
              Create Property Account
            </Link>
          </div>
        </div>
        <div className='h-full bg-black flex justify-center items-center relative'>
          <div className='mb-3'>
            <h2 className='text-white font-semibold xs:text:lg md:text-xl xs:mb-[15px] mb-10 text-center'>
              👋 Welcome back
            </h2>
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/login_img.svg`}
              alt=''
              className='mx-auto block mb-10'
              width={267}
              height={210}
              loading='lazy'
            />
            <Link
              href='/owner/login'
              className='text-sm text-black bg-primary-blue rounded-3xl p-3 text-center max-w-64 w-full mx-auto block'
              prefetch={false}
            >
              Sign In
            </Link>
          </div>
        </div>
      </div>
    </>
  );
};

export default Page;
