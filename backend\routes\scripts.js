import express from 'express';
import Clouds from '../models/clouds.js';
import RoomInventory from '../models/ARI.js';
import { assignRatesId, assignRoomsId, downloadNewData, fixMissingImageIds, fixStateAnCity, syncEzeeRoomsAndRates, updateBaseRate, updateHostelworldImages, updateLowestRatesFromARI, updateRatePlanForRoom, updateStateAndCity } from '../controller/script.js';
import ezeeModel from '../models/ezee.js';
import avability from '../models/avability.js';
import property from '../models/properties.js';
import roomModel from '../models/room.js';
import ARI from '../models/ARI.js';
import currencyRates from '../utills/currencyRate.js';

const router = express.Router();

router.get('/ARI-Update', async (req, res) => {
  try {
    console.log("clouds");
    const cloudsData = await Clouds.find({
      url: "/ARIUpdate",
      "resp.Inventory": { $exists: true, $ne: [] }
    }).lean();

    console.log(`found ${cloudsData.length} ARI updates`);

    for (const record of cloudsData) {
      const inventories = record.resp?.Inventory || [];
      const currency = record.resp?.currency || "IDR";

      for (const item of inventories) {
        const roomId = item.ota_room_id;
        if (!roomId) continue;
        // ✅ Only process if it's a valid numeric string
        if (!/^\d+$/.test(String(roomId))) {
          console.warn(`Skipping non-numeric ota_room_id: ${roomId}`);
          continue;
        }
        const start = new Date(item.start_date);
        const end = new Date(item.end_date);
        const rate = parseFloat(item.rate) || 0;
        const availableCount = parseInt(item.units) || 0;

        // find room document for propertyId reference
        const roomDoc = await roomModel.findOne({ roomId: roomId }).lean();
        if (!roomDoc) {
          console.warn(`Room not found for ota_room_id: ${roomId}`);
          continue;
        }

        for (let dt = new Date(start); dt <= end; dt.setDate(dt.getDate() + 1)) {
          const currentDate = new Date(dt);

          // RoomInventory update
          await ARI.updateOne(
            {
              roomId: roomId,
              date: currentDate
            },
            {
              $set: {
                propertyId: roomDoc.property,
                currency,
                rate,
                // Convert to EUR
                baseRate: (() => {
                  const conversionRate = currencyRates.rates?.[currency];
                  if (!conversionRate || isNaN(conversionRate)) {
                    console.warn(`No valid conversion rate found for currency: ${currency}`);
                    return null; // or 0 if you want default
                  }
                  // Since currencyRates.base is EUR, divide to get EUR value
                  return parseFloat((rate / conversionRate).toFixed(2));
                })(),
                availableUnits: availableCount,
                close: item.close || false,
                closeArrival: item.closearr || false,
                closeDeparture: item.closedep || false,
                minLos: item.min_los || 1,
                maxLos: item.max_los || 0,
                minAdvanceOffset: item.min_advanced_offset || 0,
                maxAdvanceOffset: item.max_advanced_offset || 0,
                numAdultsIncluded: item.num_adults_included || 1,
                numChildrenIncluded: item.num_children_included || 0,
                updatedAt: new Date()
              }
            },
            { upsert: true }
          );

          // RoomAvailability update
          await avability.updateOne(
            {
              roomId: roomId,
              date: currentDate
            },
            {
              $set: {
                propertyId: roomDoc.property,
                availableUnits: availableCount
              }
            },
            { upsert: true }
          );
        }
      }
    }

    return res.status(200).json({
      success: true,
      message: "Cloud ARI updates pushed to RoomInventory & RoomAvailability successfully"
    });
  } catch (error) {
    console.error("ARI-Update error", error);
    return res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

router.get('/ezee-ari-update', async (req, res) => {
  try {
    console.log("Fetching eZee availability updates...");

    // Filter only eZee availability updates
    const ezeeData = await ezeeModel.find(
      {
        url: "/push-inventory",
        "resp.res_request.roomtype": { $exists: true, $ne: [] }
      }
    )
      .sort({ _id: -1 })
      .lean();

    console.log(`Found ${ezeeData.length} eZee records`);

    for (const record of ezeeData) {
      const roomTypes = record.resp?.res_request?.roomtype || [];
      const HotelCode = req.body?.res_request?.authentication?.[0]?.hotelcode?.[0];
      for (const room of roomTypes) {
        const roomtype = req.body?.res_request?.roomtype?.[0];
        const roomId = room?.roomtypeid?.[0];
        if (isNaN(roomId)) {
          console.log(`Skipping invalid roomId: ${roomId}`);
          continue;
        }
        const fromDate = new Date(room.fromdate?.[0]);
        const toDate = new Date(room.todate?.[0]);
        const availableUnits = parseInt(room.availability?.[0]) || 0;
        if (!roomId || !fromDate || !toDate) continue;

        let current = new Date(fromDate);
        while (current <= toDate) {
          const date = new Date(current);

          await avability.updateOne(
            { roomId, date },
            {
              $set: {
                availableUnits,
                chanelPartner: "ezee"
              }
            },
            { upsert: true }
          );
          console.log("SUcees")
          current.setDate(current.getDate() + 1);
        }
      }
      await property.updateOne(
        { otaId: HotelCode },
        { $set: { isPropertyLive: true } }
      );
    }

    return res.status(200).json({
      success: true,
      message: "eZee availability successfully pushed to RoomsAvability"
    });

  } catch (error) {
    console.error("eZee ARI update error:", error);
    return res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

router.get('/room-rates-update', updateRatePlanForRoom)
router.get('/update-base-rate', updateBaseRate)
router.post('/new-property-data-fetch', downloadNewData)
router.post('/images-update', updateHostelworldImages)

router.post('/ezee-rooms-update', syncEzeeRoomsAndRates)

router.post('/rooms-id-update', assignRoomsId)
router.post('/rates-id-update', assignRatesId)
router.post('/property-list-rate-live', updateLowestRatesFromARI)
router.post('/add-default-images-ids', fixMissingImageIds)
router.post('/update-state-city', updateStateAndCity)



export default router;
