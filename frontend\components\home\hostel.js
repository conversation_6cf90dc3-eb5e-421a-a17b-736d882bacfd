/* eslint-disable react/no-unescaped-entities */
import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay, Navigation } from "swiper/modules";
import Link from "next/link";
import { eventApi } from "@/services/webflowServices";
import { useNavbar } from "./navbarContext";
import "swiper/css/navigation";
import countries from "world-countries";

const Hostel = () => {
  // eslint-disable-next-line no-unused-vars
  const [selectedImage, setSelectedImage] = useState(1);
  const [activityData, setActivityData] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [currentPage, setCurrentPage] = useState(1);
  // eslint-disable-next-line no-unused-vars
  const [totalPages, setTotalPages] = useState(1);
  const [currencyData, setCurrencyData] = useState({});
  const limit = 10;
  const isFirstRender = useRef(null);
  const { token } = useNavbar();
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(typeof window !== 'undefined' && window.innerWidth <= 768);
    };

    // Initial check
    checkMobile();

    // Add resize listener
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', checkMobile);
      return () => window.removeEventListener('resize', checkMobile);
    }
  }, []);


  useEffect(() => {
    const fetchActivityData = async () => {
      try {
        const response = await eventApi(currentPage, limit);

        setActivityData(response?.data?.data?.events || []);
        setTotalPages(response?.data?.data?.pagination?.totalPages || 1);
      } catch (error) {
        console.error("Error fetching event data:", error);
      }
    };
    if (!isFirstRender.current) {
      fetchActivityData();
    } else {
      isFirstRender.current = false;
    }
  }, [currentPage, token]);

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);

      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);

  const getCurrencySymbol = (currencyCode) => {
    return currencyData[currencyCode] || currencyCode;
  };




  return (
    <section className="w-full bg-white md:pb-16 pb-8 lg:px-6 relative">
      <div className="container overflow-hidden">
        <h2 className="font-semibold text-black font-manrope md:text-3xl sm:text-2xl text-xl">
          Discover Events and Activities with Mix
          <span className="text-[#40E0D0]">dorm</span>
        </h2>
        <p className="block mt-3 text-sm text-justify sm:text-start sm:text-base font-medium text-[#737373] font-manrope mb-6 text-[15px]">
          Travel isn't just about the destination—it's about the experiences you
          have along the way. With Mixdorm's Eventsfeature, you can explore
          exciting hostel activities and city-wide events wherever you are. From
          social gatherings to cultural festivals, Mixdorm connects you to
          events that make your trip unforgettable.
        </p>
        {
          isMobile ?
            <Swiper
              pagination={false}
              slidesPerView={3}
              modules={[Autoplay, Navigation]}
              autoplay={{ delay: 1500, pauseOnMouseEnter: true }}

              className="mySwiper myCustomSwiper"
              onSlideChange={(swiper) => setSelectedImage(swiper.realIndex)}
              loop
              breakpoints={{
                0: {
                  slidesPerView: 1.1,
                  spaceBetween: 10,
                },
                400: {
                  slidesPerView: 1.2,
                  spaceBetween: 10,
                },
                575: {
                  slidesPerView: 1.5,
                  spaceBetween: 10,
                },
                640: {
                  slidesPerView: 2,
                  spaceBetween: 20,
                },
                768: {
                  slidesPerView: 2,
                  spaceBetween: 40,
                },
                1024: {
                  slidesPerView: 3,
                  spaceBetween: 50,
                },
              }}
            >

              {activityData.length > 0 ? (
                activityData.map((slide, index) => (
                  <SwiperSlide
                    key={slide?._id}
                    className=""
                    onClick={() => setSelectedImage(index)}
                  >
                    <div className="relative w-full flex group ease-in-out">
                      <div className="text-black font-thin text-center absolute right-[14px] top-[14px] p-2 bg-[#40E0D0] rounded-[15px] uppercase text-[13px] sm:w-[60px] sm:h-[60px] w-12">
                        {new Date(slide?.startDate).toLocaleString('en-US', { month: 'short' }).toUpperCase()}
                        <span className="block sm:text-[26px] text-lg font-bold sm:leading-5 leading-3">
                          {new Date(slide?.startDate).getDate()}
                        </span>
                      </div>
                      <div className="absolute bottom-[2px] h-[40px] w-[99%] left-[2px] bg-black bg-opacity-40 opacity-1 rounded-b-[22px] group-hover:opacity-100 duration-300 ease-in-out"></div>

                      <div className=" top-[83%]  opacity-1 group-hover:opacity-100 duration-300 ease-in-out  absolute bottom-0 left-0 w-full sm:py-2 sm:px-3 sm:pb-8 pb-3 px-4">
                        <div className="w-full flex flex-col  justify-between items-center">
                          {/* <div className="w-full flex items-center">
                      <div class="flex -space-x-3 rtl:space-x-reverse">
                        <Image
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/avatar.png`}
                          alt="Ticket Icon"
                          className="border-2 border-white rounded-full sm:h-10 sm:w-10 h-7 w-7"
                          width={42}
                          height={42}
                          loading="lazy"
                        />
                        <Image
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/avatar2.png`}
                          alt="Ticket Icon"
                          className="border-2 border-white rounded-full sm:h-10 sm:w-10 h-7 w-7"
                          width={42}
                          height={42}
                          loading="lazy"
                        />
                        <Image
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/avatar3.png`}
                          alt="Ticket Icon"
                          className="border-2 border-white rounded-full sm:h-10 sm:w-10 h-7 w-7"
                          width={42}
                          height={42}
                          loading="lazy"
                        />
                      </div>
                      <h5 className="text-white text-sm ml-3">
                        {slide?.peopleGoing || 120}
                        <span className="text-bold text-sm"> People Going</span>
                      </h5>
                    </div> */}
                          <div className="w-full flex mt-0 justify-between  items-start xl:items-start ">
                            <h2 className="text-xl font-bold flex items-start">
                              {/* SHOW */}
                              <br />
                              <div className="comman-tooltip before:!top-[-30px] before:!left-[140px]"
                                data-title={slide?.title}>
                                <span className="text-xl font-bold text-white line-clamp-1">
                                  {slide?.title}
                                </span>
                              </div>
                            </h2>
                            <h2 className="text-xl font-bold text-white text-end ">
                              {getCurrencySymbol(slide?.currency || "USD")}{slide?.price}
                            </h2>
                          </div>
                        </div>
                      </div>
                      <Image
                        src={slide?.attachment?.[0]?.url || `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/event_1.jpg`}
                        alt={`Slide ${slide?._id}`}
                        className="min-w-[300px] h-[230px] object-cover rounded-3xl border-2 border-white"
                        width={402}
                        height={231}
                        loading="lazy"
                      />
                    </div>

                  </SwiperSlide>
                ))
              ) : (
                <SwiperSlide>No</SwiperSlide> // Placeholder slide
              )}



            </Swiper>
            :
            <Swiper
              pagination={false}
              slidesPerView={3}
              autoplay={{ delay: 3000, pauseOnMouseEnter: true }}
              modules={[Pagination, Autoplay, Navigation]}
              navigation={true}
              className="mySwiper myCustomSwiper mt-0 discover-event-slider overflow-visible"
              onSlideChange={(swiper) => setSelectedImage(swiper.realIndex)}
              loop
              breakpoints={{
                0: {
                  slidesPerView: 1.1,
                  spaceBetween: 10,
                },
                400: {
                  slidesPerView: 1.2,
                  spaceBetween: 10,
                },
                575: {
                  slidesPerView: 1.5,
                  spaceBetween: 10,
                },
                640: {
                  slidesPerView: 2,
                  spaceBetween: 20,
                },
                768: {
                  slidesPerView: 2,
                  spaceBetween: 40,
                },
                1024: {
                  slidesPerView: 3,
                  spaceBetween: 50,
                },
              }}
            >
              {activityData.map((slide, index) => (
                <SwiperSlide
                  key={slide?._id}
                  className=""
                  onClick={() => setSelectedImage(index)}
                >
                  <div className="relative w-full flex group ease-in-out">
                    <div className="text-black font-thin text-center absolute right-[14px] top-[14px] p-2 bg-[#40E0D0] rounded-[15px] uppercase text-[13px] sm:w-[60px] sm:h-[60px] w-12">
                      {new Date(slide?.startDate).toLocaleString('en-US', { month: 'short' }).toUpperCase()}
                      <span className="block sm:text-[26px] text-lg font-bold sm:leading-5 leading-3">
                        {new Date(slide?.startDate).getDate()}
                      </span>
                    </div>
                    <div className="absolute bottom-[2px] h-[40px] w-[99%] left-[2px] bg-black bg-opacity-40 opacity-1 rounded-b-[22px] group-hover:opacity-100 duration-300 ease-in-out"></div>

                    <div className=" top-[80%]  opacity-1 group-hover:opacity-100 duration-300 ease-in-out  absolute bottom-0 left-0 w-full sm:py-2 sm:px-3 sm:pb-8 pb-3 px-4">
                      <div className="w-full flex flex-col  justify-between items-center">
                        {/* <div className="w-full flex items-center">
                      <div class="flex -space-x-3 rtl:space-x-reverse">
                        <Image
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/avatar.png`}
                          alt="Ticket Icon"
                          className="border-2 border-white rounded-full sm:h-10 sm:w-10 h-7 w-7"
                          width={42}
                          height={42}
                          loading="lazy"
                        />
                        <Image
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/avatar2.png`}
                          alt="Ticket Icon"
                          className="border-2 border-white rounded-full sm:h-10 sm:w-10 h-7 w-7"
                          width={42}
                          height={42}
                          loading="lazy"
                        />
                        <Image
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/avatar3.png`}
                          alt="Ticket Icon"
                          className="border-2 border-white rounded-full sm:h-10 sm:w-10 h-7 w-7"
                          width={42}
                          height={42}
                          loading="lazy"
                        />
                      </div>
                      <h5 className="text-white text-sm ml-3">
                        {slide?.peopleGoing || 120}
                        <span className="text-bold text-sm"> People Going</span>
                      </h5>
                    </div> */}
                        <div className="w-full flex mt-0 justify-between  items-start xl:items-start ">
                          <h2 className="text-xl font-bold flex items-start">
                            {/* SHOW */}
                            <br />
                            <div className="comman-tooltip before:!top-[-30px] before:!left-[140px]"
                              data-title={slide?.title}>
                              <span className="text-xl font-bold text-white line-clamp-1">
                                {slide?.title}
                              </span>
                            </div>
                          </h2>
                          <h2 className="text-xl font-bold text-white text-end ">
                            {getCurrencySymbol(slide?.currency || "USD")}{slide?.price}
                          </h2>
                        </div>
                      </div>
                    </div>
                    <Image
                      src={slide?.attachment?.[0]?.url || `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/event_1.jpg`}
                      alt={`Slide ${slide?._id}`}
                      className="min-w-[300px] h-[230px] object-cover rounded-3xl border-2 border-white"
                      width={402}
                      height={231}
                      loading="lazy"
                    />
                  </div>

                </SwiperSlide>

              ))}

            </Swiper>
        }


        <div className="text-center mt-10 sm:flex hidden justify-center items-center gap-2">
          <Link
            href="/discover-event"
            aria-label="See all discoverable events"
            className="text-sm font-semibold text-black bg-primary-blue rounded-4xl py-4 px-12 hover:bg-sky-blue-750"
            prefetch={false}
          >
            How to Join?
          </Link>
        </div>
      </div>
    </section>
  );
};

export default Hostel;
