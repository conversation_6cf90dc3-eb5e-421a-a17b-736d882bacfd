 
import React from "react";

const Input = ({ type, placeholder, value, onChange, className ,name}) => {
  return (
    <input
      type={type}
      placeholder={placeholder}
      name={name}
      value={value}
      onChange={onChange}
      className={`w-full text-xs font-normal  mb-5 px-4 py-2.5 mt-1 bg-transparent border-1.5 rounded-full border-gray-350 outline-none focus:outline-none placeholder:text-gray-50/40 text-gray-50 ${className}`}
    />
  );
};

Input.defaultProps = {
  placeholder: "",
  value: "",
  onChange: () => {},
  className: "",
};

export default Input;
