import Settings from '../models/settings.js';

// Service to update settings by key
const updateSettingByKey = async (key, newValue) => {
    try {
        const updatedSetting = await Settings.findOneAndUpdate(
            { key },
            { $set: { value: newValue } },
            { new: true, upsert: false }
        );

        if (!updatedSetting) {
            throw new Error('Setting not found');
        }

        return updatedSetting;
    } catch (error) {
        throw new Error(`Error updating setting: ${error.message}`);
    }
};

// Service to get settings by key
const getSettingByKey = async (key) => {
    try {
        const setting = await Settings.findOne({ key });

        if (!setting) {
            throw new Error('Setting not found');
        }

        return setting;
    } catch (error) {
        throw new Error(`Error fetching setting: ${error.message}`);
    }
};

export { updateSettingByKey, getSettingByKey };