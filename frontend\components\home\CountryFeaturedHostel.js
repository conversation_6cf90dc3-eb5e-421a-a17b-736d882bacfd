import React, { useEffect, useRef, useState } from "react";
import HostelCardSlider from "./hostelCardSlider";
import { getFeaturedHostelApi } from "@/services/webflowServices";
import { useNavbar } from "./navbarContext";
import dynamic from "next/dynamic";
import { motion } from "framer-motion";
import { ArrowLeft, ArrowRight } from "lucide-react";
// import { FaArrowLeft, FaArrowRight } from "react-icons/fa6";

const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

const CountryFeaturedHostel = () => {
  const [featuredHostelData, setFeaturedHostelData] = useState([]);
  const [isUpdate, setIsUpdateData] = useState(false);
  const [loading, setLoading] = useState(false);

  const isFirstRender = useRef(null);
  const { currencyCode2, token } = useNavbar();

  useEffect(() => {
    const fetchFeaturedHostelData = async () => {
      try {
        const response = await getFeaturedHostelApi(currencyCode2 || "USD");

        setFeaturedHostelData(response?.data?.data?.properties || []);
      } catch (error) {
        console.error("Error fetching stay data:", error);
      }
    };
    if (!isFirstRender.current) {
      fetchFeaturedHostelData();
    } else {
      isFirstRender.current = false;
    }
  }, [currencyCode2, isUpdate, token]);

  return (
    <div
      className="bg-repeat-round w-full"
      style={{
        backgroundImage: `url(${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/featured-black-bg.png)`,
        backgroundSize: "cover",
      }}
    >
      <Loader open={loading} />
      <section className="w-full md:pb-16 pb-10 lg:px-6 py-10">
        <div className="container py-10">
          <div className="flex justify-between items-center mt-8 mb-6">
            <motion.h2
              initial={{ x: -100, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
              viewport={{ once: false }}
              className=" text-white font-manrope font-normal text-4xl md:text-5xl mt-0 xxxl:mt-12 "
            >
              <span className="text-primary-blue font-manrope font-normal text-4xl md:text-5xl">
                Featured{" "}
              </span>
              Hostels
            </motion.h2>
            <div className="flex gap-2">
              <div className="slider-button-prev cursor-pointer custom-nav bg-primary-blue">
                <ArrowLeft size={18} />
              </div>
              <div className="slider-button-next cursor-pointer custom-nav">
                <ArrowRight size={18} />
              </div>
            </div>
          </div>
          <div>
            <motion.p
              initial={{ x: -100, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
              viewport={{ once: false }}
              className=" text-white text-[15px] font-manrope font-normal mb-8"
            >
              Explore some of the top-rated hostels across the world, offering
              the perfect blend of affordability, comfort, and unbeatable
              experiences. Whether you&apos;re <br /> seeking a vibrant social scene, a
              peaceful retreat, or an adventure hub, these handpicked hostels
              are sure to meet your travel needs. Check out our top 8 <br /> featured
              hostels,each with unique amenities and competitive room rates.
            </motion.p>
          </div>

          <HostelCardSlider
            featuredHostelData={featuredHostelData}
            setIsUpdateData={setIsUpdateData}
            setLoading={setLoading}
          />
          <div className="w-full flex md:hidden items-center justify-center mt-4">
            <motion.button
              initial={{ x: -100, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
              viewport={{ once: false }} className="bg-primary-blue text-black w-[170px] h-[44px] rounded-3xl font-manrope text-xs font-bold">See all</motion.button>
          </div>
        </div>
        <div className="w-full hidden  md:flex items-center justify-center">
          <motion.button
            initial={{ x: -100, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
            viewport={{ once: false }} className="bg-primary-blue text-black w-[170px] h-[44px] rounded-3xl font-manrope text-xs font-bold">See all</motion.button>
        </div>
      </section>

    </div>
  );
};

export default CountryFeaturedHostel;
