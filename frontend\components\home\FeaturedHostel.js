import React, { useEffect, useRef, useState } from "react";
import HostelCardSlider from "./hostelCardSlider";
import { getFeaturedHostelApi } from "@/services/webflowServices";
import { useNavbar } from "./navbarContext";
import dynamic from "next/dynamic";
// import { motion } from "framer-motion";
import { ArrowLeft, ArrowRight } from "lucide-react";
import Link from "next/link";
// import { FaArrowLeft, FaArrowRight } from "react-icons/fa6";

const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

const FeaturedHostel = () => {
  const [featuredHostelData, setFeaturedHostelData] = useState([]);
  const [featuredHostelAbout, setFeaturedHostelAbout] = useState([]);
  const [isUpdate, setIsUpdateData] = useState({});
  const [loading, setLoading] = useState(false);
  const isFirstRender = useRef(null);
  const isUpdating = useRef(false);
  const { currencyCode2, token } = useNavbar();

  useEffect(() => {
    const fetchFeaturedHostelData = async () => {
      try {
        const response = await getFeaturedHostelApi(currencyCode2 || "USD");
        setFeaturedHostelData(response?.data?.data?.properties || []);
        setFeaturedHostelAbout(
          response?.data?.data?.about ||
            "Explore some of the top-rated hostels across the world, offering the perfect blend of affordability, comfort, and unbeatable experiences.Whether you're seeking a vibrant social scene, a peaceful retreat,or an adventure hub, these handpicked hostels are sure to meet your travel needs. Check out our top 8 featured hostels,each with unique amenities and competitive room rates."
        );
      } catch (error) {
        console.error("Error fetching stay data:", error);
      }
    };
    if (!isFirstRender.current) {
      fetchFeaturedHostelData();
    } else {
      isFirstRender.current = false;
    }
  }, [currencyCode2, token]);

  // Handle updates from like/unlike actions
  useEffect(() => {
    const updateKeys = Object.keys(isUpdate);
    console.log("Update Keys:", updateKeys);
    if (updateKeys.length > 0 && !isUpdating.current) {
      isUpdating.current = true;
      const hostelId = updateKeys[0];
      const updatedHostel = isUpdate[hostelId];
      console.log("Update Keys22:", isUpdate[hostelId]);

      if (updatedHostel) {
        setFeaturedHostelData(prevData => {
          const updatedData = prevData.map(hostel => {
            if (hostel._id === hostelId) {
              // Create a new object with the updated liked state
              const updatedHostelData = {
                ...hostel,
                ...updatedHostel,
                liked: Boolean(updatedHostel.liked) // Ensure liked is a boolean
              };
              console.log("Updated Hostel Data:", updatedHostelData);
              return updatedHostelData;
            }
            return hostel;
          });
          return updatedData;
        });
      }
      
      // Use setTimeout to ensure state updates are processed
      setTimeout(() => {
        setIsUpdateData({});
        isUpdating.current = false;
      }, 0);
    }
  }, [isUpdate]);

  console.log("Featured Hostel Data:", featuredHostelData);

  return (
    <div
      className='bg-repeat-round w-full mt-4'
      style={{
        backgroundImage: `url(${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/right-cross-bg.webp)`,
        backgroundSize: "cover",
      }}
    >
      <Loader open={loading} />
      <section className='w-full md:pb-16 pb-10 lg:px-6 py-10'>
        <div className='container xs:py-10 py-4'>
          <div className='flex justify-between items-center my-8'>
            {/* <motion.h2
             initial={{ x: -100, opacity: 0 }}
             whileInView={{ x: 0, opacity: 1 }}
             transition={{ duration: 0.8, ease: "easeOut" }}
             viewport={{ once: false }}  */}
            <h2 className=" text-white font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl mt-0 xxxl:mt-12 ">
              <span className="text-primary-blue font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl">Featured </span>
              Hostels
            </h2>
            {/* </motion.h2> */}
            {/* <motion.div
              initial={{ x: 100, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              viewport={{ once: false }} */}
            <div
              className="text-center sm:hidden block">
              <Link
                href="/featuredhostel"
                className="text-sm font-semibold text-black bg-primary-blue rounded-4xl py-2 px-5 hover:bg-sky-blue-750"
                prefetch={false}
              >
                See All
              </Link>
            </div>
            {/* </motion.div> */}

            <div className={`gap-2 ${featuredHostelData?.length > 4 ? 'xl:flex' : 'xl:hidden'
              } ${featuredHostelData?.length > 3.5 ? 'lg:flex' : 'lg:hidden'} ${featuredHostelData?.length > 2.5 ? 'md:flex' : 'md:hidden'} ${featuredHostelData?.length > 2 ? 'sm:flex' : 'sm:hidden'} ${featuredHostelData?.length > 1.5 ? 'hidden' : 'hidden'}`}
            >
              <div className="slider-button-prev cursor-pointer"><ArrowLeft size={18} /></div>
              <div className="slider-button-next cursor-pointer"><ArrowRight size={18} /></div>
            </div>

          </div>
          {/* <motion.p
            initial={{ x: -100, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: false }} */}
            <p
            className='hidden sm:block mt-2 text-base font-medium text-white/60 font-manrope w-[80%]'
            >
            {featuredHostelAbout}
            </p>
          {/* </motion.p> */}

          <HostelCardSlider
            featuredHostelData={featuredHostelData}
            setIsUpdateData={setIsUpdateData}
            setLoading={setLoading}
          />

          {/* <motion.div
            initial={{ y: 80, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: false }} */}
          <div
            className="text-center mt-10 hidden sm:block">
            <Link
              href="/featuredhostel"
              className="text-sm font-semibold text-black bg-primary-blue rounded-4xl py-4 px-12 hover:bg-sky-blue-750"
              prefetch={false}
            >
              See All
            </Link>
          </div>
          {/* </motion.div> */}

        </div>

      </section>
    </div>
  );
};

export default FeaturedHostel;
