import React, { useEffect, useRef, useState } from "react";
import { FiEye } from "react-icons/fi";
import { TfiPencilAlt } from "react-icons/tfi";
import Link from "next/link";
import UsersFilter from "@/components/superadmin/UserFilter";
import { userListAdmin } from "@/services/adminflowServices";
import Pagination from "@/components/common/commonPagination";
import toast from "react-hot-toast";
import Loader from "@/components/loader/loader";

const User = () => {
  const [usersData, setUsersData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalData, setTotalData] = useState();
  const isFirstRender = useRef(null);

  useEffect(() => {
    if (!isFirstRender.current) {
      fetchList(currentPage);
    } else {
      isFirstRender.current = false;
    }
  }, [currentPage, itemsPerPage]);

  const fetchList = async (page) => {
    setLoading(true);

    try {
      const response = await userListAdmin(page, itemsPerPage);

      if (response.status == 200) {
        setUsersData(response?.data?.data?.users);
        setTotalPages(response?.data?.data?.pagination?.totalPages || 1);
        setTotalData(response?.data?.data?.pagination?.totalUsers);
      }
      if (response.status !== 200) {
        toast.error(response?.data?.message);
      }
    } catch (error) {
      toast.error(error || "Something went wrong!");
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  return (
    <>
      <Loader open={loading} />
      <div className='lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616] h-screen'>
        <h2 className='text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100'>
          User
        </h2>
        <div className='mt-5'>
          <UsersFilter />
        </div>
        <div className='overflow-x-auto mt-5 rounded-xl border dark:bg-black   dark:border-none'>
          <table className='min-w-full divide-y bg-white rounded-xl  divide-gray-200 dark:bg-black  dark:border-none'>
            <thead>
              <tr className=' '>
                <th className=' py-6 bg-white text-center text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]'>
                  NAME
                </th>
                <th className='pl-1  py-6 bg-white text-center text-sm font-poppins  font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]'>
                  MOBILE
                </th>
                <th className=' py-6 bg-white text-center text-sm  font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]'>
                  DOB
                </th>
                <th className='   py-6 bg-white text-center text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]'>
                  EMAIL
                </th>
                <th className='pr-4 pl-5  py-6 bg-white text-center text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]'>
                  COUNTRY
                </th>
                <th className='pr-4 pl-5  py-6 bg-white text-center text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]'>
                  SUBSCRIPTION
                </th>
                <th className='   py-6 bg-white text-center text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]'>
                  STATUS
                </th>
                <th className='pr-3 lg:pr-6 py-6 bg-white text-center text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]'>
                  ACTION
                </th>
              </tr>
            </thead>
            <tbody className='divide-y divide-gray-200 border-y dark:divide-gray-400 text-black/70 dark:text-[#757575] '>
              {usersData.length > 0 ? (
                usersData.map((user) => (
                  <tr key={user._id}>
                    <td className='text-center whitespace-nowrap px-5 text-sm font-medium font-poppins '>
                      {user.name?.first || "-"} {user.name?.last || ""}
                    </td>
                    <td className='text-center whitespace-nowrap px-5 text-sm font-medium font-poppins'>
                      {user.contact || "-"}
                    </td>
                    <td className='text-center whitespace-nowrap px-5 text-sm font-medium font-poppins'>
                      {user.dob ? new Date(user.dob).toLocaleDateString() : "-"}
                    </td>
                    <td className='text-center whitespace-nowrap px-5 text-sm font-medium font-poppins'>
                      {user.email}
                    </td>
                    <td className='text-center whitespace-nowrap px-5 text-sm font-medium font-poppins'>
                      {user.country || "-"}
                    </td>
                    <td className='text-center whitespace-nowrap px-5 text-sm font-medium font-poppins'>
                      {user.isPremiumUser ? "Premium" : "-"}
                    </td>
                    <td className='text-center whitespace-nowrap px-5 text-sm font-medium font-poppins'>
                      <div
                        className={`py-1 px-0 rounded text-sm font-medium font-poppins ${
                          user.isEmailVerified
                            ? "text-primary-blue bg-[#CCEFED] dark:bg-[#28676382]"
                            : "text-red-600 bg-red-100 dark:bg-[#953c3c73]"
                        } font-medium`}
                      >
                        {user.isEmailVerified ? "Verified" : "Unverified"}
                      </div>
                    </td>
                    <td className='py-5 px-2 flex text-center'>
                      <Link
                        href='/superadmin/dashboard/user-detail'
                        className='border p-2 rounded-l-lg text-black/75 dark:text-[#B6B6B6]'
                        prefetch={false}
                      >
                        <FiEye className='hover:text-blue-400' />
                      </Link>
                      <Link
                        href='/superadmin/dashboard/user-edit'
                        className='border p-2 rounded-r-lg text-black/75 dark:text-[#B6B6B6]'
                        prefetch={false}
                      >
                        <TfiPencilAlt className='hover:text-yellow-400' />
                      </Link>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan='8'
                    className='px-6 py-8 text-center text-gray-500'
                  >
                    No Users Found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        {usersData?.length > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalData || 0}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
          />
        )}
      </div>
    </>
  );
};

export default User;
