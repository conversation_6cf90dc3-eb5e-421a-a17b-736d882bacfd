"use client";
import Image from "next/image";
import React from "react";
import { FaArrowTrendDown, FaArrowTrendUp } from "react-icons/fa6";
import FilterBox from "@/components/superadmin/FliterSearch";
import { IoArrowRedoOutline } from "react-icons/io5";
import { FiEye } from "react-icons/fi";

const Payment = () => {
  const paymentData = [
    {
      id: 1,
      reserveId: "#258645",
      username: "<PERSON>",
      method: "VISA",
      amount: "$256",
      time: "03:50",
      date: "04/02/24",
      status: "Sent",
    },
    {
      id: 2,
      reserveId: "#258645",
      username: "<PERSON>",
      method: "VISA",
      amount: "$256",
      time: "03:50",
      date: "04/02/24",
      status: "Sent",
    },
    {
      id: 3,
      reserveId: "#258645",
      username: "<PERSON>",
      method: "VISA",
      amount: "$256",
      time: "03:50",
      date: "04/02/24",
      status: "Sen<PERSON>",
    },
    {
      id: 4,
      reserveId: "#258645",
      username: "<PERSON>",
      method: "VISA",
      amount: "$256",
      time: "03:50",
      date: "04/02/24",
      status: "Sent",
    },
    {
      id: 5,
      reserveId: "#258645",
      username: "Alexander",
      method: "VISA",
      amount: "$256",
      time: "03:50",
      date: "04/02/24",
      status: "Sent",
    },
  ];

  return (
    <div className="w-full p-7 bg-sky-blue-20">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins ">
        Booking Revenue
      </h2>
      <div className="grid grid-cols-2 gap-4 lg:grid-cols-3 md:gap-x-5 p-2">
        <div className="h-auto rounded-2xl bg-white ">
          <div className="flex justify-between p-4">
            <div>
              <h2 className="text-black/75">Total User</h2>
              <h1 className="font-bold text-3xl pt-4">40,689</h1>
            </div>
            <div className="bg-[#E5E4FF] h-12 w-12 flex items-center justify-center rounded-full">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Total.png`}
                width={32}
                height={24}
                loading="lazy"
                alt="User"
                className="h-4 w-6"
              />
            </div>
          </div>
          <span className="text-primary-blue flex items-center gap-2 p-4">
            <FaArrowTrendUp />
            8.5%
            <p className="text-black/75">Up from yesterday</p>
          </span>
        </div>
        <div className="h-auto rounded-2xl bg-white ">
          <div className="flex justify-between p-4">
            <div>
              <h2 className="text-black/75">New Booking</h2>
              <h1 className="font-bold text-3xl pt-4">12112</h1>
            </div>
            <div className="bg-[#FFF3D6] h-12 w-12 flex items-center justify-center rounded-full">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Booking.png`}
                width={20}
                loading="lazy"
                height={20}
                alt="booking"
                className="h-5 w-5"
              />
            </div>
          </div>
          <span className="text-primary-blue flex items-center gap-2 p-4">
            <FaArrowTrendUp />
            1.3%
            <p className="text-black/75">Up from past week</p>
          </span>
        </div>
        <div className="h-auto rounded-2xl bg-white ">
          <div className="flex justify-between p-4">
            <div>
              <h2 className="text-black/75">Total Booking</h2>
              <h1 className="font-bold text-3xl pt-4">9,000</h1>
            </div>
            <div className="bg-[#D9F7E8] h-12 w-12 flex items-center justify-center rounded-full">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/TotalBooking.png`}
                width={20}
                height={20}
                alt="BookingT"
                className="h-5 w-5"
                loading="lazy"
              />
            </div>
          </div>
          <span className="text-red-600 flex items-center gap-2 p-4">
            <FaArrowTrendDown />
            4.3%
            <p className="text-black/75">Down from yesterday</p>
          </span>
        </div>
      </div>
      <div className="mt-5">
        <FilterBox />
      </div>
      <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border">
        <table className="min-w-full divide-y  bg-white rounded-xl divide-gray-200">
          <thead>
            <tr className=" ">
              <th className="px-5 py-6 bg-white text-left text-base font-semibold text-black uppercase tracking-wider">
                RESERVATION ID
              </th>
              <th className="pr-4 py-6 bg-white text-left text-base font-semibold text-black uppercase tracking-wider">
                USERNAME
              </th>
              <th className="pr-4 py-6 bg-white text-left text-base font-semibold text-black uppercase tracking-wider">
                METHOD
              </th>
              <th className="pr-4 py-6 bg-white text-left text-base font-semibold text-black uppercase tracking-wider">
                AMOUNT
              </th>
              <th className="pr-4 py-6 bg-white text-left text-base font-semibold text-black uppercase tracking-wider">
                DATE
              </th>
              <th className="pr-4 py-6 bg-white text-left text-base font-semibold text-black uppercase tracking-wider">
                STATUS
              </th>
              <th className=" py-6 bg-white text-left text-base font-semibold text-black uppercase tracking-wider">
                ACTION
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 border text-black/70">
            {paymentData.map((payment) => (
              <tr key={payment.id}>
                <td className="whitespace-nowrap px-5">{payment.reserveId}</td>
                <td className="whitespace-nowrap">{payment.username}</td>
                <td className="whitespace-nowrap">{payment.method}</td>
                <td className="whitespace-nowrap">{payment.amount}</td>
                <td className="whitespace-nowrap">{payment.date}</td>
                <td>
                  <button className="text-primary-blue font-medium py-1 px-3 rounded bg-[#CCEFED]">
                    {payment.status}
                  </button>
                </td>

                <td className=" py-5 px-2 flex">
                  <button className="border p-2 rounded-l-lg text-black/75">
                    <IoArrowRedoOutline />
                  </button>
                  <button className=" border p-2 rounded-r-lg text-black/75">
                    <FiEye />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Payment;
