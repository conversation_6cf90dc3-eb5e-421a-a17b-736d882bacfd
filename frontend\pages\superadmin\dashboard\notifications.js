"use client";
import React, { useState } from "react";
import { Plus } from "lucide-react";
import { GrAnnounce } from "react-icons/gr";
import { IoPaperPlaneOutline } from "react-icons/io5";
import NotificationFilter from "@/components/superadmin/NotificationFilter";
import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight,
} from "react-icons/md";
import { HiOutlineCog6Tooth } from "react-icons/hi2";
import { FiEye } from "react-icons/fi";
import { TfiPencilAlt } from "react-icons/tfi";
import { FaRegTrashCan } from "react-icons/fa6";
import Link from "next/link";

const Notification = () => {
  const [activeButton, setActiveButton] = useState("User");

  const userNotificationsData = [
    {
      id: 1,
      title: (
        <div>
          <span className="block">River rafting in </span>
          <span className="block">rishikesh</span>
        </div>
      ),
      message:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: "Sent",
    },
    {
      id: 2,
      title:(
        <div>
          <span className="block">River rafting in </span>
          <span className="block">rishikesh</span>
        </div>
      ),
      message:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: "Sent",
    },
    {
      id: 3,
      title: (
        <div>
          <span className="block">River rafting in </span>
          <span className="block">rishikesh</span>
        </div>
      ),
      message:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: "Sent",
    },
    {
      id: 4,
      title: (
        <div>
          <span className="block">River rafting in </span>
          <span className="block">rishikesh</span>
        </div>
      ),
      message:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: "Sent",
    },
    {
      id: 5,
      title: (
        <div>
          <span className="block">River rafting in </span>
          <span className="block">rishikesh</span>
        </div>
      ),
      message:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: "Sent",
    },
    {
      id: 6,
      title: (
        <div>
          <span className="block">River rafting in </span>
          <span className="block">rishikesh</span>
        </div>
      ),
      message:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: "Sent",
    },
    {
      id: 7,
      title: (
        <div>
          <span className="block">River rafting in </span>
          <span className="block">rishikesh</span>
        </div>
      ),
      message:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: "Sent",
    },
  ];

  const hostelOwnersNotificationsData = [
    {
      id: 1,
      title: (
        <div>
          <span className="block dark:text-[#757575]">River rafting in </span>
          <span className="block dark:text-[#757575]">rishikesh</span>
        </div>
      ),
      message:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: "Sent",
    },
    {
      id: 2,
      title: (
        <div>
          <span className="block dark:text-[#757575]">River rafting in </span>
          <span className="block dark:text-[#757575]">rishikesh</span>
        </div>
      ),
      message:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: "Sent",
    },
    {
      id: 3,
      title: (
        <div>
          <span className="block dark:text-[#757575]">River rafting in </span>
          <span className="block dark:text-[#757575]">rishikesh</span>
        </div>
      ),
      message:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: "Sent",
    },
    {
      id: 4,
      title: (
        <div>
          <span className="block dark:text-[#757575]">River rafting in </span>
          <span className="block dark:text-[#757575]">rishikesh</span>
        </div>
      ),
      message:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: "Sent",
    },
    {
      id: 5,
      title: (
        <div>
          <span className="block dark:text-[#757575]">River rafting in </span>
          <span className="block dark:text-[#757575]">rishikesh</span>
        </div>
      ),
      message:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: "Sent",
    },
    {
      id: 6,
      title: (
        <div>
          <span className="block dark:text-[#757575]">River rafting in </span>
          <span className="block dark:text-[#757575]">rishikesh</span>
        </div>
      ),
      message:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: "Sent",
    },
  ];

  const getNotificationsData = () => {
    switch (activeButton) {
      case "User":
        return userNotificationsData;
      case "HostelOwners":
        return hostelOwnersNotificationsData;
      default:
        return [];
    }
  };

  const notificationsData = getNotificationsData();

  const splitMessage = (message) => {
    const words = message.split(" ");
    const midpoint = Math.ceil(words.length / 2);
    const firstPart = words.slice(0, midpoint).join(" ");
    const secondPart = words.slice(midpoint).join(" ");
    return { firstPart, secondPart };
  };

  const handleButtonClick = (buttonName) => {
    setActiveButton(buttonName);
  };

  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <div className="flex flex-col  lg:flex-row  justify-between w-full">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Notification
        </h2>
        <div className="w-[100%] md:w-[100%] lg:w-[50%] gap-x-3 lg:gap-x-5 flex flex-wrap justify-center lg:justify-end mt-2 items-center">
          <Link
            href="announcement"
            className={`px-4 py-2 text-sm font-medium font-poppins text-white rounded relative flex justify-center items-center bg-sky-blue-650`}
            type="button"
            prefetch={false}
            onClick={() => handleButtonClick("Announcement")}
          >
            <GrAnnounce size={14} className="mr-1 -rotate-12" /> Announcement
          </Link>
          <Link
            href="bulk-notification"
            prefetch={false}
            className={`px-4 py-2 text-sm font-medium font-poppins text-white rounded relative flex justify-center items-center bg-sky-blue-650`}
            type="button"
            onClick={() => handleButtonClick("BulkNotification")}
          >
            <IoPaperPlaneOutline size={16} className="mr-1" /> Bulk Notification
          </Link>
          <Link
            href="create-notification"
            className={`px-4 py-2 mt-2 md:mt-2 lg:mt-0 text-sm font-medium font-poppins text-white rounded relative flex justify-center items-center bg-sky-blue-650`}
            prefetch={false}
            type="button"
            onClick={() => handleButtonClick("CreateNotifications")}
          >
            <Plus size={16} className="mr-1" /> Create Notifications
          </Link>
        </div>
      </div>
      <div className="mt-5">
        <NotificationFilter />
      </div>
      <div className="flex gap-4 my-4">
        <button
          className={`px-14 py-2 text-sm font-nunito font-medium rounded relative flex justify-center items-center ${
            activeButton === "User"
              ? "bg-sky-blue-650 text-white"
              : "bg-white border text-black dark:bg-transparent dark:text-gray-200"
          }`}
          type="button"
          onClick={() => handleButtonClick("User")}
        >
          User
        </button>
        <button
          className={`px-5 py-2 text-sm font-nunito font-medium  rounded relative flex justify-center items-center ${
            activeButton === "HostelOwners"
              ? "bg-sky-blue-650 text-white"
              : "bg-white border text-black dark:bg-transparent dark:text-gray-200"
          }`}
          type="button"
          onClick={() => handleButtonClick("HostelOwners")}
        >
          Hostel Owners
        </button>
      </div>
      <div className="bg-white border rounded-xl dark:bg-black dark:border-none dark:py-2">
        <div className="flex gap-4 pl-4 my-4">
          <button
            className={`px-8 md:px-14 lg:px-14 py-2 text-sm font-nunito font-medium  rounded flex justify-center items-center ${
              activeButton === "User"
                ? "bg-sky-blue-650 text-white"
                : "bg-white border text-black dark:bg-transparent dark:text-gray-200"
            }`}
            type="button"
            onClick={() => handleButtonClick("All")}
          >
            All
          </button>
          <button
            className={`px-6 py-2 text-sm font-nunito font-medium  rounded  flex justify-center items-center ${
              activeButton === "Reminder"
                ? "bg-sky-blue-650 text-white"
                : "bg-white border text-black dark:bg-transparent dark:text-gray-200"
            }`}
            type="button"
            onClick={() => handleButtonClick("Reminder")}
          >
            Reminder
          </button>
          <button
            className={`px-6 py-2 text-sm font-nunito font-medium  rounded  flex justify-center items-center ${
              activeButton === "Announcement"
                ? "bg-sky-blue-650 text-white"
                : "bg-white border text-black dark:bg-transparent dark:text-gray-200"
            }`}
            type="button"
            onClick={() => handleButtonClick("Announcement")}
          >
            Announcement
          </button>
        </div>
        <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border dark:border-none">
          <table className="min-w-full divide-y bg-white rounded-xl divide-gray-200 dark:bg-black">
            <thead>
              <tr className=" ">
                <th className="px-5 pl-12 lg:pl-10 py-4 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  TITLE
                </th>
                <th className="pr-4 pl-24 py-4 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  MESSAGE
                </th>
                <th className="pr-4 lg:pr-10 pl-8 md:pl-2 lg:pl-0  py-4 bg-white text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  TIME
                </th>
                <th className="pr-4 pl-10 lg:pl-8 py-4 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  DATE
                </th>
                <th className="pr-4 pl-4 lg:pl-1 py-4 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  STATUS
                </th>
                <th className="py-4 pl-16 lg:pl-14 bg-white text-center text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  ACTION
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 border-x border-y dark:border-x-0 text-black/70 dark:text-[#757575]">
              {notificationsData.map((notification) => {
                const { firstPart, secondPart } = splitMessage(
                  notification.message
                );
                return (
                  <tr key={notification.id}>
                    <td className="whitespace-nowrap px-8 md:px-8 lg:px-5 text-sm font-poppins font-medium text-gray-500 dark:text-[#757575]">
                      {notification.title}
                    </td>
                    <td className="whitespace-nowrap md:whitespace-nowrap break-words max-w-md text-sm font-poppins font-medium">
                      <div >
                        <span className="text-sm font-poppins font-medium text-gray-500 dark:text-[#757575]">{firstPart}</span>
                        <br />
                        <span className="text-sm font-poppins font-medium text-gray-500 dark:text-[#757575]">{secondPart}</span>
                      </div>
                      
                    </td>
                    <td className="whitespace-nowrap pl-12 lg:pl-4 px-8 md:px-8 lg:px-5 text-sm font-poppins font-medium text-gray-500 dark:text-[#757575]">{notification.time}</td>
                    <td className="whitespace-nowrap px-8 md:px-8 lg:px-5 text-sm font-poppins font-medium text-gray-500 dark:text-[#757575]">{notification.date}</td>
                    <td>
                      <button className="text-primary-blue font-medium py-1 px-8 md:px-8 lg:px-5 rounded bg-[#CCEFED] dark:bg-[#2296906e] text-sm font-poppins ">
                        {notification.status}
                      </button>
                    </td>
                    <td className="py-20 md:py-5 lg:py-5  px-8 md:px-8 lg:px-5 flex justify-end">
                      <button className=" border p-2 rounded-l-lg text-black/75 dark:text-[#757575] dark:hover:text-pink-700">
                        <HiOutlineCog6Tooth />
                      </button>
                      <Link href={"/superadmin/dashboard/notifications-details"} className="hover:text-blue-700 dark:hover:text-blue-700 border p-2 text-black/75 dark:text-[#757575]">
                        <FiEye />
                      </Link>
                      <Link href={"/superadmin/dashboard/notifications-edit"} className=" border p-2 text-black/75 hover:text-yellow-400 dark:hover:text-yellow-400 dark:text-[#757575]">
                        <TfiPencilAlt />
                      </Link>
                      <button className=" p-2 border rounded-r-lg text-red-600">
                        <FaRegTrashCan />
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
      {/* <div className="flex justify-between items-center p-4">
        <div className="text-black/70 text-sm font-poppins font-medium">
          Showing {notificationsData.length} of 25 entries
        </div>
        <div className="inline-flex items-center justify-center border rounded-xl bg-white">
          <a
            href="#"
            className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180"
          >
            <span className="sr-only">Next Page</span>
            <MdOutlineKeyboardArrowLeft />
          </a>

          <a
            href="#"
            className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180"
          >
            <span className="sr-only">Next Page</span>
            <MdOutlineKeyboardArrowRight />
          </a>
        </div>
      </div> */}
       <div className="flex justify-between items-center mt-5">
                    <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
                    <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
                      <a
                        href="#"
                        className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                      >
                        <span className="sr-only">Next Page</span>
                        <MdOutlineKeyboardArrowLeft />
                      </a>
            
                      <a
                        href="#"
                        className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                      >
                        <span className="sr-only">Next Page</span>
                        <MdOutlineKeyboardArrowRight />
                      </a>
                    </div>
                  </div>
    </div>

    // <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth">
    //   <div className="flex flex-col md:flex-col lg:flex-row items-center md:items-start lg:items-center justify-between w-full ">
    //     <h2 className="text-2xl md:text-3xl font-bold text-black">
    //       Notification
    //     </h2>
    //     <div className="w-full md:w-[100%] lg:w-[50%] gap-x-3 gap-y-2 flex flex-col md:flex-row lg:flex-row justify-end items-center mt-4 lg:mt-0">
    //       <Link
    //         href="announcement"
    //         className="px-4 py-2 text-sm font-normal text-white rounded flex justify-center items-center bg-sky-blue-650"
    //         type="button"
    //         prefetch={false}
    //         onClick={() => handleButtonClick("Announcement")}
    //       >
    //         <GrAnnounce size={18} className="mr-1" /> Announcement
    //       </Link>
    //       <Link
    //         href="bulk-notification"
    //         prefetch={false}
    //         className="px-4 py-2 text-sm font-normal text-white rounded flex justify-center items-center bg-sky-blue-650"
    //         type="button"
    //         onClick={() => handleButtonClick("BulkNotification")}
    //       >
    //         <IoPaperPlaneOutline size={18} className="mr-1" /> Bulk Notification
    //       </Link>
    //       <Link
    //         href="create-notification"
    //         className="px-4 py-2 text-sm font-normal text-white rounded flex justify-center items-center bg-sky-blue-650"
    //         prefetch={false}
    //         type="button"
    //         onClick={() => handleButtonClick("CreateNotifications")}
    //       >
    //         <Plus size={18} className="mr-1" /> Create Notifications
    //       </Link>
    //     </div>
    //   </div>
    //   <div className="mt-5">
    //     <NotificationFilter />
    //   </div>
    //   <div className="flex flex-col md:flex-row gap-4 my-4">
    //     <button
    //       className={`px-10 md:px-14 py-2 text-sm font-normal rounded flex justify-center items-center ${
    //         activeButton === "User"
    //           ? "bg-sky-blue-650 text-white"
    //           : "bg-white border text-black"
    //       }`}
    //       type="button"
    //       onClick={() => handleButtonClick("User")}
    //     >
    //       User
    //     </button>
    //     <button
    //       className={`px-5 py-2 text-sm font-normal rounded flex justify-center items-center ${
    //         activeButton === "HostelOwners"
    //           ? "bg-sky-blue-650 text-white"
    //           : "bg-white border text-black"
    //       }`}
    //       type="button"
    //       onClick={() => handleButtonClick("HostelOwners")}
    //     >
    //       Hostel Owners
    //     </button>
    //   </div>
    //   <div className="bg-white border rounded-xl overflow-x-auto">
    //     <div className="mt-5 mb-10 rounded-t-xl border">
    //       <table className="min-w-full divide-y bg-white rounded-xl divide-gray-200 text-xs md:text-base">
    //         <thead>
    //           <tr>
    //             <th className="px-3 md:px-5 py-4 md:py-6 text-left font-semibold text-black uppercase">
    //               TITLE
    //             </th>
    //             <th className="pr-2 md:pr-4 py-4 md:py-6 text-left font-semibold text-black uppercase">
    //               MESSAGE
    //             </th>
    //             <th className="pr-2 md:pr-4 py-4 md:py-6 text-left font-semibold text-black uppercase">
    //               TIME
    //             </th>
    //             <th className="pr-2 md:pr-4 py-4 md:py-6 text-left font-semibold text-black uppercase">
    //               DATE
    //             </th>
    //             <th className="pr-2 md:pr-4 py-4 md:py-6 text-left font-semibold text-black uppercase">
    //               STATUS
    //             </th>
    //             <th className="py-4 md:py-6 text-left font-semibold text-black uppercase">
    //               ACTION
    //             </th>
    //           </tr>
    //         </thead>
    //         <tbody className="divide-y divide-gray-200 border text-black/70">
    //           {notificationsData.map((notification) => {
    //             const { firstPart, secondPart } = splitMessage(
    //               notification.message
    //             );
    //             return (
    //               <tr key={notification.id} className="text-xs md:text-base">
    //                 <td className="whitespace-nowrap px-3 md:px-5">
    //                   {notification.title}
    //                 </td>
    //                 <td className="whitespace-normal break-words max-w-xs md:max-w-md">
    //                   <span>{firstPart}</span>
    //                   <br />
    //                   <span>{secondPart}</span>
    //                 </td>
    //                 <td className="whitespace-nowrap">{notification.time}</td>
    //                 <td className="whitespace-nowrap">{notification.date}</td>
    //                 <td>
    //                   <button className="text-teal-500 font-medium py-1 px-2 md:px-3 rounded bg-[#CCEFED]">
    //                     {notification.status}
    //                   </button>
    //                 </td>
    //                 <td className="py-3 md:py-5 px-2 flex">
    //                   <button className="border p-1 md:p-2 rounded-l-lg text-black/75">
    //                     <HiOutlineCog6Tooth />
    //                   </button>
    //                   <button className="border p-1 md:p-2 text-black/75">
    //                     <FiEye />
    //                   </button>
    //                   <button className="border p-1 md:p-2 text-black/75">
    //                     <TfiPencilAlt />
    //                   </button>
    //                   <button className="p-1 md:p-2 border rounded-r-lg text-red-600">
    //                     <FaRegTrashCan />
    //                   </button>
    //                 </td>
    //               </tr>
    //             );
    //           })}
    //         </tbody>
    //       </table>
    //     </div>
    //   </div>
    //   <div className="flex flex-col md:flex-row justify-between items-center p-4 text-sm">
    //     <div className="text-black/70">
    //       Showing {notificationsData.length} of 25 entries
    //     </div>
    //     <div className="inline-flex items-center justify-center border rounded-xl bg-white mt-3 md:mt-0">
    //       <a
    //         href="#"
    //         className="size-6 md:size-7 flex items-center justify-center border border-gray-100 text-black/70"
    //       >
    //         <MdOutlineKeyboardArrowLeft />
    //       </a>
    //       <a
    //         href="#"
    //         className="size-6 md:size-7 flex items-center justify-center border border-gray-100 text-black"
    //       >
    //         <MdOutlineKeyboardArrowRight />
    //       </a>
    //     </div>
    //   </div>
    // </div>
  );
};

export default Notification;
