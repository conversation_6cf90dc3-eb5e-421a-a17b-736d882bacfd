"use client";
import { Plus } from "lucide-react";
import React, { useState } from "react";
import { FaRegTrashCan } from "react-icons/fa6";
import { FiEye } from "react-icons/fi";
import { TfiPencilAlt } from "react-icons/tfi";
import Image from "next/image";
import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight,
} from "react-icons/md";
import Link from "next/link";

const FeaturedHostelPage = () => {
  // eslint-disable-next-line no-unused-vars
  const [OpenAddHostel, setOpenAddHostel] = useState("");

  const mobilehomepageData = [
    {
      id: 1,
      hostelName: (
        <div>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Amsterdam,</span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Netherlands</span>
        </div>
      ),
      Feature: (
        <div>
          <p className="text-xs md:text-sm lg-text-sm text-gray-500 leading-6 dark:text-[#757575]">
            <span className="text-black font-semibold dark:text-gray-100">Key Features:</span>{" "}
            On-Site Bar, <br /> Rooftop Terrace, Dorms & <br /> Private Rooms
          </p>
        </div>
      ),
      amount: "$28/Person",
      status: (<p className="text-[#00B69B] bg-[#CCF0EB] text-sm py-2 px-2 md:px-2 lg:px-0 rounded-lg font-semibold text-center dark:bg-[#81e0d469]">Top rated</p>),
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/featured-hostel.png`,
    },
    {
      id: 2,
      hostelName: (
        <div>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Amsterdam,</span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Netherlands</span>
        </div>
      ),
      Feature: (
        <div>
          <p className="text-xs md:text-sm lg-text-sm text-gray-500 leading-6 dark:text-[#757575]">
            <span className="text-black font-semibold dark:text-gray-100">Key Features:</span>{" "}
            On-Site Bar, <br /> Rooftop Terrace, Dorms & <br /> Private Rooms
          </p>
        </div>
      ),
      amount: "$28/Person",
      status: (<p className="text-[#00B69B] bg-[#CCF0EB] text-sm py-2 rounded-lg font-semibold text-center px-2 md:px-2 lg:px-0 dark:bg-[#81e0d469]">Top rated</p>),
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/featured-hostel.png`,
    },
    {
      id: 3,
      hostelName: (
        <div>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Amsterdam,</span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Netherlands</span>
        </div>
      ),
      Feature: (
        <div>
          <p className="text-xs md:text-sm lg-text-sm text-gray-500 leading-6 dark:text-[#757575]">
            <span className="text-black font-semibold dark:text-gray-100">Key Features:</span>{" "}
            On-Site Bar, <br /> Rooftop Terrace, Dorms & <br /> Private Rooms
          </p>
        </div>
      ),
      amount: "$28/Person",
      status: (<p className="text-[#00B69B] bg-[#CCF0EB] text-sm py-2 rounded-lg font-semibold text-center px-2 md:px-2 lg:px-0 dark:bg-[#81e0d469]">Top rated</p>),
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/featured-hostel.png`,
    },
    {
      id: 4,
      hostelName: (
        <div>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Amsterdam,</span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Netherlands</span>
        </div>
      ),
      Feature: (
        <div>
          <p className="text-xs md:text-sm lg-text-sm text-gray-500 leading-6 dark:text-[#757575]">
            <span className="text-black font-semibold dark:text-gray-100">Key Features:</span>{" "}
            On-Site Bar, <br /> Rooftop Terrace, Dorms & <br /> Private Rooms
          </p>
        </div>
      ),
      amount: "$28/Person",
      status: (<p className="text-[#00B69B] bg-[#CCF0EB] text-sm py-2 rounded-lg font-semibold text-center px-2 md:px-2 lg:px-0 dark:bg-[#81e0d469]">Top rated</p>),
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/featured-hostel.png`,
    },
    {
      id: 5,
      hostelName: (
        <div>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Amsterdam,</span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Netherlands</span>
        </div>
      ),
      Feature: (
        <div>
          <p className="text-xs md:text-sm lg-text-sm text-gray-500 leading-6 dark:text-[#757575]">
            <span className="text-black font-semibold dark:text-gray-100">Key Features:</span>{" "}
            On-Site Bar, <br /> Rooftop Terrace, Dorms & <br /> Private Rooms
          </p>
        </div>
      ),
      amount: "$28/Person",
      status: (<p className="text-[#00B69B] bg-[#CCF0EB] text-sm py-2 rounded-lg font-semibold text-center px-2 md:px-2 lg:px-0 dark:bg-[#81e0d469]">Top rated</p>),
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/featured-hostel.png`,
    },
  ];

  return (
    <div className=" lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <div className="flex items-center justify-between w-full ">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Featured Hostel
        </h2>
        <div className="w-[40%] lg:w-[50%] gap-x-5 flex justify-end items-center">
          <button
            className={` px-2 md:px-4 lg:px-4 py-1.5 md:py-2 lg:py-2 text-sm font-normal text-white rounded relative flex justify-center items-center bg-sky-blue-650 `}
            type="button"
            onClick={() => setOpenAddHostel(true)}
          >
            <Link
              href={"/superadmin/dashboard/featured-hostel-add"}
              className="flex"
            >
              <Plus size={18} className="mr-1" /> Featured Hostel
            </Link>
          </button>
        </div>
      </div>
      <div className="bg-white border-b border-l border-r rounded-xl dark:bg-black dark:border-none pb-3">
        <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border-b">
          <table className="min-w-full divide-y  bg-white rounded-xl divide-gray-200 dark:bg-black">
            <thead>
              <tr className=" ">
                <th className="py-6 pl-4 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  HOSTEL NAME
                </th>
                <th className="px-4 md:px-6 lg:px-2 py-6 pl-12 md:pl-14 lg:pl-7  bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  KEY FEATURE
                </th>
                <th className="pl-12 md:pl-12 lg:pl-4 py-6 bg-white text-left text-sm  font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  PRICE
                </th>
                <th className="pl-12 md:pl-8 lg:pl-14 px-4 md:px-4 lg:px-1 py-6 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  IMAGE
                </th>
                <th className="py-6 px-6 md:px-5 lg:px-2 pl-12 md:pl-8 lg:pl-6 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  STATUS
                </th>

                <th className="pr-8 py-6 bg-white text-end text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  ACTION
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 border-x border-y dark:border-x-0 text-black/70 dark:text-[#757575]">
              {mobilehomepageData.map((mobile) => (
                <tr key={mobile.id}>
                  <td className=" px-5 pb-6 md:pb-4 lg:pb-4 text-sm font-poppins font-medium">
                    {mobile.hostelName}
                  </td>

                  <td className="pl-8 md:pl-6 lg:pl-0 whitespace-nowrap px-4 md:px-6 lg:px-2 text-sm font-poppins font-medium">
                    {mobile.Feature}
                  </td>
                  <td className="pl-8 md:pl-6 lg:pl-0 whitespace-nowrap  text-black text-sm font-poppins font-mediumpb-10 md:pb-7 lg:pb-8 px-6 md:px-4 lg:px-2 dark:text-[#757575]">
                    {mobile.amount}
                  </td>
                  <td>
                    <button className="font-medium pl-8 md:pl-6 lg:pl-0 py-2 px-0 md:px-2 lg:px-0 rounded ">
                      <Image
                        src={mobile.image}
                        alt="Image"
                        className="w-64 h-16 md:h-16 lg:h-28 md:w-40 lg:w-44 "
                        width={80}
                        height={48}
                      />
                    </button>
                  </td>

                  <td className="whitespace-nowrap text-sm font-poppins font-medium pl-10 lg:pl-0  pb-7 md:pb-6 lg:pb-6 px-8 md:px-6 lg:px-6">
                    {mobile.status}
                  </td>
                  <td className="pt-2 pb-0 md:pt-3 lg:pt-9 md:pb-0 lg:pb-0 pl-8 lg:pl-8 px-2 flex justify-end">
                    <Link href={"/superadmin/dashboard/featured-hostel-details"} className=" border p-2 rounded-l-lg text-black/75 hover:text-blue-700 dark:text-[#757575] dark:hover:text-blue-700">
                      <FiEye />
                    </Link>
                    <button className=" border p-2 text-black/75 hover:text-yellow-400 dark:text-[#757575] dark:hover:text-yellow-400">
                      <Link href={"/superadmin/dashboard/featured-hostel-edit"}>
                        <TfiPencilAlt />
                      </Link>
                    </button>
                    <button className=" p-2 border rounded-r-lg text-red-600">
                      <FaRegTrashCan />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      {/* <div className="flex justify-between items-center mt-5">
        <div className="text-black/75 text-sm font-poppins font-medium">Showing 1-09 of 78</div>
        <div className="inline-flex items-center justify-center border rounded-xl bg-white">
          <a
            href="#"
            className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180"
          >
            <span className="sr-only">Next Page</span>
            <MdOutlineKeyboardArrowLeft />
          </a>

          <a
            href="#"
            className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180"
          >
            <span className="sr-only">Next Page</span>
            <MdOutlineKeyboardArrowRight />
          </a>
        </div>
      </div> */}
            <div className="flex justify-between items-center mt-5">
              <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
              <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
                <a
                  href="#"
                  className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                >
                  <span className="sr-only">Next Page</span>
                  <MdOutlineKeyboardArrowLeft />
                </a>
      
                <a
                  href="#"
                  className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                >
                  <span className="sr-only">Next Page</span>
                  <MdOutlineKeyboardArrowRight />
                </a>
              </div>
            </div>
    </div>
  );
};

export default FeaturedHostelPage;
