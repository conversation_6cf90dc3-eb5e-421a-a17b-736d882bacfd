import mongoose from 'mongoose';

const roomRateSchema = new mongoose.Schema({
    room: {
        type: mongoose.Types.ObjectId,
        ref: 'rooms',
    },
    date:{
        type:Date
    },
    fromDate: {
        type: Date
    }, 
    toDate: {
        type: Date
    }, 
    rate: {
        weekdayRate: {
            value: Number,
            updatedAt: Date
        },
        weekendRate: {
            value: Number,
            updatedAt: Date
        },
        averagePrice: {
            value: Number,
            updatedAt: Date
        },
        basePrice:{
            type:Number,
            required:true
        }
    },
    availability:{
        type:Number
    }
}, {
    timestamps: true
});

const roomRatesModel = mongoose.model('roomRates', roomRateSchema);

export default roomRatesModel;
