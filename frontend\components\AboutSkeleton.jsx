// components/AboutSkeleton.jsx
import React from "react";

const SkeletonBox = ({ className }) => (
  <div className={`bg-gray-400 animate-pulse ${className}`} />
);

export default function AboutSkeleton() {
  return (
    <>
    {/* <div className="space-y-10 px-6 py-10 bg-black text-white"> */}
      {/* Hero Section */}
      {/* <div className="relative w-full h-screen z-0 bg-gray-700 flex items-center justify-center overflow-hidden">
        <div className="flex flex-col justify-center">
          <SkeletonBox className="mb-5 h-20 w-[60%] mx-auto z-10" />
          <SkeletonBox className="mb-8 h-5 w-1/2 mx-auto z-10" />
        </div>
      </div> */}

      {/* Donut + Pie Charts */}
      <div className="bg-[#282828]">
        <div className="container relative pt-8 pb-10">
          <SkeletonBox className="h-10 rounded-md w-1/2 mx-auto" />
          <SkeletonBox className="h-4 rounded-md w-2/3 mx-auto mt-2" />
          <div className="grid lg:grid-cols-2 items-center lg:gap-10 gap-4 md:pb-12 pb-8 sm:pt-10 pt-5">
            <div className="grid grid-cols-2 sm:gap-4 gap-3 justify-items-center">
              {[...Array(4)].map((_, i) => (
                <SkeletonBox key={i} className="sm:w-[250px] w-[200px] sm:h-[250px] h-[200px] !bg-transparent border-[20px] border-gray-400 !rounded-full" />
              ))}
            </div>
            <div>
                <SkeletonBox className="sm:w-[450px] xs:w-[400px] w-[350px] sm:h-[450px] xs:h-[400px] h-[350px] !rounded-full mx-auto" />
            </div>
          </div>
        </div>
      </div>

      {/* Who are we? */}
      <div className="bg-[#000] pt-12 pb-8">
        <div className="container">
          <SkeletonBox className="h-10 w-1/2 mx-auto rounded-md" />
          <SkeletonBox className="h-4 w-2/3 mx-auto mt-2 rounded-md" />
          <div className="md:flex items-center lg:gap-12 gap-4 mt-10">
            <div className="lg:w-[70%] md:w-[50%] w-full">
              {[...Array(5)].map((_, i) => (
                <SkeletonBox key={i} className="h-14 w-full lg:mb-4 mb-2 rounded-md" />
              ))}
            </div>
            <SkeletonBox className="lg:w-[30%] md:w-[50%] w-full h-[350px] rounded-md" />
          </div>
        </div>
      </div>

      <div className="bg-[#282828] pt-12 pb-10">
          <div className="container">
            <SkeletonBox className="h-10 w-1/2 mx-auto rounded-md" />
            <SkeletonBox className="h-4 w-2/3 mx-auto mt-2 rounded-md" />
            <div className="w-full grid md:grid-cols-2 md:gap-4 gap-2 mt-4">
              {[...Array(6)].map((_, i) => (
                <SkeletonBox key={i} className="h-14 w-full lg:mb-4 mb-2 rounded-md" />
              ))}
            </div>

            {/* Channerl partner */}
            <SkeletonBox className="h-8 w-1/2 my-8 mx-auto rounded-md" />
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4">
              {[...Array(5)].map((_, i) => (
                <SkeletonBox key={i} className="h-20 w-full rounded-md" />
              ))}
            </div>
          </div>
      </div>

      {/* Why MixDorm Stands Out? */}
      <div className="bg-[#000]">
        <div className="container pt-6 pb-12">
          <SkeletonBox className="h-10 w-1/2 mx-auto rounded-md" />
          <div className="overflow-x-auto pt-10 pb-2">
            <div className="min-w-[700px] bg-[#e8fffa] w-full border border-black">
              {/* Table Header Skeleton */}
              <div className="grid grid-cols-4 bg-primary-blue text-black text-sm sm:text-lg">
                {["Feature", "Mixdorm", "Hostelworld", "Booking.com"].map((col, idx) => (
                  <div key={idx} className="p-3 font-semibold">
                    <SkeletonBox className="h-5 rounded-md w-3/4" />
                  </div>
                ))}
              </div>

              {/* Table Body Skeleton Rows */}
              {[...Array(10)].map((_, rowIdx) => (
                <div key={rowIdx} className="grid grid-cols-4 border border-primary-blue text-sm sm:text-base text-[#333]">
                  {[...Array(4)].map((_, colIdx) => (
                    <div key={colIdx} className="p-3">
                      <SkeletonBox className="h-4 w-full rounded-md" />
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Get Your hostel live */}
      <div className="bg-[#000] pt-12 pb-10 hidden">
        <div className="container">
            <SkeletonBox className="h-10 w-1/2 mx-auto" />
            <SkeletonBox className="h-4 w-2/3 mx-auto mt-4" />
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 pt-10">
              {[...Array(3)].map((_, i) => (
                <SkeletonBox key={i} className="h-48 w-full rounded-bl-4xl rounded-tr-4xl" />
              ))}
            </div>
            <SkeletonBox className="h-10 w-1/2 my-8 mx-auto" />
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
              {[...Array(4)].map((_, i) => (
                <SkeletonBox key={i} className="h-20 w-full rounded-md" />
              ))}
            </div>
        </div>
      </div>

      {/* Key feature */}
      <div className="bg-[#282828] py-8">
        <div className="container">
            <div className="lg:w-[75%] w-[90%] relative mx-auto bg-black/50 rounded-3xl p-6 md:p-10">
                <SkeletonBox className="h-10 w-1/2 rounded-md mb-8" />
                <div className="space-y-3">
                  {[...Array(7)].map((_, i) => (
                    <div key={i} className="flex items-center gap-3">
                      <SkeletonBox className="min-w-[48px] min-h-[48px] rounded-bl-md rounded-tr-md" />
                      <SkeletonBox className="h-8 w-full rounded-md" />
                    </div>
                  ))}
                </div>
            </div>
        </div>
      </div>

      {/* Key feature */}
      <div className="bg-[#000] py-8">
        <div className="container">
          <SkeletonBox className="h-10 w-1/2 mx-auto rounded-md" />
          <SkeletonBox className="h-4 w-2/3 mx-auto mt-4 rounded-md" />
          <div className="xs:flex justify-center lg:gap-8 sm:gap-6 gap-3 mt-8">
            <SkeletonBox className="h-28 lg:w-[35.5%] xs:w-[50%] w-[90%] xs:mx-0 mx-auto rounded-2xl" />
            <SkeletonBox className="h-28 lg:w-[35.5%] xs:w-[50%] w-[90%] xs:mx-0 mx-auto rounded-2xl" />
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-[#282828] py-6">
        <div className="container">
          <SkeletonBox className="h-10 sm:w-[250px] w-[200px] mx-auto rounded-md" />
          <div className="flex flex-wrap items-center sm:gap-7 gap-3 justify-center sm:mt-8 mt-5">
            {[...Array(5)].map((_, i) => (
              <SkeletonBox key={i} className="lg:w-[48px] lg:h-[48px] w-[40px] h-[40px] rounded-bl-md rounded-tr-md" />
            ))}
          </div>
        </div>
      </div>

    {/* </div> */}
    </>
  );
}
