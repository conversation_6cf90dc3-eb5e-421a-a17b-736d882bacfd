"use client";
import Link from "next/link";
import { useState } from "react";
import { FaRegTrashCan } from "react-icons/fa6";
import { FiEye } from "react-icons/fi";
import { HiOutlineCog6Tooth } from "react-icons/hi2";
import { IoIosStar } from "react-icons/io";
import { TfiPencilAlt } from "react-icons/tfi";
import HostelPopup from "./HostelPopup";

function HostelTable({ hostelsData }) {
  const [showPopup, setShowPopup] = useState(false);

  const handleActionClick = () => {
    setShowPopup(true);
  };

  const handleClosePopup = () => {
    setShowPopup(false);
  };

  const handleSaveChanges = () => {
    // Handle save logic here
    setShowPopup(false);
  };

  // const hostelData = [
  //   {
  //     id: 1,
  //     name: "88 Backpackers",
  //     rates: 5,
  //     rooms: 45,
  //     bed: 152,
  //     country: "INDIA",
  //     offer: "Open",
  //     status: "Verified",
  //   },
  //   {
  //     id: 2,
  //     name: "88 Backpackers",
  //     rates: 4,
  //     rooms: 45,
  //     bed: 152,
  //     country: "INDIA",
  //     offer: "Open",
  //     status: "Verified",
  //   },
  //   {
  //     id: 3,
  //     name: "88 Backpackers",
  //     rates: 3,
  //     rooms: 45,
  //     bed: 152,
  //     country: "INDIA",
  //     offer: "Open",
  //     status: "Verified",
  //   },
  //   {
  //     id: 4,
  //     name: "88 Backpackers",
  //     rates: 5,
  //     rooms: 45,
  //     bed: 152,
  //     country: "INDIA",
  //     offer: "Open",
  //     status: "Verified",
  //   },
  //   {
  //     id: 5,
  //     name: "88 Backpackers",
  //     rates: 4,
  //     rooms: 45,
  //     bed: 152,
  //     country: "INDIA",
  //     offer: "Open",
  //     status: "Verified",
  //   },
  // ];

  const [selectedRows, setSelectedRows] = useState([]);

  const handleCheckboxChange = (id) => {
    setSelectedRows((prevSelectedRows) => {
      if (prevSelectedRows.includes(id)) {
        return prevSelectedRows.filter((rowId) => rowId !== id);
      } else {
        return [...prevSelectedRows, id];
      }
    });
  };

  const renderStars = (count) => {
    const stars = [];
    for (let i = 0; i < 5; i++) {
      stars.push(
        <IoIosStar
          key={i}
          className={i < count ? "text-yellow-500" : "text-gray-300"}
        />
      );
    }
    return <div className='flex'>{stars}</div>;
  };

  return (
    <div className='overflow-x-auto mt-5 rounded-xl border dark:border-none'>
      <table className='min-w-full divide-y bg-white rounded-xl  divide-gray-200 dark:bg-black'>
        <thead>
          <tr className=' '>
            <th className='px-5'></th>
            <th className=' pl-7 py-6 bg-white text-left font-poppins text-sm font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]'>
              HOSTEL NAME
            </th>
            <th className='pr-4 pl-8 md:pl-8 lg:pl-8 py-6 bg-white text-left font-poppins text-sm font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]'>
              RATES
            </th>
            <th className='pr-4 pl-4 py-6 bg-white text-left font-poppins text-sm font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]'>
              ROOMS
            </th>
            <th className='pr-4 pl-5 md:pl-5 lg:pl-5 py-6 bg-white text-left font-poppins text-sm font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]'>
              BED
            </th>
            <th className='pr-4 pl-5 md:pl-5 lg:pl-5 py-6 bg-white text-left font-poppins text-sm font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]'>
              COUNTRY
            </th>
            <th className='pr-4 pl-5 md:pl-5 lg:pl-5 py-6 bg-white text-left font-poppins text-sm font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]'>
              OFFER
            </th>
            <th className='pr-4 pl-9  lg:pl-9 py-6 bg-white text-left font-poppins text-sm font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]'>
              STATUS
            </th>
            <th className=' py-6 bg-white text-center font-poppins text-sm font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]'>
              ACTION{" "}
            </th>
          </tr>
        </thead>
        <tbody className='divide-y divide-gray-200 border-x border-y text-black/70 dark:text-[#757575] dark:border-x-0'>
          {hostelsData.map((hostel) => (
            <tr key={hostel._id}>
              <td className='pr-4 pl-8 px-5'>
                <input
                  type='checkbox'
                  className='rounded-md h-5 w-5'
                  checked={selectedRows.includes(hostel._id)}
                  onChange={() => handleCheckboxChange(hostel._id)}
                />
              </td>
              <td className='px-6 font-poppins text-sm font-medium whitespace-nowrap'>
                {hostel.name}
              </td>
              <td className='whitespace-nowrap px-5'>
                {renderStars(hostel?.rates || 4)}
              </td>
              <td className='whitespace-nowrap px-5 font-poppins text-sm font-medium pl-8'>
                {hostel?.rooms || 0}
              </td>
              <td className='whitespace-nowrap px-5 font-poppins text-sm font-medium'>
                {hostel?.bed || 0}
              </td>
              <td className='whitespace-nowrap px-5 font-poppins text-sm font-medium pl-8'>
                {hostel?.address?.country || "-"}
              </td>
              <td className='whitespace-nowrap px-5 font-poppins text-sm font-medium pl-6'>
                {hostel?.offer || "-"}
              </td>
              <td className='whitespace-nowrap px-5'>
                <button
                  className={` font-poppins text-sm font-medium py-1 px-5 rounded   ${
                    hostel?.isPropertyVerified
                      ? "text-primary-blue bg-[#CCEFED] dark:bg-[#28676382]"
                      : "text-red-600 bg-red-100 dark:bg-[#953c3c73]"
                  }`}
                >
                  {hostel?.isPropertyVerified ? "Verified" : "Unverified"}
                </button>
              </td>
              <td className=' py-5 px-5 flex justify-center items-center'>
                <div>
                  <button
                    className=' border p-2 rounded-l-lg text-black/75 dark:text-[#757575] dark:hover:text-pink-700'
                    onClick={handleActionClick}
                  >
                    <HiOutlineCog6Tooth />
                  </button>
                </div>
                <Link
                  href='/superadmin/dashboard/hostel-detail'
                  className='border p-2 text-black/75 hover:text-blue-700 dark:text-[#757575] dark:hover:text-blue-700'
                  prefetch={false}
                >
                  <FiEye />
                </Link>
                <Link
                  href='/superadmin/dashboard/hostel-edit'
                  prefetch={false}
                  className=' border p-2 text-black/75 hover:text-yellow-400 dark:text-[#757575] dark:hover:text-yellow-400'
                >
                  <TfiPencilAlt />
                </Link>
                <button className=' p-2 border rounded-r-lg text-red-600'>
                  <FaRegTrashCan />
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      {showPopup && (
        <HostelPopup onClose={handleClosePopup} onSave={handleSaveChanges} />
      )}
    </div>
  );
}

export default HostelTable;
