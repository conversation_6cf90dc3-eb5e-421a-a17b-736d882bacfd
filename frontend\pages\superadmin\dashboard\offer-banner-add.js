

"use client";
import Link from "next/link";
import React from "react";

const addOfferBanner = () => {

  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth h-screen dark:bg-[#171616]">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        Add Blog
      </h2>
      <div className="bg-white border mt-5  rounded-xl h-auto px-4 md:px-5 lg:px-1 py-7 md:py-16 lg:py-8 dark:bg-black dark:border-none">
        <div className="bg-white w-full max-w-3xl dark:bg-black">
          <form>
            <div className="grid grid-cols-1 sm:grid-cols-2">


              <div className="relative px-6">
                <label className="block font-semibold text-sm font-poppins text-black/40 dark:text-[#B6B6B6]">
                  Offer Banner
                </label>
                <input
                  type="text"
                  className="mt-1 p-2 font-medium text-sm font-poppins w-full  rounded-md bg-[#EEF9FF] dark:bg-transparent dark:placeholder:text-[#757575]"
                  placeholder="Upload Image..."
                />
               
              </div>
            </div>

            {/* Buttons */}
            <div className="flex space-x-3 pl-6 my-8">
              <Link
                href={"/superadmin/dashboard/offer-banner"}
                type="button"
                className="flex items-center justify-center py-2 border w-40  border-gray-300 text-black font-medium text-sm font-poppins rounded-md mt-2 dark:text-gray-100"
              >
                Cancel
              </Link>
              <button
                type="submit"
                className="px-6 py-2 w-40  bg-sky-blue-650 text-white font-medium text-sm font-poppins rounded-md mt-2"
              >
                Add
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default addOfferBanner;
