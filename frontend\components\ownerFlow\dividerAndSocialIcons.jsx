import React from "react";
// import { FaFacebook, FaApple } from "react-icons/fa";
// import { Button } from "@mui/material";
import { GoogleOAuthProvider } from "@react-oauth/google";
// import axios from "axios";
// import { useRouter } from "next/router";
// import { setItemLocalStorage, setToken } from "../../utils/browserSetting";
// import { BASE_URL } from "../../utils/api";
// import { toast } from "react-hot-toast";
// import { FacebookProvider, LoginButton } from "react-facebook";
// import AppleLogin from "react-apple-login";
import dynamic from "next/dynamic";
// import { useNavbar } from "../home/<USER>";

const GoogleSocialLogin = dynamic(() => import("./googleSocialLogin"), {
  ssr: false,
});

const DividerAndSocialIcons = () => {
  // const router = useRouter();
  // const { updateUserStatus,updateUserRole } = useNavbar();

  // const handleFacebookSuccess = async (response) => {
  //   try {
  //     const { accessToken } = response.authResponse;

  //     // Manually fetch user profile from Facebook using the access token
  //     const profileRes = await axios.get(
  //       `https://graph.facebook.com/me?fields=name,email&access_token=${accessToken}`
  //     );

  //     const { name, email } = profileRes.data;

  //     const res = await axios.post(`${BASE_URL}/auth/social/login`, {
  //       token: accessToken,
  //       role: "hostel_owner",
  //       email,
  //       name,
  //     });

  //     if (res.status === 201 && res.data.status) {
  //       toast.success("Login successful!");

  //       setItemLocalStorage("name", name);
  //       setItemLocalStorage("role", res?.data?.data?.user?.role);
  //       setToken(res?.data?.data?.token);
  //       updateUserStatus(res?.data?.data?.token);
  //       updateUserRole(res?.data?.data?.user?.role);

  //       router.push("/owner/list");
  //     } else {
  //       toast.error("Social login failed: " + res.data.message);
  //     }
  //   } catch (error) {
  //     toast.error(error.response?.data.message || error.message);
  //   }
  // };

  // const handleFacebookFailure = (error) => {
  //   toast.error("Facebook login failed: " + error.message);
  // };

  // const handleAppleSuccess = async (response) => {
  //   try {
  //     const { authorization, user } = response;

  //     const { id_token } = authorization;

  //     const name = user?.name
  //       ? `${user.name.firstName} ${user.name.lastName}`
  //       : null;
  //     const email = user?.email || null;

  //     const res = await axios.post(`${BASE_URL}/auth/social/login`, {
  //       token: id_token,
  //       role: "hostel_owner",
  //       provider: "apple",
  //       name: name,
  //       email: email,
  //     });

  //     if (res.status === 201 && res.data.status) {
  //       toast.success("Login successful!");

  //       setItemLocalStorage(
  //         "name",
  //         name ||
  //           `${res.data.data.user.name.first} ${res.data.data.user.name.last}`
  //       );
  //       setItemLocalStorage("role", res?.data?.data?.user?.role);
  //       setToken(res?.data?.data?.token);
  //       updateUserStatus(res?.data?.data?.token);
  //       updateUserRole(res?.data?.data?.user?.role);

  //       router.push("/owner/list");
  //     } else {
  //       toast.error("Social login failed: " + res.data.message);
  //     }
  //   } catch (error) {
  //     toast.error(error.response?.data.message || error.message);
  //   }
  // };

  // const handleAppleFailure = (response) => {
  //   toast.error("Apple login failed: " + response.error);
  // };

  return (
    <GoogleOAuthProvider clientId={process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID}>
      <div className="flex items-center justify-center mx-8 space-x-4 mt-8">
        <span className="flex-1 border-t border-gray-200"></span>
        <span className="text-gray-600">Or</span>
        <span className="flex-1 border-t border-gray-200"></span>
      </div>

      <div className="flex items-center gap-x-4 justify-center  mb-6">
        {/* <FacebookProvider appId={process.env.NEXT_PUBLIC_FACEBOOK_APP_ID}>
          <LoginButton
            scope="public_profile,email"
            onSuccess={handleFacebookSuccess}
            onError={handleFacebookFailure}
            className="min-w-0 p-0 text-3xl sm:w-[54px] w-[45px] h-[45px] sm:h-[54px]  bg-[#1877F2] rounded-[10px] flex items-center justify-center"
          >
            <FaFacebook className="text-[#ffffff]" />
          </LoginButton>
        </FacebookProvider> */}
        <div className="flex flex-col items-center justify-center">
          <p className="text-base my-2 text-gray-600">Continue with</p>
          <GoogleSocialLogin role="hostel_owner" />
        </div>

        {/* <AppleLogin
          clientId={process.env.NEXT_PUBLIC_APPLE_CLIENT_ID}
          redirectURI={process.env.NEXT_PUBLIC_APPLE_REDIRECT_URI}
          responseType="code id_token"
          scope="name email"
          render={(renderProps) => (
            <Button
              className="min-w-0 p-0 text-3xl text-white sm:w-[54px] w-[45px] h-[45px] sm:h-[54px]  bg-black rounded-[10px]"
              onClick={renderProps.onClick}
            >
              <FaApple />
            </Button>
          )}
          onSuccess={handleAppleSuccess}
          onError={handleAppleFailure}
        /> */}
      </div>
    </GoogleOAuthProvider>
  );
};

export default DividerAndSocialIcons;
