// import React, { useState, useRef } from "react";
// import { Swiper, SwiperSlide } from "swiper/react";
// import Image from "next/image";
// import { CircleChevronLeft, CircleChevronRight } from "lucide-react";
// import SwiperCore, { Navigation, Pagination, Autoplay } from "swiper";

// SwiperCore.use([Navigation, Pagination, Autoplay]);

// export default function Carousel({ image }) {
//   const [activeIndex, setActiveIndex] = useState(0);
//   const swiperRef = useRef(null);

//   return (
//     <div className="relative">
//       {/* <Swiper
//         slidesPerView={1.7}
//         centeredSlides={true}
//         navigation={false}
//         pagination={{ clickable: true }}
//         onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
//         className="mySwiper"
//         ref={swiperRef}
//         spaceBetween={20}
//         autoplay={{
//           delay: 5000,
//           disableOnInteraction: false,
//         }}
//         initialSlide={1} // Start with the second image
//       >
//         {image.map((img, index) => (
//           <SwiperSlide key={index}>
//             <div
//               className={`relative transition-all duration-300 ${
//                 index === activeIndex
//                   ? "w-full md:h-[554px] h-[55vh]"
//                   : "w-[100%] md:h-[459px] h-[50vh] mt-6"
//               }`}
//             >
//               <Image
//                 src={`https://${img.url}`} // Added protocol to URL
//                 alt={`Image ${index + 1}`} // Updated alt text for better accessibility
//                 layout="fill"
//                 objectFit="cover"
//                 className={`rounded-3xl  `} // Simplified class since both conditions are the same
//                 loading={index < 3 ? "eager" : "lazy"}
//               />
//             </div>
//           </SwiperSlide>
//         ))}
//       </Swiper> */}
//      <Swiper
//   slidesPerView={1.7}
//   centeredSlides={true}
//   navigation={false}
//   pagination={{ clickable: true }}
//   onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
//   className="mySwiper"
//   ref={swiperRef}
//   spaceBetween={20}
//   autoplay={{
//     delay: 5000,
//     disableOnInteraction: false,
//   }}
//   initialSlide={1} // Start with the second image
// >
//   {image.map((img, index) => (
//     <SwiperSlide key={index}>
//       <div className="relative w-full md:h-[554px] h-[55vh] overflow-hidden">
//         <div
//           className={`transition-all duration-300 ease-in-out will-change-transform h-full w-full ${
//             index === activeIndex
//               ? "scale-100"
//               : "scale-[0.95] opacity-80"
//           }`}
//         >
//           <Image
//             src={`https://${img.url}`} // Fixed URL syntax
//             alt={`Image ${index + 1}`}  // Fixed alt syntax
//             layout="fill"
//             objectFit="cover"
//             className="rounded-3xl"
//             loading={index < 3 ? "eager" : "lazy"}
//           />
//         </div>
//       </div>
//     </SwiperSlide>
//   ))}
// </Swiper>

//       {/* Custom navigation icons */}
//   <div className="absolute bottom-16 left-1/2 transform -translate-x-1/2 z-10 flex space-x-16">
//     <div
//       className="flex items-center justify-center w-8 h-8 rounded-full shadow-md cursor-pointer"
//       onClick={() => swiperRef.current.swiper.slidePrev()}
//     >
//       <CircleChevronLeft size="large" color="#fff" strokeWidth="1px" />
//     </div>
//     <div
//       className="flex items-center justify-center w-8 h-8 rounded-full shadow-md cursor-pointer"
//       onClick={() => swiperRef.current.swiper.slideNext()}
//     >
//       <CircleChevronRight size="large" color="#fff" strokeWidth="1px" />
//     </div>
//   </div>
// </div>
//   );
// }
// import React, { useState, useRef } from "react";
// import { Swiper, SwiperSlide } from "swiper/react";
// import { Autoplay, Navigation, Pagination } from "swiper/modules";
// import Image from "next/image";
// import { CircleChevronLeft, CircleChevronRight } from "lucide-react";
// import { motion, AnimatePresence } from "framer-motion";

// import "swiper/css";
// import "swiper/css/navigation";
// import "swiper/css/pagination";

// export default function Carousel({ image }) {
//   const [activeIndex, setActiveIndex] = useState(1);
//   const [isModalOpen, setIsModalOpen] = useState(false);
//   const swiperRef = useRef(null);

//   return (
//     <>
//       <div className="relative">
//         <Swiper
//           slidesPerView={1.7}
//           centeredSlides={true}
//           navigation={false}
//           pagination={false}
//           onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
//           className="mySwiper"
//           ref={swiperRef}
//           spaceBetween={10}
//           autoplay={{
//             delay: 5000,
//             disableOnInteraction: false,
//           }}
//           initialSlide={1}
//           modules={[Autoplay, Navigation, Pagination]}
//         >
//           {image.map((img, index) => (
//             <SwiperSlide key={index}>
//               <div
//                 className="relative w-full md:h-[554px] h-[55vh] overflow-hidden cursor-pointer"
//                 onClick={() => {
//                   if (index === activeIndex) {
//                     setIsModalOpen(true);
//                   }
//                 }}
//               >
//                 <div
//                   className={`transition-all duration-300 ease-in-out will-change-transform h-full w-full ${
//                     index === activeIndex
//                       ? "scale-100 h-[554px]"
//                       : "scale-[0.95] opacity-80 h-[459px] mt-6"
//                   }`}
//                 >
//                   <Image
//                     src={`https://${img.url}`}
//                     alt={`Image ${index + 1}`}
//                     fill
//                     className="rounded-3xl object-cover"
//                     loading={index < 3 ? "eager" : "lazy"}
//                   />
//                 </div>
//               </div>
//             </SwiperSlide>
//           ))}
//         </Swiper>

//         {/* Custom navigation icons */}
//     <div className="absolute bottom-16 left-1/2 transform -translate-x-1/2 z-10 flex space-x-16">
//     <div
//       className="flex items-center justify-center w-8 h-8 rounded-full shadow-md cursor-pointer"
//       onClick={() => swiperRef.current.swiper.slidePrev()}
//     >
//       <CircleChevronLeft size="large" color="#fff" strokeWidth="1px" />
//     </div>
//     <div
//       className="flex items-center justify-center w-8 h-8 rounded-full shadow-md cursor-pointer"
//       onClick={() => swiperRef.current.swiper.slideNext()}
//     >
//       <CircleChevronRight size="large" color="#fff" strokeWidth="1px" />
//     </div>
//   </div>
// </div>

//       {/* Modal Popup for Collage View */}
//       <AnimatePresence>
//         {isModalOpen && (
//           <motion.div
//             initial={{ opacity: 0 }}
//             animate={{ opacity: 1 }}
//             exit={{ opacity: 0 }}
//             className="fixed inset-0 z-50 bg-black/70 flex items-center justify-center px-4"
//           >
//             <motion.div
//               initial={{ scale: 0.95, y: 50 }}
//               animate={{ scale: 1, y: 0 }}
//               exit={{ scale: 0.95, y: 50 }}
//               transition={{ duration: 0.3 }}
//               className="relative bg-white rounded-2xl max-w-6xl w-full h-[90vh] p-6 overflow-y-auto"
//             >
//               <button
//                 onClick={() => setIsModalOpen(false)}
//                 className="absolute top-4 right-4 text-3xl font-bold text-gray-600 hover:text-black"
//               >
//                 &times;
//               </button>

//               <h2 className="text-2xl font-semibold mb-6 text-center">Photo Gallery</h2>

//               <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
//                 {image.map((img, index) => (
//                   <div
//                     key={index}
//                     className="relative w-full aspect-video rounded-xl overflow-hidden"
//                   >
//                     <Image
//                       src={`https://${img.url}`}
//                       alt={`Collage Image ${index + 1}`}
//                       fill
//                       className="object-cover rounded-xl"
//                     />
//                   </div>
//                 ))}
//               </div>
//             </motion.div>
//           </motion.div>
//         )}
//       </AnimatePresence>
//     </>
//   );
// }

// import React, { useState, useRef } from "react";
// import { Swiper, SwiperSlide } from "swiper/react";
// import Image from "next/image";
// import { CircleChevronLeft, CircleChevronRight } from "lucide-react";
// import SwiperCore, { Navigation, Pagination, Autoplay } from "swiper";
// import "swiper/css";

// SwiperCore.use([Navigation, Pagination, Autoplay]);

// export default function Carousel({ image }) {
//    // eslint-disable-next-line no-unused-vars
//    const [hostelData, setHostelData] = useState();
//   const [activeIndex, setActiveIndex] = useState(0);
//   const [showModal, setShowModal] = useState(false);
//   const swiperRef = useRef(null);

//   return (
//     <div className="relative">
//       {/* Swiper Carousel */}
//       <Swiper
//         slidesPerView={1.7}
//         centeredSlides={true}
//         navigation={false}
//         pagination={false} // Disable pagination at the bottom
//         onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
//         className="mySwiper"
//         ref={swiperRef}
//         spaceBetween={10}
//         autoplay={{
//           delay: 5000,
//           disableOnInteraction: false,
//         }}
//         initialSlide={1}
//       >
//         {image.map((img, index) => (
//           <SwiperSlide key={index}>
//             <div className="relative w-full md:h-[554px] h-[55vh] overflow-hidden">
//               <div
//                 className={`transition-all duration-300 ease-in-out will-change-transform h-full w-full cursor-pointer ${
//                   index === activeIndex ? "scale-100 h-[554px]" : "scale-[0.95] opacity-80 h-[459px] mt-8"
//                 }`}
//                 onClick={() => index === activeIndex && setShowModal(true)}
//               >
//                 <Image
//                   src={`https://${img.url}`}
//                   alt={`Image ${index + 1}`}
//                   layout="fill"
//                   objectFit="cover"
//                   className="rounded-3xl"
//                   loading={index < 3 ? "eager" : "lazy"}
//                 />
//               </div>
//             </div>
//           </SwiperSlide>
//         ))}
//       </Swiper>

//       {/* Navigation Arrows */}
//       <div className="absolute bottom-16 left-1/2 transform -translate-x-1/2 z-10 flex space-x-16">
//         <div
//           className="flex items-center justify-center w-8 h-8 rounded-full shadow-md cursor-pointer"
//           onClick={() => swiperRef.current.swiper.slidePrev()}
//         >
//           <CircleChevronLeft size="large" color="#fff" strokeWidth="1px" />
//         </div>
//         <div
//           className="flex items-center justify-center w-8 h-8 rounded-full shadow-md cursor-pointer"
//           onClick={() => swiperRef.current.swiper.slideNext()}
//         >
//           <CircleChevronRight size="large" color="#fff" strokeWidth="1px" />
//         </div>
//       </div>

//       {/* Collage Modal */}
//       {showModal && (
//         <div className="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center px-4 top-8">
//           <div className="relative max-w-6xl w-full h-[90vh] overflow-y-auto bg-white rounded-xl p-4">
//             {/* Close Button */}
//             <button
//               onClick={() => setShowModal(false)}
//               className="absolute top-4 right-4 text-black text-xl font-bold"
//             >
//               &times;
//             </button>
//             <h1 className="text-4xl font-semibold mb-6 text-center text-black font-mashiny z-50">
//             Photo Gallery
//             </h1>
//             {/* Collage Grid */}
//             <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
//               {image.map((img, index) => (
//                 <div
//                   key={index}
//                   className={`relative overflow-hidden rounded-2xl group ${
//                     index % 5 === 0 ? "row-span-2 col-span-2" : ""
//                   }`}
//                 >
//                   <Image
//                     src={`https://${img.url}`}
//                     alt={`Collage Image ${index + 1}`}
//                     width={600}
//                     height={400}
//                     className="object-cover w-full h-full transition-transform duration-1000 group-hover:scale-110"
//                   />
//                 </div>
//               ))}
//             </div>
//           </div>
//         </div>
//       )}
//     </div>
//   );
// }

// import React, { useState, useRef } from "react";
// import { Swiper, SwiperSlide } from "swiper/react";
// import Image from "next/image";
// import { CircleChevronLeft, CircleChevronRight } from "lucide-react";
// import SwiperCore, { Navigation, Pagination, Autoplay } from "swiper";
// import "swiper/css";

// SwiperCore.use([Navigation, Pagination, Autoplay]);

// export default function Carousel({ image }) {
//   const [activeIndex, setActiveIndex] = useState(0);
//   const [showModal, setShowModal] = useState(false);
//   const [imagePreviewModal, setImagePreviewModal] = useState(false);
//   const [previewIndex, setPreviewIndex] = useState(0);
//   const swiperRef = useRef(null);

//   return (
//     <div className="relative">
//       {/* Swiper Carousel */}
//       <Swiper
//         slidesPerView={1.7}
//         centeredSlides={true}
//         navigation={false}
//         pagination={false}
//         onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
//         className="mySwiper"
//         ref={swiperRef}
//         spaceBetween={10}
//         autoplay={{
//           delay: 5000,
//           disableOnInteraction: false,
//         }}
//         initialSlide={1}
//       >
//         {image.map((img, index) => (
//           <SwiperSlide key={index}>
//             <div className="relative w-full md:h-[554px] h-[55vh] overflow-hidden">
//               <div
//                 className={`transition-all duration-300 ease-in-out will-change-transform h-full w-full cursor-pointer ${
//                   index === activeIndex ? "scale-100 h-[554px]" : "scale-[0.95] opacity-80 h-[459px] mt-8"
//                 }`}
//                 onClick={() => index === activeIndex && setShowModal(true)}
//               >
//                 <Image
//                   src={`https://${img.url}`}
//                   alt={`Image ${index + 1}`}
//                   layout="fill"
//                   objectFit="cover"
//                   className="rounded-3xl"
//                   loading={index < 3 ? "eager" : "lazy"}
//                 />
//               </div>
//             </div>
//           </SwiperSlide>
//         ))}
//       </Swiper>

//       {/* Navigation Arrows */}
//       <div className="absolute bottom-16 left-1/2 transform -translate-x-1/2 z-10 flex space-x-16">
//         <div
//           className="flex items-center justify-center w-8 h-8 rounded-full shadow-md cursor-pointer"
//           onClick={() => swiperRef.current.swiper.slidePrev()}
//         >
//           <CircleChevronLeft size="large" color="#fff" strokeWidth="1px" />
//         </div>
//         <div
//           className="flex items-center justify-center w-8 h-8 rounded-full shadow-md cursor-pointer"
//           onClick={() => swiperRef.current.swiper.slideNext()}
//         >
//           <CircleChevronRight size="large" color="#fff" strokeWidth="1px" />
//         </div>
//       </div>

//       {/* Collage Modal */}
//       {showModal && (
//         <div className="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center px-4 top-8">
//           <div className="relative max-w-6xl w-full h-[90vh] overflow-y-auto bg-white rounded-xl p-4">
//             <button
//               onClick={() => setShowModal(false)}
//               className="absolute top-4 right-4 text-black text-xl font-bold"
//             >
//               &times;
//             </button>
//             <h1 className="text-4xl font-semibold mb-6 text-center text-black font-mashiny z-50">
//               Photo Gallery
//             </h1>
//             <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
//               {image.map((img, index) => (
//                 <div
//                   key={index}
//                   className={`relative overflow-hidden rounded-2xl group ${
//                     index % 5 === 0 ? "row-span-2 col-span-2" : ""
//                   }`}
//                   onClick={() => {
//                     setPreviewIndex(index);
//                     setImagePreviewModal(true);
//                   }}
//                 >
//                   <Image
//                     src={`https://${img.url}`}
//                     alt={`Collage Image ${index + 1}`}
//                     width={600}
//                     height={400}
//                     className="object-cover w-full h-full transition-transform duration-1000 group-hover:scale-110"
//                   />
//                 </div>
//               ))}
//             </div>
//           </div>
//         </div>
//       )}

//       {/* Image Preview Modal */}
//       {imagePreviewModal && (
//         <div className="fixed inset-0 bg-black bg-opacity-90 z-[999] flex flex-col items-center justify-center px-4">
//           <button
//             onClick={() => setImagePreviewModal(false)}
//             className="absolute top-4 right-4 text-white text-3xl font-bold z-50"
//           >
//             &times;
//           </button>
//           <div className="relative w-full max-w-4xl h-[70vh] mb-4">
//             <Image
//               src={`https://${image[previewIndex].url}`}
//               alt={`Preview Image ${previewIndex + 1}`}
//               layout="fill"
//               objectFit="contain"
//               className="rounded-xl"
//             />
//           </div>
//           <Swiper
//             slidesPerView={5}
//             spaceBetween={10}
//             navigation
//             className="w-full max-w-5xl"
//           >
//             {image.map((img, idx) => (
//               <SwiperSlide key={idx}>
//                 <div
//                   className={`relative w-full h-20 rounded-md cursor-pointer border ${
//                     idx === previewIndex ? "border-white" : "border-transparent"
//                   }`}
//                   onClick={() => setPreviewIndex(idx)}
//                 >
//                   <Image
//                     src={`https://${img.url}`}
//                     alt={`Thumb ${idx + 1}`}
//                     layout="fill"
//                     objectFit="cover"
//                     className="rounded-md"
//                   />
//                 </div>
//               </SwiperSlide>
//             ))}
//           </Swiper>
//         </div>
//       )}
//     </div>
//   );
// }

import React, { useState, useRef, useEffect } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import Image from "next/image";
import { CircleChevronLeft, CircleChevronRight } from "lucide-react";
import SwiperCore, { Navigation, Pagination } from "swiper";
import "swiper/css";
import "swiper/css/navigation";
import { Autoplay } from "swiper/modules";

// import { FaChevronLeft, FaChevronRight } from "react-icons/fa";

SwiperCore.use([Navigation, Pagination, Autoplay]);
const videoExt = /\.(mp4|webm|ogg|mov|m4v)$/i;
export default function Carousel({ image, hostelName }) {
  const [activeIndex, setActiveIndex] = useState(0);
  const [showModal, setShowModal] = useState(false);
  const [imagePreviewModal, setImagePreviewModal] = useState(false);
  const [previewIndex, setPreviewIndex] = useState(0);
  const [originalActiveIndex, setOriginalActiveIndex] = useState(null);
  const [imagesLoaded, setImagesLoaded] = useState({});

  const handleImageLoad = (id) => {
    setImagesLoaded((prev) => ({ ...prev, [id]: true }));
  };

  const swiperRef = useRef(null);

  const mainSwiperRef = useRef(null);

  const mainThumbRef = useRef(null);

  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!mainSwiperRef.current) return;

      if (e.key === "ArrowLeft") {
        mainSwiperRef.current.swiper.slidePrev();
      } else if (e.key === "ArrowRight") {
        mainSwiperRef.current.swiper.slideNext();
      }
    };

    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, []);

  return (
    <div className="relative">
      {/* Swiper Carousel */}
      {/* <Swiper
        modules={[Autoplay]}
        slidesPerView={1.7}
        centeredSlides={true}
        navigation={false}
        pagination={false}
        onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
        className="mySwiper"
        ref={swiperRef}
        spaceBetween={10}
        autoplay={{
          delay: 5000,
          disableOnInteraction: false,
        }}
        initialSlide={1}
      >
        {image.map((img, index) => (
          <SwiperSlide key={index}>
            <div className="relative w-full md:h-[554px] h-[40vh] overflow-hidden">
              <div
                className={`transition-all duration-300 ease-in-out will-change-transform h-full w-full cursor-pointer ${
                  index === activeIndex
                    ? "scale-100 h-[554px]"
                    : " opacity-80 h-[198px] md:h-[439px]  mt-9"
                }`}
                onClick={() => index === activeIndex && setShowModal(true)}
              >
                
                {img?.objectUrl && (
                  <Image
                    // src={`https://${img.url}`}
                    src={img?.objectUrl}
                    alt={`Image ${index + 1}`}
                    layout="fill"
                    className="rounded-3xl object-cover"
                    loading={index < 3 ? "eager" : "lazy"}
                  />
                )}
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper> */}

      <Swiper
        modules={[Autoplay]}
        slidesPerView={1.7}
        centeredSlides={true}
        navigation={false}
        pagination={false}
        loop={true}
        onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
        className="mySwiper"
        ref={swiperRef}
        spaceBetween={10}
        autoplay={{
          delay: 5000,
          disableOnInteraction: false,
        }}
        initialSlide={1}
      >
        {image.length > 0 ? (
          image
            .sort((a, b) => {
              const aEndsWithVideo = videoExt.test(a.objectUrl);
              const bEndsWithVideo = videoExt.test(b.objectUrl);

              return aEndsWithVideo === bEndsWithVideo
                ? 0
                : aEndsWithVideo
                ? -1
                : 1;
            })
            .map((img, index) => {
              const isLoaded = imagesLoaded?.[`${img.id || index}`];

              return (
                <SwiperSlide key={index}>
                  <div className="relative w-full md:h-[554px] h-[40vh] overflow-hidden">
                    <div
                      className={`transition-all duration-300 ease-in-out will-change-transform h-full w-full cursor-pointer ${
                        index === activeIndex
                          ? "scale-100 h-[554px]"
                          : "opacity-80 h-[198px] md:h-[439px] mt-9"
                      }`}
                      onClick={() => {
                        if (index === activeIndex) {
                          setShowModal(true);
                        } else {
                          // Navigate to the clicked slide
                          swiperRef.current?.swiper.slideToLoop(index);
                        }
                      }}
                      onMouseEnter={() => {
                        swiperRef.current?.swiper.autoplay.stop();
                        swiperRef.current?.swiper.autoplay.pause();
                      }}
                      onMouseLeave={() => {
                        swiperRef.current?.swiper.autoplay.start();
                      }}
                    >
                      {!isLoaded && (
                        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-3xl flex items-center justify-center">
                          <h1 className="text-white font-bold font-manrope text-4xl">
                            Mixdorm
                          </h1>
                        </div>
                      )}

                      {img?.objectUrl ? (
                        videoExt.test(img.objectUrl) ? (
                          <video
                            src={img.objectUrl}
                            muted
                            playsInline
                            loop
                            autoPlay
                            onLoadedData={() => {
                              handleImageLoad(img.id || index);
                            }}
                            className={`rounded-3xl object-cover size-full ${
                              !isLoaded ? "opacity-0" : "opacity-100"
                            }`}
                          />
                        ) : (
                          <Image
                            src={img.objectUrl}
                            alt={`Image ${index + 1}`}
                            layout="fill"
                            className={`rounded-3xl object-cover ${
                              !isLoaded ? "opacity-0" : "opacity-100"
                            }`}
                            loading={index < 3 ? "eager" : "lazy"}
                            priority={index < 3}
                            onLoadingComplete={() =>
                              handleImageLoad(img.id || index)
                            }
                          />
                        )
                      ) : (
                        <div className="absolute inset-0 bg-gray-300 animate-pulse rounded-3xl flex items-center justify-center">
                          <h1 className="text-white font-bold font-manrope text-4xl">
                            Mixdorm
                          </h1>
                        </div>
                      )}
                    </div>
                  </div>
                </SwiperSlide>
              );
            })
        ) : (
          <SwiperSlide>
            <div className="relative w-full md:h-[554px] h-[40vh] overflow-hidden">
              <div className="transition-all duration-300 ease-in-out will-change-transform h-full w-full">
                <div className="absolute inset-0 bg-gray-300 animate-pulse rounded-3xl flex items-center justify-center">
                  <h1 className="text-white font-bold font-manrope text-4xl">
                    Mixdorm
                  </h1>
                </div>
              </div>
            </div>
          </SwiperSlide>
        )}
      </Swiper>

      {/* Navigation Arrows */}
      {/* <div className="absolute bottom-16 left-1/2 transform -translate-x-1/2 z-10 flex space-x-16">
        <div
          className="flex items-center justify-center w-10 h-10 rounded-full shadow-md cursor-pointer"
          onClick={() => swiperRef.current.swiper.slidePrev()}
        >
          <CircleChevronLeft size="large" color="#fff" strokeWidth="1px" />
        </div>
        <div
          className="flex items-center justify-center w-10 h-10 rounded-full shadow-md cursor-pointer"
          onClick={() => swiperRef.current.swiper.slideNext()}
        >
          <CircleChevronRight size="large" color="#fff" strokeWidth="1px" />
        </div>
      </div> */}

      {/* Collage Modal */}
      {showModal && (
        <div
          className="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center px-4 top-8"
          onClick={() => setShowModal(false)}
        >
          <div
            className="relative max-w-6xl w-full h-[90vh] overflow-y-auto bg-white rounded-xl p-4"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              onClick={() => setShowModal(false)}
              className="absolute top-4 right-4 text-black text-xl font-bold"
            >
              &times;
            </button>
            <h1 className="text-3xl font-semibold mb-6 text-center text-black font-manrope">
              {hostelName}
            </h1>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
              {image.map((img, index) => (
                <div
                  key={index}
                  className={`relative overflow-hidden rounded-2xl group ${
                    index % 5 === 0 ? "row-span-2 col-span-2" : ""
                  }`}
                  // onClick={() => {
                  //   setPreviewIndex(index);
                  //   setImagePreviewModal(true);
                  // }}
                  onClick={() => {
                    setOriginalActiveIndex(activeIndex); // Save the current carousel index
                    setPreviewIndex(index);
                    setImagePreviewModal(true);
                  }}
                >
                  <Image
                    // src={`https://${img.url}`}
                    src={img?.objectUrl}
                    alt={`Collage Image ${index + 1}`}
                    width={600}
                    height={400}
                    className="object-cover w-full h-full transition-transform duration-1000 ease-in-out group-hover:scale-110"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Image Preview Modal with Thumbnails */}
      {imagePreviewModal && (
        <div className="fixed inset-0 bg-black bg-opacity-90 z-[999] flex flex-col items-center justify-center px-4">
          <button
            onClick={() => {
              setImagePreviewModal(false);
              if (originalActiveIndex !== null) {
                swiperRef.current.swiper.slideToLoop(originalActiveIndex);
                setActiveIndex(originalActiveIndex);
                setOriginalActiveIndex(null);
              }
            }}
            className="absolute top-4 right-4 text-white text-3xl font-bold z-50"
          >
            &times;
          </button>

          {/* Main Swiper */}
          <div className="relative w-full max-w-4xl h-[70vh] mb-4">
            <Swiper
              slidesPerView={1}
              centeredSlides={true}
              navigation={false}
              pagination={false}
              loop={true}
              onSlideChange={(swiper) => {
                setActiveIndex(swiper.realIndex);
                if (mainThumbRef.current?.swiper) {
                  mainThumbRef.current.swiper.slideTo(swiper.realIndex);
                }
              }}
              className="mySwiper h-full"
              ref={mainSwiperRef}
              spaceBetween={10}
              autoplay={{
                delay: 5000,
                disableOnInteraction: false,
              }}
              initialSlide={previewIndex}
            >
              {image.map((img, idx) => (
                <SwiperSlide key={idx}>
                  <div className="w-full h-full relative">
                    <Image
                      // src={`https://${img.url}`}
                      src={img?.objectUrl}
                      alt={`Preview Image ${idx + 1}`}
                      layout="fill"
                      className="rounded-xl object-contain"
                    />
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>

            {/* Custom arrows for main swiper */}
            <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 z-10 flex space-x-16">
              <div
                className="flex items-center justify-center w-8 h-8 shadow-md cursor-pointer"
                onClick={() => mainSwiperRef.current.swiper.slidePrev()}
              >
                <CircleChevronLeft size={24} color="#fff" />
              </div>
              <div
                className="flex items-center justify-center w-8 h-8 shadow-md cursor-pointer"
                onClick={() => mainSwiperRef.current.swiper.slideNext()}
              >
                <CircleChevronRight size={24} color="#fff" />
              </div>
            </div>
          </div>

          {/* Thumbnail Swiper */}
          <div className="w-full max-w-5xl mt-4 relative">
            <Swiper
              slidesPerView={5}
              spaceBetween={10}
              centeredSlides={true}
              navigation={false}
              pagination={false}
              loop={true}
              className="mySwiper h-full"
              ref={mainThumbRef}
              autoplay={{
                delay: 5000,
                disableOnInteraction: false,
              }}
              initialSlide={previewIndex}
            >
              {image.map((img, idx) => (
                <SwiperSlide key={idx}>
                  <div
                    className={`relative w-full h-32 rounded-md cursor-pointer border ${
                      idx === activeIndex
                        ? "border-white"
                        : "border-transparent"
                    }`}
                    onClick={() => mainSwiperRef.current.swiper.slideTo(idx)}
                  >
                    <Image
                      // src={`https://${img.url}`}
                      src={img?.objectUrl}
                      alt={`Thumb ${idx + 1}`}
                      layout="fill"
                      className="rounded-md object-cover"
                    />
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>

            {/* Arrows for thumbnail swiper */}
            {/* <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 z-20 flex space-x-16">
              <div
                className="flex items-center justify-center w-8 h-8 rounded-full shadow-md cursor-pointer"
                onClick={() => mainThumbRef.current.swiper.slidePrev()}
              >
                <CircleChevronLeft size={24} color="#fff" />
              </div>
              <div
                className="flex items-center justify-center w-8 h-8 rounded-full shadow-md cursor-pointer"
                onClick={() => mainThumbRef.current.swiper.slideNext()}
              >
                <CircleChevronRight size={24} color="#fff" />
              </div>
            </div> */}
          </div>
        </div>
      )}
    </div>
  );
}
