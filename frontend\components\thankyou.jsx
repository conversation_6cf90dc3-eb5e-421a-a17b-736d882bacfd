
// import { ChevronsRight } from "lucide-react";
import { Info } from "lucide-react";
import Image from "next/image";
// import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import countries from "world-countries";

const Confirmation = ({
  successRes,
  data,
  roomData,
  name,
  paymentVerificationResponse,
}) => {
  console?.log(
    "paymentVerificationResponse",
    paymentVerificationResponse,
    successRes,
    data,
    roomData
  );
  // const router = useRouter();
  const [currencyData, setCurrencyData] = useState({});

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);
      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);

  const getCurrencySymbol = (currencyCode) => {
    return currencyData[currencyCode] || currencyCode;
  };

  const baseAdvance = paymentVerificationResponse?.baseAdvance || 0;
  const tax = (baseAdvance * 0.18).toFixed(2);
  const fees = (baseAdvance * 1.18 * 0.03).toFixed(2);

  return (
    <div className="bg-gray-100 pb-10 pt-20 relative overflow-y-auto">
   <div className="absolute -top-8 right-10 w-40 h-40 bg-cyan-400 rounded-full blur-2xl opacity-30 z-10"></div>
      <div className="absolute bottom-[40%] left-4 w-48 h-48 bg-yellow-400 rounded-full blur-2xl opacity-30 z-10"></div>
      <div className="absolute bottom-0 right-10 w-40 h-40 bg-pink-400 rounded-full blur-2xl opacity-45 z-10"></div>
      <div className="">
        <div className="bg-white text-center lg:pt-5 pt-2 rounded-3xl shadow-lg lg:w-[75%] w-[95%] z-20 mx-auto mb-4 relative">
          <div className="flex justify-center items-center">
            <Image
              className="ml-auto -mt-20 z-10 max-w-[180px] sm:max-w-[200px] md:max-w-[230px] h-auto"
              src="/thank-you.webp"
              width={230}
              height={230}
            />
            <div className="bg-yellow-100 border border-yellow-600 hight-min sm:py-3 py-2.5 sm:pl-20 pl-16 sm:pr-16 pr-8 mr-auto text-left -ml-20">
              <p className="sm:text-2xl text-xl font-semibold font-roboto text-black/90">
                Booking <br /> Confirmed !!
              </p>
            </div>
          </div>
          <div className="bg-primary-blue/60 xs:p-6 p-4 rounded-b-3xl">
            <h2 className="sm:text-2xl xs:text-xl text-lg font-semibold xs:mb-3 mb-2">
              Booked & Ready to Roll,{" "}
              <span className="text-black font-bold font-inter">{name}</span> - Mixdorm Magic Activated
            </h2>
            <p className="mb-0 xs:text-sm text-xs">
              All set to meet your vibe tribe! Your bed’s ready, your crew’s waiting, and the city’s calling. Let the stories begin.
            </p>
          </div>
        </div>
        <div className="bg-white lg:p-10 xs:p-7 p-5 rounded-3xl shadow-lg lg:w-[75%] w-[95%] z-20 mx-auto relative mb-24">
          <h3 className="font-semibold text-xl mb-2">Your Booking Detail</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 xl:grid-cols-7 sm:gap-4 gap-3">
            <div className="flex flex-col bg-gray-100 border rounded-md px-4 py-2">
              <span className="font-semibold">Order Id</span>
              <span className="text-sm whitespace-nowrap">
                {paymentVerificationResponse?.bookingId}
              </span>
            </div>

            <div className="flex flex-col bg-gray-100 border rounded-md px-4 py-2">
              <span className="font-semibold">Check In</span>
              <span className="text-sm whitespace-nowrap">
                {paymentVerificationResponse?.checkInDate
                  ? (() => {
                      const date = new Date(
                        paymentVerificationResponse?.checkInDate
                      );
                      return `${String(date.getDate()).padStart(
                        2,
                        "0"
                      )}-${String(date.getMonth() + 1).padStart(
                        2,
                        "0"
                      )}-${date.getFullYear()}`;
                    })()
                  : ""}
              </span>
            </div>

            <div className="flex flex-col bg-gray-100 border rounded-md px-4 py-2">
              <span className="font-semibold">Check Out</span>
              <span className="text-sm whitespace-nowrap">
                {paymentVerificationResponse?.checkOutDate
                  ? (() => {
                      const date = new Date(
                        paymentVerificationResponse?.checkOutDate
                      );
                      return `${String(date.getDate()).padStart(
                        2,
                        "0"
                      )}-${String(date.getMonth() + 1).padStart(
                        2,
                        "0"
                      )}-${date.getFullYear()}`;
                    })()
                  : ""}
              </span>
            </div>

            <div className="flex flex-col bg-gray-100 border rounded-md px-4 py-2 relative  ">
              <span className="font-semibold">Paid</span>
              <div className="flex items-center gap-1 text-sm">
                <span className="whitespace-nowrap">
                  {getCurrencySymbol(paymentVerificationResponse?.currency)}{" "}
                  {paymentVerificationResponse?.paidAmount}
                </span>

                {/* Tooltip Trigger */}
                <div className="relative group cursor-pointer">
                  <Info className="w-4 h-4 text-gray-500" />

                  {/* Tooltip */}
                  <div className="absolute z-10 hidden group-hover:block bg-black text-white text-xs px-2 py-1 rounded shadow-md w-max -top-10 -left-3">
                    Tax:{" "}
                    {getCurrencySymbol(paymentVerificationResponse?.currency)}{" "}
                    {tax} <br />
                    Fees:{" "}
                    {getCurrencySymbol(
                      paymentVerificationResponse?.currency
                    )}{" "}
                    {fees}
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col bg-gray-100 border rounded-md pr-2 pl-2  py-2 ">
              <span className="font-semibold whitespace-nowrap">Pay On Arrival</span>
              <span className="text-sm whitespace-nowrap ">
                {getCurrencySymbol(paymentVerificationResponse?.currency)}{" "}
                {paymentVerificationResponse?.payOnArrival}
              </span>
            </div>

            <div className="flex flex-col bg-gray-100 border rounded-md pr-2 pl-2 py-2">
              <span className="font-semibold whitespace-nowrap">Total Payment</span>
              <span className="text-sm whitespace-nowrap">
                {getCurrencySymbol(paymentVerificationResponse?.currency)}{" "}
                {paymentVerificationResponse?.paidAmount +
                  paymentVerificationResponse?.payOnArrival}
              </span>
            </div>

            <div className="flex flex-col bg-gray-100 border rounded-md px-4 py-2">
              <span className="font-semibold">Status</span>
              <span className="text-sm whitespace-nowrap">
                {paymentVerificationResponse?.status}
              </span>
            </div>
          </div>

          <div className="pt-4 mt-4">
            <h3 className="font-semibold text-xl mb-2">Room Details</h3>
       
          </div>
          {/* Add list view */}
          <div className="mt-3 overflow-x-auto hover-scrollbar md:block hidden">
            <table className="w-full min-w-[600px] border rounded-t-lg border-gray-300">
              <thead>
                <tr>
                  <th className="px-3 py-2.5 bg-black/70 font-semibold sm:text-base text-sm text-white text-center text-nowrap">
                    Order ID
                  </th>
                  <th className="px-3 py-2.5 bg-black/70 font-semibold sm:text-base text-sm text-white text-center text-nowrap">
                    Room Type
                  </th>
                  <th className="px-3 py-2.5 bg-black/70 font-semibold sm:text-base text-sm text-white text-center text-nowrap">
                    Beds/Rooms
                  </th>
                  <th className="px-3 py-2.5 bg-black/70 font-semibold sm:text-base text-sm text-white text-center text-nowrap">
                    Check In
                  </th>
                  <th className="px-3 py-2.5 bg-black/70 font-semibold sm:text-base text-sm text-white text-center text-nowrap">
                    Check Out
                  </th>
                  <th className="px-3 py-2.5 bg-black/70 font-semibold sm:text-base text-sm text-white text-center  text-nowrap">
                    Paid
                  </th>
                  <th className="px-3 py-2.5 bg-black/70 font-semibold sm:text-base text-sm text-white text-center text-nowrap ">
                    Pay On Arrival
                  </th>
                  <th className="px-3 py-2.5 bg-black/70 font-semibold sm:text-base text-sm text-white text-center text-nowrap">
                    Total Payment
                  </th>
                  <th className="px-3 py-2.5 bg-black/70 font-semibold sm:text-base text-sm text-white text-center text-nowrap">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody>
                {paymentVerificationResponse?.subBookings?.map(
                  (bookingData) => (
                    <tr key={bookingData?.subBookingId}>
                      <td className="xs:text-sm text-xs px-3 py-3 border-b text-center">
                        {bookingData?.subBookingId}
                      </td>
                      <td className="xs:text-sm text-xs px-3 py-3 border-b text-center flex flex-col whitespace-nowrap">
                        {bookingData?.roomName || " - "}({bookingData?.ratePlan?.name})
                      </td>
                      <td className="xs:text-sm text-xs px-3 py-3 border-b text-center">
                        {bookingData?.beds || "1"}
                      </td>
                      <td className="xs:text-sm text-xs px-3 py-3 border-b text-center">
                        {bookingData?.checkInDate
                          ? new Date(
                              bookingData?.checkInDate
                            ).toLocaleDateString("en-GB")
                          : ""}
                      </td>
                      <td className="xs:text-sm text-xs px-3 py-3 border-b text-center">
                        {bookingData?.checkOutDate
                          ? new Date(
                              bookingData?.checkOutDate
                            ).toLocaleDateString("en-GB")
                          : ""}
                      </td>
                      <td className="xs:text-sm text-xs px-3 py-3  flex items-center gap-1 relative group text-center">
                        {getCurrencySymbol(bookingData?.currency)}{" "}
                        {(
                          bookingData?.rate?.baseAmount *
                          0.15 *
                          1.18 *
                          1.03
                        ).toFixed(2)}
                        {/* Info icon with tooltip */}
                        <div className="relative  cursor-pointer">
                          <Info className="w-4 h-4 text-gray-500 peer" />

                          {/* Tooltip */}
                          <div className="absolute z-10 hidden peer-hover:block bg-black text-white text-xs px-2 py-1 rounded shadow-md w-max -top-6 -left-8  whitespace-nowrap">
                            Tax and Fees Included
                          </div>
                        </div>
                      </td>
                      <td className="xs:text-sm text-xs px-3 py-3 border-b text-center">
                        {getCurrencySymbol(bookingData?.currency)}{" "}
                        {(bookingData?.rate?.baseAmount * 0.85).toFixed(2)}
                      </td>
                      <td className="xs:text-sm text-xs px-3 py-3 border-b text-center">
                        {getCurrencySymbol(bookingData?.currency)}{" "}
                        {(
                          bookingData?.rate?.baseAmount * 0.85 +
                          bookingData?.rate?.baseAmount * 0.15 * 1.18 * 1.03
                        ).toFixed(2)}
                      </td>
                      <td className="xs:text-sm text-xs px-3 py-3 border-b text-center">
                        <span className="text-green-950 py-1 px-3 border border-green-800 bg-green-200 rounded-full capitalize">
                          {bookingData?.status}
                        </span>
                      </td>
                    </tr>
                  )
                )}
              </tbody>
            </table>
          </div>

          <div className="md:hidden block">
            {paymentVerificationResponse?.subBookings?.map((bookingData) => (
              <table key={bookingData?.subBookingId} className="mt-3 w-full border rounded-t-lg border-gray-300">
                <tbody>
                  <tr key={bookingData?.subBookingId}>
                    <th className="px-3 border-b py-2.5 bg-black/70 font-semibold sm:text-base text-sm text-white text-left text-nowrap">
                      Order ID
                    </th>
                    <td className="xs:text-sm text-xs px-3 py-3 border-b">
                      {bookingData?.subBookingId}
                    </td>
                  </tr>
                  <tr>
                    <th className="px-3 border-b py-2.5 bg-black/70 font-semibold sm:text-base text-sm text-white text-left text-nowrap">
                      Beds/Rooms
                    </th>
                    <td className="xs:text-sm text-xs px-3 py-3 border-b">
                      {bookingData?.beds || "1"}
                    </td>
                  </tr>
                  <tr>
                    <th className="px-3 border-b py-2.5 bg-black/70 font-semibold sm:text-base text-sm text-white text-left text-nowrap">
                      Check In
                    </th>
                    <td className="xs:text-sm text-xs px-3 py-3 border-b">
                      {bookingData?.checkInDate
                        ? new Date(bookingData?.checkInDate).toLocaleDateString(
                            "en-GB"
                          )
                        : ""}
                    </td>
                  </tr>
                  <tr>
                    <th className="px-3 border-b py-2.5 bg-black/70 font-semibold sm:text-base text-sm text-white text-left text-nowrap">
                      Check Out
                    </th>
                    <td className="xs:text-sm text-xs px-3 py-3 border-b">
                      {bookingData?.checkOutDate
                        ? new Date(
                            bookingData?.checkOutDate
                          ).toLocaleDateString("en-GB")
                        : ""}
                    </td>
                  </tr>
                  <tr>
                    <th className="px-3 border-b py-2.5 bg-black/70 font-semibold sm:text-base text-sm text-white text-left text-nowrap">
                      Paid
                    </th>
                    <td className="xs:text-sm text-xs px-3 py-3 border-b flex">
                      {getCurrencySymbol(bookingData?.currency)}{" "}
                      {(
                        bookingData?.rate?.baseAmount *
                        0.15 *
                        1.18 *
                        1.03
                      ).toFixed(2)}
                      {/* Info icon with tooltip */}
                      <span className="relative group cursor-pointer ml-2 md:ml-0">
                        <Info className="w-4 h-4 text-gray-500" />

                        {/* Tooltip */}
                        <div className="absolute z-10 hidden group-hover:block bg-black text-white text-xs px-2 py-1 rounded shadow-md w-max top-6 left-0 whitespace-nowrap">
                          Tax and Fees Included
                        </div>
                      </span>
                    </td>
                  </tr>

                  <tr>
                    <th className="px-3 border-b py-2.5 bg-black/70 font-semibold sm:text-base text-sm text-white text-left text-nowrap">
                      Pay On Arrival
                    </th>
                    <td className="xs:text-sm text-xs px-3 py-3 border-b">
                      {getCurrencySymbol(bookingData?.currency)}{" "}
                      {(bookingData?.rate?.baseAmount * 0.85).toFixed(2)}
                    </td>
                  </tr>
                  <tr>
                    <th className="px-3 border-b py-2.5 bg-black/70 font-semibold sm:text-base text-sm text-white text-left text-nowrap">
                      Total Payment
                    </th>
                    <td className="xs:text-sm text-xs px-3 py-3 border-b">
                      {getCurrencySymbol(bookingData?.currency)}{" "}
                      {(
                        bookingData?.rate?.baseAmount * 0.85 +
                        bookingData?.rate?.baseAmount * 0.15 * 1.18 * 1.03
                      ).toFixed(2)}
                    </td>
                  </tr>
                  <tr>
                    <th className="px-3 border-b py-2.5 bg-black/70 font-semibold sm:text-base text-sm text-white text-left text-nowrap">
                      Status
                    </th>
                    <td className="xs:text-sm text-xs px-3 py-3 border-b">
                      <span className="text-green-950 py-1 px-3 border border-green-800 bg-green-200 rounded-full capitalize">
                        {bookingData?.status}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            ))}


          </div>




       
        </div>
      </div>
    </div>
  );
};

export default Confirmation;
