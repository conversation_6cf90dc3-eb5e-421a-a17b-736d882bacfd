"use client";
import React, { useState } from "react";
import ActionPopup from "@/components/superadmin/ActionPopup";
import Link from "next/link";


const userDetails = () => {
  const [showPopup, setShowPopup] = useState(false);


  const handleActionClick = () => {
    setShowPopup(true);
    
  };

  const handleClosePopup = () => {
    setShowPopup(false);
  };

  const handleSaveChanges = () => {
    // Handle save logic here
    setShowPopup(false);
  };
  return (
    // <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth ">
    //   <h2 className="text-3xl font-bold text-black dark:text-white">
    //     User Details
    //   </h2>
    //   <div className="bg-white border rounded-xl mt-5 h-auto">
    //     <div className="p-8 flex flex-col gap-y-6">

    //       <div>
    //         <h1 className="text-xl font-bold">Personal Information</h1>

    //         {/* First Row */}
    //         <div className="flex flex-wrap gap-x-4 my-6">
    //           <div className="flex w-full sm:w-[45%] md:w-[15%] flex-col">
    //             <strong className="text-black/75">NAME</strong>
    //             <p className="mt-1 text-black/65">Christine Brooks</p>
    //           </div>
    //           <div className="flex w-full sm:w-[45%] md:w-[15%] flex-col mt-2">
    //             <strong className="text-black/75">Mobile Number</strong>
    //             <p className="mt-1 text-black/65">25364125</p>
    //           </div>
    //           <div className="flex w-full sm:w-[90%] md:w-[30%] flex-col mt-2">
    //             <strong className="text-black/75">DOB</strong>
    //             <p className="mt-1 text-black/65">04 Sep 1998</p>
    //           </div>
    //         </div>

    //         {/* Second Row */}
    //         <div className="flex flex-wrap gap-x-4">
    //           <div className="flex w-full sm:w-[45%] md:w-[15%] flex-col mt-2">
    //             <strong className="text-black/75">EMAIL</strong>
    //             <p className="mt-1 text-black/65">chistinn25@</p>
    //           </div>
    //           <div className="flex w-full sm:w-[45%] md:w-[15%] flex-col mt-2">
    //             <strong className="text-black/75">Age</strong>
    //             <p className="mt-1 text-black/65">35</p>
    //           </div>
    //           <div className="flex w-full sm:w-[90%] md:w-[30%] flex-col mt-2">
    //             <strong className="text-black/75">Gender</strong>
    //             <p className="mt-1 text-black/65">Male</p>
    //           </div>
    //         </div>
    //       </div>

    //       <div>
    //         <h1 className="text-xl font-bold">Residential Information</h1>
    //         <div className="flex flex-wrap gap-x-20 my-6">
    //           {/* Country Section */}
    //           <div className="flex w-full sm:w-[45%] md:w-[15%] flex-col">
    //             <strong className="text-black/75">Country</strong>
    //             <p className="mt-1 text-black/65">India</p>
    //           </div>

    //           {/* State Section */}
    //           <div className="flex w-full sm:w-[45%] md:w-[15%] flex-col mt-2">
    //             <strong className="text-black/75">State</strong>
    //             <p className="mt-1 text-black/65">Gujrat</p>
    //           </div>

    //           {/* Address Section */}
    //           <div className="flex w-full sm:w-[90%] md:w-[30%] flex-col mt-2">
    //             <strong className="text-black/75">Address</strong>
    //             <p className="mt-2 text-black/65">
    //               1901 Thornridge Cir. Shiloh, Hawaii 81063
    //             </p>
    //           </div>
    //         </div>

    //         <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-x-20 mt-2">
    //           {/* Pin Code Section */}
    //           <div className="flex flex-col">
    //             <strong className="text-black/75">Pin Code</strong>
    //             <p className="mt-1 text-black/65">309212</p>
    //           </div>
    //         </div>
    //       </div>

    //       <div>
    //         <h1 className="text-xl font-bold">Subscription Information</h1>
    //         <div className="flex items-center gap-x-20 mt-6">
    //           <div className="flex w-[15%] flex-col">
    //             <strong className="text-black/75">Subscription</strong>
    //             <p className="mt-2 text-black/65">Premium</p>
    //           </div>
    //         </div>
    //       </div>
    //       <div>
    //         <h1 className="text-xl font-bold">Verification Information</h1>
    //         <div className="flex items-center gap-x-20 my-6">
    //           <div className="flex w-[15%] flex-col">
    //             <strong className="text-black/75">Verification</strong>
    //             <p className="mt-2 text-black/65">Verified</p>
    //           </div>
    //         </div>
    //       </div>
    //       <div className="flex items-center justify-center">
    //         <button
    //           className="text-white p-2 w-full max-w-md rounded bg-sky-blue-650"
    //           onClick={handleActionClick}
    //         >
    //           Action
    //         </button>
    //       </div>
    //     </div>
    //   </div>
    //   {showPopup && (
    //     <ActionPopup onClose={handleClosePopup} onSave={handleSaveChanges} />
    //   )}
    // </div>

    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616] " >
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        User Details
      </h2>
      <div className="bg-white border rounded-xl mt-5 h-auto dark:bg-black dark:border-none">
        <div className="p-8 flex flex-col gap-y-6">
          {/* Personal Information */}
          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">Personal Information</h1>

            {/* First Row */}
            <div className="flex flex-wrap gap-4 my-6">
              <div className="flex flex-col w-full sm:w-[45%] lg:w-[15%]">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">Name</strong>
                <p className="mt-1 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">Christine Brooks</p>
              </div>
              <div className="flex flex-col w-full sm:w-[45%] lg:w-[15%]">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">Mobile Number</strong>
                <p className="mt-1 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">25364125</p>
              </div>
              <div className="flex flex-col w-full sm:w-[90%] lg:w-[30%]">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">DOB</strong>
                <p className="mt-1 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">04 Sep 1998</p>
              </div>
            </div>

            {/* Second Row */}
            <div className="flex flex-wrap gap-4">
              <div className="flex flex-col w-full sm:w-[45%] lg:w-[15%]">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">Email</strong>
                <p className="mt-1 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">chistinn25@</p>
              </div>
              <div className="flex flex-col w-full sm:w-[45%] lg:w-[15%]">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">Age</strong>
                <p className="mt-1 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">35</p>
              </div>
              <div className="flex flex-col w-full sm:w-[90%] lg:w-[30%]">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">Gender</strong>
                <p className="mt-1 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">Male</p>
              </div>
            </div>
          </div>

          {/* Residential Information */}
          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">Residential Information</h1>
            <div className="flex flex-wrap gap-4 my-6">
              <div className="flex flex-col w-full sm:w-[45%] lg:w-[15%]">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">Country</strong>
                <p className="mt-1 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">India</p>
              </div>
              <div className="flex flex-col w-full sm:w-[45%] lg:w-[15%]">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">State</strong>
                <p className="mt-1 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">Gujrat</p>
              </div>
              <div className="flex flex-col w-full sm:w-[90%] lg:w-[30%]">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">Address</strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  1901 Thornridge Cir. Shiloh, Hawaii 81063
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mt-2">
              <div className="flex flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">Pin Code</strong>
                <p className="mt-1 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">309212</p>
              </div>
            </div>
          </div>

          {/* Subscription Information */}
          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">Subscription Information</h1>
            <div className="flex flex-wrap gap-4 items-center mt-6">
              <div className="flex flex-col w-full sm:w-[45%] lg:w-[15%]">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">Subscription</strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">Premium</p>
              </div>
            </div>
          </div>

          {/* Verification Information */}
          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">Verification Information</h1>
            <div className="flex flex-wrap gap-4 items-center my-6">
              <div className="flex flex-col w-full sm:w-[45%] lg:w-[15%]">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">Verification</strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">Verified</p>
              </div>
            </div>
          </div>

          {/* Action Button */}
          {/* <div className="flex items-center justify-center">
            <button
              className="text-white p-2 w-full text-base font-semibold font-poppins max-w-md rounded bg-sky-blue-650"
              onClick={handleActionClick}
            >
              Action
            </button>
          </div> */}
            {/* Buttons */}
            <div className="flex items-start justify-start  gap-2 mt-6 px-4">
              <Link
                href={"/superadmin/dashboard/user"}
                className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-24 py-2 text-sm font-medium font-poppins border  rounded  border-gray-200 text-black-100 dark:text-gray-100`}
              >
                Cancel
              </Link>
              <div className="flex items-center justify-center">
            <button
              className="text-white py-2 px-12 md:px-16 lg:px-24 text-base font-semibold font-poppins max-w-md rounded bg-sky-blue-650"
              onClick={handleActionClick}
            >
              Action
            </button>
          </div>
            </div>
        </div>
      </div>

      
      {showPopup && (
        <ActionPopup onClose={handleClosePopup} onSave={handleSaveChanges} />
      )}
      
    </div>
  );
};

export default userDetails;
