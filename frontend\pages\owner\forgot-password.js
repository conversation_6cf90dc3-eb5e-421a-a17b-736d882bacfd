import React, { useEffect, useState } from "react";
import { IoIosArrowBack } from "react-icons/io";
import { useRouter } from "next/router";
import toast, { Toaster } from "react-hot-toast";
import { forgot<PERSON>ass<PERSON><PERSON> } from "@/services/webflowServices";

const Page = () => {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const router = useRouter();

  const [baseUrl, setBaseUrl] = useState("");

  useEffect(() => {
    if (typeof window !== "undefined") {
      const currentUrl = window.location.origin;
      setBaseUrl(currentUrl);
    }
  }, []);

  const handleForgotPassword = async (e) => {
    e.preventDefault();

    if (email === "") {
      setError("Please enter your email.");
      return;
    }

    setError("");
    setLoading(true);

    try {
      const response = await forgot<PERSON><PERSON><PERSON><PERSON>({
        email,
        reset_link: `${baseUrl}/owner/reset-password`,
      });

      if (response?.data?.status) {
        toast.success(
          response?.data?.message || "Password reset email sent successfully!"
        );
        // router.push("/reset-password");
      } else {
        toast.error(response?.data?.message || "Somthing Went Wrong");
      }
    } catch (error) {
      console.error("Error during forgot password request:", error);

      if (error?.response?.status === 404) {
        toast.error(error?.response?.data?.message || "Email not found.");
      } else {
        toast.error("An unexpected error occurred. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Toaster position='top-center' />
      <div className='min-h-screen flex items-center justify-center bg-[#F7F7F7] w-full pb-[10rem] pt-[3rem]'>
        <div className='bg-white rounded-2xl w-[90%] max-w-md p-5 font-manrope'>
          <form onSubmit={handleForgotPassword}>
            <div className='flex items-center mb-8 justify-between'>
              <button
                type='button'
                onClick={() => router.push("/owner/login")}
                className='cursor-pointer'
              >
                <IoIosArrowBack />
              </button>
              <h2 className=' text-xl font-bold text-center text-gray-800'>
                👋 Forgot Password?
              </h2>
              <IoIosArrowBack className='text-white opacity-0' />
            </div>
            <div className='mb-4'>
              <input
                type='email'
                id='email'
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className='w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500'
                placeholder='Email'
              />
            </div>
            <button
              type='submit'
              className={`w-full my-3  font-semibold  text-white py-4 rounded-4xl  transition duration-200 ${
                !email
                  ? "bg-gray-300 cursor-not-allowed"
                  : "bg-[#40E0D0] hover:bg-[#40E0D0]"
              }`}
              disabled={loading || !email}
            >
              {loading ? "Sending..." : "Get Email"}
            </button>
            {error && (
              <div className='mb-4 text-center text-red-600'>{error}</div>
            )}
            {/* <button
              type="button"
              onClick={() => router.push("/login")}
              className="flex items-center mt-4 cursor-pointer"
            >
              <ArrowBackIcon className="mr-2" />
              Go back
            </button> */}
          </form>
        </div>
      </div>
    </>
  );
};

export default Page;
