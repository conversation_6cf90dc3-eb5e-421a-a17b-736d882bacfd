"use client";
import {
  Bell<PERSON>ing,
  ChevronDown,
  Check,
  Globe,
  Menu,
  Search,
} from "lucide-react";
import Image from "next/image";
import React, { useState } from "react";
import { MdManageAccounts } from "react-icons/md";
import { <PERSON><PERSON><PERSON><PERSON>, FaUser } from "react-icons/fa6";
import SuperSidebar from "./SuperSidebar";
import { useRef, useEffect } from "react";
import "@fontsource/nunito-sans";



const SuperLayout = () => {
  
  const dropdownRef = useRef(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState("English"); // Default selected language
  const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false); // Profile dropdown state
  const [isSidebarOpen, setIsSidebarOpen] = useState(false); // State for sidebar visibility on mobile

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const toggleProfileDropdown = () => {
    setIsProfileDropdownOpen(!isProfileDropdownOpen);
  };

  const selectLanguage = (language) => {
    setSelectedLanguage(language);
    setIsDropdownOpen(false); // Close dropdown after selection
  };
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsProfileDropdownOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <>
      {/* {isSidebarOpen && (
        <RxCross2
          className="bg-transparent "
          onClick={() => setIsSidebarOpen(false)}
        />
      )} */}
      <SuperSidebar
        setIsSidebarOpen={setIsSidebarOpen}
        isSidebarOpen={isSidebarOpen}
      />
      {/* <Menu
        className='duration-200 ease-in-out cursor-pointer text-black-100 hover:text-black'
        size={20}
        onClick={() => setIsSidebarOpen(!isSidebarOpen)}
      /> */}

      <div className="flex items-center shadow justify-between w-full h-auto px-2 md:px-5 lg:px-5 py-3 bg-white dark:bg-[#0D0D0D] text-black-100 lg:pl-[255px] md:pl-[255px]">
        {/* Left Section */}
        <div className="flex items-center gap-x-3">
          {/* Menu Icon - Visible only on mobile */}
          <div className="lg:hidden md:hidden">
            <Menu
              className="duration-200 ease-in-out cursor-pointer text-black-100 hover:text-black dark:text-[#B6B6B6]"
              size={20}
              onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            />
          </div>
          {/* Search Input */}
          <div className="relative w-full sm:w-60 lg:w-80 pr-4">
            <input
              type="search"
              placeholder="Search for rooms and offers"
              className="pl-8 bg-gray-100 text-gray-200 rounded-full py-2 w-full outline-none border-2 border-gray-200 text-xs font-poppins dark:bg-[#0D0D0D] dark:border dark:text-[#616161]"
            />
            <Search
              className="absolute  text-gray-500 dark:text-[#616161] top-1/2 left-2.5 transform -translate-y-1/2"
              size={16}
            />
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center gap-x-3 md:gap-x-4 lg:gap-x-4">
          {/* Notification Icon */}
          <div className="cursor-pointer text-gray-540 dark:text-[#B6B6B6]">
            <BellRing size={20} className="font-light" />
          </div>

          {/* Language Dropdown */}
          <div className="relative flex items-center text-gray-540 dark:text-[#B6B6B6] gap-x-1">
            <div className="lg:flex md:flex items-center hidden gap-x-1">
              <Globe size={20} className="font-light" />
              <div
                className="cursor-pointer text-sm flex items-center gap-1"
                onClick={toggleDropdown}
              >
                <span>{selectedLanguage}</span>
                <ChevronDown size={16} />
              </div>
            </div>
            {/* Icon only for mobile */}
            <div className="lg:hidden md:hidden" onClick={toggleDropdown}>
              <Globe size={20} className="font-light" />
            </div>
            {isDropdownOpen && (
              <div
                className="fixed inset-0 bg-black/30 backdrop-blur-sm z-10"
                onClick={toggleDropdown} // Close dropdown when clicking on the overlay
              ></div>
            )}
            {isDropdownOpen && (
              <div className="absolute top-full right-0 mt-2 w-52 bg-white dark:bg-[#171616] rounded-xl shadow-lg z-10">
                <h1 className="px-4 py-3 font-nunito text-sm lg:text-base  font-normal">
                  Select Language
                </h1>
                <hr />
                {/* <ul>
                  {["English", "French", "Spanish"].map((language) => (
                    <li
                      key={language}
                      className="px-4 py-4 hover:bg-gray-100 cursor-pointer flex items-center justify-between"
                      onClick={() => selectLanguage(language)}
                    >
                      <div className="flex items-center gap-2">
                        <Image
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/${language}.png`}
                          width={44}
                          height={30}
                          alt={language}
                        />
                        {language}
                      </div>
                      {selectedLanguage === language && (
                        <Check size={20} className="text-black" />
                      )}
                    </li>
                  ))}
                </ul> */}
                <ul>
                  {["English", "French", "Spanish"].map((language) => (
                    <li
                      key={language}
                      className="px-4 py-4 hover:bg-gray-100 dark:hover:bg-[#474646d0] cursor-pointer flex items-center justify-between"
                      onClick={() => selectLanguage(language)}
                    >
                      <div className="flex items-center gap-4 text-sm">
                        {" "}
                        
                        <Image
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/${language}.png`}
                          width={44}
                          height={30}
                          alt={language}
                        />
                        {language}
                      </div>
                      {selectedLanguage === language && (
                        <Check size={20} className="text-black dark:text-[#B6B6B6]" />
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          {/* Profile Section */}
          <div className="relative flex items-center gap-x-2" ref={dropdownRef}>
            {/* Icon for mobile */}
            <div
              className="flex lg:hidden items-center justify-center text-neutral-850 dark:text-[#B6B6B6]"
              onClick={toggleProfileDropdown}
            >
              <FaUser className="text-lg" />
            </div>
            {/* Full Profile Info for larger screens */}
            <div className="hidden lg:flex items-center gap-x-3">
              <div className="flex items-center justify-center text-white rounded-full w-11 h-11 bg-yellow-550">
                <h1 className="text-base font-bold" onClick={toggleProfileDropdown}>M</h1>
              </div>
              <div>
                <h3 className="text-sm font-bold text-neutral-700 dark:text-[#B6B6B6]" onClick={toggleProfileDropdown}>Moni Roy</h3>
                <p className="text-xs font-semibold text-neutral-850 dark:text-[#B6B6B6]">Admin</p>
              </div>
            </div>
            {/* Dropdown Trigger */}
            <div
              className="hidden lg:flex items-center justify-center w-5 h-5 duration-200 ease-in-out border rounded-full border-neutral-850 text-neutral-850 dark:text-[#B6B6B6]"
              onClick={toggleProfileDropdown}
            >
              <ChevronDown size={16}  className={`${
                        isProfileDropdownOpen ? "rotate-180 mb-0.5" : ""
                      }`}/>
            </div>
            {isProfileDropdownOpen && (
              <div className="absolute top-full right-0 mt-2 w-56 bg-white dark:bg-[#171616] rounded-lg shadow-lg z-10">
                <ul>
                  <li className="lg:hidden items-center flex px-3 pt-2 gap-x-3">
                    <div className="flex items-center justify-center text-white rounded-full w-11 h-11 bg-yellow-550">
                      <h1 className="text-base font-bold">M</h1>
                    </div>
                    <div>
                      <h3 className="text-sm font-bold text-neutral-700 dark:text-[#B6B6B6]">
                        Moni Roy
                      </h3>
                      <p className="text-xs font-semibold text-neutral-850 dark:text-[#B6B6B6]">
                        Admin
                      </p>
                    </div>
                  </li>
                  <li className="px-4 py-4 flex items-center gap-2 hover:bg-gray-100 cursor-pointer text-base dark:text-[#B6B6B6] dark:hover:bg-[#474646d0]">
                    <MdManageAccounts size={22} className="text-sky-blue-650" />
                    Manage Profiles
                  </li>
                  <hr />
                  <li className="px-4 py-4 flex items-center gap-2 hover:bg-gray-100 cursor-pointer text-base dark:text-[#B6B6B6] dark:hover:bg-[#474646d0]">
                    <FaKey size={16} className="text-[#8080FF]" />
                    Change Password
                  </li>
                  <hr />
                  <li className="px-4 py-4 flex items-center gap-2 hover:bg-gray-100 cursor-pointer text-base dark:text-[#B6B6B6] dark:hover:bg-[#474646d0]">
                    <Image
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/LogOut.png`}
                      width={14}
                      height={12}
                      alt="LogOut"
                    />
                    Log out
                  </li>
                </ul>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default SuperLayout;
