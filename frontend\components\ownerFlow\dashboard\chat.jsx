/* eslint-disable react/no-unescaped-entities */
import React, { useState } from "react";
import { TabContext, TabList, TabPanel } from "@mui/lab";
import { Tab, Box } from "@mui/material";
// import Box from "@mui/material/Box";
import Image from "next/image";
// import Tab from "@mui/material/Tab";
// import TabContext from "@mui/lab/TabContext";
// import TabList from "@mui/lab/TabList";
// import TabPanel from "@mui/lab/TabPanel";
import { IoCallOutline } from "react-icons/io5";
import { CiCamera } from "react-icons/ci";

const Chat = () => {
  const [value, setValue] = useState("chat");

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <>
      <div className="sm:flex block">
        <div className="w-[380px] p-5 pr-0">
          <TabContext value={value}>
            <Box
            >
              {/* Scrollable wrapper for tabs */}
              <Box sx={{ overflowX: "auto", whiteSpace: "nowrap", width: "100%" }}>
                <TabList
                  onChange={handleChange}
                  aria-label="lab API tabs example"
                  indicatorColor="none" 
                  sx={{
                    display: "flex",
                    overflowX: "auto", 
                    flexWrap: "nowrap",
                  }}
                >
                  {[
                    { label: "Hostel chat", value: "chat" },
                    { label: "Mix Creator", value: "mixcreator" },
                    { label: "Mix Ride", value: "mixride" },
                    { label: "Event", value: "event" },
                  ].map((tab) => (
                    <Tab
                      key={tab.value}
                      label={tab.label}
                      value={tab.value}
                      className="rounded-lg text-sm font-medium capitalize first:ml-0 mx-1 p-2.5"
                      sx={{
                        border: "1px solid #D0D3D9",
                        color: "#000000",
                        minWidth: "auto",
                        "&.Mui-selected": {
                          background: "#000000",
                          color: "#ffffff",
                        },
                      }}
                    />
                  ))}
                </TabList>
              </Box>
            </Box>

            <TabPanel value="chat" className="px-0 pb-0 pt-0 mt-3 pr-3 overflow-y-auto min-h-[490px] max-h-[590px] modalscroll">
              {Array(5)
                .fill(0)
                .map((_, index) => (
                  <div
                    key={index}
                    className="flex items-center mb-2 cursor-pointer hover:bg-gray-100 p-2 rounded-md"
                  >
                    <div className="flex items-center flex-1 pr-3">
                      <div className="w-12 h-12 bg-gray-300 rounded-full mr-3">
                        <Image
                          alt=""
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/HostelImg.png`}
                          width={45}
                          height={45}
                          loading="lazy"
                          className="w-12 h-12 rounded-full"
                        />
                      </div>
                      <div className="flex-1">
                        <h2 className="text-base text-black font-normal font-roboto">Alex Lee</h2>
                        <p className="text-[#888888] text-sm font-normal">Hello</p>
                      </div>
                    </div>
                    {index === 0 && (
                      <span className="w-7 h-7 rounded-full bg-[#40E0D0] text-black flex items-center justify-center text-sm font-bold">
                        10
                      </span>
                    )}
                    {index === 3 && (
                      <span className="w-7 h-7 rounded-full bg-[#FF7F50] text-black flex items-center justify-center text-sm font-bold">
                        10
                      </span>
                    )}
                  </div>
                ))}
            </TabPanel>

            <TabPanel value="mixcreator" className="px-0 pb-0 pt-0 mt-3 pr-3 overflow-y-auto min-h-[490px] max-h-[590px] modalscroll">
              {Array(5)
                .fill(0)
                .map((_, index) => (
                  <div
                    key={index}
                    className="flex items-center mb-2 cursor-pointer hover:bg-gray-100 p-2 rounded-md"
                  >
                    <div className="flex items-center flex-1 pr-3">
                      <div className="w-12 h-12 bg-gray-300 rounded-full mr-3">
                        <Image
                          alt=""
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/HostelImg.png`}
                          width={45}
                          height={45}
                          loading="lazy"
                          className="w-12 h-12 rounded-full"
                        />
                      </div>
                      <div className="flex-1">
                        <h2 className="text-base text-black font-normal font-roboto">Alex Lee</h2>
                        <p className="text-[#888888] text-sm font-normal">Hello</p>
                      </div>
                    </div>
                    {index === 1 && (
                      <span className="w-7 h-7 rounded-full bg-[#40E0D0] text-black flex items-center justify-center text-sm font-bold">
                        10
                      </span>
                    )}
                    {index === 4 && (
                      <span className="w-7 h-7 rounded-full bg-[#FF7F50] text-black flex items-center justify-center text-sm font-bold">
                        10
                      </span>
                    )}
                  </div>
              ))}
            </TabPanel>
            <TabPanel value="mixride" className="px-0 pb-0 pt-0 mt-3 pr-3 overflow-y-auto min-h-[490px] max-h-[590px] modalscroll">
              {Array(5)
                .fill(0)
                .map((_, index) => (
                  <div
                    key={index}
                    className="flex items-center mb-2 cursor-pointer hover:bg-gray-100 p-2 rounded-md"
                  >
                    <div className="flex items-center flex-1 pr-3">
                      <div className="w-12 h-12 bg-gray-300 rounded-full mr-3">
                        <Image
                          alt=""
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/HostelImg.png`}
                          width={45}
                          height={45}
                          loading="lazy"
                          className="w-12 h-12 rounded-full"
                        />
                      </div>
                      <div className="flex-1">
                        <h2 className="text-base text-black font-normal font-roboto">Alex Lee</h2>
                        <p className="text-[#888888] text-sm font-normal">Hello</p>
                      </div>
                    </div>
                    {index === 0 && (
                      <span className="w-7 h-7 rounded-full bg-[#40E0D0] text-black flex items-center justify-center text-sm font-bold">
                        10
                      </span>
                    )}
                    {index === 3 && (
                      <span className="w-7 h-7 rounded-full bg-[#FF7F50] text-black flex items-center justify-center text-sm font-bold">
                        10
                      </span>
                    )}
                  </div>
              ))}
            </TabPanel>
            <TabPanel value="event" className="px-0 pb-0 pt-0 mt-3 pr-3 overflow-y-auto min-h-[490px] max-h-[590px] modalscroll">
              {Array(4)
                .fill(0)
                .map((_, index) => (
                  <div
                    key={index}
                    className="flex items-center mb-2 cursor-pointer hover:bg-gray-100 p-2 rounded-md"
                  >
                    <div className="flex items-center flex-1 pr-3">
                      <div className="w-12 h-12 bg-gray-300 rounded-full mr-3">
                        <Image
                          alt=""
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/HostelImg.png`}
                          width={45}
                          height={45}
                          loading="lazy"
                          className="w-12 h-12 rounded-full"
                        />
                      </div>
                      <div className="flex-1">
                        <h2 className="text-base text-black font-normal font-roboto">Alex Lee</h2>
                        <p className="text-[#888888] text-sm font-normal">Hello</p>
                      </div>
                    </div>
                    {index === 0 && (
                      <span className="w-7 h-7 rounded-full bg-[#40E0D0] text-black flex items-center justify-center text-sm font-bold">
                        10
                      </span>
                    )}
                    {index === 3 && (
                      <span className="w-7 h-7 rounded-full bg-[#FF7F50] text-black flex items-center justify-center text-sm font-bold">
                        10
                      </span>
                    )}
                  </div>
              ))}
            </TabPanel>
          </TabContext>
        </div>
        <div className="flex-1 border-l border-[#D6D6D6] relative">
          <div className="p-5 flex items-center justify-between bg-[#EEEEEE] border-b border-[#D6D6D6]">
            <div className="w-full gap-3 flex items-center">
              <div className="w-[40px] h-[40px] rounded-full overflow-hidden">
                <Image
                  alt=""
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/HostelImg.png`}
                  width={40}
                  height={40}
                  loading="lazy"
                />
              </div>
              <h6 className="text-[13px] font-normal text-black mb-0">
                Alex Lee
                <span className="block text-[#88888899]">
                  GO Stops BIR
                </span>
              </h6>
            </div>
            <button
              type="button"
              className="border border-[#D6D6D6] w-[40px] h-[40px] flex items-center justify-center text-black hover:bg-primary-blue hover:text-white rounded-full text-2xl"
            >
              <IoCallOutline />
            </button>
          </div>
          <div>
            <p className="text-black text-center text-sm mb-6">
              Chats will stay for 24 hours after being viewed
            </p>
            <div className="p-5 overflow-y-auto min-h-[490px] max-h-[590px] pb-[90px] modalscroll">
              <div className="grid grid-cols-12 gap-y-4">
                <div className="col-start-1 sm:col-end-8 col-end-10 rounded-lg">
                  <div className="inline-flex flex-row items-center bg-[#D9F9F6] py-2 pl-2 pr-3 border-l border-[#40E0D0] min-w-60 rounded-tr-3xl">
                    <Image
                      alt=""
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/HostelImg.png`}
                      width={30}
                      height={30}
                      loading="lazy"
                      className="rounded-full"
                    />
                    <div className="relative ml-2 text-sm">
                      <div>Hey How are you today?</div>
                    </div>
                  </div>
                  <span className="text-[#888888] text-xs block mt-1">
                    4:23 PM
                  </span>
                </div>
                <div className="col-start-1 sm:col-end-8 col-end-10 rounded-lg">
                  <div className="inline-flex flex-row items-center bg-[#D9F9F6] py-2 pl-2 pr-3 border-l border-[#40E0D0] min-w-60 rounded-tr-3xl">
                    <Image
                      alt=""
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/HostelImg.png`}
                      width={30}
                      height={30}
                      loading="lazy"
                      className="rounded-full"
                    />
                    <div className="relative ml-2 text-sm ">
                      <div>
                        Lorem ipsum dolor sit amet, consectetur adipisicing
                        elit. Vel ipsa commodi illum saepe numquam maxime
                        asperiores voluptate sit, minima perspiciatis.
                      </div>
                    </div>
                  </div>
                </div>
                <div className="right-side sm:col-start-6 col-start-3 col-end-13 rounded-lg flex justify-end group">
                  <div className="inline-flex items-center justify-between flex-row-reverse bg-[#FFE5DC] py-2 pr-2 pl-3 border-r border-[#FF7F50] min-w-60 rounded-tl-3xl">
                    <Image
                      alt=""
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/HostelImg.png`}
                      width={30}
                      height={30}
                      loading="lazy"
                      className="rounded-full"
                    />
                    <div className="relative mr-2 text-sm">
                      <div>I'm ok what about you?</div>
                    </div>
                  </div>
                </div>

                <div className="right-side sm:col-start-6 col-start-3 col-end-13 rounded-lg flex justify-end group">
                  <div className="inline-flex items-center justify-between flex-row-reverse bg-[#FFE5DC] py-2 pr-2 pl-3 border-r border-[#FF7F50] min-w-60 group-first:rounded-tl-3xl group-last:rounded-bl-3xl">
                    <Image
                      alt=""
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/HostelImg.png`}
                      width={30}
                      height={30}
                      loading="lazy"
                      className="rounded-full"
                    />
                    <div className="relative mr-2 text-sm">
                      <div>Lorem ipsum dolor sit, amet consectetur adipisicing. ?</div>
                    </div>
                  </div>
                </div>

              </div>
              <div className="inline-flex flex-row items-center bg-white w-full px-4 absolute left-0 bottom-0 py-4">
                <div className="relative w-full border rounded-4xl  pl-4  flex items-center p-1">
                  <button className="w-8 py-1 text-2xl pr-2 border-r border-primary-blue text-primary-blue">
                    <CiCamera />
                  </button>
                  <input
                    type="text"
                    className="flex w-full flex-1 p-3 focus:outline-none outline-none"
                    placeholder="Messages"
                  />
                  <button className="bg-[#000000] hover:bg-primary-blue text-[15px] rounded-4xl text-white px-2 w-20 py-3 font-bold">
                    Send
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Chat;
