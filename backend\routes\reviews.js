import express from 'express';
import {
  addReviewController,
  updateReviewController,
  getReviewByIdController,
  listAllReviewsByHostelController,
  deleteReviewController,
  listAllReviewsController,
  getRatingDetailsByPropertyIdController
} from '../controller/reviews.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();

router.post('/', checkAuth('add_review'), addReviewController);
router.put('/:id', checkAuth('update_review'), updateReviewController);
router.get('/all/:propertyId', listAllReviewsByHostelController);
router.get('/:id', checkAuth('get_review'), getReviewByIdController);
router.get('/', checkAuth('get_all_reviews'), listAllReviewsController);
router.get('/rating-details/:propertyId', getRatingDetailsByPropertyIdController);
router.delete('/:id', checkAuth('delete_review'), deleteReview<PERSON>ontroller);

/**
 * @swagger
 * tags:
 *   name: Reviews
 *   description: API for managing reviews
*/

/**
 * @swagger
 * components:
 *   schemas:
 *     Review:
 *       type: object
 *       required:
 *         - user
 *         - property
 *         - rating
 *       properties:
 *         user:
 *           type: string
 *           description: User ID
 *         property:
 *           type: string
 *           description: Property ID
 *         rating:
 *           type: number
 *           minimum: 1
 *           maximum: 5
 *           description: Rating (1-5)
 *         comment:
 *           type: string
 *           description: Review comment
 *         images:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               url:
 *                 type: string
 *                 description: Image URL
 *               caption:
 *                 type: string
 *                 description: Image caption
 *         likes:
 *           type: number
 *           description: Number of likes
 *         dislikes:
 *           type: number
 *           description: Number of dislikes
 *         isActive:
 *           type: boolean
 *           description: Is the booking active
 *         isDeleted:
 *           type: boolean
 *           description: Is the booking deleted
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the booking was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the booking was last updated
*/

/**
 * @swagger
 * /reviews:
 *   post:
 *     summary: Add a new review
 *     tags: [Reviews]
 *     x-isPublic: true
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Review'
 *     responses:
 *       201:
 *         description: Review added successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Review'
 *       500:
 *         description: Internal server error
*/

/**
 * @swagger
 * /reviews/{id}:
 *   put:
 *     summary: Update a review by ID
 *     tags: [Reviews]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Review ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Review'
 *     responses:
 *       200:
 *         description: Review updated successfully
 *       500:
 *         description: Internal server error
*/

/**
 * @swagger
 * /reviews/{id}:
 *   get:
 *     summary: Get a review by ID
 *     tags: [Reviews]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Review ID
 *     responses:
 *       200:
 *         description: Review retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Review'
 *       500:
 *         description: Internal server error
*/

/**
 * @swagger
 * /reviews/all/{propertyId}:
 *   get:
 *     summary: List all reviews for a property
 *     tags: [Reviews]
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         schema:
 *           type: string
 *         required: true
 *         description: Property ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of reviews per page
 *       - name: filter
 *         in: query
 *         required: false
 *         schema:
 *           type: object
 *           style: deepObject
 *           explode: true
 *     responses:
 *       200:
 *         description: Reviews retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 reviews:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Review'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     totalReviews:
 *                       type: integer
 *                 ratings:
 *                   type: object
 *                   additionalProperties:
 *                     type: integer
 *       500:
 *         description: Internal server error
*/

/**
 * @swagger
 * /reviews:
 *   get:
 *     summary: List all reviews
 *     tags: [Reviews]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of reviews per page
 *       - name: filter
 *         in: query
 *         required: false
 *         schema:
 *           type: object
 *           style: deepObject
 *           explode: true
 *     responses:
 *       200:
 *         description: Reviews retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 reviews:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Review'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     totalReviews:
 *                       type: integer
 *                 ratings:
 *                   type: object
 *                   additionalProperties:
 *                     type: integer
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /reviews/{id}:
 *   delete:
 *     summary: Delete a review by ID
 *     tags: [Reviews]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Review ID
 *     responses:
 *       200:
 *         description: Review deleted successfully
 *       500:
 *         description: Internal server error
*/

/**
 * @swagger
 * /reviews/rating-details/{propertyId}:
 *   get:
 *     summary: Get rating details by property ID
 *     description: Retrieve total number of reviews, average star rating, and group by star rating for a given property ID.
 *     tags: [Reviews]
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the property
 *     responses:
 *       200:
 *         description: Rating Details Retrieved Successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 overallRating:
 *                   type: string
 *                   description: Overall average star rating, rounded to two decimal places
 *                 totalReviews:
 *                   type: number
 *                   description: Total number of reviews for the property
 *                 reviews:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       star:
 *                         type: number
 *                         description: Star rating (e.g., 1, 2, 3, 4, 5)
 *                       reviews:
 *                         type: number
 *                         description: Number of reviews with this star rating
 *       400:
 *         description: Invalid propertyId
 *       500:
 *         description: Internal server error
 */

export default router;