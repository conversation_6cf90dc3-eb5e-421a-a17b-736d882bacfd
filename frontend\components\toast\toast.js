 
// toast.js
import React from 'react';
import { createRoot } from 'react-dom/client';
import { <PERSON> , CheckChe<PERSON> } from 'lucide-react';
import { IoAlertOutline , IoInformationCircleOutline } from "react-icons/io5";
import { FaCircle } from "react-icons/fa";
import { GoAlert } from "react-icons/go";


// Toast component remains the same
const Toast = ({ message, type, onClose, subText, actionText }) => {
  const [isVisible, setIsVisible] = React.useState(true);

  React.useEffect(() => {
    const timer = setTimeout(() => {
    setIsVisible(false);
    setTimeout(onClose, 300);
    }, 5000);

    return () => clearTimeout(timer);


  }, [onClose]);

  const getIcon = () => {
    switch (type) {
      case 'error':
        // return <svg
        //   xmlns="http://www.w3.org/2000/svg"
        //   width={24}
        //   height={24}
        //   viewBox="0 0 24 24"
        //   fill="currentColor"
        //   className="icon icon-tabler icons-tabler-filled icon-tabler-info-hexagon min-w-[30px] min-h-[30px] w-6 h-6 text-red-500"
        // >
        //   <path stroke="none" d="M0 0h24v24H0z" fill="none" />
        //   <path d="M10.425 1.414a3.33 3.33 0 0 1 3.026 -.097l.19 .097l6.775 3.995l.096 .063l.092 .077l.107 .075a3.224 3.224 0 0 1 1.266 2.188l.018 .202l.005 .204v7.284c0 1.106 -.57 2.129 -1.454 2.693l-.17 .1l-6.803 4.302c-.918 .504 -2.019 .535 -3.004 .068l-.196 -.1l-6.695 -4.237a3.225 3.225 0 0 1 -1.671 -2.619l-.007 -.207v-7.285c0 -1.106 .57 -2.128 1.476 -2.705l6.95 -4.098zm1.575 9.586h-1l-.117 .007a1 1 0 0 0 0 1.986l.117 .007v3l.007 .117a1 1 0 0 0 .876 .876l.117 .007h1l.117 -.007a1 1 0 0 0 .876 -.876l.007 -.117l-.007 -.117a1 1 0 0 0 -.764 -.857l-.112 -.02l-.117 -.006v-3l-.007 -.117a1 1 0 0 0 -.876 -.876l-.117 -.007zm.01 -3l-.127 .007a1 1 0 0 0 0 1.986l.117 .007l.127 -.007a1 1 0 0 0 0 -1.986l-.117 -.007z" />
        // </svg>

        return <IoAlertOutline className='font-extrabold' color="#b52333" size={22} />;
      case 'success':
        // return <svg
        //   version="1.1"
        //   id="Capa_1"
        //   xmlns="http://www.w3.org/2000/svg"
        //   xmlnsXlink="http://www.w3.org/1999/xlink"
        //   viewBox="0 0 50 50"
        //   xmlSpace="preserve"
        //   className='w-6 h-6 text-white min-w-[30px] min-h-[30px]'
        //   fill="#000000"
        // >
        //   <g id="SVGRepo_bgCarrier" strokeWidth={0} />
        //   <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round" />
        //   <g id="SVGRepo_iconCarrier">
        //     {" "}
        //     <circle style={{ fill: "#25AE88" }} cx={25} cy={25} r={25} />{" "}
        //     <polyline
        //       style={{
        //         fill: "none",
        //         stroke: "#FFFFFF",
        //         strokeWidth: 2,
        //         strokeLinecap: "round",
        //         strokeLinejoin: "round",
        //         strokeMiterlimit: 10
        //       }}
        //       points=" 38,15 22,33 12,25 "
        //     />{" "}
        //   </g>
        // </svg>;
        return <CheckCheck className='font-extrabold' color="#489c79" size={22} />;

      case 'info':
      //   return <svg
      //   xmlns="http://www.w3.org/2000/svg"
      //   width={24}
      //   height={24}
      //   viewBox="0 0 24 24"
      //   fill="currentColor"
      //   className="icon icon-tabler icons-tabler-filled min-w-[30px] min-h-[30px] icon-tabler-alert-square-rounded w-6 h-6 text-blue-500"
      // >
      //   <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      //   <path d="M12 2l.642 .005l.616 .017l.299 .013l.579 .034l.553 .046c4.687 .455 6.65 2.333 7.166 6.906l.03 .29l.046 .553l.041 .727l.006 .15l.017 .617l.005 .642l-.005 .642l-.017 .616l-.013 .299l-.034 .579l-.046 .553c-.455 4.687 -2.333 6.65 -6.906 7.166l-.29 .03l-.553 .046l-.727 .041l-.15 .006l-.617 .017l-.642 .005l-.642 -.005l-.616 -.017l-.299 -.013l-.579 -.034l-.553 -.046c-4.687 -.455 -6.65 -2.333 -7.166 -6.906l-.03 -.29l-.046 -.553l-.041 -.727l-.006 -.15l-.017 -.617l-.004 -.318v-.648l.004 -.318l.017 -.616l.013 -.299l.034 -.579l.046 -.553c.455 -4.687 2.333 -6.65 6.906 -7.166l.29 -.03l.553 -.046l.727 -.041l.15 -.006l.617 -.017c.21 -.003 .424 -.005 .642 -.005zm.01 13l-.127 .007a1 1 0 0 0 0 1.986l.117 .007l.127 -.007a1 1 0 0 0 0 -1.986l-.117 -.007zm-.01 -8a1 1 0 0 0 -.993 .883l-.007 .117v4l.007 .117a1 1 0 0 0 1.986 0l.007 -.117v-4l-.007 -.117a1 1 0 0 0 -.993 -.883z" />
      // </svg>;
      return <IoInformationCircleOutline className='font-extrabold' color="#3187f0" size={22} />;
      case 'warning':
        // return <svg
        //   xmlns="http://www.w3.org/2000/svg"
        //   width={24}
        //   height={24}
        //   viewBox="0 0 24 24"
        //   fill="currentColor"
        //   className="icon icon-tabler icons-tabler-filled min-w-[30px] min-h-[30px] icon-tabler-alert-triangle w-6 h-6 text-yellow-500"
        // >
        //   <path stroke="none" d="M0 0h24v24H0z" fill="none" />
        //   <path d="M12 1.67c.955 0 1.845 .467 2.39 1.247l.105 .16l8.114 13.548a2.914 2.914 0 0 1 -2.307 4.363l-.195 .008h-16.225a2.914 2.914 0 0 1 -2.582 -4.2l.099 -.185l8.11 -13.538a2.914 2.914 0 0 1 2.491 -1.403zm.01 13.33l-.127 .007a1 1 0 0 0 0 1.986l.117 .007l.127 -.007a1 1 0 0 0 0 -1.986l-.117 -.007zm-.01 -7a1 1 0 0 0 -.993 .883l-.007 .117v4l.007 .117a1 1 0 0 0 1.986 0l.007 -.117v-4l-.007 -.117a1 1 0 0 0 -.993 -.883z" />
        // </svg>;

        return <GoAlert className='font-extrabold' color="#fac42b" size={22} />;
      default:
        return null;
    }
  };

  const getSubTextColor = () => {
    switch (type) {
      case 'error':
        return 'red-500';
      case 'success':
        return 'green-600';
      case 'info':
        return 'blue-500';
      case 'warning':
        return 'yellow-600';
      default:
        return 'gray-500';
    }
  };

  const getBg = () => {
    switch (type) {
      case 'error':
        return 'bg-[#ffdde2]';
      case 'success':
        return 'bg-[#c4efd5]';
      case 'info':
        return 'bg-[#e7eefa]';
      case 'warning':
        return 'bg-[#fef7ea]';
      default:
        return 'bg-gray-100';
    }
  };

  const getCircle = () => {
    switch (type) {
      case 'error':
        return '#eca6ad';
      case 'success':
        return '#99deb9';
      case 'info':
        return '#88beff';
      case 'warning':
        return '#ffdf7fc7';
      default:
        return 'bg-gray-100';
    }
  };

  return (
    <div
      className={`transform transition-all duration-300 ease-in-out max-w-full w-full animated ${
        isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
      }`}
    >
      <div className={`${getBg()} relative rounded-xl overflow-hidden shadow-lg shadow-gray-500 sm:p-4 p-2 mb-4 flex items-start justify-between w-full`}>
        {/* <div className={`absolute top-0 left-0 h-full w-[5px] bg-${getSubTextColor()} overflow-hidden rounded-l-lg`}></div> */}
        <div className='relative flex items-center gap-3'>
          <FaCircle className='absolute -top-8 -left-8 z-10 w-[50px] h-[50px] sm:w-[60px] sm:h-[60px]' color={`${getCircle()}`} />
          <FaCircle className='absolute -bottom-9 left-0 z-10' color={`${getCircle()}`} size={40} />
          <FaCircle className='absolute top-4 left-7 z-10' color={`${getCircle()}`} size={20} />
          <div className="mt-[2px] rounded-full sm:min-w-10 sm:min-h-10 min-w-8 min-h-8 bg-white flex items-center z-20 justify-center">
            {getIcon()}
          </div>
          <div className="flex-1">
            {/* text-${getSubTextColor()} */}
            <p className={`text-black sm:text-[15px] text-[14px] font-bold mt-0`}>{message}</p>
            {subText && (
              <p className="text-black/60 font-medium sm:text-[13px] text-[12px]">
                {subText}
              </p>
            )}
            {actionText && (
              <button className="text-blue-500 text-[16px] mt-2 hover:text-blue-600">
                {actionText}
              </button>
            )}
          </div>
        </div>
        <button
          onClick={() => {
            setIsVisible(false);
            setTimeout(onClose, 300);
          }}
          className="text-black hover:text-gray-600"
        >
          <X className="w-4 h-4" />
        </button>
        <div className="absolute bottom-0 left-0 w-[97%] z-20 ml-1 h-[4px] bg-transparent overflow-hidden rounded-b-xl">
          <div className={`h-full bg-${getSubTextColor()} animate-toastProgress rounded-e-0`}></div>
        </div>
      </div>
    </div>
  );
};

// Create a toast container manager
const createToastContainer = () => {
  const container = document.createElement('div');
  container.className = 'fixed top-[50px] right-[25px] z-[9999] space-y-4 sm:w-[300px] w-max max-w-full';
  document.body.appendChild(container);
  return createRoot(container);
};

let toastRoot = null;
let toasts = [];

const renderToasts = () => {
  if (!toastRoot) {
    toastRoot = createToastContainer();
  }

  toastRoot.render(
    <div>
      {toasts.map((toast) => (
        <Toast
          key={toast.id}
          {...toast}
          onClose={() => {
            toasts = toasts.filter(t => t.id !== toast.id);
            renderToasts();
          }}
        />
      ))}
    </div>
  );
};

// Toast API
const toast = {
  show: (message, type, options = {}) => {
    const id = Math.random().toString(36).substr(2, 9);
    toasts = [...toasts, { id, message, type, ...options }];
    renderToasts();
    return id;
  },

  success: (message, options = {}) => toast.show(message, 'success', options),
  error: (message, options = {}) => toast.show(message, 'error', options),
  info: (message, options = {}) => toast.show(message, 'info', options),
  warning: (message, options = {}) => toast.show(message, 'warning', options)
};

export default toast;