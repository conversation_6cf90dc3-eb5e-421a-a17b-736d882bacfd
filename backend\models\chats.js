import mongoose from 'mongoose';

const messageSchema = mongoose.Schema(
    {
        sender: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'users',
            required: true,
        },
        receiver: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'users',
            required: true,
        },
        message: {
            type: String,
            required: true,
        },
        type: {
            type: String
        },
        isRead: {
            type: Boolean,
            default: false,
        },
        attachments: [],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        roomId: { type: mongoose.Schema.Types.ObjectId, ref: 'chatRooms' },
    },
    {
        timestamps: true,
    }
);

const Message = mongoose.model('chats', messageSchema);
export default Message;
