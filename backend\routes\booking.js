import express from 'express';
import { addBooking<PERSON>ontroller, updateBookingController, 
    getBookingByIdController, listAllBookingsByUserController,
    deleteBookingController, listAllBookingsController,
    checkAvailabilityController, checkBookingByDateRangeController ,checkout,cancelBookingController} from '../controller/booking.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();

router.post('/', checkAuth('add_booking'), addBookingController);
router.post('/check-availability',checkAuth('check_availability') ,checkAvailabilityController);
router.put('/cancel', checkAuth('cancel_booking'), cancelBookingController);
router.post('/check-booking-range', checkBookingByDateRangeController);
router.put('/:id', checkAuth('update_booking'), updateBookingController);
router.get('/all', checkAuth('list_bookings'), listAllBookingsController);
router.post('/checkout', checkout);
router.get('/:id', getBookingByIdController);
router.delete('/:id', checkAuth('delete_booking'), deleteBookingController);



export default router;

/**
 * @swagger
 * tags:
 *   name: Booking
 *   description: Booking Management
*/

/**
 * @swagger
 * /booking:
 *   post:
 *     tags: [Booking]
 *     x-isPublic: true
 *     summary: Add a new booking
 *     description: Creates a new booking
 *     security:
 *       - bearerAuth: []
 *     consumes:
 *       - application/json
 *     produces:
 *       - application/json
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/definitions/Booking'
 *     responses:
 *       201:
 *         description: Booking Added Successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/BookingResponse'
 *       500:
 *         description: Internal Server Error
 */

/**
 * @swagger
 * /booking/check-availability:
 *   post:
 *     tags: [Booking]
 *     summary: Check room availability
 *     description: Checks if a particular room in a property is available for specified check-in and check-out dates
 *     security:
 *       - bearerAuth: []
 *     consumes:
 *       - application/json
 *     produces:
 *       - application/json
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               propertyId:
 *                 type: string
 *                 description: ID of the property
 *                 example: "6123456789abcdef01234567"
 *               roomId:
 *                 type: string
 *                 description: ID of the room
 *                 example: "6123456789abcdef01234567"
 *               checkIn:
 *                 type: string
 *                 format: date
 *                 description: Check-in date
 *                 example: "2024-08-15"
 *               checkOut:
 *                 type: string
 *                 format: date
 *                 description: Check-out date
 *                 example: "2024-08-20"
 *     responses:
 *       200:
 *         description: Booking Retrieved Successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/BookingResponse'
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Property, room, checkIn, checkOut are mandatory!"
 *       500:
 *         description: Internal Server Error
 */
/**
 * @swagger
 * /booking/check-booking-range:
 *   post:
 *     tags: [Booking]
 *     x-isPublic: true
 *     summary: Check bookings for multiple rooms within date ranges
 *     description: Returns a booking status for multiple rooms with their respective date ranges.
 *     security:
 *       - bearerAuth: []
 *     consumes:
 *       - application/json
 *     produces:
 *       - application/json
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               rooms:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     roomId:
 *                       type: string
 *                       example: "64a12345bcd6789ef0123456"
 *                     startDate:
 *                       type: string
 *                       format: date
 *                       example: "2024-08-12"
 *                     endDate:
 *                       type: string
 *                       format: date
 *                       example: "2024-08-20"
 *     responses:
 *       200:
 *         description: Booking status for each room and their respective date ranges
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       roomId:
 *                         type: string
 *                         example: "64a12345bcd6789ef0123456"
 *                       status:
 *                         type: string
 *                         example: "success"
 *                       bookingStatus:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             date:
 *                               type: string
 *                               format: date
 *                               example: "2024-08-12"
 *                             isBooked:
 *                               type: boolean
 *                               example: true
 *                       message:
 *                         type: string
 *                         example: "Failed to check booking status."
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Rooms array is required."
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "An error occurred"
 */
/**
 * @swagger
 * /booking/{id}:
 *   put:
 *     tags: [Booking]
 *     summary: Update booking by ID
 *     description: Updates a booking
 *     security:
 *       - bearerAuth: []
 *     consumes:
 *       - application/json
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/definitions/Booking'
 *     responses:
 *       200:
 *         description: Booking updated successfully
 *         content:
 *           application/json:
 *             example:
 *               message: 'Booking updated successfully'
 *       500:
 *         description: Internal Server Error
 */

/**
 * @swagger
 * /booking/{id}:
 *   get:
 *     tags: [Booking]
 *     summary: Get booking by ID
 *     description: Returns a single booking
 *     security:
 *       - bearerAuth: []
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         type: string
 *     responses:
 *       200:
 *         description: Booking Retrieved Successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/BookingResponse'
 *       500:
 *         description: Internal Server Error
 */
/**
 * @swagger
 * /booking/all:
 *   get:
 *     tags: [Booking]
 *     summary: List all bookings by user
 *     description: Returns a list of bookings for the authenticated user, with optional filters for category, property, and date range.
 *     security:
 *       - bearerAuth: []
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: page
 *         in: query
 *         required: false
 *         schema:
 *           type: integer
 *           default: 1
 *       - name: limit
 *         in: query
 *         required: false
 *         schema:
 *           type: integer
 *           default: 10
 *       - name: category
 *         in: query
 *         required: true
 *         schema:
 *           type: string
 *           enum: [reservations, arrivals, departures, inhouse]
 *         description: The category of bookings to filter (e.g., reservations, arrivals, departures, inhouse)
 *       - name: property
 *         in: query
 *         required: false
 *         schema:
 *           type: string
 *         description: The property ID to filter bookings by.
 *       - name: startDate
 *         in: query
 *         required: false
 *         schema:
 *           type: string
 *           format: date
 *         description: The start date to filter bookings from (YYYY-MM-DD).
 *       - name: endDate
 *         in: query
 *         required: false
 *         schema:
 *           type: string
 *           format: date
 *         description: The end date to filter bookings up to (YYYY-MM-DD).
 *     responses:
 *       200:
 *         description: Bookings Retrieved Successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 bookings:
 *                   type: array
 *                   items:
 *                     $ref: '#/definitions/BookingResponse'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     totalBookings:
 *                       type: integer
 *       400:
 *         description: Bad Request (Invalid parameters)
 *       500:
 *         description: Internal Server Error
 */

/**
 * @swagger
 * /booking/{id}:
 *   delete:
 *     tags: [Booking]
 *     summary: Delete booking by ID
 *     description: Soft deletes a booking
 *     security:
 *       - bearerAuth: []
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         type: string
 *     responses:
 *       200:
 *         description: Booking Deleted Successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       500:
 *         description: Internal Server Error
 */

/**
 * @swagger
 * definitions:
 *   Booking:
 *     type: object
 *     required:
 *       - room
 *       - checkInDate
 *       - checkOutDate
 *       - guests
 *       - tax
 *       - totalAmount
 *     properties:
 *       user:
 *         type: string
 *         description: User ID reference
 *       guestDetails:
 *         type: object
 *         properties:
 *           name:
 *             type: string
 *             description: Guest name
 *           email:
 *             type: string
 *             description: Guest email
 *           phone:
 *             type: string
 *             description: Guest phone number
 *       referenceNumber:
 *         type: string
 *         description: Booking reference number
 *       property:
 *         type: string
 *         description: Property ID reference
 *       room:
 *         type: string
 *         description: Room ID reference
 *       roomNumber:
 *         type: string
 *         description: Room number
 *       beds:
 *         type: number
 *         description: Number of beds
 *       checkInDate:
 *         type: string
 *         format: date-time
 *         description: Check-in date
 *       checkOutDate:
 *         type: string
 *         format: date-time
 *         description: Check-out date
 *       guests:
 *         type: object
 *         properties:
 *           adults:
 *             type: number
 *             description: Number of adult guests
 *           children:
 *             type: number
 *             description: Number of children guests
 *       rate:
 *         type: number
 *         description: Rate per night
 *       currency:
 *         type: string
 *         description: Currency code
 *       tax:
 *         type: number
 *         description: Tax amount
 *       totalAmount:
 *         type: number
 *         description: Total amount including tax
 *       isCancel:
 *         type: boolean
 *         description: Is the booking cancelled
 *       cancelledDate:
 *         type: string
 *         format: date-time
 *         description: Date when the booking was cancelled
 *       cancelledBy:
 *         type: string
 *         description: User ID of the person who cancelled the booking
 *       status:
 *         type: string
 *         enum: [pending, confirmed, cancelled]
 *         description: Booking status
 *       paymentStatus:
 *         type: string
 *         enum: [unpaid, paid]
 *         description: Payment status
 *       isActive:
 *         type: boolean
 *         description: Is the booking active
 *       isDeleted:
 *         type: boolean
 *         description: Is the booking deleted
 *       createdAt:
 *         type: string
 *         format: date-time
 *         description: Timestamp when the booking was created
 *       updatedAt:
 *         type: string
 *         format: date-time
 *         description: Timestamp when the booking was last updated
 */

/**
 * @swagger
 * definitions:
 *   BookingResponse:
 *     type: object
 *     required:
 *       - room
 *       - checkInDate
 *       - checkOutDate
 *       - guests
 *       - tax
 *       - totalAmount
 *     properties:
 *       user:
 *         type: string
 *         description: User ID reference
 *       guestDetails:
 *         type: object
 *         properties:
 *           name:
 *             type: string
 *             description: Guest name
 *           email:
 *             type: string
 *             description: Guest email
 *           phone:
 *             type: string
 *             description: Guest phone number
 *       referenceNumber:
 *         type: string
 *         description: Booking reference number
 *       property:
 *         type: string
 *         description: Property ID reference
 *       room:
 *         type: string
 *         description: Room ID reference
 *       roomNumber:
 *         type: string
 *         description: Room number
 *       beds:
 *         type: number
 *         description: Number of beds
 *       checkInDate:
 *         type: string
 *         format: date-time
 *         description: Check-in date
 *       checkOutDate:
 *         type: string
 *         format: date-time
 *         description: Check-out date
 *       guests:
 *         type: object
 *         properties:
 *           adults:
 *             type: number
 *             description: Number of adult guests
 *           children:
 *             type: number
 *             description: Number of children guests
 *       rate:
 *         type: number
 *         description: Rate per night
 *       currency:
 *         type: string
 *         description: Currency code
 *       tax:
 *         type: number
 *         description: Tax amount
 *       totalAmount:
 *         type: number
 *         description: Total amount including tax
 *       isCancel:
 *         type: boolean
 *         description: Is the booking cancelled
 *       cancelledDate:
 *         type: string
 *         format: date-time
 *         description: Date when the booking was cancelled
 *       cancelledBy:
 *         type: string
 *         description: User ID of the person who cancelled the booking
 *       status:
 *         type: string
 *         enum: [pending, confirmed, cancelled]
 *         description: Booking status
 *       paymentStatus:
 *         type: string
 *         enum: [unpaid, paid]
 *         description: Payment status
 *       isActive:
 *         type: boolean
 *         description: Is the booking active
 *       isDeleted:
 *         type: boolean
 *         description: Is the booking deleted
 *       createdAt:
 *         type: string
 *         format: date-time
 *         description: Timestamp when the booking was created
 *       updatedAt:
 *         type: string
 *         format: date-time
 *         description: Timestamp when the booking was last updated
 */
/**
 * @swagger
 * /booking/checkout:
 *   post:
 *     tags: [Booking]
 *     summary: Get checkout details for multiple rooms
 *     description: Returns room details required for the checkout process, including the room name, currency, average price, total payable amount, and duration of stay for up to 3 rooms.
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               checkIn:
 *                 type: string
 *                 format: date
 *                 description: Check-in date
 *                 example: "2024-09-20"
 *               checkOut:
 *                 type: string
 *                 format: date
 *                 description: Check-out date
 *                 example: "2024-09-23"
 *               bedType:
 *                 type: string
 *                 description: Type of bed (either 'lower' or 'upper')
 *                 example: "lower"
 *               currency:
 *                 type: string
 *                 description: Target currency for price conversion
 *                 example: "INR"
 *               roomSelections:
 *                 type: array
 *                 description: An array of room selections, each containing roomId and beds
 *                 items:
 *                   type: object
 *                   properties:
 *                     roomId:
 *                       type: string
 *                       description: ID of the room
 *                       example: "66da9ec31cc5eacd2fb64de2"
 *                     beds:
 *                       type: integer
 *                       description: Number of beds selected in the room
 *                       example: 2
 *     responses:
 *       201:
 *         description: Checkout details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 propertyName:
 *                   type: string
 *                   description: Name of the property
 *                   example: "Sample Hostel"
 *                 propertyFirstImage:
 *                   type: string
 *                   description: URL of the first image of the property
 *                   example: "https://s3.amazonaws.com/sample-image.jpg"
 *                 checkIn:
 *                   type: string
 *                   format: date
 *                   description: Check-in date
 *                   example: "2024-09-20"
 *                 checkOut:
 *                   type: string
 *                   format: date
 *                   description: Check-out date
 *                   example: "2024-09-23"
 *                 noOfDays:
 *                   type: integer
 *                   description: Number of days between check-in and check-out
 *                   example: 3
 *                 noOfGuests:
 *                   type: integer
 *                   description: Number of guests
 *                   example: 4
 *                 roomsData:
 *                   type: array
 *                   description: Array of room details
 *                   items:
 *                     type: object
 *                     properties:
 *                       roomName:
 *                         type: string
 *                         description: Name of the room
 *                         example: "Deluxe Room"
 *                       perDayRoomPrice:
 *                         type: number
 *                         description: Per-day room price in the target currency
 *                         example: 120.00
 *                       roomTotalPrice:
 *                         type: number
 *                         description: Total price for the room stay in the target currency
 *                         example: 360.00
 *                 payableOnArrival:
 *                   type: number
 *                   description: Payable amount on arrival (85% of the total)
 *                   example: 561.00
 *                 payableNow:
 *                   type: number
 *                   description: Payable amount now (15% of the total)
 *                   example: 99.00
 *                 totalAmount:
 *                   type: number
 *                   description: Total payable amount for all rooms in the target currency
 *                   example: 660.00
 *                 currency:
 *                   type: string
 *                   description: Target currency for the checkout process
 *                   example: "INR"
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Invalid request parameters"
 *       404:
 *         description: Room not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Room with ID 66da9ec31cc5eacd2fb64de2 not found"
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Internal Server Error"
 */
/**
 * @swagger
 * /booking/cancel:
 *   put:
 *     tags: [Booking]
 *     summary: Cancel booking by ID
 *     description: Cancels a booking by its ID passed in the request body.
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *             properties:
 *               id:
 *                 type: string
 *                 description: Booking ID to cancel
 *           example:
 *             id: "64f98b08322ff45b76e82abc"
 *     responses:
 *       200:
 *         description: Booking cancelled successfully
 *         content:
 *           application/json:
 *             example:
 *               message: Booking Cancelled successfully
 *       500:
 *         description: Internal Server Error
 */
