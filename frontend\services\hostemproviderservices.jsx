import httpServices from "./httpServices";

export const hostelOwnerSignup = (payload) => {
  return httpServices.post(`/auth/hostel-provider`, payload);
};

export const CheckHostelProperty = (payload) => {
  return httpServices.post(
    `http://localhost:4000/property/check-property`,
    payload
  );
};

export const ResetPasswordApi = (payload) => {
  return httpServices.put(`/auth/reset-password`, payload);
};

export const EditAboutusApi = (payload, id) => {
  return httpServices.put(`/property/${id}`, payload);
};

export const AddRoomApi = (payload) => {
  return httpServices.post(`/room/`, payload);
};

export const AddBookingApi = (payload) => {
  return httpServices.post(`/booking/`, payload);
};

export const RoomListApi = (id, currentPage, propertiesPerPage) => {
  return httpServices.get(
    `/room/all/${id}?page=${currentPage}&limit=${propertiesPerPage}`
  );
};
// export const RoomListApiget = (id) => {
//   return httpServices.get(`/room/all/${id}/`)
// }
export const EDitRoomtApi = (slug, payload) => {
  return httpServices.put(`/room/${slug}`, payload);
};
export const DeleteRoomApi = (slug) => {
  return httpServices.delete(`/room/${slug}`);
};

export const AddEventApi = (payload) => {
  return httpServices.post(`/events/`, payload);
};

export const GetReviewApi = (id,currentPage,propertiesPerPage) => {
  return httpServices.get(`/reviews/all/${id}?page=${currentPage}&limit=${propertiesPerPage}`)
}


export const BookingListApi = (id, currentPage, propertiesPerPage) => {
  return httpServices.get(
    `/booking/all/${id}?page=${currentPage}&limit=${propertiesPerPage}`
  );
};

export const EventListApi = () => {
  return httpServices.get(
    `/events/`
  );
};

export const EditEventApi = (id,payload) => {
  return httpServices.put(
    `/events/${id}`,payload
  );
};

export const GetEventApi = (id) => {
  return httpServices.get(
    `/events/${id}`
  );
};


export const DeleteEventApi = (id) => {
  return httpServices.delete(
    `/events/${id}`
  );
};

export const searchGetApi = (name) => {
  return httpServices.get(`/otaProperties/list/${name}`)
}
