import React, { useState } from "react";
import { Check , X} from "lucide-react";
import Image from "next/image";

const AddChannel = () => {

    const [activeSection, setActiveSection] = useState("default");    
    const handleSecondSectionClick = () => {
        setActiveSection("connetionmgt");
    };


    return (
      <>
       
          <section className='w-full'>
            <div className='w-full'>
              <div className='max-w-[780px] mx-auto pb-5'>

              {activeSection === "default" && (
                <div>
                    <p className="font-bold sm:text-[18px] text-[16px] mb-5 p-0">Agent Authorisation</p>
                    <div className="bg-[#E0EFD8] text-[#3C5E46] flex gap-1.5 rounded-lg p-3 mb-4">
                        <Check className="font-bold text-[#3C5E46] min-w-6 min-h-6 w-6 h-6" />
                        <p className="mb-0 sm:text-base text-sm">Please specify connection details in the form Bellow. You can find these details in channel extranet or get them your mixdorm profile manager</p>
                    </div>
                    <div className="sm:mb-5 mb-3">
                        <label
                        className='block text-black sm:text-sm text-xs font-medium mb-1.5 '
                        htmlFor='username'
                        >
                        Hostel Id key for the channel Mixdorm
                        </label>
                        <input
                        type='text'
                        name='name'
                        className='w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500'
                        placeholder='Exl.2145236'
                        />
                    </div>
                    <button
                        type='submit'
                        className='bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-auto text-sm'
                        onClick={handleSecondSectionClick}
                        >
                        Continue
                    </button>
                </div>
              )}

                {activeSection === "connetionmgt" && (
                    <div>
                        <p className="font-bold sm:text-[18px] text-[16px] mb-5 rounded-lg p-0">Connection  Management</p>
                        <div className="bg-[#40E0D01A] flex justify-between items-center mb-3 rounded-lg p-3">
                            <div className="flex gap-1.5">
                                <Check className="font-bold text-[#40E0D0] sm:min-w-6 sm:min-h-6 sm:w-6 sm:h-6 min-w-4 min-h-4 w-4 h-4" />
                                <span className="text-sm text-[#40E0D0]">Connection confirmation</span>
                            </div>
                            <Image src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/que.svg`} width={22} height={22}/>
                        </div>
                        <div className="bg-[#40E0D01A] flex justify-between items-center mb-3 rounded-lg p-3">
                            <div className="flex gap-1.5">
                                <Check className="font-bold text-[#40E0D0] min-w-6 min-h-6 w-6 h-6" />
                                <span className="text-sm text-[#40E0D0]">Rate Plan Mapping</span>
                            </div>
                            <Image src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/que.svg`} width={22} height={22}/>
                        </div>
                        <div className="bg-[#FF2E001A] flex justify-between items-center mb-3 rounded-lg p-3">
                            <div className="flex gap-1.5">
                                <X className="font-bold text-[#FF2E00] min-w-6 min-h-6 w-6 h-6" />
                                <span className="text-sm text-[#FF2E00]">Room Mappingn</span>
                            </div>
                            <Image src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/que.svg`} width={22} height={22}/>
                        </div>
                        <button
                        type='submit'
                        className='bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-auto text-sm'
                        >
                        Disconnect Request
                    </button>
                    </div>
                )}

              </div>

  
            </div>
          </section>
      </>
    );
  };
  
  export default AddChannel;
  