import React from "react";
import { RiDiscountPercentFill } from "react-icons/ri";
import { MdStars } from "react-icons/md";
import { FaAward } from "react-icons/fa6";
import Image from "next/image";

const RedeemPoint = () => {
  
  return (
    <>
       <div className="w-full">
        <div className="mb-7">
          <h2 className="text-[#40E0D0] flex gap-1 text-2xl font-bold">
            Redeem
            <span className="text-black ml-0.5"> Points</span>
          </h2>
          <p className="text-black text-sm font-medium">
            Use your points to unlock exclusive rewards !
          </p>
        </div>
        <div className="mb-6 flex gap-2">
          <MdStars size={32} className="text-[#FFC700]" />
          <h2 className="text-black text-2xl font-bold">
            You have 1,250 points
          </h2>
        </div>
        <div className="mb-6">
          <table className="border border-[#40E0D0] w-full rounded-xl">
            <thead>
              <tr>
                <th className="bg-primary-blue text-black text-xl font-bold py-3 text-left pl-3">
                  Booking
                </th>
                <th className="bg-primary-blue text-black text-xl font-bold py-3 text-left pl-3">
                  Referral Points
                </th>
                <th className="bg-primary-blue text-black text-xl font-bold py-3 text-left pl-3">
                  Event Points
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="bg-[#D9F9F6] text-lg text-[#5E5E5E] font-normal py-3 border-r border-[#40E0D0] text-left pl-3">
                  Earned from your bookings
                </td>
                <td className="bg-[#D9F9F6] text-lg text-[#5E5E5E] font-normal py-3 border-r border-[#40E0D0] text-left pl-3">
                  Earned from referring friends
                </td>
                <td className="bg-[#D9F9F6] text-lg text-[#5E5E5E] font-normal py-3 text-left pl-3">
                  Earned from attending events.
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <h3 className="text-[#40E0D0] text-xl font-bold mb-5">
          How
          <span className="text-black ml-0.5"> to Earn Points</span>
        </h3>
        <div className="flex justify-between mb-6">
          <div className="inline-block relative">
          <div className="flex justify-center">
          <div className="bg-[#FF5A3C] font-normal text-white flex items-center gap-1 rounded-lg h-[56px] px-3 w-[130px]">
            <FaAward size={25} />100 points
            </div>
          </div>

             <div className="text-center mt-2">
              <p className="text-lg font-bold">Booking Rewards</p>
              <p className="text-lg font-normal">Earn 100 points for every 
                <br />
                night booked </p>
            </div>
            </div>
            <div className="inline-block relative">
          <div className="flex justify-center">
          <div className="bg-[#FF5A3C] font-normal text-white flex items-center gap-1 rounded-lg h-[56px] w-[207px] px-2">
          <div><MdStars size={25} /></div> 50 points for Riview 
          <br />
          100 points for referral
            </div>
          </div>

             <div className="text-center mt-2">
              <p className="text-lg font-bold">Engagement Points</p>
              <p className="text-lg font-normal">Engage with the platform and
                <br />
                earn points!” </p>
            </div>
            </div>
            <div className="inline-block relative">
          <div className="flex justify-center">
          <div className="bg-[#FF5A3C] font-normal text-white flex items-center gap-1 rounded-lg h-[56px] w-[267px] px-2">
                <Image src="https://mixdorm.s3.ap-south-1.amazonaws.com/front-images/handstar.png" className="w-7 h-7" alt="handstars"/><span>200 points for each event
              <br />
              Points vary based on activity
            </span>
            </div>
          </div>

             <div className="text-center mt-2">
              <p className="text-lg font-bold">Activity Points</p>
              <p className="text-lg font-normal">Participate in activities to earn 
                <br />
                more points!</p>
            </div>
            </div>
        </div>
        <div className="mb-5">
        <h3 className="text-[#40E0D0] text-xl font-bold">
          Redeem
          <span className="text-black ml-0.5"> Your Points</span>
        </h3>
        <p className="text-black text-sm font-medium">
        Use your points for discounts, free stays, or exclusive offers
          </p>
        </div>
        <div className="flex gap-2">
        <button
          type="button"
          className="py-3 transition-all bg-[#00A854] px-7 text-lg text-white font-semibold rounded-full flex gap-1 items-center"
        >
        <RiDiscountPercentFill size={20} /> Redeem for Discounts
        </button>
        <button
          type="button"
          className="py-3 transition-all bg-[#EEEEEE] px-7 text-lg text-black font-semibold rounded-full flex gap-1 items-center"
        >
        <FaAward size={20} /> View Exclusive Offers
        </button>
        </div>
 
      </div>
    </>
  
  );
};

export default RedeemPoint;
