/* eslint-disable no-constant-binary-expression */
import { RoomListApi } from "@/services/hostemproviderservices";
import { Minus, Plus } from "lucide-react";
import React, { useState, useEffect, useRef } from "react";
import axios from "axios";
import toast, { Toaster } from "react-hot-toast";
import { getItemLocalStorage } from "@/utils/browserSetting";
import Image from "next/image";
import Loader from "@/components/loader/loader";
import countries from "world-countries";
import { format } from "date-fns";
import CustomSelect from "@/components/common/CustomDropdown2";
import { isSameDay, isToday, } from "date-fns";
import "react-datepicker/dist/react-datepicker.css";
import {
  AddRateApi,
  getRateByRoomApi,
  RoomTypeListApi,
} from "@/services/ownerflowServices";

const AddRate = ({ closeaddModal }) => {
  const [addroom, setAddRoom] = useState({
    name: "",
    type: "",
    weekdayRate: "",
    weekendRate: "",
    currency: "",
    toDate: "",
    fromDate: "",
  });

  const [properties, setProperties] = useState([]);
  const [roomTypesList, setRoomTypeList] = useState([]);

  const [currencies, setCurrencies] = useState([]);

  const [isLoading, setIsLoading] = useState(false);

  const [selectedOption, setSelectedOption] = useState("amount");

  const [percentage, setPercentage] = useState(10); // Default percentage value

  const [errors, setErrors] = useState({
    name: "",
    type: "",
    weekdayRate: "",
    weekendRate: "",
    toDate: "",
    fromDate: "",
  });

  const [showCalendar, setShowCalendar] = useState(false);
  const [selectedDate, setSelectedDate] = useState("");
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const calendarRef = useRef(null);
  const inputRef = useRef(null);
  const [showFromCalendar, setShowFromCalendar] = useState(false);
  const [currentMonthFrom, setCurrentMonthFrom] = useState(new Date());
  const calendarRefFrom = useRef(null);

  const handlePrevMonth = () => {
    setCurrentMonth(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() - 1, 1)
    );
  };

  const handleNextMonth = () => {
    setCurrentMonth(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() + 1, 1)
    );
  };

  // Functions to move months
  const handlePrevMonthFrom = () => {
    setCurrentMonthFrom(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() - 1, 1)
    );
  };

  const handleNextMonthFrom = () => {
    setCurrentMonthFrom(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() + 1, 1)
    );
  };
  // Generate days for current month
  function generateDays(month) {
    const daysInMonth = new Date(
      month.getFullYear(),
      month.getMonth() + 1,
      0
    ).getDate();
    const firstDayIndex = new Date(
      month.getFullYear(),
      month.getMonth(),
      1
    ).getDay();

    const days = [];

    // Add blank spaces for days of previous month
    for (let i = 0; i < firstDayIndex; i++) {
      days.push(null);
    }

    // Add days of the current month
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(new Date(month.getFullYear(), month.getMonth(), i));
    }

    return days;
  }
  // Handle clicking a date
  const handleFromDateClick = (day) => {
    const formattedDate = format(day, "MM-dd-yyyy");

    setAddRoom((prev) => ({
      ...prev,
      fromDate: formattedDate,
    }));
    setShowFromCalendar(false);
  };
  const handleDateClick = (day) => {
    const formattedDate = format(day, "MM-dd-yyyy"); // format the selected date
    setSelectedDate(formattedDate);
    setShowCalendar(false); // close calendar after selecting
  };
  // const handleDateClick = (day) => {
  //   const formattedDate = format(day, "MM-dd-yyyy");
  //   setSelectedDate(formattedDate); // Store formatted string

  //   handleChange({
  //     target: {
  //       name: "date",
  //       value: formattedDate,
  //     },
  //   });

  //   setShowCalendar(false);
  // };

  // Close calendar if clicked outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target) &&
        !inputRef.current.contains(event.target)
      ) {
        setShowCalendar(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleIncrease = () => {
    if (percentage < 100) {
      setPercentage((prev) => prev + 1); // Increase percentage by 1
    }
  };

  const handleDecrease = () => {
    if (percentage > 0) {
      setPercentage((prev) => prev - 1); // Decrease percentage by 1
    }
  };

  const isCurrenciesFetched = useRef(null);
  const isFirstRender = useRef(null);
  const id = getItemLocalStorage("hopid");

  useEffect(() => {
    if (id && !isFirstRender.current) {
      fetchList(id);
      fetchRoomTypeList();
      fetchCurrencies();
    } else {
      isFirstRender.current = false;
    }
  }, [id]);

  const fetchCurrencies = async () => {
    setIsLoading(true);
    try {
      const response = await axios.get(
        "https://api.exchangerate-api.com/v4/latest/USD"
      );
      if (response.data && response.data.rates) {
        // Convert rates to array of currency codes
        const currencyList = Object.keys(response.data.rates);
        setCurrencies(currencyList);
      }
    } catch (error) {
      console.error("Error fetching currencies:", error);
      toast.error("Failed to fetch currencies");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!isCurrenciesFetched.current) {
      const fetchCurrenciesWithFlags = async () => {
        setIsLoading(true);
        try {
          // Fetch currency rates
          const exchangeRateResponse = await axios.get(
            "https://api.exchangerate-api.com/v4/latest/USD"
          );

          if (exchangeRateResponse.data) {
            const currencyRates = exchangeRateResponse.data.rates;

            // Map currency codes to country data using the world-countries package
            const currencyList = Object.keys(currencyRates).map(
              (currencyCode) => {
                const country = countries.find((country) =>
                  country.currencies
                    ? Object.keys(country.currencies).includes(currencyCode)
                    : false
                );

                return {
                  code: currencyCode,
                  name:
                    country?.currencies?.[currencyCode]?.name || currencyCode, // Fallback to the currency code if no name is found
                  symbol: country?.currencies?.[currencyCode]?.symbol || "", // Fallback to an empty string if no symbol is found
                  flag:
                    `https://flagcdn.com/w320/${country?.cca2?.toLowerCase()}.png` ||
                    "https://via.placeholder.com/30x25", // Fallback to a placeholder if no flag is found
                };
              }
            );

            setCurrencies(currencyList);
          }
        } catch (error) {
          console.error("Error fetching currencies or rates:", error);
        } finally {
          setIsLoading(false);
        }
      };

      fetchCurrenciesWithFlags();
      isCurrenciesFetched.current = true; // Ensure the fetch happens only once
    }
  }, []);

  const fetchList = async (id) => {
    setIsLoading(true);
    try {
      const response = await RoomListApi(id, 1, 100);
      if (response.status === 200) {
        setProperties(response.data.data.rooms);
      } else {
        // Handle error response
        toast.error("Failed to fetch rooms");
      }
    } catch (error) {
      console.log("Error fetching rooms:", error);
      toast.error("Error fetching rooms");
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRoomTypeList = async () => {
    setIsLoading(true);
    try {
      const response = await RoomTypeListApi();
      if (response.data.status) {
        setRoomTypeList(response.data.data.roomTypes);
      } else {
        // Handle error response
        toast.error("Failed to fetch rooms types");
      }
    } catch (error) {
      console.log("Error fetching rooms types:", error);
      toast.error("Error fetching rooms types");
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = async (e) => {
    const { name, value } = e.target;
    console.log("name", name, value);
    if (name === "name") {
      setAddRoom({
        ...addroom,
        [name]: value,
      });
      const selectedRoomName = e.target.value.label;
      const room = properties.find((r) => r.name === selectedRoomName);
      const ratesData = await getRateByRoomApi(room?._id);
      console.log("ratesData", ratesData);
      setAddRoom({
        ...addroom,
        ...room,
        weekdayRate: room?.rate?.weekdayRate?.value || "",
        weekendRate: room?.rate?.weekendRate?.value || "",
        currency: room?.currency || "",
      });
    } else {
      const { name, value } = e.target;
      setAddRoom({
        ...addroom,
        [name]: value,
      });
    }
  };

  // eslint-disable-next-line no-unused-vars
  const [selectedValues, setSelectedValues] = useState({
    name: addroom.name || "",
    type: addroom.type || "",
  });

  console.log("addRoom", addroom, roomTypesList);

  const validateFields = () => {
    let isValid = true;
    const newErrors = { name: "", type: "", weekdayRate: "", weekendRate: "" };

    if (!addroom.name) {
      newErrors.name = "Select Name is required";
      isValid = false;
    }
    if (!addroom?.type?.value) {
      newErrors.type = "Choose Room Type is required";
      isValid = false;
    }
    if (!addroom.weekdayRate) {
      newErrors.weekdayRate = "Weekday rate is required";
      isValid = false;
    }
    if (!addroom.weekendRate) {
      newErrors.weekendRate = "Weekend rate is required";
      isValid = false;
    }
    if (!addroom.toDate) {
      newErrors.toDate = "To Date is required";
      isValid = false;
    }
    if (!addroom.fromDate) {
      newErrors.fromDate = "From Date is required";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleAddRate = async () => {
    if (validateFields()) {
      const payload = {
        room: addroom._id,
        date: new Date().toISOString().split("T")[0],
        toDate: addroom?.toDate,
        fromDate: addroom?.fromDate,
        rate: {
          weekdayRate: {
            value: Number(addroom.weekdayRate),
            updatedAt: new Date().toISOString(),
          },
          weekendRate: {
            value: Number(addroom.weekendRate),
            updatedAt: new Date().toISOString(),
          },
          averagePrice: {
            value:
              (Number(addroom.weekdayRate) + Number(addroom.weekendRate)) / 2 ||
              0,
          },
          basePrice: Number(addroom.weekdayRate),
        },
        currency: addroom.currency.value,
      };
      setIsLoading(true);
      try {
        await AddRateApi(payload);
        toast.success("Rate added successfully");
        closeaddModal();
        // eslint-disable-next-line no-unused-vars
      } catch (error) {
        toast.error("Failed to add rate");
      } finally {
        setIsLoading(false);
      }
    }
  };

  return (
    <>
      <Loader open={isLoading} />
      <div className="max-w-[780px] mx-auto pb-8">
        <Toaster />
        <div className="grid grid-cols-2 sm:gap-5 gap-3">
          <div className="sm:col-span-1 col-span-2 relative">
            <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
              {selectedValues.name ? "Room Name" : "Select Name"}
            </label>

            <CustomSelect
              name="name"
              options={properties.map((room) => ({
                value: room?._id,
                label: room?.name,
              }))}
              value={
                addroom.name
                  ? {
                      value: addroom?._id,
                      label: `${addroom.name}`,
                    }
                  : null
              }
              onChange={(selectedOption) =>
                handleChange({
                  target: { name: "name", value: selectedOption },
                })
              }
              placeholder="Select Room"
              className={errors.name ? "border-red-500" : ""}
            />
            {errors.name && (
              <p className="text-red-600 text-sm">{errors.name}</p>
            )}
          </div>

          <div className="sm:col-span-1 col-span-2 relative">
            <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
              Choose Room Type
            </label>

            <CustomSelect
              name="type"
              options={roomTypesList.map((roomType) => ({
                value: roomType?._id,
                label: roomType?.RoomType,
              }))}
              value={
                addroom.type
                  ? {
                      value: addroom?.type?.value,
                      label: `${addroom.type?.label}`,
                    }
                  : null
              }
              onChange={(selectedOption) =>
                handleChange({
                  target: { name: "type", value: selectedOption },
                })
              }
              placeholder="Select Room Type"
              className={errors.type ? "border-red-500" : ""}
            />
            {errors.type && (
              <p className="text-red-600 text-sm">{errors.type}</p>
            )}
          </div>

          <div className="col-span-2">
            <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
              Set the rate values
            </label>
            <div className="grid sm:grid-cols-2  gap-4">
              <div>
                <div
                  onClick={() => setSelectedOption("amount")}
                  className={`w-full flex justify-between sm:px-4 px-2 sm:py-2.5 py-2 border rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs cursor-pointer ${
                    selectedOption === "amount"
                      ? "text-black bg-[#40E0D0] border-[#40E0D0]"
                      : "text-black/50 bg-white border-black/50"
                  }`}
                >
                  <input
                    type="radio"
                    value="amount"
                    checked={selectedOption === "amount"}
                    onChange={() => setSelectedOption("amount")}
                    style={{ display: "none" }}
                  />
                  <span>Amount</span>
                  <Image
                    className={`${
                      selectedOption === "amount" ? "block" : "hidden"
                    }`}
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/checked.svg`}
                    width={20}
                    height={20}
                  />
                </div>
              </div>
              <div>
                <div
                  onClick={() => setSelectedOption("percentage")}
                  className={`w-full flex justify-between sm:px-4 px-2 sm:py-2.5 py-2 border rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs cursor-pointer ${
                    selectedOption === "percentage"
                      ? "text-black bg-[#40E0D0] border-[#40E0D0]"
                      : "text-black/50 bg-white border-black/50"
                  }`}
                >
                  <input
                    type="radio"
                    value="percentage"
                    checked={selectedOption === "percentage"}
                    onChange={() => setSelectedOption("percentage")}
                    style={{ display: "none" }}
                  />
                  <span>Percentage</span>
                  <Image
                    className={`${
                      selectedOption === "percentage" ? "block" : "hidden"
                    }`}
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/checked.svg`}
                    width={20}
                    height={20}
                  />
                </div>

                {selectedOption === "percentage" && (
                  <div className="sm:mt-5 mt-3">
                    <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                      Percentage discount off the standard rate
                    </label>
                    <div className="w-full flex justify-between sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500">
                      <button
                        onClick={handleDecrease}
                        style={{ cursor: "pointer" }}
                        disabled={percentage <= 0}
                      >
                        <Minus
                          color="#00000080"
                          className="w-[20px] h-[20px]"
                        />
                      </button>
                      <input
                        className="text-center w-full"
                        type="text"
                        value={`${percentage}%`}
                        readOnly
                      />

                      <button
                        onClick={handleIncrease}
                        style={{ cursor: "pointer" }}
                        disabled={percentage >= 100}
                      >
                        <Plus color="#00000080" className="w-[20px] h-[20px]" />
                      </button>
                    </div>
                    <div className="grid grid-cols-3 font-medium text-sm text-center mt-6">
                      <div>
                        <p className="font-medium sm:text-sm text-xs">Thu</p>
                        <p className="font-medium sm:text-sm text-xs">May 04</p>
                      </div>
                      <div>
                        <p className="font-medium sm:text-sm text-xs">Fri</p>
                        <p className="font-medium sm:text-sm text-xs">May 05</p>
                      </div>
                      <div>
                        <p className="font-medium sm:text-sm text-xs">Sat</p>
                        <p className="font-medium sm:text-sm text-xs">May 06</p>
                      </div>
                    </div>
                    <div className="grid grid-cols-3 font-medium text-sm text-center sm:mt-6 mt-4 gap-1.5">
                      <label className="block text-black sm:text-sm text-xs font-medium mb-1.5 col-span-3 text-left">
                        Percentage discount off the standard rate
                      </label>
                      <input
                        type="text"
                        name=""
                        className="w-full sm:px-2.5 px-1.5 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500 font-normal"
                        placeholder="10000.00"
                      />
                      <input
                        type="text"
                        name=""
                        className="w-full sm:px-2.5 px-1.5 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500 font-normal"
                        placeholder="15000.00"
                      />
                      <input
                        type="text"
                        name=""
                        className="w-full sm:px-2.5 px-1.5 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500 font-normal"
                        placeholder="20000.00"
                      />
                    </div>
                    <div className="grid grid-cols-3 font-medium text-sm text-center sm:mt-6 mt-4 gap-1.5">
                      <label className="block text-black sm:text-sm text-xs font-medium mb-1.5 col-span-3 text-left">
                        Non- refundable Rate
                      </label>
                      <input
                        type="text"
                        name=""
                        className="w-full sm:px-2.5 px-1.5 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500 font-normal"
                        placeholder="900.00"
                      />
                      <input
                        type="text"
                        name=""
                        className="w-full sm:px-2.5 px-1.5 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500 font-normal"
                        placeholder="13500.00"
                      />
                      <input
                        type="text"
                        name=""
                        className="w-full sm:px-2.5 px-1.5 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500 font-normal"
                        placeholder="13500.00"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          {/* <div>
            <label
              className="block text-black sm:text-sm text-xs font-medium mb-1.5"
              htmlFor="weekday-rate"
            >
              To Date
            </label>

            <DatePicker
              selected={addroom.toDate ? new Date(addroom.toDate) : null}
              onChange={(date) =>
                handleChange({ target: { name: "toDate", value: date } })
              }
              placeholderText="mm/dd/yyyy"
              dateFormat="MM/dd/yyyy"
              className="block w-full p-2 px-4 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-320 placeholder:text-gray-320"
              name="toDate"
              required
              popperPlacement="bottom-start" // This forces the calendar to appear at the bottom
              
            />

            {errors.toDate && (
              <p className="text-red-600 text-sm">{errors.toDate}</p>
            )}
          </div> */}
          {/* <div>
            <label
              className='block text-black sm:text-sm text-xs font-medium mb-1.5'
              htmlFor='weekday-rate'
            >
              To Date
            </label>
            <input
                type='date'
                name='toDate'
                value={addroom.toDate}
                onChange={handleChange}
                onFocus={(e) => e.target.showPicker()}
                className='block w-full p-2 px-4 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-320 placeholder:text-gray-320'
                required
              />
            {errors.toDate && (
              <p className="text-red-600 text-sm">{errors.toDate}</p>
            )}
          </div> */}
          <div className="relative w-full max-w-xs">
            <label
              className="block text-black sm:text-sm text-xs font-medium mb-1.5"
              htmlFor="toDate"
            >
              To Date
            </label>

            <input
              type="text"
              name="toDate"
              id="toDate"
              ref={inputRef}
              value={selectedDate}
              placeholder="mm/dd/yyyy"
              readOnly
              onClick={() => setShowCalendar(!showCalendar)}
              className="block w-full p-2 px-4 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-800 placeholder:text-gray-400 cursor-pointer"
            />

            {showCalendar && (
              <div
                ref={calendarRef}
                className="absolute top-full left-0 bg-white border border-black/50 rounded-lg shadow-lg px-4 py-2 z-50 w-full mt-0.5"
              >
                {/* Month navigation */}
                <div className="flex items-center justify-between mb-4">
                  <button
                    onClick={handlePrevMonth}
                    className="text-lg font-bold px-2"
                  >
                    &#8592;
                  </button>

                  <span className="text-base font-semibold">
                    {format(currentMonth, "MMMM yyyy")}
                  </span>

                  <button
                    onClick={handleNextMonth}
                    className="text-lg font-bold px-2"
                  >
                    &#8594;
                  </button>
                </div>

                {/* Weekdays */}
                <div className="grid grid-cols-7 gap-2 text-center text-sm font-semibold text-gray-600">
                  <div>Su</div>
                  <div>Mo</div>
                  <div>Tu</div>
                  <div>We</div>
                  <div>Th</div>
                  <div>Fr</div>
                  <div>Sa</div>
                </div>

                {/* Days */}
                <div className="grid grid-cols-7 gap-1 mt-2 text-center text-sm">
                  {generateDays(currentMonth).map((day, index) =>
                    day ? (
                      // <button
                      //   key={index}
                      //   className="hover:bg-primary-blue hover:text-white rounded-full p-2"
                      //   onClick={() => handleDateClick(day)}
                      // >
                      //   {day.getDate()}
                      // </button>
                      <button
                        key={index}
                        className={`rounded-full p-2 text-sm flex items-center justify-center
                          ${isToday(day) ? "border-2 border-primary-blue" : ""} ${
                          selectedDate && isSameDay(selectedDate, day)
                            ? "bg-primary-blue text-white"
                            : "hover:bg-primary-blue hover:text-white"
                        }`}
                        onClick={() => handleDateClick(day)}
                      >
                        {day.getDate()}
                      </button>
                    ) : (
                      <div key={index} />
                    )
                  )}
                </div>
              </div>
            )}
            {errors.fromDate && (
              <p className="text-red-600 text-sm">{errors.toDate}</p>
            )}
          </div>

          {/* <div>
            <label
              className="block text-black sm:text-sm text-xs font-medium mb-1.5"
              htmlFor="weekday-rate"
            >
              From Date
            </label>
            <input
              type="date"
              name="fromDate"
              value={addroom.fromDate}
              onChange={handleChange}
              onFocus={(e) => e.target.showPicker()}
              className="block w-full p-2 px-4 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-320 placeholder:text-gray-320"
              required
            />
            {errors.fromDate && (
              <p className="text-red-600 text-sm">{errors.fromDate}</p>
            )}
          </div> */}
          <div className="relative w-full max-w-xs">
            <label
              className="block text-black sm:text-sm text-xs font-medium mb-1.5"
              htmlFor="fromDate"
            >
              From Date
            </label>

            <input
              type="text"
              name="fromDate"
              id="fromDate"
              value={addroom.fromDate}
              placeholder="mm/dd/yyyy"
              readOnly
              onClick={() => setShowFromCalendar(!showFromCalendar)}
              className="block w-full p-2 px-4 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-800 placeholder:text-gray-400 cursor-pointer"
              required
            />

            {showFromCalendar && (
              <div
                ref={calendarRefFrom}
                className="absolute top-full left-0 mt-0.5 bg-white border border-black/50 rounded-lg shadow-lg px-4 py-2 z-50 w-full"
              >
                {/* Month navigation */}
                <div className="flex items-center justify-between mb-4">
                  <button
                    onClick={handlePrevMonthFrom}
                    className="text-lg font-bold px-2"
                  >
                    &#8592;
                  </button>

                  <span className="text-base font-semibold">
                    {format(currentMonthFrom, "MMMM yyyy")}
                  </span>

                  <button
                    onClick={handleNextMonthFrom}
                    className="text-lg font-bold px-2"
                  >
                    &#8594;
                  </button>
                </div>

                {/* Weekdays */}
                <div className="grid grid-cols-7 gap-2 text-center text-sm font-semibold text-gray-600">
                  <div>Su</div>
                  <div>Mo</div>
                  <div>Tu</div>
                  <div>We</div>
                  <div>Th</div>
                  <div>Fr</div>
                  <div>Sa</div>
                </div>

                {/* Days */}
                <div className="grid grid-cols-7 gap-1 mt-2 text-center text-sm">
                  {generateDays(currentMonthFrom).map((day, index) =>
                    day ? (
                      // <button
                      //   key={index}
                      //   className="hover:bg-primary-blue hover:text-white rounded-full p-2"
                      //   onClick={() => handleFromDateClick(day)}
                      // >
                      //   {day.getDate()}
                      // </button>
                      <button
                        key={index}
                        className={`rounded-full p-2 text-sm flex items-center justify-center 
                          ${isToday(day) ? "border-2 border-primary-blue" : ""} ${
                          addroom.fromDate === format(day, "MM-dd-yyyy")
                            ? "bg-primary-blue text-white"
                            : "hover:bg-primary-blue hover:text-white"
                        }`}
                        onClick={() => handleFromDateClick(day)}
                      >
                        {day.getDate()}
                      </button>
                    ) : (
                      <div key={index} />
                    )
                  )}
                </div>
              </div>
            )}

            {errors.fromDate && (
              <p className="text-red-600 text-sm">{errors.fromDate}</p>
            )}
          </div>

          <div>
            <label
              className="block text-black sm:text-sm text-xs font-medium mb-1.5"
              htmlFor="weekday-rate"
            >
              Weekday rate
            </label>
            <div className="inner-currency-wrap">
              <div className="flex items-center w-full sm:px-1 px-2 py-1 border border-black/50 rounded-lg focus-within:ring-1 focus-within:ring-teal-500">
                {/* <select
                name='currency'
                value={addroom.currency.value}
                onChange={handleChange}
                className='sm:w-20 w-16 focus:outline-none text-black sm:text-sm text-xs placeholder:text-gray-500'
              >
                <option disabled value=''>
                  EUR
                </option>
                {currencies.map((currency) => (
                  <option key={currency.code} value={currency.code}>
                    {currency?.code}
                  </option>
                ))}
              </select> */}

                <CustomSelect
                  className="w-[150%]"
                  options={currencies.map((currency) => ({
                    value: currency.code,
                    label: (
                      <span className="flex items-center">
                        <Image 
                          src={currency.flag || "/placeholder.svg"}
                          alt={`${currency.code} Flag`}
                          className="inline-block w-4 h-3 mr-2"
                          width={20}
                          height={15}
                        />
                        {currency.code} ({currency.symbol})
                      </span>
                    ),
                  }))}
                  value={addroom.currency}
                  onChange={(selectedOption) =>
                    handleChange({
                      target: { name: "currency", value: selectedOption },
                    })
                  }
                  placeholder="Currency"
                />
                <span className="mr-2 text-gray-300">|</span>
                <input
                  type="number"
                  name="weekdayRate"
                  placeholder="weekdayRate"
                  value={addroom.weekdayRate}
                  onChange={handleChange}
                  className={`w-full bg-transparent focus:outline-none text-black sm:text-sm text-xs placeholder:text-gray-500 pl-9 ${
                    errors.weekdayRate ? "border-red-500" : ""
                  }`}
                />
              </div>
            </div>
            {errors.weekdayRate && (
              <p className="text-red-600 text-sm">{errors.weekdayRate}</p>
            )}
          </div>

          <div>
            <label
              className="block text-black sm:text-sm text-xs font-medium mb-1.5"
              htmlFor="weekday-rate"
            >
              Weekend rate
            </label>
            <div className="inner-currency-wrap">
              <div className="flex items-center w-full sm:px-1 px-2 py-1 border border-black/50 rounded-lg focus-within:ring-1 focus-within:ring-teal-500">
                {/* <select
                name='currency'
                value={addroom.currency.value}
                onChange={handleChange}
                className='sm:w-20 w-16 focus:outline-none text-black sm:text-sm text-xs placeholder:text-gray-500'
              >
                <option disabled value=''>
                  EUR
                </option>
                {currencies.map((currency) => (
                  <option key={currency.code} value={currency.code}>
                    {currency?.code}
                  </option>
                ))}
              </select> */}

                <CustomSelect
                  className="w-[150%]"
                  options={currencies.map((currency) => ({
                    value: currency.code,
                    label: (
                      <span className="flex items-center">
                        <Image 
                          src={currency.flag || "/placeholder.svg"}
                          alt={`${currency.code} Flag`}
                          className="inline-block w-4 h-3 mr-2"
                          width={20}
                          height={15}
                        />
                        {currency.code} ({currency.symbol})
                      </span>
                    ),
                  }))}
                  value={addroom.currency}
                  onChange={(selectedOption) =>
                    handleChange({
                      target: { name: "currency", value: selectedOption },
                    })
                  }
                  placeholder="Currency"
                />
                <span className="mr-2 text-gray-300">|</span>
                <input
                  type="number"
                  name="weekendRate"
                  placeholder="weekendRate"
                  value={addroom.weekendRate}
                  onChange={handleChange}
                  className={`w-full bg-transparent focus:outline-none text-black sm:text-sm text-xs placeholder:text-gray-500 pl-9 ${
                    errors.weekendRate ? "border-red-500" : ""
                  }`}
                />
              </div>
            </div>
            {errors.weekendRate && (
              <p className="text-red-600 text-sm">{errors.weekendRate}</p>
            )}
          </div>
        </div>

        <div className="flex items-center justify-between w-full sm:my-14 my-7 gap-4 col-span-2 py-4 bg-white/60 sticky bottom-0 backdrop-blur-sm">
          <button
            className="hover:bg-black bg-transparent hover:text-white text-black font-medium py-2 px-4 border border-black rounded-lg w-full text-sm"
            onClick={closeaddModal}
          >
            Cancel
          </button>
          <button
            className="bg-black hover:bg-transparent text-white hover:text-black font-medium py-2 px-4 border border-black rounded-lg w-full text-sm"
            onClick={handleAddRate}
          >
            Add Rate
          </button>
        </div>
      </div>
    </>
  );
};

export default AddRate;
