import s3 from '../config/aws.js';
import fs from 'fs/promises';

// Upload file to S3
export async function uploadFile(filePath, bucketName, keyName) {
  try {
    const fileContent = await fs.readFile(filePath);

    const params = {
      Bucket: bucketName,
      Key: keyName,
      Body: fileContent,
      ACL: 'public-read'
    };

    const data = await s3.upload(params).promise();
    console.log(`File uploaded successfully. ${data.Location}`);
  } catch (err) {
    console.error('Error uploading file:', err);
  }
}

// List files in S3 bucket
export async function listFiles(bucketName) {
  try {
    const params = {
      Bucket: bucketName
    };

    const data = await s3.listObjectsV2(params).promise();
    data.Contents.forEach(file => {
      console.log(`File: ${file.Key}`);
    });
  } catch (err) {
    console.error('Error listing files:', err);
  }
}
