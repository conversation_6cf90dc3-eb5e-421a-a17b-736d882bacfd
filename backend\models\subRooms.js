import mongoose from 'mongoose';
const roomSchema = new mongoose.Schema(
    {
        name: {
            type: String,
            required: true,
        },
        property: {
            type: mongoose.Types.ObjectId,
            ref: 'properties',
            required: true,
        },
        room: {
            type: mongoose.Types.ObjectId,
            ref: 'rooms',
            required: true,
        },
        ezeeId: { type: String, required: true },

    },
    {
        timestamps: true,
    }
);


const roomInstancesModel = mongoose.model('roomInstances', roomSchema);

export default roomInstancesModel;