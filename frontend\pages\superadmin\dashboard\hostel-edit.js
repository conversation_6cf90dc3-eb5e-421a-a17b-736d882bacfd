// "use client";
// import EditHostel from "@/components/superadmin/EditHostel";
// import React, { useState } from "react";
// import { FaChevronDown, FaChevronUp } from "react-icons/fa";
// import Image from "next/image";
// import Link from "next/link";

// const HostelEditForm = ({ onClose }) => {
//   const [activeTab, setActiveTab] = useState(0);
//   const [activeForm, setActiveForm] = useState("editHostel");
//   const handleTab = (index) => {
//     setActiveTab(index);
//   };

//   const [isCountryDropdownOpen, setIsCountryDropdownOpen] = useState(false);
//   const [selectedCountry, setSelectedCountry] = useState("India");

//   const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false);
//   const [selectedStatus, setSelectedStatus] = useState("Verify");

//   const [isOfferDropdownOpen, setIsOfferDropdownOpen] = useState(false);
//   const [selectedOffer, setSelectedOffer] = useState("Discount");

//   // Toggle functions
//   const toggleCountryDropdown = () =>
//     setIsCountryDropdownOpen(!isCountryDropdownOpen);
//   const toggleStatusDropdown = () =>
//     setIsStatusDropdownOpen(!isStatusDropdownOpen);
//   const toggleOfferDropdown = () =>
//     setIsOfferDropdownOpen(!isOfferDropdownOpen);

//   // Option select handlers
//   const handleCountrySelect = (country) => {
//     setSelectedCountry(country);
//     setIsCountryDropdownOpen(false);
//   };

//   const handleStatusSelect = (status) => {
//     setSelectedStatus(status);
//     setIsStatusDropdownOpen(false);
//   };

//   const handleOfferSelect = (offer) => {
//     setSelectedOffer(offer);
//     setIsOfferDropdownOpen(false);
//   };

//   return (
//     <div>
//       {/* <div className="bg-white border flex items-center mt-5 justify-center p-10 rounded-xl h-auto">
//         <div className="bg-white w-full max-w-3xl">
//           <div className="flex justify-center gap-3 mb-10">
//             <button
//               className={`py-2 px-8 ${activeForm === "editHostel" ? "bg-sky-blue-650 text-white" : "border border-gray-300 text-gray-600"} rounded`}
//               onClick={() => setActiveForm("editHostel")}
//             >
//               Edit Hostel Detail
//             </button>
//             <button
//               className={`py-2 px-8 ${activeForm === "roomDetail" ? "bg-sky-blue-650 text-white" : "border border-gray-300 text-gray-600"} rounded`}
//               onClick={() => setActiveForm("roomDetail")}
//             >
//               Edit Room Detail
//             </button>
//             <button
//               className={`py-2 px-8 ${activeForm === "rateDetail" ? "bg-sky-blue-650 text-white" : "border border-gray-300 text-gray-600"} rounded`}
//               onClick={() => setActiveForm("rateDetail")}
//             >
//               Edit Rate Detail
//             </button>
//           </div>
//           {activeForm === "editHostel" && (
//             <form>
//               <div className="grid grid-cols-1 mt-10 md:grid-cols-2 gap-6">
                
//                 <div>
//                   <label className="block font-semibold text-black/40">
//                     Hostel Name
//                   </label>
//                   <input
//                     type="text"
//                     placeholder="Enter hostel name"
//                     className="mt-1 p-2 w-full border rounded-md bg-[#EEF9FF]"
//                   />
//                 </div>
//                 <div>
//                   <label className="block font-semibold text-black/40">
//                     Hostel Address
//                   </label>
//                   <input
//                     type="text"
//                     placeholder="Enter hostel address"
//                     className="mt-1 p-2 w-full border rounded-md bg-[#EEF9FF]"
//                   />
//                 </div>
//                 <div>
//                   <label className="block font-semibold text-black/40">
//                     Rate
//                   </label>
//                   <input
//                     type="number"
//                     placeholder="Enter rate"
//                     className="mt-1 p-2 w-full border rounded-md bg-[#EEF9FF]"
//                   />
//                 </div>
//                 <div>
//                   <label className="block font-semibold text-black/40">
//                     Floor
//                   </label>
//                   <input
//                     type="number"
//                     placeholder="Enter floor"
//                     className="mt-1 p-2 w-full border rounded-md bg-[#EEF9FF]"
//                   />
//                 </div>
//                 <div>
//                   <label className="block font-semibold text-black/40">
//                     Room
//                   </label>
//                   <input
//                     type="number"
//                     placeholder="Enter room"
//                     className="mt-1 p-2 w-full border rounded-md bg-[#EEF9FF]"
//                   />
//                 </div>
//                 <div>
//                   <label className="block font-semibold text-black/40">
//                     Bed
//                   </label>
//                   <input
//                     type="number"
//                     placeholder="Enter bed count"
//                     className="mt-1 p-2 w-full border rounded-md bg-[#EEF9FF]"
//                   />
//                 </div>
//                 <div>
//                   <label className="block font-semibold text-black/40">
//                     Country
//                   </label>
//                   <div className="relative mt-1">
//                     <button
//                       className="w-full bg-[#EEF9FF] border p-2 rounded flex items-center justify-between"
//                       onClick={toggleCountryDropdown}
//                       type="button"
//                     >
//                       {selectedCountry}
//                       {isCountryDropdownOpen ? (
//                         <FaChevronUp className="text-gray-500" />
//                       ) : (
//                         <FaChevronDown className="text-gray-500" />
//                       )}
//                     </button>
//                     {isCountryDropdownOpen && (
//                       <ul className="absolute bg-white border mt-1 rounded w-full z-10">
//                         <li
//                           className="p-2 hover:bg-blue-100 cursor-pointer"
//                           onClick={() => handleCountrySelect("India")}
//                         >
//                           India
//                         </li>
//                         <li
//                           className="p-2 hover:bg-blue-100 cursor-pointer"
//                           onClick={() => handleCountrySelect("USA")}
//                         >
//                           USA
//                         </li>
//                         <li
//                           className="p-2 hover:bg-blue-100 cursor-pointer"
//                           onClick={() => handleCountrySelect("Canada")}
//                         >
//                           Canada
//                         </li>
//                       </ul>
//                     )}
//                   </div>
//                 </div>
//                 <div>
//                   <label className="block font-semibold text-black/40">
//                     Offer
//                   </label>
//                   <div className="relative mt-1">
//                     <button
//                       className="w-full bg-[#EEF9FF] border p-2 rounded flex items-center justify-between"
//                       onClick={toggleOfferDropdown}
//                       type="button"
//                     >
//                       {selectedOffer}
//                       {isOfferDropdownOpen ? (
//                         <FaChevronUp className="text-gray-500" />
//                       ) : (
//                         <FaChevronDown className="text-gray-500" />
//                       )}
//                     </button>
//                     {isOfferDropdownOpen && (
//                       <ul className="absolute bg-white border mt-1 rounded w-full z-10">
//                         <li
//                           className="p-2 hover:bg-blue-100 cursor-pointer"
//                           onClick={() => handleOfferSelect("Discount")}
//                         >
//                           Discount
//                         </li>
//                         <li
//                           className="p-2 hover:bg-blue-100 cursor-pointer"
//                           onClick={() => handleOfferSelect("Special Offer")}
//                         >
//                           Special Offer
//                         </li>
//                         <li
//                           className="p-2 hover:bg-blue-100 cursor-pointer"
//                           onClick={() => handleOfferSelect("No Offer")}
//                         >
//                           No Offer
//                         </li>
//                       </ul>
//                     )}
//                   </div>
//                 </div>
//                 <div>
//                   <label className="block font-semibold text-black/40">
//                     Status
//                   </label>
//                   <div className="relative mt-1">
//                     <button
//                       className="w-full bg-[#EEF9FF] border p-2 rounded flex items-center justify-between"
//                       onClick={toggleStatusDropdown}
//                       type="button"
//                     >
//                       {selectedStatus}
//                       {isStatusDropdownOpen ? (
//                         <FaChevronUp className="text-gray-500" />
//                       ) : (
//                         <FaChevronDown className="text-gray-500" />
//                       )}
//                     </button>
//                     {isStatusDropdownOpen && (
//                       <ul className="absolute bg-white border mt-1 rounded w-full z-10">
//                         <li
//                           className="p-2 hover:bg-blue-100 cursor-pointer"
//                           onClick={() => handleStatusSelect("Verify")}
//                         >
//                           Verify
//                         </li>
//                         <li
//                           className="p-2 hover:bg-blue-100 cursor-pointer"
//                           onClick={() => handleStatusSelect("Not Verified")}
//                         >
//                           Not Verified
//                         </li>
//                       </ul>
//                     )}
//                   </div>
//                 </div>
//               </div>
//               <div className="flex justify-center space-x-3 my-8 mx-28">
//                 <button
//                   type="button"
//                   className="px-6 py-2 border w-[50%] border-gray-300 text-black font-semibold rounded-md"
//                 >
//                   Cancel
//                 </button>
//                 <button
//                   type="submit"
//                   className="px-6 py-2 w-[50%] bg-sky-blue-650 text-white font-semibold rounded-md"
//                 >
//                   Save
//                 </button>
//               </div>
//             </form>
//           )}
//           {activeForm === "roomDetail" && (
//             <div className="bg-white ">
//               <div className="grid grid-cols-2 gap-6">
//                 <div>
//                   <label className="block text-black/40 text-sm font-bold mb-2">
//                     Room Name
//                   </label>
//                   <input
//                     type="text"
//                     placeholder="#banana"
//                     className="bg-[#EEF9FF] appearance-none border  rounded w-full p-2 text-gray-700 leading-tight focus:outline-none focus:bg-white focus:border-blue-500"
//                   />
//                 </div>
//                 <div>
//                   <label className="block text-black/40 text-sm font-bold mb-2">
//                     Room Type
//                   </label>
//                   <input
//                     type="text"
//                     placeholder="Standard room"
//                     className="bg-[#EEF9FF] appearance-none border  rounded w-full p-2 text-gray-700 leading-tight focus:outline-none focus:bg-white focus:border-blue-500"
//                   />
//                 </div>
//                 <div>
//                   <label className="block text-black/40 text-sm font-bold mb-2">
//                     Total beds
//                   </label>
//                   <input
//                     type="text"
//                     placeholder="54"
//                     className="bg-[#EEF9FF] appearance-none border  rounded w-full p-2 text-gray-700 leading-tight focus:outline-none focus:bg-white focus:border-blue-500"
//                   />
//                 </div>
//                 <div>
//                   <label className="block text-black/40 text-sm font-bold mb-2">
//                     Ensuite
//                   </label>
//                   <input
//                     type="text"
//                     placeholder="14"
//                     className="bg-[#EEF9FF] appearance-none border  rounded w-full p-2 text-gray-700 leading-tight focus:outline-none focus:bg-white focus:border-blue-500"
//                   />
//                 </div>
//                 <div>
//                   <label className="block text-black/40 text-sm font-bold mb-2">
//                     Rate
//                   </label>
//                   <input
//                     type="text"
//                     placeholder="45"
//                     className="bg-[#EEF9FF] appearance-none border  rounded w-full p-2 text-gray-700 leading-tight focus:outline-none focus:bg-white focus:border-blue-500"
//                   />
//                 </div>
//                 <div>
//                   <label className="block text-black/40 text-sm font-bold mb-2">
//                     Currency
//                   </label>
//                   <input
//                     type="text"
//                     placeholder="152"
//                     className="bg-[#EEF9FF] appearance-none border  rounded w-full p-2 text-gray-700 leading-tight focus:outline-none focus:bg-white focus:border-blue-500"
//                   />
//                 </div>
//                 <div>
//                   <label className="block text-black/40 text-sm font-bold mb-2">
//                     Room Photo
//                   </label>
//                   <div className="flex items-center">
//                     <img
//                       src="https://via.placeholder.com/150"
//                       alt="Room"
//                       className="w-28 h-16 rounded-xl mr-4"
//                     />
//                     <div className="text-black/90 flex items-center justify-center gap-x-12">
//                       <p>Banana Room.jpg</p>
//                       <p className="text-sm">252 KB</p>
//                     </div>
//                   </div>
//                 </div>
//                 <div>
//                   <label className="block text-black/40 text-sm font-bold mb-2">
//                     Room Description
//                   </label>
//                   <input
//                     type="text"
//                     placeholder="Closed"
//                     className="bg-[#EEF9FF] appearance-none border  rounded w-full p-2 text-gray-700 leading-tight focus:outline-none focus:bg-white focus:border-blue-500"
//                   />
//                 </div>
//                 <div className="col-span-2">
//                   <label className="block text-black/40 text-sm font-bold mb-2">
//                     Rate Type
//                   </label>
//                   <input
//                     type="text"
//                     placeholder="Verified"
//                     className="bg-[#EEF9FF] appearance-none border  rounded w-[49%] p-2 text-gray-700 leading-tight focus:outline-none focus:bg-white focus:border-blue-500"
//                   />
//                 </div>
//               </div>
//               <div className="flex justify-center space-x-3 my-8 mx-28">
//                 <button
//                   type="button"
//                   className="px-6 py-2 border w-[50%]  text-black font-semibold rounded-md"
//                 >
//                   Cancel
//                 </button>
//                 <button
//                   type="submit"
//                   className="px-6 py-2 w-[50%] bg-sky-blue-650 text-white font-semibold rounded-md"
//                 >
//                   Save
//                 </button>
//               </div>
//             </div>
//           )}
//           {activeForm === "rateDetail" && (
//             <div className="bg-white ">
//               <div className="grid grid-cols-2 gap-6">
//                 <div>
//                   <label className="block text-black/40 text-sm font-bold mb-2">
//                     Room
//                   </label>
//                   <input
//                     type="text"
//                     placeholder="88 Backpackers"
//                     className="bg-[#EEF9FF] appearance-none border rounded w-full p-2 text-gray-700 leading-tight focus:outline-none focus:bg-white focus:border-blue-500"
//                   />
//                 </div>
//                 <div>
//                   <label className="block text-black/40 text-sm font-bold mb-2">
//                     Rate Type
//                   </label>
//                   <input
//                     type="text"
//                     placeholder="Dorm"
//                     className="bg-[#EEF9FF] appearance-none border rounded w-full p-2 text-gray-700 leading-tight focus:outline-none focus:bg-white focus:border-blue-500"
//                   />
//                 </div>
//                 <div>
//                   <label className="block text-black/40 text-sm font-bold mb-2">
//                     Rate Value
//                   </label>
//                   <input
//                     type="text"
//                     placeholder="1000"
//                     className="bg-[#EEF9FF] appearance-none border rounded w-full p-2 text-gray-700 leading-tight focus:outline-none focus:bg-white focus:border-blue-500"
//                   />
//                 </div>
//                 <div>
//                   <label className="block text-black/40 text-sm font-bold mb-2">
//                     Percentage
//                   </label>
//                   <input
//                     type="text"
//                     placeholder="10%"
//                     className="bg-[#EEF9FF] appearance-none border rounded w-full p-2 text-gray-700 leading-tight focus:outline-none focus:bg-white focus:border-blue-500"
//                   />
//                 </div>
//               </div>
//               <div className="flex justify-center space-x-3 my-8 mx-28">
//                 <button
//                   type="button"
//                   className="px-6 py-2 border w-[50%] border-gray-300 text-black font-semibold rounded-md"
//                 >
//                   Cancel
//                 </button>
//                 <button
//                   type="submit"
//                   className="px-6 py-2 w-[50%] bg-sky-blue-650 text-white font-semibold rounded-md"
//                 >
//                   Save
//                 </button>
//               </div>
//             </div>
//           )}
//         </div>
//       </div> */}

//       <div className="lg:pl-[255px] md:pl-[180px] sm:pl-[10px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
//         <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
//           Hostel Edit
//         </h2>
//         <div className="w-full h-full mt-5 bg-white rounded p-7">
//           <div className="flex flex-wrap items-center justify-center mx-auto gap-5">
//             <button
//               type="button"
//               className={`relative flex w-[165px] items-center justify-center h-10 px-5 py-2 text-sm font-semibold border rounded ${
//                 activeTab === 0
//                   ? "bg-sky-blue-650 text-white"
//                   : "border-gray-200 text-black-100"
//               }`}
//               onClick={() => handleTab(0)}
//             >
//               Edit Hostel Detail
//             </button>
//             <button
//               type="button"
//               className={`relative flex w-[165px] items-center justify-center h-10 px-5 py-2 text-sm font-semibold border rounded ${
//                 activeTab === 1
//                   ? "bg-sky-blue-650 text-white"
//                   : "border-gray-200 text-black-100"
//               }`}
//               onClick={() => handleTab(1)}
//             >
//               Edit Room Detail
//             </button>
//             <button
//               type="button"
//               className={`relative flex w-[165px] items-center justify-center h-10 px-5 py-2 text-sm font-semibold border rounded ${
//                 activeTab === 2
//                   ? "bg-sky-blue-650 text-white"
//                   : "border-gray-200 text-black-100"
//               }`}
//               onClick={() => handleTab(2)}
//             >
//               Edit Rate Detail
//             </button>
//           </div>

//           <div className="mt-10 w-full md:w-[90%] sm:w-[95%] mx-auto ">
//             {activeTab === 0 && (
//               <>
//                 <div className="flex flex-wrap items-center justify-between w-full gap-y-5 pl-0 md:pl-2 lg:pl-0">
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Hostel Name
//                     </label>
//                     <input
//                       type="text"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                       placeholder="88 Backpackers"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Hostel Address
//                     </label>
//                     <input
//                       type="text"
//                       placeholder="1901 Thornridge Cir. Shiloh, Hawaii 81063"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Rate
//                     </label>
//                     <input
//                       type="text"
//                       placeholder="4.5"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Flour
//                     </label>
//                     <input
//                       type="text"
//                       placeholder="14"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Room
//                     </label>
//                     <input
//                       type="text"
//                       placeholder="45"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Bed
//                     </label>
//                     <input
//                       type="text"
//                       placeholder="152"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 placeholder-black rounded outline-none bg-sky-blue-25 h-9 placeholder:font-semibold"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Country
//                     </label>
//                     <select className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 placeholder-black rounded outline-none bg-sky-blue-25 h-9 placeholder:font-semibold">
//                       <option selected disabled>
//                         India
//                       </option>
//                     </select>
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Offer
//                     </label>
//                     <input
//                       type="text"
//                       placeholder="Closed"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 placeholder-black rounded outline-none bg-sky-blue-25 h-9 placeholder:font-semibold"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Status
//                     </label>
//                     <input
//                       type="text"
//                       placeholder="Verified"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 placeholder-black rounded outline-none bg-sky-blue-25 h-9 placeholder:font-semibold"
//                     />
//                   </div>
//                 </div>

//                 <div className="flex items-center justify-center gap-4 mt-5">
//                   <Link
//                     href={"/superadmin/dashboard/hostel"}
//                     className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-20 py-2 text-sm font-semibold  border  rounded  border-gray-200 text-black-100`}
//                   >
//                     Cancel
//                   </Link>
//                   <button
//                     type="button"
//                     className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-20  py-2 text-sm font-semibold  border  rounded  bg-sky-blue-650 text-white `}
//                   >
//                     Save
//                   </button>
//                 </div>
//               </>
//             )}
//             {activeTab === 1 && (
//               <>
//                 <div className="flex flex-wrap items-center justify-between w-full gap-y-5 pl-0 md:pl-2 lg:pl-0">
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Room Name
//                     </label>
//                     <input
//                       type="text"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                       placeholder="#banana"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Room Type
//                     </label>
//                     <input
//                       type="text"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                       placeholder="Standard room"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Total beds
//                     </label>
//                     <input
//                       type="text"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                       placeholder="54"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Ensuite
//                     </label>
//                     <input
//                       type="text"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                       placeholder="14"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Rate
//                     </label>
//                     <input
//                       type="text"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                       placeholder="45"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Currency
//                     </label>
//                     <input
//                       type="text"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                       placeholder="152"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Room Photo
//                     </label>
//                     <div className="relative w-full">
//                       <input
//                         type="file"
//                         className="relative z-30 w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none opacity-0 bg-sky-blue-25 h-9"
//                       />
//                       <div className="absolute flex items-center justify-between w-full top-2 text-gray-50">
//                         <div className="rounded-lg border-2 border-gray-200">
//                           <Image
//                             src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`}
//                             alt="img"
//                             width={140}
//                             height={50}
//                             title="img"
//                             className="object-contain w-fit h-fit max-w-24 max-h-36 flex items-center rounded-md"
//                           />
//                         </div>
//                         <span className="text-sm font-semibold pl-0 md:pl-4 lg:pl-0">
//                           Banana Room.Jpj
//                         </span>
//                         <span className="text-sm font-semibold">252 KB</span>
//                       </div>
//                     </div>
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Room Description
//                     </label>
//                     <input
//                       type="text"
//                       placeholder="Closed"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Rate Type
//                     </label>
//                     <input
//                       type="text"
//                       placeholder="Verified"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                     />
//                   </div>
//                 </div>

//                 <div className="flex items-center justify-center gap-4 mt-5">
//                   <Link
//                     href={"/superadmin/dashboard/hostel"}
//                     className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-20 py-2 text-sm font-semibold  border  rounded  border-gray-200 text-black-100`}
//                   >
//                     Cancel
//                   </Link>
//                   <button
//                     type="button"
//                     className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-20  py-2 text-sm font-semibold  border  rounded  bg-sky-blue-650 text-white `}
//                   >
//                     Save
//                   </button>
//                 </div>
//               </>
//             )}
//             {activeTab === 2 && (
//               <>
//                 <div className="flex flex-wrap items-center justify-between w-full gap-y-5 md:gap-x-4 pl-0 md:pl-2 lg:pl-0">
//                   <div className="w-full md:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Room
//                     </label>
//                     <input
//                       type="text"
//                       placeholder="88 Backpackers"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                     />
//                   </div>
//                   <div className="w-full md:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Rate Type
//                     </label>
//                     <input
//                       type="text"
//                       placeholder="Dorm"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                     />
//                   </div>
//                   <div className="w-full md:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Rate Value
//                     </label>
//                     <input
//                       type="text"
//                       placeholder="1000"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                     />
//                   </div>
//                   <div className="w-full md:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Percentage
//                     </label>
//                     <input
//                       type="text"
//                       placeholder="10"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                     />
//                   </div>
//                 </div>

//                 <div className="flex items-center justify-center gap-4 mt-5">
//                   <Link
//                     href={"/superadmin/dashboard/hostel"}
//                     className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-20 py-2 text-sm font-semibold  border  rounded  border-gray-200 text-black-100 `}
//                   >
//                     Cancel
//                   </Link>
//                   <button
//                     type="button"
//                     className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-20  py-2 text-sm font-semibold  border  rounded  bg-sky-blue-650 text-white `}
//                   >
//                     Save
//                   </button>
//                 </div>
//               </>
//             )}
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default HostelEditForm;









"use client";
import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";

const EditHostel = () => {
  const [activeTab, setActiveTab] = useState(0);

  const handleTab = (index) => {
    setActiveTab(index);
  };
  return (

    <div className="lg:pl-[255px] md:pl-[180px] sm:pl-[10px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616] h-screen">
      <h2 className="text-2xl lg:text-3xl font-poppins font-bold text-black dark:text-gray-100">
        Edit Hostel
      </h2>
      <div className="w-full h-auto mt-5 bg-white rounded p-7 dark:bg-black">
        <div className="flex flex-wrap items-center justify-center mx-auto gap-5">
          <button
            type="button"
            className={`relative flex w-[165px] items-center justify-center h-10 px-5 py-2 text-sm font-semibold font-poppins border rounded ${
              activeTab === 0
                ? "bg-sky-blue-650 text-white"
                : "border-gray-200 text-black-100 dark:text-[#B6B6B6]"
            }`}
            onClick={() => handleTab(0)}
          >
            Edit Hostel Detail
          </button>
          <button
            type="button"
            className={`relative flex w-[165px] items-center justify-center h-10 px-5 py-2 text-sm font-semibold font-poppins border rounded ${
              activeTab === 1
                ? "bg-sky-blue-650 text-white"
                : "border-gray-200 text-black-100 dark:text-[#B6B6B6]"
            }`}
            onClick={() => handleTab(1)}
          >
            Edit Room Detail
          </button>
          <button
            type="button"
            className={`relative flex w-[165px] items-center justify-center h-10 px-5 py-2 text-sm font-semibold font-poppins border rounded ${
              activeTab === 2
                ? "bg-sky-blue-650 text-white"
                : "border-gray-200 text-black-100 dark:text-[#B6B6B6]"
            }`}
            onClick={() => handleTab(2)}
          >
            Edit Rate Detail
          </button>
        </div>

        <div className="mt-10 w-full md:w-[90%] sm:w-[95%] mx-auto ">
          {activeTab === 0 && (
            <>

              <div className="flex flex-wrap items-center justify-between w-full gap-y-5 pl-0 md:pl-2 lg:pl-0 h-auto">
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Hostel Name
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:text-sm placeholder:font-poppins dark:bg-transparent dark:placeholder:text-[#757575]"
                    placeholder="88 backpckers"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Hostel Address
                  </label>
                  <input
                    type="text"
                    placeholder="xxxyyyzzz"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:text-sm placeholder:font-poppins dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Rate
                  </label>
                  <input
                    type="text"
                    placeholder="4.5"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:text-sm placeholder:font-poppins dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Floor
                  </label>
                  <input
                    type="text"
                    placeholder="first"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:text-sm placeholder:font-poppins dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Room
                  </label>
                  <input
                    type="text"
                    placeholder="265"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:text-sm placeholder:font-poppins dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Bed
                  </label>
                  <input
                    type="text"
                    placeholder="65"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 placeholder-black rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:text-sm placeholder:font-poppins dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Country
                  </label>
                  <select className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 placeholder-black rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:text-sm placeholder:font-poppins dark:bg-transparent dark:placeholder:text-[#757575]">
                  <option className="dark:bg-black dark:text-[#757575]">
                      India
                    </option>
                    <option className="dark:bg-black dark:text-[#757575]">
                      Japan
                    </option>
                  </select>
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Offer
                  </label>
                  <input
                    type="text"
                    placeholder="Offer"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 placeholder-black rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:text-sm placeholder:font-poppins dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Status
                  </label>
                  <input
                    type="text"
                    placeholder="booked"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 placeholder-black rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:text-sm placeholder:font-poppins dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
              </div>

              <div className="flex items-center justify-center gap-4 mt-5">
              <Link href={"/superadmin/dashboard/hostel"}
                  type="button"
                  className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-20 py-2 text-sm font-semibold font-poppins border  rounded  border-gray-200 text-black-100 dark:text-gray-100`}
                  
                >
                  Cancel
                </Link>
                <button
                  type="button"
                  className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-20  py-2 text-sm font-semibold font-poppins border  rounded  bg-sky-blue-650 text-white `}
                >
                  Save
                </button>
              </div>
            </>
          )}
          {activeTab === 1 && (
            <>

              <div className="flex flex-wrap items-center justify-between w-full gap-y-5 pl-0 md:pl-2 lg:pl-0">
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Room Name
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                    placeholder="#banana"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Room Type
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                    placeholder="suite"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Total beds
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                    placeholder="84"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Ensuite
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                    placeholder="Ensuite"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Rate
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                    placeholder="4"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Currency
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                    placeholder="INR"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Room Photo
                  </label>
                  <div className="relative w-full">
                    <input
                      type="file"
                      className="relative z-30 w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none opacity-0 bg-sky-blue-25 h-9 dark:bg-transparent dark:placeholder:text-[#757575]"
                    />
                    <div className="absolute flex items-center justify-between w-full top-2 text-gray-50">
                      <div className="rounded-lg border-2 border-gray-200">
                        <Image
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`}
                          alt="img"
                          width={140}
                          height={50}
                          title="img"
                          className="object-contain w-fit h-fit max-w-24 max-h-36 flex items-center rounded-md"
                        />
                      </div>
                      <span className="text-sm font-semibold pl-0 md:pl-4 lg:pl-0 dark:text-[#757575]">
                        Banana Room.Jpj
                      </span>
                      <span className="text-sm font-semibold dark:text-[#757575]">252 KB</span>
                    </div>
                  </div>
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Room Description
                  </label>
                  <input
                    type="text"
                    placeholder="Description"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Rate Type
                  </label>
                  <input
                    type="text"
                    placeholder="Type"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
              </div>

              <div className="flex items-center justify-center gap-4 mt-5 ">
              <Link href={"/superadmin/dashboard/hostel"}
                  type="button"
                  className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-20 py-2 text-sm font-semibold font-poppins border  rounded  border-gray-200 text-black-100 dark:text-gray-100`}
                  
                >
                  Cancel
                </Link>
                <button
                  type="button"
                  className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-20  py-2 text-sm font-semibold font-poppins border  rounded  bg-sky-blue-650 text-white `}
                >
                  Save
                </button>
              </div>
            </>
          )}
          {activeTab === 2 && (
            <>
              <div className="flex flex-wrap items-center justify-between w-full gap-y-5 md:gap-x-4 pl-0 md:pl-2 lg:pl-0">
                <div className="w-full md:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Room
                  </label>
                  <input
                    type="text"
                    placeholder="88 Backpackers"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
                <div className="w-full md:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Rate Type
                  </label>
                  <input
                    type="text"
                    placeholder="Dorm"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
                <div className="w-full md:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Rate Value
                  </label>
                  <input
                    type="text"
                    placeholder="1000"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
                <div className="w-full md:w-[48%]">
                  <label className="text-sm font-medium text-gray-500 dark:text-[#B6B6B6]">
                    Percentage
                  </label>
                  <input
                    type="text"
                    placeholder="10"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
              </div>

              <div className="flex items-center justify-center gap-4 mt-5">
                <Link href={"/superadmin/dashboard/hostel"}
                  type="button"
                  className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-20 py-2 text-sm font-semibold font-poppins border  rounded  border-gray-200 text-black-100 dark:text-gray-100`}
                  
                >
                  Cancel
                </Link>
                <button
                  type="button"
                  className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-20  py-2 text-sm font-semibold font-poppins border  rounded  bg-sky-blue-650 text-white `}
                >
                  Save
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default EditHostel;
