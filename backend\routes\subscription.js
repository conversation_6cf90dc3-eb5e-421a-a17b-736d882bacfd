import express from 'express';
import { createPlan,fetchPlanById,fetchAllPlans,createSubscription,getActiveSubscription,pauseSubscription,resumeSubscription,cancelSubscription,getSubscriptionHistory} from '../controller/subscription.js';


const router = express.Router();

// router.post('/createPlan', createPlan);
// router.get('/:id',fetchPlanById);
// router.get('/all-plans',fetchAllPlans);
// router.post('/createSubscription',createSubscription);
// router.get('/:user_id/active',getActiveSubscription);
// router.post('/pause',pauseSubscription);
// router.post('/resume',resumeSubscription);
// router.post('/cancel',cancelSubscription);
// router.get('/', getSubscriptionHistory);


export default router;

/**
 * @swagger
 * tags:
 *   name: Subscription
 *   description: Subscription endpoints
 */

/**
 * @swagger
 * /subscription/createPlan:
 *   post:
 *     summary: Create a new subscription plan
 *     tags: [Subscription]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Name of the subscription plan
 *                 example: Gold Membership
 *               description:
 *                 type: string
 *                 description: Description of the subscription plan
 *                 example: Access to exclusive content
 *               amount:
 *                 type: number
 *                 description: Amount in the smallest currency unit (e.g., paise)
 *                 example: 150000
 *               currency:
 *                 type: string
 *                 description: Currency for the payment (default is INR)
 *                 example: INR
 *               period:
 *                 type: string
 *                 description: Billing cycle (e.g., daily, weekly, monthly, yearly)
 *                 example: monthly
 *               interval:
 *                 type: number
 *                 description: Interval count (e.g., every 1 month)
 *                 example: 1
 *             required:
 *               - name
 *               - amount
 *               - period
 *               - interval
 *     responses:
 *       '201':
 *         description: Plan created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 razorpayPlan:
 *                   type: object
 *                   description: Details of the plan created in Razorpay
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: Razorpay plan ID
 *                       example: plan_9A33XWu170gUtm
 *                     entity:
 *                       type: string
 *                       example: plan
 *                     period:
 *                       type: string
 *                       example: monthly
 *                     interval:
 *                       type: number
 *                       example: 1
 *                     item:
 *                       type: object
 *                       description: Plan item details
 *                       properties:
 *                         name:
 *                           type: string
 *                           example: Premium Plan
 *                         description:
 *                           type: string
 *                           example: Access to premium features
 *                         amount:
 *                           type: number
 *                           example: 100000
 *                         currency:
 *                           type: string
 *                           example: INR
 *                 savedPlan:
 *                   type: object
 *                   description: Details of the plan saved in the database
 *                   properties:
 *                     name:
 *                       type: string
 *                       example: Gold Membership
 *                     description:
 *                       type: string
 *                       example: Access to exclusive content
 *                     amount:
 *                       type: number
 *                       example: 150000
 *                     currency:
 *                       type: string
 *                       example: INR
 *                     period:
 *                       type: string
 *                       example: monthly
 *                     interval:
 *                       type: number
 *                       example: 1
 *                     created_at:
 *                       type: string
 *                       format: date-time
 *                       example: 2024-12-06T10:00:00Z
 *       '400':
 *         description: Bad request - Missing or invalid input
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Missing required fields: name, amount, interval, or period"
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */

/**
 * @swagger
 * /subscription/{id}:
 *   get:
 *     summary: Fetch a subscription plan by its ID
 *     tags: [Subscription]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: The unique ID of the subscription plan
 *     responses:
 *       '200':
 *         description: Plan fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Plan fetched successfully
 *                 plan:
 *                   type: object
 *                   $ref: '#/components/schemas/Plan'
 *       '400':
 *         description: Bad request - Missing or invalid input
 *         content:
 *           application/json:
 *             example:
 *               error: "Plan ID is required"
 *       '404':
 *         description: Plan not found
 *         content:
 *           application/json:
 *             example:
 *               error: "Plan not found"
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               error: "Internal server error"
 */

/**
 * @swagger
 * /subscription/all-plans:
 *   get:
 *     summary: Fetch all subscription plans
 *     tags: [Subscription]
 *     responses:
 *       '200':
 *         description: Plans fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Plans fetched successfully
 *                 plans:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Plan'
 *       '404':
 *         description: No plans found
 *         content:
 *           application/json:
 *             example:
 *               message: "No plans found"
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               error: "Internal server error"
 */

/**
 * @swagger
 * /subscription/createSubscription:
 *   post:
 *     summary: Create a new subscription
 *     tags: [Subscription]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               plan_id:
 *                 type: string
 *                 description: ID of the plan to subscribe to
 *                 example: plan_9A33XWu170gUtm
 *               user_id:
 *                 type: string
 *                 description: ID of the user subscribing (linked to the User model)
 *                 example: 60c72b2f9f1b2c001f8b1e44
 *               total_count:
 *                 type: number
 *                 description: Total number of billing cycles for the subscription
 *                 example: 12
 *               customer_notify:
 *                 type: boolean
 *                 description: Whether to notify the customer via email/SMS
 *                 example: true
 *               start_at:
 *                 type: number
 *                 description: Unix timestamp for when the subscription should start (default is 1 hour from now)
 *                 example: 1707158400
 *               addons:
 *                 type: array
 *                 description: Optional addons for the subscription
 *                 items:
 *                   type: object
 *                   properties:
 *                     item:
 *                       type: object
 *                       properties:
 *                         name:
 *                           type: string
 *                           description: Name of the addon
 *                           example: Setup Fee
 *                         amount:
 *                           type: number
 *                           description: Amount for the addon in the smallest currency unit (e.g., paise)
 *                           example: 20000
 *                         currency:
 *                           type: string
 *                           description: Currency for the addon
 *                           example: INR
 *             required:
 *               - plan_id
 *               - user_id
 *               - total_count
 *     responses:
 *       '201':
 *         description: Subscription created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Subscription created successfully
 *                 subscription_id:
 *                   type: string
 *                   description: ID of the created subscription in Razorpay
 *                   example: sub_9A34XWu170gUtm
 *                 subscription_status:
 *                   type: string
 *                   description: Current status of the subscription
 *                   example: active
 *                 savedSubscription:
 *                   type: object
 *                   description: Details of the saved subscription in the database
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: Razorpay subscription ID
 *                       example: sub_9A34XWu170gUtm
 *                     plan_id:
 *                       type: string
 *                       example: plan_9A33XWu170gUtm
 *                     user_id:
 *                       type: string
 *                       description: ID of the user who owns the subscription
 *                       example: 60c72b2f9f1b2c001f8b1e44
 *                     customer_id:
 *                       type: string
 *                       example: cust_123456
 *                     total_count:
 *                       type: number
 *                       example: 12
 *                     remaining_count:
 *                       type: number
 *                       example: 11
 *                     customer_notify:
 *                       type: boolean
 *                       example: true
 *                     start_at:
 *                       type: string
 *                       format: date-time
 *                       example: 2024-12-07T10:00:00Z
 *                     end_at:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-12-07T10:00:00Z
 *                     addons:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           item:
 *                             type: object
 *                             properties:
 *                               name:
 *                                 type: string
 *                                 example: Setup Fee
 *                               amount:
 *                                 type: number
 *                                 example: 20000
 *                               currency:
 *                                 type: string
 *                                 example: INR
 *                     status:
 *                       type: string
 *                       example: active
 *                     current_start:
 *                       type: string
 *                       format: date-time
 *                       example: 2024-12-07T10:00:00Z
 *                     current_end:
 *                       type: string
 *                       format: date-time
 *                       example: 2025-01-07T10:00:00Z
 *                     created_at:
 *                       type: string
 *                       format: date-time
 *                       example: 2024-12-06T10:00:00Z
 *                     updated_at:
 *                       type: string
 *                       format: date-time
 *                       example: 2024-12-06T11:00:00Z
 *       '400':
 *         description: Bad request - Missing or invalid input
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Plan ID, User ID, and Total Count are required."
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Failed to create subscription."
 *                 details:
 *                   type: string
 *                   example: "Internal server error"
 */

/**
 * @swagger
 * /subscription/{user_id}/active:
 *   get:
 *     summary: Retrieve the active subscription of a user
 *     tags: [Subscription]
 *     description: This endpoint fetches the active subscription details of a user based on the user_id.
 *     parameters:
 *       - in: path
 *         name: user_id
 *         required: true
 *         description: The ID of the user whose active subscription is to be fetched.
 *         schema:
 *           type: string
 *     responses:
 *       '200':
 *         description: Active subscription fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Active subscription fetched successfully.
 *                 subscription:
 *                   type: object
 *                   properties:
 *                     plan:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         name:
 *                           type: string
 *                         description:
 *                           type: string
 *                         price:
 *                           type: number
 *                           format: float
 *                     total_count:
 *                       type: integer
 *                       example: 100
 *                     remaining_count:
 *                       type: integer
 *                       example: 50
 *                     status:
 *                       type: string
 *                       example: active
 *                     start_at:
 *                       type: string
 *                       format: date-time
 *                       example: "2024-12-09T00:00:00Z"
 *                     end_at:
 *                       type: string
 *                       format: date-time
 *                       example: "2025-12-09T00:00:00Z"
 *                     current_start:
 *                       type: string
 *                       format: date-time
 *                       example: "2024-12-09T00:00:00Z"
 *                     current_end:
 *                       type: string
 *                       format: date-time
 *                       example: "2024-12-09T01:00:00Z"
 *       '404':
 *         description: User not found or no active subscription found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: User not found.
 *                 message:
 *                   type: string
 *                   example: No active subscription found.
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Failed to fetch active subscription.
 *                 details:
 *                   type: string
 *                   example: No additional details provided.
 */

/**
 * @swagger
 * /subscription/pause:
 *   post:
 *     summary: Pause a subscription
 *     tags: [Subscription]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               subscriptionId:
 *                 type: string
 *                 description: The ID of the subscription to pause
 *                 example: sub_xxx
 *             required:
 *               - subscriptionId
 *     responses:
 *       '200':
 *         description: Subscription paused successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Subscription paused successfully.
 *                 razorpayResponse:
 *                   type: object
 *                   description: Details of the pause response from Razorpay
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: Razorpay subscription ID
 *                       example: sub_xxx
 *                     status:
 *                       type: string
 *                       description: Updated status of the subscription
 *                       example: paused
 *                 updatedSubscription:
 *                   type: object
 *                   description: Details of the updated subscription in the database
 *                   properties:
 *                     _id:
 *                       type: string
 *                       example: 64f9b5b5b1d7e81234567890
 *                     user_id:
 *                       type: string
 *                       example: 64f9b5b5b1d7e81234567891
 *                     plan_id:
 *                       type: string
 *                       example: plan_xxx
 *                     status:
 *                       type: string
 *                       example: inactive
 *                     start_at:
 *                       type: string
 *                       format: date-time
 *                       example: 2023-10-01T00:00:00.000Z
 *                     end_at:
 *                       type: string
 *                       format: date-time
 *                       example: 2024-10-01T00:00:00.000Z
 *                     updated_at:
 *                       type: string
 *                       format: date-time
 *                       example: 2024-12-10T12:00:00Z
 *       '400':
 *         description: Bad request - Missing or invalid input
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Subscription ID is required."
 *       '404':
 *         description: Subscription not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Subscription not found."
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Failed to pause subscription."
 */

/**
 * @swagger
 * /subscription/resume:
 *   post:
 *     summary: Resume a paused subscription
 *     tags: [Subscription]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               subscriptionId:
 *                 type: string
 *                 description: The ID of the subscription to resume
 *                 example: sub_xxx
 *             required:
 *               - subscriptionId
 *     responses:
 *       '200':
 *         description: Subscription resumed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Subscription resumed successfully.
 *                 razorpayResponse:
 *                   type: object
 *                   description: Details of the resume response from Razorpay
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: Razorpay subscription ID
 *                       example: sub_xxx
 *                     status:
 *                       type: string
 *                       description: Updated status of the subscription
 *                       example: active
 *                 updatedSubscription:
 *                   type: object
 *                   description: Details of the updated subscription in the database
 *                   properties:
 *                     _id:
 *                       type: string
 *                       example: 64f9b5b5b1d7e81234567890
 *                     user_id:
 *                       type: string
 *                       example: 64f9b5b5b1d7e81234567891
 *                     plan_id:
 *                       type: string
 *                       example: plan_xxx
 *                     status:
 *                       type: string
 *                       example: active
 *                     start_at:
 *                       type: string
 *                       format: date-time
 *                       example: 2023-10-01T00:00:00.000Z
 *                     end_at:
 *                       type: string
 *                       format: date-time
 *                       example: 2024-10-01T00:00:00.000Z
 *                     updated_at:
 *                       type: string
 *                       format: date-time
 *                       example: 2024-12-10T12:00:00Z
 *       '400':
 *         description: Bad request - Missing or invalid input
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Only inactive subscriptions can be resumed."
 *       '404':
 *         description: Subscription not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Subscription not found."
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Failed to resume subscription."
 */


/**
 * @swagger
 * /subscription/cancel:
 *   post:
 *     summary: Cancel a subscription
 *     tags: [Subscription]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               subscriptionId:
 *                 type: string
 *                 description: The ID of the subscription to cancel
 *                 example: sub_xxx
 *               cancel_at:
 *                 type: string
 *                 description: When to cancel the subscription ("now" or "end_billing_cycle")
 *                 example: now
 *             required:
 *               - subscriptionId
 *     responses:
 *       '200':
 *         description: Subscription canceled successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Subscription canceled successfully.
 *                 razorpayResponse:
 *                   type: object
 *                   description: Details of the cancellation from Razorpay
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: Razorpay subscription ID
 *                       example: sub_xxx
 *                     status:
 *                       type: string
 *                       description: Updated status of the subscription
 *                       example: canceled
 *                 updatedSubscription:
 *                   type: object
 *                   description: Details of the updated subscription in the database
 *                   properties:
 *                     _id:
 *                       type: string
 *                       example: 64f9b5b5b1d7e81234567890
 *                     user_id:
 *                       type: string
 *                       example: 64f9b5b5b1d7e81234567891
 *                     plan_id:
 *                       type: string
 *                       example: plan_xxx
 *                     status:
 *                       type: string
 *                       example: canceled
 *                     start_at:
 *                       type: string
 *                       format: date-time
 *                       example: 2023-10-01T00:00:00.000Z
 *                     end_at:
 *                       type: string
 *                       format: date-time
 *                       example: 2024-10-01T00:00:00.000Z
 *                     updated_at:
 *                       type: string
 *                       format: date-time
 *                       example: 2024-12-10T12:00:00Z
 *       '400':
 *         description: Bad request - Missing or invalid input
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Subscription ID is required."
 *       '404':
 *         description: Subscription not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Subscription not found."
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Failed to cancel subscription."
 */
/**
 * @swagger
 * /subscription:
 *   get:
 *     summary: Get subscription history with optional filters
 *     tags: [Subscription]
 *     parameters:
 *       - in: query
 *         name: startDate
 *         required: false
 *         schema:
 *           type: string
 *           format: date
 *         description: |
 *           Start date for filtering subscriptions (format: YYYY-MM-DD)
 *       - in: query
 *         name: endDate
 *         required: false
 *         schema:
 *           type: string
 *           format: date
 *         description: |
 *           End date for filtering subscriptions (format: YYYY-MM-DD)
 *       - in: query
 *         name: userId
 *         required: false
 *         schema:
 *           type: string
 *         description: |
 *           User ID to filter subscriptions by user
 *       - in: query
 *         name: status
 *         required: false
 *         schema:
 *           type: string
 *         description: |
 *           Status of the subscription (e.g., active, canceled)
 *       - in: query
 *         name: page
 *         required: false
 *         schema:
 *           type: integer
 *           default: 1
 *         description: |
 *           Page number for pagination
 *       - in: query
 *         name: limit
 *         required: false
 *         schema:
 *           type: integer
 *           default: 10
 *         description: |
 *           Number of items per page for pagination
 *       - in: query
 *         name: sortField
 *         required: false
 *         schema:
 *           type: string
 *           default: created_at
 *         description: |
 *           Field to sort the results by (e.g., created_at, status)
 *       - in: query
 *         name: sortOrder
 *         required: false
 *         schema:
 *           type: string
 *           default: desc
 *         description: |
 *           Sort order (asc or desc)
 *     responses:
 *       '200':
 *         description: Subscription history fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Subscription history fetched successfully.
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       user_id:
 *                         type: string
 *                         description: User ID
 *                         example: 12345
 *                       plan_id:
 *                         type: string
 *                         description: Plan ID
 *                         example: premium_plan
 *                       customer_id:
 *                         type: string
 *                         description: Customer ID
 *                         example: cust_12345
 *                       razorpay_subscription_id:
 *                         type: string
 *                         description: Razorpay subscription ID
 *                         example: sub_9A33XWu170gUtm
 *                       total_count:
 *                         type: integer
 *                         description: Total count of the subscription
 *                         example: 10
 *                       remaining_count:
 *                         type: integer
 *                         description: Remaining count for the subscription
 *                         example: 5
 *                       status:
 *                         type: string
 *                         description: Status of the subscription (active, canceled)
 *                         example: active
 *                       addons:
 *                         type: array
 *                         items:
 *                           type: string
 *                         description: Addons associated with the subscription
 *                         example: ["addon_1", "addon_2"]
 *                       start_at:
 *                         type: string
 *                         format: date-time
 *                         description: Subscription start date
 *                         example: 2024-12-01T00:00:00Z
 *                       end_at:
 *                         type: string
 *                         format: date-time
 *                         description: Subscription end date
 *                         example: 2025-12-01T00:00:00Z
 *                       current_start:
 *                         type: string
 *                         format: date-time
 *                         description: Current billing cycle start date
 *                         example: 2024-12-01T00:00:00Z
 *                       current_end:
 *                         type: string
 *                         format: date-time
 *                         description: Current billing cycle end date
 *                         example: 2024-12-31T23:59:59Z
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                         description: Subscription creation date
 *                         example: 2024-12-01T10:00:00Z
 *                       updated_at:
 *                         type: string
 *                         format: date-time
 *                         description: Subscription last update date
 *                         example: 2024-12-05T10:00:00Z
 *                 meta:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       description: Total number of subscriptions matching the query
 *                       example: 50
 *                     page:
 *                       type: integer
 *                       description: Current page number
 *                       example: 1
 *                     limit:
 *                       type: integer
 *                       description: Number of items per page
 *                       example: 10
 *                     pages:
 *                       type: integer
 *                       description: Total number of pages
 *                       example: 5
 *       '400':
 *         description: Bad request - Invalid query parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid date format or pagination values."
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Internal server error"
 */

