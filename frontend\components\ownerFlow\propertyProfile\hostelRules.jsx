import React, { useEffect, useState, Fragment } from "react";
import Switch from "@mui/material/Switch";
import { editPropertyApi } from "@/services/ownerflowServices";
import toast from "react-hot-toast";
import Loader from "@/components/loader/loader";
import Image from "next/image";
import { Dialog, Transition } from "@headlessui/react";

const HostelRules = ({ id, data, updatePropertyData }) => {
  const [rules, setRules] = useState({
    checkIn: "",
    checkOut: "",
    cancellation: "",
    childPolicy: "",
    ageRestriction: "",
    pets: false,
  });
  const [editRules, setEditRules] = useState(false);
  const [addRules, setAddRules] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [openAddhostel, setopenAddhostel] = useState(false);
  const closehostelModal = () => setopenAddhostel(false);
  const [openEdithostel, setopenEdithostel] = useState(false);
  const closehosteleditModal = () => setopenEdithostel(false);

  const [placeholder, setPlaceholder] = useState("Enter rules title");
  const [showDropdown, setShowDropdown] = useState(false);
  const [extraInputs, setExtraInputs] = useState([]); // Track extra input fields

  const options = [
    "Check In",
    "Check Out",
    "Cancellation/prepayment",
    "Children and beds",
    "Age restriction",
    "Pets",
  ];

  // Function to add another input field
  const addExtraInput = () => {
    setExtraInputs([...extraInputs, ""]); // Add an empty input field
  };

  console.log("editRules", editRules);
  useEffect(() => {
    if (editRules && data) {
      setRules({
        checkIn: data?.rules?.checkIn || "",
        checkOut: data?.rules?.checkOut || "",
        cancellation: data?.rules?.cancellation || "",
        childPolicy: data?.rules?.childPolicy || "",
        ageRestriction: data?.rules?.ageRestriction || "",
        pets: data?.rules?.pets || false,
      });
    } else {
      setRules({
        checkIn: "",
        checkOut: "",
        cancellation: "",
        childPolicy: "",
        ageRestriction: "",
        pets: false,
      });
    }
  }, [editRules, data]);

  // const handleInputChange = (e) => {
  //   const { name, value, type, checked } = e.target;
  //   setRules((prev) => ({
  //     ...prev,
  //     [name]: type === "checkbox" ? checked : value,
  //   }));
  // };

  const validateForm = (data) => {
    return (
      data.checkIn &&
      data.checkOut &&
      data.cancellation &&
      data.childPolicy &&
      data.ageRestriction &&
      typeof data.pets === "boolean"
    );
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm(rules)) {
      toast.error("All fields are required.");
      return;
    }

    setIsLoading(true);
    try {
      const payload = {
        rules: {
          checkIn: rules.checkIn,
          checkOut: rules.checkOut,
          cancellation: rules.cancellation,
          childPolicy: rules.childPolicy,
          ageRestriction: rules.ageRestriction,
          pets: rules.pets,
        },
      };

      if (id) {
        const response = await editPropertyApi(id, payload);
        if (response?.data?.status) {
          toast.success(
            response?.data?.message || "Property updated successfully!"
          );
          setRules({
            checkIn: "",
            checkOut: "",
            cancellation: "",
            childPolicy: "",
            ageRestriction: "",
            pets: false,
          });
          setAddRules(false);
          setEditRules(false);
          updatePropertyData();
        }
      } else {
        toast.error("No property data available to update.");
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Failed to submit property details.");
    } finally {
      setIsLoading(false);
    }
  };

  // console.log("rule", rules);

  const [ruless, setRuless] = useState([
    { title: "", details: "" }, // Initial field set
  ]);

  const handleAddMore = () => {
    setRuless([...ruless, { title: "", details: "" }]);
  };

  const handleInputChange = (index, field, value) => {
    const updatedRules = [...ruless];
    updatedRules[index][field] = value;
    setRules(updatedRules);
  };

  // const [areas, setAreas] = useState([
  //     { areaName: "", file: null } // Initial field
  //   ]);

  //   const handleAddMore = () => {
  //     setAreas([...areas, { areaName: "", file: null }]);
  //   };

  //   const handleFileChange = (e, index) => {
  //     const file = e.target.files[0];
  //     const updatedAreas = [...areas];
  //     updatedAreas[index].file = file;
  //     setAreas(updatedAreas);
  //   };

  //   const handleRemove = (index) => {
  //     const updatedAreas = areas.filter((_, i) => i !== index);
  //     setAreas(updatedAreas);
  //   };

  return (
    <div>
      <Loader open={isLoading} />
      {!addRules && !editRules && (
        <>
          <div className="flex justify-between items-center mb-2">
            <h2 className="sm:text-xl text-base font-medium">Hostel Rules</h2>
            <div className="flex sm:gap-2 gap-1">
              <button
                // onClick={() => setAddRules(true)}
                onClick={() => setopenAddhostel(true)}
                className="sm:flex block items-center text-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-primary-blue font-medium"
              >
                Add Rules
              </button>
              <button
                // onClick={() => setEditRules(true)}
                onClick={() => setopenEdithostel(true)}
                className="flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-primary-blue font-medium"
              >
                Edit Rules
              </button>
            </div>
          </div>
          {/* <div className="w-full border rounded-lg">
            {rules?.map((rule, index) => (
              <div key={index} className="flex items-start p-4 border-b">
                <div className="flex-1">
                  <h3 className="font-medium font-Poppins text-lg text-[#383E49] mb-1">
                    {rule.title}
                  </h3>
                  <p className="text-sm text-[#605959]">{rule.content}</p>
                </div>
              </div>
            ))}
          </div> */}
        </>
      )}

      <div>
        <div className="flex items-start sm:gap-24 gap-8 border border-[#EEEEEE] first:rounded-t-lg sm:p-5 p-3">
          <p className="flex items-center lg:min-w-[22%] lg:w-[22%] sm:min-w-[30%] sm:w-[30%] min-w-[30%] w-[30%] gap-2.5 mb-0 font-medium sm:text-xl text-sm">
            <Image src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/checkin-b.svg`} width={22} height={22} />
            <span>Check In</span>
          </p>
          <p className="font-bold sm:text-lg text-sm">14:00 - 23:00</p>
        </div>
        <div className="flex items-start sm:gap-24 gap-8 border border-[#EEEEEE] first:rounded-t-lg sm:p-5 p-3 border-t-0">
          <p className="flex items-center lg:min-w-[22%] lg:w-[22%] sm:min-w-[30%] sm:w-[30%] min-w-[30%] w-[30%] gap-2.5 mb-0 font-medium sm:text-xl text-sm">
            <Image src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/checkout-b.svg`} width={22} height={22} />
            <span>Check Out</span>
          </p>
          <p className="font-bold sm:text-lg text-sm">14:00 - 23:00</p>
        </div>
        <div className="flex items-start sm:gap-24 gap-8 border border-[#EEEEEE] first:rounded-t-lg sm:p-5 p-3 border-t-0">
          <p className="flex items-center lg:min-w-[22%] lg:w-[22%] sm:min-w-[30%] sm:w-[30%] min-w-[30%] w-[30%] gap-2.5 mb-0 font-medium sm:text-xl text-sm">
            <Image src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/info-b.svg`} width={22} height={22} />
            <span>Cancellation/ prepayment</span>
          </p>
          <p className="font-normal sm:text-lg text-sm">
            Cancellation and prepayment policies vary according to accommodation
            type. Please enter the dates of your stay and check the conditions
            of your required room.
          </p>
        </div>
        <div className="flex items-start sm:gap-24 gap-8 border border-[#EEEEEE] first:rounded-t-lg sm:p-5 p-3 border-t-0">
          <p className="flex items-center lg:min-w-[22%] lg:w-[22%] sm:min-w-[30%] sm:w-[30%] min-w-[30%] w-[30%] gap-2.5 mb-0 font-medium sm:text-xl text-sm">
            <Image src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/child-b.svg`} width={22} height={22} />
            <span>Children and beds</span>
          </p>
          <p className="font-normal sm:text-lg text-sm">
            <p className="font-semibold sm:text-lg text-sm">Child Policies</p>
            <p className="font-normal sm:text-lg text-sm">
              Children are not allowed.
            </p>
            <p className="font-semibold sm:text-lg text-sm">
              Cot and extra bed policies
            </p>
            <p className="font-normal sm:text-lg text-sm">
              Cots and extra beds are not available at this property.
            </p>
          </p>
        </div>
        <div className="flex items-start sm:gap-24 gap-8 border border-[#EEEEEE] first:rounded-t-lg sm:p-5 p-3 border-t-0">
          <p className="flex items-center lg:min-w-[22%] lg:w-[22%] sm:min-w-[30%] sm:w-[30%] min-w-[30%] w-[30%] gap-2.5 mb-0 font-medium sm:text-xl text-sm">
            <Image src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/age-b.svg`} width={22} height={22} />
            <span>Age restriction</span>
          </p>
          <p className="font-normal sm:text-lg text-sm">
            Check- in is only possible for guests age between 18 and 45
          </p>
        </div>
        <div className="flex items-start sm:gap-24 gap-8 border border-[#EEEEEE] first:rounded-t-lg sm:p-5 p-3 border-t-0">
          <p className="flex items-center lg:min-w-[22%] lg:w-[22%] sm:min-w-[30%] sm:w-[30%] min-w-[30%] w-[30%] gap-2.5 mb-0 font-medium sm:text-xl text-sm">
            <Image src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/pets-b.svg`} width={22} height={22} />
            <span>Pets</span>
          </p>
          <p className="font-normal sm:text-lg text-sm">
            Pets are not allowed.
          </p>
        </div>
      </div>

      <Transition show={openAddhostel} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closehostelModal}>
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <h2 className="page-title">Add Hostel Rules</h2>
                    <button
                      onClick={closehostelModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <div>
                      <div>
                        {ruless.map((rule, index) => (
                          <div
                            key={index}
                            className="py-4 first:border-t-0 border-t"
                          >
                            {/* Area Name */}
                            <div>
                              <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                                Rules Title
                              </label>
                              <div className="relative w-full">
                                <input
                                  type="text"
                                  name={`title-${index}`}
                                  className={`w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500`}
                                  placeholder={placeholder || "Enter rules"} // Default placeholder
                                  onFocus={() => setShowDropdown(true)} // Show dropdown when input is clicked
                                  onBlur={() =>
                                    setTimeout(
                                      () => setShowDropdown(false),
                                      200
                                    )
                                  } // Hide dropdown after selection
                                  onChange={(e) =>
                                    setPlaceholder(e.target.value)
                                  }
                                />

                                {showDropdown && (
                                  <div className="absolute w-full mt-1 bg-white border border-black/50 rounded-lg shadow-lg z-10">
                                    {options.map((option, i) => (
                                      <div
                                        key={i}
                                        className="px-4 py-2 text-black text-sm cursor-pointer hover:bg-gray-200"
                                        onMouseDown={() =>
                                          setPlaceholder(option)
                                        } // Update placeholder when clicked
                                      >
                                        {option}
                                      </div>
                                    ))}
                                  </div>
                                )}
                                {/* Extra input field when "Children and beds" is selected */}
                                {/* {placeholder === "Children and beds" && (
                                  <input
                                    type="text"
                                    name={`extra-${index}`}
                                    className="w-full mt-2 sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 sm:text-sm text-xs text-black"
                                    placeholder="Enter details about children and beds"
                                  />
                                )} */}

                                {/* Dynamically added input fields */}
                                {/* {extraInputs.map((_, i) => (
                                  <input
                                    key={i}
                                    type="text"
                                    className="w-full mt-2 sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 sm:text-sm text-xs text-black"
                                    placeholder={`Additional detail ${i + 1}`}
                                  />
                                ))} */}

                              

                                {placeholder === "Children and beds" && (
                                  <div className="mt-2">
                                    <input
                                      type="text"
                                      name={`extra-${index}`}
                                      className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 sm:text-sm text-xs text-black"
                                      placeholder="Enter details about children and beds"
                                    />

                                    {/* Additional input fields */}
                                    {extraInputs.map((_, i) => (
                                      <input
                                        key={i}
                                        type="text"
                                        name={`extra-${index}-${i}`}
                                        className="w-full mt-2 sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 sm:text-sm text-xs text-black"
                                        placeholder="Enter more details"
                                      />
                                    ))}

                                    {/* Add button (Only shown when "Children and beds" is selected) */}
                                    <div className="flex items-center justify-end w-full mt-4 gap-4 col-span-2 bg-white/80 bottom-0 backdrop-blur-sm">
                                  <button
                                    type="button"
                                    onClick={addExtraInput}
                                    className="bg-[#40E0D0] hover:bg-transparent text-black hover:text-[#40E0D0] border-2 font-medium py-2 px-6 border-[#40E0D0] rounded-lg text-sm "
                                  >
                                    Add More
                                  </button>
                                </div>
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Add Photo */}
                            <div className="relative mt-3">
                              <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                                Rules Details
                              </label>
                              <textarea
                                name={`details-${index}`}
                                className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                                placeholder="Enter rules details"
                                rows={4}
                                value={rule.details}
                                onChange={(e) =>
                                  handleInputChange(
                                    index,
                                    "details",
                                    e.target.value
                                  )
                                }
                              ></textarea>
                            </div>
                          </div>
                        ))}

                        {/* Add More Button */}
                        <div className="flex items-center justify-end w-full mt-4 gap-4 col-span-2 bg-white/80 bottom-0 backdrop-blur-sm">
                          <button
                            type="button"
                            onClick={handleAddMore}
                            className="bg-[#40E0D0] hover:bg-transparent text-black hover:text-[#40E0D0] border-2 font-medium py-2 px-6 border-[#40E0D0] rounded-lg text-sm sm:w-[152px] w-full"
                          >
                            Add More
                          </button>
                        </div>
                      </div>
                      <div className="xs:flex block  items-center justify-between w-full sm:my-14 my-7 gap-4 col-span-2 py-4 bg-white/80 sticky bottom-0 backdrop-blur-sm">
                        <button
                          type="button"
                          className="hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 px-4 border-black rounded-lg w-full text-sm xs:mb-0 mb-2"
                          onClick={closehostelModal}
                        >
                          Cancel
                        </button>
                        <button
                          type="submit"
                          className="bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm"
                        >
                          Add
                        </button>
                      </div>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      <Transition show={openEdithostel} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-50"
          onClose={closehosteleditModal}
        >
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <h2 className="page-title">Edit Hostels Rules</h2>
                    <button
                      onClick={closehosteleditModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <div>
                      <div>
                        {ruless.map((rule, index) => (
                          <div
                            key={index}
                            className="py-4 first:border-t-0 border-t"
                          >
                            {/* Area Name */}
                            <div>
                              <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                                Rules Title
                              </label>
                              <input
                                type="text"
                                name={`title-${index}`}
                                className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                                placeholder="Enter rules title"
                                style={{ outline: "none" }}
                              />
                            </div>

                            {/* Add Photo */}
                            <div className="relative mt-3">
                              <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                                Rules Details
                              </label>
                              <textarea
                                name={`details-${index}`}
                                className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                                placeholder="Enter rules details"
                                rows={4}
                                value={rule.details}
                                onChange={(e) =>
                                  handleInputChange(
                                    index,
                                    "details",
                                    e.target.value
                                  )
                                }
                              ></textarea>
                            </div>
                          </div>
                        ))}

                        {/* Add More Button */}
                        <div className="flex items-center justify-end w-full mt-4 gap-4 col-span-2 bg-white/80 bottom-0 backdrop-blur-sm">
                          <button
                            type="button"
                            onClick={handleAddMore}
                            className="bg-[#40E0D0] hover:bg-transparent text-black hover:text-[#40E0D0] border-2 font-medium py-2 px-6 border-[#40E0D0] rounded-lg text-sm sm:w-[152px] w-full"
                          >
                            Add More
                          </button>
                        </div>
                      </div>
                      <div className="xs:flex block  items-center justify-between w-full sm:my-14 my-7 gap-4 col-span-2 py-4 bg-white/80 sticky bottom-0 backdrop-blur-sm">
                        <button
                          type="button"
                          className="hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 px-4 border-black rounded-lg w-full text-sm xs:mb-0 mb-2"
                          onClick={closehosteleditModal}
                        >
                          Cancel
                        </button>
                        <button
                          type="submit"
                          className="bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm"
                        >
                          Save changes
                        </button>
                      </div>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      {addRules && (
        <>
          <div className="mx-auto px-6 py-9 max-w-[70%] grid sm:grid-cols-2 gap-4 w-full mt-3">
            <div>
              <label className="block text-gray-700 text-sm font-bold mb-2 mt-4">
                Check In
              </label>
              <input
                type="date"
                name="checkIn"
                value={rules.checkIn}
                onChange={handleInputChange}
                className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
                placeholder="Check In"
              />
            </div>
            <div>
              <label className="block text-gray-700 text-sm font-bold mb-2 mt-4">
                Check Out
              </label>
              <input
                type="date"
                name="checkOut"
                value={rules.checkOut}
                onChange={handleInputChange}
                className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
                placeholder="checkOut"
              />
            </div>
            <div>
              <label className="block text-gray-700 text-sm font-bold mb-2 mt-4">
                Children and beds
              </label>
              <input
                type="text"
                name="childPolicy"
                value={rules.childPolicy}
                onChange={handleInputChange}
                className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
                placeholder="Children and Beds"
              />
            </div>
            <div>
              <label className="block text-gray-700 text-sm font-bold mb-2 mt-4">
                Age Restriction
              </label>
              <input
                type="text"
                name="ageRestriction"
                value={rules.ageRestriction}
                onChange={handleInputChange}
                className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
                placeholder="Age Restriction"
              />
            </div>
            <div>
              <label className="block text-gray-700 text-sm font-bold mb-2 mt-4">
                Cancellation/ Prepayment
              </label>
              <textarea
                name="cancellation"
                value={rules.cancellation}
                onChange={handleInputChange}
                className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
                placeholder="Cancellation / Prepayment"
              />
            </div>
            <div>
              <label className="text-sm font-light text-gray-500 block mb-1">
                Pets
              </label>
              <Switch
                name="pets"
                checked={rules.pets}
                onChange={handleInputChange}
              />
            </div>

            <div className="flex items-center justify-between mx-auto mt-11 gap-4 col-span-2 w-3/4">
              <button
                type="submit"
                className="bg-primary-blue w-full font-semibold text-white p-4 rounded-full transition duration-200 hover:bg-sky-blue-750 hover:text-white"
                onClick={handleSubmit}
              >
                Submit
              </button>
            </div>
            <div className="flex items-center justify-between mx-auto mt-11 gap-4 col-span-2 w-3/4">
              <button
                type="submit"
                className="bg-primary-blue w-full font-semibold text-white p-4 rounded-full transition duration-200 hover:bg-sky-blue-750 hover:text-white"
                onClick={() => setAddRules(false)}
              >
                Back
              </button>
            </div>
          </div>
        </>
      )}
      {editRules && (
        <div className="mx-auto px-6 py-9 max-w-[70%] grid sm:grid-cols-2 gap-4 w-full mt-3">
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2 mt-4">
              Check In
            </label>
            <input
              type="date"
              name="checkIn"
              value={rules.checkIn}
              onChange={handleInputChange}
              className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
              placeholder="Check In"
            />
          </div>
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2 mt-4">
              Check Out
            </label>
            <input
              type="date"
              name="checkOut"
              value={rules.checkOut}
              onChange={handleInputChange}
              className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
              placeholder="checkOut"
            />
          </div>
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2 mt-4">
              Children and beds
            </label>
            <input
              type="text"
              name="childPolicy"
              value={rules.childPolicy}
              onChange={handleInputChange}
              className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
              placeholder="Children and Beds"
            />
          </div>
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2 mt-4">
              Age Restriction
            </label>
            <input
              type="text"
              name="ageRestriction"
              value={rules.ageRestriction}
              onChange={handleInputChange}
              className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
              placeholder="Age Restriction"
            />
          </div>
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2 mt-4">
              Cancellation/ Prepayment
            </label>
            <textarea
              name="cancellation"
              value={rules.cancellation}
              onChange={handleInputChange}
              className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
              placeholder="Cancellation / Prepayment"
            />
          </div>
          <div>
            <label className="block text-gray-700 text-sm font-bold mb-2 mt-4">
              Pets
            </label>
            <Switch
              name="pets"
              checked={rules.pets}
              onChange={handleInputChange}
            />
          </div>

          <div className="flex items-center justify-between mx-auto mt-11 gap-4 col-span-2 w-3/4">
            <button
              type="submit"
              className="bg-primary-blue w-full font-semibold text-white p-4 rounded-full transition duration-200 hover:bg-sky-blue-750 hover:text-white"
              onClick={handleSubmit}
            >
              Submit
            </button>
          </div>
          <div className="flex items-center justify-between mx-auto mt-11 gap-4 col-span-2 w-3/4">
            <button
              type="submit"
              className="bg-primary-blue w-full font-semibold text-white p-4 rounded-full transition duration-200 hover:bg-sky-blue-750 hover:text-white"
              onClick={() => setEditRules(false)}
            >
              Back
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default HostelRules;
