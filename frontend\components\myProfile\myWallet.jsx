import React, { useEffect, useRef, useState } from "react";
import dynamic from "next/dynamic";
import { getItemLocalStorage } from "@/utils/browserSetting";
import { getWalletDataApi } from "@/services/webflowServices";
import Loader from "../loader/loader";

const AddAmount = dynamic(() => import("./addAmount"), {
  ssr: false,
});
const Rewards = dynamic(() => import("./rewards"), {
  ssr: false,
});

const MyWalletPage = () => {
  const [openAddAmount, setopenAddAmount] = useState(false);
  const handleOpenAddAmount = () => {setopenAddAmount(true); setopenReward(false)};
  const [openReward, setopenReward] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [walletData, setWalletData] = useState([]);
  const [loading, setLoading] = useState(false);

  const handleOpenReward = () => {setopenReward(true); setopenAddAmount(false);};
  const isFirstRender = useRef(null);
  const userId = getItemLocalStorage("id");

  const fetchWalletData = async (id) => {
    setLoading(true);
    try {
      const response = await getWalletDataApi(id);
      setWalletData(response?.data?.data);
    } catch (error) {
      console.error("Error fetching payment data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!isFirstRender.current) {
      fetchWalletData(userId);
    } else {
      isFirstRender.current = false;
    }
  }, [userId]);

  return (
    <>
      {/* My Profile */}
      <Loader open={loading} />
      {!openAddAmount && !openReward &&

      <div className='w-full'>
        <div className='flex justify-between'>
          <h2 className='text-[#40E0D0] flex gap-1 text-2xl font-bold mb-6'>
            My
            <span className='text-black ml-0.5'> Wallet</span>
          </h2>
          <div>
            <button
              className='rounded-full hover:bg-sky-blue-750 hover:text-white transition-all bg-primary-blue py-2 sm:px-8 px-5 text-black font-semibold capitalize mx-auto block sm:text-base text-sm'
              onClick={handleOpenAddAmount}
            >
              + Add Card
            </button>
          </div>
        </div>
        <div className='mb-5 flex gap-2'>
          <div className='w-[240px] sm:h-[100px] h-[80px] border border-[#FF72AD] rounded-3xl sm:px-6 px-4 flex items-center cursor-pointer'>
            <div className=''>
              <p className='sm:text-lg text-base text-black font-semibold'>Total Balance</p>
              <p className='text-[#FF72AD] font-semibold sm:text-2xl text-xl'>52,000</p>
            </div>
          </div>
          <div
            className='w-[240px] sm:h-[100px] h-[80px] border border-[#40E0D0] rounded-3xl px-5 flex items-center cursor-pointer'
            onClick={handleOpenReward}
          >
            <div className=''>
              <p className='sm:text-lg text-base text-black font-semibold'>Reward Point</p>
              <p className='text-[#40E0D0] font-semibold sm:text-2xl text-xl'>2,000</p>
            </div>
          </div>
        </div>
        <h4 className='font-extrabold text-black underline'>Wallet History</h4>
        <div className='mt-5'>
          <div className='flex w-full border-b border-grey sm:py-3 py-2 justify-between pl-2'>
            <div>
              <p className='text-sm font-normal text-black'>
                <span className='font-bold'>Wallet - </span>Added{" "}
                <span className='text-green-600 font-semibold'>20000</span> INR
              </p>
              <p className='text-xs text-[#AAACAE]'>5 min ago</p>
            </div>
            <div>
              <p className='sm:text-lg text-base text-green-600 font-semibold'>+20000</p>
            </div>
          </div>
          <div className='flex w-full border-b border-grey sm:py-3 py-2 justify-between pl-2'>
            <div>
              <p className='text-sm font-normal text-black'>
                <span className='font-bold'>Booking - </span>Booked 4 Bed Mixed
                Dorm <span className='text-red-600 font-semibold'>5000</span>{" "}
                INR
              </p>
              <p className='text-xs text-[#AAACAE]'>5 min ago</p>
            </div>
            <div>
              <p className='sm:text-lg text-base text-red-600 font-semibold'>-2500</p>
            </div>
          </div>
          <div className='flex w-full border-b border-grey sm:py-3 py-2 justify-between pl-2'>
            <div>
              <p className='text-sm font-normal text-black'>
                <span className='font-bold'>Ride - </span>Book Mix Ride Delhi to
                Rishikesh{" "}
                <span className='text-red-600 font-semibold'>700</span> INR
              </p>
              <p className='text-xs text-[#AAACAE]'>5 min ago</p>
            </div>
            <div>
              <p className='sm:text-lg text-base text-red-600 font-semibold'>-700</p>
            </div>
          </div>
          <div className='flex w-full border-b border-grey sm:py-3 py-2 justify-between pl-2'>
            <div>
              <p className='text-sm font-normal text-black'>
                <span className='font-bold'>Wallet - </span>Added{" "}
                <span className='text-green-600 font-semibold'>7000</span> INR
              </p>
              <p className='text-xs text-[#AAACAE]'>5 min ago</p>
            </div>
            <div>
              <p className='sm:text-lg text-base text-green-600 font-semibold'>+20000</p>
            </div>
          </div>
        </div>
      </div>
      }

      {/* Add Amount */}
      {openAddAmount && !openReward && <AddAmount />}
      {/* Rewards Start */}
      {openReward && !openAddAmount && <Rewards />}
    </>
  );
};

export default MyWalletPage;
