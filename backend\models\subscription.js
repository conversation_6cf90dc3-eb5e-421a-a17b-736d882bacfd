import mongoose from 'mongoose';

const subscriptionSchema = new mongoose.Schema({
  user_id: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'users', // Matches the `User` model name
    required: true 
  },
  plan_id: { 
    type: String, 
    required: true 
  },
  customer_id: { 
    type: String, 
    required: true 
  },
  razorpay_subscription_id: {
    type: String, 
    required: true
  },
  total_count: { 
    type: Number 
  },
  remaining_count: { 
    type: Number 
  },
  status: { 
    type: String, 
    required: true, 
    enum: [
      'created',      
      'active',       
      'authenticated',
      'pending',      
      'completed',    
      'halted',       
      'cancelled',    
      'expired'       
    ],
  },
  addons: [
    {
      item: {
        name: String,
        amount: Number,
        currency: String,
      },
    },
  ], 
  start_at: { 
    type: Date 
  },
  end_at: { 
    type: Date 
  },
  current_start: { 
    type: Date 
  },
  current_end: { 
    type: Date 
  },
  created_at: { 
    type: Date, 
    default: Date.now 
  },
  updated_at: { 
    type: Date, 
    default: Date.now 
  },
});

const Subscription = mongoose.model('subscriptions', subscriptionSchema); // Collection name in lowercase plural
export default Subscription;
