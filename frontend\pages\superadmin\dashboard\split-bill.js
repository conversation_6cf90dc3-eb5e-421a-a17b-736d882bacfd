"use client";
import { Plus } from "lucide-react";
import React from "react";
import { FiEye } from "react-icons/fi";
import { TfiPencilAlt } from "react-icons/tfi";
import { FaRegTrashCan } from "react-icons/fa6";
import Image from "next/image";
import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight,
} from "react-icons/md";
import Link from "next/link";

const MixCreator = () => {
  const creatorData = [
    {
      id: 1,
      headlineText: "Split bill fairfare",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/split-bill.png`,
    },
    {
      id: 2,
      headlineText: "Mix mate",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mix-mate.png`,
    },
    {
      id: 3,
      headlineText: "Mix ride",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mix-ride.png`,
    },
  ];
  return (
    <div className="w-full p-7 bg-sky-blue-20 lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px]  float-end  overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <div className="flex items-center justify-between w-full flex-wrap">
        <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        Split bill fairfare, Mix mate, Mix ride
        </h2>
        <div className="w-[100%] md:w-[100%] lg:w-[50%] gap-x-2 flex justify-start  lg:justify-end items-center mt-3 lg:mt-3">
          <Link href={"/superadmin/dashboard/split-bill-add"}
            className={` px-2 py-2 text-sm font-normal text-white rounded relative flex justify-center items-center bg-sky-blue-650 `}
            type="button"
            
          >
            <Plus size={18} className="mr-1" /> Split bill fairfare, mixmate, mixride
          </Link>
        </div>
      </div>
      <div className="bg-white border-b border-l border-r rounded-xl dark:bg-black dark:border-none">
        <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border-t dark:border-none">
          <table className="min-w-full divide-y bg-white rounded-xl divide-gray-200 dark:bg-black">
            <thead>
              <tr>
                <th className="px-6 py-6 bg-white text-left text-sm font-poppins font-semibold text-gray-600 uppercase tracking-wider whitespace-nowrap dark:bg-black dark:text-[#B6B6B6]">
                  HEADLINE TEXT
                </th>

                <th className="px-2 pl-16 md:pl-20 lg:pl-12 pr-6 py-6 bg-white text-left text-sm font-poppins font-semibold text-gray-600 uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  IMAGE
                </th>

                <th className="pr-9 md:pr-7 lg:pr-8 py-6 bg-white text-end text-sm font-poppins font-semibold text-gray-600 uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]" >
                  ACTION
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 border-x border-y dark:border-x-0 text-black/70 dark:text-[#757575]">
              {creatorData.map((creator) => (
                <tr key={creator.id}>
                  <td className="whitespace-nowrap px-5 text-sm font-poppins font-medium">
                    {creator.headlineText}
                  </td>

                  <td>
                    <button className="font-medium py-1 rounded pl-12 lg:pl-0">
                      <Image
                        src={creator.image}
                        width={80}
                        height={216}
                        alt="Image"
                        className="w-28 h-48 md:w-32 md:h-52  lg:w-36 lg:h-60 rounded-md"
                      />
                    </button>
                  </td>

                  <td className="py-32 md:py-24 lg:py-20 pl-14 lg:pl-0 pr-2 md:pr-0 lg:pr-3 flex justify-end">
                    <Link href={"/superadmin/dashboard/split-bill-details"} className="border p-2 rounded-l-lg text-black/75 hover:text-blue-700 dark:text-[#757575] dark:hover:text-blue-700">
                      <FiEye />
                    </Link>
                    <Link href={"/superadmin/dashboard/split-bill-edit"} className="border p-2 text-black/75 hover:text-yellow-400 dark:text-[#757575] dark:hover:text-blue-700">
                      <TfiPencilAlt />
                    </Link>
                    <button className="p-2 border rounded-r-lg text-red-600">
                      <FaRegTrashCan />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      {/* <div className="flex justify-between items-center mt-5">
        <div className="text-black/75 text-sm font-poppins font-medium">Showing 1-09 of 78</div>
        <div className="inline-flex items-center justify-center border rounded-xl bg-white">
          <a
            href="#"
            className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180"
          >
            <span className="sr-only">Next Page</span>
            <MdOutlineKeyboardArrowLeft />
          </a>

          <a
            href="#"
            className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180"
          >
            <span className="sr-only">Next Page</span>
            <MdOutlineKeyboardArrowRight />
          </a>
        </div>
      </div> */}
            <div className="flex justify-between items-center mt-5">
              <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
              <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
                <a
                  href="#"
                  className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                >
                  <span className="sr-only">Next Page</span>
                  <MdOutlineKeyboardArrowLeft />
                </a>
      
                <a
                  href="#"
                  className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                >
                  <span className="sr-only">Next Page</span>
                  <MdOutlineKeyboardArrowRight />
                </a>
              </div>
            </div>
    </div>
  );
};

export default MixCreator;
