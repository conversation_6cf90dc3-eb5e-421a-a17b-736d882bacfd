import mongoose from 'mongoose';

const subBookingSchema = new mongoose.Schema(
  {
    bookingId: {
      type: mongoose.Types.ObjectId,
      required: true,
    },
    user: {
      type: mongoose.Types.ObjectId,
      ref: 'users',
      required: true
    },
    property: {
      type: mongoose.Types.ObjectId,
      ref: 'users',
      required: true

    },
    subBookingId: {
      type: Number,
      required: true,
    },
    room: {
      type: mongoose.Types.ObjectId,
      ref: 'rooms',
      required: true,
    },
    roomNumber: String,
    bedType: String,
    beds: Number,
    checkInDate: { type: Date, required: true },
    checkOutDate: { type: Date, required: true },
    guests: {
      adults: Number,
      children: Number,
    },
    beds: { type: Number },
    rate: {
      baseAmount: Number,
      taxAmount: Number,
      serviceFee: Number,
      totalAmount: Number,
      currency: { type: String, default: 'INR' },
    },
    totalAmount: { type: Number },
    // 💰 New breakdown fields
    payableNow: { type: Number, default: 0 },
    payableOnArrival: { type: Number, default: 0 },
    baseAdvance: { type: Number, default: 0 },
    gst: { type: Number, default: 0 },
    paymentGatewayCharge: { type: Number, default: 0 },
    totalPaidAmount: { type: Number, default: 0 },

    ratePlan: {
      name: String,
      id: Number
    },
    status: {
      type: String,
      enum: ['pending', 'confirmed', 'cancelled'],
      default: 'confirmed',
    },
    nights: { type: Number },
    isCancel: { type: Boolean, default: false },
    cancelledDate: Date,
    cancelledBy: mongoose.Types.ObjectId,
    isDeleted: { type: Boolean, default: false },
  },
  { timestamps: true }
);

subBookingSchema.index({ subBookingId: 1 }, { unique: true });

const subBookingModel = mongoose.model('subBookings', subBookingSchema, "subBookings");
export default subBookingModel;
