import Response from "../utills/response.js";
import { addReview, updateReviewById, getReviewById, listAllReviewsByProperty, deleteReviewById, listAllReviews, getRatingDetailsByPropertyId } from "../services/reviews.js";
import { noticeboardAdd } from "./noticeboard.js";

const addReviewController = async (req, res) => {
  try {
    const name = req.user.name
    if (req.body.name) {
      name = req.body.name.first
    }
    const reviewData = { ...req.body, user: req.user._id, country: req.user?.country, userName: name };
    const newReview = await addReview(reviewData);
    const reviewMessage = `${reviewData.comment}`
    const dynamicId = { review: newReview._id }
    const dynamicData = {
      rating: newReview.rating,
      comment: newReview.comment,
    }
    await noticeboardAdd(req.user._id, reviewMessage, 'propertyReview', 'Review Added On Property', true, dynamicId, dynamicData)
    return Response.Created(res, newReview, 'Review Added Successfully');
  } catch (error) {
    return Response.InternalServerError(res, null, error.message);
  }
};

const updateReviewController = async (req, res) => {
  try {
    const { id } = req.params;
    const updatedReview = await updateReviewById(id, req.body);
    return Response.OK(res, updatedReview, 'Review Updated Successfully');
  } catch (error) {
    return Response.InternalServerError(res, null, error.message);
  }
};

const getReviewByIdController = async (req, res) => {
  try {
    const { id } = req.params;
    const review = await getReviewById(id);
    return Response.OK(res, review, 'Review Retrieved Successfully');
  } catch (error) {
    return Response.InternalServerError(res, null, error.message);
  }
};

const listAllReviewsByHostelController = async (req, res) => {
  try {
    const { propertyId } = req.params;
    let { page, limit, ...filter } = req.query;

    // Convert page and limit to integers
    page = parseInt(page);
    limit = parseInt(limit);

    // If page or limit are not valid numbers, default them
    if (isNaN(page) || page <= 0) {
      page = 1;
    }
    if (isNaN(limit) || limit <= 0) {
      limit = 10;
    }
    const reviewsData = await listAllReviewsByProperty(propertyId, filter, page, limit);
    const totalPages = Math.ceil(reviewsData.totalReviews / parseInt(limit));
    const pagination = {
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages,
      totalReviews: reviewsData.totalReviews
    };

    return Response.OK(res, { reviews: reviewsData.reviews, pagination, ratings: reviewsData.ratings }, 'Reviews Retrieved Successfully');
  } catch (error) {
    return Response.InternalServerError(res, null, error.message);
  }
};

const listAllReviewsController = async (req, res) => {
  try {
    let { page, limit, ...filter } = req.query;

    // Convert page and limit to integers
    page = parseInt(page);
    limit = parseInt(limit);

    // If page or limit are not valid numbers, default them
    if (isNaN(page) || page <= 0) {
      page = 1;
    }
    if (isNaN(limit) || limit <= 0) {
      limit = 10;
    }

    const reviewsData = await listAllReviews(filter, page, limit);

    const totalPages = Math.ceil(reviewsData.totalReviews / parseInt(limit));
    const pagination = {
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages,
      totalReviews: reviewsData.totalReviews
    };

    return Response.OK(res, { reviews: reviewsData.reviews, pagination, ratings: reviewsData.ratings }, 'Reviews Retrieved Successfully');
  } catch (error) {
    return Response.InternalServerError(res, null, error.message);
  }
};


const deleteReviewController = async (req, res) => {
  try {
    const { id } = req.params;
    const result = await deleteReviewById(id);
    return Response.OK(res, 'Review Deleted Successfully');
  } catch (error) {
    return Response.InternalServerError(res, null, error.message);
  }
};

const getRatingDetailsByPropertyIdController = async (req, res) => {
  try {
    const { propertyId } = req.params;

    const ratingDetails = await getRatingDetailsByPropertyId(propertyId);
    return Response.OK(res, ratingDetails, 'Rating Details Retrieved Successfully');
  } catch (error) {
    return Response.InternalServerError(res, null, error.message);
  }
};

export { addReviewController, updateReviewController, getReviewByIdController, listAllReviewsByHostelController, deleteReviewController, listAllReviewsController, getRatingDetailsByPropertyIdController };