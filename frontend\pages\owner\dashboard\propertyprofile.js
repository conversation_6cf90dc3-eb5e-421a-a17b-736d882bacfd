import React, { useEffect, useRef, useState } from "react";
import { propertyDetailsApi } from "@/services/ownerflowServices";
import { useRouter } from "next/router";
import dynamic from "next/dynamic";
import Image from "next/image";
// import toast from "react-hot-toast";
import Loader from "@/components/loader/loader";
import Head from "next/head";

const AboutUs = dynamic(() =>
  import("@/components/ownerFlow/propertyProfile/aboutus")
);
const HostelPhotos = dynamic(() =>
  import("@/components/ownerFlow/propertyProfile/hostelPhotos")
);
// const HostelRules = dynamic(() =>
//   import("@/components/ownerFlow/propertyProfile/hostelRules")
// );
const HostelRulesNew = dynamic(() =>
  import("@/components/ownerFlow/propertyProfile/hostelRules2")
);
const CancellationPolicy = dynamic(() =>
  import("@/components/ownerFlow/propertyProfile/cancellationPolicy")
);
const GeneralPolicy = dynamic(() =>
  import("@/components/ownerFlow/propertyProfile/generalPolicy")
);
const Amenities = dynamic(() =>
  import("@/components/ownerFlow/propertyProfile/amenities")
);
// const ContactUs = dynamic(() =>
//   import("@/components/ownerFlow/propertyProfile/contactUs")
// );
const ContactUs = dynamic(
  () => import("@/components/ownerFlow/propertyProfile/contactUs"),
  {
    loading: () => (
      <>
        <div>
          {/* Header Skeleton */}
          <div className="flex justify-between items-center mb-5">
            <div className="animate-pulse bg-gray-200 h-6 w-32 rounded"></div>
            <div className="flex gap-2">
              <div className="animate-pulse bg-gray-200 h-10 w-32 rounded-lg"></div>
            </div>
          </div>

          {/* Content Skeleton */}
          <div className="flex flex-wrap">
            <div className="w-full mb-4 lg:w-1/3 lg:mb-0 flex flex-col gap-4">
              <div className="p-4 mb-4 border border-gray-200 rounded-lg shadow-md min-h-28 animate-pulse">
                <div className="flex items-center mb-3">
                  <div className="bg-gray-200 w-5 h-5 rounded-full mr-2"></div>
                  <div className="bg-gray-200 h-4 w-24 rounded"></div>
                </div>
                <div className="space-y-2">
                  <div className="bg-gray-200 h-3 w-full rounded"></div>
                  <div className="bg-gray-200 h-3 w-3/4 rounded"></div>
                  <div className="bg-gray-200 h-3 w-2/3 rounded"></div>
                </div>
              </div>
              <div className="p-4 mb-4 border border-gray-200 rounded-lg shadow-md min-h-28 animate-pulse">
                <div className="flex items-center mb-3">
                  <div className="bg-gray-200 w-5 h-5 rounded-full mr-2"></div>
                  <div className="bg-gray-200 h-4 w-24 rounded"></div>
                </div>
                <div className="space-y-2">
                  <div className="bg-gray-200 h-3 w-full rounded"></div>
                  <div className="bg-gray-200 h-3 w-3/4 rounded"></div>
                  <div className="bg-gray-200 h-3 w-2/3 rounded"></div>
                </div>
              </div>
            </div>
            <div className="w-full lg:w-[45%] px-4 animate-pulse">
              <div className="bg-gray-200 rounded-xl h-[275px] w-full"></div>
            </div>
          </div>
        </div>
      </>
    ),
    ssr: false,
  }
);
const ChannelPartner = dynamic(() =>
  import("@/components/ownerFlow/propertyProfile/channelPartner")
);

const Profile = () => {
  const [activeTab, setActiveTab] = useState(1);
  const [selectedId, setSelectedId] = useState(null);
  const [data, setData] = useState(null);
  const [redirectHandled, setRedirectHandled] = useState(false);
  const [loading, setLoading] = useState(false);
  // const toastShownRef = useRef(false);
  const router = useRouter();

  const containerRef = useRef(null);
  let isDown = false;
  let startX;
  let scrollLeft;

  const handleMouseDown = (e) => {
    isDown = true;
    containerRef.current.classList.add("cursor-grabbing");
    startX = e.pageX - containerRef.current.offsetLeft;
    scrollLeft = containerRef.current.scrollLeft;
  };

  const handleMouseLeaveOrUp = () => {
    isDown = false;
    containerRef.current.classList.remove("cursor-grabbing");
  };

  const handleMouseMove = (e) => {
    if (!isDown) return;
    e.preventDefault();
    const x = e.pageX - containerRef.current.offsetLeft;
    const walk = (x - startX) * 1; // Adjust for sensitivity
    containerRef.current.scrollLeft = scrollLeft - walk;
  };

  const tabs = [
    // {
    //   id: 1,
    //   name: "Edit Information",
    // },
    {
      id: 1,
      name: "About Us",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/about.svg`,
      icon1: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/about-b.svg`,
      count: "--",
    },
    {
      id: 2,
      name: "Hostel Photos",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/hotelimg.svg`,
      icon1: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/hotelimg-b.svg`,
      count: data?.images?.length || 0,
    },
    {
      id: 3,
      name: "Things To Note",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/hostelrule.svg`,
      icon1: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/hostelrule-b.svg`,
      count: 1,
    },
    {
      id: 4,
      name: "Cancellation Policy",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/cancellation.svg`,
      icon1: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/cancellation-b.svg`,
      count: 1,
    },
    {
      id: 5,
      name: "General Policy",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/gpolicy-b.svg`,
      icon1: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/gpolicy.svg`,
      count: 1,
    },
    {
      id: 6,
      name: "Amenities",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/amenities.svg`,
      icon1: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/amenities-b.svg`,
      count: data?.freeFacilities?.length || 0,
    },
    {
      id: 7,
      name: "Contact Us",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/contact.svg`,
      icon1: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/contact-b.svg`,
      count: "--",
    },
    // {
    //   id: 8,
    //   name: "Channel Partner",
    //   icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/channel.svg`,
    //   icon1: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/channel-b.svg`,
    //   count: "--",
    // },
  ];

  useEffect(() => {
    const storedId = localStorage.getItem("hopid");
    if (!storedId && !redirectHandled) {
      // if (!toastShownRef.current) {
      //   toast.error("Please select at least one property.");
      //   toastShownRef.current = true;
      // }
      // router.push("/dashboard");
      setRedirectHandled(true);
      return;
    }
    setSelectedId(storedId);
  }, [redirectHandled, router]);

  useEffect(() => {
    if (selectedId) {
      fetchPropertiesById(selectedId);
    }
  }, [selectedId]);

  const fetchPropertiesById = async (id) => {
    setLoading(true);
    try {
      const response = await propertyDetailsApi(id);

      if (response?.status === 200) {
        setData(response?.data?.data);
      }
    } catch (error) {
      console.error("Error fetching properties:", error.message);
    } finally {
      setLoading(false);
    }
  };

  const updatePropertyData = () => {
    fetchPropertiesById(selectedId);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      // case 1:
      //   return (
      //     <EditProperty
      //       id={selectedId}
      //       data={data}
      //       updatePropertyData={updatePropertyData}
      //     />
      //   );
      case 1:
        return (
          <AboutUs
            id={selectedId}
            data={data}
            loading={loading}
            updatePropertyData={updatePropertyData}
            setLoading={setLoading}
          />
        );
      case 2:
        return (
          <HostelPhotos
            id={selectedId}
            loading={loading}
            data={data}
            updatePropertyData={updatePropertyData}
          />
        );
      case 3:
        return (
          <HostelRulesNew
            id={selectedId}
            loading={loading}
            data={data}
            updatePropertyData={updatePropertyData}
            setLoading={setLoading}
          />
        );
      case 4:
        return (
          <CancellationPolicy
            id={selectedId}
            loading={loading}
            data={data}
            updatePropertyData={updatePropertyData}
            setLoading={setLoading}
          />
        );
      case 5:
        return (
          <GeneralPolicy
            id={selectedId}
            loading={loading}
            data={data}
            updatePropertyData={updatePropertyData}
            setLoading={setLoading}
          />
        );
      case 6:
        return (
          <Amenities
            id={selectedId}
            loading={loading}
            data={data}
            updatePropertyData={updatePropertyData}
          />
        );
      case 7:
        return (
          <ContactUs
            id={selectedId}
            loading={loading}
            data={data}
            updatePropertyData={updatePropertyData}
          />
        );
      case 8:
        return <ChannelPartner id={selectedId} data={data} />;
      default:
        return <AboutUs id={selectedId} data={data} />;
    }
  };

  return (
    <>
      <Head>
        <title>Property Profile Management | Mixdorm</title>
      </Head>
      <Loader open={loading} />
      <section className="w-full">
        <h1 className="page-title mb-5">Property Profile</h1>
        <ul
          ref={containerRef}
          className="flex gap-3 w-full max-w-full overflow-y-auto no-scrollbar cursor-grab pr-5"
          onMouseDown={handleMouseDown}
          onMouseLeave={handleMouseLeaveOrUp}
          onMouseUp={handleMouseLeaveOrUp}
          onMouseMove={handleMouseMove}
        >
          {/* {tabs.map((tab) => (
          <li
            className={`relative pb-3 w-fit mmd:mt-6 cursor-pointer first:mt-0 text-sm mmd:text-base font-medium ${
              activeTab === tab.id
                ? "text-primary-blue"
                : "text-slate-320"
            }`}
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
          >
            {tab.name}
            {activeTab === tab.id && (
              <div className='absolute bottom-0 w-full h-1 rounded-full bg-primary-blue'></div>
            )}
          </li>
        ))} */}
          {tabs.map((item) => (
            <li
              key={item.id}
              className={`flex items-center sm:gap-10 lg:gap-4 gap-2 sm:p-4 p-2 sm:pb-5 pb-2 relative border rounded-lg border-[#D0D3D9] sm:min-w-[257px] min-w-[190px] w-full ${
                activeTab === item.id ? "bg-black" : ""
              }`}
              onClick={() => setActiveTab(item.id)}
            >
              <div>
                <a
                  className={`font-medium ${
                    activeTab === item.id ? "text-[#ffffff]" : "text-[#000000]"
                  } cursor-pointer text-left xl:text-lg text-sm`}
                  href="#"
                >
                  {item.name}
                </a>
                <h5
                  className={`text-base text-[#00000080] font-semibold mt-3 ${
                    activeTab === item.id
                      ? "text-[#FFFFFF80]"
                      : "text-[#00000080]"
                  }`}
                >
                  <span
                    className={`lg:text-2xl text-lg ${
                      activeTab === item.id
                        ? "text-[#40E0D0]"
                        : "text-[#000000]"
                    }`}
                  >
                    {" "}
                    {item.count}
                  </span>
                </h5>
              </div>
              {activeTab === item.id ? (
                <Image
                  src={item.icon}
                  width={40}
                  height={40}
                  alt="BgImg"
                  className="ml-auto xl:w-[40px] xl:h-[40px] lg:w-[30px] lg:h-[30px] w-[25px] h-[25px]"
                  loading="lazy"
                />
              ) : (
                <Image
                  src={item.icon1}
                  width={40}
                  height={40}
                  alt="BgImg"
                  className="ml-auto xl:w-[40px] xl:h-[40px] lg:w-[30px] lg:h-[30px] w-[25px] h-[25px]"
                  loading="lazy"
                />
              )}
            </li>
          ))}
        </ul>
        <div className="w-full bg-white border border-slate-200 border-1 my-5 sm:px-5 px-2 pt-5 sm:pb-20 pb-5 rounded-lg">
          {renderTabContent()}
        </div>
      </section>
    </>
  );
};

export default Profile;
