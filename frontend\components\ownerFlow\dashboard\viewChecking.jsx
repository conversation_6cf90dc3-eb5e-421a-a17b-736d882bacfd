/* eslint-disable react/jsx-key */
import Loader from "@/components/loader/loader";
import { getcheckInApi } from "@/services/ownerflowServices";
import Image from "next/image";
import { useEffect, useState } from "react";

const ViewChecking = ({ editId }) => {
  const [formData, setFormData] = useState({
    bookingId: "",
    name: "",
    checkIn: "",
    checkOut: "",
    document: "",
    attachment: null,
    comingFrom: {
      address: "",
      location: {
        type: "Point",
        coordinates: [0, 0],
      },
    },
    goingTo: {
      address: "",
      location: {
        type: "Point",
        coordinates: [0, 0],
      },
    },
    signature: null,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState("");

  useEffect(() => {
    if (editId) {
      fetchData(editId);
    }
  }, [editId]);

  const fetchData = async (editId) => {
    setIsLoading(true);
    try {
      const res = await getcheckInApi(editId);
      if (res?.status == 200) {
        const data = res?.data?.data;
        setFormData({
          bookingId: data?.booking,
          name: data?.guestDetails?.name,
          checkIn: data?.checkIn.split("T")[0],
          checkOut: data?.checkOut.split("T")[0],
          document: "",
          attachment: data?.uploadedDocuments,
          comingFrom: {
            address: data?.fromLocation,
            location: {
              type: "Point",
              coordinates: [0, 0],
            },
          },
          goingTo: {
            address: data?.toLocation,
            location: {
              type: "Point",
              coordinates: [0, 0],
            },
          },
          signature: null,
        });
        setPhoneNumber(`+${data?.guestDetails?.phone?.number.toString()}`);
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setIsLoading(false);
    }
  };

  console.log("formData", formData);

  return (
    <>
      <Loader open={isLoading} />
      <section className='w-full'>
        <div className='w-full'>
          <div className='max-w-[780px] mx-auto pb-5'>
            <div className='grid sm:grid-cols-[1.4fr_3fr] grid-cols-2 sm:gap-4 gap-3 font-inter'>
              <div>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Booking Id :
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Name :
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Phone Number :
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Check In :
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Check Out:
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Upload Documents :
                </p>
              </div>
              <div>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData?.bookingId}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData?.name}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {phoneNumber}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData?.checkIn}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData?.checkOut}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData?.attachment?.map((image, index) => (
                    <Image
                      className='w-[54px] h-[36px] object-cover rounded-sm'
                      src={image?.url}
                      alt={`Existing Image ${index + 1}`}
                      width={54}
                      height={36}
                    ></Image>
                  ))}
                </p>
              </div>
              <div>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Photo :
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Location :
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Sign :
                </p>
              </div>
              <div>
              {formData?.attachment?.map((image, index) => (
                    <Image
                      className='w-[54px] h-[36px] object-cover rounded-sm'
                      src={image?.url}
                      alt={`Existing Image ${index + 1}`}
                      width={54}
                      height={36}
                    ></Image>
                  ))}
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                {formData?.comingFrom?.address} to {formData?.goingTo?.address}
                </p>
                {formData?.signature?.map((image, index) => (
                    <Image
                      className='w-[54px] h-[36px] object-cover rounded-sm'
                      src={image?.url}
                      alt={`Existing Image ${index + 1}`}
                      width={54}
                      height={36}
                    ></Image>
                  )) || " - "}
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default ViewChecking;
