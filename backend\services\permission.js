import Permission from '../models/permission.js';

// Create a new permission
const createPermission = async (permissionData) => {
    const permission = new Permission(permissionData);
    return await permission.save();
};

// Get all permissions
const getAllPermissions = async () => {
    return await Permission.find().populate('user', 'name email');
};

// Get a specific permission by ID
const getPermissionById = async (permissionId) => {
    return await Permission.findById({_id : permissionId}).populate('user', 'name email');
};

// Update a permission by ID
const updatePermission = async (permissionId, permissionData) => {
    return await Permission.findByIdAndUpdate({_id : permissionId}, permissionData, { new: true });
};

// Delete a permission by ID
const deletePermission = async (permissionId) => {
    return await Permission.findByIdAndUpdate({_id : permissionId}, {isDeleted : true, isActive : false});
};

export { createPermission, getAllPermissions, getPermissionById, updatePermission, deletePermission };