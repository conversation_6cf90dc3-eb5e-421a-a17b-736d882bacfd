/* eslint-disable no-irregular-whitespace */
/* eslint-disable react/no-unescaped-entities */
import { Camera } from "lucide-react";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import {
  editPro<PERSON>le<PERSON><PERSON>,
  getAllReviewsList<PERSON>pi,
  getProfile<PERSON><PERSON>,
  getTravellersVisitedApi,
} from "@/services/ownerflowServices";
import toast from "react-hot-toast";
import { useRouter } from "next/router";
import Link from "next/link";
import {
  IoIosArrowForward,
  IoIosListBox,
} from "react-icons/io";
import { GoShareAndroid } from "react-icons/go";
import { BASE_URL } from "@/utils/api";
import { useHeaderOwner } from "@/components/ownerFlow/headerContex";
import { MapPinned, CalendarDays, Settings, Trash2 } from "lucide-react";
import { HiOfficeBuilding } from "react-icons/hi";
import { FaStar, FaCalendarAlt, FaRegEdit } from "react-icons/fa";
import { Fa<PERSON>hartPie } from "react-icons/fa6";
import { MdNotificationsActive } from "react-icons/md";
import { RiShieldUserFill } from "react-icons/ri";
import { HiOutlineUserGroup } from "react-icons/hi2";
import dynamic from "next/dynamic";

const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

const Profile = () => {
  const [selectedId, setSelectedId] = useState(null);
  const [redirectHandled, setRedirectHandled] = useState(false);
  // const toastShownRef = useRef(false);
  const fileInputRef = useRef(null);
  const [travellersCount, setTravellersCount] = useState(0);
  const [uploading, setUploading] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [reviews, setReviews] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [currentPage, setCurrentPage] = useState(1);
  const [reviewPerPage] = useState(10); // Set the number of reviews per page
  // eslint-disable-next-line no-unused-vars
  const [totalReviews, setTotalReviews] = useState(0);
  // eslint-disable-next-line no-unused-vars
  const [rating, setRating] = useState();

  const isFirstRender = useRef(true);

  const router = useRouter();

  // const tabs = [
  //   {
  //     id: 1,
  //     name: "Edit Information",
  //   },
  //   {
  //     id: 2,
  //     name: "About Us",
  //   },
  //   {
  //     id: 3,
  //     name: "Hostel Photos",
  //   },
  //   {
  //     id: 4,
  //     name: "Hostel Rules",
  //   },
  //   {
  //     id: 5,
  //     name: "Cancellation Policy",
  //   },
  //   {
  //     id: 6,
  //     name: "General Policy",
  //   },
  //   {
  //     id: 7,
  //     name: "Amenities",
  //   },
  //   {
  //     id: 8,
  //     name: "Contact US",
  //   },
  //   {
  //     id: 9,
  //     name: "Channel Partner",
  //   },
  // ];

  const { profileData, propertyData, updateuserData } = useHeaderOwner();

  useEffect(() => {
    const storedId = localStorage.getItem("hopid");
    if (!storedId && !redirectHandled) {
      // if (!toastShownRef.current) {
      //   toast.error("Please select at least one property.");
      //   toastShownRef.current = true;
      // }
      router.push("/owner/login");
      setRedirectHandled(true);
      return;
    }
    setSelectedId(storedId);
  }, [redirectHandled, router]);

  useEffect(() => {
    if (!isFirstRender.current && selectedId) {
      fetchTravellersCount(selectedId);
      fetchReviews(selectedId);
    } else {
      isFirstRender.current = false;
    }
  }, [selectedId]);

  // const fetchUserData = async () => {
  //   try {
  //     const response = await getProfileApi();

  //     if (response?.status === 200) {
  //       setProfileData(response?.data?.data);
  //     }
  //   } catch (error) {
  //     console.error("Error fetching profile:", error.message);
  //   }
  // };

  const fetchTravellersCount = async (id) => {
    try {
      const response = await getTravellersVisitedApi(id);

      if (response?.status === 200) {
        setTravellersCount(response?.data?.data);
      }
    } catch (error) {
      console.error("Error fetching profile:", error.message);
    }
  };

  const fetchReviews = async (id) => {
    try {
      const response = await getAllReviewsListApi(
        id,
        currentPage,
        reviewPerPage
      );
      console?.log("response", response);
      if (response.status) {
        setReviews(response?.data?.data?.reviews);
        setTotalReviews(response?.data?.data?.pagination?.totalReviews);
        setRating(response?.data?.data?.ratings);
      }
    } catch (error) {
      console.error("Error fetching reviews:", error);
    }
  };

  // const updatePropertyData = () => {
  //   fetchPropertiesById(selectedId);
  // };

  // const renderTabContent = () => {
  //   switch (activeTab) {
  //     case 1:
  //       return (
  //         <EditProperty
  //           id={selectedId}
  //           data={data}
  //           updatePropertyData={updatePropertyData}
  //         />
  //       );
  //     case 2:
  //       return (
  //         <AboutUs
  //           id={selectedId}
  //           data={data}
  //           updatePropertyData={updatePropertyData}
  //         />
  //       );
  //     case 3:
  //       return (
  //         <HostelPhotos
  //           id={selectedId}
  //           data={data}
  //           updatePropertyData={updatePropertyData}
  //         />
  //       );
  //     case 4:
  //       return (
  //         <HostelRules
  //           id={selectedId}
  //           data={data}
  //           updatePropertyData={updatePropertyData}
  //         />
  //       );
  //     case 5:
  //       return (
  //         <CancellationPolicy
  //           id={selectedId}
  //           data={data}
  //           updatePropertyData={updatePropertyData}
  //         />
  //       );
  //     case 6:
  //       return (
  //         <GeneralPolicy
  //           id={selectedId}
  //           data={data}
  //           updatePropertyData={updatePropertyData}
  //         />
  //       );
  //     case 7:
  //       return (
  //         <Amenities
  //           id={selectedId}
  //           data={data}
  //           updatePropertyData={updatePropertyData}
  //         />
  //       );
  //     case 8:
  //       return <ContactUs id={selectedId} data={data} />;
  //     case 9:
  //       return <ChannelPartner id={selectedId} data={data} />;
  //     default:
  //       return <AboutUs id={selectedId} data={data} />;
  //   }
  // };

  const handleFileChange = async (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      setUploading(true);
      // setErrors({ ...errors, file: null });

      try {
        const formData = new FormData();
        formData.append("file", selectedFile);

        const presignedUrlResponse = await fetch(
          `${BASE_URL}/fileUpload/generate-presigned-url`,
          {
            method: "POST",
            body: formData,
          }
        );

        if (!presignedUrlResponse.ok) {
          throw new Error("Failed to get presigned URL");
        }

        const presignedUrlData = await presignedUrlResponse.json();
        const { objectURL } = presignedUrlData.data;

        if (presignedUrlData?.status) {
          const response = await editProfileApi({
            profileImage: {
              objectURL: objectURL,
            },
          });
          if (response?.data?.status) {
            toast.success(
              response?.data?.message || "Profile updated successfully!"
            );

            try {
              const response = await getProfileApi();

              if (response?.status === 200) {
                updateuserData(response?.data?.data);
              }
            } catch (error) {
              console.error("Error fetching profile:", error.message);
            }
          }
        }

        toast.success("Profile picture uploaded successfully!");
      } catch (error) {
        console.error("Error uploading profile picture", error);
        toast.error("Error uploading profile picture");
       
      } finally {
        setUploading(false);
      }
    } else {
      toast.error("Error uploading profile picture");
    }
  };

  console.log("datatata", travellersCount);

  return (
    <>
      <Loader open={uploading} />
      <section className='w-full'>
        <h2 className='text-xl font-medium text-black-230'>Profile</h2>
        <div className='w-full mt-5 bg-white rounded-md shadow-md p-4'>
          <div className='flex justify-between gap-2'>
            <div className='flex m-2'>
              <div className='w-10 sm:w-14 h-10 sm:h-14 md:w-20 md:h-20 mmd:w-[73px] mmd:h-[73px] rounded-full relative'>
                <Image
                  src={
                    profileData?.profileImage?.objectURL
                      ? profileData?.profileImage?.objectURL
                      : `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/avatar.png`
                  }
                  alt={propertyData?.name || "Property"}
                  className='w-full h-full rounded-full'
                  width={500}
                  height={500}
                  loading='lazy'
                />
                <div
                  className='absolute flex items-center justify-center w-5 h-5 rounded-full sm:w-8 sm:h-8 -bottom-0 -right-0 bg-primary-blue cursor-pointer z-10'
                  onClick={() => fileInputRef.current.click()}
                >
                  <Camera size={15} className='w-2 text-white sm:w-5' />
                </div>
                <input
                  type='file'
                  ref={fileInputRef}
                  style={{ display: "none" }}
                  onChange={handleFileChange}
                  accept='image/*'
                />
                <div className='absolute top-0 right-0 bottom-0 left-0'>
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/profile_pic_circle.png`}
                    alt='Elipse'
                    width={180}
                    height={180}
                    className='absolute left-2 top-1/2 transform -translate-y-1/2'
                    loading='lazy'
                  />
                </div>
              </div>
              <div className='ml-5'>
                <h3 className='font-bold text-[20px] font-manrope'>
                  {propertyData ? `${propertyData.name}` : ""}
                </h3>
                <div className='px-7 py-2 text-sm text-[#1570EF] font-normal bg-[#C8DAF4] rounded-lg mt-2 flex gap-1'>
                  {travellersCount?.visitorCount || 0} travellers visited
                </div>
              </div>
            </div>
            <div className='flex gap-2'>
              <button
                type='button'
                className='w-10 h-10 bg-blue-600 text-white rounded-lg flex items-center justify-center text-xl'
              >
                <FaRegEdit />
              </button>
              <button
                type='button'
                className='w-10 h-10 bg-blue-600 text-white rounded-lg flex items-center justify-center text-xl'
              >
                <Settings />
              </button>
              <button
                type='button'
                className='w-10 h-10 bg-blue-600 text-white rounded-lg flex items-center justify-center'
              >
                <Trash2 />
              </button>
              <button
                type='button'
                className='w-10 h-10 bg-blue-600 text-white rounded-lg flex items-center justify-center text-xl'
              >
                <GoShareAndroid />
              </button>
            </div>
          </div>

          <div className='grid lg:grid-cols-2 grid-cols-1 gap-3'>
            <div>
              <div className='border border-gray-300 rounded-xl p-5 mt-10'>
                <p className='text-lg font-medium'>Property information</p>
                <div className='flex'>
                  <div className='lg:flex grid mt-5 justify-between w-full gap-2'>
                    <div className='flex gap-2'>
                      <span className='w-12 h-12 rounded-lg bg-[#40E0D033] text-[#40E0D0] flex items-center justify-center text-2xl'>
                        <MapPinned width={20} height={20} alt='' />
                      </span>
                      <h4 className='mb-0'>
                        <Link
                          href=''
                          className='text-base font-bold mb-0 text-black'
                          prefetch={false}
                        >
                          Location
                          <br />
                          <span className='text-gray-400 font-normal text-sm'>
                            {propertyData?.address?.state},{" "}
                            {propertyData?.address?.country}
                          </span>
                        </Link>
                      </h4>
                    </div>
                    <div className='flex gap-2'>
                      <span className='w-12 h-12 rounded-lg bg-[#FF72AD33] text-[#FF72AD] flex items-center justify-center text-2xl'>
                        <HiOfficeBuilding width={20} height={20} alt='' />
                      </span>
                      <h4 className='mb-0'>
                        <Link
                          href=''
                          className='text-base font-bold mb-0 text-black'
                          prefetch={false}
                        >
                          Hostel Type
                          <br />
                          <span className='text-gray-400 font-normal text-sm'>
                            {propertyData?.type}
                          </span>
                        </Link>
                      </h4>
                    </div>
                    <div className='flex gap-2'>
                      <span className='w-12 h-12 rounded-lg bg-[#FFC70033] text-[#FFC700] flex items-center justify-center text-2xl'>
                        <FaStar width={20} height={20} />
                      </span>
                      <h4 className='mb-0'>
                        <Link
                          href=''
                          className='text-base font-bold mb-0 text-black'
                          prefetch={false}
                        >
                          Rating
                          <br />
                          <span className='text-gray-400 font-normal text-sm'>
                            {propertyData?.starRating} Rating
                          </span>
                        </Link>
                      </h4>
                    </div>
                  </div>
                </div>
              </div>
              <div className='border border-gray-300 rounded-xl p-5 mt-5'>
                <p className='text-lg font-medium mb-5'>Profile Settings </p>
                <div className='flex items-center gap-4 mb-5 cursor-pointer'>
                  <span className='w-12 h-12 rounded-lg bg-[#FF72AD33] text-[#FF72AD] flex items-center justify-center text-2xl'>
                    <IoIosListBox width={20} height={20} />
                  </span>
                  <h4 className='mb-0'>
                    <Link
                      href='/owner/dashboard/availability'
                      className='text-base font-bold mb-0'
                      prefetch={false}
                    >
                      Manage Listings
                    </Link>
                  </h4>
                  <Link href='' className='ml-auto' prefetch={false}>
                    <IoIosArrowForward />
                  </Link>
                </div>
                <div className='flex items-center gap-4 mb-5 cursor-pointer'>
                  <span className='w-12 h-12 rounded-lg bg-[#FFAD7233] text-[#FFAD72] flex items-center justify-center text-2xl'>
                    <FaCalendarAlt width={20} height={20} />
                  </span>
                  <h4 className='mb-0'>
                    <Link
                      href='/owner/dashboard/availability'
                      className='text-base font-bold mb-0'
                      prefetch={false}
                    >
                      Availability Calendar
                    </Link>
                  </h4>
                  <Link href='' className='ml-auto' prefetch={false}>
                    <IoIosArrowForward />
                  </Link>
                </div>
                <div className='flex items-center gap-4 mb-5 cursor-pointer'>
                  <span className='w-12 h-12 rounded-lg bg-[#D092F533] text-[#D092F5] flex items-center justify-center text-2xl'>
                    <FaChartPie width={20} height={20} />
                  </span>
                  <h4 className='mb-0'>
                    <Link
                      href='/owner/dashboard/analytics'
                      className='text-base font-bold mb-0'
                      prefetch={false}
                    >
                      Analytics & Insights
                    </Link>
                  </h4>
                  <Link href='' className='ml-auto' prefetch={false}>
                    <IoIosArrowForward />
                  </Link>
                </div>
                <div className='flex items-center gap-4 mb-5 cursor-pointer'>
                  <span className='w-12 h-12 rounded-lg bg-[#40E0D033] text-[#40E0D0] flex items-center justify-center text-2xl'>
                    <MdNotificationsActive width={20} height={20} />
                  </span>
                  <h4 className='mb-0'>
                    <Link
                      // href='/owner/dashboard/noticeboard'
                      href="#"
                      className='text-base font-bold mb-0'
                      prefetch={false}
                    >
                      Notifications
                    </Link>
                  </h4>
                  <Link href='' className='ml-auto' prefetch={false}>
                    <IoIosArrowForward />
                  </Link>
                </div>
                <div className='flex items-center gap-4 mb-5 cursor-pointer'>
                  <span className='w-12 h-12 rounded-lg bg-[#E3EFFF] text-[#72ADFF] flex items-center justify-center text-2xl'>
                    <RiShieldUserFill width={20} height={20} />
                  </span>
                  <h4 className='mb-0'>
                    <Link
                      href=''
                      className='text-base font-bold mb-0'
                      prefetch={false}
                    >
                      Account Security
                    </Link>
                  </h4>
                  <Link href='' className='ml-auto' prefetch={false}>
                    <IoIosArrowForward />
                  </Link>
                </div>
                <div className='flex items-center gap-4 mb-5 cursor-pointer'>
                  <span className='w-12 h-12 rounded-lg bg-[#DE000033] text-[#DE0000] flex items-center justify-center text-2xl'>
                    <RiShieldUserFill width={20} height={20} />
                  </span>
                  <h4 className='mb-0'>
                    <Link
                      href='/owner/accountdeleteowner'
                      target='_blank'
                      className='text-base font-bold mb-0'
                      prefetch={false}
                    >
                      Delete Account / Disconnect Channel
                    </Link>
                  </h4>
                  <Link href='' className='ml-auto' prefetch={false}>
                    <IoIosArrowForward />
                  </Link>
                </div>
              </div>
            </div>
            <div className='grid lg:grid-cols-2 grid-cols-1 mt-10 gap-2'>
              <div className='border border-gray-300 rounded-xl p-5'>
                <div className='flex justify-between'>
                  <p className='text-lg font-medium mb-5'>Recent Bookings</p>
                  <Link
                    href='#'
                    className='text-blue-500 no-underline text-lg font-bold'
                  >
                    See all
                  </Link>
                </div>
                <div className='flex border border-gray-400 rounded-lg p-2 gap-2 mt-2'>
                  <div className='w-16 h-16'>
                    <Image 
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/profile.png`}
                      alt='profile'
                    />
                  </div>
                  <div>
                    <p className='text-black font-bold text-sm'>Elena Sah</p>
                    <p className='text-xs text-black font-medium flex gap-1 mt-2'>
                      <CalendarDays size={15} />{" "}
                      <span>Jun 05, 2024, 2 nights</span>
                    </p>
                    <p className='text-xs text-black font-medium flex gap-1 mt-2'>
                      <HiOutlineUserGroup size={15} /> <span>3 Guest</span>
                    </p>
                  </div>
                </div>
                <div className='flex border border-gray-400 rounded-lg p-2 gap-2 mt-2'>
                  <div className='w-16 h-16'>
                    <Image 
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/profile.png`}
                      alt='profile'
                    />
                  </div>
                  <div>
                    <p className='text-black font-bold text-sm'>Elena Sah</p>
                    <p className='text-xs text-black font-medium flex gap-1 mt-2'>
                      <CalendarDays size={15} />{" "}
                      <span>Jun 05, 2024, 2 nights</span>
                    </p>
                    <p className='text-xs text-black font-medium flex gap-1 mt-2'>
                      <HiOutlineUserGroup size={15} /> <span>3 Guest</span>
                    </p>
                  </div>
                </div>
                <div className='flex border border-gray-400 rounded-lg p-2 gap-2 mt-2'>
                  <div className='w-16 h-16'>
                    <Image 
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/profile.png`}
                      alt='profile'
                    />
                  </div>
                  <div>
                    <p className='text-black font-bold text-sm'>Elena Sah</p>
                    <p className='text-xs text-black font-medium flex gap-1 mt-2'>
                      <CalendarDays size={15} />{" "}
                      <span>Jun 05, 2024, 2 nights</span>
                    </p>
                    <p className='text-xs text-black font-medium flex gap-1 mt-2'>
                      <HiOutlineUserGroup size={15} /> <span>3 Guest</span>
                    </p>
                  </div>
                </div>
                <div className='flex border border-gray-400 rounded-lg p-2 gap-2 mt-2'>
                  <div className='w-16 h-16'>
                    <Image 
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/profile.png`}
                      alt='profile'
                    />
                  </div>
                  <div>
                    <p className='text-black font-bold text-sm'>Elena Sah</p>
                    <p className='text-xs text-black font-medium flex gap-1 mt-2'>
                      <CalendarDays size={15} />{" "}
                      <span>Jun 05, 2024, 2 nights</span>
                    </p>
                    <p className='text-xs text-black font-medium flex gap-1 mt-2'>
                      <HiOutlineUserGroup size={15} /> <span>3 Guest</span>
                    </p>
                  </div>
                </div>
                <div className='flex border border-gray-400 rounded-lg p-2 gap-2 mt-2'>
                  <div className='w-16 h-16'>
                    <Image 
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/profile.png`}
                      alt='profile'
                    />
                  </div>
                  <div>
                    <p className='text-black font-bold text-sm'>Elena Sah</p>
                    <p className='text-xs text-black font-medium flex gap-1 mt-2'>
                      <CalendarDays size={15} />{" "}
                      <span>Jun 05, 2024, 2 nights</span>
                    </p>
                    <p className='text-xs text-black font-medium flex gap-1 mt-2'>
                      <HiOutlineUserGroup size={15} /> <span>3 Guest</span>
                    </p>
                  </div>
                </div>
                <div className='flex border border-gray-400 rounded-lg p-2 gap-2 mt-2'>
                  <div className='w-16 h-16'>
                    <Image 
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/profile.png`}
                      alt='profile'
                    />
                  </div>
                  <div>
                    <p className='text-black font-bold text-sm'>Elena Sah</p>
                    <p className='text-xs text-black font-medium flex gap-1 mt-2'>
                      <CalendarDays size={15} />{" "}
                      <span>Jun 05, 2024, 2 nights</span>
                    </p>
                    <p className='text-xs text-black font-medium flex gap-1 mt-2'>
                      <HiOutlineUserGroup size={15} /> <span>3 Guest</span>
                    </p>
                  </div>
                </div>
              </div>
              <div className='border border-gray-300 rounded-xl p-5'>
                <div className='flex justify-between'>
                  <p className='text-lg font-medium mb-5'>Recent Reviews</p>
                  <Link
                    href='#'
                    className='text-blue-500 no-underline text-lg font-bold'
                  >
                    See all
                  </Link>
                </div>
                <div>
                  <div className='relative bg-[#CDF0ED] w-full p-5 rounded-lg shadow-lg'>
                    <div className='flex items-center mb-4'>
                      <Image 
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/profile.png`}
                        alt='User Image'
                        className='w-12 h-12 rounded-full border border-white shadow-md'
                      />
                      <div className='ml-3'>
                        <h3 className='text-black font-bold'>Steve H.</h3>
                        <p className='text-black/80 text-sm'>Marketing Manager</p>
                      </div>

                      <div className='ml-auto flex items-center'>
                        <span className='text-yellow-400 text-lg'>★★★★★</span>
                        <span className='text-gray-400 text-lg'>★</span>
                      </div>
                    </div>

                    <p className='text-black text-sm'>
                      "I've been consistently impressed with the quality of
                      service provided by this website. They have exceeded my
                      expectations and delivered exceptional results. Highly
                      recommended!"
                    </p>

                    <div className='absolute bottom-0 left-6 w-6 h-6 bg-[#CDF0ED] rotate-45 transform translate-y-3'></div>
                  </div>
                  <div className='relative bg-[#CDF0ED] w-full p-5 rounded-lg shadow-lg mt-5'>
                    <div className='flex items-center mb-4'>
                      <Image 
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/profile.png`}
                        alt='User Image'
                        className='w-12 h-12 rounded-full border border-white shadow-md'
                      />
                      <div className='ml-3'>
                        <h3 className='text-black font-bold'>Steve H.</h3>
                        <p className='text-black/80 text-sm'>Marketing Manager</p>
                      </div>

                      <div className='ml-auto flex items-center'>
                        <span className='text-yellow-400 text-lg'>★★★★★</span>
                        <span className='text-gray-400 text-lg'>★</span>
                      </div>
                    </div>

                    <p className='text-black text-sm'>
                      "I've been consistently impressed with the quality of
                      service provided by this website. They have exceeded my
                      expectations and delivered exceptional results. Highly
                      recommended!"
                    </p>

                    <div className='absolute bottom-0 left-6 w-6 h-6 bg-[#CDF0ED] rotate-45 transform translate-y-3'></div>
                  </div>
                  <div className='relative bg-[#CDF0ED] w-full p-5 rounded-lg shadow-lg mt-5'>
                    <div className='flex items-center mb-4'>
                      <Image 
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/profile.png`}
                        alt='User Image'
                        className='w-12 h-12 rounded-full border border-white shadow-md'
                      />
                      <div className='ml-3'>
                        <h3 className='text-black font-bold'>Steve H.</h3>
                        <p className='text-black/80 text-sm'>Marketing Manager</p>
                      </div>

                      <div className='ml-auto flex items-center'>
                        <span className='text-yellow-400 text-lg'>★★★★★</span>
                        <span className='text-gray-400 text-lg'>★</span>
                      </div>
                    </div>

                    <p className='text-black text-sm'>
                      "I've been consistently impressed with the quality of
                      service provided by this website. They have exceeded my
                      expectations and delivered exceptional results. Highly
                      recommended!"
                    </p>

                    <div className='absolute bottom-0 left-6 w-6 h-6 bg-[#CDF0ED] rotate-45 transform translate-y-3'></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* <div className="max-w-[525px] mx-auto">
            <div className="justify-center w-full gap-y-6 text-center">
              <div className="mx-auto w-10 sm:w-14 h-10 sm:h-14 md:w-20 md:h-20 mmd:w-[110px] mmd:h-[110px] rounded-full relative">
                <Image
                  src={
                    profileData?.profileImage?.objectURL
                      ? profileData?.profileImage?.objectURL
                      : `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/avatar.png`
                  }
                  alt={propertyData?.name || "Property"}
                  className="w-full h-full rounded-full"
                  width={500}
                  height={500}
                  loading="lazy"
                /> 
                <div
                  className="absolute flex items-center justify-center w-5 h-5 rounded-full cursor-pointer sm:w-8 sm:h-8 -bottom-0 -right-0 bg-primary-blue"
                  onClick={() => fileInputRef.current.click()}
                >
                  <Camera size={20} className="w-2 text-white sm:w-5" />
                </div>
                <input
                  type="file"
                  ref={fileInputRef}
                  style={{ display: "none" }}
                  onChange={handleFileChange}
                  accept="image/*"
                />
              </div>
               <h3 className="font-bold text-27px font-manrope text-center mt-5">
                {propertyData ? `${propertyData.name}` : ""}
              </h3>
              <h6 className='text-sm font-normal text-center'>{data ? `${data.name}` : ""}</h6>
            </div>
            <div className="items-center flex justify-center gap-2 mt-5 pb-8">
              <Link
                href=""
                className="w-10 h-10 rounded-full border border-[#D6D6D6] flex items-center justify-center hover:bg-primary-blue hover:border-primary-blue hover:text-white"
                prefetch={false}
              >
                <FiPhone />
              </Link>
              <Link
                href=""
                className="w-10 h-10 rounded-full border border-[#D6D6D6] flex items-center justify-center hover:bg-primary-blue hover:border-primary-blue hover:text-white"
                prefetch={false}
              >
                <BiMessageDetail />
              </Link>
              <Link
                href=""
                className="w-10 h-10 rounded-full border border-[#D6D6D6] flex items-center justify-center hover:bg-primary-blue hover:border-primary-blue hover:text-white"
                prefetch={false}
              >
                <IoCameraOutline />
              </Link>
            </div>

            <div className="py-6 border-t border-[#D6D6D6] px-4">
              <h4 className="text-base font-bold mb-4 sm:mb-6">
                Profile information
              </h4>
              <button className="border border-[#FF72AD] rounded-lg w-full gap-4 flex items-center px-6 py-6 bg-[#FFE3EF]">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/travel.svg`}
                  alt=""
                  width={26}
                  height={30}
                  loading="lazy"
                />
                <span className="underline text-sm text-[#FF72AD]">
                  {`Travellers Visited (${travellersCount?.visitorCount})`}
                </span>
                <IoIosArrowDropdownCircle className="ml-auto text-[#FF72AD] text-2xl" />
              </button>
            </div>
            <div className="py-6 border-t border-[#D6D6D6] px-4">
              <h4 className="text-base font-bold mb-4 sm:mb-6">Highlights</h4>
              <div className="flex items-center flex-wrap gap-2">
                {propertyData?.photos?.map((photo, index) => (
                  <Image
                    key={index}
                    src={`https://${photo?.url}`}
                    alt={`Highlight ${index + 1}`}
                    className="rounded-sm w-[92px] h-[92px] object-cover"
                    width={50}
                    height={50}
                    loading="lazy"
                  />
                ))}
              </div>
            </div>
            <div className="py-6 border-t border-[#D6D6D6] px-4">
              <div className="flex items-center gap-4  mb-5">
                <span className="w-12 h-12 rounded-lg bg-[#E3EFFF] text-[#72ADFF] flex items-center justify-center text-2xl">
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/disMsg.svg`}
                    width={20}
                    height={20}
                    alt=""
                    loading="lazy"
                  />
                </span>
                <h4 className="mb-0">
                  <Link
                    href=""
                    className="text-base font-bold mb-0 text-[#72ADFF]"
                    prefetch={false}
                  >
                    Disappearing Message{" "}
                  </Link>
                </h4>
                <Link href="" className="ml-auto" prefetch={false}>
                  <IoIosArrowForward />
                </Link>
              </div>

              <div className="flex items-center gap-4 mb-5">
                <span className="w-12 h-12 rounded-lg bg-[#FFE3EF] text-[#FF72AD] flex items-center justify-center text-2xl">
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/clearChat.svg`}
                    width={20}
                    height={20}
                    alt=""
                    loading="lazy"
                  />
                </span>
                <h4 className="mb-0">
                  <Link
                    href=""
                    className="text-base font-bold mb-0 text-[#FF72AD]"
                    prefetch={false}
                  >
                    Clear Chat
                  </Link>
                </h4>
                <Link href="" className="ml-auto" prefetch={false}>
                  <IoIosArrowForward />
                </Link>
              </div>

              <div className="flex items-center gap-4  mb-5">
                <span className="w-12 h-12 rounded-lg bg-[#F6D2D6] text-[#D32030] flex items-center justify-center text-2xl">
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/blockUser.svg`}
                    width={20}
                    height={20}
                    alt=""
                    loading="lazy"
                  />
                </span>
                <h4 className="mb-0">
                  <Link
                    href=""
                    className="text-base font-bold mb-0 text-[#D32030]"
                    prefetch={false}
                  >
                    Block Alex Lee
                  </Link>
                </h4>
                <Link href="" className="ml-auto" prefetch={false}>
                  <IoIosArrowForward />
                </Link>
              </div>
            </div>
          </div> */}

          {/* <div className="flex items-center justify-center text-white bg-primary-blue rounded-lg cursor-pointer w-9 h-9">
            <FaRegEdit
              size={20}
              className="text-white"
              onClick={() => setShowEdit(true)}
            />
          </div> */}
          {/* <div className="flex flex-wrap justify-between w-full px-3 mt-5 md:px-5 mmd:px-7">
          <div className="w-full mmd:w-[16%] bg-slate-240 rounded-lg p-3 mmd:p-7 text-base font-inter">
            <ul className="flex flex-row flex-wrap gap-y-2 mmd:gap-y-0 gap-x-4 mmd:gap-x-0 mmd:flex-col ">
              {tabs.map((tab) => (
                <li
                  className={`relative pb-3 w-fit mmd:mt-6 cursor-pointer first:mt-0 text-sm mmd:text-base font-medium ${
                    activeTab === tab.id
                      ? "text-primary-blue"
                      : "text-slate-320"
                  }`}
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                >
                  {tab.name}
                  {activeTab === tab.id && (
                    <div className="absolute bottom-0 w-full h-1 rounded-full bg-primary-blue"></div>
                  )}
                </li>
              ))}
            </ul>
          </div>
          <div className="w-full mmd:w-[82%]">{renderTabContent()}</div>
        </div> */}
        </div>
      </section>
    </>
  );
};

export default Profile;
