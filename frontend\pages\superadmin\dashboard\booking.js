"use client";
import { Plus, Search } from "lucide-react";
import React from "react";
import { FiEye } from "react-icons/fi";
import { TfiPencilAlt } from "react-icons/tfi";
import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight,
} from "react-icons/md";
import BookingFilter from "@/components/superadmin/BookingFilter";
import Link from "next/link";

const Booking = () => {
  const usersData = [
    {
      id: 1,
      name: "Stuart Coats",
      hostelname: `88 Backpackers`,
      bookpre: 25364125,
      checkIn: `04/02/24`,
      checkOut: `09/02/24`,
      country: "UK",
      payStatus: "2500 / 5000",
      status: "Booked",
    },
    {
      id: 2,
      name: "Savannah Nguyen",
      hostelname: `88 Backpackers`,
      bookpre: 25364125,
      checkIn: `04/02/24`,
      checkOut: `09/02/24`,
      country: "INDIA",
      payStatus: "1000000",
      status: "Cancelled",
    },
    {
      id: 3,
      name: "<PERSON> Nguyen",
      hostelname: `88 Backpackers`,
      bookpre: 25364125,
      checkIn: `04/02/24`,
      checkOut: `09/02/24`,
      country: "INDIA",
      payStatus: "30000/36000",
      status: "Processing",
    },
    {
      id: 4,
      name: "Savannah Nguyen",
      hostelname: `88 Backpackers`,
      bookpre: 25364125,
      checkIn: `04/02/24`,
      checkOut: `09/02/24`,
      country: "INDIA",
      payStatus: "50000",
      status: "Booked",
    },
    {
      id: 5,
      name: "Savannah Nguyen",
      hostelname: `88 Backpackers`,
      bookpre: 25364125,
      checkIn: `04/02/24`,
      checkOut: `09/02/24`,
      country: "INDIA",
      payStatus: "40000",
      status: "On hold",
    },
  ];

  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <div className="flex items-center justify-between w-full ">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">Booking</h2>
        <div className="w-[70%] md:w-[50%] lg:w-[50%] gap-x-3 md:gap-x-5 lg:gap-x-5 flex justify-end items-center">
          <div className="relative w-28 lg:w-fit">
            <input
              type="search"
              className="relative bg-white flex items-center justify-center w-full py-2 pl-10 pr-4 text-xs font-normal border border-gray-200 rounded-full outline-none text-gray-130 h-9 dark:bg-transparent"
              placeholder="Search hostel , Country etc.."
            />
            <Search
              className="absolute -translate-y-1/2 top-1/2 left-3 text-gray-130 dark:text-[#757575]"
              size={20}
            />
          </div>
          <Link href={"/superadmin/dashboard/booking-add"}
            className={`w-[100px] lg:w-[130px] px-2 lg:px-4 py-2 text-xs font-normal text-white rounded relative flex justify-center items-center h-9 bg-sky-blue-650 `}
            type="button"
            // onClick={() => setOpenAddHostel(true)}
          >
            <Plus size={18} className="mr-1" /> Add Booking
          </Link>
        </div>
      </div>
      <div className="mt-5">
        <BookingFilter />
      </div>
      <div className="overflow-x-auto mt-5 rounded-xl border dark:border-none">
        <table className="w-full max-w-full divide-y bg-white rounded-xl divide-gray-200 dark:bg-black">
          <thead>
            <tr>
              <th className="pl-14 pb-6 md:pb-11 lg:pb-6 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                NAME
              </th>
              <th className="pl-10  pb-6 md:pb-11 lg:pb-6 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                HOSTEL NAME
              </th>
              <th className="pl-8 pr-4 pb-3 md:pb-6 lg:pb-1 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                BOOKING <p className="text-sm font-poppins font-semibold dark:bg-black dark:text-[#B6B6B6]">PREFERENCES</p>
              </th>
              <th className="pr-6 pl-8 pt-8 pb-12 md:pb-14 lg:pt-5 lg:pb-7 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider whitespace-nowrap dark:bg-black dark:text-[#B6B6B6]">
                CHECK IN & <p className="text-sm font-poppins font-semibold dark:bg-black dark:text-[#B6B6B6]">CHECK OUT</p>
              </th>
              <th className="pr-4 pl-7 pb-8 md:pb-11 lg:pb-7 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                COUNTRY
              </th>
              <th className=" pl-10 pr-4 pb-4 md:pb-6 lg:pb-3 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                PAYMENT <p className="text-sm font-poppins font-semibold dark:bg-black dark:text-[#B6B6B6]">STATUS</p>
              </th>
              <th className="pl-10 pr-4 pb-8 md:pb-11 lg:pb-6 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                STATUS
              </th>
              <th className="pl-5 pb-8 md:pb-11 lg:pb-6 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                ACTION
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 border-x border-y dark:border-x-0 text-black/70 dark:text-[#757575]">
            {usersData.map((user) => (
              <tr key={user.id}>
                <td className="whitespace-nowrap px-8 text-sm font-medium font-poppins">{user.name}</td>
                <td className="whitespace-nowrap px-8 text-sm font-medium font-poppins">{user.hostelname}</td>
                <td className="whitespace-nowrap px-8 text-sm font-medium font-poppins pl-10">{user.bookpre}</td>
                <td className="whitespace-nowrap px-8 text-sm font-medium font-poppins pt-4 pl-10">
                  {user.checkIn}
                  <p className=" whitespace-nowrap text-sm font-medium font-poppins">{user.checkOut}</p>
                </td>
                <td className="whitespace-nowrap px-8 text-sm font-medium font-poppins pl-12">{user.country}</td>
                <td className="whitespace-nowrap px-8 text-sm font-medium font-poppins">{user.payStatus}</td>
                <td className="whitespace-nowrap px-8 text-sm font-medium font-poppins">
                  <button
                    className={`py-1 px-3 rounded ${
                      user.status === "Booked"
                        ? "text-primary-blue bg-[#CCEFED] dark:bg-[#2963608a]"
                        : user.status === "Processing"
                        ? "text-purple-600 bg-purple-100 dark:bg-[#3723578a]"
                        : user.status === "On hold"
                        ? "text-orange-500 bg-orange-100 dark:bg-[#7d472f8a]"
                        : "text-red-600 bg-red-100 dark:bg-[#6b28288a]"
                    } font-medium`}
                  >
                    {user.status}
                  </button>
                </td>
                <td className="py-5 px-5 flex">
                  <Link
                    href="/superadmin/dashboard/booking-detail"
                    className="border p-2 rounded-l-lg hover:text-blue-700 text-black/75  dark:text-[#757575] dark:hover:text-blue-700"
                    prefetch={false}
                  >
                    <FiEye />
                  </Link>
                  <Link href="/superadmin/dashboard/booking-edit" className="hover:text-yellow-400 border p-2 rounded-r-lg text-black/75 dark:text-[#757575] dark:hover:text-yellow-400">
                    <TfiPencilAlt />
                  </Link>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {/* <div className="flex justify-between items-center mt-5">
        <div className="text-black/75">Showing 1-09 of 78</div>
        <div className="inline-flex items-center justify-center border rounded-xl bg-white">
          <a
            href="#"
            className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180"
          >
            <span className="sr-only">Next Page</span>
            <MdOutlineKeyboardArrowLeft />
          </a>

          <a
            href="#"
            className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180"
          >
            <span className="sr-only">Next Page</span>
            <MdOutlineKeyboardArrowRight />
          </a>
        </div>
      </div> */}

       <div className="flex justify-between items-center mt-5">
                    <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
                    <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
                      <a
                        href="#"
                        className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                      >
                        <span className="sr-only">Next Page</span>
                        <MdOutlineKeyboardArrowLeft />
                      </a>
            
                      <a
                        href="#"
                        className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                      >
                        <span className="sr-only">Next Page</span>
                        <MdOutlineKeyboardArrowRight />
                      </a>
                    </div>
                  </div>
    </div>
  );
};

export default Booking;
