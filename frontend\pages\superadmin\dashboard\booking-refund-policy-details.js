/* eslint-disable react/no-unescaped-entities */
import <PERSON> from "next/link";
import React from "react";

const RefundDetails = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 dark:bg-[#171616] overflow-y-auto scroll-smooth ">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        Booking Refund Details
      </h2>
      <div className="bg-white border rounded-xl mt-5 h-auto dark:bg-black dark:border-none">
        <div className="p-8 flex flex-col gap-y-6">
          <div className="">
            <div className="flex ">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  General terms and conditions
                </h1>
                <div>
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • All guests must be at least 18 years of age to check-in at
                    MixDorm.{" "}
                  </p>
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • Valid photo identification is mandatory at the time of
                    check-in. Acceptable IDs include Passport, Aadhar Card,
                    Driving License, and Voter ID card. PAN Card is not{" "}
                    accepted. Guests with local IDs will not be admitted.{" "}
                  </p>{" "}
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • MixDorm is designed for solo travelers and young
                    backpackers; families are encouraged to consider more
                    family-oriented accommodations.{" "}
                  </p>{" "}
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • Advance payments made at the time of reservation are
                    non-refundable.{" "}
                  </p>{" "}
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • Full prepayment is required at the time of check-in.{" "}
                  </p>{" "}
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • Non-resident guests are not allowed inside the rooms; they
                    can be met in the common areas.{" "}
                  </p>{" "}
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • For groups of 3 or 4, accommodation in the same dorm room
                    is not guaranteed.{" "}
                  </p>{" "}
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • Different destinations and properties may have specific
                    policies during certain times of the year.{" "}
                  </p>{" "}
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • Guests are responsible for their personal belongings.{" "}
                  </p>{" "}
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • Outside pets are not allowed.{" "}
                  </p>{" "}
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • Breakfast is not included in the room rate and is charged
                    separately at the property.{" "}
                  </p>
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • The hostel reserves the right to take appropriate action
                    against any guest whose behavior is deemed inappropriate.{" "}
                  </p>{" "}
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • Right to admission is reserved.{" "}
                  </p>{" "}
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • Certain policies are specific to individual bookings and
                    will be notified at the time of booking.{" "}
                  </p>{" "}
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • Policies apply unless otherwise specified on the
                    individual destination's booking page.{" "}
                  </p>
                </div>
              </div>
              <div className="">
                <Link href={"/superadmin/dashboard/edit-booking-refund"} className="text-white text-sm font-poppins py-1 md:py-2 lg:py-2 px-6 lg:px-8   rounded bg-sky-blue-650">
                  Edit
                </Link>
              </div>
            </div>
            <div className="flex w-[90%]">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  Booking extantion policy
                </h1>
                <div>
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • Extensions of stay are subject to current room rates and
                    availability.
                  </p>

                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • Current room rates may differ from the rates at which the
                    rooms were originally booked.
                  </p>
                </div>
              </div>
            </div>
            <div className="flex w-[90%]">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  Cancellation Policy
                </h1>
                <div>
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • Advance payments made at the time of reservation are
                    non-refundable.
                  </p>

                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • Full prepayment of the remaining amount is required at
                    check-in.
                  </p>

                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • Policies apply unless otherwise specified on the
                    individual destination's booking page.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-start justify-start p-8">
          <Link
            href={"/superadmin/dashboard/booking-refund-policy"}
            className="text-white py-2 w-32 max-w-md rounded bg-sky-blue-650 flex items-center justify-center"
          >
            Cancel
          </Link>
        </div>
      </div>
    </div>
  );
};

export default RefundDetails;
