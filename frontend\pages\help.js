 
import React from "react";
import dynamic from "next/dynamic";
import Head from "next/head";
const HeroNavbar = dynamic(() => import("@/components/footer/heroNavBar"), {
  ssr: false,
});

const Help = () => {
  return (
    <>
      <Head>
        <title>Help & Support | Mixdorm Hostel Booking</title>
        <meta
          name="description"
          content="Get Assistance with Your Booking, Check-In, Technical Issues, and More. Reach Out to Mixdorm’s Support Team Anytime."
        />
      </Head>
      <HeroNavbar />
      <div className='bg-white pb-4 md:pb-[10rem] font-manrope md:pt-12 pt-8'>
        <div className='container md:px-4 lg:px-0 xl:px-0 md:pr-8 lg:pr-10 xl:pr-20'>
          <h2 className='font-bold  text-black font-manrope md:mb-8 mb-5 md:text-[35px] sm:text-xl text-xl'>
            Help
          </h2>
          <h3 className='font-bold md:text-xl text-lg'>
            How Can We Assist You Today?
          </h3>
          <p className='text-sm text-gray-500 my-4'>
            At MixDorm, we’re here to make your experience as smooth and
            enjoyable as possible. Whether you need help with a booking, have
            questions about our services, or need support during your stay, our
            Help page provides resources and contact information to get you the
            assistance you need.
          </p>

          <div className='mb-8'>
            <h3 className='font-bold mb-4 md:text-xl text-lg'>
              Common Issues and Solutions
            </h3>
            <ol className='list-decimal pl-8'>
              <li className='text-lg font-bold mb-4'>
                Booking Assistance
                <div className='my-4'>
                  <p className='text-sm text-gray-500'>
                    <strong className='text-black'>Problem:</strong> I need to
                    modify or cancel my booking.
                  </p>
                  <p className='text-sm text-gray-500'>
                    <strong className='text-black'>Solution:</strong> You can
                    modify or cancel your booking by logging into your account
                    on our website or app. For further assistance, please
                    contact our customer support team.
                  </p>
                </div>
              </li>

              <li className='text-lg font-bold mb-4'>
                Check-In and Check-Out Details
                <div className='my-4'>
                  <p className='text-sm text-gray-500'>
                    <strong className='text-black'>Problem:</strong> I have a
                    question about check-in or check-out times.
                  </p>
                  <p className='text-sm text-gray-500'>
                    <strong className='text-black'>Solution:</strong> Standard
                    check-in is at 12 noon, and check-out is at 10 AM. Early
                    check-in or late check-out may be available upon request.
                    Contact the property directly for availability.
                  </p>
                </div>
              </li>

              <li className='text-lg font-bold mb-4'>
                Lost and Found
                <div className='my-4'>
                  <p className='text-sm text-gray-500'>
                    <strong className='text-black'>Problem:</strong> I lost
                    something during my stay.
                  </p>
                  <p className='text-sm text-gray-500'>
                    <strong className='text-black'>Solution:</strong> Report
                    lost items to our customer support team as soon as possible.
                    We will make every effort to locate and return lost
                    belongings.
                  </p>
                </div>
              </li>

              <li className='text-lg font-bold mb-4'>
                Technical Support
                <div className='my-4'>
                  <p className='text-sm text-gray-500'>
                    <strong className='text-black'>Problem:</strong> I’m having
                    trouble using the website or app.
                  </p>
                  <p className='text-sm text-gray-500 '>
                    <strong className='text-black'>Solution:</strong> Try
                    clearing your browser cache or updating the app. If the
                    issue persists, contact our technical support team for help.
                  </p>
                </div>
              </li>
            </ol>
          </div>
          <h3 className='font-bold mb-4 md:text-xl text-lg'>Contact Us</h3>
          <div className='my-4'>
            <p className='text-sm text-gray-500'>
              If you need help that isn’t covered here, our customer support
              team is ready to assist you:
            </p>
            <p className='text-sm text-gray-500'>Email: <EMAIL></p>
          </div>
          <h3 className='font-bold mb-4 md:text-xl text-lg'>
            Support Hours - Our customer support team is available{" "}
          </h3>
          <div className='my-4'>
            <p className='text-sm text-gray-500'>Monday-Friday: 9 AM - 6 PM</p>
            <p className='text-sm text-gray-500'>
              Saturday-Sunday: 10 AM - 4 PM
            </p>
          </div>

          <h3 className='font-bold mb-4 md:text-xl text-lg'>Contact Us</h3>

          <div className='my-4'>
            <p className='text-sm text-gray-500'>
              If you need help that isn’t covered here, our customer support
              team is ready to assist you:
            </p>

            <p className='text-sm text-gray-500'>Email: <EMAIL></p>
          </div>

          <h3 className='font-bold mb-4 md:text-xl text-lg'>
            Support Hours - Our customer support team is available{" "}
          </h3>

          <div className='my-4'>
            <p className='text-sm text-gray-500'>Monday-Friday: 9 AM - 6 PM</p>
            <p className='text-sm text-gray-500'>
              Saturday-Sunday: 10 AM - 4 PM
            </p>
          </div>

          <p className='text-sm text-gray-500 my-4'>
            For urgent matters outside of these hours, please leave a message or
            send an email, and we will respond as soon as possible.
          </p>

          <h3 className='font-bold mb-4 md:text-xl text-lg'>
            Feedback and Suggestions
          </h3>

          <p className='text-sm text-gray-500 my-4'>
            We value your feedback and are always looking for ways to improve.
            If you have any suggestions or comments, please feel free to share
            them with us:
          </p>
          <p className='text-sm text-gray-500 my-4'>
            Email: <EMAIL>
          </p>
          <h3 className='font-bold mb-4 md:text-xl text-lg'>Resources</h3>
          <h3 className='font-bold mb-4 md:text-xl text-lg'>Help Center</h3>

          <ul className='list-disc pl-8'>
            <li className='text-sm text-gray-500 '>
              {" "}
              Access our detailed guides and troubleshooting tips in the Help
              Center.
              <br /> Community Forum
            </li>
            <li className='text-sm text-gray-500 '>
              {" "}
              Join our Community Forum to connect with other travelers and share
              your experiences.
            </li>
          </ul>
          <p className='text-sm text-gray-500 my-4'>
            Thank you for choosing MixDorm. We’re here to ensure you have a
            great stay, so don’t hesitate to reach out if you need assistance!
            Feel free to tailor the content and contact details to match
            MixDorm’s specific needs and support structure.
          </p>
        </div>
      </div>
    </>
  );
};

export default Help;
