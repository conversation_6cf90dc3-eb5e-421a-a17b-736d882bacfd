// "use client";
// import React, { useEffect, useRef, useState } from "react";
// import { FaChevronDown, FaChevronUp } from "react-icons/fa";
// import { SlCalender } from "react-icons/sl";
// import { Go<PERSON>lock } from "react-icons/go";
// import { TbCalendarClock } from "react-icons/tb";
// import { LuClock3 } from "react-icons/lu";
// import Link from "next/link";
// import { FaCalendarDays } from "react-icons/fa6";

// const AnnouncementNotification = () => {
//   const [notificationType, setNotificationType] = useState("");
//   const [selectedNotification, setSelectedNotification] = useState("Select Notification");
//   const [recipients, setRecipients] = useState("");
//   const [selectedRecipients, setSelectedRecipients] = useState("Select Recipients");
//   const [announcementType, setAnnouncementType] = useState("");
//   const [selectedAnnouncement, setSelectedAnnouncement] = useState("Select Announcement");
//   const [sendType, setSendType] = useState("");
//   const [selectedSend, setSelectedSend] = useState("Select Send");

//   const [isDropdownOpen, setIsDropdownOpen] = useState({
//     notificationType: false,
//     recipients: false,
//     announcementType: false,
//     sendType: false,
//   });

//    const notificationRef = useRef(null);
//     const recipientsRef = useRef(null);
//     const announcementRef = useRef(null);
//     const sendRef = useRef(null);

//     // Function to close all dropdowns
//     const closeAllDropdowns = () => {
//       setNotificationType(false);
//       setRecipients(false);
//       setAnnouncementType(false);
//       setSendType(false);
//     };
//       // Effect to handle outside click
//       useEffect(() => {
//         const handleClickOutside = (event) => {
//           if (
//             notificationRef.current &&
//             !notificationRef.current.contains(event.target) &&
//             recipientsRef.current &&
//             !recipientsRef.current.contains(event.target) &&
//             announcementRef.current &&
//             !announcementRef.current.contains(event.target) &&
//             sendRef.current &&
//             !sendRef.current.contains(event.target)
//           ) {
//             closeAllDropdowns();
//           }
//         };

//         document.addEventListener("click", handleClickOutside);
//         return () => {
//           document.removeEventListener("click", handleClickOutside);
//         };
//       }, []);

//   const notificationOptions = [
//     "Notification Type 1",
//     "Notification Type 2",
//     "Notification Type 3",
//   ];
//   const recipientOptions = ["User", "Admin", "Hostel Owner"];
//   const announcementOptions = ["Any one", "Specific group"];
//   const sendOptions = ["Schedule", "Immediate"];

//   const toggleDropdown = (key) => {
//     setIsDropdownOpen((prev) => ({
//       ...prev,
//       [key]: !prev[key],
//     }));
//   };

//   const handleSelect = (key, option) => {
//     if (key === "notificationType") setNotificationType(notificationOptions);
//     if (key === "recipients") setRecipients(option);
//     if (key === "announcementType") setAnnouncementType(option);
//     if (key === "sendType") setSendType(option);
//     toggleDropdown(key);
//     closeAllDropdowns();
//   };

//     // Toggle dropdown functions
//     const toggleNotificationDropdown = () => {
//       closeAllDropdowns();
//       setNotificationType(!notificationType);
//     };

//     const toggleRecipentsDropdown = () => {
//       closeAllDropdowns();
//       setRecipients(!recipients);
//     };

//     const toggleAnnouncementDropdown = () => {
//       closeAllDropdowns();
//       setAnnouncementType(!announcementType);
//     };

//     const toggleSendDropdown = () => {
//       closeAllDropdowns();
//       setSendType(!sendType);
//     };

//   return (
// <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 dark:bg-[#171616] overflow-y-auto scroll-smooth">
//   <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
//     Announcement Notification
//   </h2>

//   <div className="bg-white border mt-5 p-6 md:p-10 rounded-xl dark:bg-black dark:border-none">
//     <div className="bg-white w-full max-w-3xl dark:bg-black">
//       <form>
//         {/* <h2 className="text-xl font-bold font-poppins mb-5 dark:text-gray-100">
//           Announcement Notification
//         </h2> */}

//         {/* Adjusted for small & medium screen responsiveness */}
//         <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//           {/* Notification Type Dropdown */}
//           <div className="relative" ref={notificationRef}>
//             <label className="text-sm font-poppins text-black/40 font-semibold mb-2 block dark:text-[#B6B6B6]">
//               Notification type
//             </label>
//             <button
//               type="button"
//               onClick={toggleNotificationDropdown}
//               className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-poppins font-medium dark:bg-transparent dark:text-[#757575] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
//             >
//               {selectedNotification}
//               {notificationType ? (
//                 <FaChevronUp className="dark:text-[#757575]"/>
//               ) : (
//                 <FaChevronDown className="dark:text-[#757575]"/>
//               )}
//             </button>
//             {notificationType && (
//               <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg text-sm dark:bg-black">
//                 {notificationOptions.map((option, index) => (
//                   <li
//                     key={index}
//                     className="px-4 py-2 cursor-pointer hover:bg-[#EEF9FF] font-poppins font-medium text-sm dark:text-[#757575] dark:hover:bg-[#393939b7]"
//                     onClick={() => handleSelect("notificationType", option)}
//                   >
//                     {option}
//                   </li>
//                 ))}
//               </ul>
//             )}
//           </div>

//           {/* Recipients Dropdown */}
//           <div className="relative" ref={recipientsRef}>
//             <label className="text-sm font-poppins text-black/40 font-semibold mb-2 block dark:text-[#B6B6B6]">
//               Recipients
//             </label>
//             <button
//               type="button"
//               onClick={toggleRecipentsDropdown}
//               className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-poppins font-medium dark:bg-transparent dark:text-[#757575] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
//             >
//               {selectedRecipients}
//               {recipients ? (
//                 <FaChevronUp className="dark:text-[#757575]"/>
//               ) : (
//                 <FaChevronDown className="dark:text-[#757575]"/>
//               )}
//             </button>
//             {recipients && (
//               <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg dark:bg-black ">
//                 {recipientOptions.map((option, index) => (
//                   <li
//                     key={index}
//                     className="px-4 py-2 cursor-pointer hover:bg-[#EEF9FF] font-poppins font-medium text-sm dark:text-[#757575] dark:hover:bg-[#393939b7]"
//                     onClick={() => handleSelect("recipients", option)}
//                   >
//                     {option}
//                   </li>
//                 ))}
//               </ul>
//             )}
//           </div>
//         </div>

//         {/* Title */}
//         <div className="grid grid-cols-1 gap-6 mt-4">
//           <div>
//             <label className="text-sm font-poppins text-black/40 font-semibold mb-2 block dark:text-[#B6B6B6]">
//               Title
//             </label>
//             <input
//               type="text"
//               placeholder="Title"
//               className=" bg-[#EEF9FF] border  px-4 py-2 rounded-md text-sm font-poppins font-medium dark:bg-transparent dark:text-[#757575] w-[49%] dark:border-white focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
//               value="4.5"
//             />
//           </div>
//         </div>

//         {/* Announcement & Schedule */}
//         <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
//           {/* Announcement Visibility */}
//           <div className="relative" ref={announcementRef}>
//             <label className="text-sm font-poppins text-black/40 font-semibold mb-2 block dark:text-[#B6B6B6]">
//               Who can see this announcement?
//             </label>
//             <button
//               type="button"
//               onClick={toggleAnnouncementDropdown}
//               className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-poppins font-medium dark:bg-transparent dark:text-[#757575] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
//             >
//               {selectedAnnouncement}
//               {announcementType ? (
//                 <FaChevronUp className="dark:text-[#757575]"/>
//               ) : (
//                 <FaChevronDown className="dark:text-[#757575]"/>
//               )}
//             </button>
//             {announcementType && (
//               <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg dark:bg-black">
//                 {announcementOptions.map((option, index) => (
//                   <li
//                     key={index}
//                     className="px-4 py-2 cursor-pointer hover:bg-[#EEF9FF] font-poppins font-medium text-sm dark:text-[#757575] dark:hover:bg-[#393939b7]"
//                     onClick={() => handleSelect("announcementType", option)}
//                   >
//                     {option}
//                   </li>
//                 ))}
//               </ul>
//             )}
//           </div>

//           {/* Schedule Announcement */}
//           <div className="relative" ref={sendRef}>
//             <label className="text-sm font-poppins text-black/40 font-semibold mb-2 mt-[0px] md:mt-[19px] lg:mt-[0px] block dark:text-[#B6B6B6]">
//               Send this Announcement
//             </label>
//             <button
//               type="button"
//               onClick={toggleSendDropdown}
//               className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-poppins font-medium dark:bg-transparent dark:text-[#757575] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
//             >
//               {selectedSend}
//               {sendType ? (
//                 <FaChevronUp className="dark:text-[#757575]"/>
//               ) : (
//                 <FaChevronDown className="dark:text-[#757575]"/>
//               )}
//             </button>
//             {sendType && (
//               <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg dark:bg-black">
//                 {sendOptions.map((option, index) => (
//                   <li
//                     key={index}
//                     className="px-4 py-2 cursor-pointer hover:bg-[#EEF9FF] font-poppins font-medium text-sm dark:text-[#757575] dark:hover:bg-[#393939b7] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
//                     onClick={() => handleSelect("sendType", option)}
//                   >
//                     {option}
//                   </li>
//                 ))}
//               </ul>
//             )}
//           </div>
//         </div>

//                     {/* Announcement & Schedule */}
//                     <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">

//           <div className="relative">
//             <label className="text-sm font-poppins text-black/40 font-semibold mb-2 block dark:text-[#B6B6B6]">
//               Check-in-Date
//             </label>
//             <button
//               type="button"

//               className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-poppins font-medium dark:bg-transparent dark:text-[#757575] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
//             >
//                12/02/24
//               <FaCalendarDays />
//             </button>

//           </div>

//           {/* Schedule Announcement */}
//           <div className="relative">
//             <label className="text-sm font-poppins text-black/40 font-semibold mb-2 mt-[0px] md:mt-[19px] lg:mt-[0px] block dark:text-[#B6B6B6]">
//             Check-out-Date
//             </label>
//             <button
//               type="button"

//               className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-poppins font-medium dark:bg-transparent dark:text-[#757575] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
//             >
//               12/02/24
//               <FaCalendarDays />
//             </button>

//           </div>
//         </div>

//         {/* Message */}
//         <div className="grid grid-cols-1 gap-6 mt-4">
//           <div>
//             <label className="text-sm text-black/40 font-semibold font-poppins mb-2 block dark:text-[#B6B6B6]">
//               Message
//             </label>
//             <textarea
//               rows="3"
//               placeholder="Message"
//               className="w-full bg-[#EEF9FF] border px-4 py-2 rounded-md font-poppins font-medium text-sm dark:bg-transparent dark:text-[#757575] dark:border-white focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
//               value="45"
//             />
//           </div>
//         </div>

//         {/* Buttons */}
//         <div className="flex items-center justify-center gap-4 mt-10">
//                 <Link
//                   href={"/superadmin/dashboard/notifications"}
//                   className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-24 py-2 text-sm font-semibold  font-poppins border  rounded  border-gray-200 text-black-100 dark:text-gray-100`}
//                 >
//                   Cancel
//                 </Link>
//                 <button
//                   type="button"
//                   className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-24  py-2 text-sm font-semibold font-poppins border  rounded  bg-sky-blue-650 text-white `}
//                 >
//                   Create
//                 </button>
//               </div>
//       </form>
//     </div>
//   </div>
// </div>
//   );
// };

// export default AnnouncementNotification;

"use client";
import Link from "next/link";
import React, { useEffect, useRef, useState } from "react";
import { FaChevronDown, FaChevronUp } from "react-icons/fa";
import { FaCalendarDays } from "react-icons/fa6";

const AnnouncementNotification = () => {
  const [selectedNotification, setSelectedNotification] = useState(
    "Select Notification"
  );
  const [selectedRecipients, setSelectedRecipients] =
    useState("Select Recipients");
  const [selectedAnnouncement, setSelectedAnnouncement] = useState(
    "Select Announcement"
  );
  const [selectedSend, setSelectedSend] = useState("Select Send");

  const [isDropdownOpen, setIsDropdownOpen] = useState({
    notificationType: false,
    recipients: false,
    announcementType: false,
    sendType: false,
  });

  const notificationRef = useRef(null);
  const recipientsRef = useRef(null);
  const announcementRef = useRef(null);
  const sendRef = useRef(null);

  const notificationOptions = [
    "Notification Type 1",
    "Notification Type 2",
    "Notification Type 3",
  ];
  const recipientOptions = ["User", "Admin", "Hostel Owner"];
  const announcementOptions = ["Anyone", "Specific group"];
  const sendOptions = ["Schedule", "Immediate"];

  // Function to close all dropdowns
  const closeAllDropdowns = () => {
    setIsDropdownOpen({
      notificationType: false,
      recipients: false,
      announcementType: false,
      sendType: false,
    });
  };

  // Effect to handle outside clicks
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        !notificationRef.current?.contains(event.target) &&
        !recipientsRef.current?.contains(event.target) &&
        !announcementRef.current?.contains(event.target) &&
        !sendRef.current?.contains(event.target)
      ) {
        closeAllDropdowns();
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, []);

  // Toggle dropdown functions
  const toggleDropdown = (key) => {
    setIsDropdownOpen((prev) => ({
      notificationType:
        key === "notificationType" ? !prev.notificationType : false,
      recipients: key === "recipients" ? !prev.recipients : false,
      announcementType:
        key === "announcementType" ? !prev.announcementType : false,
      sendType: key === "sendType" ? !prev.sendType : false,
    }));
  };

  // Handle selection
  const handleSelect = (key, option) => {
    if (key === "notificationType") setSelectedNotification(option);
    if (key === "recipients") setSelectedRecipients(option);
    if (key === "announcementType") setSelectedAnnouncement(option);
    if (key === "sendType") setSelectedSend(option);

    closeAllDropdowns();
  };

  return (
    // <div className="p-7 bg-sky-blue-20 dark:bg-[#171616]">
    //   <h2 className="text-2xl font-bold text-black dark:text-gray-100">
    //     Announcement Notification
    //   </h2>

    //   <div className="bg-white border mt-5 p-6 rounded-xl dark:bg-black">
    //     <form>
    //       <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
    //         {/* Notification Type Dropdown */}
    //         <div className="relative" ref={notificationRef}>
    //           <label className="text-sm font-semibold mb-2 block dark:text-[#B6B6B6]">
    //             Notification Type
    //           </label>
              // <button
              //   type="button"
              //   onClick={() => toggleDropdown("notificationType")}
              //   className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-medium dark:bg-transparent dark:text-[#757575]"
              // >
              //   {selectedNotification}
              //   {isDropdownOpen.notificationType ? (
              //     <FaChevronUp />
              //   ) : (
              //     <FaChevronDown />
              //   )}
              // </button>
              // {isDropdownOpen.notificationType && (
              //   <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg text-sm dark:bg-black">
              //     {notificationOptions.map((option, index) => (
              //       <li
              //         key={index}
              //         className="px-4 py-2 cursor-pointer hover:bg-[#EEF9FF] dark:hover:bg-[#393939b7]"
              //         onClick={() => handleSelect("notificationType", option)}
              //       >
              //         {option}
              //       </li>
              //     ))}
              //   </ul>
              // )}
    //         </div>

    //         {/* Recipients Dropdown */}
    //         <div className="relative" ref={recipientsRef}>
    //           <label className="text-sm font-semibold mb-2 block dark:text-[#B6B6B6]">
    //             Recipients
    //           </label>
              // <button
              //   type="button"
              //   onClick={() => toggleDropdown("recipients")}
              //   className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-medium dark:bg-transparent dark:text-[#757575]"
              // >
              //   {selectedRecipients}
              //   {isDropdownOpen.recipients ? (
              //     <FaChevronUp />
              //   ) : (
              //     <FaChevronDown />
              //   )}
              // </button>
              // {isDropdownOpen.recipients && (
              //   <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg dark:bg-black">
              //     {recipientOptions.map((option, index) => (
              //       <li
              //         key={index}
              //         className="px-4 py-2 cursor-pointer hover:bg-[#EEF9FF] dark:hover:bg-[#393939b7]"
              //         onClick={() => handleSelect("recipients", option)}
              //       >
              //         {option}
              //       </li>
              //     ))}
              //   </ul>
              // )}
    //         </div>
    //       </div>

    //       {/* Buttons */}
    //       <div className="flex items-center justify-center gap-4 mt-10">
    //         <button
    //           type="button"
    //           className="h-10 px-12 py-2 text-sm font-semibold border rounded border-gray-200 text-black dark:text-gray-100"
    //         >
    //           Cancel
    //         </button>
    //         <button
    //           type="button"
    //           className="h-10 px-12 py-2 text-sm font-semibold border rounded bg-sky-blue-650 text-white"
    //         >
    //           Create
    //         </button>
    //       </div>
    //     </form>
    //   </div>
    // </div>
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 dark:bg-[#171616] overflow-y-auto scroll-smooth h-screen">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        Announcement Notification
      </h2>

      <div className="bg-white border mt-5 p-6 md:p-10 rounded-xl dark:bg-black dark:border-none">
        <div className="bg-white w-full max-w-3xl dark:bg-black">
          <form>
            {/* <h2 className="text-xl font-bold font-poppins mb-5 dark:text-gray-100">
              Announcement Notification
            </h2> */}

            {/* Adjusted for small & medium screen responsiveness */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Notification Type Dropdown */}
              <div className="relative" ref={notificationRef}>
                <label className="text-sm font-poppins text-black/40 font-semibold mb-2 block dark:text-[#B6B6B6]">
                  Notification type
                </label>
                {/* <button
                  type="button"
                  onClick={toggleNotificationDropdown}
                  className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-poppins font-medium dark:bg-transparent dark:text-[#757575] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
                >
                  {selectedNotification}
                  {notificationType ? (
                    <FaChevronUp className="dark:text-[#757575]" />
                  ) : (
                    <FaChevronDown className="dark:text-[#757575]" />
                  )}
                </button>
                {notificationType && (
                  <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg text-sm dark:bg-black">
                    {notificationOptions.map((option, index) => (
                      <li
                        key={index}
                        className="px-4 py-2 cursor-pointer hover:bg-[#EEF9FF] font-poppins font-medium text-sm dark:text-[#757575] dark:hover:bg-[#393939b7]"
                        onClick={() => handleSelect("notificationType", option)}
                      >
                        {option}
                      </li>
                    ))}
                  </ul>
                )} */}
                <button
                type="button"
                onClick={() => toggleDropdown("notificationType")}
                className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-medium dark:bg-transparent dark:text-[#757575] focus:outline-none focus:ring-2 focus:ring-blue-800 focus:border-blue-800"
              >
                {selectedNotification}
                {isDropdownOpen.notificationType ? (
                  <FaChevronUp />
                ) : (
                  <FaChevronDown />
                )}
              </button>
              {isDropdownOpen.notificationType && (
                <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg text-sm dark:bg-[#171616] dark:border-none">
                  {notificationOptions.map((option, index) => (
                    <li
                      key={index}
                      className="px-4 py-2 cursor-pointer hover:bg-[#EEF9FF] dark:hover:bg-[#393939b7] dark:text-[#757575] text-sm"
                      onClick={() => handleSelect("notificationType", option)}
                    >
                      {option}
                    </li>
                  ))}
                </ul>
              )}
              </div>

              {/* Recipients Dropdown */}
              <div className="relative" ref={recipientsRef}>
                <label className="text-sm font-poppins text-black/40 font-semibold mb-2 block dark:text-[#B6B6B6]">
                  Recipients
                </label>
                {/* <button
                  type="button"
                  onClick={toggleRecipentsDropdown}
                  className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-poppins font-medium dark:bg-transparent dark:text-[#757575] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
                >
                  {selectedRecipients}
                  {recipients ? (
                    <FaChevronUp className="dark:text-[#757575]" />
                  ) : (
                    <FaChevronDown className="dark:text-[#757575]" />
                  )}
                </button>
                {recipients && (
                  <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg dark:bg-black ">
                    {recipientOptions.map((option, index) => (
                      <li
                        key={index}
                        className="px-4 py-2 cursor-pointer hover:bg-[#EEF9FF] font-poppins font-medium text-sm dark:text-[#757575] dark:hover:bg-[#393939b7]"
                        onClick={() => handleSelect("recipients", option)}
                      >
                        {option}
                      </li>
                    ))}
                  </ul>
                )} */}
                <button
                type="button"
                onClick={() => toggleDropdown("recipients")}
                className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-medium dark:bg-transparent dark:text-[#757575] focus:outline-none focus:ring-2 focus:ring-blue-800 focus:border-blue-800"
              >
                {selectedRecipients}
                {isDropdownOpen.recipients ? (
                  <FaChevronUp />
                ) : (
                  <FaChevronDown />
                )}
              </button>
              {isDropdownOpen.recipients && (
                <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg dark:bg-[#171616] dark:border-none">
                  {recipientOptions.map((option, index) => (
                    <li
                      key={index}
                      className="px-4 py-2 cursor-pointer hover:bg-[#EEF9FF] dark:hover:bg-[#393939b7] dark:text-[#757575] text-sm"
                      onClick={() => handleSelect("recipients", option)}
                    >
                      {option}
                    </li>
                  ))}
                </ul>
              )}
              </div>
            </div>

            {/* Title */}
            <div className="grid grid-cols-1 gap-6 mt-4">
              <div>
                <label className="text-sm font-poppins text-black/40 font-semibold mb-2 block dark:text-[#B6B6B6]">
                  Title
                </label>
                <input
                  type="text"
                  placeholder="Title"
                  className=" bg-[#EEF9FF] border  px-4 py-2 rounded-md text-sm font-poppins font-medium dark:bg-transparent dark:text-[#757575] w-[49%] dark:border-white focus:outline-none focus:border-none focus:ring-2 focus:ring-blue-800 focus:border-blue-800"
                  value="4.5"
                />
              </div>
            </div>

            {/* Announcement & Schedule */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
              {/* Announcement Visibility */}
              <div className="relative" ref={announcementRef}>
                <label className="text-sm font-poppins text-black/40 font-semibold mb-2 block dark:text-[#B6B6B6]">
                  Who can see this announcement?
                </label>
                {/* <button
                  type="button"
                  onClick={() => toggleDropdown("recipients")}
                  className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-poppins font-medium dark:bg-transparent dark:text-[#757575] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
                >
                  {selectedAnnouncement}
                  {announcementType ? (
                    <FaChevronUp className="dark:text-[#757575]" />
                  ) : (
                    <FaChevronDown className="dark:text-[#757575]" />
                  )}
                </button>
                {announcementType && (
                  <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg dark:bg-black">
                    {announcementOptions.map((option, index) => (
                      <li
                        key={index}
                        className="px-4 py-2 cursor-pointer hover:bg-[#EEF9FF] font-poppins font-medium text-sm dark:text-[#757575] dark:hover:bg-[#393939b7]"
                        onClick={() => handleSelect("announcementType", option)}
                      >
                        {option}
                      </li>
                    ))}
                  </ul>
                )} */}

<button
                type="button"
                onClick={() => toggleDropdown("announcementType")}
                className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-medium dark:bg-transparent dark:text-[#757575] focus:outline-none focus:ring-2 focus:ring-blue-800 focus:border-blue-800"
              >
                {selectedAnnouncement}
                {isDropdownOpen.announcementType ? (
                  <FaChevronUp />
                ) : (
                  <FaChevronDown />
                )}
              </button>
              {isDropdownOpen.announcementType && (
                <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg dark:bg-[#171616] dark:border-none ">
                  {announcementOptions.map((option, index) => (
                    <li
                      key={index}
                      className="px-4 py-2 cursor-pointer hover:bg-[#EEF9FF] dark:hover:bg-[#393939b7] dark:text-[#757575] text-sm"
                      onClick={() => handleSelect("announcementType", option)}
                    >
                      {option}
                    </li>
                  ))}
                </ul>
              )}
              </div>

              {/* Schedule Announcement */}
              <div className="relative" ref={sendRef}>
                <label className="text-sm font-poppins text-black/40 font-semibold mb-2 mt-[0px] md:mt-[19px] lg:mt-[0px] block dark:text-[#B6B6B6]">
                  Send this Announcement
                </label>
                {/* <button
                  type="button"
                  onClick={toggleSendDropdown}
                  className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-poppins font-medium dark:bg-transparent dark:text-[#757575] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
                >
                  {selectedSend}
                  {sendType ? (
                    <FaChevronUp className="dark:text-[#757575]" />
                  ) : (
                    <FaChevronDown className="dark:text-[#757575]" />
                  )}
                </button>
                {sendType && (
                  <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg dark:bg-black">
                    {sendOptions.map((option, index) => (
                      <li
                        key={index}
                        className="px-4 py-2 cursor-pointer hover:bg-[#EEF9FF] font-poppins font-medium text-sm dark:text-[#757575] dark:hover:bg-[#393939b7] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
                        onClick={() => handleSelect("sendType", option)}
                      >
                        {option}
                      </li>
                    ))}
                  </ul>
                )} */}
                                <button
                type="button"
                onClick={() => toggleDropdown("sendType")}
                className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-medium dark:bg-transparent dark:text-[#757575] focus:outline-none focus:ring-2 focus:ring-blue-800 focus:border-blue-800"
              >
                {selectedSend}
                {isDropdownOpen.sendType ? (
                  <FaChevronUp />
                ) : (
                  <FaChevronDown />
                )}
              </button>
              {isDropdownOpen.sendType && (
                <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg dark:bg-[#171616] dark:border-none">
                  {sendOptions.map((option, index) => (
                    <li
                      key={index}
                      className="px-4 py-2 cursor-pointer hover:bg-[#EEF9FF] dark:hover:bg-[#393939b7] dark:text-[#757575] text-sm"
                      onClick={() => handleSelect("sendType", option)}
                    >
                      {option}
                    </li>
                  ))}
                </ul>
              )}
              </div>
            </div>

            {/* Announcement & Schedule */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
              <div className="relative">
                <label className="text-sm font-poppins text-black/40 font-semibold mb-2 block dark:text-[#B6B6B6]">
                  Check-in-Date
                </label>
                <button
                  type="button"
                  className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-poppins font-medium dark:bg-transparent dark:text-[#757575] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
                >
                  12/02/24
                  <FaCalendarDays />
                </button>
              </div>

              {/* Schedule Announcement */}
              <div className="relative">
                <label className="text-sm font-poppins text-black/40 font-semibold mb-2 mt-[0px] md:mt-[19px] lg:mt-[0px] block dark:text-[#B6B6B6]">
                  Check-out-Date
                </label>
                <button
                  type="button"
                  className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-poppins font-medium dark:bg-transparent dark:text-[#757575] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
                >
                  12/02/24
                  <FaCalendarDays />
                </button>
              </div>
            </div>

            {/* Message */}
            <div className="grid grid-cols-1 gap-6 mt-4">
              <div>
                <label className="text-sm text-black/40 font-semibold font-poppins mb-2 block dark:text-[#B6B6B6]">
                  Message
                </label>
                <textarea
                  rows="3"
                  placeholder="Message"
                  className="w-full bg-[#EEF9FF] border px-4 py-2 rounded-md font-poppins font-medium text-sm dark:bg-transparent dark:text-[#757575] dark:border-white focus:border-none focus:outline-none focus:ring-2 focus:ring-blue-800 focus:border-blue-800"
                  value="45"
                />
              </div>
            </div>

            {/* Buttons */}
            <div className="flex items-center justify-center gap-4 mt-10">
              <Link
                href={"/superadmin/dashboard/notifications"}
                className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-24 py-2 text-sm font-semibold  font-poppins border  rounded  border-gray-200 text-black-100 dark:text-gray-100`}
              >
                Cancel
              </Link>
              <button
                type="button"
                className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-24  py-2 text-sm font-semibold font-poppins  rounded  bg-sky-blue-650 text-white `}
              >
                Create
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AnnouncementNotification;
