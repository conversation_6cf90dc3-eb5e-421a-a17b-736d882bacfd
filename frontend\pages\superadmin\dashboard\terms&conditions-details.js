import Link from "next/link";
import React from "react";

const TermsDetails = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 dark:bg-[#171616] overflow-y-auto scroll-smooth ">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        Terms & Condition Details
      </h2>
      <div className="bg-white border rounded-xl mt-5 h-auto dark:bg-black dark:border-none">
        <div className="px-8 py-4 flex flex-col gap-y-4">
          <div>
            <div className="flex ">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  Introduction
                </h1>
                <p className=" text-black/55 text-sm font-poppins font-medium dark:text-[#757575]">
                  Welcome to MixDorm! These Terms and Conditions (Terms) govern
                  your use of our website, services, and products provided by
                  MixDorm (we, us, our). By accessing or using our website and
                  services, you agree to comply with and be bound by these
                  Terms. If you do not agree with these Terms, please do not use
                  our services.
                </p>
              </div>
              <div className="">
                <Link href={"/superadmin/dashboard/terms&conditions-edit"} className="text-white text-sm font-poppins py-1 md:py-2 lg:py-2 px-6 lg:px-8   rounded bg-sky-blue-650">
                  Edit
                </Link>
              </div>
            </div>
            <div className="flex w-[90%]">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  Eligibility
                </h1>
                <p className=" text-black/55 text-sm font-poppins font-medium dark:text-[#757575]">
                  You must be at least 18 years of age to use our services and
                  make a booking.By using our services, you represent and
                  warrant that you meet the age requirementand have the legal
                  capacity to enter into these Terms..
                </p>
              </div>
            </div>
            <div className="flex w-[90%]">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  Reservation and payments
                </h1>
                <p className=" text-black/55 text-sm font-poppins font-medium dark:text-[#757575]">
                  All reservations are subject to availability and
                  confirmation.Advance payments made at the time of booking are
                  non-refundable.Full prepayment is require check-in.Prices and
                  availability are subject to change without notice.
                </p>
              </div>
            </div>
            <div className="flex w-[90%]">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  Use of Services
                </h1>
                <p className=" text-black/55 text-sm font-poppins font-medium dark:text-[#757575]">
                  We accept major credit cards, debit cards, and other online
                  payment methods. Payment is processed securely through our
                  payment gateway.
                </p>
              </div>
            </div>
            <div className="flex w-[90%]">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  Check-in and Check- out
                </h1>
                <p className=" text-black/55 text-sm font-poppins font-medium dark:text-[#757575]">
                  Yes, once you receive a confirmation email, your booking is
                  guaranteed. We use secure payment methods to ensure your
                  reservation is safe.
                </p>
              </div>
            </div>
            <div className="flex w-[90%]">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  Group bookings
                </h1>
                <p className=" text-black/55 text-sm font-poppins font-medium dark:text-[#757575]">
                  Yes, you can modify or cancel your booking based on our
                  Cancellation Policy. Please visit our website or contact our
                  customer support team for assistance with changes.
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-start justify-start p-8">
          <Link
            href={"/superadmin/dashboard/terms&conditions"}
            className="text-white py-2 w-32 max-w-md rounded bg-sky-blue-650 flex items-center justify-center"
          >
            Cancel
          </Link>
        </div>
      </div>
    </div>
  );
};

export default TermsDetails;
