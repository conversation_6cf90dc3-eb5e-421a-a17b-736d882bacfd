"use client";

import Image from "next/image";
import Link from "next/link";
// import { motion, useInView } from "framer-motion";
import { useRef, useState } from "react";

const logos = [
  {
    src: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/HyperGuest01.webp`,
    alt: "eZee Absolute",
    className: "",
  },
  {
    src: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/SiteMinder02.webp`,
    alt: "Siteminder",
    className: "",
  },
  {
    src: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Allocator02.webp`,
    alt: "yanolja cloud solution",
    className: "",
  },
  {
    src: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mypartner01.webp`,
    alt: "My partner",
    className: "",
  },
  {
    src: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Indonesia01.webp`,
    alt: "Indonesia",
    className: "",
  },
  {
    src: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Thailand01.webp`,
    alt: "Thailand",
    className: "",
  },
  {
    src: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/India01.webp`,
    alt: "India",
    className: "",
  },
  {
    src: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Combodia01.webp`,
    alt: "Cambodia",
    className: "",
  },
  {
    src: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/SriLanka01.webp`,
    alt: "Sri Lanka",
    className: " ",
  },
  {
    src: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Ezee01.webp`,
    alt: "Eze",
    className: "",
  },
  {
    src: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Cloudbeds01.webp`,
    alt: "Cloudbeds",
    className: "",
    onClick: () => (window.location.href = "/cloudbeds/channel-connection"),
  },
  {
    src: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/./Yanolja01.webp`,
    alt: "yanolja",
    className: "",
  },
];

// const shadowColors = [
//   "shadow-[#aefff7]/80", // turquoise
//   "shadow-[#ffc5e2]/80", // hot pink
//   "shadow-[#fff0a1]/80", // gold
//   "shadow-[#d6aaff]/80", // blue violet
//   "shadow-[#95f4cf]/80", // medium spring green
//   "shadow-[#febba3]/80", // orange red
//   "shadow-[#a7d3ff]/80", // dodger blue
//   "shadow-[#ffbbae]/80", // tomato
//   "shadow-[#d6aaff]/80", // chartreuse
//   "shadow-[#fbb1d9]/80", // deep pink
//   "shadow-[#aff7f8]/80", // dark turquoise
//   "shadow-[#d6aaff]/80", // medium purple
// ];

export default function ChannelPartners() {
  const containerRef = useRef(null);
  // const isInView = useInView(containerRef, { triggerOnce: false, margin: "-100px" });
  const [loadedImages, setLoadedImages] = useState({});

  const handleImageLoad = (index) => {
    setLoadedImages((prev) => ({
      ...prev,
      [index]: true,
    }));
  };

  // Define entry directions to alternate
  // const directions = [
  //   { x: -50, opacity: 0 }, // from left
  //   { x: 50, opacity: 0 },  // from right
  //   { y: -50, opacity: 0 }, // from top
  // ];

  return (
    <div
      className="py-16 md:py-16 px-0 md:px-6 relative bg-repeat-round w-full"
      style={{
        backgroundImage: `url(${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/right-cross-bg.webp)`,
        backgroundSize: "cover",
      }}
    >
      {/* <div className="absolute -top-20 -left-8 w-40 h-40 bg-cyan-400 rounded-full blur-2xl opacity-30 -z-10"></div> */}
      <div className="container flex flex-col lg:flex-row">
        <div className="space-y-6 w-[100%] lg:w-[45%] flex flex-col justify-center mb-4 lg:mb-0">
          {/* <motion.h2
            initial={{ x: -100, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: false }} */}
          <h2 className="font-mashiny font-normal xs:text-4xl text-3xl md:text-6xl text-left text-white">
            <span className="text-primary-blue">Our</span> Channel Partner
          </h2>
          {/* </motion.h2> */}
          {/* <motion.p
            initial={{ x: -100, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: false }} */}
          <p className="text-white/60 text-sm font-manrope hidden lg:block w-[90%]">
            At Mixdorm, we believe travel is more than just reaching a
            destination—it&apos;s about the experiences, events, and connections
            you make along the way. Through our powerful network of channel
            partners,{" "}
            <Link href={"/owner/about-mixdorm"} className="text-primary-blue">
              {" "}
              hostel owners{" "}
            </Link>{" "}
            can now list and promote exciting hostel events, local festivals,
            and city happenings directly on our platform. From backpacker
            parties to cultural experiences, Mixdorm’s Events feature helps
            travelers discover what’s happening nearby—making each stay more
            social, immersive, and memorable
          </p>
          {/* </motion.p> */}
          {/* <motion.div
            initial={{ x: -100, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: false }}
          > */}
          <div>
            <button className="bg-primary-blue text-black w-[154px] h-[44px] rounded-full shadow hover:bg-teal-400 transition duration-300 text-xs font-bold font-manrope hidden lg:block">
              <Link href={"/exploreworld"}>Explore World </Link>
            </button>
          </div>
          {/* </motion.div> */}
        </div>

        <div
          ref={containerRef}
          className="grid grid-cols-3 xs:grid-cols-4 xs:gap-4 gap-2"
        >
          {logos.map((logo, index) => {
            // const animateFrom = directions[index % directions.length];

            return (
              // <motion.div
              //   key={index}
              //   initial={animateFrom}
              //   animate={isInView ? { x: 0, y: 0, opacity: 1 } : animateFrom}
              //   transition={{
              //     type: "spring",
              //     stiffness: 100,
              //     damping: 8,
              //     mass: 0.8,
              //     delay: index * 0.1,
              //   }}
              <div
                key={index}
                className={`bg-[#fff] xxs:w-[100px] xs:w-[110px] sm:w-[140px] lg:w-[130px] xl:w-[170px] 
                  xxs:h-[90px] xs:h-[100px] sm:h-[100px] md:h-[120px] max-h-[90px] min-h-[90px] 
                  xs:max-h-[100px] xs:min-h-[100px] md:max-h-[120px] md:min-h-[120px] 
                  shadow-2xl shadow-white/50 p-4 z-10 border border-[#272727]`}
              >
                <div className="relative w-full h-full">
                  {!loadedImages[index] && (
                    <div className="absolute inset-0 animate-pulse bg-gray-200 rounded-md z-0" />
                  )}
                  <Image
                    src={logo.src}
                    alt={logo.alt}
                    fill
                    sizes="(max-width: 480px) 100px, (max-width: 640px) 110px, (max-width: 768px) 140px, (max-width: 1024px) 130px, 170px"
                    className={`transition-opacity duration-300 ease-in-out rounded-md ${
                      loadedImages[index] ? "opacity-100" : "opacity-0"
                    }`}
                    onLoad={() => handleImageLoad(index)}
                    onClick={logo.onClick || (() => {})}
                    style={{ cursor: logo.onClick ? "pointer" : "default" , objectFit: "contain" }}
                  />
                </div>
              </div>
              // </motion.div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
