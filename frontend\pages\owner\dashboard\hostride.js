/* eslint-disable react/jsx-key */
import React, { useEffect, useRef, useState, Fragment } from "react";
import Image from "next/image";
import dynamic from "next/dynamic";
import {
  DeleteHostRide<PERSON>pi,
  hostRideListApi,
} from "@/services/ownerflowServices";
import dayjs from "dayjs";
import Loader from "@/components/loader/loader";
import countries from "world-countries";
import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/react";
import { Trash, MoreVertical, Pencil, Eye } from "lucide-react";
import toast from "react-hot-toast";
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
  Transition,
} from "@headlessui/react";
import Pagination from "@/components/common/commonPagination";
import Head from "next/head";

const Addhostride = dynamic(
  () => import("@/components/ownerFlow/dashboard/addhostride"),
  {
    ssr: false,
  }
);

const Edithostride = dynamic(
  () => import("@/components/ownerFlow/dashboard/edithostride"),
  {
    ssr: false,
  }
);

const Viewhostride = dynamic(
  () => import("@/components/ownerFlow/dashboard/viewhostride"),
  {
    ssr: false,
  }
);

const Filter = dynamic(() => import("../../../components/model/filter"), {
  ssr: false,
});

const Hostride = () => {
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [openaddhostride, setOpenaddhostride] = useState(false);
  const [rideData, setRideData] = useState([]);
  const [loading, setLoading] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [totalRides, setTotalRides] = useState(0);
  const [currencyData, setCurrencyData] = useState({});
  const [editId, setEditId] = useState(null);
  const [editOpen, setEditOpen] = useState(false);
  const isFirstRender = useRef(null);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalData, setTotalData] = useState();

  const [isModalOpen, setIsModalOpen] = useState(false);
  const closefilterModal = () => setIsModalOpen(false);
  // Filter Modal
  // eslint-disable-next-line no-unused-vars
  const [openFilter, setOpenFilter] = useState(false);

  const [isOpen, setIsOpen] = useState(false);
  const [iseditOpen, setIseditOpen] = useState(false);
  const [isviewOpen, setIsviewOpen] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [isUpdate, setIsUpdate] = useState(false);
  const openModal = () => setIsOpen(true);
  const closeModal = () => setIsOpen(false);
  const closeeditModal = () => setIseditOpen(false);
  const closeviewModal = () => setIsviewOpen(false);

  const handlePageChange = (page) => {
    setCurrentPage(page); // Update current page on page change
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const fetchRideListData = async () => {
    setLoading(true);
    try {
      const response = await hostRideListApi(currentPage, itemsPerPage);
      setRideData(response?.data?.data?.rides || []);
      setTotalRides(response?.data?.data?.totalRides || 0); // Update total rides
      setTotalPages(
        Math.ceil((response?.data?.data?.totalRides || 0) / itemsPerPage)
      ); // Calculate total pages
      setTotalData(response?.data?.data?.totalRides || 0);
    } catch (error) {
      console.error("Error fetching review data:", error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (!isFirstRender.current) {
      fetchRideListData();
    } else {
      isFirstRender.current = false;
    }
  }, [currentPage, itemsPerPage]);

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);
      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);
  const getCurrencySymbol = (currencyCode) => {
    return currencyData[currencyCode] || currencyCode;
  };

  const updateRideList = () => {
    fetchRideListData();
  };

  const handleDelete = async (id) => {
    setLoading(true);
    try {
      const response = await DeleteHostRideApi(id);
      console.log("response", response);
      if (response?.data?.success) {
        toast.success(response?.data?.message);
        fetchRideListData();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>HostRide Management | Mixdorm</title>
      </Head>
      <Loader open={loading} />
      {/* {!openaddhostride && !editOpen && (
        <Breadcrumb
          items={[
            { label: "Dashbord", href: "/owner/dashboard" },
            { label: "Host Rides" },
          ]}
        />
      )} */}
      {openaddhostride ? (
        <Addhostride
          onClose={() => setOpenaddhostride(false)}
          updateRideList={updateRideList}
        />
      ) : editOpen ? (
        <Edithostride
          onClose={() => setEditOpen(false)}
          editId={editId}
          setEditId={setEditId}
          updateRideList={updateRideList}
        />
      ) : (
        <section className="w-full">
          <div className="flex justify-between items-center">
            <h1 className="page-title">Host Ride</h1>
            <div className="flex sm:gap-2 gap-1">
              <button
                className="sm:flex block items-center text-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-primary-blue font-medium"
                onClick={() => setIsModalOpen(true)}
              >
                <Image
                  className="sm:mr-2 mx-auto"
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/filter.svg`}
                  width={20}
                  height={20}
                />
                Filter
              </button>
              <button
                className="flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-primary-blue font-medium"
                onClick={openModal}
              >
                Host Ride
              </button>
              {/* <button
                className='flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-primary-blue font-medium'
                onClick={openeditModal}
              >
                Edit Room
              </button> */}
            </div>
          </div>
          <div className="w-full mt-5">
            <div className="w-full">
              <div className="w-full mt-5 text-nowrap overflow-x-auto rounded-lg">
                <table className="w-full border border-gray-340 rounded-lg font-inter">
                  <thead>
                    <tr>
                      <th className="pl-6 py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left rounded-tl-lg">
                        Ride Number
                      </th>
                      <th className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left">
                        From Where
                      </th>
                      <th className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left">
                        Destination
                      </th>
                      <th className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left">
                        Date
                      </th>
                      <th className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left">
                        Time
                      </th>
                      <th className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left">
                        Mode of Transport
                      </th>
                      <th className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left">
                        Price
                      </th>
                      <th className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left">
                        Status
                      </th>
                      <th className="pr-6 py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left rounded-tr-lg">
                        Action
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {rideData?.map((data, index) => (
                      <tr key={index}>
                        <td className="text-xs font-medium pl-6 py-3.5 px-4 border-b  text-black">
                          {data?.number}
                        </td>
                        <td className="text-xs font-medium py-3.5 px-4 border-b text-black ">
                          {data?.fromAddress?.address}
                        </td>
                        <td className="text-xs font-medium py-3.5 px-4 border-b text-black ">
                          {data?.destination?.address}
                        </td>
                        <td className="text-xs font-bold p-2 border-b">
                          {dayjs(data?.date).format("DD/MM/YYYY") || new Date()}
                        </td>
                        <td className="text-xs font-bold p-2 border-b">
                          {data?.time?.start}
                        </td>
                        <td className="text-xs font-medium py-3.5 px-4 border-b text-black ">
                          {data?.transportMode}
                        </td>
                        <td className="text-xs font-medium py-3.5 px-4 border-b text-black ">
                          {getCurrencySymbol(data?.currency) || "$"}{" "}
                          {data?.price || 0}
                        </td>
                        <td className="text-xs font-medium py-3.5 px-4 border-b text-black">
                          <button className="text-xs font-normal text-[#F9A63A] bg-[#F9A63A33] rounded-2xl p-1.5 px-2">
                            Booked
                          </button>
                        </td>
                        <td className="p-4 relative text-xs font-medium py-3.5 px-4 border-b text-black">
                          <Menu
                            as="div"
                            className="relative inline-block text-left"
                          >
                            <div>
                              <MenuButton>
                                <MoreVertical
                                  aria-hidden="true"
                                  size={16}
                                ></MoreVertical>
                              </MenuButton>
                            </div>

                            <MenuItems
                              transition
                              // ref={(el) => {
                              //   if (el) {
                              //     const rect = el.getBoundingClientRect();
                              //     const windowHeight = window.innerHeight;
                              //     const isLastItem =
                              //       index === rideData.length - 1;
                              //     const isOnlyItem = rideData.length === 1;

                              //     // Clear any previously applied classes
                              //     el.classList.remove(
                              //       "bottom-full",
                              //       "mb-2",
                              //       "mt-2",
                              //       "top-1/2",
                              //       "-translate-y-1/2"
                              //     );

                              //     if (isOnlyItem) {
                              //       // Center the dropdown vertically in the viewport
                              //       el.classList.add(
                              //         "top-1/2",
                              //         "-translate-y-1/2"
                              //       );
                              //     } else if (
                              //       isLastItem ||
                              //       rect.bottom > windowHeight
                              //     ) {
                              //       el.classList.add("bottom-full", "mb-2");
                              //     } else {
                              //       el.classList.add("mt-2");
                              //     }
                              //   }
                              // }}
                              ref={(el) => {
                                if (el) {
                                  const rect = el.getBoundingClientRect();
                                  const windowHeight = window.innerHeight;
                                  const isLastItem =
                                    index === rideData.length - 1;
                                  const isSecondLastItem =
                                    index === rideData.length - 2;
                                  const isOnlyItem = rideData.length === 1;
                                  const hasMoreThanTwoItems =
                                    rideData.length > 2;

                                  // Clear previous classes
                                  el.classList.remove(
                                    "bottom-full",
                                    "mb-2",
                                    "mt-2",
                                    "top-1/2",
                                    "-translate-y-1/2"
                                  );

                                  if (isOnlyItem) {
                                    el.classList.add(
                                      "top-1/2",
                                      "-translate-y-1/2"
                                    );
                                  } else if (
                                    isLastItem ||
                                    (hasMoreThanTwoItems && isSecondLastItem) ||
                                    rect.bottom > windowHeight
                                  ) {
                                    el.classList.add("bottom-full", "mb-2");
                                  } else {
                                    el.classList.add("mt-2");
                                  }
                                }
                              }}
                              className="absolute right-0 z-10 mt-2 w-max origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
                            >
                              <div>
                                <MenuItem>
                                  <button
                                    href="#"
                                    className="px-4 py-2 text-sm w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-t-md"
                                    onClick={() => {
                                      setIsviewOpen(true);
                                      setEditId(data?._id);
                                    }}
                                  >
                                    <Eye size={16}></Eye>
                                    View
                                  </button>
                                </MenuItem>
                                <MenuItem>
                                  <button
                                    href="#"
                                    className="px-4 py-2 text-sm w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-t-md"
                                    onClick={() => {
                                      setIseditOpen(true);
                                      setEditId(data?._id);
                                    }}
                                  >
                                    <Pencil size={16}></Pencil>
                                    Edit
                                  </button>
                                </MenuItem>
                                <MenuItem>
                                  <button
                                    onClick={() => {
                                      handleDelete(data?._id);
                                    }}
                                    className="px-4 py-2 text-sm text-red-600 w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-b-md"
                                  >
                                    <Trash size={16} className="text-red-600" />{" "}
                                    <span>Delete</span>
                                  </button>
                                </MenuItem>
                              </div>
                            </MenuItems>
                          </Menu>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                {/* {totalPages > 1 && (
                  <div className='flex justify-center mt-4'>
                    <CustomPagination
                      currentPage={currentPage}
                      total={totalPages}
                      onPageChange={handlePageChange}
                    />
                  </div>
                )} */}

                {rideData?.length > 0 && (
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    totalItems={totalData || 0}
                    itemsPerPage={itemsPerPage}
                    onPageChange={handlePageChange}
                    onItemsPerPageChange={handleItemsPerPageChange}
                  />
                )}
              </div>
            </div>
          </div>
        </section>
      )}

      {isModalOpen && (
        <Dialog
          open={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          className="relative z-50"
        >
          <DialogBackdrop
            transition
            className="fixed inset-0 bg-[#000000B2] transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
          />

          <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
            <div className="flex min-h-full justify-center p-4 text-center items-center sm:p-0">
              <DialogPanel
                transition
                className="relative transform overflow-hidden rounded-2xl bg-white text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-full sm:max-w-[490px] max-w-full w-full data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95"
              >
                <div className="bg-white sm:px-7 sm:pb-7 pt-3 p-3 pb-3">
                  <DialogTitle>
                    <h3 className="text-center text-black font-bold sm:text-lg text-sm">
                      Filter
                    </h3>
                  </DialogTitle>
                  <Filter closefilterModal={closefilterModal} />
                </div>
              </DialogPanel>
            </div>
          </div>
        </Dialog>
      )}

      <Transition show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeModal}>
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[95%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <h2 className="page-title">Add Host Ride</h2>
                    <button
                      onClick={closeModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4 pb-10">
                    <Addhostride
                      setIsUpdate={setIsUpdate}
                      closeModal={closeModal}
                      updateRideList={updateRideList}
                    ></Addhostride>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      <Transition show={iseditOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeeditModal}>
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <h2 className="page-title">Edit Host ride</h2>
                    <button
                      onClick={closeeditModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <Edithostride
                      closeeditModal={closeeditModal}
                      editId={editId}
                      updateRideList={updateRideList}
                    ></Edithostride>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      <Transition show={isviewOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeviewModal}>
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <h2 className="page-title">View Host ride</h2>
                    <button
                      onClick={closeviewModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <Viewhostride
                      editId={editId}
                      setIsUpdate={setIsUpdate}
                    ></Viewhostride>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
};

export default Hostride;
