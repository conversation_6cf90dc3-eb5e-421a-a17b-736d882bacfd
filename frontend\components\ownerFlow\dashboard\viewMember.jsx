import React, { useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";
import { ViewMemberListApi } from "@/services/ownerflowServices";
import { getItemLocalStorage } from "@/utils/browserSetting";
import dynamic from "next/dynamic";
import countries from "world-countries";
import { format } from "date-fns";
import Pagination from "@/components/common/commonPagination";

const Event = dynamic(
  () => import("@/components/ownerFlow/dashboard/addEvent"),
  {
    ssr: false,
  }
);

const EditEvent = dynamic(
  () => import("@/components/ownerFlow/dashboard/editEvent"),
  {
    ssr: false,
  }
);

const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

const ViewMember = () => {
  // Filter Modal
  // eslint-disable-next-line no-unused-vars
  const [openFilter, setOpenFilter] = useState(false);

  const [editOpen, setEditOpen] = useState(false);
  const [openAddEvent, setOpenAddEvent] = useState(false);
  const [editId, setEditId] = useState(null);
  // eslint-disable-next-line no-unused-vars
  const [currencyData, setCurrencyData] = useState({});
  const isFirstRender = useRef(null);

  const headers = [
    "Sr No.",
    "Name",
    "Booking Id",
    "Country",
    "Date Of Event",
    "Booked Date",
    "Payment Status",
    "Phone Number",
    "Email Id",
    // "State",
    // "Country",
    // "Location",
    // "Attachment",
    // "Description",
    // "Tags",
    // "Status",
    // "Action",
  ];

  const id = getItemLocalStorage("selectedId");
  const [currentPage, setCurrentPage] = useState(1);
  const [eventsMembersList, setEventsMembersList] = useState([]);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(false);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  // eslint-disable-next-line no-unused-vars
  const [totalData, setTotalData] = useState();

  useEffect(() => {
    if (!isFirstRender.current) {
      fetchList(currentPage);
    } else {
      isFirstRender.current = false;
    }
  }, [id, currentPage, itemsPerPage]);

  const fetchList = async () => {
    setLoading(true);
    try {
      const response = await ViewMemberListApi(currentPage, itemsPerPage);
      setEventsMembersList(response?.data?.data?.eventsMembers);
      setTotalPages(response?.data?.data?.pagination?.totalPages || 1);

      // eslint-disable-next-line no-empty
      if (response.status == 200) {
      }
      if (response.status !== 200) {
        toast.error(response?.data?.message);
      }
      // eslint-disable-next-line no-unused-vars
    } catch (error) {
      /* empty */
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const updateEventList = () => {
    fetchList(1);
  };

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);
      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);

  return (
    <>
      <Loader open={loading} />
      {openAddEvent ? (
        <Event
          onClose={() => setOpenAddEvent(false)}
          updateEventList={updateEventList}
        />
      ) : editOpen ? (
        <EditEvent
          onClose={() => setEditOpen(false)}
          editId={editId}
          setEditId={setEditId}
          updateEventList={updateEventList}
        />
      ) : (
        <section className="w-full">
          {loading ? (
            <div className="overflow-x-auto mt-4 rounded-md">
              <table className="min-w-full border">
                <thead>
                  <tr className="bg-gray-100 border-b">
                    {headers.map((_, index) => (
                      <th
                        key={index}
                        className="p-4 text-xs font-bold text-[#000000] text-left"
                      >
                        <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse"></div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {[...Array(5)].map((_, rowIndex) => (
                    <tr className="border-b" key={rowIndex}>
                      {headers.map((_, cellIndex) => (
                        <td key={cellIndex} className="p-3">
                          <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="overflow-x-auto mt-4 rounded-md">
              <table className="min-w-full border">
                <thead>
                  <tr className="bg-gray-100 border-b">
                    {headers.map((header, index) => (
                      <th
                        key={index}
                        className="p-4 text-xs font-bold text-[#000000] text-left"
                      >
                        {header}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {eventsMembersList?.map((item, index) => (
                    <tr className="border-b" key={index}>
                      <td className="p-3 text-xs text-[#000]">{index + 1}</td>
                      <td className="p-3 text-xs text-[#000]">
                        {item?.user?.name?.first}
                      </td>
                      <td className="p-3 text-xs text-[#000]">
                        {item?.bookingRefNumber}
                      </td>
                      <td className="p-3 text-xs text-[#000]">
                        {item?.event?.currency}
                      </td>
                      <td className="p-3 text-xs text-[#000]">
                        {format(
                          new Date(item?.event?.startDate),
                          "dd MMM yyyy"
                        )}
                      </td>
                      <td className="p-3 text-xs text-[#000]">
                        {format(new Date(item?.createdAt), "dd MMM yyyy")}
                      </td>
                      <td className="p-3 text-xs text-[#000]">
                        {item?.paymentStatus}
                      </td>
                      <td className="p-3 text-xs text-[#000]">
                        <span
                          className={`inline-block px-2 py-1 text-xs whitespace-nowrap rounded-full ${
                            item?.status === "Upcoming"
                              ? "bg-yellow-100 text-yellow-700"
                              : item?.status === "Ongoing"
                              ? "bg-green-100 text-green-700"
                              : item?.status === "Completed"
                              ? "bg-gray-100 text-gray-700"
                              : ""
                          }`}
                        >
                          +326865262563
                        </span>
                      </td>
                      <td className="p-3 text-xs text-[#000] ">
                        <EMAIL>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          {/* {totalPages > 1 && (
            <div className='flex justify-center mt-4'>
              <CustomPagination
                currentPage={currentPage}
                total={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          )} */}
          {eventsMembersList?.length > 0 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={totalData || 0}
              itemsPerPage={itemsPerPage}
              onPageChange={handlePageChange}
              onItemsPerPageChange={handleItemsPerPageChange}
            />
          )}
        </section>
      )}
    </>
  );
};

export default ViewMember;
