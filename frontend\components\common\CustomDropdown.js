import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';
import { useOutsideClick } from './useOutsideClick';

const CustomSelect = ({ options, value, onChange, placeholder, label }) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleSelect = (option) => {
    onChange(option);
    setIsOpen(false);
  };

  const ref = useOutsideClick(() => {
    setIsOpen(false);
  });

  const selectedOption = options.find((option) => option.value === (typeof value === "string" ? value : value?.value))


  return (
    <div className="relative" ref={ref}>
      <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
        {label}
      </label>
      <div
        className={`w-full sm:h-[41.6px] h-[33.6px] sm:px-4 px-2 sm:py-2.5 py-2 sm:text-sm text-xs border border-black/50 rounded-lg cursor-pointer relative inner-currency ${
          !selectedOption ? "text-gray-500" : "text-black"
        }`}
        onClick={() => setIsOpen(!isOpen)}
      >
               {selectedOption ? <span className="flex items-center">{selectedOption.label}</span> : placeholder}

        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
          <ChevronDown className="w-5 h-5" />
        </div>
      </div>

      {isOpen && (
        <ul className="absolute w-full bg-white border border-black/50 rounded-lg mt-1 shadow-lg z-20 max-h-[300px] overflow-y-auto modalscroll">
          {options.map((option) => (
            <li
              key={option.value}
              className={`px-4 py-2 cursor-pointer hover:bg-[#40E0D0] hover:text-white text-sm ${
                selectedOption?.value === option.value ? "bg-[#40E0D0] text-white" : ""
              }`}
              onClick={() => handleSelect(option)}
            >
              <span className="flex items-center">
              {option.label}
              </span>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default CustomSelect;
