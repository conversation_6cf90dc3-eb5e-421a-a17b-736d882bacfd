import mongoose from 'mongoose';

const contactUsSchema = new mongoose.Schema({
    name: {
        type: String,
        required : true
    },
    email: {
        type: String,
        required : true
    },
    subject: {
        type: String,
        required : true
    },
    categories: {
        type: String,
    },
    description: {
        type: String,
    },
    status:{
        type: String,
        default : '0'
    }
});

const contactUsModel = mongoose.model('contactus', contactUsSchema);

export default contactUsModel;
