import React from "react";
import Image from "next/image";

const Webcheckindetails = () => {
    return (
      <>
        <section className='w-full'>
          <h2 className='text-xl font-large text-black-230'>Web Check in Details</h2>
          <div className='w-full mt-5 bg-white shadow-4xl p-6 rounded-2xl border'>
            <div className='lg:w-[400px] sm:w-full mx-auto py-8'>
              <div className='grid sm:grid-cols-2 gap-4'>
                <div>
                  <label className='font-semibold'>Booking Id:</label>
                </div>
                <div>
                  <label className='font-normal'>#8532</label>
                </div>
                <div>
                  <label className='font-semibold'>Name:</label>
                </div>
                <div>
                  <label className='font-normal'>Khusboo jilka</label>
                </div>
                <div>
                  <label className='font-semibold'>Phone Number :</label>
                </div>
                <div>
                  <label className='font-normal'>+919999999999</label>
                </div>
                <div>
                  <label className='font-semibold'>Check In:</label>
                </div>
                <div>
                  <label className='font-normal'>Sep 25,2019</label>
                </div>
                <div>
                  <label className='font-semibold'>Check Out:</label>
                </div>
                <div>
                  <label className='font-normal'>Sep 27,2019</label>
                </div>
                <div>
                  <label className='font-semibold'>Aadhar Card:</label>
                </div>
                <div>
                 <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/MyStay.jpg`}
                    alt='Aadharcard'
                    title='Aadharcard'
                    width={64}
                    height={32}
                    className='object-cover'
                    loading='lazy'
                  />
                </div>
                <div>
                  <label className='font-semibold'>Coming From:</label>
                </div>
                <div>
                  <label className='font-normal'>Mumbai</label>
                </div>
                <div>
                  <label className='font-semibold'>Going For:</label>
                </div>
                <div>
                <label className='font-normal'>Rishikesh</label>
                </div>
                <div>
                  <label className='font-semibold'>Status:</label>
                </div>
                <div>
                <label className='font-normal'>Confirm</label>
                </div>  
                <div>
                  <label className='font-semibold'>Sign:</label>
                </div>
                <div>
                <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/MyStay.jpg`}
                    alt='Sign'
                    title='Sign'
                    width={64}
                    height={32}
                    className='object-cover'
                    loading='lazy'
                  />
                </div>  
              </div>
            </div>
          </div>
        </section>
      </>
    );
};
export default Webcheckindetails;