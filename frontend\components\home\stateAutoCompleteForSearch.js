import { searchAutocomplete<PERSON>pi } from "@/services/webflowServices";
import React, { useState, useEffect, useRef } from "react";
import { IoLocationOutline } from "react-icons/io5";
import { Building2, Loader2 } from "lucide-react";

const StateAutocompleteForSearch = ({ state, setState, size = "small" }) => {
  const [location, setLocation] = useState(state);
  const [suggestions, setSuggestions] = useState([]);
  const [debounceTimer, setDebounceTimer] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const autocompleteRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        autocompleteRef.current &&
        !autocompleteRef.current.contains(event.target)
      ) {
        setSuggestions([]);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleChange = (e) => {
    const { value } = e.target;
    setLocation(value);

    if (debounceTimer) clearTimeout(debounceTimer);

    const newTimer = setTimeout(() => {
      if (value.length > 2) {
        fetchSuggestions(value);
      } else {
        setSuggestions([]);
      }
    }, 500);

    setDebounceTimer(newTimer);
  };

  const fetchSuggestions = async (query) => {
    try {
      setIsLoading(true);
      const response = await searchAutocompleteApi(query);

      const categorizedSuggestions = response?.data?.data?.reduce(
        (acc, category) => {
          acc.push({
            category: category?.category,
            items: category?.items?.map((item) => ({
              label: item?.label,
              type: item?.type,
              id: item?.id,
            })),
          });
          return acc;
        },
        []
      );

      // Sort categories to show Country first, then City, then Property
      const sortedSuggestions = categorizedSuggestions.sort((a, b) => {
        const order = { Country: 0, City: 1, Property: 2 };
        return (order[a.category] ?? 3) - (order[b.category] ?? 3);
      });

      setSuggestions(sortedSuggestions);
      setState(query);
    } catch (error) {
      console.error("Error fetching suggestions:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectSuggestion = (suggestion) => {
    setLocation(suggestion?.label);
    setState(suggestion?.label);
    setSuggestions([]);
  };

  const inputClasses = size === "large" ? "middle px-3" : " font-middle px-1";
  const iconClasses = size === "large" ? "text-2xl" : "text-xl";
  const suggestionClasses =
    size === "large" ? "text-lg py-1 px-4" : "base:text-sm text-base py-1 px-2";
  const placeholderClasses = size === "large" ? "text-lg" : "text-base";

  return (
    <div
      ref={autocompleteRef}
      className='flex flex-col items-center justify-center w-full relative'
    >
      <div className='flex items-center w-full'>
        <IoLocationOutline
          className={`mr-0.5 text-white md:text-black  ${iconClasses}`}
        />
        <input
          type='text'
          value={location}
          onChange={handleChange}
          placeholder={state || "Where to?"}
          className={`w-44 placeholder-white md:placeholder-black font-semibold rounded py-1 base:py-0 ${inputClasses} ${placeholderClasses}`}
          style={{ outline: "none" }}
          maxLength={50}
        />
      </div>
      {isLoading && (
        <div className='w-full min-w-[300px] h-[100px] flex items-center justify-center rounded-2xl p-3 z-50 bg-white shadow-lg absolute left-0 lg:left-[-20px] top-[62px]'>
          <Loader2 className='h-6 w-6 animate-spin text-primary-blue' />
        </div>
      )}
      {!isLoading && suggestions.length > 0 && (
        <ul className='w-full min-w-[300px] max-h-[290px] overflow-auto rounded-2xl p-3 z-50 bg-white shadow-lg absolute left-0 lg:left-[-20px] top-[42px]'>
          {suggestions?.map((category) => (
            <React.Fragment key={category?.category}>
              <li className='font-semibold text-[#636c7d] py-2 text-sm'>
                {category?.category}
              </li>
              {category?.items?.map((item) => (
                <li
                  key={item?.id}
                  onClick={() => handleSelectSuggestion(item)}
                  className={`cursor-pointer text-[16px] hover:bg-gray-100 rounded-xl py-3 flex gap-2 font-normal ${suggestionClasses}`}
                >
                  <Building2 size={15} /> {item?.label}
                </li>
              ))}
            </React.Fragment>
          ))}
        </ul>
      )}
    </div>
  );
};

export default StateAutocompleteForSearch;
