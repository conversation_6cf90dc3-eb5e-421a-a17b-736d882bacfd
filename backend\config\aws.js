import { S3 } from 'aws-sdk';
import { decrypt } from '../utills/encryptions';
const s3 = new S3({
  accessKeyId: await decrypt(process.env.AWS_ACCESS_KEY_ID),
  secretAccessKey: await decrypt(process.env.AWS_SECRET_ACCESS_KEY),
  region: process.env.AWS_REGION
});
const uploadImageToS3 = async(filePath, bucketName, key) => {
  // Read content from the file
  const fileContent = fs.readFileSync(filePath);

  // Setting up S3 upload parameters
  const params = {
      Bucket: await decrypt(bucketName),
      Key: key, // File name you want to save as in S3
      Body: fileContent,
      ContentType: 'image/jpeg' // or 'image/png', depending on the file
  };

  // Uploading files to the bucket
  s3.upload(params, (err, data) => {
      if (err) {
          console.error('Error uploading image:', err);
          return;
      }
      console.log(`File uploaded successfully. ${data.Location}`);
  });
};

export default s3;
