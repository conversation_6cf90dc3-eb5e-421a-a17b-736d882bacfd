"use client";
import Link from "next/link";
import React from "react";

const editPrivacy = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth h-screen dark:bg-[#171616]">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        Edit Privacy Policy
      </h2>
      <div className="bg-white border flex justify-center mt-5  rounded-xl h-auto  px-4 md:px-5 lg:px-1 py-7 md:py-16 lg:py-8 dark:bg-black dark:border-none">
        <div className="bg-white w-full max-w-3xl dark:bg-black">
          <form>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div className="relative">
                <label className="block font-semibold font-poppins text-sm text-black/40 dark:text-[#B6B6B6]">
                  Title
                </label>
                <input
                  className="mt-1 px-2 py-3 w-full font-poppins text-sm font-medium rounded-md bg-[#EEF9FF] border border-gray-400 placeholder:text-black/40 placeholder:font-medium dark:bg-transparent dark:placeholder:text-[#757575] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
                  placeholder="Welcome to Mixdorm"
                />
              </div>

              <div className="relative">
                <label className="block font-semibold font-poppins text-sm text-black/40 dark:text-[#B6B6B6]">
                  Sub Text
                </label>
                <textarea
                  className="mt-1 w-full h-40 px-4 py-2 border rounded-lg placeholder-gray-400 text-gray-700 resize-none bg-[#EEF9FF] border-gray-400 placeholder:text-black/40 placeholder:font-medium placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
                  placeholder="The MixDorm Global Hostel Awards aim to elevate the standards of
            hostel hospitality by recognizing the finest accommodations
            worldwide. These awards celebrate the hostels that embody the spirit
            of adventure, foster community among travelers, and provide
            unparalleled service and comfort."
                ></textarea>
              </div>
              <div className="relative">
                <label className="block font-semibold font-poppins text-sm text-black/40 dark:text-[#B6B6B6]">
                  Title
                </label>
                <input
                  className="mt-1 px-2 py-3 w-full font-poppins text-sm font-medium rounded-md bg-[#EEF9FF] border border-gray-400 placeholder:text-black/40 placeholder:font-medium dark:bg-transparent dark:placeholder:text-[#757575] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
                  placeholder="At MixDorm, we believe that travel is more than just a journey; it's an experience that connects people, cultures, and stories. Founded with a vision to revolutionize the hostel experience, MixDorm is not just a place to stay but a community where travelers from around the world come together to create unforgettable memories"
                />
              </div>

              <div className="relative">
                <label className="block font-semibold font-poppins text-sm text-black/40 dark:text-[#B6B6B6]">
                  Sub Title
                </label>
                <textarea
                  className="mt-1 w-full h-40 px-4 py-2 border rounded-lg placeholder-gray-400 text-gray-700 resize-none bg-[#EEF9FF] border-gray-400 placeholder:text-black/40 placeholder:font-medium placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
                  placeholder="The MixDorm Global Hostel Awards aim to elevate the standards of
            hostel hospitality by recognizing the finest accommodations
            worldwide. These awards celebrate the hostels that embody the spirit
            of adventure, foster community among travelers, and provide
            unparalleled service and comfort."
                ></textarea>
              </div>
              <div className="relative">
                <label className="block font-semibold font-poppins text-sm text-black/40 dark:text-[#B6B6B6]">
                  Title
                </label>
                <input
                  className="mt-1 px-2 py-3 w-full font-poppins text-sm font-medium rounded-md bg-[#EEF9FF] border border-gray-400 placeholder:text-black/40 placeholder:font-medium dark:bg-transparent dark:placeholder:text-[#757575] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
                  placeholder="Welcome to Mixdorm"
                />
              </div>

              <div className="relative">
                <label className="block font-semibold font-poppins text-sm text-black/40 dark:text-[#B6B6B6]">
                  Sub Title
                </label>
                <textarea
                  className="mt-1 w-full h-40 px-4 py-2 border rounded-lg placeholder-gray-400 text-gray-700 resize-none bg-[#EEF9FF] border-gray-400 placeholder:text-black/40 placeholder:font-medium placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
                  placeholder="The MixDorm Global Hostel Awards aim to elevate the standards of
            hostel hospitality by recognizing the finest accommodations
            worldwide. These awards celebrate the hostels that embody the spirit
            of adventure, foster community among travelers, and provide
            unparalleled service and comfort."
                ></textarea>
              </div>
              <div className="relative">
                <label className="block font-semibold font-poppins text-sm text-black/40 dark:text-[#B6B6B6]">
                  Title
                </label>
                <input
                  className="mt-1 px-2 py-3 w-full font-poppins text-sm font-medium rounded-md bg-[#EEF9FF] border border-gray-400 placeholder:text-black/40 placeholder:font-medium dark:bg-transparent dark:placeholder:text-[#757575] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
                  placeholder="Welcome to Mixdorm"
                />
              </div>

              <div className="relative">
                <label className="block font-semibold font-poppins text-sm text-black/40 dark:text-[#B6B6B6]">
                  Sub Title
                </label>
                <textarea
                  className="mt-1 w-full h-40 px-4 py-2 border rounded-lg placeholder-gray-400 text-gray-700 resize-none bg-[#EEF9FF] border-gray-400 placeholder:text-black/40 placeholder:font-medium placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
                  placeholder="The MixDorm Global Hostel Awards aim to elevate the standards of
            hostel hospitality by recognizing the finest accommodations
            worldwide. These awards celebrate the hostels that embody the spirit
            of adventure, foster community among travelers, and provide
            unparalleled service and comfort."
                ></textarea>
              </div>
            </div>

            {/* Buttons */}
            <div className="flex flex-wrap items-center justify-center space-x-6 my-8 mb-12">
              <Link
                href={"/superadmin/dashboard/privacy-policy"}
                type="button"
                className="py-2 flex items-center justify-center border w-40 lg:w-56 border-gray-300 text-black font-poppins text-sm font-medium rounded-md mt-2 dark:text-gray-100"
              >
                Cancel
              </Link>
              <button
                type="submit"
                className="px-6 py-2 w-40 lg:w-56 bg-sky-blue-650 text-white font-poppins text-sm font-medium rounded-md mt-2"
              >
                Submit
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default editPrivacy;
