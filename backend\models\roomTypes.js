import mongoose from 'mongoose';

const roomTypeSchema = mongoose.Schema({
    RoomTypeId: {
        type: String,
        required: true,
    },
    RoomType: {
        type: String,
        required: true
    },
    type:{
        type:String
    },
    isDeleted: {
        type: Boolean,
        default: false,
    },
    isActive: {
        type: Boolean,
        default: true
    },
    avaliableRooms:{
        type:Number
    }
},
    {
        timestamps: true
    }
);

const roomTypesModel = mongoose.model("roomTypes", roomTypeSchema);
export default roomTypesModel;