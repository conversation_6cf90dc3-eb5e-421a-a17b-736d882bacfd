/* eslint-disable react/no-unescaped-entities */

import Link from "next/link";
import Image from "next/image";
import { GiCheckMark } from "react-icons/gi";
import Banner from "@/components/home/<USER>";
import Head from "next/head";

const Meetbuddies = () => {
  return (
    <>
      <Head>
        <title>Travel Smarter with Mixdorm: Share, Split & Ride</title>
        <meta
          name='description'
          content='Discover Mixdorm’s Ai-Powered Tools for Budget-Friendly Travel. Split Costs, Meet Travel Mates, and Ride Together with Ease and Fun.'
        />
      </Head>
            <div
              style={{
                backgroundImage: `url(${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/top-black-bg.jpeg)`,
                backgroundSize: "cover",
                backgroundPosition: "center",
                backgroundRepeat: "no-repeat",
              }}
              className="pb-10 md:pb-14"
            >
              <Banner />{" "}
            </div>
      <section className='lg:pt-12 pt-8 lg:pb-16 border-t border-gray-200'>
        <div className='container px-4 lg:px-14'>
          <div className='grid lg:grid-cols-2 items-center lg:gap-10 gap-4 md:mb-12 mb-8'>
            <div className=' flex items-center justify-center'>
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/discoverEvent1.jpg`}
                width={591}
                height={681}
                alt='BgImg'
                className='rounded-4xl'
                loading='lazy'
              />
            </div>
            <div>
              <h3 className='font-bold lg:text-3xl text-xl text-black lg:mb-5 mb-3'>
                Mix Split Bill
              </h3>
              <p className='text-[#737373] text-sm'>
                Ever faced the hassle of splitting expenses while traveling with
                friends? Say goodbye to awkward calculations! With Mix Split
                Bill, you can easily divide hostel costs, meals, tours, and
                transportation expenses between your group in just a few taps.
                This AI-powered tool ensures fair and transparent splits, so
                everyone pays their share without any confusion.
              </p>
              <ul className='lg:my-10 my-6'>
                <li className='flex lg:mb-6 mb-4'>
                  <GiCheckMark className='mr-2 text-[#34D674] w-4 pt-1' />
                  <p className='text-black text-base flex-1'>
                    <b>Perfect for group travelers:</b> Whether you're splitting
                    a hostel dorm in Thailand or a meal in Mexico City, it’s
                    never been easier to manage travel costs.
                  </p>
                </li>
                <li className='flex lg:mb-6 mb-4'>
                  <GiCheckMark className='mr-2 text-[#34D674] w-4 pt-1' />
                  <p className='text-black text-base flex-1'>
                    <b>Stay organized:</b> Keep track of who owes what, and
                    avoid any misunderstandings with instant updates and
                    reminders.
                  </p>
                </li>
              </ul>
              <Link
                href='#'
                className='text-sm inline-block font-semibold text-black bg-primary-blue rounded-4xl py-4 px-12 hover:bg-sky-blue-750'
                prefetch={false}
              >
                Download Now
              </Link>
            </div>
            <div className='lg:order-2 flex items-center justify-center'>
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/discoverEvent2.jpg`}
                width={591}
                height={681}
                alt='BgImg'
                className='rounded-4xl'
                loading='lazy'
              />
            </div>
            <div className='lg:order-1'>
              <h3 className='font-bold lg:text-3xl text-xl text-black lg:mb-5 mb-3'>
                Mix Mate (Travel Buddy)
              </h3>
              <p className='text-[#737373] text-sm'>
                Looking for travel buddies on the go? With Mix Mate, our unique
                "Travel Tinder" feature, you can find like-minded travelers to
                explore cities with, share experiences, and even make new
                friends. Whether you're backpacking through Europe or
                island-hopping in Southeast Asia, Mix Mate connects you with
                fellow explorers based on interests, destinations, and
                activities
              </p>
              <ul className='lg:my-10 my-6'>
                <li className='flex lg:mb-6 mb-4'>
                  <GiCheckMark className='mr-2 text-[#34D674] w-4 pt-1' />
                  <p className='text-black text-base flex-1'>
                    <b>Swipe, match, travel!:</b> Just swipe through potential
                    travel mates, match with people heading to the same
                    destinations, and start planning your adventures together
                  </p>
                </li>
                <li className='flex lg:mb-6 mb-4'>
                  <GiCheckMark className='mr-2 text-[#34D674] w-4 pt-1' />
                  <p className='text-black text-base flex-1'>
                    <b>Stay social on the move:</b> Great for solo travelers who
                    want to meet new friends and share experiences along the
                    journey.
                  </p>
                </li>
              </ul>
              <Link
                href='#'
                className='text-sm inline-block font-semibold text-black bg-primary-blue rounded-4xl py-4 px-12 hover:bg-sky-blue-750'
                prefetch={false}
              >
                Download Now
              </Link>
            </div>
            <div className='lg:order-3 flex items-center justify-center'>
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/discoverEvent1.jpg`}
                width={591}
                height={681}
                alt='BgImg'
                className='rounded-4xl'
                loading='lazy'
              />
            </div>
            <div className='lg:order-4'>
              <h3 className='font-bold lg:text-3xl text-xl text-black lg:mb-5 mb-3'>
                Mix Ride
              </h3>
              <p className='text-[#737373] text-sm'>
                Need a ride to your next hostel or want to share a cab to the
                airport? With Mix Ride, carpooling becomes seamless. Share your
                ride with other travelers heading in the same direction and
                split the cost, making your journey both budget-friendly and
                ecoonscious. Whether it's a tuk-tuk ride in Thailand or a taxi
                in New York, Mix Ride helps you connect with fellow travelers to
                save money and reduce your carbon footprint.
              </p>
              <ul className='lg:my-10 my-6'>
                <li className='flex lg:mb-6 mb-4'>
                  <GiCheckMark className='mr-2 text-[#34D674] w-4 pt-1' />
                  <p className='text-black text-base flex-1'>
                    <b>Save money:</b> Share rides and cut costs on
                    transportation while meeting new friends.
                  </p>
                </li>
                <li className='flex lg:mb-6 mb-4'>
                  <GiCheckMark className='mr-2 text-[#34D674] w-4 pt-1' />
                  <p className='text-black text-base flex-1'>
                    <b>Eco-friendly travel:</b> Reduce your environmental impact
                    by sharing rides instead of going solo.
                  </p>
                </li>
              </ul>
              <Link
                href='#'
                className='text-sm inline-block font-semibold text-black bg-primary-blue rounded-4xl py-4 px-12 hover:bg-sky-blue-750'
                prefetch={false}
              >
                Download Now
              </Link>
            </div>
          </div>

          <h2 className='font-extrabold text-black font-manrope md:text-3xl sm:text-2xl text-xl mb-8 lg:mb-4'>
            <span className='text-primary-blue'>Download</span> the Mixdorm App
            Now!
          </h2>
          <p className='text-[#737373] text-sm mb-5'>
            With these powerful features, Mixdorm is more than just a hostel
            booking platform—it’s your ultimate travel companion. Meet fellow
            travelers, save on group expenses, share rides, and make
            unforgettable memories along the way.
          </p>
          <p className='text-[#737373] text-sm mb-5'>
            <span className='text-primary-blue'>Hit the Download Button </span>
            now to start your journey with Mixdorm! Experience travel with ease,
            meet new people, and explore the world in the smartest, most
            budget-friendly way possible.
          </p>
        </div>
      </section>
    </>
  );
};

export default Meetbuddies;
