import express from 'express';
import { add<PERSON><PERSON><PERSON><PERSON>ontroller, getQueryByIdController, listAllQueriesController } from '../controller/contactUs.js';

const router = express.Router();

router.post('/', addQueryController);
router.get('/', listAllQueriesController);
router.get('/:id', getQueryByIdController);

export default router;

/**
 * @swagger
 * /contact-us:
 *   post:
 *     summary: Create a new contact us query
 *     tags: [ContactUs]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/definitions/ContactUsInput'
 *     responses:
 *       201:
 *         description: Query created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/ContactUsResponse'
 *       500:
 *         description: Internal Server Error
*/

/**
 * @swagger
 * /contact-us:
 *   get:
 *     summary: List all queries
 *     tags: [ContactUs]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of queries per page
 *     responses:
 *       200:
 *         description: Queried retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/ContactUsListResponse'
 *       500:
 *         description: Internal Server Error
*/

/**
 * @swagger
 * /contact-us/{id}:
 *   get:
 *     summary: Get a query by ID
 *     tags: [ContactUs]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Query ID
 *     responses:
 *       200:
 *         description: Query retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/ContactUsResponse'
 *       404:
 *         description: Query not found
 *       500:
 *         description: Internal Server Error
*/

/**
 * @swagger
 * definitions:
 *   ContactUsInput:
 *     type: object
 *     required:
 *       - name
 *       - email
 *       - subject
 *     properties:
 *       name:
 *         type: string
 *         description: Name of the user submitting the query
 *       email:
 *         type: string
 *         format: email
 *         description: The email of the user submitting the query
 *       subject:
 *         type: string
 *         description: The subject of the query
 *       categories:
 *         type: string
 *         description: The category of the query
 *       description:
 *         type: string
 *         description: The description of the query
 *   ContactUsResponse:
 *     type: object
 *     properties:
 *       _id:
 *         type: string
 *         description: Unique ID of the query
 *       email:
 *         type: string
 *         format: email
 *         description: The email of the user submitting the query
 *       subject:
 *         type: string
 *         description: The subject of the query
 *       categories:
 *         type: string
 *         description: The category of the query
 *       description:
 *         type: string
 *         description: The description of the query
 *       status:
 *         type: string
 *         description: The status of the query (0 = pending, 1 = resolved, etc.)
 *       createdAt:
 *         type: string
 *         format: date-time
 *         description: The date and time when the query was created
 *       updatedAt:
 *         type: string
 *         format: date-time
 *         description: The date and time when the query was last updated
 *   ContactUsListResponse:
 *     type: object
 *     properties:
 *       queries:
 *         type: array
 *         items:
 *           $ref: '#/definitions/ContactUsResponse'
 *       pagination:
 *         type: object
 *         properties:
 *           page:
 *             type: integer
 *             description: The current page number
 *           limit:
 *             type: integer
 *             description: The number of queries per page
 *           totalPages:
 *             type: integer
 *             description: The total number of pages
 *           totalQueries:
 *             type: integer
 *             description: The total number of queries
*/
