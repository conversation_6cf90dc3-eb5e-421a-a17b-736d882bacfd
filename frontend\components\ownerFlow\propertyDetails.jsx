import React, { useState, useCallback, useEffect } from "react";
import ExpandMoreRoundedIcon from "@mui/icons-material/ExpandMoreRounded";
import "react-phone-number-input/style.css";
import { Country, State, City } from "country-state-city";
import Select from "react-select";
import { Autocomplete } from "@react-google-maps/api";
import toast, { Toaster } from "react-hot-toast";
import Link from "next/link";
import Image from "next/image";

const initialFormData = {
  name: "",
  type: "",
  email: "",
  address: { lineOne: "", lineTwo: "" },
  postCode: "",
  country: "",
  countryName: "",
  state: "",
  stateName: "",
  city: "",
  termsAccepted: false,
  phoneNumber: "",
  latitude: "",
  longitude: "",
};

const typeOptions = [
  { value: "hostel", label: "Hostel" },
  { value: "homestay", label: "Homestay" },
  { value: "bed_and_breakfast", label: "Bed & Breakfast" },
];

const googleMapsApiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

function PropertyDetails({ onContinue, viewFormData, data, updateData }) {
  const [formData, setFormData] = useState(initialFormData);
  const [isLoading, setIsLoading] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [phoneNumber, setPhoneNumber] = useState("");
  const [stateOptions, setStateOptions] = useState([]);
  const [cityOptions, setCityOptions] = useState([]);
  const [autocomplete, setAutocomplete] = useState(null);
  const [isApiLoaded, setIsApiLoaded] = useState(false);

  console.log("data4631", data, formData);

  const loadGoogleMapsApi = () => {
    const script = document.createElement("script");
    script.src = `https://maps.googleapis.com/maps/api/js?key=${googleMapsApiKey}&libraries=places`;
    script.async = true;
    script.onload = () => setIsApiLoaded(true);
    script.onerror = () => {
      console.error("Failed to load Google Maps API");
      setIsApiLoaded(false);
    };
    document.head.appendChild(script);
  };

  useEffect(() => {
    if (window.google && window.google.maps) {
      setIsApiLoaded(true);
    } else {
      loadGoogleMapsApi();
    }
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      if (viewFormData) {
        setFormData(viewFormData);
        setPhoneNumber(viewFormData.phoneNumber);
        if (viewFormData.country) {
          const states = State.getStatesOfCountry(viewFormData.country);
          setStateOptions(
            states.map((s) => ({ label: s.name, value: s.isoCode }))
          );
        }
        if (viewFormData.state) {
          const cities = City.getCitiesOfState(
            viewFormData.country,
            viewFormData.state
          );
          setCityOptions(cities.map((c) => ({ label: c.name, value: c.name })));
        }
      }
    };
    fetchData();
  }, [viewFormData]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    // Update local form data
    const updatedFormData = {
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    };

    // Set the local state
    setFormData(updatedFormData);

    // Update the shared state via `updateData`
    updateData(updatedFormData);
  };

  const handleCountryChange = (selectedOption) => {
    const countryCode = selectedOption.value;
    const countryName = Country.getCountryByCode(countryCode)?.name || "";

    setFormData({
      ...formData,
      country: countryCode,
      countryName,
      state: "",
      stateName: "",
      city: "",
    });

    updateData({
      country: countryCode,
      countryName,
      state: "",
      stateName: "",
      city: "",
    });

    const states = State.getStatesOfCountry(countryCode);
    setStateOptions(states.map((s) => ({ label: s.name, value: s.isoCode })));
    setCityOptions([]);
  };

  const handleStateChange = (selectedOption) => {
    const stateCode = selectedOption.value;
    const countryCode = formData.country;
    const stateName =
      State.getStateByCodeAndCountry(stateCode, countryCode)?.name || "";

    setFormData({
      ...formData,
      state: stateCode,
      stateName,
      city: "",
    });

    updateData({
      state: stateCode,
      stateName,
      city: "",
    });

    const cities = City.getCitiesOfState(countryCode, stateCode);
    setCityOptions(cities.map((c) => ({ label: c.name, value: c.name })));
  };

  const handleAddressChange = useCallback((place) => {
    if (!place || !place.geometry || !place.address_components) {
      toast.error("Invalid place selected.");
      return;
    }

    const addressComponents = place.address_components;

    let country = "";
    let state = "";
    let stateName = "";
    let city = "";
    let postCode = "";

    addressComponents.forEach((component) => {
      const types = component.types;

      if (types.includes("country")) {
        country = component.short_name;
      }
      if (types.includes("administrative_area_level_1")) {
        state = component.short_name;
        stateName = component.long_name;
      }
      if (
        types.includes("locality") ||
        types.includes("administrative_area_level_2") ||
        types.includes("administrative_area_level_3")
      ) {
        city = component.long_name;
      }
      if (types.includes("postal_code")) {
        postCode = component.long_name;
      }
    });

    setFormData((prevData) => ({
      ...prevData,
      address: { ...prevData.address, lineOne: place.formatted_address },
      latitude: place.geometry.location.lat(),
      longitude: place.geometry.location.lng(),
      country,
      state,
      stateName,
      city,
      postCode,
    }));

    updateData({
      address: { ...data.address, lineOne: place.formatted_address },
      latitude: place.geometry.location.lat(),
      longitude: place.geometry.location.lng(),
      country,
      state,
      stateName,
      city,
      postCode,
    });

    const countryName = Country.getCountryByCode(country)?.name || "";
    setFormData((prevData) => ({
      ...prevData,
      country,
      countryName,
    }));
    updateData({
      country,
      countryName,
    });

    if (country) {
      const states = State.getStatesOfCountry(country);
      setStateOptions(states.map((s) => ({ label: s.name, value: s.isoCode })));
    }

    if (country && state) {
      const cities = City.getCitiesOfState(country, state);
      setCityOptions(cities.map((c) => ({ label: c.name, value: c.name })));
    }
  }, []);

  const validateForm = (data) => {
    return (
      data.type &&
      data.name &&
      data.address.lineOne &&
      data.postCode &&
      data.country &&
      data.state &&
      data.termsAccepted
    );
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm(data)) {
      toast.error("All fields are required and phone number must be valid.");
      return;
    }
    onContinue(viewFormData.id);

    setIsLoading(true);
    // try {
    //   const payload = {
    //     type: formData.type,
    //     name: formData.name,
    //     address: {
    //       lineOne: formData.address.lineOne,
    //       city: formData.city,
    //       state: formData.stateName,
    //       country: formData.countryName,
    //       zipcode: formData.postCode,
    //     },
    //     latitude: formData.latitude,
    //     longitude: formData.longitude,
    //   };
    // if (viewFormData && viewFormData.id) {
    //   const response = await editPropertyApi(viewFormData.id, payload);
    //   if (response?.data?.status) {
    //     setPropertyId(viewFormData.id);
    //     toast.success(
    //       response?.data?.message || "Property updated successfully!"
    //     );
    //     onContinue(viewFormData.id);
    //   }
    // } else {
    //   const response = await AddPropertyApi(payload);
    //   if (response?.data?.status) {
    //     setPropertyId(response?.data?.data?._id);
    //     toast.success(
    //       response?.data?.message || "Property added successfully!"
    //     );
    //     onContinue(response.data.data._id);
    //   }
    // }
    // } catch (error) {
    //   console.error("Error submitting form:", error);
    //   toast.error("Failed to submit property details.");
    // } finally {
    //   setIsLoading(false);
    // }
  };

  if (!isApiLoaded) {
    return <div>Loading...</div>;
  }

  return (
    <div className='max-w-3xl mx-auto'>
      <div className='bg-white p-6 rounded-[30px] shadow-md'>
        <h2 className='py-8 text-2xl font-bold text-gray-800 xs:w-[90%] w-full mx-auto'>
          👋 Hi, <span className='text-primary-blue'>Big Boss</span> Add your
          property details
        </h2>
        <form className='xs:w-[90%] w-full mx-auto' onSubmit={handleSubmit}>
          <h4 className='my-5 font-bold text-black font-manrope'>
            Property Information
          </h4>
          <div className='space-y-4'>
            <div className='relative'>
              <input
                id='name'
                name='name'
                type='text'
                placeholder='Property Name'
                value={data.name}
                onChange={handleChange}
                className='w-full px-4 py-3 mt-2 border rounded-3xl focus:outline-none focus:ring-[#40E0D0] bg-transparent font-manrope placeholder:font-light h-[52px]'
              />
            </div>
            {/* <input
              id='email'
              name='email'
              type='email'
              placeholder='Email'
              value={formData.email}
              onChange={handleChange}
              className='w-full px-4 py-3 mt-2 border rounded-3xl focus:outline-none focus:ring-[#40E0D0] bg-transparent'
            /> */}
            <div className='relative'>
              <select
                id='type'
                name='type'
                value={data.type}
                onChange={handleChange}
                className='w-full px-4 py-3 mt-2 border rounded-3xl focus:outline-none focus:ring-[#40E0D0] bg-transparent appearance-none font-manrope placeholder:font-light'
                style={{ background: "none", paddingRight: "2.5rem" }}
              >
                <option value='' disabled>
                  Select Property Type
                </option>
                {typeOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <ExpandMoreRoundedIcon className='absolute right-3 top-[56%] transform -translate-y-1/2 pointer-events-none text-[#0B0A0A]' />
            </div>

            {/* <div className='flex items-center space-x-2'>
              <PhoneInput
                defaultCountry='IN'
                international
                withCountryCallingCode
                placeholder='Phone Number'
                value={phoneNumber}
                onChange={setPhoneNumber}
                className='w-full px-4 py-3 mt-2 border rounded-3xl focus:outline-none focus:ring-[#40E0D0] bg-transparent'
              />
            </div> */}
            <div className='relative'>
              <h4 className='my-5 font-bold text-black font-manrope'>
                Property Location
              </h4>
              <Autocomplete
                onLoad={(autocompleteInstance) =>
                  setAutocomplete(autocompleteInstance)
                }
                onPlaceChanged={() => {
                  if (autocomplete) {
                    const place = autocomplete.getPlace();
                    if (!place || !place.geometry) {
                      toast.error("Invalid place selected.");
                      return;
                    }
                    handleAddressChange(place); // Call your address handling logic
                  } else {
                    toast.error("Autocomplete is not initialized yet.");
                  }
                }}
              >
                <input
                  id='address.lineOne'
                  name='address'
                  type='text'
                  placeholder='Address'
                  value={data.address.lineOne}
                  onChange={(e) => {
                    const updatedAddress = {
                      ...data.address,
                      lineOne: e.target.value,
                    };

                    // Update local state
                    setFormData({
                      ...data,
                      address: updatedAddress,
                    });

                    // Update shared state via `updateData`
                    updateData({
                      ...data,
                      address: updatedAddress,
                    });
                  }}
                  className='w-full px-4 py-3 mt-2 border border-[#EEEEEE] rounded-3xl focus:outline-none focus:ring-[#40E0D0] bg-transparent font-manrope placeholder:font-light h-[52px]'
                />
              </Autocomplete>
              <Image
                className='absolute right-3 top-14'
                src='${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/blue-location.svg'
                alt=''
                width={24}
                height={24}
              />
            </div>
            <input
              id='postCode'
              name='postCode'
              type='text'
              placeholder='Post Code'
              value={data.postCode}
              onChange={handleChange}
              className='w-full px-4 py-3 mt-2 border rounded-3xl focus:outline-none focus:ring-[#40E0D0] bg-transparent font-manrope placeholder:font-light h-[52px]'
            />
            <Select
              options={Country.getAllCountries().map((c) => ({
                label: c.name,
                value: c.isoCode,
              }))}
              onChange={handleCountryChange}
              value={
                data?.country
                  ? Country.getAllCountries()
                      .map((c) => ({
                        label: c.name,
                        value: c.isoCode,
                      }))
                      .find((c) => c.value === data.country) || null
                  : null
              }
              placeholder='Select Country'
              className='w-full font-manrope placeholder:font-light h-[52px]'
            />

            <Select
              options={stateOptions}
              onChange={handleStateChange}
              placeholder='Select State'
              value={stateOptions.find((s) => s.value === data.state)}
              className='h-[52px]'
            />

            <Select
              options={cityOptions}
              onChange={(e) => {
                const updatedData = { ...data, city: e.value };

                // Update local state
                setFormData(updatedData);

                // Update shared state
                updateData(updatedData);
              }}
              placeholder='Select City'
              value={cityOptions.find((c) => c.value === data.city)}
              className='h-[52px]'
            />

            <div className='flex items-center'>
              <input
                id='termsAccepted'
                name='termsAccepted'
                type='checkbox'
                checked={data.termsAccepted}
                onChange={handleChange}
                className='mr-2'
              />
              <label
                className='font-normal font-manrope text-[#6D6D6D]'
                htmlFor='termsAccepted'
              >
                I have read and agreed to all{" "}
                <Link
                  href='/terms-condition'
                  className='text-primary-blue font-normal uppercase underline underline-offset-2'
                  target='_blank'
                  prefetch={false}
                >
                  {" "}
                  Terms and Condition
                </Link>
              </label>
            </div>

            <button
              type='submit'
              disabled={isLoading || !validateForm(data)}
              className={`w-full py-3 mt-4 rounded-3xl focus:outline-none ${
                isLoading || !validateForm(data)
                  ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                  : "bg-[#e5e7eb] text-black hover:bg-primary-blue"
              }`}
            >
              {isLoading ? "Submitting..." : "Submit"}
            </button>
          </div>
        </form>
        <Toaster />
      </div>
    </div>
  );
}

export default PropertyDetails;
