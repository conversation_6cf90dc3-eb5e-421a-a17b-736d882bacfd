import roomModel from '../models/room.js';
import RatePlan from '../models/roomRatePlans.js';

const addRoom = async (data, userId) => {
    const roomData = {
        ...data,
        addedBy: userId  // Include the user ID in the addedBy field
    };
    return await roomModel.create(roomData);
};

// const updateRoomBySlug = async (slug, newData) => {
//     try {
//         const room = await roomModel.findOne({ slug });
//         if (!room) {
//             throw new Error('Room Not Found');
//         }
//         const updatedRoom = await roomModel.updateOne({ slug }, { $set: newData });
//         return updatedRoom;
//     } catch (error) {
//         console.error("Error updating room:", error.message);
//         throw error;
//     }
// };

import mongoose from "mongoose";

export const updateRoomById = async (roomId, newData) => {
    try {
        const room = await roomModel.findById(roomId);
        if (!room) throw new Error("Room Not Found");

        const updateOps = {};

        // /** Handle images updates */
        // if (Array.isArray(newData.images) && newData.images.length > 0) {
        //     const imageIdsToDelete = [];
        //     const imagesToAdd = [];

        //     for (const image of newData.images) {
        //         if (image.action === "delete" && Array.isArray(image.id) && image.id.length > 0) {
        //             imageIdsToDelete.push(...image.id);
        //         } else if (image.action === "delete" && image._id) {
        //             imageIdsToDelete.push(image._id);
        //         } else if (image.action === "add") {
        //             const imgDoc = {
        //                 _id: image._id || new mongoose.Types.ObjectId()
        //             };
        //             if (image.s3Uri) imgDoc.s3Uri = image.s3Uri;
        //             if (image.objectUrl) imgDoc.objectUrl = image.objectUrl;
        //             if (image.s3ObjectUrl) imgDoc.s3ObjectUrl = image.s3ObjectUrl;

        //             imagesToAdd.push(imgDoc);
        //         }
        //     }

        //     if (imageIdsToDelete.length > 0) {
        //         const objectIdsToDelete = imageIdsToDelete.map(id => new mongoose.Types.ObjectId(id));
        //         updateOps.$pull = {
        //             ...(updateOps.$pull || {}),
        //             images: { _id: { $in: objectIdsToDelete } }
        //         };
        //     }

        //     if (imagesToAdd.length > 0) {
        //         updateOps.$push = {
        //             ...(updateOps.$push || {}),
        //             images: { $each: imagesToAdd }
        //         };
        //     }

        //     // Prevent full array overwrite by removing from $set
        //     delete newData.images;
        // }

        /** Handle slug change if name changes */
        if (newData.name && newData.name !== room.name) {
            newData.slug = await roomModel.generateSlug(newData.name, room._id);
        }

        /** Handle other fields */
        if (Object.keys(newData).length > 0) {
            updateOps.$set = { ...(updateOps.$set || {}), ...newData };
        }

        /** Perform atomic update */
        const updatedRoom = await roomModel.updateOne({ _id: roomId }, updateOps);
        return updatedRoom;

    } catch (error) {
        console.error("Error updating room:", error.message);
        throw error;
    }
};



const getRoomsByPropertyId = async (propertyId, filter, page, limit) => {
    const skip = (page - 1) * limit;
    const query = { property: propertyId, ...filter, isDeleted: false, isActive: true };
    const rooms = await roomModel.find(query).skip(skip).limit(limit).exec();
    const totalRooms = await roomModel.countDocuments(query);

    return { rooms, totalRooms };
};


const getRoomBySlug = async (slug) => {
    try {
        const room = await roomModel.findOne({ slug });
        if (!room) {
            throw new Error('Room Not Found');
        }
        return room;
    } catch (error) {
        console.error("Error getting room:", error.message);
        throw error;
    }
};

const deleteRoomBySlug = async (slug) => {
    try {
        const room = await roomModel.findOne({ slug });
        if (!room) {
            throw new Error('Room Not Found');
        }
        await roomModel.updateOne({ slug }, { $set: { isDeleted: true, isActive: false } });
        return { message: 'Deleted Successfully' };
    } catch (error) {
        console.error("Error deleting room:", error.message);
        throw error;
    }
};

const getRoomListByPropertyId = async (propertyId) => {
    try {
        const rooms = await roomModel.find({ property: propertyId }, '_id name').exec();
        if (!rooms || rooms.length === 0) {
            throw new Error('No rooms found for the given property ID');
        }
        return rooms;
    } catch (error) {
        console.error("Error fetching rooms by property ID:", error.message);
        throw error;
    }
};



export const getNextRoomId = async () => {
    try {
        const lastRoom = await roomModel
            .find({ roomId: { $exists: true } })
            .sort({ roomId: -1 }) // descending to get max roomId
            .limit(1)
        console.log("lastRoom", lastRoom)
        if (!lastRoom || lastRoom.length === 0 || !lastRoom[0].roomId) {
            throw new Error('No existing roomId found in the database');
        }

        return lastRoom[0].roomId + 1;
    } catch (error) {
        console.error("Error fetching next roomId by property ID:", error.message);
        throw error;
    }
};
export const getNextRatePlanId = async () => {
    try {
        const lastRate = await RatePlan
            .find({ otaRateId: { $exists: true } })
            .sort({ otaRateId: -1 }) // descending to get max otaRateId
            .limit(1)
            .exec();
        console.log("lastRate", lastRate)


        // If no existing rate plans, start from 800000
        if (!lastRate.length || !lastRate[0]?.otaRateId) {
            return 800000;
        }
        return lastRate[0].otaRateId + 1;
    } catch (error) {
        console.error("Error fetching next roomId by property ID:", error.message);
        throw error;
    }
};

export { addRoom, getRoomsByPropertyId, getRoomBySlug, deleteRoomBySlug, getRoomListByPropertyId };