import React from "react";
import { FaEllipsisV } from "react-icons/fa";
import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight,
} from "react-icons/md";
import Link from "next/link";

const Status = () => {
  const status = [
    {
      title : "Verified",
      userId: 1,
      color : "#00B69B",
      active: "Active",

    },
    {
      title : "Unverified",
      userId: 1,
      color : "#EF3826",
      active: "Active",

    },
    {
      title : "Booked",
      userId: 1,
      color : "#00B69B",
      active: "Active",

    },
    {
      title : "Cancelled",
      userId: 1,
      color : "#EF3826",
      active: "Active",

    },
    {
      title : "Processing",
      userId: 1,
      color : "#00B69B",
      active: "Active",

    },
    {
      title : "On Hold",
      userId: 1,
      color : "#EF3826",
      active: "Active",

    },
    {
      title : "Sent",
      userId: 1,
      color : "#00B69B",
      active: "Active",


    },
  ];

  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">Status</h2>

      <div className="overflow-x-auto mt-5 rounded-xl bg-white border dark:bg-black dark:border-none">
        <table className="min-w-full mb-10">
          <thead>
            <tr className="w-full rounded-xl border dark:border-none">
              <th className="pb-6 pt-4 px-5 pl-7  text-sm font-poppins  text-left font-semibold text-black/75 dark:text-[#B6B6B6]">
                TITLE
              </th>
              <th className="pb-6 pt-4 px-5 pl-7 text-sm font-poppins   text-left font-semibold text-black/75 dark:text-[#B6B6B6]">
                COLOR
              </th>
              <th className="pb-6 pt-4 px-5 pl-7  text-sm font-poppins  text-left font-semibold text-black/75 dark:text-[#B6B6B6]">
                ACTIVE
              </th>
              <th className="pb-6 pt-4 px-5 pl-8 md:pl-5  text-sm font-poppins  text-left font-semibold  text-black/75 dark:text-[#B6B6B6]">
                ACTION
              </th>

            </tr>
          </thead>
          <tbody>
            {status.map((log, index) => (
              <tr key={index} className="w-full border-x border-y dark:border-x-0">
                <td className="py-6 px-8 md:px-5  text-black/65 dark:text-[#757575]">
                  <div className="text-sm font-poppins font-medium whitespace-nowrap dark:text-[#757575]">{log.title}</div>
                </td>
                <td className="py-6 px-8 md:px-5  text-black/65 whitespace-nowrap text-sm font-poppins font-medium dark:text-[#757575]">
                  {log.color}
                </td>
                <td className="py-6 px-8 md:px-5 whitespace-nowrap">
                  <span className="text-primary-blue text-sm font-poppins font-medium bg-teal-100 rounded-md px-3 py-1.5 dark:bg-[#0e6c7267]">
                    {log.active}
                  </span>
                </td>

                <td className="py-6 px-8  ">
                  <button className="text-gray-500 bg-slate-50 dark:bg-[#363535] border p-1.5 rounded-md dark:border-none">
                    {" "}
                    <Link href={"/superadmin/dashboard/management-status-details"}>
                    <FaEllipsisV className="text-gray-400 hover:text-gray-500 dark:text-[#757575]" />
                    </Link>
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
       <div className="flex justify-between items-center mt-5">
                    <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
                    <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
                      <a
                        href="#"
                        className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                      >
                        <span className="sr-only">Next Page</span>
                        <MdOutlineKeyboardArrowLeft />
                      </a>
            
                      <a
                        href="#"
                        className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                      >
                        <span className="sr-only">Next Page</span>
                        <MdOutlineKeyboardArrowRight />
                      </a>
                    </div>
                  </div>
    </div>
  );
};

export default Status;
