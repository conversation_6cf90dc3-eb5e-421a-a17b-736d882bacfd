import React, { useState } from "react";
import Image from "next/image";
import Input from "@/components/superadmin/Input";
import Label from "@/components/superadmin/Label";
import Button from "@/components/superadmin/Button";
import toast from "react-hot-toast";
import { setItemLocalStorage, setToken } from "@/utils/browserSetting";
import { useNavbar } from "@/components/home/<USER>";
import { logInAdminApi } from "@/services/adminflowServices";
import { sendOtpAdminApi } from "@/services/adminflowServices";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import VisibilityOffOutlinedIcon from "@mui/icons-material/VisibilityOffOutlined";
import { useRouter } from "next/router";
import Loader from "@/components/loader/loader";

const Signin = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [emailError, setEmailError] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const { updateUserStatus, updateUserRole } = useNavbar();
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();

  const validate = () => {
    let valid = true;
    setEmailError("");
    setPasswordError("");
    if (!email) {
      setEmailError("Email is required");
      valid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      setEmailError("Invalid email format");
      valid = false;
    }
    if (!password) {
      setPasswordError("Password is required");
      valid = false;
    }
    return valid;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validate()) return;
    setLoading(true);
    try {
      const response = await logInAdminApi({
        email: email,
        password: password,
      });
      if (response?.data?.status) {
        setItemLocalStorage("name", response?.data?.data?.user?.name.first);
        setItemLocalStorage("id", response?.data?.data?.user?._id);
        setItemLocalStorage("role", response?.data?.data?.user?.role);
        setToken(response?.data?.data?.token);
        updateUserStatus(response?.data?.data?.token);
        updateUserRole(response?.data?.data?.user?.role);
        toast.success(response?.data?.message);
        // Call sendOtpAdminApi after successful login
        const otpRes = await sendOtpAdminApi({ email });
        if (otpRes?.data?.status) {
          toast.success(otpRes?.data?.message || "Email Verification Sent");
          router.push("/superadmin/auth");
        }
      } else {
        toast.error(response?.data?.message || "Login failed");
      }
    } catch (error) {
      toast.error(
        error?.response?.data?.message || error.message || "Login failed"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Loader open={loading} />
      <section className='flex flex-col md:flex-row w-full h-screen bg-[#50C2FF] md:bg-white lg:bg-white'>
        {/* Left Section */}
        <div className='w-full md:w-1/2 flex items-center justify-center py-36 lg:py-0 md:py-0'>
          <div className='flex flex-col w-[90%] md:w-[68%] px-8 py-10 md:px-4 md:py-20 bg-white border shadow-2xl'>
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/logo.svg`}
              width={155}
              height={40}
              alt='Mixdorm'
              title='Mixdorm'
              className='object-contain w-fit h-fit max-w-[186px] max-h-11'
              loading='lazy'
            />
            <h2 className='mt-8 font-semibold text-black text-xl'>
              Administration Login
            </h2>
            <div>
              <form className='mt-4' onSubmit={handleSubmit}>
                <Label>Email</Label>
                <Input
                  type='email'
                  placeholder='<EMAIL>'
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className={emailError ? "border-red-500" : ""}
                />
                {emailError && (
                  <div className='text-red-500 text-xs '>{emailError}</div>
                )}
                <Label>Password</Label>
                <div className='relative'>
                  <Input
                    type={showPassword ? "text" : "password"}
                    placeholder='Password'
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className={passwordError ? "border-red-500" : ""}
                  />
                  <button
                    type='button'
                    className='absolute right-4 top-[15px] text-gray-300 transform'
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <VisibilityOutlinedIcon />
                    ) : (
                      <VisibilityOffOutlinedIcon />
                    )}
                  </button>
                </div>
                {passwordError && (
                  <div className='text-red-500 text-xs mb-2'>
                    {passwordError}
                  </div>
                )}

                <Button
                  text={
                    loading ? (
                      <div className='absolute loader-button'>
                        <div className='loader'></div>
                      </div>
                    ) : (
                      "Login"
                    )
                  }
                  type='submit'
                  disabled={loading}
                />
              </form>
            </div>
          </div>
        </div>

        {/* Right Section */}
        <div className='hidden md:flex w-1/2 bg-[#50C2FF] items-end justify-start pt-10 pl-10'>
          <Image
            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/superadmin_login_img.png`}
            width={720}
            height={1024}
            loading='lazy'
            alt='Admin dash'
            className='h-full w-full object-cover object-left-top'
          />
        </div>
      </section>
    </>
  );
};

export default Signin;
