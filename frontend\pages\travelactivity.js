import Image from "next/image";
import { Box, Tab } from "@mui/material";
import { <PERSON>b<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bPanel } from "@mui/lab";
import { useEffect, useRef, useState } from "react";
import { useRouter } from "next/router";
import Banner from "@/components/home/<USER>";
import { getTravelActivitesApi } from "@/services/webflowServices";
import dynamic from "next/dynamic";
import Link from "next/link";
import Head from "next/head";

const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

const Page = () => {
  const [value, setValue] = useState("0");
  const [activitesData, setActiviesData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [imagesLoaded, setImagesLoaded] = useState({});
  const [imagesError, setImagesError] = useState({});
  const router = useRouter();
  const isFirstRender = useRef(null);

  const categories = [
    "Explore Ruins",
    "Beach Snorkel",
    "City Cycling",
    "Mountain Trek",
    "Food Tour",
    "River Cruise",
    "Spa Retreat",
    "Road Trips",
    "City Rush",
  ];

  useEffect(() => {
    const { category: urlCategory } = router.query;

    if (!urlCategory) {
      // If no query parameter is present, redirect to the first category
      router.replace(
        {
          pathname: router.pathname,
          query: { category: encodeURIComponent(categories[0]) },
        },
        undefined,
        { shallow: true }
      );
      setValue("0");
      fetchActivitesData(categories[0]);
    } else {
      const initialIndex = categories.indexOf(decodeURIComponent(urlCategory));

      if (initialIndex !== -1) {
        setValue(String(initialIndex));
        fetchActivitesData(categories[initialIndex]);
      }
    }
  }, [router.query]);

  const fetchActivitesData = async (category) => {
    if (!isFirstRender.current) {
      setLoading(true);
      try {
        const response = await getTravelActivitesApi(category);
        setActiviesData(response?.data?.data?.activities || []);
      } catch (error) {
        console.error("Error fetching activites data:", error);
      } finally {
        setLoading(false);
      }
    } else {
      isFirstRender.current = false;
    }
  };

  const handleChange = (event, newValue) => {
    setValue(newValue);
    const selectedCategory = categories[newValue];
    router.push(
      {
        pathname: router.pathname,
        query: { category: encodeURIComponent(selectedCategory) },
      },
      undefined,
      { shallow: true }
    );
  };
  // const { category } = router.query;
  // const decodedCategory = decodeURIComponent(category || "Travel Activities");
  // const title = `${decodedCategory} Experiences | Mixdorm`;

  const handleImageLoad = (id, index) => {
    setImagesLoaded(prev => ({ ...prev, [`${id}-${index}`]: true }));
  };

  const handleImageError = (id, index) => {
    setImagesError(prev => ({ ...prev, [`${id}-${index}`]: true }));
  };

  return (
    <>
      <Head>
        <title>
        {/* {title} */}
        Top Hostels Near Historic Sites & Ancient Ruins
        </title>
        <meta
          name='description'
          content='From Angkor Wat to Machu Picchu, Explore Global Ruins and Stay In Nearby Hostels for a Cultural, Budget-Friendly Travel Experience.'
        />
      </Head>
      <Loader open={loading} />
      <div
        style={{
          backgroundImage: `url(${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/top-black-bg.jpeg)`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
        className="pb-10 md:pb-14"
      >
        <Banner />{" "}
      </div>
      <section className='pt-8 lg:pb-28 border-t border-gray-200 '>
        <div className='container px-4 lg:px-14'>
          <h2 className='font-bold text-black font-manrope md:text-3xl sm:text-2xl text-xl mb-6'>
            Travel by <span className='text-primary-blue xs:text-4xl text-3xl  font-semibold font-mashiny'> Activities</span>
          </h2>

          {loading ? (
            <div className="space-y-6">
              {/* Tab skeleton */}
              <div className="flex gap-2 overflow-x-auto pb-2">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="h-10 w-24 bg-gray-200 rounded-full animate-pulse"></div>
                ))}
              </div>

              {/* Content skeleton */}
              <div className="space-y-4">
                <div className="h-8 w-1/3 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-2/3 bg-gray-200 rounded animate-pulse"></div>

                {/* Activity card skeleton */}
                <div className="rounded-3xl border border-[#E4E6E8]">
                  <div className="w-full h-60 bg-gray-200 animate-pulse rounded-t-3xl"></div>
                  <div className="p-5 space-y-3">
                    <div className="h-5 w-1/2 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-4 w-3/4 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <Box sx={{ width: "100%", typography: "body1" }}>
              <TabContext value={value}>
                <Box>
                  <TabList
                    onChange={handleChange}
                    // aria-label="lab API tabs example"
                    indicatorColor='white'
                    className='gap-4 overflow-x-auto'
                    variant='scrollable'
                    scrollButtons='auto'
                    aria-label='scrollable auto tabs example'
                  >
                    {categories.map((category, index) => (
                      <Tab
                        label={category}
                        value={index.toString()}
                        key={index}
                        sx={{
                          fontSize: "14px",
                          textTransform: "capitalize",
                          borderRadius: "36px",
                          border: "1px solid #e4e6e8",
                          backgroundColor: "#fff",
                          fontWeight: "600",
                          letterSpacing: "0px",
                          fontFamily: "'Manrope', sans-serif",
                          margin: "0 5px",
                          "&.Mui-selected": {
                            backgroundColor: "#40E0D0",
                            color: "black",
                            borderColor: "#40E0D0",
                          },
                        }}
                      />
                    ))}
                  </TabList>
                </Box>
                {categories.map((category, index) => (
                  <TabPanel value={index.toString()} key={index} className='px-0 mb-8 md:mb-0'>
                    <h3 className='text-2xl font-bold mb-2'>{category}</h3>
                    <p className='text-[#737373] text-base'>
                      Explore ancient ruins and historical landmarks around the
                      world, ideal for history buffs.
                    </p>
                    {activitesData.map((activity, i) => {

                      const imageKey = `${activity.id}-${i}`;
                      const isLoaded = imagesLoaded[imageKey];
                      const hasError = imagesError[imageKey];

                      return (
                        <div
                          key={i}
                          className='relative rounded-3xl border border-[#E4E6E8] lg:mt-8 mt-6'
                        >
                          {/* Image placeholder for loading or error state */}

                          {(!isLoaded || hasError) && (
                            <div className="w-full md:h-96 h-60 bg-gray-200 animate-pulse rounded-t-3xl flex items-center justify-center">
                              <h1 className="text-white font-bold font-manrope text-4xl">
                                MixDorm
                              </h1>
                            </div>
                          )}

                          {/* Actual image */}
                          {!hasError && (
                            <div className="relative w-full md:h-96 h-60">
                              <Image
                                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}${activity?.image?.url || ''}`}
                                alt="Activity Image"
                                fill
                                className={`rounded-t-3xl object-cover transition-opacity duration-300 ${!isLoaded ? 'opacity-0' : 'opacity-100'
                                  }`}
                                loading="lazy"
                                onLoadingComplete={() => handleImageLoad(activity.id, i)}
                                onError={() => handleImageError(activity.id, i)}
                              />
                            </div>
                          )}

                          {/* <Image
                        src={activity?.image?.url}
                        width={1300}
                        height={370}
                        alt='Activity Image'
                        className='w-full rounded-t-3xl md:max-h-96 max-h-60 object-cover'
                        loading='lazy'
                      /> */}
                          <div className='p-5 relative'>
                            <div className='sm:flex justify-between md:mb-3 mb-2'>
                              <h4 className='md:text-xl text-base mb-0 font-bold'>
                                {activity?.name}
                              </h4>

                              <h4 className='md:text-xl text-base mb-0 font-bold cursor-pointer'>
                                Hostel:
                                <Link
                                  href={`/hostels-detail/${activity?.hostelId}`}
                                  prefetch={false}
                                  className=' cursor-pointer hover:text-primary-blue'
                                >
                                  {activity?.hostel}
                                </Link>
                              </h4>
                            </div>
                            <p className='text-[#737373] md:text-base text-sm'>
                              {activity?.description}
                            </p>
                          </div>
                        </div>)
                    })}
                  </TabPanel>
                ))}
              </TabContext>
            </Box>)}
        </div>
      </section>
    </>
  );
};

export default Page;
