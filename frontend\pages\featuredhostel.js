/* eslint-disable react/jsx-key */
import React, { useEffect, useRef, useState } from "react";
import SwapVertOutlinedIcon from "@mui/icons-material/SwapVertOutlined";
import Image from "next/image";
import dynamic from "next/dynamic";
import Banner from "@/components/home/<USER>";
import { getFeaturedHostelApi } from "@/services/webflowServices";
import { useNavbar } from "@/components/home/<USER>";
import countries from "world-countries";
import Head from "next/head";
import { TbMap2 } from "react-icons/tb";
import HostelCard from "@/components/home/<USER>";
// const HostelCard = dynamic(
//   () => import("@/components/home/<USER>"),
//   {
//     ssr: false,
//   }
// );
const NewMapComponent = dynamic(
  () => import("@/components/map/newMapComponent"),
  {
    ssr: false,
  }
);
// const MapComponent = dynamic(() => import("@/components/map/mapComponent"), {
//   ssr: false,
// });
const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

const Page = () => {
  const [hostelData, setHostelData] = useState([]);
  const [locationAbout, setLocationAbout] = useState("");
  const [loading, setLoading] = useState(true);
  const [isUpdate, setIsUpdateData] = useState(false);
  const [currencyData, setCurrencyData] = useState({});
  const isFirstRender = useRef(null);
  const { currencyCode2, token } = useNavbar();
  const [showMap, setShowMap] = useState(false);
  const [showMapList, setShowMapList] = useState(false);
  const { updateMapState } = useNavbar();
  const mapBottomRef = useRef(null);
  const [loadingLocationAbout, setLoadingLocationAbout] = useState(true);

  const handleMap = () => {
    setShowMap(true);
    setShowMapList(false);
    updateMapState(true);

    // Scroll to the bottom of the map after a short delay (wait for rendering)
    setTimeout(() => {
      mapBottomRef.current?.scrollIntoView({ behavior: "smooth" });
    }, 100);
  };

  useEffect(() => {
    const fetchHostelData = async () => {
      setLoading(true);
      setLoadingLocationAbout(true);
      try {
        const response = await getFeaturedHostelApi(currencyCode2 || "USD");
        setHostelData(response?.data?.data?.properties || []);
        setLocationAbout(response?.data?.data?.about || "");
        setLoadingLocationAbout(false);
      } catch (error) {
        console.error("Error fetching stay data:", error);
        setLoadingLocationAbout(false);
      } finally {
        setLoading(false);
      }
    };
    if (!isFirstRender.current) {
      fetchHostelData();
    } else {
      isFirstRender.current = false;
    }
  }, [currencyCode2, isUpdate, token]);

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);
      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);

  const getCurrencySymbol = (currencyCode) => {
    return currencyData[currencyCode] || currencyCode;
  };

  const handleShowMap = (selectedItem = null) => {
    setShowMap(!showMap);
    updateMapState(!showMap);
    setShowMapList(false);

    if (selectedItem?._id) {
      localStorage.setItem("selectedMapProperty", JSON.stringify(selectedItem));
    }
  };

  return (
    <>
      <Head>
        <title>Featured Hostels Handpicked for You | Mixdorm</title>
      </Head>
      <Loader open={loading} />
      <div
        style={{
          backgroundImage: `url(${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/top-black-bg.jpeg)`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
        className="pb-10 md:pb-14"
      >
        <Banner />{" "}
      </div>
      <section
        className={` border-t border-gray-200  ${
          showMap ? "pt-6 pb-6" : "pt-8 lg:pb-28"
        }`}
      >
        <div className="w-full xxxl:container px-4 lg:px-10">
          <div className="sm:flex items-center justify-between pb-4">
            <h2
              className={`font-bold text-black font-manrope md:text-3xl sm:text-2xl text-xl ${
                showMap ? "mb-2" : "mb-6"
              }`}
            >
              Featured Hostel
            </h2>
            <div className="relative flex gap-2">
              <button className="border border-[#EEEEEE] text-black px-4 py-2 rounded-3xl flex items-center text-sm font-semibold">
                <SwapVertOutlinedIcon className="mr-2" />
                Sort by
              </button>
              <button className="border border-[#EEEEEE] text-black px-4 py-2 rounded-3xl flex items-center text-sm font-semibold">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/filter.png`}
                  width={14}
                  height={14}
                  alt="BgImg"
                  className="w-full max-w-3 mr-2"
                  loading="lazy"
                />
                Filter
              </button>
            </div>
          </div>
          {!showMap && (
            <div className="">
              <div className="w-full ">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {hostelData?.map((item) => (
                    <HostelCard
                      tag="Top Rated"
                      title={`${item?.name},${item?.address?.country}`}
                      image={item?.images?.[0]?.objectUrl}
                      guest="4-6 guest"
                      time="2 days 3 nights"
                      feature={item?.freeFacilities}
                      price={`${getCurrencySymbol(
                        item?.lowestAveragePricePerNight?.currency
                      )} ${item?.lowestAveragePricePerNight?.value}`}
                      rating="4.9"
                      review="672"
                      hostelId={item?._id}
                      setIsUpdateData={setIsUpdateData}
                      setLoading={setLoading}
                      liked={item?.liked}
                    />
                  ))}
                </div>
                <button
                  onClick={() => {
                    handleMap();
                    handleShowMap();
                  }}
                  className="fixed bottom-8 bg-black text-white z-20 px-4 py-2 rounded-[50px] left-[50%] transform -translate-x-1/2 flex items-center gap-2"
                >
                  <span className="flex items-center gap-2 text-base">
                    <TbMap2 className="text-xl" />
                    View Map
                  </span>
                </button>
              </div>
              {/* <div className="mid:w-[40%] w-full">
              <MapComponent property={hostelData} />
            </div> */}
            </div>
          )}
          {!showMap && (
            <div className="my-8 mb-12 lg:mb-0 p-4 rounded-3xl shadow-md border border-gray-200">
              {loadingLocationAbout ? (
                <div>
                  <div className="h-6 bg-slate-200 rounded w-1/2 mb-4 animate-pulse"></div>
                  <div className="space-y-2">
                    <div className="h-3 bg-slate-200 rounded w-[90%] animate-pulse"></div>
                    <div className="h-3 bg-slate-200 rounded w-[85%] animate-pulse"></div>
                    <div className="h-3 bg-slate-200 rounded w-[80%] animate-pulse"></div>
                  </div>
                </div>
              ) : (
                <>
                  <h2 className="font-bold text-black font-manrope md:text-3xl sm:text-2xl text-xl mb-3">
                    Why Book With{" "}
                    <span className="text-primary-blue xs:text-4xl text-3xl  font-semibold font-mashiny">
                      Mixdorm
                    </span>
                    ?
                  </h2>
                  <p className="text-[#737373] text-xs md:text-base">
                    {locationAbout}
                  </p>
                </>
              )}
            </div>
          )}
        </div>
        {!showMapList && showMap && (
          <div className="mid:w-[100%] w-full mt-4 self-start">
            <NewMapComponent
              property={hostelData}
              setShowMapList={setShowMapList}
              showMapList={showMapList}
              showMap={showMap}
              setShowMap={setShowMap}
            />
            <div ref={mapBottomRef} />
          </div>
        )}
      </section>
    </>
  );
};

export default Page;
