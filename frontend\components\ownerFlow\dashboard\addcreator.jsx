import React, { useEffect, useRef, useState } from "react";
import { CloudUpload } from "lucide-react";
import { format, isSameDay, isToday } from "date-fns";
import "react-datepicker/dist/react-datepicker.css";

const Addcreator = () => {
  // eslint-disable-next-line no-unused-vars
  const [calendarOpen, setCalendarOpen] = useState(false);

  // State to store form data and error messages
  const [formData, setFormData] = useState({
    instagramUrl: "",
    searchCreator: "",
    arrivalDate: null, // Single date for arrival
    email: "",
    serviceOffer: "",
    images: [],
  });

  const [errors, setErrors] = useState({
    instagramUrl: "",
    searchCreator: "",
    arrivalDate: "",
    email: "",
    serviceOffer: "",
    images: "",
  });

  const [showCalendar, setShowCalendar] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
 
  const [selectedDate, setSelectedDate] = useState(null);
  const calendarRef = useRef(null);
  const inputRef = useRef(null);

  const handlePrevMonth = () => {
    setCurrentMonth(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() - 1, 1)
    );
  };

  const handleNextMonth = () => {
    setCurrentMonth(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() + 1, 1)
    );
  };

  // Generate days for current month
  function generateDays(month) {
    const daysInMonth = new Date(
      month.getFullYear(),
      month.getMonth() + 1,
      0
    ).getDate();
    const firstDayIndex = new Date(
      month.getFullYear(),
      month.getMonth(),
      1
    ).getDay();

    const days = [];

    // Add blank spaces for days of previous month
    for (let i = 0; i < firstDayIndex; i++) {
      days.push(null);
    }

    // Add days of the current month
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(new Date(month.getFullYear(), month.getMonth(), i));
    }

    return days;
  }

  const handleInputChange = (e) => {
    const dateValue = e.target.value;
    setSelectedDate(new Date(dateValue));
  };

  // Close calendar if clicked outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target) &&
        !inputRef.current.contains(event.target)
      ) {
        setShowCalendar(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle file input change
  const handleFileChange = (e) => {
    setFormData((prev) => ({
      ...prev,
      images: e.target.files,
    }));
  };

  // Validate form data
  const validate = () => {
    let tempErrors = {};
    tempErrors.instagramUrl = formData.instagramUrl
      ? ""
      : "Instagram/Youtube URL is required.";
    tempErrors.searchCreator = formData.searchCreator
      ? ""
      : "Please select a creator.";
    tempErrors.arrivalDate = formData.arrivalDate
      ? ""
      : "Arrival date is required.";
    tempErrors.email = formData.email ? "" : "Email is required.";
    tempErrors.serviceOffer = formData.serviceOffer
      ? ""
      : "Service offer is required.";
    tempErrors.images =
      formData.images.length > 0 ? "" : "At least one image is required.";

    setErrors(tempErrors);

    return Object.values(tempErrors).every((error) => error === "");
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    if (validate()) {
      console.log("Form data:", formData);
      // Process form submission
    } else {
      console.log("Form has errors");
    }
  };

  return (
    <section className="w-full">
      <div className="w-full bg-white shadow-4xl">
        <div className="">
          <form onSubmit={handleSubmit}>
            <div className="grid sm:grid-cols-2 gap-4">
              <div>
                <label className="font-semibold text-sm font-inter">
                  Instagram handle URL/ Youtube URL
                </label>
                <input
                  type="text"
                  name="instagramUrl"
                  value={formData.instagramUrl}
                  onChange={handleChange}
                  className={`w-full px-3 py-3 mt-1 border rounded-xl  text-black text-sm placeholder:text-gray-500 focus:outline-none ${
                    errors.instagramUrl ? "border-red-500" : ""
                  }`}
                  placeholder="Enter Link"
                />
                {errors.instagramUrl && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.instagramUrl}
                  </p>
                )}
              </div>
              <div>
                <label className="font-semibold">Search Creator</label>
                <select
                  name="searchCreator"
                  value={formData.searchCreator}
                  onChange={handleChange}
                  className={`w-full px-3 py-3 mt-1  border rounded-xl focus:outline-none  text-black text-sm placeholder:text-gray-500 ${
                    errors.searchCreator ? "border-red-500" : ""
                  }`}
                >
                  <option className="text-grey">Search</option>
                  <option value="option1">option 1</option>
                  <option value="option2">option 2</option>
                </select>
                {errors.searchCreator && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.searchCreator}
                  </p>
                )}
              </div>
              <div className="relative col-span-2">
                <label className="font-semibold text-sm font-inter">
                  Date of Arrival
                </label>

                <input
                  type="text"
                  name="toDate"
                  id="toDate"
                  ref={inputRef}
                  value= {selectedDate ? format(selectedDate, "MM/dd/yyyy") : ""}
                  placeholder="mm/dd/yyyy"
                  readOnly
                  onClick={() => setShowCalendar(!showCalendar)}
                  className="block w-full p-2 px-4 py-3 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-800 placeholder:text-gray-400 cursor-pointer"
                />

                {showCalendar && (
                  <div
                    ref={calendarRef}
                    className="absolute top-full left-0 bg-white border border-black/50 rounded-lg shadow-lg px-4 py-2 z-50 w-full mt-0.5"
                  >
                    {/* Month navigation */}
                    <div className="flex items-center justify-between mb-4">
                      <button
                        onClick={handlePrevMonth}
                        className="text-lg font-bold px-2"
                      >
                        &#8592;
                      </button>

                      <span className="text-base font-semibold">
                        {format(currentMonth, "MMMM yyyy")}
                      </span>

                      <button
                        onClick={handleNextMonth}
                        className="text-lg font-bold px-2"
                      >
                        &#8594;
                      </button>
                    </div>

                    {/* Weekdays */}
                    <div className="grid grid-cols-7 gap-2 text-center text-sm font-semibold text-gray-600">
                      <div>Su</div>
                      <div>Mo</div>
                      <div>Tu</div>
                      <div>We</div>
                      <div>Th</div>
                      <div>Fr</div>
                      <div>Sa</div>
                    </div>

                    {/* Days */}
                    <div className="grid grid-cols-7 gap-1 mt-2 text-center text-sm">
                      {generateDays(currentMonth).map((day, index) =>
                        day ? (
                          <button
                            key={index}
                            className={`rounded-full p-2 flex items-center justify-center ${
                              isToday(day) ? "border-2 border-primary-blue" : ""
                            } ${
                              isSameDay(day, selectedDate)
                                ? "bg-primary-blue text-white"
                                : "hover:bg-primary-blue hover:text-white"
                            }`}
                            onClick={() => {
                              handleInputChange({
                                target: {
                                  name: "date",
                                  value: format(day, "MM-dd-yyyy"),
                                },
                              });
                              setShowCalendar(false);
                            }}
                          >
                            {day.getDate()}
                          </button>
                        ) : (
                          <div key={index} />
                        )
                      )}
                    </div>
                  </div>
                )}

                {errors.arrivalDate && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.arrivalDate}
                  </p>
                )}
              </div>
              <div>
                <label className="font-semibold text-sm font-inter">
                  Email
                </label>
                <input
                  type="text"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className={`w-full px-3 py-3 mt-1 border rounded-xl focus:outline-none text-black text-sm placeholder:text-gray-500 ${
                    errors.email ? "border-red-500" : ""
                  }`}
                  placeholder="Enter Email"
                />
                {errors.email && (
                  <p className="text-red-500 text-xs mt-1">{errors.email}</p>
                )}
              </div>
              <div>
                <label className="font-semibold text-sm font-inter">
                  Service Offer
                </label>
                <select
                  name="serviceOffer"
                  value={formData.serviceOffer}
                  onChange={handleChange}
                  className={`w-full px-3 py-3 mt-1 border rounded-xl focus:outline-none text-black text-sm placeholder:text-gray-500 ${
                    errors.serviceOffer ? "border-red-500" : ""
                  }`}
                >
                  <option className="text-grey">Select Offer</option>
                  <option value="option1">option 1</option>
                  <option value="option2">option 2</option>
                </select>
                {errors.serviceOffer && (
                  <p className="text-red-500 text-xs mt-1">
                    {errors.serviceOffer}
                  </p>
                )}
              </div>
              <div className="relative col-span-2">
                <input
                  type="file"
                  name="images"
                  multiple
                  onChange={handleFileChange}
                  className="z-10 cursor-pointer block w-full p-2 px-4 text-sm bg-transparent border rounded-lg opacity-0 border-gray-220 focus:outline-none text-slate-320 placeholder:text-gray-320 absolute top-0 left-0 right-0 bottom-0"
                />
                <div className="w-full px-3 py-2.5 border rounded-lg focus:outline-none  text-gray-500 text-sm placeholder:text-gray-500 flex items-center justify-between border-dashed border-blue-230 bg-blue-230/10 cursor-pointer">
                  Upload Attachment
                  <CloudUpload className="text-blue-230" size={22} />
                </div>
                {errors.images && (
                  <p className="text-red-500 text-xs mt-1">{errors.images}</p>
                )}
              </div>
            </div>
            <div className="flex justify-end my-5 gap-4 py-4 bg-white/60 sticky bottom-0 backdrop-blur-sm">
              <button className="hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm">
                Cancel
              </button>
              <button
                onClick={handleSubmit}
                className="bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm"
              >
                Add Creator
              </button>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
};

export default Addcreator;
