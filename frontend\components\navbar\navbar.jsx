/* eslint-disable react/no-unknown-property */

"use client";
import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { MdOutlineNotificationsActive } from "react-icons/md";
import { ChevronDown, Globe, Grip, User, X } from "lucide-react";
import dynamic from "next/dynamic";
import { HiMenuAlt3 } from "react-icons/hi";
import { Box, Divider, Drawer, Paper } from "@mui/material";
import { getItemLocalStorage } from "@/utils/browserSetting";
import { useNavbar } from "../home/<USER>";
// import { Toaster, toast } from 'sonner'
import { getPropertyCountApi } from "@/services/webflowServices";
import MenuPopup from "../popup/menuPopup";
import MenuPopupMobile from "../popup/menuPopupMobile";
import toast from "../toast/toast";
import { useRouter } from "next/router";
import { FaRegUser } from "react-icons/fa";
import { motion, AnimatePresence } from "framer-motion";
// import { PiHandCoinsFill } from "react-icons/pi";

// import axios from "axios";

const CountryModal = dynamic(() => import("../model/countryModel"), {
  ssr: false,
});
const LoginPopup = dynamic(() => import("../popup/loginPopup"), { ssr: false });
// const MenuPopup = dynamic(() => import("../popup/menuPopup"), { ssr: false });
const Noticeboard = dynamic(() => import("../model/noticeboard"), {
  ssr: false,
});
const MyProfile = dynamic(() => import("../model/myProfile"), { ssr: false });

const Navbar = () => {
  const [openCountryModal, setOpenCountryModal] = useState(false);
  const handleOpenCountryModal = () => setOpenCountryModal(true);
  const handleCloseCountryModal = () => setOpenCountryModal(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [showLoginPopup, setShowLoginPopup] = useState(false);
  const [hoveringTop, setHoveringTop] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isMenuOpenMobile, setIsMenuOpenMobile] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [hasToken, setHasToken] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [roles, setRole] = useState(false);
  const [isMyProfileOpen, setIsMyProfileOpen] = useState(false);
  const [flagUrl, setFlagUrl] = useState("");
  const [currencyCode, setCurrencyCode] = useState("");
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  // const [timeLeft, setTimeLeft] = useState(2 * 24 * 60 * 60); // 2 days in seconds
  const popupRef = useRef(null);
  // const containerRef = useRef(null);

  const toggleMyProfile = () => setIsMyProfileOpen(!isMyProfileOpen);
  const toggleMenu = () => {
    if (!token || role !== "user") {
      toast.error("Please Login !", {
        subText: "You need to be Logged In",
      });
    } else {
      setIsMenuOpen(!isMenuOpen);
    }
  };
  const toggleMenuMobile = () => {
    if (!token || role !== "user") {
      toast.error("Please Login !", {
        subText: "You need to be Logged In",
      });
    } else {
      setIsMenuOpenMobile(!isMenuOpenMobile);
    }
  };
  // const toggleMenuMobile = () => setIsMenuOpenMobile(!isMenuOpenMobile);
  const toggleLoginPopup = () => setShowLoginPopup(!showLoginPopup);
  // const [user, setUser] = useState(null);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleRouteChange = () => {
      if (isMenuOpen) toggleMenu(false);
      if (isMenuOpenMobile) toggleMenuMobile(false);
    };

    router.events.on("routeChangeStart", handleRouteChange);

    // Cleanup
    return () => {
      router.events.off("routeChangeStart", handleRouteChange);
    };
  }, [isMenuOpen, isMenuOpenMobile]);

  const updateTokenState = () => {
    const token = getItemLocalStorage("token");
    setHasToken(!!token);
  };

  const updateRoleState = () => {
    const role = getItemLocalStorage("role");
    setRole(role);
  };

  const updateCountry = () => {
    const flag = getItemLocalStorage("selectedCountryFlag");
    const code = getItemLocalStorage("selectedCurrencyCode");

    setFlagUrl(flag);
    setCurrencyCode(code);
    updateCountry2(flag, code);
  };

  useEffect(() => {
    updateTokenState();
  }, []);

  useEffect(() => {
    updateRoleState();
  }, []);

  useEffect(() => {
    const handleStorageChange = (event) => {
      if (event.key === "token") {
        updateTokenState();
      }
    };

    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);

  useEffect(() => {
    const handleStorageChange = (event) => {
      if (event.key === "role") {
        updateRoleState();
      }
    };

    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);

  useEffect(() => {
    const originalSetItem = localStorage.setItem;
    localStorage.setItem = function (key) {
      const event = new Event("itemInserted");
      originalSetItem.apply(this, arguments);
      if (key === "token") {
        window.dispatchEvent(event);
      }
    };

    const handleItemInserted = () => {
      updateTokenState();
    };

    window.addEventListener("itemInserted", handleItemInserted);

    return () => {
      window.removeEventListener("itemInserted", handleItemInserted);
      localStorage.setItem = originalSetItem;
    };
  }, []);

  useEffect(() => {
    const originalSetItem = localStorage.setItem;
    localStorage.setItem = function (key) {
      const event = new Event("itemInserted");
      originalSetItem.apply(this, arguments);
      if (key === "token") {
        window.dispatchEvent(event);
      }
    };

    const handleItemInserted = () => {
      updateRoleState();
    };

    window.addEventListener("itemInserted", handleItemInserted);

    return () => {
      window.removeEventListener("itemInserted", handleItemInserted);
      localStorage.setItem = originalSetItem;
    };
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 0);
    };

    const handleMouseMove = (event) => {
      setHoveringTop(event.clientY < 85);
    };

    window.addEventListener("scroll", handleScroll);
    window.addEventListener("mousemove", handleMouseMove);

    return () => {
      window.removeEventListener("scroll", handleScroll);
      window.removeEventListener("mousemove", handleMouseMove);
    };
  }, []);

  useEffect(() => {
    const selectedCountryFlag = getItemLocalStorage("selectedCountryFlag");
    const selectedCurrencyCode = getItemLocalStorage("selectedCurrencyCode");

    if (selectedCountryFlag) {
      const url = selectedCountryFlag;
      setFlagUrl(url);
      setCurrencyCode(selectedCurrencyCode);
    }
  }, []);
  // Notice Board Modal
  const [openNoticeBoard, setOpenNoticeBoard] = useState(false);
  const [openNoticeBoardDetails, setOpenNoticeBoardDetails] = useState(false);

  const handleOpenNoticeBoard = async () => {
    if (getItemLocalStorage("token") && role === "user") {
      const propertyCountResponse = await getPropertyCountApi();
      if (propertyCountResponse?.data?.data?.totalBooking > 0) {
        setOpenNoticeBoardDetails(true);
      } else if (propertyCountResponse?.data?.data?.totalBooking === 0) {
        setOpenNoticeBoard(true);
      }
    } else {
      toast.error("Please Login !", {
        subText: "You need to be Logged In",
      });
    }
  };
  const handleCloseNoticeBoard = () => {
    setOpenNoticeBoard(false);
    setOpenNoticeBoardDetails(false);
  };

  // Mobile Drawer
  const [openDrawer, setOpenDrawer] = useState(false);

  const toggleDrawer = (newOpen) => () => {
    setOpenDrawer(newOpen);
  };

  const { updateCountry2, token, role } = useNavbar();

  // Update country when the component is mounted
  useEffect(() => {
    const selectedCountryFlag = getItemLocalStorage("selectedCountryFlag");
    const selectedCurrencyCode = getItemLocalStorage("selectedCurrencyCode");

    if (selectedCountryFlag && selectedCurrencyCode) {
      updateCountry2(selectedCountryFlag, selectedCurrencyCode);
    }
    setFlagUrl(selectedCountryFlag);
    setCurrencyCode(selectedCurrencyCode);
  }, [updateCountry2]);

  useEffect(() => {
    const handleScroll = () => {
      if (window.innerWidth < 768) return;
      if (window.scrollY > 900) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Auto show popup on load, close after 4 seconds
  useEffect(() => {
    setIsPopupOpen(true);
    const closeTimer = setTimeout(() => {
      setIsPopupOpen(false);
    }, 4000);
    return () => clearTimeout(closeTimer);
  }, []);

  // Countdown timer
  // useEffect(() => {
  //   if (!isPopupOpen) return;

  //   const interval = setInterval(() => {
  //     setTimeLeft((prev) => (prev > 0 ? prev - 1 : 0));
  //   }, 1000);
  //   return () => clearInterval(interval);
  // }, [isPopupOpen]);

  // Close on outside click
  useEffect(() => {
    function handleClickOutside(event) {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        setIsPopupOpen(false);
      }
    }

    if (isPopupOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [isPopupOpen]);

  // Format time left as hh:mm:ss
  // const formatTime = (seconds) => {
  //   const days = Math.floor(seconds / (24 * 3600));
  //   const hrs = Math.floor((seconds % (24 * 3600)) / 3600);
  //   const mins = Math.floor((seconds % 3600) / 60);
  //   const secs = seconds % 60;

  //   return `${days}d:${hrs.toString().padStart(2, "0")}:${mins
  //     .toString()
  //     .padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  // };

  //   useEffect(() => {
  //   const fetchUserData = async () => {
  //     try {
  //       const res = await axios.get("/api/user/me", {
  //         headers: {
  //           Authorization: `Bearer ${token}`,
  //         },
  //       });
  //       setUser(res.data); // Make sure the API returns { name: "User Name", ... }
  //     } catch (error) {
  //       console.error("Error fetching user data:", error);
  //     }
  //   };

  //   if (token) fetchUserData();
  // }, [token]);

  const router = useRouter();
  const isHomePage = router.pathname === "/";
  const isTopHostelPage =
    router.pathname === "/tophostel" ||
    router.pathname === "/exploreworld" ||
    router.pathname === "/featuredhostel" ||
    router.pathname === "/travelactivity" ||
    router.pathname === "/meetbuddies" ||
    router.pathname === "/discover-event" ||
    router.pathname === "/search";

  console.log("token", token);

  return (
    <>
      {/* <Toaster richColors position="top-right" /> */}
      <section
        className={`w-full duration-300 ease-in-out sticky top-0 z-40 overflow-visible ${
          scrolled && !hoveringTop ? "-top-10" : "sticky top-0"
        }`}
      >
        <div className='flex items-center justify-center px-5 py-2.5 bg-black w-full min-h-[44px]'>
          <p className='text-white xs:text-sm text-xs leading-tight text-center'>
            Get 1 Month Free
            <Link href={"/my-profile?section=membership"}>
              <span className='text-primary-blue'> Mix Premium </span>
            </Link>
            Membership – Sign Up Now!
          </p>
          {!token || role !== "user" ? (
            <button
              className='absolute inset-0 w-full h-full'
              aria-label='Login required to access Mix Premium Membership'
              onClick={() => {
                toast.error("Please Login !", {
                  subText: "You need to be Logged In",
                });
              }}
            />
          ) : null}
        </div>
        <div
          className={`w-full hidden lg:flex ${
            isTopHostelPage
              ? "absolute z-50 bg-black bg-opacity-10 shadow-md backdrop-blur-sm"
              : isHomePage
              ? isScrolled
                ? "bg-white shadow-md"
                : "bg-white bg-opacity-10 fixed top-10 z-50 backdrop-blur-sm"
              : "bg-transparent lg:bg-white shadow-md"
          }`}
        >
          <div className='relative z-50 flex items-start justify-between py-4 container'>
            <div className='w-[45%] flex items-center gap-x-3'>
              <li className='md:hidden block'>
                <button
                  // href="#"
                  rel='canonical'
                  className={`text-2xl font-manrope justify-center items-center font-bold cursor-pointer  duration-300 ease-in-out  ${
                    isTopHostelPage
                      ? "text-white"
                      : isHomePage
                      ? isScrolled
                        ? "text-black"
                        : "text-white"
                      : "text-black"
                  }`}
                  onClick={toggleDrawer(true)}
                  prefetch={false}
                >
                  <HiMenuAlt3 />
                </button>
              </li>
              <Link href='/' rel='canonical' prefetch={false}>
                <Image
                  src={
                    isTopHostelPage
                      ? `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Mixdorm-white-D.svg`
                      : isHomePage
                      ? isScrolled
                        ? `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/logo.svg`
                        : `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Mixdorm-white-D.svg`
                      : `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/logo.svg`
                  }
                  width={155}
                  height={40}
                  alt='Mixdorm'
                  title='Mixdorm'
                  className='max-w-[110px] md:max-w-[155px] md:max-h-24 relative z-50 w-fit object-contain bg-blend-color-burn cursor-pointer hover:scale-95 duration-500 ease-in-out'
                  loading='lazy'
                />
              </Link>
            </div>
            <div className='w-[55%] flex justify-end items-center'>
              <ul className='flex items-center justify-center md:gap-x-5 gap-x-3'>
                <li className='md:block hidden'>
                  <Link
                    href='/owner/list-your-hostel'
                    passHref
                    className='text-xs text-center font-manrope min-w-[140px] w-full block font-bold bg-primary-blue cursor-pointer rounded-9xl text-black duration-300 ease-in-out px-5 py-3'
                    prefetch={false}
                  >
                    List Your Hostel
                  </Link>
                </li>
                {/* <li className='hidden md:block relative'>
                  <button
                    // href="0"
                    rel='canonical'
                    className={`text-sm font-manrope items-center font-bold cursor-pointer rounded-9xl text-black duration-300 ease-in-out gap-x-2 flex ${
                      isTopHostelPage
                        ? "text-white"
                        : isHomePage
                        ? isScrolled
                          ? "text-black"
                          : "text-white"
                        : "text-black"
                    }`}
                    onClick={handleOpenNoticeBoard}
                    prefetch={false}
                  >
                    <MdOutlineNotificationsActive
                      size={20}
                      className={`font-normal text-black ${
                        isTopHostelPage
                          ? "text-white"
                          : isHomePage
                          ? isScrolled
                            ? "text-black"
                            : "text-white"
                          : "text-black"
                      }`}
                    />{" "}
                    Noticeboard
                  </button>
                  <span className='absolute left-4 top-[-10px] min-h-3.5 min-w-3.5 max-h-4.5 max-w-4.5 animate-ping rounded-full bg-primary-blue'></span>
                  <span className='absolute flex items-center justify-center left-4 top-[-10px] min-h-3.5 min-w-3.5 max-h-4.5 max-w-4.5 rounded-full bg-primary-blue text-[10px] font-medium text-black text-center leading-none'>
                    2
                  </span>
                </li> */}
                {/* <li className='hidden md:block relative' ref={containerRef}>
                  <button
                    className={`text-sm font-manrope items-center font-bold cursor-pointer rounded-9xl text-black duration-300 ease-in-out gap-x-2 flex ${
                      isTopHostelPage
                        ? "text-white"
                        : isHomePage
                        ? isScrolled
                          ? "text-black"
                          : "text-white"
                        : "text-black"
                    }`}
                    onClick={() => setIsPopupOpen(true)}
                  >
                    <PiHandCoinsFill
                      size={20}
                      className={`${
                        isTopHostelPage
                          ? "text-white"
                          : isHomePage
                          ? isScrolled
                            ? "text-black"
                            : "text-white"
                          : "text-black"
                      }`}
                    />
                    Settle
                  </button>
                  <span className='absolute left-4 top-[-10px] min-h-3.5 min-w-3.5 max-h-4.5 max-w-4.5 animate-ping rounded-full bg-primary-blue'></span>
                  <span className='absolute flex items-center justify-center left-4 top-[-10px] min-h-3.5 min-w-3.5 max-h-4.5 max-w-4.5 rounded-full bg-primary-blue text-[10px] font-medium text-black text-center leading-none'>
                    2
                  </span>
                  {isPopupOpen && (
                    <div
                      ref={popupRef}
                      className='absolute z-50 mt-2 bg-white p-5 rounded-xl shadow-xl border border-gray-100 w-80'
                    >
                      <button
                        onClick={() => setIsPopupOpen(false)}
                        className='absolute top-3 right-3 text-gray-400 hover:text-gray-600 text-xl transition-colors'
                      >
                        &times;
                      </button>

                      <p className='text-gray-500 text-xs font-medium uppercase tracking-wide mb-1'>
                        Settle your booking payment now.
                      </p>

                      <p className='my-3 font-manrope font-bold text-gray-900 hover:text-primary-blue cursor-pointer text-xl transition-colors'>
                        <Link href={"/my-profile?section=stay"}>
                          Tamasha Udaipur
                        </Link>
                      </p>

                      <div className='flex items-center justify-between py-2 border-b border-gray-100'>
                        <span className='text-sm text-gray-700'>
                          Delux room
                        </span>
                        <span className='text-sm font-medium text-gray-900'>
                          ₹1299.00
                        </span>
                      </div>

                      <div className='mt-4 p-3 bg-red-50 rounded-lg'>
                        <p className='text-sm text-red-600 font-semibold text-center'>
                          Time left: {formatTime(timeLeft)}
                        </p>
                      </div>
                    </div>
                  )}
                </li> */}
                <li>
                  {!token ? (
                    <button
                      // href="#"
                      className={`text-sm font-manrope flex items-center font-bold cursor-pointer  duration-300 ease-in-out gap-x-2 ${
                        isTopHostelPage
                          ? "text-white"
                          : isHomePage
                          ? isScrolled
                            ? "text-black"
                            : "text-white"
                          : "text-black"
                      }`}
                      onClick={toggleLoginPopup} // Open Login Popup if no token
                      prefetch={false}
                    >
                      <User size={20} />

                      <span className='hidden md:block'>Traveller</span>
                    </button>
                  ) : token && role === "user" ? (
                    <button
                      // href="#"
                      rel='canonical'
                      className={`text-sm font-manrope flex items-center font-bold cursor-pointer  duration-300 ease-in-out gap-x-2 ${
                        isTopHostelPage
                          ? "text-white"
                          : isHomePage
                          ? isScrolled
                            ? "text-black"
                            : "text-white"
                          : "text-black"
                      }`}
                      // onClick={toggleMyProfile} // Open MyProfile if token exists
                      prefetch={false}
                    >
                      <User size={20} />
                      {/* {user?.name || "Profile"} */}
                      {getItemLocalStorage("name")}
                    </button>
                  ) : (
                    <Link
                      href='#'
                      className={`text-sm font-manrope flex items-center font-bold cursor-pointer text-black duration-300 ease-in-out gap-x-2 ${
                        isTopHostelPage
                          ? "text-white"
                          : isHomePage
                          ? isScrolled
                            ? "text-black"
                            : "text-white"
                          : "text-black"
                      }`}
                      onClick={toggleLoginPopup} // Open Login Popup if no token
                      prefetch={false}
                    >
                      <User size={20} />
                      Traveller
                    </Link>
                  )}
                </li>
                <li className='hidden md:block'>
                  <button
                    type='button'
                    className={`text-sm font-manrope items-center font-bold cursor-pointer text-black duration-300 ease-in-out gap-x-2 flex ${
                      isTopHostelPage
                        ? "text-white"
                        : isHomePage
                        ? isScrolled
                          ? "text-black"
                          : "text-white"
                        : "text-black"
                    }`}
                    onClick={handleOpenCountryModal}
                  >
                    {flagUrl ? (
                      <Image
                        src={flagUrl}
                        alt='Country Flag'
                        width={20}
                        height={20}
                        loading='lazy'
                      />
                    ) : (
                      <Globe size={20} />
                    )}
                    {currencyCode ? currencyCode : "Country"}
                    <ChevronDown size={18} />
                  </button>
                </li>
                <li className='flex'>
                  {/* For mobile screens (0-575px) */}
                  <button
                    aria-label='Mobile Menu'
                    className={`sm:hidden text-sm font-manrope font-bold cursor-pointer text-black duration-300 ease-in-out ${
                      isTopHostelPage
                        ? "text-white"
                        : isHomePage
                        ? isScrolled
                          ? "text-black"
                          : "text-white"
                        : "text-black"
                    }`}
                    onClick={toggleMenuMobile}
                    prefetch={false}
                  >
                    <Grip size={24} />
                  </button>
                  {/* For larger screens (576px and above) */}
                  <button
                    aria-label='Mobile Menu'
                    className={`hidden sm:block text-sm font-manrope font-bold cursor-pointer text-black duration-300 ease-in-out ${
                      isTopHostelPage
                        ? "text-white"
                        : isHomePage
                        ? isScrolled
                          ? "text-black"
                          : "text-white"
                        : "text-black"
                    }`}
                    onClick={toggleMenu}
                    prefetch={false}
                  >
                    <Grip size={24} />
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </div>
        {!isHomePage && (
          <div
            className={`w-full flex lg:hidden ${
              isTopHostelPage
                ? "absolute z-50 bg-black bg-opacity-10 shadow-md backdrop-blur-sm"
                : "bg-white border-y border-white"
            }`}
          >
            <div className='relative z-50 flex items-start justify-between py-4 container'>
              <div className='w-[30%] flex items-center gap-x-3'>
                <Link href='/' rel='canonical' prefetch={false}>
                  <Image
                    src={
                      isTopHostelPage
                        ? `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Mixdorm-white-D.svg`
                        : `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/logo.svg`
                    }
                    width={155}
                    height={40}
                    alt='Content Ai'
                    title='Content Ai'
                    className='max-w-[110px] md:max-w-[155px] md:max-h-24 relative z-50 w-fit object-contain bg-blend-color-burn cursor-pointer hover:scale-95 duration-500 ease-in-out'
                    loading='lazy'
                  />
                </Link>
              </div>
              <div className='w-[55%] flex justify-end items-center'>
                <ul className='flex items-center justify-center md:gap-x-5 gap-x-3'>
                  <li className='relative'>
                    <button
                      // href="0"
                      rel='canonical'
                      className={`text-sm font-manrope items-center font-bold cursor-pointer rounded-9xl  duration-300 ease-in-out gap-x-2 flex ${
                        isTopHostelPage ? "text-white" : "text-black"
                      }`}
                      onClick={handleOpenNoticeBoard}
                      aria-label='Notification'
                    >
                      <MdOutlineNotificationsActive
                        size={26}
                        className={`font-normal ${
                          isTopHostelPage ? "text-white" : "text-black"
                        }`}
                      />{" "}
                    </button>
                    <span className='absolute left-4 top-[-10px] min-h-3.5 min-w-3.5 max-h-4.5 max-w-4.5 animate-ping rounded-full bg-primary-blue'></span>
                    <span className='absolute flex items-center justify-center left-4 top-[-10px] min-h-3.5 min-w-3.5 max-h-4.5 max-w-4.5 rounded-full bg-primary-blue text-[10px] font-medium text-black text-center leading-none'>
                      2
                    </span>
                  </li>

                  <li>
                    {!token ? (
                      <button
                        // href="#"
                        className={`text-sm font-manrope flex items-center font-bold cursor-pointer  duration-300 ease-in-out gap-x-2 ${
                          isTopHostelPage ? "text-white" : "text-black"
                        }`}
                        onClick={toggleLoginPopup} // Open Login Popup if no token
                        aria-label='User'
                      >
                        <FaRegUser
                          size={20}
                          className={`font-bold ${
                            isTopHostelPage ? "text-white" : "text-black"
                          }`}
                        />

                        <span className='hidden md:block'>Traveller</span>
                      </button>
                    ) : token && role === "user" ? (
                      // <button
                      //   // href="#"
                      //   rel="canonical"
                      //   className={`text-sm font-manrope flex items-center  justify-center font-bold cursor-pointer  duration-300 ease-in-out gap-x-2 border-2 border-black-100 rounded-full h-7 w-7  ${isTopHostelPage ? "text-white" : "text-black"}`}
                      //   // onClick={toggleMyProfile} // Open MyProfile if token exists
                      // >
                      //   {/* <FaRegUser size={20} />  */}
                      //   A
                      // </button>
                      <div className='relative'>
                        <button
                          className={`text-sm font-manrope flex items-center justify-center font-bold cursor-pointer duration-300 ease-in-out gap-x-2  rounded-full h-7 w-7  ${
                            isTopHostelPage
                              ? "bg-primary-blue text-black"
                              : "bg-primary-blue text-black"
                          }`}
                          onMouseEnter={() => setShowTooltip(true)}
                          onMouseLeave={() => setShowTooltip(false)}
                        >
                          {getItemLocalStorage("name")
                            ?.charAt(0)
                            ?.toUpperCase() || "A"}
                        </button>
                        <AnimatePresence>
                          {showTooltip && (
                            <motion.div
                              initial={{ opacity: 0, y: 5 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: 5 }}
                              className='absolute bottom-full left-1/2 transform -translate-x-1/2 bg-white text-black text-xs rounded py-1 px-2 whitespace-nowrap'
                            >
                              {getItemLocalStorage("name")}
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                    ) : (
                      <Link
                        href='#'
                        className={`text-sm font-manrope flex items-center font-bold cursor-pointer text-black duration-300 ease-in-out gap-x-2 ${
                          isTopHostelPage ? "text-white" : "text-black"
                        } `}
                        onClick={toggleLoginPopup} // Open Login Popup if no token
                        prefetch={false}
                        aria-label='User'
                      >
                        <FaRegUser size={20} />
                        <span className='hidden md:block'>Traveller</span>
                      </Link>
                    )}
                  </li>
                  <li className=''>
                    <button
                      type='button'
                      className={`text-sm font-manrope items-center font-bold cursor-pointer  duration-300 ease-in-out gap-x-2 flex ${
                        isTopHostelPage ? "text-white" : "text-black"
                      }`}
                      onClick={handleOpenCountryModal}
                      aria-label='Flag'
                    >
                      {flagUrl ? (
                        <div className='relative w-6 h-6 rounded-full overflow-hidden'>
                          <Image
                            src={flagUrl}
                            alt='Country Flag'
                            fill
                            className='object-cover rounded-full'
                            sizes='24px'
                            loading='lazy'
                          />
                        </div>
                      ) : (
                        <Globe size={20} />
                      )}
                    </button>
                  </li>
                  <li className='flex'>
                    {/* For mobile screens (0-575px) */}
                    {/* <button
                      aria-label="Mobile Menu"
                      className={`sm:hidden text-sm font-manrope font-bold cursor-pointer text-black duration-300 ease-in-out ${isTopHostelPage ? "text-white" : "text-black"}`}
                      onClick={toggleMenuMobile}
                    >
                      <Grip size={24} />
                    </button> */}
                    {/* For larger screens (576px and above) */}
                    <button
                      aria-label='Mobile Menu'
                      className={`block text-sm font-manrope font-bold cursor-pointer   duration-300 ease-in-out ${
                        isTopHostelPage ? "text-white" : "text-black"
                      }`}
                      onClick={toggleMenuMobile}
                    >
                      <Grip size={24} />
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </section>

      {/* <Drawer open={openDrawer} onClose={toggleDrawer(false)} className="nav-bar-humburger fadeInLeft animated" anchor="left"> */}
      <Drawer
        open={openDrawer}
        onClose={toggleDrawer(false)}
        className='nav-bar-humburger fadeInLeft animated'
        anchor='left'
      >
        <Box
          sx={{ width: 346 }}
          role='presentation'
          borderRadius={10}
          onClick={toggleDrawer(false)}
        >
          <Paper
            sx={{
              width: 326,
              background: "#fff",
              borderRadius: "0 0 10px 10px",
              height: "100vh",
              elevation: 0,
            }}
            elevation={0}
            borderRadius={10}
          >
            <Link
              href='/'
              rel='canonical'
              className='p-4 block'
              prefetch={false}
            >
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/logo.svg`}
                width={155}
                height={40}
                alt='Mixdorm'
                title='Mixdorm'
                className='max-w-[110px] md:max-w-[155px] md:max-h-24 relative z-50 w-fit object-contain bg-blend-color-burn cursor-pointer hover:scale-95 duration-500 ease-in-out'
                loading='lazy'
              />
            </Link>
            <button
              className='absolute top-4 right-4'
              onClick={toggleDrawer(false)}
            >
              <X size={22} />
            </button>
            <Divider />
            <div className='p-3'>
              <label
                htmlFor=''
                className='text-sm sm:text-base flex items-center gap-4 mb-4'
              >
                <Globe size={20} /> Select Currency
              </label>
              <button
                type='button'
                className='flex items-center gap-x-2 text-sm font-manrope font-bold cursor-pointer text-black duration-300 ease-in-out'
                onClick={handleOpenCountryModal}
              >
                {flagUrl ? (
                  <Image
                    src={flagUrl}
                    alt='Country Flag'
                    width={20}
                    height={20}
                    loading='lazy'
                  />
                ) : (
                  <Globe size={20} />
                )}
                {currencyCode ? currencyCode : "Country"}
                <ChevronDown size={18} />
              </button>
            </div>

            {/* <div className='p-3'>
              <Link
                href='#'
                rel='canonical'
                className='text-sm font-manrope items-center font-bold cursor-pointer rounded-9xl text-black duration-300 ease-in-out gap-x-2 flex'
                onClick={handleOpenNoticeBoard}
                prefetch={false}
              >
                <MdOutlineNotificationsActive
                  size={20}
                  className='font-normal text-black'
                />{" "}
                Noticeboard
              </Link>
            </div> */}

            <div className='p-3'>
              <Link
                href='/owner/hostel-login'
                rel='canonical'
                className='block px-5 py-3 text-center font-manrope text-base font-bold bg-primary-blue cursor-pointer rounded-4xl text-black duration-300 ease-in-out'
                prefetch={false}
              >
                List Your Hostel
              </Link>
            </div>
          </Paper>
        </Box>
      </Drawer>

      <Noticeboard
        close={handleCloseNoticeBoard}
        open={openNoticeBoard}
        openNoticeBoardDetails={openNoticeBoardDetails}
      />
      {isMenuOpen && (
        <MenuPopup
          isOpen={isMenuOpen}
          toggleMenu={toggleMenu}
          updateTokenState={updateTokenState}
          toggleLoginPopup={toggleLoginPopup}
          updateRoleState={updateRoleState}
        />
      )}
      {isMenuOpenMobile && (
        <MenuPopupMobile
          isOpen={isMenuOpenMobile}
          toggleMenu={toggleMenuMobile}
          updateTokenState={updateTokenState}
          toggleLoginPopup={toggleLoginPopup}
          updateRoleState={updateRoleState}
        />
      )}

      <MyProfile
        isMenuOpen={isMyProfileOpen}
        toggleMenu={toggleMyProfile}
        updateTokenState={updateTokenState}
        updateRoleState={updateRoleState}
      />
      <LoginPopup isOpen={showLoginPopup} onClose={toggleLoginPopup} />

      <CountryModal
        openCountryModal={openCountryModal}
        handleCloseCountryModal={handleCloseCountryModal}
        updateCountry={updateCountry}
      />
    </>
  );
};

export default dynamic(() => Promise.resolve(Navbar), { ssr: false });
