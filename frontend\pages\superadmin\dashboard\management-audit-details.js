"use client";
import Link from "next/link";
import React from "react";


const auditdetails = () => {
 
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616] h-screen">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        Audit Logs Details
      </h2>
      <div className="bg-white border rounded-xl mt-5 h-[80vh] dark:bg-black dark:border-none dark:h-auto">
        <div className="p-8 flex flex-col gap-y-6">
          <div>
            <div className="flex flex-wrap gap-x-10 lg:gap-x-20 my-6">
              <div className="flex w-[30%] lg:w-[15%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#B6B6B6]">NAME</strong>
                <p className="mt-2 text-black/55 text-base font-medium font-poppins dark:text-[#757575]">Product Admin</p>
                <p className="mt-2 text-black/55 text-base font-medium font-poppins dark:text-[#757575]">Uer ID: 2</p>
              </div>
              <div className="flex w-[45%] lg:w-[17%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#B6B6B6]">Mobile Number</strong>
                <p className="mt-2 text-black/55 text-base font-medium font-poppins dark:text-[#757575]">Content Management</p>
              </div>
              <div className="flex w-[30%] flex-col mt-5 lg:mt-0">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#B6B6B6]">IP Address</strong>
                <p className="mt-2 text-black/55 text-base font-medium font-poppins dark:text-[#757575]">127.0.01</p>
              </div>
            </div>
            <div className="flex flex-wrap gap-x-10 lg:gap-x-20 mt-6">
              <div className="flex w-[30%] lg:w-[15%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#B6B6B6]">UserAgent</strong>
                <p className="mt-2 text-black/55 text-base font-medium font-poppins dark:text-[#757575]">chistinn25@</p>
              </div>
              <div className="flex w-[45%] lg:w-[17%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#B6B6B6]">Timestamp</strong>
                <p className="mt-2 text-black/55 text-base font-medium font-poppins dark:text-[#757575]">30-05-2024 17:05 PM</p>
              </div>
              <div className="flex w-[100%] lg:w-[38%] flex-col mt-5 lg:mt-0">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#B6B6B6]">Old Value</strong>
                <p className="mt-2 text-black/55 text-base font-medium font-poppins dark:text-[#757575]">
                  Amet minim mollit non deserunt ullamco est sit aliqua dolor do
                  amet sint. Velit officia consequat duis enim velit mollit.
                  Exercitation veniam consequat sunt nostrud amet.
                </p>
              </div>
            </div>

          </div>
        </div>
        <div className="flex items-start justify-start p-8">
          <Link
              href={"/superadmin/dashboard/management-audit"}
              className="text-white py-2 w-32 rounded bg-sky-blue-650 flex items-center justify-center"
            >
              Cancel
            </Link>
          </div>
      </div>
      
    </div>
  );
};

export default auditdetails;
