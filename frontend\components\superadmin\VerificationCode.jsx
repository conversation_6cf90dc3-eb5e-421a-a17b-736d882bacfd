import React from "react";

const VerificationCode = ({ otp, setOtp, onResend }) => {
  const handleChange = (e, index) => {
    const newOTP = [...otp];
    newOTP[index] = e.target.value.replace(/[^0-9]/g, "");
    setOtp(newOTP);
  };

  return (
    <div>
      <h2 className="mb-2 mt-4 font-semibold text-base">Enter Code</h2>
      <div className="flex gap-3">
        {otp.map((digit, index) => (
          <input
            key={index}
            type="text"
            maxLength="1"
            value={digit}
            onChange={(e) => handleChange(e, index)}
            className="w-8 h-8 md:w-7 md:h-7 lg:w-12 lg:h-12 text-center text-lg border border-gray-300 rounded-full focus:outline-none focus:ring-1 focus:ring-gray-400"
          />
        ))}
      </div>
      <div className="py-5">
        <button
          type="button"
          className="text-sky-blue-650 text-sm font-medium underline"
          onClick={onResend}
        >
          Resend verification code
        </button>
      </div>
    </div>
  );
};

export default VerificationCode;
