import emailTemplates from "../models/emailtemplates.js"
import blogsCategoriesModel from "../models/blogCategories.js"
const defaultEmailTemplates = [
    {
        name: 'otpLogin',
        subject: 'Email Verification',
        text: "Hello {{name}},<br>\n<p> Your OTP code for login in Mixdorm account is <strong>{{otp}}</strong></p>\nThank you,<br>\nTeam MixDorm",
        flag: 1
    },
    {
        name: 'forgotPassword',
        subject: 'Password Reset Request',
        text: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
            color: #333;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
        }
        .email-header {
            background-color: rgba(64, 224, 208, 1) !important;
            color: #ffffff;
            padding: 20px;
            text-align: center;
        }
        .email-header h1 {
            margin: 0;
            font-size: 24px;
        }
        .email-body {
            padding: 20px;
        }
        .email-body p {
            line-height: 1.6;
        }
        .cta-button {
            display: inline-block;
            background-color: #000000;
            color: #ffffff;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        .cta-button:hover {
            background-color: #333333;
        }
        .email-footer {
            background-color: rgba(64, 224, 208, 1) !important;
            color: #ffffff;
            text-align: center;
            padding: 10px;
        }
        .email-footer p {
            margin: 0;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <h1>Password Reset Request</h1>
        </div>
        <div class="email-body">
            <p>Hello {{name}},</p>
            <p>We received a request to reset your password for your MixDorm account.</p>
            <p>To reset your password, please click the button below:</p>
            <p style="text-align: center;">
                <a href="{{reset_link}}" class="cta-button">Reset Password</a>
            </p>
            <p>If you did not request a password reset, please ignore this email.</p>
        </div>
        <div class="email-footer">
            <p>Thank you,<br>Team MixDorm</div>
    </div>
</body>
</html>`,
        flag: 2
    },
    {
        name: 'userOtpSignUp',
        subject: 'Welcome to MixDorm! Verify Your Email Address',
        text: "Hello {{name}},<br>\n<p>Welcome to MixDorm! To complete your registration, please verify your email address using the OTP code below:</p>\n<p><strong>{{otp}}</strong></p>\n<p>Thank you,<br>\nTeam MixDorm</p>",
        flag: 3
    },
    {
        name: 'userOtpVerificationSuccess',
        subject: 'User Email Verification is Successful',
        text: "Hello {{name}},<br>\n<p>Your email has been successfully verified. You can now enjoy all the features of your MixDorm account.</p>\n<p>Thank you,<br>\nTeam MixDorm</p>",
        flag: 4
    },
    {
        name: 'userQuerySubmittedSuccessfully',
        subject: 'Your Query has been Submitted Successfully',
        text: "Hello {{name}},<br>\n<p>Dear user, your query has been submitted successfully. We will reach out to you shortly with further updates. Thank you for reaching out to us.</p>\n<p>Best regards,<br>\nTeam MixDorm</p>",
        flag: 5
    },
    {
        name: 'newEnquiryNotification',
        subject: 'New User Query Received',
        text: `<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mixdorm Notification</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Montserrat', sans-serif;
            background-color: #f6f6f6;
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            padding: 20px;
            background-color: #E0F7FA;
            color: #ffffff;
        }
        .header svg {
            width: 80px;
            height: 80px;
        }
        .content {
            padding: 20px;
            color: #333333;
        }
        h3 {
            font-size: 18px;
            margin: 0 0 10px;
        }
        p, ul {
            font-size: 14px;
            line-height: 1.6;
            margin: 0 0 15px;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin-bottom: 5px;
        }
        .footer {
            text-align: center;
            padding: 10px;
            background-color: #f6f6f6;
            font-size: 12px;
            color: #777777;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
              <img src="https://mixdorm-s3.s3.ap-south-1.amazonaws.com/logo/mixdromLogo.jpg" alt="Mixdorm Logo" style="width: 150px; height: auto;" />
        </div>
        <div class="content">
            <h3>Hi, <span style="font-weight: 600; color: #3EA28D;">Aayush</span></h3>
            <p>We have received a new query from a user. Below are the details:</p>
            <ul>
                <li><strong>User Name:</strong> {{userName}}</li>
                <li><strong>User Email:</strong> {{userEmail}}</li>
                <li><strong>Subject:</strong> {{subject}}</li>
                <li><strong>Categories:</strong> {{categories}}</li>
                <li><strong>Description:</strong> {{description}}</li>
            </ul>
        </div>
    </div>
</body>
</html>`,
        flag: 6
    },
    {
        name: 'propertyListed',
        subject: 'Your Property is Now Listed on MixDorm!',
        text: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Property Listed on MixDorm</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
            color: #333;
        }
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
        }
        .email-header {
            background-color: #2a9d8f;
            color: #ffffff;
            padding: 20px;
            text-align: center;
        }
        .email-header h1 {
            margin: 0;
            font-size: 24px;
        }
        .email-body {
            padding: 20px;
        }
        .email-body p {
            line-height: 1.6;
        }
        .property-name {
            font-weight: bold;
        }
        .cta-button {
            display: inline-block;
            background-color: #e76f51;
            color: #ffffff;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        .cta-button:hover {
            background-color: #d65a43;
        }
        .email-footer {
            background-color: #2a9d8f;
            color: #ffffff;
            text-align: center;
            padding: 10px;
        }
        .email-footer p {
            margin: 0;
            font-size: 12px;
        }
        .social-icons img {
            width: 24px;
            margin: 0 5px;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <h1>Your Property is Listed!</h1>
        </div>
        <div class="email-body">
            <p>Hi,</p>
            <p>We’re excited to let you know that your property, <span class="property-name">{{propertyName}}</span>, is now live on MixDorm and ready for bookings!</p>
            <p>Travelers from all over the world can now explore, book, and experience your property. Here’s what you can do next:</p>
            <ul>
                <li>Get Notified of Bookings: You’ll receive real-time notifications for every booking and inquiry.</li>
                <li>Maximize Exposure: Take advantage of MixDorm's global audience to attract more guests.</li>
            </ul>
            <a href="[Property Dashboard Link]" class="cta-button">Manage Your Property</a>
        </div>
        <div class="email-footer">
            <p>Thank you for choosing MixDorm! If you have any questions, feel free to reach out at <a href="mailto:[Support Email]" style="color: #ffffff;"><EMAIL></a>.</p>
            <!--<div class="social-icons">-->
            <!--    <a href="[Facebook Link]"><img src="facebook-icon.png" alt="Facebook"></a>-->
            <!--    <a href="[Twitter Link]"><img src="twitter-icon.png" alt="Twitter"></a>-->
            <!--    <a href="[Instagram Link]"><img src="instagram-icon.png" alt="Instagram"></a>-->
            <!--</div>-->
        </div>
    </div>
</body>
</html>`,
        flag: 7
    },
    {
        "name": "newsletter-subscribe",
        "flag": 8,
        "subject": "Thank You for Subscribing to MixDorm! ",
        "text": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Welcome to MixDorm</title>\n  <style>\n    /* Reset styles for consistent rendering */\n    body, table, td, a {\n      text-decoration: none;\n      font-family: 'Open Sans', sans-serif;\n      color: #2C3E50;\n      margin: 0;\n      padding: 0;\n    }\n    img {\n      border: 0;\n      display: block;\n      line-height: 100%;\n      outline: none;\n      text-decoration: none;\n    }\n    table {\n      border-collapse: collapse;\n    }\n    .email-container {\n      width: 100%;\n      max-width: 600px;\n      margin: 0 auto;\n      background-color: #ffffff;\n      border: 1px solid #eaeaea;\n    }\n    .header {\n      background-color: #2C3E50;\n      color: white;\n      padding: 20px;\n      text-align: center;\n      font-size: 24px;\n      font-family: 'Montserrat', sans-serif;\n    }\n    .hero-image {\n      width: 100%;\n      height: auto;\n    }\n    .content {\n      padding: 20px;\n      font-size: 16px;\n      line-height: 1.6;\n    }\n    .content h2 {\n      color: #2C3E50;\n      font-family: 'Montserrat', sans-serif;\n    }\n    .content p {\n      margin: 0 0 10px;\n    }\n    .bullet-points {\n      padding-left: 20px;\n    }\n    .button {\n      display: inline-block;\n      padding: 10px 20px;\n      color: #ffffff;\n      background-color: #3498DB;\n      border-radius: 4px;\n      text-align: center;\n      font-weight: bold;\n      text-decoration: none;\n    }\n    .yellow-button {\n      background-color: #F1C40F;\n      color: #2C3E50;\n    }\n    .social-icons img {\n      width: 24px;\n      margin: 0 5px;\n    }\n    .footer {\n      font-size: 12px;\n      color: #7f8c8d;\n      text-align: center;\n      padding: 10px 20px;\n      background-color: #f9f9f9;\n    }\n    .footer a {\n      color: #3498DB;\n    }\n  </style>\n</head>\n<body>\n\n  <table class=\"email-container\">\n    <!-- Header -->\n    <tr>\n      <td class=\"header\">\n        <strong>Welcome to MixDorm!</strong>\n      </td>\n    </tr>\n\n    <!-- Hero Image -->\n    <tr>\n      <td>\n        <img src=\"https://mixdorm-live.s3.ap-south-1.amazonaws.com/front-images/logo.svg\" alt=\"Welcome to MixDorm\" class=\"hero-image\">\n      </td>\n    </tr>\n\n    <!-- Content -->\n    <tr>\n      <td class=\"content\">\n        <h2>Introduction to MixDorm</h2>\n        <p>At MixDorm, we believe travel should be affordable, flexible, and social. We connect you with amazing hostels in top destinations worldwide, offer exclusive travel deals, and give you tips to make the most of every journey.</p>\n\n        <h2>Exclusive Subscriber Benefits</h2>\n        <ul class=\"bullet-points\">\n          <li>Early access to exclusive hostel deals and discounts</li>\n          <li>Inspiring travel itineraries and activity recommendations</li>\n          <li>Insider tips on hidden gems and must-visit spots</li>\n          <li>Updates on new MixDorm features and app improvements</li>\n        </ul>\n\n        <h2>What’s Next?</h2>\n        <p>In the meantime, feel free to start exploring our app or website and begin planning your next adventure! Here are some quick ways to get started:</p>\n        <ul class=\"bullet-points\">\n          <li>Check out trending destinations - discover the hottest hostels around the world.</li>\n          <li>Browse by activity - find hostels perfect for beach days, mountain hikes, or city adventures.</li>\n          <li>Create your profile - personalize your experience and keep track of your favorite hostels.</li>\n        </ul>\n\n        <!-- Call-to-Action Buttons -->\n        <div style=\"text-align: center; margin-top: 20px;\">\n          <a href=\"https://mixdorm.com\" class=\"button\">Start Exploring</a>\n          <a href=\"app-download-link\" class=\"button yellow-button\">Download the App</a>\n        </div>\n      </td>\n    </tr>\n\n    <!-- Social Media Links -->\n    <tr>\n      <td class=\"content\" style=\"text-align: center;\">\n        <h2>Follow Us on Social Media</h2>\n        <p>Get daily travel inspiration and connect with other MixDorm adventurers!</p>\n        <div class=\"social-icons\">\n          <a href=\"https://www.instagram.com/mixdorm.ai/?igsh=bHFnczNpMGNjMnAy#\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-instagram text-white\"><rect width=\"20\" height=\"20\" x=\"2\" y=\"2\" rx=\"5\" ry=\"5\"></rect><path d=\"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z\"></path><line x1=\"17.5\" x2=\"17.51\" y1=\"6.5\" y2=\"6.5\"></line></svg></a>\n          <a href=\"https://www.facebook.com/people/Mixdorm/61559838912283/?mibextid=LQQJ4d\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"#fff\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-facebook\"><path d=\"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z\"></path></svg></a>\n        </div>\n      </td>\n    </tr>\n\n    <!-- Footer -->\n    <tr>\n      <td class=\"footer\">\n        <p>If you’d like to stop receiving emails, you can <a href=\"unsubscribe-link\">unsubscribe here</a>.</p>\n        <p>For support, contact us at <a href=\"mailto:<EMAIL>\"><EMAIL></a>.</p>\n      </td>\n    </tr>\n  </table>\n\n</body>\n</html>"
    }      
];
async function initializeEmailTemplates() {
    for (const templateData of defaultEmailTemplates) {
        try {
            await emailTemplates.findOneAndUpdate(
                { name: templateData.name },
                templateData,
                { upsert: true, new: true }
            );
            console.log(`Email template '${templateData.name}' initialized or updated`);
        } catch (err) {
            console.error(`Error initializing email template '${templateData.name}':`, err);
        }
    }
}

export { initializeEmailTemplates };
