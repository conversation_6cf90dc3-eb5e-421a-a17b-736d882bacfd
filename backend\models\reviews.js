import mongoose from 'mongoose';

const reviewSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'users',
    required: true
  },
  property: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'properties',
    required: true
  },
  rating: {
    type: Number,
    min: 0,
    max: 5,
    required: false
  },
  comment: {
    type: String,
    required: false
  },
  images: [
    {
      url: {
        type: String,
        required: false
      },
      title: {
        type: String
      }
    }
  ],
  likes: {
    type: Number, // string or number
    default: 0
  },
  dislikes: {
    type: Number, // string or number
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isDeleted: {
    type: Boolean,
    default: false,
  },
  country:{
    type:String
  },
  userName:{
    type:String
  }
}, {timestamps : true});

const reviewModel = mongoose.model('reviews', reviewSchema);

export default reviewModel;