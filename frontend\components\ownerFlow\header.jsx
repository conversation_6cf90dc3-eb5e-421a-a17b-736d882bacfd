import { Search, Grip, CircleUser, BellRing } from "lucide-react";
import { useState, useEffect, Fragment } from "react";
import Image from "next/image";
import Link from "next/link";
import { useNavbar } from "../home/<USER>";
import { useRouter } from "next/router";
import { useHeaderOwner } from "./headerContex";
import { Dialog, Transition } from "@headlessui/react";
import MobileModal from "./mobileModel";
import CustomSelect from "@/components/common/CustomDropdown";
import toast from "react-hot-toast";
import countriesData from "world-countries";

const Header = ({ collapsed, setCollapsed }) => {
  // const [profileData, setProfileData] = useState(null);
  // const [data, setData] = useState(null);
  // const isFirstRender = useRef(true);
  const [open, setOpen] = useState(false);
  const openMenu = () => setOpen(true);
  const closeMenu = () => setOpen(false);

  const [isDataLoaded, setIsDataLoaded] = useState(false);

  const { token, role, hopid } = useNavbar();
  const router = useRouter();

    useEffect(() => {
    // Sidebar starts normal
      const collapseTimer = setTimeout(() => {
        setCollapsed(true); // Collapse after 2s
      }, 2000); // Adjust time before collapse
  
      const expandTimer = setTimeout(() => {
        setCollapsed(false); // Expand back after another 1s
      }, 3000); // 2s + 1s = total 3s delay for expansion
  
      return () => {
        clearTimeout(collapseTimer);
        clearTimeout(expandTimer);
      };
    }, []);


  // Automatically close the menu when the route changes
  useEffect(() => {
    const handleRouteChange = () => {
      closeMenu();
    };

    router.events.on("routeChangeStart", handleRouteChange);
    return () => {
      router.events.off("routeChangeStart", handleRouteChange);
    };
  }, [router]);

  useEffect(() => {
    if (token !== undefined && role !== undefined && hopid !== undefined) {
      setIsDataLoaded(true);
    }
  }, [token, role, hopid]);

  useEffect(() => {
    if (isDataLoaded) {
      const handleLogoutAndRemoveToken = async () => {
        if (!token && role !== "hostel_owner" && !hopid) {
          toast.success("Please Login..");
          router.push("/owner/login");
        }
      };

      handleLogoutAndRemoveToken(); // Call the async function
    }
  }, [isDataLoaded, token, role, hopid]);

  // useEffect(() => {
  //   if (!isFirstRender.current) {
  //     fetchUserData();
  //     const selectedId = localStorage.getItem("hopid");

  //     if (selectedId) {
  //       fetchPropertiesById(selectedId);
  //     }
  //   } else {
  //     isFirstRender.current = false;
  //   }
  //   if (window.innerWidth < 640) {
  //     setIsSidebarOpen(false); // Close sidebar on mobile after selecting
  //   }
  // };

  // const fetchPropertiesById = async (id) => {
  //   try {
  //     const response = await propertyDetailsApi(id);

  //     if (response?.status === 200) {
  //       setData(response?.data?.data);
  //     }
  //   } catch (error) {
  //     console.error("Error fetching properties:", error.message);
  //   }
  // };

  const { profileData, propertyData } = useHeaderOwner();
  console.log("propertyData", propertyData);

  const [countries, setCountries] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState(null);
  const [countryOptions, setCountryOptions] = useState([]);

  useEffect(() => {
    // Use world-countries package to get country data
    const data = countriesData
      .map((country) => ({
        ...country,
        currencyCode:
          country?.currencies && Object.keys(country.currencies).length > 0
            ? Object.keys(country.currencies)[0]
            : "N/A",
      }))
      .sort((a, b) => a.name.common.localeCompare(b.name.common));
    setCountries(data);
  }, []);

  useEffect(() => {
    const options = countries.map((country) => {
      const flagCode = country.cca2?.toLowerCase();
      const flag = flagCode
        ? `https://flagcdn.com/w320/${flagCode}.png`
        : "/placeholder.svg";
      return {
        value: {
          currencyCode: country.currencyCode,
          flag,
          name: country.name.common,
          cca2: country.cca2,
        },
        label: (
          <span className='flex items-center'>
            <Image
              src={flag}
              alt={`${country.name.common} Flag`}
              className='inline-block w-4 h-3 mr-2'
              width={20}
              height={15}
            />
            {country.name.common} ({country.cca2})
          </span>
        ),
      };
    });
    setCountryOptions(options);
  }, [countries]);

  const { updateCountryOwner,currencyCodeOwner,selectedOwner } = useNavbar();


    useEffect(() => {
      if (selectedOwner) {
        setSelectedCountry(selectedOwner)
      }
    }, [selectedOwner]);

  useEffect(() => {
    if (countryOptions.length > 0) {
      let userSelectedCountryName = null;
      try {
        userSelectedCountryName = localStorage.getItem("selectedOwnerCountry");
      } catch (e) {
        console.error("Error accessing localStorage:", e);
      }

      let match = null;
      if (userSelectedCountryName) {
        match = countryOptions.find(
          (option) =>
            option.value.name.toLowerCase() === userSelectedCountryName.toLowerCase()
        );
      }

      // 2. If not found, fallback to propertyData?.address?.country
      if (!match && propertyData?.address?.country) {
        match = countryOptions.find(
          (option) =>
            option.value.name.toLowerCase() === propertyData.address.country.toLowerCase()
        );
      }

      if (match) {
        setSelectedCountry(match);
        updateCountryOwner(match);
      }
    }
  }, [propertyData?.address?.country, countryOptions]);

  console.log({
    currencyCodeOwner,
    selectedOwner,
    selectedCountry
  });
  console.log("countries",countries,selectedOwner)


  return (
    <>
      <section className='flex items-center justify-center w-full sm:h-20 h-16 sm:px-5 px-3 sm:py-3 py-2 bg-black lg:px-8 sticky top-0 z-50 border '>
        <div className='flex items-center justify-between w-full font-Inter'>
          <div className='flex items-center justify-start md:gap-5 gap-3 text-white'>
            {/* <MoreHorizontal
              size={24}
              className="text-white cursor-pointer"
              onClick={toggleDrawer(true)} // Open the drawer
            /> */}
            <Grip
              size={24}
              className='text-white cursor-pointer font-bold md:hidden block'
              onClick={openMenu} // Open the drawer
            />

            <button
              onClick={() => setCollapsed(!collapsed)}
              className='text-black md:block hidden'
            >
              <Grip size={24} className='cursor-pointer text-white font-bold' />
            </button>

            <Transition show={open} as={Fragment}>
              <Dialog as='div' className='relative z-50' onClose={closeMenu}>
                {/* Overlay */}
                <Transition.Child
                  as={Fragment}
                  enter='ease-out duration-300'
                  enterFrom='opacity-0'
                  enterTo='opacity-100'
                  leave='ease-in duration-200'
                  leaveFrom='opacity-100'
                  leaveTo='opacity-0'
                >
                  <div className='fixed inset-0 bg-black/50' />
                </Transition.Child>

                {/* Slide-In Modal */}
                <div className='fixed inset-0 overflow-hidden'>
                  <div className='absolute inset-0 flex justify-start'>
                    <Transition.Child
                      as={Fragment}
                      enter='transform transition ease-out duration-300'
                      enterFrom='-translate-x-full'
                      enterTo='translate-x-0'
                      leave='transform transition ease-in duration-200'
                      leaveFrom='translate-x-0'
                      leaveTo='-translate-x-full'
                    >
                      <Dialog.Panel className='mobilemenu w-fit h-full bg-white shadow-xl overflow-y-auto overflow-x-hidden relative'>
                        {/* Modal Header */}
                        <button
                          onClick={closeMenu}
                          className='text-black font-bold hover:text-gray-800 absolute right-3 top-4 z-50'
                        >
                          &#10005; {/* Close icon */}
                        </button>

                        {/* Modal Content */}
                        <div>
                          {/* Conditionally Render MobileModal */}
                          {open && <MobileModal />}
                        </div>
                      </Dialog.Panel>
                    </Transition.Child>
                  </div>
                </div>
              </Dialog>
            </Transition>

            {/* <Drawer
              anchor="left"
              open={open}
              onClose={toggleDrawer(false)}
            >
              <MobileModal toggleDrawer={toggleDrawer} open={open} />
            </Drawer> */}

            {/* <Menu
              size={24}
              className="hidden text-black cursor-pointer md:block"
            /> */}
            {/* <Link href="/owner/hostel-login" prefetch={false}>
            <Link href="/owner/dashboard" prefetch={false}>
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mixdrom.svg`}
                width={129}
                height={27}
                alt="logo"
                loading="lazy"
              />
            </Link> */}
            <div className='relative'>
              <input
                type='search'
                placeholder='Search for rooms and offers'
                className='rounded-3xl sm:pl-9 pl-7 md:text-sm text-xs !mt-0 !mb-0 bg-transparent py-1 lg:w-80 sm:w-40 w-32 h-10 outline-none border border-white/50'
              />
              <Search
                className='absolute text-white/50 top-1/2 left-2.5 transform -translate-y-1/2'
                size={19}
              />
            </div>
          </div>
        </div>
        <div className='flex items-center justify-start md:gap-x-4 sm:gap-x-3 gap-x-2'>
          {/* <div className="flex items-center gap-2 text-white">
            <CircleHelp size={20} className="cursor-pointer" />
            <span className="text-xs">Help</span>
          </div> */}

          <Link href='#'>
            <div className='flex items-center sm:gap-2 gap-1 text-white'>
              <BellRing size={20} className='cursor-pointer' />
              <span className='sm:block hidden text-xs'>Noticeboard</span>
            </div>
          </Link>

          <div className='header-country inner-currency-wrap'>
            <CustomSelect
              options={countryOptions}
              value={selectedCountry}
              onChange={(selectedOption) => {
                updateCountryOwner(selectedOption);
                setSelectedCountry(selectedOption);
              }}
              placeholder={
                <span className='flex gap-1.5 items-center text-white'>
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/lang.svg`}
                    width={22}
                    height={22}
                  />
                  <span className='sm:block hidden'>Country</span>
                </span>
              }
            />
          </div>

          <Link href='/owner/dashboard' prefetch={false}>
            <div className='relative w-8 h-8 rounded-full'>
              {profileData?.profileImage?.objectURL ||
              propertyData?.images?.[0]?.objectUrl ? (
                <Image
                  src={
                    profileData?.profileImage?.objectURL ||
                    `${
                      propertyData?.images?.[0]?.objectUrl?.startsWith("https")
                        ? ""
                        : "https://"
                    }${propertyData?.images?.[0]?.objectUrl}`
                  }
                  width={36}
                  height={32}
                  alt='Avatar'
                  title='Avatar'
                  className='w-9 h-8 rounded-lg cursor-pointer border border-white object-cover'
                  loading='lazy'
                />
              ) : (
                <CircleUser
                  className='w-8 h-8 rounded-full cursor-pointer text-white'
                  size={40}
                />
              )}
            </div>
          </Link>
        </div>
      </section>
    </>
  );
};

export default Header;
