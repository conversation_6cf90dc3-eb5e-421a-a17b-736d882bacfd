import dynamic from 'next/dynamic';
const ReactApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

const DonutChart = ({
  label,
  value,
  users,
  color = '#26a688',
  bgColor = '#d6f5ee',
}) => {
  const series = [value, 100 - value];

  const options = {
    chart: {
      type: 'donut',
    },
    labels: [label, ''],
    colors: [color, bgColor],
    plotOptions: {
      pie: {
        donut: {
          size: '75%',
          labels: {
            show: true,
            name: { show: false },
            value: { show: true, color: '#fff', fontSize: '24px'},
            total: {
              show: true, // ✅ always show this
              showAlways: true,
              fontSize: '24px',
              fontWeight: 700,
              color: '#2C3E50',
              label: '', // hide default text
              formatter: () => `${value}%`, // 💡 always show this
            },
          },
        },
      },
    },
    legend: { show: false },
    dataLabels: { enabled: false },
    tooltip: { enabled: false },
    stroke: { width: 0 },
    responsive: [
        {
            breakpoint: 1024, // tablets
            options: {
            chart: {
                width: 280,
            },
            plotOptions: {
                pie: {
                donut: {
                    size: '75%', // Slightly thicker ring
                },
                },
            },
            },
        },
        {
            breakpoint: 640, // mobile
            options: {
            chart: {
                width: 200,
            },
            plotOptions: {
                pie: {
                donut: {
                    size: '80%', // Even thicker on small screens
                },
                },
            },
            },
        },
    ],
  };

  return (
    <div className="flex flex-col items-center space-y-2">
      <div className="w-full max-w-[300px] sm:max-w-[250px] mx-auto">
        <ReactApexChart
            options={options}
            series={series}
            type="donut"
            width="100%" // Let the container control size
        />
      </div>
      <div className="text-center mt-0">
        <p className="font-bold text-base text-[#ffffff]">{label}</p>
        <p className="text-sm text-[#ffffff]">
          {(users ?? 0).toLocaleString()} users
        </p>
      </div>
    </div>
  );
};

export default DonutChart;
