"use client";
import React from "react";
import { FiEye } from "react-icons/fi";
import { TfiPencilAlt } from "react-icons/tfi";
import { FaRegTrashCan } from "react-icons/fa6";
import Image from "next/image";
import { Plus } from "lucide-react";
import Link from "next/link";
import { MdOutlineKeyboardArrowLeft, MdOutlineKeyboardArrowRight } from "react-icons/md";

const BlogLanding = () => {
  const aboutusData = [
    {
      id: 1,
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Blog.png`,
    },
    {
      id: 2,
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Blog.png`,
    },
    {
      id: 3,
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Blog.png`,
    },
  ];
  return (
    
    <div className="w-full p-7 bg-sky-blue-20 lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px]  float-end  overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <div className="flex items-center justify-between w-full ">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Landing Blogs
        </h2>
        <div className="w-[50%] gap-x-5 flex justify-end items-center">
          <Link href={"/superadmin/dashboard/content-blog-add"}
            className={`px-4 py-2 text-sm font-normal text-white rounded relative flex justify-center items-center bg-sky-blue-650`}
            type="button"
            
          >
            <Plus size={18} className="mr-1" /> Landing Blogs
          </Link>
        </div>
      </div>
      <div className="bg-white border-b border-l border-r rounded-xl dark:bg-black dark:border-none">
        <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border-t dark:border-none">
          <table className="min-w-full divide-y bg-white rounded-xl divide-gray-200 dark:bg-black">
            <thead>
              <tr>
                <th className="pl-16 py-6 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  CARD
                </th>
                <th className="pr-10 py-6 bg-white text-end text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  ACTION
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 border-x border-y dark:border-x-0 text-black/70 dark:text-[#757575]">
              {aboutusData.map((aboutus) => (
                <tr key={aboutus.id}>
                  <td className="text-left pl-6">
                    <button className="font-medium py-4 rounded">
                      <Image
                        src={aboutus.image}
                        width={147}
                        height={191}
                        loading="lazy"
                        alt="Image"
                        className="w-38 h-52 mx-auto"
                      />
                    </button>
                  </td>
                  <td className="py-5 pr-5 flex justify-end">
                    <Link href={"/superadmin/dashboard/content-blog-details"} className="hover:text-blue-700 border p-2 rounded-l-lg text-black/75 dark:text-[#757575] dark:hover:text-blue-700">
                      <FiEye />
                    </Link>
                    <Link href={"/superadmin/dashboard/content-blog-edit"} className="border p-2 text-black/75 hover:text-yellow-400 dark:text-[#757575] dark:hover:text-yellow-400">
                      <TfiPencilAlt />
                    </Link>
                    <button className="p-2 border rounded-r-lg text-red-600">
                      <FaRegTrashCan />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
       <div className="flex justify-between items-center mt-5">
                    <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
                    <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
                      <a
                        href="#"
                        className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                      >
                        <span className="sr-only">Next Page</span>
                        <MdOutlineKeyboardArrowLeft />
                      </a>
            
                      <a
                        href="#"
                        className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                      >
                        <span className="sr-only">Next Page</span>
                        <MdOutlineKeyboardArrowRight />
                      </a>
                    </div>
                  </div>
    </div>
  
  );
};

export default BlogLanding;
