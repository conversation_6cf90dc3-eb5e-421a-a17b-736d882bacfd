import mongoose from 'mongoose';

const RideRecentSearchSchema = new mongoose.Schema({
    ride: {
        type: mongoose.Types.ObjectId,
        required: true,
        ref: 'rides',
    },
    user:{
        type: mongoose.Types.ObjectId,
        ref: 'users',
    }
},{
    timestamps:true
});

const RiderecentSearches = mongoose.model('rideRecentSearches', RideRecentSearchSchema,"rideRecentSearches");

export default RiderecentSearches;
