/* eslint-disable react/no-unescaped-entities */
import Image from "next/image";
import React, { useState, useEffect } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation } from "swiper/modules";
import Link from "next/link";
// import { motion } from "framer-motion";
import { ArrowLeft, ArrowRight } from "lucide-react";

const TravelActivity = () => {

  const [loading, setLoading] = useState(true);
  const data = [
    {
      id: 1,
      title: "Explore Ruins",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Travel-activity1.webp`,
      Link: "/travelactivity?category=Explore Ruins",
    },
    {
      id: 2,
      title: "Beach Snorkel",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Travel-activity2.webp`,
      Link: "/travelactivity?category=Beach Snorkel", 
    },
    {
      id: 3,
      title: "City Cycling",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Travel-activity3.webp`,
      Link: "/travelactivity?category=City Cycling",
    },
    {
      id: 4,
      title: "Mountain Trek",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Travel-activity4.webp`,
      Link: "/travelactivity?category=Mountain Trek",
    },
    {
      id: 5,
      title: "Food Tour",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Travel-activity5.webp`,
      Link: "/travelactivity?category=Food Tour",
    },
    {
      id: 6,
      title: "River Cruise",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Travel-activity6.webp`,
      Link: "/travelactivity?category=River Cruise",
    },
    {
      id: 7,
      title: "Spa Retreat",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Travel-activity7.webp`,
      Link: "/travelactivity?category=Spa Retreat",
    },
    {
      id: 8,
      title: "Road Trip",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Travel-activity8.webp`,
      Link: "/travelactivity?category=Road Trips",
    },
    {
      id: 9,
      title: "City Rush",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Travel-activity9.webp`,
      Link: "/travelactivity?category=City Rush",
    },
  ];

  useEffect(() => {
    if (data && data.length > 0) {
      setLoading(false);
    }
  }, [data]);

  return (
    <>
      <section className="w-full bg-white md:py-16 sm:py-10 py-8 lg:px-6 relative">
        <div className="absolute -top-10 right-8 w-40 h-40 bg-cyan-400 rounded-full blur-2xl opacity-30"></div>
        <div className="absolute -top-20 -left-8 w-48 h-48 bg-yellow-300 rounded-full blur-2xl opacity-30"></div>
        <div className="absolute bottom-10 right-4 w-40 h-40 bg-pink-300 rounded-full blur-2xl opacity-40"></div>
        <div className="container relative">
          <div className="flex justify-between items-center">
            {/* <motion.h2
              initial={{ x: -100, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              viewport={{ once: false }}  */}
            <h2 className="font-normal xs:text-4xl text-3xl md:text-6xl text-primary-blue font-mashiny sm:mb-8 mb-3">
              Travel by <span className="text-black"> Activities</span>
            </h2>
            {/* </motion.h2> */}
            {/* <motion.div
              initial={{ x: 100, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              viewport={{ once: false }} */}
            <div
              className="text-center sm:hidden block">
              <Link
                href="/travelactivity"
                className="text-sm font-semibold text-black bg-primary-blue rounded-4xl py-2 px-5 hover:bg-sky-blue-750"
                prefetch={false}
              >
                See All
              </Link>
            </div>
            {/* </motion.div> */}
            <div className={`gap-2 ${data?.length > 9 ? 'xl:flex' : 'xl:hidden'
              } ${data?.length > 8 ? 'lg:flex' : 'lg:hidden'} ${data?.length > 6 ? 'md:flex' : 'md:hidden'} ${data?.length > 4.6 ? 'sm:flex' : 'sm:hidden'} ${data?.length > 3.4 ? 'hidden' : 'hidden'}`}
            >
              <div className="slider-button-prev cursor-pointer"><ArrowLeft size={18} /></div>
              <div className="slider-button-next cursor-pointer"><ArrowRight size={18} /></div>
            </div>
          </div>
          {/* <motion.p
            initial={{ x: -100, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: false }} */}
          <p
            className="hidden sm:block mt-2 text-base font-medium text-[#737373] font-manrope mb-3 w-[80%]">
            Discover top-rated hostels and dormitories<Link href={"/travelactivity"} className="text-primary-blue"> travel activities</Link> around the world, curated for every kind of
            traveler. Whether you're chasing adventure, looking for a social backpackers’ vibe, or
            craving a peaceful budget retreat, our handpicked hostels offer the perfect mix of comfort,
            affordability, and experience.
            Explore our top 8 featured hostels, each offering unique amenities, vibrant atmospheres,
            and competitive room ratesfor solo travelers, couples, and groups alike.
            Start your hostel booking journey with Mixdorm today and find the stay that matches your
            travel style.
          </p>
          {/* </motion.p> */}
          {/* <motion.div
            initial={{ y: 80, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: false }}
          > */}
          <div>
            <Swiper
              modules={[Autoplay, Navigation]}
              // autoplay={{ delay: 1500 }}
              slidesPerView={18}
              loop
              navigation={{
                prevEl: '.slider-button-prev',
                nextEl: '.slider-button-next',
              }}
              speed={1000}
              spaceBetween={14}
              className="mySwiper myCustomSwiper travelactivity-slider discover-event-slider-home overflow-hidden"
              breakpoints={{
                0: {
                  slidesPerView: 3.2,
                },
                480: {
                  slidesPerView: 3.4,
                },
                640: {
                  slidesPerView: 4.6,
                },
                768: {
                  slidesPerView: 6,
                },
                1024: {
                  slidesPerView: 8,
                },
                1280: {
                  slidesPerView: 9,
                },
              }}
            >
              {loading ? (
                // Render 8 skeleton slides
                Array.from({ length: 8 }).map((_, index) => (
                  <SwiperSlide key={`skeleton-${index}`}>
                    <div className="flex flex-col items-center py-3">
                      <div className="w-24 h-24 rounded-full bg-gray-200 animate-pulse mb-4" />
                      <div className="w-16 h-4 bg-gray-200 animate-pulse rounded" />
                    </div>
                  </SwiperSlide>
                ))
              ) : (
                // Render actual data
                data.map((item) => (
                  <SwiperSlide key={item.id}>
                    <Link
                      href={item?.Link}
                      className="font-manrope text-center tracking-normal group transition-all py-3 block"
                      prefetch={false}
                    >
                      <div className="relative w-20 h-20 min-w-20 min-h-20 xs:w-24 xs:h-24 xs:min-w-24 xs:min-h-24 mx-auto mb-4 rounded-full overflow-hidden">
                        <Image
                          src={item.image}
                          alt={`Travel by Activity ${item.title}`}
                          fill
                          className={`rounded-full object-cover group-hover:scale-110 transition-all ${loading ? "bg-slate-200 animate-pulse" : "bg-slate-200"}`}
                          sizes="(max-width: 640px) 80px, 96px"
                          loading="lazy"
                          
                        />
                      </div>
                      <p className="xs:text-sm text-xs font-bold mb-0 group-hover:text-primary-blue transition-all">
                        {item.title}
                      </p>
                    </Link>
                  </SwiperSlide>
                ))
              )}
            </Swiper>
          </div>
          {/* </motion.div> */}
          {/* <motion.div
            initial={{ y: 80, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: false }} */}
          <div
            className="text-center mt-10 hidden sm:block">
            <Link
              href="/travelactivity"
              className="text-sm font-semibold text-black bg-primary-blue rounded-4xl py-4 px-12 hover:bg-sky-blue-750"
              prefetch={false}
            >
              See All
            </Link>
          </div>
          {/* </motion.div> */}
        </div>
      </section>
    </>
  );
};

export default TravelActivity;