import React, { useEffect, useRef, useState } from "react";
import toast, { Toaster } from "react-hot-toast";
import Image from "next/image";
import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/react";
import {
  Trash,
  MoreVertical,
  Pencil,
  Eye,
  Trash2,
  CircleChevronRight,
  CircleChevronLeft,
} from "lucide-react";
import { DeleteRoomApi, RoomListApi } from "@/services/ownerflowServices";
import { getItemLocalStorage } from "@/utils/browserSetting";
import dynamic from "next/dynamic";
import countries from "world-countries";
import Pagination from "@/components/common/commonPagination";
import { Swiper, SwiperSlide } from "swiper/react";

const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

const Roomlist = ({
  setEditRoom,
  setEditId,
  setIseditOpen,
  setIsviewOpen,
  isUpdate,
}) => {
  const id = getItemLocalStorage("hopid");
  const [properties, setProperties] = useState([]);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [currencyData, setCurrencyData] = useState({});
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalData, setTotalData] = useState();
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [roomToDelete, setRoomToDelete] = useState(null);
  // eslint-disable-next-line no-unused-vars
  const [deleting, setDeleting] = useState(false);

  const isFirstRender = useRef(null);

  const [imagePreviewModal, setImagePreviewModal] = useState(false);
  const [previewIndex, setPreviewIndex] = useState(0);
  const [imagesToPreview, setImagesToPreview] = useState([]);

  const mainSwiperRef = useRef(null);
  const mainThumbRef = useRef(null);
  const [activeIndex, setActiveIndex] = useState(0);

  const handlePreviewClick = (images, index = 0) => {
    setImagesToPreview(images);
    setPreviewIndex(index);
    setImagePreviewModal(true);
  };

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);
      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);

  // const getCurrencySymbol = (currencyCode) => {
  //   return currencyData[currencyCode] || currencyCode;
  // };

  useEffect(() => {
    if (id && !isFirstRender.current) {
      fetchList();
    } else {
      isFirstRender.current = false;
    }
  }, [currentPage, isUpdate, itemsPerPage]);

  const fetchList = async () => {
    try {
      setLoading(true);
      const response = await RoomListApi(id, currentPage, itemsPerPage);
      if (response.status === 200) {
        setTotalPages(response?.data?.data?.pagination?.totalPages);
        setTotalData(response?.data?.data?.pagination?.totalRooms);
        setProperties(response?.data?.data?.rooms);
      } else {
        toast.error(response?.data?.message);
      }
    } catch (error) {
      console.error("Error fetching room list:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (slug) => {
    setRoomToDelete(slug);
    setIsDeleteModalOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!roomToDelete) return;

    try {
      setLoading(true);
      const res = await DeleteRoomApi(roomToDelete);
      if (res?.status === 200) {
        fetchList();
        toast.success("Room deleted successfully");
        setIsDeleteModalOpen(false);
        setRoomToDelete(null);
      }
    } catch (error) {
      console.error("Error deleting room:", error);
      toast.error("Failed to delete room");
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  return (
    <>
      <Loader open={loading} />
      <Toaster position="top-center" />
      {isDeleteModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="text-center">
              <Trash2 className="mx-auto h-12 w-12 text-red-500" />
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                Delete Room
              </h3>
              <p className="mt-2 text-sm text-gray-500">
                Are you sure you want to delete this room? This action cannot be
                undone.
              </p>
            </div>
            <div className="mt-6 flex justify-center space-x-4">
              <button
                onClick={() => {
                  setIsDeleteModalOpen(false);
                  setRoomToDelete(null);
                }}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none"
              >
                No, Cancel
              </button>
              <button
                onClick={handleConfirmDelete}
                disabled={deleting}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none disabled:opacity-50"
              >
                {deleting ? "Deleting..." : "Yes, Delete"}
              </button>
            </div>
          </div>
        </div>
      )}

      {loading ? (
        <div className="w-full mt-5 text-nowrap overflow-x-auto rounded-lg overflow-y-visible">
          <table className="w-full border border-gray-340 rounded-lg font-inter">
            <thead className="rounded-t-lg">
              <tr className="rounded-t-lg">
                <th className="pl-6 py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left rounded-tl-lg">
                  Room ID
                </th>
                <th className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left">
                  Room Name
                </th>
                <th className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left">
                  Descriptions
                </th>
                <th className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left">
                  Weekdays / <br /> Weekend
                </th>
                <th className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left">
                  Currency
                </th>
                <th className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left">
                  Room Photo
                </th>
                <th className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left">
                  Room Type
                </th>
                <th className="pr-6 py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left rounded-tr-lg"></th>
              </tr>
            </thead>
            <tbody>
              {/* Skeleton rows */}
              {[...Array(5)].map((_, index) => (
                <tr key={index}>
                  <td className="text-xs font-medium pl-6 py-3.5 px-4 border-b">
                    <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse"></div>
                  </td>
                  <td className="text-xs font-medium py-3.5 px-4 border-b">
                    <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse"></div>
                  </td>
                  <td className="text-xs font-medium py-3.5 px-4 border-b">
                    <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse"></div>
                  </td>
                  <td className="text-xs font-medium py-3.5 px-4 border-b">
                    <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
                  </td>
                  <td className="text-xs font-medium py-3.5 px-4 border-b">
                    <div className="h-4 bg-gray-200 rounded w-2/3 animate-pulse"></div>
                  </td>
                  <td className="text-xs font-medium py-3.5 px-4 border-b">
                    <div className="flex space-x-2">
                      <div className="h-[36px] w-[54px] bg-gray-200 rounded-sm animate-pulse"></div>
                    </div>
                  </td>
                  <td className="text-xs font-medium py-3.5 px-4 border-b">
                    <div className="h-6 bg-gray-200 rounded-3xl w-20 animate-pulse"></div>
                  </td>
                  <td className="text-xs font-semibold pr-6 py-3.5 border-b text-center">
                    <div className="h-4 w-4 bg-gray-200 rounded-full animate-pulse mx-auto"></div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {/* Skeleton pagination */}
          <div className="flex justify-between items-center mt-4 animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="flex space-x-2">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-8 w-8 bg-gray-200 rounded-md"></div>
              ))}
            </div>
          </div>
        </div>
      ) : (
        <div className="w-full mt-5 text-nowrap overflow-x-auto rounded-lg overflow-y-visible">
          <table className="w-full border border-gray-340 rounded-lg font-inter">
            <thead className="rounded-t-lg">
              <tr className="rounded-t-lg">
                <th className="pl-6 py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left rounded-tl-lg">
                  Room ID
                </th>
                <th className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left">
                  Room Name
                </th>
                <th className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left">
                  Descriptions
                </th>
                {/* <th className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left">
                  Weekdays / <br /> Weekend
                </th>
                <th className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left">
                  Currency
                </th> */}
                <th className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left">
                  Room Photo
                </th>

                {/* <th className='py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left'>
                Descriptions
              </th>
              <th className='py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left'>
                Tags
              </th> */}
                <th className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left">
                  Room Type
                </th>
                {/* <th className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left">
                Room Category
              </th> */}
                <th className="pr-6 py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left rounded-tr-lg"></th>
              </tr>
            </thead>
            <tbody>
              {properties?.map((val, index) => (
                <tr key={index}>
                  <td className="text-xs font-medium pl-6 py-3.5 px-4 border-b  text-black">
                    {val?.id ? `#${val.id}` : `#0${index + 1}`}
                  </td>
                  <td className="text-xs font-medium py-3.5 px-4 border-b  text-black">
                    {val?.name}
                  </td>
                  <td className="text-xs font-medium py-3.5 px-4 border-b  text-black">
                    {val?.description || "---"}
                  </td>
                  {/* <td className=" text-xs font-medium py-3.5 px-4 border-b  text-black">
                    {getCurrencySymbol(val?.currency)}{" "}
                    {val?.rate?.weekdayRate?.value} /{" "}
                    {getCurrencySymbol(val?.currency)}{" "}
                    {val?.rate?.weekendRate?.value ||
                      val?.rate?.weekdayRate?.value}
                  </td> */}
                  {/* <td
                  className=' text-xs font-medium py-3.5 px-4 border-b  text-black'
                // onClick={() => {
                //   setEditRoom(val?.slug);
                //   setActiveTab(4);
                // }}
                >
                  {getCurrencySymbol(val?.currency)}{" "}
                  {val?.rate?.weekendRate?.value || val?.rate?.weekdayRate?.value}
                </td> */}
                  {/* <td className="text-xs font-medium py-3.5 px-4 border-b text-black ">
                    {`${val?.currency}(${getCurrencySymbol(val?.currency)})`}
                  </td> */}
                  {/* <td className="text-xs font-medium py-3.5 px-4 border-b  text-black">
                    {val?.images?.length > 0 ? (
                      <div className="flex space-x-2">
                        {val.images.map((image, index) => (
                          <Image
                            key={index}
                            // src={image.url}
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/${image?.objectUrl}`}
                            alt={`image-${index}`}
                            className="object-cover h-[36px] rounded-sm overflow-hidden"
                            width={54}
                            height={36}
                            loading="lazy"
                          />
                        ))}
                      </div>
                    ) : (
                      <span>No Images</span>
                    )}
                  </td> */}
                  <td className="text-xs font-medium py-3.5 px-4 border-b text-black">
                    {val?.images?.length > 0 ? (
                      <div className="flex">
                        {val.images.slice(0, 3).map((image, index) => (
                          <div
                            key={index}
                            className="relative"
                            style={{
                              marginLeft: index === 0 ? "0" : "-10%",
                              zIndex: index + 1,
                            }}
                          >
                            <Image
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/${image?.objectUrl}`}
                              alt={`image-${index}`}
                              className="object-cover h-[36px] rounded-sm overflow-hidden border border-white"
                              width={54}
                              height={36}
                              loading="lazy"
                              style={{
                                opacity:
                                  index === 0 ? 0.85 : index === 1 ? 0.7 : 1,
                              }}
                            />
                            {index === 2 && val.images.length > 3 && (
                              <div
                                className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center cursor-pointer"
                                onClick={() =>
                                  handlePreviewClick(val.images, 2)
                                }
                              >
                                <span className="text-white text-xs font-bold">
                                  +{val.images.length - 3}
                                </span>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <span>No Images</span>
                    )}
                  </td>
                  {/* <td className='text-xs font-medium py-3.5 px-4 border-b text-black'>
                  {val?.description}
                </td> */}
                  {/* <td className='text-xs font-medium py-3.5 px-4 border-b text-black'>
                  {val?.bedAnBreakfast && (
                    <span className='label bg-green-100 text-green-700 px-2 py-1 rounded'>
                      Bed & Breakfast
                    </span>
                  )}
                  {val?.freeCancellation && (
                    <span className='label bg-blue-100 text-blue-700 px-2 py-1 rounded'>
                      Free Cancellation
                    </span>
                  )}
                  {val?.nonRefundable && (
                    <span className='label bg-red-100 text-red-700 px-2 py-1 rounded'>
                      Non-Refundable
                    </span>
                  )}
                </td> */}
                  {/* <td className="text-xs font-medium py-3.5 px-4 border-b text-black ">
                  <span className="label bg-[#EEEEEE] text-[#448DF2] px-2 py-1 rounded-3xl capitalize">
                    {val?.type}
                  </span>
                </td> */}
                  <td className="text-xs font-medium py-3.5 px-4 border-b text-black ">
                    <span className="label bg-[#EEEEEE] text-gray-500 px-2 py-1 rounded-3xl capitalize">
                      {val?.dormitory ? "Dormitory" : "Private"}
                    </span>
                  </td>

                  {/* <td className="text-xs font-medium py-3.5 px-4 border-b text-black ">
                  <span className="label bg-[#EEEEEE] text-[#448DF2] px-2 py-1 rounded-3xl capitalize">
                    4 Bed Mixdorm Dormitory
                  </span>
                </td> */}
                  <td className="text-xs font-semibold pr-6 py-3.5 border-b text-center">
                    <Menu as="div" className="relative inline-block text-left">
                      <div>
                        <MenuButton>
                          <MoreVertical
                            aria-hidden="true"
                            size={16}
                          ></MoreVertical>
                        </MenuButton>
                      </div>

                      <MenuItems
                        transition
                        ref={(el) => {
                          if (el) {
                            const rect = el.getBoundingClientRect();
                            const windowHeight = window.innerHeight;
                            const isLastItem = index === properties.length - 1;
                            const isSecondLastItem =
                              index === properties.length - 2;
                            const isOnlyItem = properties.length === 1;
                            const hasMoreThanTwoItems = properties.length > 2;

                            // Clear previous classes
                            el.classList.remove(
                              "bottom-full",
                              "mb-2",
                              "mt-2",
                              "top-1/2",
                              "-translate-y-1/2"
                            );

                            if (isOnlyItem) {
                              el.classList.add("top-1/2", "-translate-y-1/2");
                            } else if (
                              isLastItem ||
                              (hasMoreThanTwoItems && isSecondLastItem) ||
                              rect.bottom > windowHeight
                            ) {
                              el.classList.add("bottom-full", "mb-2");
                            } else {
                              el.classList.add("mt-2");
                            }
                          }
                        }}
                        // className="absolute z-50 w-40 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 focus:outline-none"
                        style={{
                          right: 0,
                        }}
                        className="absolute right-0 z-10 w-max origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5  focus:outline-none data-[closed]:scale-95 data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
                      >
                        <div>
                          <MenuItem>
                            <button
                              href="#"
                              className="px-4 py-2 text-sm w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-t-md"
                              onClick={() => {
                                setEditRoom(true);
                                setIsviewOpen(true);
                                setEditId(val);
                              }}
                            >
                              <Eye size={16}></Eye>
                              View
                            </button>
                          </MenuItem>
                          <MenuItem>
                            <button
                              href="#"
                              className="px-4 py-2 text-sm w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-t-md"
                              onClick={() => {
                                setEditRoom(true);
                                setIseditOpen(true);
                                setEditId(val);
                              }}
                            >
                              <Pencil size={16}></Pencil>
                              Edit
                            </button>
                          </MenuItem>
                          <MenuItem>
                            <button
                              onClick={() => handleDelete(val?.slug)}
                              className="px-4 py-2 text-sm text-red-600 w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-b-md"
                            >
                              <Trash size={16} className="text-red-600" />{" "}
                              <span>Delete</span>
                            </button>
                          </MenuItem>
                        </div>
                      </MenuItems>
                    </Menu>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {properties?.length > 0 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={totalData || 0}
              itemsPerPage={itemsPerPage}
              onPageChange={handlePageChange}
              onItemsPerPageChange={handleItemsPerPageChange}
            />
          )}
        </div>
      )}
      {imagePreviewModal && (
        <div className="fixed inset-0 bg-black bg-opacity-90 z-[999] flex flex-col items-center justify-center px-4">
          {/* Close Button */}
          <button
            onClick={() => setImagePreviewModal(false)}
            className="absolute top-4 right-4 text-white text-3xl font-bold z-50"
          >
            &times;
          </button>

          {/* Main Swiper */}
          <div className="relative w-full max-w-4xl h-[70vh] mb-4">
            <Swiper
              slidesPerView={1}
              centeredSlides={true}
              onSlideChange={(swiper) => {
                setActiveIndex(swiper.realIndex);
                if (mainThumbRef.current?.swiper) {
                  mainThumbRef.current.swiper.slideTo(swiper.realIndex);
                }
              }}
              className="h-full"
              ref={mainSwiperRef}
              spaceBetween={10}
              initialSlide={previewIndex}
            >
              {imagesToPreview.map((img, idx) => (
                <SwiperSlide key={idx}>
                  <div className="w-full h-full relative">
                    <Image
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/${img?.objectUrl}`}
                      alt={`Preview Image ${idx + 1}`}
                      layout="fill"
                      className="rounded-xl object-contain"
                    />
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>

            {/* Arrows */}
            <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 z-10 flex space-x-16">
              <div
                className="flex items-center justify-center w-8 h-8 shadow-md cursor-pointer"
                onClick={() => mainSwiperRef.current?.swiper.slidePrev()}
              >
                <CircleChevronLeft size={24} color="#fff" />
              </div>
              <div
                className="flex items-center justify-center w-8 h-8 shadow-md cursor-pointer"
                onClick={() => mainSwiperRef.current?.swiper.slideNext()}
              >
                <CircleChevronRight size={24} color="#fff" />
              </div>
            </div>
          </div>

          {/* Thumbnail Swiper */}
          <div className="w-full max-w-5xl mt-4 relative">
            <Swiper
              slidesPerView={Math.min(5, imagesToPreview.length)}
              spaceBetween={10}
              centeredSlides={true}
              className="h-full"
              ref={mainThumbRef}
              initialSlide={previewIndex}
            >
              {imagesToPreview.map((img, idx) => (
                <SwiperSlide key={idx}>
                  <div
                    className={`relative w-full h-32 rounded-md cursor-pointer border ${
                      idx === activeIndex
                        ? "border-white"
                        : "border-transparent"
                    }`}
                    onClick={() => mainSwiperRef.current?.swiper.slideTo(idx)}
                  >
                    <Image
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/${img?.objectUrl}`}
                      alt={`Thumb ${idx + 1}`}
                      layout="fill"
                      className="rounded-md object-cover"
                    />
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        </div>
      )}
    </>
  );
};

export default Roomlist;
