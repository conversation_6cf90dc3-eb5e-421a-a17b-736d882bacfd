import express from "express";
import { createGroup,addMemberToGroup,removeMemberFromGroup,deleteGroup,getGroupById } from '../controller/group.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();

// router.post("/create", checkAuth('create'), createGroup);
router.post('/create', createGroup); 
router.post('/:groupId/addMember',addMemberToGroup);
router.delete('/:groupId/removeMember/:userId',removeMemberFromGroup);
router.delete('/:groupId/deleteGroup',deleteGroup);
router.get('/:groupId/getGroupById',getGroupById)

export default router;

/**
 * @swagger
 * tags:
 *   name: Group
 *   description: API for managing groups and users
 */

/**
 * @swagger
 * /group/create:
 *   post:
 *     summary: Create a new group
 *     description: Creates a new group and associates it with the creator and members.
 *     tags: [Group]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - createdBy
 *             properties:
 *               name:
 *                 type: string
 *                 description: The name of the group.
 *                 example: Book Club
 *               createdBy:
 *                 type: string
 *                 description: The ID of the user creating the group.
 *                 example: 64c75d7e8f1b2c001f4e95e5
 *               members:
 *                 type: array
 *                 description: Array of user IDs to be added as members.
 *                 items:
 *                   type: string
 *                 example: ["64c75d7e8f1b2c001f4e95e6", "64c75d7e8f1b2c001f4e95e7"]
 *     responses:
 *       201:
 *         description: Group created successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 _id:
 *                   type: string
 *                   description: The ID of the created group.
 *                   example: 64c75d7e8f1b2c001f4e95f0
 *                 name:
 *                   type: string
 *                   description: The name of the group.
 *                   example: Book Club
 *                 createdBy:
 *                   type: string
 *                   description: The ID of the user who created the group.
 *                   example: 64c75d7e8f1b2c001f4e95e5
 *                 members:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: List of member IDs in the group.
 *                   example: ["64c75d7e8f1b2c001f4e95e6", "64c75d7e8f1b2c001f4e95e7"]
 *       400:
 *         description: Invalid request parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Group name and creator ID are required.
 *       404:
 *         description: Creator user not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Creator not found.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Internal server error.
 */

/**
 * @swagger
 * /group/{groupId}/addMember:
 *   post:
 *     summary: Add a member to a group
 *     description: Adds a user to an existing group by validating if both the group and user exist and ensuring the user is not already a member.
 *     tags: [Group]
 *     parameters:
 *       - name: groupId
 *         in: path
 *         description: The ID of the group to which the user is being added.
 *         required: true
 *         schema:
 *           type: string
 *           example: 64c75d7e8f1b2c001f4e95f0
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *                 description: The ID of the user to be added to the group.
 *                 example: 64c75d7e8f1b2c001f4e95e6
 *     responses:
 *       200:
 *         description: User added to group successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User added to group successfully."
 *       400:
 *         description: Bad request, missing groupId or userId, or user already a member.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Group ID and User ID are required."
 *       404:
 *         description: Group or User not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Group not found."
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error."
 */

/**
 * @swagger
 * /group/{groupId}/removeMember/{userId}:
 *   delete:
 *     summary: Remove a member from a group
 *     description: Removes a user from a group's membership list by validating if both the group and user exist and if the user is a member of the group.
 *     tags: [Group]
 *     parameters:
 *       - name: groupId
 *         in: path
 *         description: The ID of the group from which the user is being removed.
 *         required: true
 *         schema:
 *           type: string
 *       - name: userId
 *         in: path
 *         description: The ID of the user to be removed from the group.
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: User removed from group successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User removed from group successfully."
 *       400:
 *         description: Bad request, missing groupId or userId, or user not a member.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User is not a member of the group."
 *       404:
 *         description: Group or User not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Group not found."
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error."
 */

/**
 * @swagger
 * /group/{groupId}/deleteGroup:
 *   delete:
 *     summary: Mark a group as deleted (soft delete)
 *     description: Marks a group as deleted instead of hard-deleting it, preserving transaction history.
 *     tags: [Group]
 *     parameters:
 *       - name: groupId
 *         in: path
 *         description: The ID of the group to be marked as deleted.
 *         required: true
 *         schema:
 *           type: string
 *           example: "64c75d7e8f1b2c001f4e95e6"
 *     responses:
 *       200:
 *         description: Group marked as deleted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Group marked as deleted successfully."
 *       404:
 *         description: Group not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Group not found."
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error."
 */

/**
 * @swagger
 * /group/{groupId}/getGroupById:
 *   get:
 *     summary: Find a group by ID
 *     description: Fetches details of a group by its ID if it is not deleted.
 *     tags: [Group]
 *     parameters:
 *       - name: groupId
 *         in: path
 *         description: The ID of the group to be fetched.
 *         required: true
 *         schema:
 *           type: string
 *           example: "64c75d7e8f1b2c001f4e95e6"
 *     responses:
 *       200:
 *         description: Group found successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Group found successfully."
 *                 data:
 *                   type: object
 *                   properties:
 *                     _id:
 *                       type: string
 *                       example: "64c75d7e8f1b2c001f4e95e6"
 *                     name:
 *                       type: string
 *                       example: "Study Group"
 *                     createdBy:
 *                       type: object
 *                       properties:
 *                         _id:
 *                           type: string
 *                           example: "5b3c50b0f53e6c001f58a02c"
 *                         name:
 *                           type: string
 *                           example: "John Doe"
 *                         email:
 *                           type: string
 *                           example: "<EMAIL>"
 *                     members:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           _id:
 *                             type: string
 *                             example: "5b3c50b0f53e6c001f58a02d"
 *                           name:
 *                             type: string
 *                             example: "Jane Doe"
 *                           email:
 *                             type: string
 *                             example: "<EMAIL>"
 *       404:
 *         description: Group not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Group not found."
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error."
 */







