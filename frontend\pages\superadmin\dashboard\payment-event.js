"use client";
import React from "react";
import { FaArrowTrendDown, FaArrowTrendUp } from "react-icons/fa6";
import Image from "next/image";
import { IoArrowRedoOutline } from "react-icons/io5";
import { FiEye } from "react-icons/fi";
import "@fontsource/poppins";
import Link from "next/link";
import EventFilter from "@/components/superadmin/EventFilter";
import { MdOutlineKeyboardArrowLeft, MdOutlineKeyboardArrowRight } from "react-icons/md";

const PaymentEvent = () => {
  const paymentData = [
    {
      id: 1,
      eventId: "#2586",
      eventname: "<PERSON>",
      method: "VISA",
      amount: "$299",
      activity: "Club Hopping",
      date: "04/02/24",
      status: "Paid",
    },
    {
      id: 2,
      eventId: "#2645",
      eventname: "Alexander",
      method: "VISA",
      amount: "$599",
      activity: "Club Hopping",
      date: "04/02/24",
      status: "Paid",
    },
    {
      id: 3,
      eventId: "#2545",
      eventname: "<PERSON>",
      method: "VISA",
      amount: "$1299",
      activity: "Club Hopping",
      date: "04/02/24",
      status: "Paid",
    },
    {
      id: 4,
      eventId: "#2545",
      eventname: "Alexander",
      method: "VISA",
      amount: "$1299",
      activity: "Club Hopping",
      date: "04/02/24",
      status: "Paid",
    },
    {
      id: 5,
      eventId: "#8645",
      eventname: "Alexander",
      method: "VISA",
      amount: "$1299",
      activity: "Club Hopping",
      date: "04/02/24",
      status: "Paid",
    },
    {
      id: 6,
      eventId: "#2645",
      eventname: "Alexander",
      method: "VISA",
      amount: "$1299",
      activity: "Club Hopping",
      date: "04/02/24",
      status: "Paid",
    },
  ];

  return (
    <div className="w-full p-7 bg-sky-blue-20 lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px]  float-end  overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        Event Revenue
      </h2>
        {/* <div className="flex items-center justify-between py-7 flex-wrap gap-y-6">
                    <div className="flex flex-col justify-between p-4 rounded-2xl bg-white w-[358px]  h-[161px]">
                      <div className="flex justify-between">
                        <div className="py-2">
                          <h2 className="text-black/75 text-base font-medium font-poppins">
                            Total Payment
                          </h2>
                          <h1 className="font-bold text-black text-3xl font-poppins  pt-2">
                            40,689
                          </h1>
                        </div>
                        <div className="bg-[#E5E4FF] h-12 w-12 mt-2 flex items-center justify-center rounded-2xl">
                          <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Total.png`}
                            width={16}
                            height={24}
                            alt="Total"
                            className="h-5 w-6"
                          />
                        </div>
                      </div>
                      <span className="text-teal-500 flex items-center gap-1 text-sm font-semibold font-poppins">
                        <FaArrowTrendUp />
                        8.5%
                        <p className="text-black/75 text-sm font-semibold font-poppins">
                          Up from yesterday
                        </p>
                      </span>
                    </div>
                    <div className="flex flex-col justify-between p-4 rounded-2xl bg-white w-[358px] h-[161px]">
                      <div className="flex justify-between">
                        <div className="py-2">
                          <h2 className="text-black/75 text-base font-poppins font-medium">
                            Paid Payment
                          </h2>
                          <h1 className="font-bold text-black text-3xl font-poppins  pt-2">
                            12,112
                          </h1>
                        </div>
                        <div className="bg-[#FFF3D6] h-12 w-12 mt-2 flex items-center justify-center rounded-2xl">
                          <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Paid.png`}
                            width={16}
                            height={24}
                            alt="Paid"
                            className="h-5 w-6"
                          />
                        </div>
                      </div>
                      <span className="text-teal-500 flex items-center gap-1 text-sm font-semibold font-poppins">
                        <FaArrowTrendUp />
                        1.3%
                        <p className="text-black/75 text-sm font-poppins font-semibold">
                          Up from past week
                        </p>
                      </span>
                    </div>
                    <div className="flex flex-col justify-between p-4 rounded-2xl bg-white w-[358px] h-[161px]">
                      <div className="flex justify-between">
                        <div className="py-2">
                          <h2 className="text-black/75 text-base font-medium font-poppins">
                            Pending Payment
                          </h2>
                          <h1 className="font-bold text-3xl font-poppins pt-2 text-black">
                            9,000
                          </h1>
                        </div>
                        <div className="bg-[#D9F7E8] h-12 w-12 mt-2 flex items-center justify-center rounded-2xl">
                          <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Pending.png`}
                            width={16}
                            height={24}
                            alt="Pending"
                            className="h-5 w-6"
                          />
                        </div>
                      </div>
                      <span className="text-red-600 flex items-center gap-1 text-sm font-poppins font-semibold">
                        <FaArrowTrendDown />
                        4.3%
                        <p className="text-black/75 text-sm font-poppins font-semibold">
                          Down from yesterday
                        </p>
                      </span>
                    </div>
      
                  </div> */}

                  <div className="flex items-center justify-between py-7 flex-wrap gap-y-6">
                          <div className="flex flex-col justify-between p-4 rounded-2xl bg-white w-[358px]  h-[161px] dark:bg-black">
                            <div className="flex justify-between">
                              <div className="py-2">
                                <h2 className="text-black/75 text-base font-medium font-poppins dark:text-[#757575]">
                                  Total Payment
                                </h2>
                                <h1 className="font-bold text-black text-3xl font-poppins  pt-2 dark:text-gray-200">
                                  40,689
                                </h1>
                              </div>
                              <div className="bg-[#E5E4FF] h-12 w-12 mt-2 flex items-center justify-center rounded-2xl">
                                <Image
                                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Total.png`}
                                  width={16}
                                  height={24}
                                  alt="Total"
                                  className="h-5 w-6"
                                />
                              </div>
                            </div>
                            <span className="text-primary-blue flex items-center gap-1 text-sm font-semibold font-poppins">
                              <FaArrowTrendUp />
                              8.5%
                              <p className="text-black/75 text-sm font-semibold font-poppins dark:text-gray-200">
                                Up from yesterday
                              </p>
                            </span>
                          </div>
                          <div className="flex flex-col justify-between p-4 rounded-2xl bg-white w-[358px] h-[161px] dark:bg-black">
                            <div className="flex justify-between">
                              <div className="py-2">
                                <h2 className="text-black/75 text-base font-poppins font-medium dark:text-[#757575]">
                                  Paid Payment
                                </h2>
                                <h1 className="font-bold text-black text-3xl font-poppins  pt-2 dark:text-gray-200">
                                  12,112
                                </h1>
                              </div>
                              <div className="bg-[#FFF3D6] h-12 w-12 mt-2 flex items-center justify-center rounded-2xl">
                                <Image
                                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Paid.png`}
                                  width={16}
                                  height={24}
                                  alt="Paid"
                                  className="h-5 w-6"
                                />
                              </div>
                            </div>
                            <span className="text-primary-blue flex items-center gap-1 text-sm font-semibold font-poppins">
                              <FaArrowTrendUp />
                              1.3%
                              <p className="text-black/75 text-sm font-poppins font-semibold dark:text-gray-200">
                                Up from past week
                              </p>
                            </span>
                          </div>
                          <div className="flex flex-col justify-between p-4 rounded-2xl bg-white w-[358px] h-[161px] dark:bg-black">
                            <div className="flex justify-between">
                              <div className="py-2">
                                <h2 className="text-black/75 text-base font-medium font-poppins dark:text-[#757575]">
                                  Pending Payment
                                </h2>
                                <h1 className="font-bold text-3xl font-poppins pt-2 text-black dark:text-gray-200">
                                  9,000
                                </h1>
                              </div>
                              <div className="bg-[#D9F7E8] h-12 w-12 mt-2 flex items-center justify-center rounded-2xl ">
                                <Image
                                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Pending.png`}
                                  width={16}
                                  height={24}
                                  alt="Pending"
                                  className="h-5 w-6"
                                />
                              </div>
                            </div>
                            <span className="text-red-600 flex items-center gap-1 text-sm font-poppins font-semibold">
                              <FaArrowTrendDown />
                              4.3%
                              <p className="text-black/75 text-sm font-poppins font-semibold dark:text-gray-200">
                                Down from yesterday
                              </p>
                            </span>
                          </div>
                        </div>
      <div className="mt-5">
        <EventFilter/>
      </div>
      <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border dark:border-none">
        <table className="min-w-full divide-y  bg-white rounded-xl divide-gray-200 dark:bg-black">
          <thead>
            <tr className="">
              <th className=" whitespace-nowrap font-poppins py-6 px-2 pl-5 bg-white text-left text-sm font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                EVENT ID
              </th>
              <th className="font-poppins pr-4 pl-5  py-6 bg-white text-left text-sm font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                EVENT NAME
              </th>
              <th className="pr-4 pl-5 font-poppins  py-6 bg-white text-left text-sm font-semibold text-black uppercase whitespace-nowrap tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                EVENT NAME
              </th>
              <th className="pr-4 pl-4 lg:pl-4 py-6 font-poppins bg-white text-left text-sm font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                METHOD
              </th>
              <th className="pr-4 pl-4 lg:pl-4  py-6 font-poppins bg-white text-left text-sm font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                AMOUNT
              </th>

              <th className="pr-4 pl-7  py-6 font-poppins bg-white text-left text-sm font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                DATE
              </th>
              <th className="pr-4 pl-5 py-6 font-poppins bg-white text-left text-sm font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                STATUS
              </th>
              <th className=" py-6 font-poppins bg-white text-center text-sm font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                ACTION
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 border-x border-y dark:border-x-0 text-black/70 dark:text-[#757575]">
            {paymentData.map((payment) => (
              <tr key={payment.id}>
                <td className="pl-6 whitespace-nowrap font-poppins px-5 text-sm font-medium ">{payment.eventId}</td>
                <td className="whitespace-nowrap font-poppins px-5  text-sm medium">{payment.activity}</td>
                <td className="pl-7 whitespace-nowrap font-poppins px-5 text-sm medium">{payment.eventname}</td>
                <td className="pl-7 whitespace-nowrap font-poppins px-5 text-sm medium">{payment.method}</td>
                <td className="pl-7 whitespace-nowrap font-poppins px-5 text-sm medium">{payment.amount}</td>
                <td className="whitespace-nowrap font-poppins px-5  text-sm medium">{payment.date}</td>
                <td className="px-5 ">
                  <button className="text-primary-blue font-poppins text-sm font-medium py-1 px-4 rounded bg-[#CCEFED] dark:bg-[#26545270]">
                    {payment.status}
                  </button>
                </td>

                <td className=" py-5 px-5  flex justify-center">
                  <button className="border p-2 rounded-l-lg text-black/75 dark:text-[#757575]">
                    <IoArrowRedoOutline />
                  </button>
                  <Link href={"/superadmin/dashboard/payment-event-details"} className=" border p-2 rounded-r-lg text-black/75 hover:text-blue-700 dark:text-[#757575] dark:hover:text-blue-700">
                    <FiEye />
                  </Link>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
       <div className="flex justify-between items-center mt-5">
                    <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
                    <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
                      <a
                        href="#"
                        className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                      >
                        <span className="sr-only">Next Page</span>
                        <MdOutlineKeyboardArrowLeft />
                      </a>
            
                      <a
                        href="#"
                        className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                      >
                        <span className="sr-only">Next Page</span>
                        <MdOutlineKeyboardArrowRight />
                      </a>
                    </div>
                  </div>
    </div>
  );
};

export default PaymentEvent;
