import express from "express";
import { getUserTravelingDetailsController, listAllBookingsByUserController } from "../controller/booking.js";
import {usersList} from "../controller/auth.js"
import {checkAuth} from '../middleware/auth.js';

const router = express.Router();

router.get('/profile/traveling-details', checkAuth('user_travelling_details'), getUserTravelingDetailsController);
router.get('/my-stays', checkAuth('list_bookings_by_user'), listAllBookingsByUserController);
router.get('/list', checkAuth('get_users_list'), usersList);

export default router;
/**
 * @swagger
 * tags:
 *   name: Users
 *   description: User management endpoints
 */

/**
 * @swagger
 * /users/profile/traveling-details:
 *   get:
 *     tags: [Users]
 *     summary: Get the logged-in user's traveling details grouped by country
 *     description: Retrieves the traveling details of the logged-in user grouped by country.
 *     security:
 *       - bearerAuth: []
 *     produces:
 *       - application/json
 *     responses:
 *       200:
 *         description: Successfully retrieved user's traveling details
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   country:
 *                     type: string
 *                     description: The country where the property is located
 *                   properties:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         name:
 *                           type: string
 *                           description: The name of the property
 *                         address:
 *                           type: string
 *                           description: The address of the property
 *                         city:
 *                           type: string
 *                           description: The city where the property is located
 *                         state:
 *                           type: string
 *                           description: The state where the property is located
 *                         country:
 *                           type: string
 *                           description: The country where the property is located
 *       500:
 *         description: Internal Server Error
 */

/**
 * @swagger
 * /users/my-stays:
 *   get:
 *     tags: [Users]
 *     summary: List all bookings by user
 *     description: Returns a list of bookings for the authenticated user
 *     security:
 *       - bearerAuth: []
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: page
 *         in: query
 *         required: false
 *         type: integer
 *         default: 1
 *       - name: limit
 *         in: query
 *         required: false
 *         type: integer
 *         default: 10
 *       - name: filter
 *         in: query
 *         required: false
 *         schema:
 *           type: object
 *           style: deepObject
 *           explode: true
 *     responses:
 *       200:
 *         description: Bookings Retrieved Successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 bookings:
 *                   type: array
 *                   items:
 *                     $ref: '#/definitions/BookingResponse'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     totalBookings:
 *                       type: integer
 *       500:
 *         description: Internal Server Error
 */
/**
 * @swagger
 * /users/list:
 *   get:
 *     tags: [Users]
 *     summary: Get a list of users
 *     description: Returns a list of all users who are not deleted and have the role of "user". Each user object includes the user's ID and name.
 *     security:
 *       - bearerAuth: []
 *     produces:
 *       - application/json
 *     responses:
 *       200:
 *         description: Successfully retrieved the list of users
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   _id:
 *                     type: string
 *                     description: The unique identifier of the user
 *                   name:
 *                     type: string
 *                     description: The name of the user
 *       500:
 *         description: Internal Server Error
 */
