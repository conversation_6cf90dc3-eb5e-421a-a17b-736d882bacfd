import { ChevronRight, ChevronLeft } from "lucide-react";

const Pagination = ({ 
  currentPage, 
  totalPages, 
  totalItems, 
  itemsPerPage = 12,
  itemsPerPageOptions = [12, 20, 40], 
  onPageChange,
  onItemsPerPageChange 
}) => {

  const renderPageNumbers = () => {
    let pages = [];
    if (totalPages <= 5) {
      // Show all pages if total pages <= 5
      for (let i = 1; i <= totalPages; i++) pages.push(i);
    } else {
      if (currentPage <= 3) {
        // Near the start - show first 3 pages, ellipsis, and last page
        pages = [1, 2, 3, 4, "...", totalPages];
      } else if (currentPage >= totalPages - 2) {
        // Near the end
        pages = [1, "...", totalPages - 2, totalPages - 1, totalPages];
      } else {
        // Middle - show current page and one page on each side
        pages = [
          1,
          "...",
          currentPage - 1,
          currentPage,
          currentPage + 1,
          currentPage + 2,
          "...",
          totalPages
        ];
      }
    }

    return pages.map((page, index) =>
      page === "..." ? (
        <span key={`ellipsis-${index}`} className="px-2">...</span>
      ) : (
        <button
          key={page}
          onClick={() => onPageChange(page)}
          className={`border rounded min-w-[30px] min-h-[30px] px-1 mx-1 ${
            currentPage === page ? "bg-primary-blue text-white" : "border-gray-300"
          }`}
        >
          {page}
        </button>
      )
    );
  };

  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  return (
    <div className="flex items-center justify-between mt-5 flex-col md:flex-row">
      {/* Pagination Controls */}
      <div className="flex items-center text-sm">
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="border min-w-[30px] min-h-[30px] px-1 mx-1 rounded bg-[#EEEEEE] disabled:opacity-50"
        >
          <ChevronLeft size={18} />
        </button>
        {renderPageNumbers()}
        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="border min-w-[30px] min-h-[30px] px-1 mx-1 rounded bg-[#EEEEEE] disabled:opacity-50"
        >
          <ChevronRight size={18} />
        </button>
      </div>

      {/* Items Per Page */}
      <div className="flex items-center text-sm my-4 md:my-0 ml-1 md:ml-0">
        <span className="mr-2">Items {startItem} - {endItem} of {totalItems}</span>
        <select
          value={itemsPerPage}
          onChange={(e) => onItemsPerPageChange(Number(e.target.value))}
          className="border rounded px-2 py-1"
        >
          {itemsPerPageOptions.map((option) => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </select>
        <span className="ml-2">per page</span>
      </div>
    </div>
  );
};

export default Pagination;
