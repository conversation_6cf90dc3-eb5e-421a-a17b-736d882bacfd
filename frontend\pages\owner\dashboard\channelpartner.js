import React, { useEffect, useState, Fragment } from "react";
import Image from "next/image";
import Link from "next/link";
import { Search } from "lucide-react";
import { GoQuestion } from "react-icons/go";
import FormControlLabel from "@mui/material/FormControlLabel";
import Checkbox from "@mui/material/Checkbox";
import { Typography } from "@mui/material";
import dynamic from "next/dynamic";
import { FaCheck } from "react-icons/fa";
import { BsFillQuestionCircleFill } from "react-icons/bs";
import { IoClose } from "react-icons/io5";
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
  Transition,
} from "@headlessui/react";
import { channelManagersListApi } from "@/services/ownerflowServices";
// import Breadcrumb from "@/components/breadcrumb/breadcrumb";
import ContractAgreement from "@/components/ownerFlow/dashboard/contractAgreement";
import AddChannel from "@/components/ownerFlow/dashboard/addChannel";
import { XMarkIcon } from "@heroicons/react/24/outline";
import toast from "react-hot-toast";
import Head from "next/head";

const ChannelPartnerNote = dynamic(
  () => import("../../../components/model/channelPartnerNote"),
  {
    ssr: false,
  }
);

const Channelpartner = () => {
  // PartnerNote Modal
  const [openPartnerNote, setOpenPartnerNote] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedTerm, setSelectedTerm] = useState("");
  const [openAddchannel, setOpenAddchannel] = useState(false);
  const closechannelModal = () => setOpenAddchannel(false);

  const fetchSuggestions = async () => {
    if (!searchTerm.trim()) {
      setSuggestions([]);
      return;
    }

    setLoading(true);
    try {
      const response = await channelManagersListApi(searchTerm);

      if (response.status === 200) {
        setSuggestions(response?.data?.data || []);
      } else {
        toast.error(response?.data?.message || "Failed to fetch suggestions");
      }
    } catch (error) {
      console.error("Error fetching suggestions:", error);
      toast.error("Something went wrong while fetching suggestions.");
    } finally {
      setLoading(false);
    }
  };
  const handleOpenPartnerNote = () => {
    setOpenPartnerNote(true);
  };
  const handleClosePartnerNote = () => {
    setOpenPartnerNote(false);
  };

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      if (searchTerm !== selectedTerm) {
        fetchSuggestions();
      }
    }, 300);

    return () => clearTimeout(delayDebounceFn); // Cleanup timeout
  }, [searchTerm]);

  const handleSuggestionClick = (suggestion) => {
    console.log("Selected Suggestion:", suggestion);
    setSearchTerm(suggestion);
    setSelectedTerm(suggestion);
    setSuggestions([]);
  };

  const [isModalOpen, setIsModalOpen] = useState(false);

  const [activeSection, setActiveSection] = useState("default");

  // eslint-disable-next-line no-unused-vars
  const [isFadingOut, setIsFadingOut] = useState(false);

  const handleModalClick = () => {
    setIsFadingOut(true); // Start fade-out effect
    setTimeout(() => {
      setIsModalOpen(false);
      setActiveSection("contractAgreement");
      setIsFadingOut(false); // Reset fade-out state
    }, 1000); // Matches fade animation duration
  };

  // const [selected, setSelected] = useState({
  //   rates: true,
  //   rooms: true,
  //   guest: true,
  //   reporting: true,
  //   terms: true,
  // });

  // const checkboxes = [
  //   { key: "rates", label: "Rates And Availability" },
  //   { key: "rooms", label: "Rooms & Pricing" },
  //   { key: "guest", label: "Guest Reviews" },
  //   { key: "reporting", label: "Reporting" },
  //   {
  //     key: "terms",
  //     label: (
  //       <>
  //         <p className='text-[#6D6D6D] sm:text-base text-sm'>
  //           I have read and agreed to all{" "}
  //           <span className='text-[#40E0D0] underline underline-offset-4 uppercase'>
  //             terms and conditions
  //           </span>
  //         </p>
  //       </>
  //     ),
  //   },
  // ];

  // const toggleCheckbox = (key) => {
  //   setSelected((prev) => ({
  //     ...prev,
  //     [key]: !prev[key],
  //   }));
  // };

  return (
    <>
      {/* <Breadcrumb
        items={[
          { label: "Dashbord", href: "/owner/dashboard" },
          { label: "Channel Partner" },
        ]}
      /> */}

      <Head>
        <title>Channel Partner Management | Mixdorm</title>
      </Head>
      {activeSection === "default" && (
        <div>
          <h1 className='text-xl font-medium mb-5'>Channel Integration</h1>
          <div className='w-full bg-white border border-slate-200 border-1 my-5 sm:px-8 px-4 pt-6 sm:pb-20 pb-10 rounded-lg'>
            <div className='flex sm:flex-row flex-col-reverse flex-wrap sm:items-start items-center gap-3'>
              <div className='relative flex-1 w-full'>
                <button
                  type='button'
                  className='absolute text-black top-[10px] left-3'
                >
                  <Search size={24} color='#858D9D' />
                </button>
                <input
                  type='text'
                  className='px-10 py-3 rounded rounded-b-none border-b-0 border-slate-200 w-full outline-none border text-sm font-light bg-[#EEEEEE]'
                  placeholder='Search'
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                {searchTerm && suggestions.length > 0 && (
                  <div className='pt-4 absolute left-0 right-0 z-5 bg-white max-h-60 overflow-auto border border-t-0 border-slate-200'>
                    <p className='pl-10 font-medium sm:text-base text-sm'>
                      Suggestion
                    </p>
                    {loading ? (
                      <div className='py-1 pl-10  font-medium text-base text-[#00000080]'>
                        Loading...
                      </div>
                    ) : suggestions.length > 0 ? (
                      suggestions.map((suggestion, index) => (
                        <div
                          key={index}
                          onClick={() =>
                            handleSuggestionClick(suggestion?.name)
                          }
                          className='py-1 pl-10 font-medium sm:text-base text-sm hover:bg-gray-100 cursor-pointer text-[#00000080]'
                        >
                          {suggestion?.name}
                        </div>
                      ))
                    ) : (
                      <div className='py-1 pl-10 font-medium sm:text-base text-sm text-[#00000080]'>
                        No suggestions found
                      </div>
                    )}
                  </div>
                )}

                {/* Channel parntner list */}
                {/* <div className='mt-5 pl-10'>
                  <div className='flex justify-between sm:mb-7 mb-3'>
                    <div>
                      <p className='sm:text-lg text-sm mb-2 font-bold'>
                        E Zee Absolue
                      </p>
                      <p className='sm:text-base text-sm font-normal'>
                        100% Connection Quality
                      </p>
                    </div>
                    <span className='sm:text-base text-sm text-[#40E0D0] underline font-bold underline-offset-4'>
                      More Details
                    </span>
                  </div>
                  <p className='sm:text-lg text-sm mb-2 font-bold'>
                    Available connection types
                  </p>
                  <p className='sm:text-base text-sm font-normal sm:mb-12 mb-5'>
                    Mixdorm offer the following connection type, which can be
                    grouped or mandatory depending on the provider, By selecting
                    a connection type, you allow this provider to perform the
                    corresponding actions on your behalf.
                  </p>

                  <div className='space-y-4'>
                    {checkboxes.map(({ key, label }) => (
                      <label
                        key={key}
                        className='flex items-center cursor-pointer'
                      >
                        <input
                          type='checkbox'
                          checked={selected[key]}
                          onChange={() => toggleCheckbox(key)}
                          className='hidden'
                        />
                        <div
                          className={`sm:w-6 sm:h-6 min-w-4 min-h-4 w-5 h-5 flex items-center justify-center border rounded transition-all ${
                            selected[key] ? "bg-[#40E0D0]" : "border-[#979797]"
                          }`}
                        >
                          {selected[key] && (
                            <span className='text-black sm:text-lg text-md'>
                              ✔
                            </span>
                          )}
                        </div>
                        <span className='ml-2 sm:text-base text-sm font-medium text-black'>
                          {label}
                        </span>
                      </label>
                    ))}
                  </div>
                  <button
                    className='bg-[#40E0D0] hover:bg-transparent text-black hover:text-[#40E0D0] border-2 font-normal py-2 px-4 mt-5 sm:mt-10 border-[#40E0D0] rounded-lg text-sm sm:w-[152px] w-full'
                    onClick={handleOpenPartnerNote}
                  >
                    Connect
                  </button>
                </div> */}
              </div>

              <div className='flex gap-3'>
                <Link
                  href='#'
                  className='bg-[#40E0D0] text-black font-medium xs:px-4 px-2 py-3 xs:text-sm text-xs rounded-lg'
                  prefetch={false}
                >
                  Channel Partner List
                </Link>
                <button
                  className='bg-[#40E0D0] text-black font-medium xs:px-4 px-2 py-3 xs:text-sm text-xs rounded-lg'
                  onClick={() => setOpenAddchannel(true)}
                >
                  Add Channel Partners
                </button>
              </div>
            </div>

            {/* Blank Channel Partner */}
            <div className='text-center sm:py-20 py-10'>
              <h2 className='md:text-2xl text-lg sm:mb-12 mb-6 font-bold text-black'>
                More then 200+ Channel Partners <br /> List of channels that are
                currently integrated to MixDorm API.
              </h2>
              <Link
                href='#'
                className='bg-black text-white px-4 py-3 text-sm text-medium rounded-lg inline-flex gap-2 items-center h-10'
                onClick={() => setIsModalOpen(true)}
                prefetch={false}
              >
                <GoQuestion size={20} /> Help
              </Link>
            </div>

            {/* Channel partner List */}
            <div className='pt-8 hidden'>
              <div className='max-w-[860px] px-5'>
                <div className='flex items-start mb-8'>
                  <h6 className='font-semibold text-black text-lg'>
                    E Zee Absolue{" "}
                    <span className='block font-normal text-[#888888] mt-2  text-base'>
                      100% Connection Quality
                    </span>
                  </h6>
                  <Link
                    href='#'
                    className='ml-auto font-semibold text-[#1570EF] text-base underline'
                    prefetch={false}
                  >
                    More Details
                  </Link>
                </div>

                <h6 className='font-semibold text-black text-lg mb-2'>
                  Available connection types
                </h6>
                <p className='block font-normal text-[#888888] mt-2 text-base'>
                  Mixdorm offer the following connection type, which can be
                  grouped or mandatory depending on the provider, By selecting a
                  connection type, you allow this provider to perform the
                  corresponding actions on your behalf.
                </p>

                <ul className='my-6'>
                  <li>
                    <FormControlLabel
                      control={<Checkbox defaultChecked />}
                      sx={{
                        "& .Mui-checked": {
                          color: "#40E0D0", // Set the checked color
                        },
                      }}
                      label='Rates and availibility'
                    />
                  </li>
                  <li>
                    <FormControlLabel
                      control={<Checkbox defaultChecked />}
                      sx={{
                        "& .Mui-checked": {
                          color: "#40E0D0", // Set the checked color
                        },
                      }}
                      label='Reservations'
                    />
                  </li>
                  <li>
                    <FormControlLabel
                      control={<Checkbox defaultChecked />}
                      sx={{
                        "& .Mui-checked": {
                          color: "#40E0D0", // Set the checked color
                        },
                      }}
                      label='Guest Reviews'
                    />
                  </li>
                  <li>
                    <FormControlLabel
                      control={<Checkbox defaultChecked />}
                      sx={{
                        "& .Mui-checked": {
                          color: "#40E0D0", // Set the checked color
                        },
                      }}
                      label='Reporting'
                    />
                  </li>
                  <li>
                    <FormControlLabel
                      control={<Checkbox defaultChecked />}
                      sx={{
                        "& .Mui-checked": {
                          color: "#40E0D0", // Set the checked color
                        },
                      }}
                      label={
                        <Typography>
                          I have read and agreed to all{" "}
                          <Link
                            href='#'
                            style={{
                              color: "#40E0D0",
                              textDecoration: "underline",
                              textTransform: "capitalize",
                            }}
                            prefetch={false}
                          >
                            terms and conditions
                          </Link>
                        </Typography>
                      }
                    />
                  </li>
                </ul>
                <button
                  type='button'
                  className='bg-blue-500 text-white px-4 py-3 text-sm rounded'
                  onClick={handleOpenPartnerNote}
                >
                  Connect
                </button>
              </div>
            </div>

            {/* Agent Authorisation */}
            <div className='pt-6 max-w-[900px] mx-auto hidden'>
              <h6 className='font-semibold text-black text-lg mb-5'>
                Agent Authorisation
              </h6>
              <div className='flex items-start bg-[#E0EFD8] rounded-xl p-3 mb-6'>
                <span className='w-7 mt-1'>
                  <FaCheck />
                </span>
                <p className='text-[#3C5E46] text-base flex-1 mb-0'>
                  Please specify connection details in the form Bellow. You can
                  find these details in channel extranet or get them your
                  mixdorm profile manager
                </p>
              </div>

              <form>
                <label htmlFor='' className='block mb-1 text-sm text-[#383E49]'>
                  Hostel Id key for the channel Mixdorm
                </label>
                <input
                  type='text'
                  className='p-3 rounded-lg w-full outline-none border text-sm font-light mb-6'
                  placeholder='Exl.2145236'
                />

                <button
                  type='button'
                  className='bg-blue-500 text-white px-6 py-3 text-sm rounded-lg'
                >
                  Continue
                </button>
              </form>
            </div>

            {/* Connection Management */}
            <div className='pt-6 max-w-[900px] mx-auto hidden'>
              <h6 className='font-semibold text-black text-lg mb-4'>
                Connection Management
              </h6>
              <ul className='flex flex-col gap-4 mb-4'>
                <li>
                  <button
                    type='button'
                    className='bg-[#E8F1FE] rounded-lg text-[#1570EF] py-3 px-5 flex items-center text-base w-full'
                  >
                    <FaCheck className='mr-2' /> Connection confirmation
                    <BsFillQuestionCircleFill className='text-[#888888] ml-auto' />
                  </button>
                </li>
                <li>
                  <button
                    type='button'
                    className='bg-[#E8F1FE] rounded-lg text-[#1570EF] py-3 px-5 flex items-center text-base w-full'
                  >
                    <FaCheck className='mr-2' /> Connection confirmation
                    <BsFillQuestionCircleFill className='text-[#888888] ml-auto' />
                  </button>
                </li>
                <li>
                  <button
                    type='button'
                    className='bg-[#FFEBE6] rounded-lg text-[#FF2E00] py-3 px-5 flex items-center text-base w-full'
                  >
                    <IoClose className='mr-2 text-xl' /> Connection confirmation
                    <BsFillQuestionCircleFill className='text-[#888888] ml-auto' />
                  </button>
                </li>
              </ul>
              <button
                type='button'
                className='bg-blue-500 text-white px-6 py-3 text-sm rounded-lg'
              >
                Disconnect request
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Section 2 with a Button to Go to Section 3 */}
      {activeSection === "contractAgreement" && (
        <>
          <ContractAgreement />
        </>
      )}

      {isModalOpen && (
        <Dialog
          open={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          className='relative z-50'
        >
          <DialogBackdrop
            transition
            className='fixed inset-0 bg-[#000000B2] transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in'
          />

          <div className='fixed inset-0 z-10 w-screen overflow-y-auto'>
            <div className='flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0'>
              <DialogPanel
                transition
                className='relative transform overflow-hidden rounded-2xl bg-white text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-full sm:max-w-3xl max-w-full w-full data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95'
              >
                <button
                  onClick={() => setIsModalOpen(false)}
                  className='absolute top-4 right-4 text-gray-500 hover:text-gray-900'
                >
                  <XMarkIcon className='h-6 w-6 text-black font-bold hover:text-slate-400	' />
                </button>

                <div className='bg-white sm:px-14 sm:pb-7 pt-5 p-3 pb-3'>
                  <div className='mt-4'>
                    <Image
                      className='mx-auto mb-7'
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/channel-agreement.svg`}
                      width={241.35}
                      height={146}
                    ></Image>
                    <div className='mt-3 sm:mt-0 sm:text-left'>
                      <DialogTitle
                        as='h3'
                        className='font-semibold text-black sm:text-2xl text-lg'
                      >
                        Contract Agreement
                      </DialogTitle>
                      <div className='mt-3.5'>
                        <p
                          className='text-sm font-normal mb-6'
                          style={{ color: "#888888" }}
                        >
                          Welcome to MixDorm! Before we move forward with your
                          property integration, we need you to review and accept
                          our partnership agreement.
                        </p>
                        <p className='text-sm font-bold mb-1'>
                          Key Highlights:
                        </p>
                        <div className='pl-2.5'>
                          <FormControlLabel
                            className='block'
                            control={
                              <Checkbox
                                className='p-0'
                                sx={{
                                  "&.Mui-checked": {
                                    color: "#40E0D0", // Set the checked color
                                  },
                                }}
                              />
                            }
                            label={
                              <span className='text-sm text-[#888888]'>
                                <strong className='text-black'>
                                  Standard Commission:
                                </strong>{" "}
                                15% per booking.
                              </span>
                            }
                          />
                          <FormControlLabel
                            className='block'
                            control={
                              <Checkbox
                                className='p-0'
                                sx={{
                                  "&.Mui-checked": {
                                    color: "#40E0D0", // Set the checked color
                                  },
                                }}
                              />
                            }
                            label={
                              <span className='text-sm text-[#888888]'>
                                <strong className='text-black'>
                                  Exclusive Top Featured Property:
                                </strong>{" "}
                                20% commission, boosting your visibility to
                                attract more bookings. By proceeding, you agree
                                to provide accurate property details, honor all
                                bookings made through MixDorm, and adhere to our
                                terms of service.
                              </span>
                            }
                          />
                        </div>
                        <span className='text-sm text-[#888888]'>
                          <strong className='text-black'>
                            Integration Requirements:
                          </strong>{" "}
                          You must provide valid API credentials for your
                          selected channel partner.
                        </span>{" "}
                        <br></br>
                        <span className='text-sm text-[#888888]'>
                          <strong className='text-black'>
                            Termination Clause:
                          </strong>{" "}
                          Either party may terminate this agreement with 30
                          days’ notice.
                        </span>
                        <FormControlLabel
                          className='flex items-center gap-2 text-[#6D6D6D] text-base pl-2.5 mt-2'
                          control={
                            <Checkbox
                              className='p-0'
                              sx={{
                                "&.Mui-checked": {
                                  color: "#40E0D0", // Set the checked color
                                },
                              }}
                            />
                          }
                          label='I agree to the terms and conditions'
                        />
                      </div>
                      <div className='flex justify-center mt-7'>
                        <button
                          className='h-10 mx-auto px-6 rounded-lg text-white text-center bg-black text-sm font-semibold'
                          onClick={handleModalClick}
                        >
                          Proceed to sign
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </DialogPanel>
            </div>
          </div>
        </Dialog>
      )}

      {/* Add Channel partner Modal */}
      <Transition show={openAddchannel} as={Fragment}>
        <Dialog as='div' className='relative z-50' onClose={closechannelModal}>
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter='ease-out duration-300'
            enterFrom='opacity-0'
            enterTo='opacity-100'
            leave='ease-in duration-200'
            leaveFrom='opacity-100'
            leaveTo='opacity-0'
          >
            <div className='fixed inset-0 bg-black/50' />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className='fixed inset-0 overflow-hidden'>
            <div className='absolute inset-0 flex justify-end sm:top-20 top-16'>
              <Transition.Child
                as={Fragment}
                enter='transform transition ease-out duration-300'
                enterFrom='translate-x-full'
                enterTo='translate-x-0'
                leave='transform transition ease-in duration-200'
                leaveFrom='translate-x-0'
                leaveTo='translate-x-full'
              >
                <Dialog.Panel className='md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll'>
                  {/* Modal Header */}
                  <div className='sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50'>
                    <h2 className='page-title'>Add Channel Partner</h2>
                    <button
                      onClick={closechannelModal}
                      className='text-gray-500 hover:text-gray-800'
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className='sm:px-6 px-4'>
                    <AddChannel
                      closechannelModal={closechannelModal}
                    ></AddChannel>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      <ChannelPartnerNote
        close={handleClosePartnerNote}
        open={openPartnerNote}
      />
    </>
  );
};

export default Channelpartner;
