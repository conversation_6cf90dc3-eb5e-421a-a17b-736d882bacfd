import ARI from '../models/ARI.js';
import property from '../models/properties.js';
import roomRatesModel from '../models/rates.js';
import roomModel from '../models/room.js';
import currencyRates from '../utills/currencyRate.js';
import Response from '../utills/response.js';
import axios from 'axios'
import RatePlan from '../models/roomRatePlans.js';
import mongoose from 'mongoose';
import roomInstancesModel from '../models/subRooms.js';
import { getNextRatePlanId, getNextRoomId } from '../services/room.js';
import hostelLoginModel from "../models/hostelsLogin.js"
// Controller to update settings by key
export const updateRatePlanForRoom = async (req, res) => {
    try {
        console.log("Assigning otaRateIds...");

        // Get the latest otaRateId from existing rate plans
        const lastRatePlan = await roomRatesModel.findOne({ otaRateId: { $regex: /^rate_\d+$/ } })
            .sort({ otaRateId: -1 })
            .lean();

        let rateCounter = 1001;
        if (lastRatePlan?.otaRateId) {
            const lastId = parseInt(lastRatePlan.otaRateId.split("_")[1], 10);
            if (!isNaN(lastId)) {
                rateCounter = lastId + 1;
            }
        }

        const rooms = await roomModel.find({}).sort({ _id: 1 }).lean();
        console.log(`Total rooms found: ${rooms.length}`);

        let createdCount = 0;

        for (let i = 0; i < rooms.length; i++) {
            const room = rooms[i];
            const existing = await roomRatesModel.findOne({ room: room._id });

            if (existing) {
                console.log(`${i + 1}. Skipping room "${room.name}" – already has rate plan`);
                continue;
            }

            const otaRateId = `rate_${rateCounter++}`;

            const newRatePlan = new roomRatesModel({
                name: `${room.name || "Room"} Standard Rate`,
                otaRateId,
                room: room._id,
                rate: room?.rate?.averagePrice?.value || 100,
                currency: room.currency || "USD",
                restrictions: {
                    closed: false,
                    minStay: 0,
                    maxStay: 365,
                },
                createdAt: new Date(),
            });

            await newRatePlan.save();
            createdCount++;

            console.log(`${i + 1}. Rate plan created for room: "${room.name}"`);
        }

        return res.status(200).json({
            success: true,
            message: `${createdCount} new rate plans created successfully.`
        });

    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

export const updateBaseRate = async (req, res) => {
    try {
        console.log("rate converstion script")
        const ratesMap = currencyRates.rates;

        const idrToEur = 1 / ratesMap["IDR"];
        const inrToEur = 1 / ratesMap["INR"];

        if (!idrToEur || !inrToEur) {
            return res.status(400).json({
                success: false,
                message: "Conversion rates for IDR or INR not found in currencyRates",
            });
        }

        // Fetch ARIs with currency IDR and INR
        const roomRates = await ARI.find({ currency: { $in: ["IDR", "INR"] } });

        let updatedCount = 0;

        for (const room of roomRates) {
            let conversionRate = 1;
            if (room.currency === "IDR") {
                conversionRate = idrToEur;
            } else if (room.currency === "INR") {
                conversionRate = inrToEur;
            }

            const baseRate = parseFloat((room.rate * conversionRate).toFixed(2));

            await ARI.updateOne(
                { _id: room._id },
                {
                    $set: {
                        baseRate,
                    },
                }
            );

            updatedCount++;
        }

        return res.status(200).json({
            success: true,
            message: `${updatedCount} room rates converted and updated to baseRate (EUR).`,
        });
    } catch (error) {
        console.error("Error updating baseRate:", error);
        return res.status(500).json({
            success: false,
            message: "Internal server error",
        });
    }
};

export const downloadNewData = async (req, res) => {
    const { hostelworldId, date } = req.query;

    try {
        const propertyUrl = `https://prod.apigee.hostelworld.com/legacy-msapi-service/property/${hostelworldId}?show-rooms=1`;
        const propertyResponse = await axios.get(propertyUrl);
        const propertyData = propertyResponse.data;

        const existingProperty = await property.findOne({ name: propertyData.name });
        if (existingProperty) {
            return res.status(409).json({
                success: false,
                message: `Property "${propertyData.name}" already exists`,
                mixdormOTAId: existingProperty.otaId
            });
        }

        const lastProperty = await property.findOne({ otaId: { $exists: true } })
            .sort({ otaId: -1 }).lean();
        const nextOtaId = lastProperty?.otaId ? lastProperty.otaId + 1 : 1;

        const availabilityUrl = `https://prod.apigee.hostelworld.com/legacy-hwapi-service/2.2/properties/${hostelworldId}/availability/?guests=1&num-nights=1&date-start=${date}&show-rate-restrictions=true&application=web&reservation-id=external`;
        console.log("availabilityUrl", availabilityUrl)

        const availabilityResponse = await axios.get(availabilityUrl);
        const roomRateData = availabilityResponse.data;

        // Upload property images to S3
        const uploadedImages = [];

        if (Array.isArray(propertyData.images)) {
            for (let img of propertyData.images) {
                if (!img?.prefix || !img?.suffix) continue;

                const imageUrl = `https://${img.prefix}${img.suffix}`;
                const key = `property-Images/${hostelworldId}_${Date.now()}_${Math.random().toString(36).slice(2)}.jpg`;
                const uploadResult = await uploadToS3(imageUrl, key);
                console.log("upload done")
                console.log("uploadedImages", uploadedImages)
                if (uploadResult) uploadedImages.push(uploadResult);
            }
        }

        const newProperty = new property({
            name: propertyData.name,
            nameEN: propertyData.nameEN,
            type: propertyData.type,
            isActive: true,
            isNew: true,
            isPropertyVerified: true,
            isDeleted: false,
            isOTA: true,
            aboutUs: propertyData.description,
            houseRules: {
                notes: propertyData.houseRules?.notes || "",
                cancellationPolicy: propertyData.houseRules?.cancellationPolicy || {},
                checkin: propertyData.houseRules?.checkin || {},
                checkout: propertyData.houseRules?.latestCheckout || ""
            },
            location: {
                type: "Point",
                coordinates: [
                    parseFloat(propertyData.location.longitude),
                    parseFloat(propertyData.location.latitude)
                ]
            },
            address: {
                lineOne: propertyData.location.address1,
                lineTwo: propertyData.location.address2 || "",
                city: propertyData.location.city?.name || "",
                state: propertyData.location.region?.name || "",
                country: propertyData.location.country?.name || ""
            },
            otaId: nextOtaId,
            images: uploadedImages,
            country: propertyData.location.country?.name || "",
            distanceFromCityCenter: propertyData?.location?.distance?.kilometers
        });

        const savedProperty = await newProperty.save();

        const roomMap = {
            ...(roomRateData.rooms?.dorms || {}),
            ...(roomRateData.rooms?.privates || {})
        };

        const insertedRooms = [];
        for (const [roomId, room] of Object.entries(roomMap)) {
            const roomName = room.name?.trim();
            if (!roomName) continue;

            const existingRoom = await roomModel.findOne({
                name: new RegExp(`^${roomName}$`, 'i'),
                property: savedProperty._id
            });

            if (existingRoom) continue;

            const firstRatePlan = room.ratePlans?.[0] || {};
            const priceValue = parseFloat(firstRatePlan.price?.amount || room.lowestPricePerNight?.value || 0);
            const currency = firstRatePlan.currency || room.lowestPricePerNight?.currency || "INR";
            const type = roomName.toLowerCase().includes("dorm") ? "dorm" : "private";

            const newRoom = new roomModel({
                property: savedProperty._id,
                name: roomName,
                description: room.description || '',
                roomId: await getNextRoomId(),
                currency,
                availableUnits: room.totalBedsAvailable || 0,
                units: room.totalBedsAvailable || 0,
                type,
                dormitory: type === "dorm",
                privateRoom: type === "private",
                ensuite: room.ensuite === '1' || room.ensuite === 1,
                capacity: parseInt(room.capacity) || 1,
                grade: room.grade || '',
                rate: {
                    weekdayRate: {
                        value: priceValue,
                        updatedAt: new Date()
                    },
                    weekendRate: {
                        value: priceValue,
                        updatedAt: new Date()
                    },
                    averagePrice: {
                        value: priceValue,
                        updatedAt: new Date()
                    }
                },
                images: (room.images || []).map(img => ({
                    url: img.url || '',
                    title: img.title || ''
                }))
            });

            await newRoom.save();
            insertedRooms.push(newRoom);
        }

        const updatedHostelOwner = await hostelLoginModel.findOneAndUpdate(
            { otaId: nextOtaId, role: "hostel_owner" },
            {
                $set: {
                    property: savedProperty._id,
                    address: `${savedProperty.address.lineOne || ''}, ${savedProperty.address.lineTwo || ''}, ${savedProperty.address.state || ''}, ${savedProperty.address.city || ''}, ${savedProperty.address.country || ''}`,
                    password: "$2b$10$.nMxhwJX7ZiexiURTuZbo.Sm6G5xDWqlyIdAfOs5xeajXtwRojhPS",
                    isEmailVerified: true,
                    isDeleted: false
                },
                $setOnInsert: {
                    name: savedProperty.name,
                    role: "hostel_owner",
                    email: "",
                    contact: "",
                    profileImage: {
                        filename: "",
                        path: "",
                        objectURL: ""
                    },
                    privacy: {
                        email: false,
                        phone: false
                    }
                }
            },
            {
                new: true,
                upsert: true
            }
        );

        res.status(201).json({
            success: true,
            message: "Property and room data saved from Hostelworld",
            propertyId: savedProperty.otaId
        });

    } catch (err) {
        console.error("Error inserting data from Hostelworld:", err.message);
        res.status(500).json({
            success: false,
            message: "Error fetching or saving property/room data",
            error: err.message
        });
    }
};

export const syncEzeeRoomsAndRates = async (req, res) => {
    try {
        const { otaId, name, ezeeHotelCode, ezeeAuthCode } = req.body;

        // 1. Get Property by otaId
        const propertyData = await property.findOne({ otaId });
        if (!propertyData) {
            return res.status(404).json({ success: false, message: 'Property not found' });
        }

        // 2. Fetch Room Types

        let roomTypesResponse = await axios.post(
            'https://live.ipms247.com/pmsinterface/pms_connectivity.php',
            {
                RES_Request: {
                    Request_Type: "RoomInfo",
                    NeedPhysicalRooms: 1,
                    Authentication: {
                        HotelCode: ezeeHotelCode, // or use ezeeHotelCode variable
                        AuthCode: ezeeAuthCode, // or use ezeeAuthCode variable
                    }
                }
            },
            {
                headers: {
                    'Content-Type': 'application/json',
                    'Cookie': 'AWSALB=k2/cborVIHoM+hNEzjsOlvzy9qSwYts1X/2ksPBy3iRHIle8AWsIZ44ObRkbzCaROstghYbHlAayuSYQUiPhrp5Ei1ovx6SD25HPk5n0VjrgOvV0JSCY4us5Qbqb; AWSALBCORS=k2/cborVIHoM+hNEzjsOlvzy9qSwYts1X/2ksPBy3iRHIle8AWsIZ44ObRkbzCaROstghYbHlAayuSYQUiPhrp5Ei1ovx6SD25HPk5n0VjrgOvV0JSCY4us5Qbqb'
                }
            }
        );

        roomTypesResponse = roomTypesResponse.data;
        console.log("roomTypesResponse", roomTypesResponse)

        // 1. Delete existing rooms of this property
        await roomModel.deleteMany({ property: propertyData._id });
        console.log(`Deleted existing rooms for property ${propertyData._id}`);

        const roomTypes = roomTypesResponse.RoomInfo?.RoomTypes.RoomType || [];
        console.log("roomTypes", roomTypes.length)
        // 3. Upsert each Room and its SubRooms
        for (const roomType of roomTypes) {
            const { ID, Name, Rooms } = roomType;
            console.log(ID)
            console.log(Name)
            const roomDoc = await roomModel.findOneAndUpdate(
                { ezeeId: ID, property: propertyData._id },
                {
                    name: Name,
                    property: propertyData._id,
                    ezeeId: ID,
                    roomId: await getNextRoomId()

                },
                { new: true, upsert: true }
            );
            if (Rooms) {
                for (const subRoom of Rooms) {
                    await roomInstancesModel.findOneAndUpdate(
                        { ezeeId: subRoom.RoomID, property: propertyData._id },
                        {
                            name: subRoom.RoomName,
                            property: property._id,
                            room: roomDoc._id,
                            ezeeId: subRoom.RoomID,

                        },
                        { new: true, upsert: true }
                    );
                }
            }
        }

        console.log("rooms added")
        // // 4. Fetch Rate Plans
        // const ratePlansResponse = await axios.post('https://live.ipms247.com/SyncService.svc/GetRatePlans', {
        //     HotelCode: ezeeHotelCode,
        //     AuthCode: ezeeAuthCode,
        // });

        // Rate Types Add
        const rateTypeGroups = roomTypesResponse.RoomInfo?.RateTypes;
        const rateTypes = rateTypeGroups ? Object.values(rateTypeGroups).flat() : [];
        console.log("rateTypes", rateTypes);
        for (const ratePlan of rateTypes) {
            await RatePlan.findOneAndUpdate(
                { ezeeId: ratePlan.ID, property: propertyData._id },
                {
                    name: ratePlan.Name,
                    ezeeId: ratePlan.ID,
                    property: propertyData._id,
                    otaRateId: await getNextRatePlanId()
                },
                { new: true, upsert: true }
            );
        }


        const ratePlans = Object.values(roomTypesResponse.RoomInfo?.RatePlans || {}).flat();
        console.log("Rate plan added")
        const rooms = await roomModel.find({ property: propertyData._id });
        for (const room of rooms) {
            for (const rateType of rateTypes) {
                // Try to find an existing RatePlan
                const existing = await RatePlan.findOne({
                    ezeeId: rateType.ID,
                    property: propertyData._id,
                    room: room._id
                });

                if (existing) {
                    // Update existing rate plan
                    existing.name = rateType.Name;
                    await existing.save();
                    console.log(`🔁 Updated RatePlan "${rateType.Name}" for room "${room.name}"`);
                } else {
                    // Create new rate plan with next otaRateId
                    const nextId = await getNextRatePlanId();
                    await RatePlan.create({
                        name: rateType.Name,
                        ezeeId: rateType.ID,
                        property: propertyData._id,
                        room: room._id,
                        otaRateId: nextId
                    });
                    console.log(`➕ Added RatePlan "${rateType.Name}" to room "${room.name}"`);
                }
            }
        }


        return res.status(200).json({
            success: true,
            message: 'Rooms, SubRooms, and RatePlans synced successfully',
            totalRooms: roomTypes.length,
            totalRatePlans: ratePlans.length
        });

    } catch (err) {
        console.error('[Sync Error]', err);
        res.status(500).json({ success: false, error: err.message });
    }
};

export const assignRoomsId = async (req, res) => {
    try {
        const rooms = await roomModel.find({}).sort({ _id: 1 })
        console.log(`🔍 Found ${rooms.length} rooms`);
        let roomIdCounter = 100000;

        for (const room of rooms) {
            const currentType = room?.type;
            const capacity = room?.capacity || 0;

            if (!currentType) continue;

            const roomId = roomIdCounter++;

            const update = {
                units: capacity,
                availableUnits: capacity,
                roomId: roomId
            };

            const result = await roomModel.updateOne(
                { _id: new mongoose.Types.ObjectId(room._id) },
                { $set: update }
            );

            console.log(`📝 Updated room ${room._id} ,roomId: ${roomId}`);
        }

        console.log("✅ Room data update complete.z ");
    } catch (error) {
        console.error("❌ Error updating rate IDs:", error.message);
        return res.status(500).json({
            success: false,
            message: "Error assigning otaRateIds.",
            error: error.message,
        });
    }

}
export const assignRatesId = async (req, res) => {
    try {
        const rates = await RatePlan.find({}).sort({ _id: 1 })
        console.log(`🔍 Found ${rates.length} rate documents`);
        let rateIdCounter = 800000;

        for (const rate of rates) {
            const otaRateId = rateIdCounter++;

            await RatePlan.updateOne(
                { _id: new mongoose.Types.ObjectId(rate._id) },
                { $set: { otaRateId } }
            );

            console.log(`📝 Updated rate ${rate._id} → otaRateId: ${otaRateId}`);
        }

        console.log("✅ Numeric otaRateId assignment complete.");
        return res.status(200).json({
            success: true,
            message: `Assigned otaRateIds to ${rates.length} documents.`,
        });



    } catch (error) {
        console.error("❌ Error updating rate IDs:", error.message);
        return res.status(500).json({
            success: false,
            message: "Error assigning otaRateIds.",
            error: error.message,
        });
    }

}
import AWS from 'aws-sdk';
import { PassThrough } from 'stream';

// Configure AWS S3
const s3 = new AWS.S3({
    accessKeyId: "********************",
    secretAccessKey: "aHdxhingPfKtP8+M38u9NTmXLV0fCIL3z2m7qhsy",
    region: "ap-south-1",
});

// Upload function
export async function uploadToS3(imageUrl, key) {
    try {
        const response = await axios({
            url: imageUrl,
            method: 'GET',
            responseType: 'stream',
        });

        const pass = new PassThrough();
        response.data.pipe(pass);

        const params = {
            Bucket: "mixdorm-live",
            Key: key,
            Body: pass,
            ContentType: response.headers['content-type'],
        };

        await s3.upload(params).promise();

        return {
            s3Uri: `s3://mixdorm-live/${key}`,
            objectUrl: `https://de2qijr9wfk4u.cloudfront.net/${key}`,
        };
        console.log("111111111DOne")
    } catch (err) {
        console.error(`Failed to upload image ${key}:`, err.message);
        return null;
    }
}

export const updateLowestRatesFromARI = async (req, res) => {
    try {
        const liveProperties = await property.find({ isPropertyLive: true });
        console.log(`Found ${liveProperties.length} live properties`);

        const today = new Date();
        today.setHours(0, 0, 0, 0); // normalize date

        for (const prop of liveProperties) {
            const { _id, otaId, name } = prop;

            const lowestRateData = await ARI.findOne({
                propertyId: _id,
                date: today,
                rate: { $gt: 0 }
            }).sort({ rate: 1 });

            if (lowestRateData) {
                const minValidRate = lowestRateData.rate;

                const updateResult = await property.updateOne(
                    { otaId },
                    {
                        $set: {
                            "lowestAveragePricePerNight.value": minValidRate,
                            "lowestAveragePricePerNight.currency": "INR",
                            lowestAveragePrivatePricePerNight: minValidRate
                        }
                    }
                );

                console.log(`✅ Updated ${name} to ₹${minValidRate}`, updateResult);
            } else {
                console.log(`⚠️ No valid rate for ${name}`);
            }
        }

        console.log("🎯 All eligible properties updated.");

        return res.status(200).json({
            success: true,
            message: `All live properties updated successfully.`,
        });
    } catch (err) {
        console.error("❌ Error updating rates from ARI:", err.message);
        return res.status(500).json({
            success: false,
            message: "Server error while updating rates.",
            error: err.message
        });
    }
};


export const updateHostelworldImages = async (req, res) => {
    const { otaId, hostelworldId } = req.query;

    if (!otaId || !hostelworldId) {
        return res.status(400).json({
            success: false,
            message: "otaId and hostelworldId are required",
        });
    }

    try {
        // Step 1: Find property by otaId
        const existingProperty = await property.findOne({ otaId: parseInt(otaId) });

        if (!existingProperty) {
            return res.status(404).json({
                success: false,
                message: `Property with otaId ${otaId} not found.`,
            });
        }

        // Step 2: Fetch property data from Hostelworld
        const propertyUrl = `https://prod.apigee.hostelworld.com/legacy-msapi-service/property/${hostelworldId}?show-rooms=1`;
        const response = await axios.get(propertyUrl);
        const propertyData = response.data;

        if (!Array.isArray(propertyData.images)) {
            return res.status(404).json({
                success: false,
                message: "No images found in Hostelworld response",
            });
        }

        // Step 3: Upload images to S3
        const uploadedImages = [];

        for (const img of propertyData.images) {
            if (!img?.prefix || !img?.suffix) continue;

            const imageUrl = `https://${img.prefix}${img.suffix}`;
            const key = `property-Images/${hostelworldId}_${Date.now()}_${Math.random().toString(36).slice(2)}.jpg`;

            const uploadResult = await uploadToS3(imageUrl, key);
            if (uploadResult) uploadedImages.push(uploadResult);
        }

        // Step 4: Update only the `images` and `distanceFromCityCenter` fields
        const updateFields = {
            images: uploadedImages,
            distanceFromCityCenter: propertyData?.location?.distance?.kilometers || 0
        };

        await property.updateOne({ otaId: parseInt(otaId) }, { $set: updateFields });

        return res.status(200).json({
            success: true,
            message: `Images and distance updated for property otaId ${otaId}`,
            updatedFields: updateFields
        });

    } catch (err) {
        console.error("Update error:", err.message);
        return res.status(500).json({
            success: false,
            message: "Failed to update property images or distance",
            error: err.message
        });
    }
};
export const fixMissingImageIds = async (req, res) => {
    try {
        // Fetch all properties with missing image _id
        const properties = await property.find({ "images._id": { $exists: false } });
        console.log("properties", properties.length)
        let bulkOps = [];
        let fixedCount = 0;

        for (const prop of properties) {
            const updatedImages = prop.images.map(img => {
                if (!img._id) {
                    return { ...img.toObject?.() || img, _id: new mongoose.Types.ObjectId() };
                }
                return img;
            });

            bulkOps.push({
                updateOne: {
                    filter: { _id: prop._id },
                    update: { $set: { images: updatedImages } }
                }
            });

            fixedCount++;
        }

        if (bulkOps.length > 0) {
            await property.bulkWrite(bulkOps);
        }

        return res.status(200).json({
            success: true,
            message: `Fixed missing image IDs in ${fixedCount} properties.`
        });

    } catch (err) {
        console.error("Fix error:", err);
        return res.status(500).json({
            success: false,
            message: "Failed to fix missing image IDs",
            error: err.message
        });
    }
};
export const fixStateAnCity = async (req, res) => {
    try {
        await updateStateAndCity();
        res.status(200).json({ message: "City & State update started" });
    } catch (error) {
        console.log(error);
        res.status(500).json({ error: "Something went wrong" });
    }
};
// ✅ Utility: clean address strings
function cleanAddress(addressLine) {
    return addressLine
        ?.replace(/[\/\\]/g, " ") // replace slashes with spaces
        .replace(/\s+/g, " ")     // collapse multiple spaces
        .trim();
}

async function fetchGeocode(query) {
    const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${query}&key=${process.env.GOOGLE_API_KEY}`;
    const response = await fetch(url);
    return response.json();
}

// assuming your Mongoose model is imported as `property` elsewhere

const GOOGLE_API_KEY = 'AIzaSyBv_hPcDOPcrTfHnLrFNduHgJWDwv1pjfU';

export async function updateStateAndCity(req, res) {
    try {
        const properties = await property.find({ "address.country": "India" });

        for (const propertyDetail of properties) {
            const propertyName = propertyDetail?.name?.trim();
            if (!propertyName) {
                console.log(`❌ Skippingg document without name: ${propertyDetail._id}`);
                continue;
            }

            console.log(`🔍 Processing: ${propertyName}`);

            let city = null;
            let state = null;

            try {
                // 1) Google Places Text Search (best for business names)
                // const query = `${propertyName} ${propertyDetail.address?.city ? propertyDetail.address.city + " " : ""}India`.trim();
                const query = `${propertyDetail.address?.lineOne ? propertyDetail.address.lineOne + ", " : ""}${propertyDetail.address?.lineTwo ? propertyDetail.address.lineTwo + ", " : ""
                    }India`.trim();

                const textSearchUrl = `https://maps.googleapis.com/maps/api/place/textsearch/json?query=${encodeURIComponent(query)}&key=${GOOGLE_API_KEY}`;
                const searchResp = await axios.get(textSearchUrl);

                if (searchResp.data?.results?.length) {
                    const placeId = searchResp.data.results[0].place_id;

                    // 2) Place Details (only request address components)
                    const detailsUrl = `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&fields=address_component&key=${GOOGLE_API_KEY}`;
                    const detailsResp = await axios.get(detailsUrl);

                    const components = detailsResp.data?.result?.address_components || [];
                    ({ city, state } = extractCityState(components));
                }

                // 3) Fallback → Geocoding API (biased to India)
                if (!city || !state) {
                    console.log(`⚠️ Places miss, geocoding: ${propertyName}`);
                    const geocodeUrl = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(
                        `${propertyName}, India`
                    )}&components=country:IN&key=${GOOGLE_API_KEY}`;
                    const geoResp = await axios.get(geocodeUrl);

                    const components = geoResp.data?.results?.[0]?.address_components || [];
                    const out = extractCityState(components);
                    city = city || out.city;
                    state = state || out.state;
                }

                // 4) Update Mongo only if something was found
                if (city || state) {
                    await property.updateOne(
                        { _id: propertyDetail._id },
                        {
                            $set: {
                                "address.city": city || propertyDetail.address?.city || "",
                                "address.state": state || propertyDetail.address?.state || "",
                            },
                        },
                        { runValidators: false }
                    );

                    console.log(`✅ Updated: ${propertyName} → city: ${city || "—"}, state: ${state || "—"}`);
                } else {
                    console.log(`❌ Could not fetch city/state for: ${propertyName}`);
                }
            } catch (err) {
                console.error(`❌ Error for ${propertyName}:`, err.message);
            }
        }

        if (res) res.status(200).json({ message: "Update process finished" });
    } catch (error) {
        console.error("❌ Controller error:", error.message);
        if (res) res.status(500).json({ message: "Internal server error" });
    }
}

// Helper to parse city/state from Google address components
function extractCityState(components = []) {
    let city = null;
    let state = null;

    for (const c of components) {
        if (
            c.types.includes("locality") ||
            c.types.includes("administrative_area_level_2") ||
            c.types.includes("postal_town")
        ) {
            city = city || c.long_name;
        }
        if (c.types.includes("administrative_area_level_1")) {
            state = state || c.long_name;
        }
    }
    return { city, state };
}





