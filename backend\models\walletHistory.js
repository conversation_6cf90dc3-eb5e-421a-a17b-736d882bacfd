import mongoose from 'mongoose';

const walletHistorySchema = new mongoose.Schema({
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
    amount: {
        type: Number,
        required: true,
    },
    currency:{
        type:String
    },
    date: {
        type: Date,
        required: true
    },
    type: {
        type: String,
    },
    paymentId: {
        type: String,
        required: false
    },
    payment_details: {
        type: Object,
        required: false
    },
    paymentStatus: {
        type: String,
        enum: ['paid', 'failed'],
        required: true,
    },
    last_payment_error: {
        type: Object
    },
    },
    {
        timestamps: true,
    },);

const WalletHistory = mongoose.model('walletHistory', walletHistorySchema); 

export default WalletHistory;

