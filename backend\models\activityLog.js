import mongoose from 'mongoose';
const Schema = mongoose.Schema;

const activityLogSchema = new Schema({
  action: { 
    type: String, 
    required: true 
},
  details: { 
    type: String 
},
  user: { 
    type: Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
},
  timestamp: { 
    type: Date, 
    default: Date.now 
},
});

const activityLogModel = mongoose.model('ActivityLog', activityLogSchema);

export default activityLogModel;

