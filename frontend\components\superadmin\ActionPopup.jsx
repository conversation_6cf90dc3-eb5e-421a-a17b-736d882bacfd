 
"use client";
import React, { useState } from "react";
import { useEffect, useRef } from "react";
import { FaChevronDown, FaChevronUp } from "react-icons/fa"; // Import icons

const ActionPopup = ({ onClose, onSave }) => {
  const popupRef = useRef(null);
  const dropdownRef = useRef(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState("Verify"); // Default value

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const handleOptionClick = (option) => {
    setSelectedOption(option);
    setIsDropdownOpen(false);
  };
   

  useEffect(() => {
    function handleDroppownClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    }
    document.addEventListener("mousedown", handleDroppownClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleDroppownClickOutside);
    };
  }, []);


  useEffect(() => {
    function handleClickOutside(event) {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        onClose(); // Close the popup
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [onClose]);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex justify-center items-center z-50 p-2 ">
      <div className="bg-white rounded-xl w-[400px] dark:bg-[#171616]" ref={popupRef}>
        <h2 className="text-3xl text-center font-bold font-poppins my-4 dark:text-gray-100">User Action</h2>
        <hr />
        <div className="flex flex-col mx-6 my-4">
          <label className=" text-black/50 text-base font-medium font-poppins dark:text-[#ffffff]">Christine Brooks</label>
          <label className="font-semibold text-black/40 mt-5 text-sm font-poppins dark:text-[#B6B6B6]">Status</label>

          {/* Custom Dropdown */}
          <div className="relative mt-2" ref={dropdownRef}>
            <button
              className="w-full bg-[#EEF9FF] border p-2 rounded flex items-center justify-between dark:bg-[#141313] dark:text-[#B6B6B6]"
              onClick={toggleDropdown}
            >
              {selectedOption}
              {isDropdownOpen ? (
                <FaChevronUp className="text-gray-500 dark:text-[#B6B6B6]" />
              ) : (
                <FaChevronDown className="text-gray-500 dark:text-[#B6B6B6]" />
              )}
            </button>

            {/* Dropdown options */}
            {isDropdownOpen && (
              <ul className="absolute bg-white border mt-1 rounded w-full z-10 dark:bg-[#141313]">
                <li
                  className="p-2 hover:bg-blue-100 cursor-pointer text-sm font-poppins font-medium dark:text-[#B6B6B6] dark:hover:bg-[#333232d4]"
                  onClick={() => handleOptionClick("Verify")}
                >
                  Verify
                </li>
                <li
                  className="p-2 hover:bg-blue-100 cursor-pointer text-sm font-poppins font-medium dark:text-[#B6B6B6] dark:hover:bg-[#333232d4]"
                  onClick={() => handleOptionClick("Unverify")}
                >
                  Unverify
                </li>
              </ul>
            )}
          </div>
        </div>
        <div className="flex justify-between gap-4 m-6">
          <button
            className="border w-[50%] px-4 py-2 rounded text-sm font-poppins font-medium
            dark:text-[#ffffff]"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            className="bg-sky-blue-650  w-[50%] text-white px-4 py-2 rounded text-sm font-poppins font-medium"
            onClick={onSave}
          >
            Save changes
          </button>
        </div>
      </div>
    </div>
  );
};

export default ActionPopup;
