 
import React from "react";
import { PiFileTextDuotone } from "react-icons/pi";
import Image from "next/image";

function ChatMessage({ message, time, isSender, isFile, fileName, fileSize }) {
  return (
    <div className={`flex ${isSender ? "justify-end" : "justify-start"} my-4`}>
      {/* Show Profile2 on the left if not sender */}
      {!isSender && (
        <Image
          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile2.png`}
          width={37}
          height={37}
          alt="Profile"
          className="rounded-full mr-4 w-12 h-12"
          loading="lazy"
        />
      )}

      <div className="w-full">
        {isFile ? (
          <div className="flex items-center p-4 bg-[#EEF9FF] rounded-lg max-w-sm dark:bg-[#1d1c1c]">
            <div className="p-2 mr-3 bg-[#FEC53D] rounded-md">
              <PiFileTextDuotone className="text-xl text-white" />
            </div>
            <div>
              <p className="text-sm font-semibold dark:text-[#B6B6B6]">{fileName}</p>
              <p className="text-xs text-gray-500 dark:text-[#757575]">{fileSize}</p>
            </div>
            <div className="ml-auto">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Download.png`}
                width={24}
                height={24}
                alt="download"
                loading="lazy"
                
              />
            </div>
          </div>
        ) : (
          <p
            className={`text-sm rounded-lg ${
              isSender ? " text-left dark:text-[#B6B6B6]" : "bg-[#EEF9FF] text-left p-4 dark:bg-[#171616] dark:text-[#B6B6B6]"
            }`}
          >
            {message}
          </p>
        )}
        <p
          className={`text-xs text-gray-500 dark:text-[#B6B6B6] ${
            isSender ? "text-left" : "text-left"
          }`}
        >
          {time}
        </p>
      </div>

      {/* Show Profile1 on the right if sender */}
      {isSender && (
        <Image
          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
          width={37}
          height={37}
          alt="Profile"
          loading="lazy"
          className="rounded-full w-12 h-12"
        />
      )}
    </div>
  );
}

export default ChatMessage;
