import React, { useState, Fragment, useEffect, useRef } from "react";
import {
  editProfileApi,
  editProperty<PERSON>pi,
  getProfileApi,
} from "@/services/ownerflowServices";
import toast from "react-hot-toast";
import Image from "next/image";
import { Dialog, Transition } from "@headlessui/react";
import { BASE_URL } from "@/utils/api";
import { useHeaderOwner } from "../headerContex";

const AboutUs = ({ id, data, updatePropertyData, loading, setLoading }) => {
  const [aboutUsData, setAboutUsData] = useState(data?.aboutUs || "");
  const [edit, setEdit] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageFailed, setImageFailed] = useState(false);
  const [openEditabout, setopenEditabout] = useState(false);
  const closeabouteditModal = () => setopenEditabout(false);
  const fileInputRef = useRef(null);
  const { profileData, propertyData, updateuserData } = useHeaderOwner();

  useEffect(() => {
    setAboutUsData(data?.aboutUs);
  }, [data]);

  const handleInputChange = (e) => {
    setAboutUsData(e.target.value);
  };
  const handleSubmit = async () => {
    setLoading(true);
    try {
      const payload = {
        aboutUs: aboutUsData,
      };
      const res = await editPropertyApi(id, payload);
      if (res?.status === 200) {
        toast.success(res?.data?.message);
        setEdit(false);
        updatePropertyData();
        closeabouteditModal();
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = async (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      setLoading(true);
      // setErrors({ ...errors, file: null });

      try {
        const formData = new FormData();
        formData.append("files", selectedFile);

        const presignedUrlResponse = await fetch(
          `${BASE_URL}/fileUpload/generate-presigned-url`,
          {
            method: "POST",
            body: formData,
          }
        );

        if (!presignedUrlResponse.ok) {
          throw new Error("Failed to get presigned URL");
        }

        const presignedUrlData = await presignedUrlResponse.json();
        // const { objectURL } = presignedUrlData.data;
        const objectURL =
          Array.isArray(presignedUrlData.data) &&
          presignedUrlData.data[0]?.path;

        if (presignedUrlData?.status) {
          const response = await editProfileApi({
            profileImage: {
              objectURL: objectURL,
            },
          });
          if (response?.data?.status) {
            toast.success(
              response?.data?.message || "Profile updated successfully!"
            );

            try {
              const response = await getProfileApi();

              if (response?.status === 200) {
                updateuserData(response?.data?.data);
              }
            } catch (error) {
              console.error("Error fetching profile:", error.message);
            }
          }
        }

        toast.success("Profile picture uploaded successfully!");
      } catch (error) {
        console.error("Error uploading profile picture", error);
        toast.error("Error uploading profile picture");
      } finally {
        setLoading(false);
      }
    } else {
      toast.error("Error uploading profile picture");
    }
  };

  return (
    <div>
      {loading ? (
        <div>
          {!edit ? (
            <>
              <div className="flex justify-between items-center mb-2">
                <div className="sm:w-32 w-24 h-6 bg-gray-200 rounded animate-pulse"></div>
                <div className="flex sm:gap-2 gap-1">
                  <div className="sm:w-24 w-20 h-8 bg-gray-200 rounded-lg animate-pulse"></div>
                  <div className="sm:w-24 w-20 h-8 bg-gray-200 rounded-lg animate-pulse"></div>
                </div>
              </div>
              <div className="flex gap-7 items-start">
                <div className="relative">
                  <div className="rounded-full min-w-[80px] min-h-[80px] w-[80px] h-[80px] bg-gray-200 animate-pulse"></div>
                  <div className="absolute w-5 h-5 rounded-full sm:w-8 sm:h-8 -bottom-0 -right-0 bg-gray-200 animate-pulse"></div>
                </div>
                <div className="flex-1 space-y-3">
                  <div className="w-3/4 h-5 bg-gray-200 rounded animate-pulse"></div>
                  <div className="w-full h-4 bg-gray-200 rounded animate-pulse"></div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-gray-200 rounded-full animate-pulse"></div>
                    <div className="w-1/2 h-4 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                  <div className="space-y-2 mt-5">
                    <div className="w-full h-3 bg-gray-200 rounded animate-pulse"></div>
                    <div className="w-full h-3 bg-gray-200 rounded animate-pulse"></div>
                    <div className="w-2/3 h-3 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                </div>
              </div>
            </>
          ) : (
            ""
          )}

          {/* Skeleton for Edit Modal */}
          <div className="fixed inset-0 bg-black/50 hidden">
            <div className="fixed inset-0 overflow-hidden">
              <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
                <div className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md">
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <div className="w-24 h-6 bg-gray-200 rounded animate-pulse"></div>
                    <div className="w-6 h-6 bg-gray-200 rounded-full animate-pulse"></div>
                  </div>
                  <div className="sm:px-6 px-4">
                    <div className="w-full h-32 bg-gray-200 rounded-lg animate-pulse"></div>
                    <div className="flex items-center justify-between w-full sm:my-14 my-7 gap-4 col-span-2 py-4 bg-white/80 sticky bottom-0 backdrop-blur-sm">
                      <div className="w-full h-10 bg-gray-200 rounded-lg animate-pulse"></div>
                      <div className="w-full h-10 bg-gray-200 rounded-lg animate-pulse"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Skeleton for Add Modal */}
          <div className="fixed inset-0 bg-black/50 hidden">
            <div className="fixed inset-0 overflow-hidden">
              <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
                <div className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md">
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <div className="w-24 h-6 bg-gray-200 rounded animate-pulse"></div>
                    <div className="w-6 h-6 bg-gray-200 rounded-full animate-pulse"></div>
                  </div>
                  <div className="sm:px-6 px-4">
                    <div className="w-full h-32 bg-gray-200 rounded-lg animate-pulse"></div>
                    <div className="flex items-center justify-between w-full sm:my-14 my-7 gap-4 col-span-2 py-4 bg-white/80 sticky bottom-0 backdrop-blur-sm">
                      <div className="w-full h-10 bg-gray-200 rounded-lg animate-pulse"></div>
                      <div className="w-full h-10 bg-gray-200 rounded-lg animate-pulse"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div>
          {!edit ? (
            <>
              <div className="flex justify-between items-center mb-2">
                <h1 className="sm:text-xl text-base font-medium">About Us</h1>
                <div className="flex sm:gap-2 gap-1">
                  <button
                    className="sm:flex block items-center text-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-primary-blue font-medium"
                    onClick={() => {
                      setopenEditabout(true);
                    }}
                  >
                    Edit About Us
                  </button>
                </div>
              </div>
              <div className="flex gap-7 items-start">
                <div className="relative">
                  <input
                    type="file"
                    name="images"
                    multiple
                    accept=".jpg, .jpeg, .png"
                    className="z-10 cursor-pointer block w-full p-2 px-4 text-sm bg-transparent border rounded-lg opacity-0 border-gray-220 focus:outline-none text-slate-320 placeholder:text-gray-320 absolute top-0 left-0 right-0 bottom-0"
                    placeholder="Upload Photos"
                  />
                  {/* <Image
                    className="rounded-full min-w-[80px] min-h-[80px] w-[80px] h-[80px] border border-red-600"
                    src={
                      profileData?.profileImage?.objectURL ||
                      (propertyData?.images?.length > 0 &&
                        `${
                          propertyData?.images?.[0]?.objectUrl?.startsWith(
                            "https"
                          )
                            ? ""
                            : "https://"
                        }${propertyData?.images?.[0]?.objectUrl}`)
                    }
                    width={80}
                    height={80}
                  ></Image> */}
                  <div className="relative rounded-full min-w-[80px] min-h-[80px] w-[80px] h-[80px] ">
                    {/* Placeholder */}
                    {(!profileData?.profileImage?.objectURL &&
                      !(propertyData?.images?.length > 0)) ||
                    imageFailed ||
                    !imageLoaded ? (
                      <div className="absolute inset-0 bg-gray-200 rounded-full flex items-center justify-center">
                        <span className="text-base font-bold text-gray-400">
                          MixDorm
                        </span>
                      </div>
                    ) : null}

                    {/* Actual Image */}
                    <Image
                      className="rounded-full w-full h-full object-cover"
                      src={
                        profileData?.profileImage?.objectURL ||
                        (propertyData?.images?.length > 0 &&
                          `${
                            propertyData?.images?.[0]?.objectUrl?.startsWith(
                              "https"
                            )
                              ? ""
                              : "https://"
                          }${propertyData?.images?.[0]?.objectUrl}`)
                      }
                      width={80}
                      height={80}
                      onLoadingComplete={() => setImageLoaded(true)}
                      onError={() => setImageFailed(true)}
                      style={{
                        opacity: imageLoaded && !imageFailed ? 1 : 0,
                        transition: "opacity 0.3s ease-in-out",
                      }}
                    />
                  </div>

                  <div
                    className="absolute flex items-center justify-center w-5 h-5 rounded-full sm:w-8 sm:h-8 -bottom-0 -right-0 cursor-pointer z-10"
                    onClick={() => fileInputRef.current.click()}
                  >
                    <Image
                      className="absolute"
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/camera.svg`}
                      width={35}
                      height={35}
                    ></Image>
                  </div>
                  <input
                    type="file"
                    ref={fileInputRef}
                    style={{ display: "none" }}
                    onChange={handleFileChange}
                    accept="image/*"
                  />
                </div>
                <div>
                  <p className="font-bold sm:text-3xl text-base font-manrope">
                    {data?.name}
                  </p>
                  <p className="text-[#888888] sm:text-base text-sm font-normal font-inter mt-5">
                    {data?.aboutUs}
                  </p>
                </div>
              </div>
            </>
          ) : (
            ""
          )}

          <Transition show={openEditabout} as={Fragment}>
            <Dialog
              as="div"
              className="relative z-50"
              onClose={closeabouteditModal}
            >
              {/* Overlay */}
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <div className="fixed inset-0 bg-black/50" />
              </Transition.Child>

              {/* Slide-In Modal */}
              <div className="fixed inset-0 overflow-hidden">
                <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
                  <Transition.Child
                    as={Fragment}
                    enter="transform transition ease-out duration-300"
                    enterFrom="translate-x-full"
                    enterTo="translate-x-0"
                    leave="transform transition ease-in duration-200"
                    leaveFrom="translate-x-0"
                    leaveTo="translate-x-full"
                  >
                    <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                      {/* Modal Header */}
                      <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                        <h2 className="page-title">Edit about</h2>
                        <button
                          onClick={closeabouteditModal}
                          className="text-gray-500 hover:text-gray-800"
                        >
                          &#10005; {/* Close icon */}
                        </button>
                      </div>

                      {/* Modal Content */}
                      <div className="sm:px-6 px-4">
                        <textarea
                          name="description"
                          className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                          placeholder="Enter About Us"
                          rows={4}
                          value={aboutUsData}
                          onChange={handleInputChange}
                          style={{ outline: "none" }}
                        ></textarea>
                        <div className="xs:flex block  items-center justify-between w-full sm:my-14 my-7 gap-4 col-span-2 py-4 bg-white/80 sticky bottom-0 backdrop-blur-sm">
                          <button
                            type="button"
                            className="hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 px-4 border-black rounded-lg w-full text-sm xs:mb-0 mb-2"
                            onClick={closeabouteditModal}
                          >
                            Cancel
                          </button>
                          <button
                            type="submit"
                            onClick={handleSubmit}
                            className="bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm"
                          >
                            Save Changes
                          </button>
                        </div>
                      </div>
                    </Dialog.Panel>
                  </Transition.Child>
                </div>
              </div>
            </Dialog>
          </Transition>

          {/* <textarea
        value={aboutUsData}
        onChange={handleInputChange}
        autoFocus // Add this prop to focus the textarea
        className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500 h-72"
      />
      <button
        className="bg-[#40E0D0] font-semibold text-white py-4 px-10 rounded-full w-full max-w-52 mx-auto block hover:bg-[#40E0D0] transition duration-200 mt-6"
        onClick={handleSubmit}
      >
        Submit
      </button> */}

          {/* {addAboutUs && <AddAbout onClose={() => setAddAboutUs(false)} />} */}

          {/* {editAboutUs && (
        <EditAbout
          onClose={() => setEditAboutUs(false)}
          aboutUsData={aboutUsData}
        />
      )} */}
        </div>
      )}
    </div>
  );
};

export default AboutUs;
