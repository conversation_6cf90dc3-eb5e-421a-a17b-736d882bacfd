import express from 'express';
import { createCheckIn, getCheckInStatus
     ,getCheckInByProperty,editCheckIn,deleteCheckIn} from '../controller/eCheckIns.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();

// Route to create a new e-check-in
router.post('/', checkAuth("add_eCheckIn"),createCheckIn);
router.get('/all/:id',getCheckInByProperty);

// Route to get check-in status by booking ID
router.get('/:id', getCheckInStatus);
router.put('/:id', editCheckIn);
router.delete('/:id', deleteCheckIn);

export default router;
/**
 * @swagger
 * /check-in:
 *   post:
 *     summary: Create E-Check-In
 *     tags: [E-Check-In]
 *     description: Allows guests to submit their details for E-Check-In.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               checkIn:
 *                 type: string
 *                 format: date
 *                 example: "2024-09-22"
 *               checkOut:
 *                 type: string
 *                 format: date
 *                 example: "2024-09-23"
 *               guestDetails:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                     example: "Priyanka"
 *                   phone:
 *                     type: string
 *                     example: "1234567"
 *               uploadedDocuments:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     url:
 *                       type: string
 *                       example: "www.example.com"
 *               fromLocation:
 *                 type: string
 *                 example: "Ahmedabad"
 *               toLocation:
 *                 type: string
 *                 example: "Dubai"
 *               property:
 *                 type: string
 *                 format: objectId
 *                 example: "66bd2f7592a0a6fb036f346f"
 *     responses:
 *       201:
 *         description: Check-in details submitted successfully.
 *       400:
 *         description: Bad request (missing required fields)
 *       500:
 *         description: Internal Server Error
 */


/**
 * @swagger
 * /check-in/{id}:
 *   get:
 *     summary: Get Check-In Status
 *     tags: [E-Check-In]
 *     description: Retrieves check-in details for a specific booking.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: The ID of the CheckIn.
 *         schema:
 *           type: string
 *           format: objectId
 *     responses:
 *       200:
 *         description: Check-in details retrieved successfully.
 *       404:
 *         description: Check-in details not found.
 *       500:
 *         description: Internal Server Error
 */

/**
 * @swagger
 * /check-in/all/{id}:
 *   get:
 *     summary: Get Check-In Status
 *     tags: [E-Check-In]
 *     description: Retrieves check-in details for a specific booking.
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: The ID of the Property.
 *         schema:
 *           type: string
 *           format: objectId
 *     responses:
 *       200:
 *         description: Check-in details retrieved successfully.
 *       404:
 *         description: Check-in details not found.
 *       500:
 *         description: Internal Server Error
 */

/**
 * @swagger
 * /check-in/{id}:
 *   put:
 *     summary: Edit Check-In Details
 *     tags: [E-Check-In]
 *     description: Updates an existing E-Check-In record.
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: The ID of the CheckIn to update.
 *         schema:
 *           type: string
 *           format: objectId
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               checkIn:
 *                 type: string
 *                 format: date
 *                 example: "2024-12-20"
 *               checkOut:
 *                 type: string
 *                 format: date
 *                 example: "2024-12-25"
 *               guestDetails:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                     example: "John Doe"
 *                   phone:
 *                     type: string
 *                     example: "+1234567890"
 *               uploadedDocuments:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     url:
 *                       type: string
 *                       example: "www.example.com"
 *               fromLocation:
 *                 type: string
 *                 example: "Airport"
 *               toLocation:
 *                 type: string
 *                 example: "Hotel"
 *               property:
 *                 type: string
 *                 format: objectId
 *                 example: "66bd2f7592a0a6fb036f346f"
 *     responses:
 *       200:
 *         description: Check-in details updated successfully.
 *       404:
 *         description: Check-in record not found.
 *       500:
 *         description: Internal Server Error
 */
/**
 * @swagger
 * /check-in/{id}:
 *   delete:
 *     summary: Delete Check-In Record
 *     tags: [E-Check-In]
 *     description: Deletes an existing E-Check-In record by ID.
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: The ID of the CheckIn to delete.
 *         schema:
 *           type: string
 *           format: objectId
 *     responses:
 *       200:
 *         description: Check-in record deleted successfully.
 *       404:
 *         description: Check-in record not found.
 *       500:
 *         description: Internal Server Error
 */
