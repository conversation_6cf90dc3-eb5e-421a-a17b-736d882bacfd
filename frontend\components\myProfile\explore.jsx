import React from "react";
import Link from "next/link";
import { BiSolidCrown } from "react-icons/bi";
import { ChevronRight } from "lucide-react";
import { PiLockKeyLight } from "react-icons/pi";
import { RiDiscountPercentFill   } from "react-icons/ri";
import { FaCarAlt  } from "react-icons/fa";
import { BsAwardFill, BsFillGiftFill } from "react-icons/bs";

const AddAmount = () => {
  
  return (
    <>
        <div>
            <div className="mb-4">
              <ul
                className="flex flex-wrap justify-between"
                data-tabs-toggle="#default-tab-content"
                role="tablist"
              >
                <li className="me-2 cursor-pointer" role="presentation">
                  <div className="w-[380px] h-[185px] bg-[url('https://mixdorm.s3.ap-south-1.amazonaws.com/front-images/explorebg.png')] rounded-3xl border border-[#FFAD72] p-5">
                    <div className="flex justify-between">
                      <h3 className="font-bold text-black text-2xl">
                        Explorer
                      </h3>
                      <div>
                        <p className="text-xs font-semibold">
                          Reward <span className="text-sm">Points</span>
                        </p>
                        <p className="text-lg font-semibold text-[#FFAD72] flex justify-end">
                          1,000
                        </p>
                      </div>
                    </div>
                    <div className="w-[120px] h-[28px] bg-[#FFAD72] mt-5 flex px-1 justify-between items-center">
                      <BiSolidCrown size={15} className="text-white" />
                      <p className="text-sm text-white font-semibold">
                        4 Benefits
                      </p>
                      <ChevronRight size={15} className="text-white" />
                    </div>
                    <div className="mt-4">
                      <div className="mb-1 text-sm font-medium dark:text-white flex justify-between">
                        <span>1000/5000</span> <span>Redeem</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mb-4 dark:bg-gray-700">
                        <div className="bg-[#FFAD72] h-1.5 rounded-full dark:bg-blue-500 w-1/2"></div>
                      </div>
                    </div>
                  </div>
                </li>
                <li
                  className="me-2 cursor-pointer flex items-center"
                  role="presentation"
                >
                  <div className="w-[290px] h-[145px] bg-[#D9D9D9] rounded-3xl border border-[#DDDDDD] p-4">
                    {/* <img className="" width="380px" height="185px" src="" alt="explore" /> */}
                    <div className="flex justify-between">
                      <h3 className="font-bold text-black text-xl">
                        Adventurer
                      </h3>
                      <div>
                        <p className="text-xs font-semibold">
                          Reward <span className="text-sm">Points</span>
                        </p>
                        <p className="text-sm font-semibold text-black flex justify-end">
                          6,000
                        </p>
                      </div>
                    </div>
                    <div className="mt-1 flex justify-center">
                      <PiLockKeyLight size={35} />
                    </div>
                    <div className="mt-1">
                      <div className="mb-1 text-xs font-medium dark:text-white flex justify-between">
                        <span>6000/12000</span> <span>Redeem</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mb-4 dark:bg-gray-700">
                        <div className="bg-black h-1.5 rounded-full dark:bg-blue-500 w-1/2"></div>
                      </div>
                    </div>
                  </div>
                </li>
                <li
                  className="me-2 cursor-pointer flex items-center"
                  role="presentation"
                >
                  <div className="w-[218px] h-[107px] bg-[#D9D9D9] rounded-3xl border border-[#DDDDDD] p-3">
                    {/* <img className="" width="380px" height="185px" src="" alt="explore" /> */}
                    <div className="flex justify-between">
                      <h3 className="font-bold text-black text-lg">Nomad</h3>
                      <div>
                        <p className="text-xs text-[7px] font-semibold">
                          Reward <span className="text-[8px]">Points</span>
                        </p>
                        <p className="text-[12px] font-semibold text-black flex justify-end">
                          6,000
                        </p>
                      </div>
                    </div>
                    <div className="mt-1 flex justify-center">
                      <PiLockKeyLight size={20} />
                    </div>
                    <div className="mt-1">
                      <div className="mb-1 text-[8px] font-medium dark:text-white flex justify-between">
                        <span>6000/12000</span> <span>Redeem</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mb-4 dark:bg-gray-700">
                        <div className="bg-black h-1.5 rounded-full dark:bg-blue-500 w-1/2"></div>
                      </div>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
            <div className="mb-4">
              <p className="text-black text-xl font-normal">
                <span className="text-black font-extrabold">Benefits :</span> 4
                Benefits that introduce users to Mixdorm’s basic perks and
                features.
              </p>
            </div>
            <div className="mb-4 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
                <RiDiscountPercentFill size={22} />
              </span>
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  1. 3% Discount on Bookings:
                </span>{" "}
                4 Benefits that introduce users to Mixdorm’s basic perks and
                features.
              </p>
            </div>
            <div className="mb-2 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
                <FaCarAlt size={22} />
              </span>
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  2. 1 Free Mix Ride:
                </span>{" "}
                Complimentary access to one Mix Ride, helping users connect with
                travel companions.
              </p>
            </div>
            <div className="mb-2 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
                <BsAwardFill size={22} />
              </span>
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  3. 2x Reward Points on Every Booking:
                </span>{" "}
                Users earn double the points on each booking, speeding up their
                progress to the next tier
              </p>
            </div>
            <div className="mb-2 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
                <BsFillGiftFill size={22} />
              </span>{" "}
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  4. Gift Cards on Milestones:
                </span>{" "}
                Small gift cards or vouchers are awarded for reaching milestone
                bookings (e.g., after 5 or 10 bookings).
              </p>
            </div>
            <div className="flex justify-between mt-7 mb-3">
              <div>
                <h4 className="font-extrabold text-black">
                  <span className="text-primary-blue underline">
                    Wallet History{" "}
                  </span>{" "}
                  &nbsp;&nbsp;&nbsp;My Point History
                </h4>
              </div>

              <div>
                <Link
                  href="#"
                  className="text-lg font-semibold text-black hover:text-sky-blue-750"
                >
                  See All
                </Link>
              </div>
            </div>
            <div className="mb-4">
              <div className="flex py-3 border-b border-grey-600 justify-between">
                <div>
                  <p className="text-[16px] font-bold">
                    Booking Points-{" "}
                    <span className="font-normal">
                      Get 100 Point On booking
                    </span>
                  </p>
                  <p className="text-[16px] font-normal text-[#AAACAE]">
                    5 min ago
                  </p>
                </div>
                <div className="rounded-full bg-[#D9F9F6] text-center p-[12px]">
                  <p className="text-xs text-primary-blue font-normal">
                    Points
                  </p>
                  <p className="text-sm text-primary-blue font-semibold">100</p>
                </div>
              </div>
              <div className="flex py-3 border-b border-grey-600 justify-between">
                <div>
                  <p className="text-[16px] font-bold">
                    Event Points-{" "}
                    <span className="font-normal">
                      Get 100 Point On booking
                    </span>
                  </p>
                  <p className="text-[16px] font-normal text-[#AAACAE]">
                    5 min ago
                  </p>
                </div>
                <div className="rounded-full bg-[#D9F9F6] text-center p-[12px]">
                  <p className="text-xs text-primary-blue font-normal">
                    Points
                  </p>
                  <p className="text-sm text-primary-blue font-semibold">100</p>
                </div>
              </div>
              <div className="flex py-3 border-b border-grey-600 justify-between">
                <div>
                  <p className="text-[16px] font-bold">
                    Mix Ride Points-{" "}
                    <span className="font-normal">
                      Get 100 Point On booking
                    </span>
                  </p>
                  <p className="text-[16px] font-normal text-[#AAACAE]">
                    5 min ago
                  </p>
                </div>
                <div className="rounded-full bg-[#D9F9F6] text-center p-[12px]">
                  <p className="text-xs text-primary-blue font-normal">
                    Points
                  </p>
                  <p className="text-sm text-primary-blue font-semibold">100</p>
                </div>
              </div>
              <div className="flex py-3 border-b border-grey-600 justify-between">
                <div>
                  <p className="text-[16px] font-bold">
                    Mix Creator Points-{" "}
                    <span className="font-normal">
                      Get 100 Point On booking
                    </span>
                  </p>
                  <p className="text-[16px] font-normal text-[#AAACAE]">
                    5 min ago
                  </p>
                </div>
                <div className="rounded-full bg-[#D9F9F6] text-center p-[12px]">
                  <p className="text-xs text-primary-blue font-normal">
                    Points
                  </p>
                  <p className="text-sm text-primary-blue font-semibold">100</p>
                </div>
              </div>
              <div className="flex py-3 border-b border-grey-600 justify-between">
                <div>
                  <p className="text-[16px] font-bold">
                    Booking Points-{" "}
                    <span className="font-normal">
                      Get 100 Point On booking
                    </span>
                  </p>
                  <p className="text-[16px] font-normal text-[#AAACAE]">
                    5 min ago
                  </p>
                </div>
                <div className="rounded-full bg-[#D9F9F6] text-center p-[12px]">
                  <p className="text-xs text-primary-blue font-normal">
                    Points
                  </p>
                  <p className="text-sm text-primary-blue font-semibold">100</p>
                </div>
              </div>
              <div className="flex py-3 border-b border-grey-600 justify-between">
                <div>
                  <p className="text-[16px] font-bold">
                    Event Points-{" "}
                    <span className="font-normal">
                      Get 100 Point On booking
                    </span>
                  </p>
                  <p className="text-[16px] font-normal text-[#AAACAE]">
                    5 min ago
                  </p>
                </div>
                <div className="rounded-full bg-[#D9F9F6] text-center p-[12px]">
                  <p className="text-xs text-primary-blue font-normal">
                    Points
                  </p>
                  <p className="text-sm text-primary-blue font-semibold">100</p>
                </div>
              </div>
            </div>
          </div>
  </>
  );
};

export default AddAmount;
