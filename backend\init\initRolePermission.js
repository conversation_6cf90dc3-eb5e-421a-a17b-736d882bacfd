import Role from '../models/roles.js';
import userModel from '../models/auth.js'
import bcrypt from 'bcrypt';

const defaultRoles = [
  {
    role: 'admin',
    permissions: [
      'user_profile',
      'add_hostel',
      "add_sub_admin",
      "get_admins",
      "update_sub_admin",
      "delete_sub_admin",
      "add_hostel_owner",
      "user_profile",
      "update_user_profile",
      "update_hostel",
      "delete_hostel",
      "add_property",
      "update_property",
      "list_properties",
      "get_user",
      "add_room",
      "update_room",
      "delete_room",
      "get_users",
      "list_properties",
      "add_booking",
      "update_booking",
      "delete_booking",
      "list_bookings_by_user",
      "get_review",
      "get_property_reviews",
      "get_all_reviews",
      "update-approval-status",
      "ota_management",
      "property_by_state",
      "add_blog",
      "update_blog",
      "delete_blog",
      "add_review",
      "check_availability",
      "add_page",
      "add_permission",
      "update_permission",
      "get_all_permissions",
      "get_permission",
      "delete_permission",
      "user_travelling_details",
      "list_all_properties",
      "update_settings",
      "get_settings",
      "get_user_details",
      "profile_view",
      "get_all_rides",
      "get_ride",
      "cancel_ride",
      "event_review",
      "travellers_visited",
      "add_manual_payments",
      "get_manual_payments",
      "add_eCheckIn",
      "send_message",
      "get_chanel_managers_list",
      "delete_chats",
      "cancel_booking"
    ]
  }, {
    role: 'sub_admin',
    permissions: [
      'user_profile',
      'add_hostel',
      "update_sub_admin",
      "delete_sub_admin",
      "add_hostel_owner",
      "user_profile",
      "update_hostel",
      "delete_hostel",
      "add_property",
      "update_property",
      "list_properties",
      "get_user",
      "add_room",
      "update_room",
      "delete_room",
      "get_users",
      "list_properties",
      "get_review",
      "get_property_reviews",
      "get_all_reviews",
      "update-approval-status",
      "ota_management",
      "property_by_state",
      "add_blog",
      "update_blog",
      "delete_blog",
      "check_availability",
      "update_user_profile",
      "add_review",
      "add_page",
      "get_checkout_page",
      "user_travelling_details",
      "list_all_properties",
      "update_settings",
      "get_settings",
      "get_user_details",
      "profile_view",
      "get_all_rides",
      "get_ride",
      "cancel_ride",
      "get_users_list",
      "event_review",
      "travellers_visited",
      "add_manual_payments",
      "get_manual_payments",
      "add_eCheckIn",
      "send_message",
      "get_chanel_managers_list",
      "delete_chats"
    ]
  }, {
    role: 'hostel_owner',
    permissions: [
      'user_profile',
      'add_hostel',
      "update_hostel",
      "delete_hostel",
      "add_property",
      "update_property",
      "list_properties",
      "add_room",
      "update_room",
      "delete_room",
      "add_event",
      "update_event",
      "delete_event",
      "list_events",
      "check_property",
      "list_my_properties",
      "list_bookings",
      "add_booking",
      "update_booking",
      "delete_booking",
      "list_bookings_by_user",
      "get_review",
      "get_property_reviews",
      "property_details",
      "add_blog",
      "update_blog",
      "delete_blog",
      "check_availability",
      "update_user_profile",
      "add_review",
      "get_checkout_page",
      "user_travelling_details",
      "view_bookings",
      "book_event",
      "get_user_details",
      "get_users_list",
      "event_review",
      "travellers_visited",
      "add_manual_payments",
      "get_manual_payments",
      "add_eCheckIn",
      "get_all_rides",
      "view_notifications",
      "add_ride",
      "send_message",
      "get_chanel_managers_list",
      "get_chats",
      "recent_search",
      "delete_chats",
      "cancel_booking",
      "profile_view",
      "get_room_rate"
    ]
  }, {
    role: 'user',
    permissions: [
      'user_profile',
      "add_review",
      "update_review",
      "get_review",
      "get_property_reviews",
      "delete_review",
      "list_bookings_by_user",
      "add_blog",
      "update_blog",
      "delete_blog",
      "check_booking_range",
      "update_user_profile",
      "user_travelling_details",
      "get_checkout_page",
      "book_event",
      "view_bookings",
      "list_events",
      "get_user_details",
      "profile_view",
      "add_ride",
      "get_all_rides",
      "get_ride",
      "cancel_ride",
      "get_my_rides",
      "get_users_list",
      "event_review",
      "send_message",
      "get_chanel_managers_list",
      "get_chats",
      "view_notifications",
      "recent_search",
      "list_my_properties",
      "delete_chats",
      "cancel_booking",
      "profile_view",
      "wallet_add",
      "wallet_view",
      "add_event",
      "get_room_rate"
    ]
  }];
  const hashedPassword = await bcrypt.hash("Admin@mixdorm", 10);
  
const defaultAdmin = {
  name: {
    first: 'Admin Mixdorm '
  },
  email: '<EMAIL>',
  password:hashedPassword,
  role: 'admin'
};
async function initializeDefaultAdmin() {
  try {
    let user = await userModel.findOne({ email: defaultAdmin.email });
    if (!user) {
      user = new userModel(defaultAdmin);
      await user.save();
      console.log(`Default admin user created: ${defaultAdmin.username}`);
    } else {
      console.log(`Default admin user already exists: ${defaultAdmin.username}`);
    }
  } catch (err) {
    console.error('Error initializing default admin user:', err);
  }
}
async function initializeRoles() {
  for (const roleData of defaultRoles) {
    try {
      let role = await Role.findOneAndUpdate(
        { role: roleData.role },
        {
          $addToSet: { permissions: { $each: roleData.permissions } } // Adds permissions if they don't already exist
        },
        { upsert: true, new: true }
      );
      console.log(`Role '${roleData.role}' permission updated`);
    } catch (err) {
      console.error(`Error initializing role '${roleData.role}':`, err);
    }
  }
}
export { initializeRoles, initializeDefaultAdmin };
