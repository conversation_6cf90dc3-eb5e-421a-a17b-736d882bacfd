import groupModel from "../models/group.js";
import userModel from "../models/auth.js";
import Response from "../utills/response.js";
import ActivityLog from "../models/activityLog.js";

const createGroup = async (req, res) => {
    try {
        const { name, createdBy, members = [] } = req.body;
    
        // Validate input
        if (!name || !createdBy) {
          return Response.BadRequest(res, null, "Group name and creator ID are required.");
        }
    
        // Validate creator and members in a single query
        const users = await userModel.find({ _id: { $in: [createdBy, ...members] } }).select('_id');
        const userIds = users.map(user => user._id.toString());
    
        if (!userIds.includes(createdBy)) {
          return Response.NotFound(res, null, "Creator not found.");
        }
    
        const invalidMembers = members.filter(member => !userIds.includes(member));
        if (invalidMembers.length > 0) {
          return Response.NotFound(res, null, `Invalid member IDs: ${invalidMembers.join(', ')}`);
        }
    
        // Create the group
        const group = await groupModel.create({ name, createdBy, members });
    
        // Add group to users in one bulk operation
        const userUpdates = [createdBy, ...members];
        await userModel.updateMany(
          { _id: { $in: userUpdates } },
          { $push: { groups: group._id } }
        );
    
        return Response.Created(res, group, "Group created successfully.");
      } catch (err) {
        console.error("Error creating group:", err);
        return Response.InternalServerError(res, null, "Internal server error.");
      }
};

const addMemberToGroup = async (req, res) => {
    try {
      const { groupId } = req.params;
      const { userId } = req.body;
  
      // Validate input
      if (!groupId || !userId) {
        return Response.BadRequest(res, null, "Group ID and User ID are required.");
      }
  
      // Check if group and user exist
      const group = await groupModel.findById(groupId);
      const user = await userModel.findById(userId);
  
      if (!group) {
        return Response.NotFound(res, null, "Group not found.");
      }
  
      if (!user) {
        return Response.NotFound(res, null, "User not found.");
      }
  
      // Check if user is already a member of the group
      if (group.members.includes(userId)) {
        return Response.BadRequest(res, null, "User is already a member of the group.");
      }
  
      // Add the user to the group
      group.members.push(userId);
      await group.save();
  
      // Add group to user's groups list
      await userModel.updateOne(
        { _id: userId },
        { $push: { groups: group._id } }
      );
  
      // Activity Log
      const userName = `${user.name.first} ${user.name.last || ""}`.trim();
      const log = new ActivityLog({
        action: "Added member to group",
        details: `${userName} was added to group ${group.name}`,
        user: user._id,
      });
      await log.save();
  
      return Response.OK(res, null, "User added to group successfully.");
    } catch (err) {
      console.error("Error adding member to group:", err);
      return Response.InternalServerError(res, null, "Internal server error.");
    }
  };
  
  const removeMemberFromGroup = async (req, res) => {
    try {
      const { groupId, userId } = req.params;
  
      // Validate input
      if (!groupId || !userId) {
        return Response.BadRequest(res, null, "Group ID and User ID are required.");
      }
  
      // Check if group and user exist
      const group = await groupModel.findById(groupId);
      const user = await userModel.findById(userId);
  
      if (!group) {
        return Response.NotFound(res, null, "Group not found.");
      }
  
      if (!user) {
        return Response.NotFound(res, null, "User not found.");
      }
  
      // Check if the user is a member of the group
      if (!group.members.includes(userId)) {
        return Response.BadRequest(res, null, "User is not a member of the group.");
      }
  
      // Remove the user from the group
      group.members = group.members.filter((member) => member.toString() !== userId);
      await group.save();
  
      // Remove the group from the user's groups list
      await userModel.updateOne(
        { _id: userId },
        { $pull: { groups: group._id } }
      );
  
      // Activity Log
      const userName = `${user.name.first} ${user.name.last || ""}`.trim();
      const log = new ActivityLog({
        action: "Removed member from group",
        details: `${userName} was removed from group ${group.name}`,
        user: user._id,
      });
      await log.save();
  
      return Response.OK(res, null, "User removed from group successfully.");
    } catch (err) {
      console.error("Error removing member from group:", err);
      return Response.InternalServerError(res, null, "Internal server error.");
    }
  };

  const deleteGroup = async (req, res) => {
    try {
      const { groupId } = req.params;
  
      // Validate input
      if (!groupId) {
        return Response.BadRequest(res, null, "Group ID is required.");
      }
  
      // Find the group
      const group = await groupModel.findById(groupId);
  
      if (!group) {
        return Response.NotFound(res, null, "Group not found.");
      }
  
      // Mark the group as "deleted" instead of hard-deleting
      group.isDeleted = true;
      group.deletedAt = new Date();
      await group.save();
  
      // Optionally update associated user references
      await userModel.updateMany(
        { _id: { $in: group.members } },
        { $pull: { groups: group._id } }
      );
  
      // Log activity for transparency
      const log = new ActivityLog({
        action: "Marked group as deleted",
        details: `Group ${group.name} was marked as deleted by user with ID ${group.createdBy}`,
        user: group.createdBy,
      });
      await log.save();
  
      return Response.OK(res, null, "Group marked as deleted successfully.");
    } catch (err) {
      console.error("Error marking group as deleted:", err);
      return Response.InternalServerError(res, null, "Internal server error.");
    }
  };

  const getGroupById = async (req, res) => {
    try {
      const { groupId } = req.params;
  
      // Validate input
      if (!groupId) {
        return Response.BadRequest(res, null, "Group ID is required.");
      }
  
      // Find the group by ID, considering only non-deleted groups
      const group = await groupModel.findOne({ _id: groupId, isDeleted: false })
        .populate('members', 'name email') // Optionally populate member details
        .populate('createdBy', 'name email'); // Optionally populate creator details
  
      if (!group) {
        return Response.NotFound(res, null, "Group not found.");
      }
  
      // Return the group details
      return Response.OK(res, group, "Group found successfully.");
    } catch (err) {
      console.error("Error finding group by ID:", err);
      return Response.InternalServerError(res, null, "Internal server error.");
    }
  };
  
  
  
export { createGroup,addMemberToGroup,removeMemberFromGroup,deleteGroup,getGroupById };
