 
import {
  LayoutDashboard,
  User,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";

const Navbar = () => {
  const pathname = usePathname();



  const menu = [
    {
      id: 1,
      name: "Dashboard",
      link: "/owner/dashboard",
      icon: <LayoutDashboard className="group-hover:text-blue-230" size={20} />,
    },
    {
      id: 2,
      // name: "Profile",
      // link: "/dashboard/profile",
      name: "Analytics",
      link: "/owner/dashboard/analytics",
      icon: <User className="group-hover:text-blue-230" size={20} />,
    },
    // {
    //   id: 3,
    //   name: "Availability",
    //   link: "/owner/dashboard/availability",
    //   icon: <CalendarClock className="group-hover:text-blue-230" size={20} />,
    // },
    // {
    //   id: 4,
    //   name: "Booking",
    //   link: "/owner/dashboard/booking",
    //   icon: <CalendarDays className="group-hover:text-blue-230" size={20} />,
    // },
    // {
    //   id: 5,
    //   name: "Event",
    //   link: "/owner/dashboard/event",
    //   icon: (
    //     <BiSolidCalendarStar className="group-hover:text-blue-230" size={20} />
    //   ),
    // },
    // {
    //   id: 6,
    //   name: "Payment",
    //   link: "/owner/dashboard/payment",
    //   icon: <GiReceiveMoney className="group-hover:text-blue-230" size={20} />,
    // },
    // {
    //   id: 7,
    //   name: "Reviews",
    //   link: "/owner/dashboard/reviews",
    //   image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/review.svg`,
    // },
    {
      id: 8,
      name: "Noticeboard",
      link: "/owner/dashboard/noticeboard",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/notiboard.svg`,
    },
    {
      id: 9,
      name: "Profile",
      link: "/owner/dashboard/propertyprofile",
      // image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/notiboard.svg`,
    },
  ];

  return (
    <section className="relative items-center hidden w-full px-3 bg-white rounded-md md:flex h-14 mmd:h-16 mmd:px-6 shadow-md">
      <ul className="flex items-center justify-start h-full gap-x-5">
        {menu.map((item) => (
          <li
            className={`relative flex items-center justify-start text-base font-medium duration-300 ease-in-out cursor-pointer hover:text-blue-230 group  font-Inter ${
              pathname === item.link ? "text-blue-230" : "text-slate-500"
            } `}
            key={item.id}
            onClick={item.onClick ? item.onClick : null} 
          >
            <Link
              href={item.link}
              className="flex items-center justify-start gap-x-2"
              prefetch={false}
            >
              {pathname === item.link && (
                <>
                  <span className="font-light ">{item?.icon}</span>
                  {item?.image && (
                    <Image
                      src={item.image}
                      alt={item.name}
                      title={item.name}
                      width={20}
                      height={20}
                      className={`object-contain group-hover:grayscale-0 w-5 h-5 ${
                        pathname === item.link ? "grayscale-0" : "grayscale "
                      }`}
                      loading="lazy"
                    />
                  )}{" "}
                </>
              )}{" "}
              <p className="text-sm font-manrope">{item.name}</p>
            </Link>
            {pathname === item.link && (
              <div className="absolute w-full h-1 rounded-full -bottom-5 bg-blue-230"></div>
            )}
          </li>
        ))}
      </ul>
    </section>
  );
};

export default Navbar;
