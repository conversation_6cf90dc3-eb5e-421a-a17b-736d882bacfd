import Response from '../utills/response.js';
import { bookEvent, listUserBookings, cancelBookingById } from '../services/eventBooking.js';
import { generateEventNumber } from '../utills/helper.js';

// Controller to book an event
export const bookEventController = async (req, res) => {
    try {
        const userId = req.user._id; 
        const bookingData = { ...req.body, user: userId };
        const eventRefNumber = await generateEventNumber();
        console.log("eventRefNumber",eventRefNumber)
        bookingData.bookingRefNumber = eventRefNumber; 
        const newBooking = await bookEvent(bookingData);
        return Response.Created(res, newBooking, 'Event booked successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

// Controller to list user's bookings
export const listUserBookingsController = async (req, res) => {
    try {
        const userId = req.user._id;
        const bookings = await listUserBookings(userId);
        return Response.OK(res, bookings, 'Events retrieved successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

// Controller to cancel a booking
export const cancelBookingController = async (req, res) => {
    try {
        const bookingId = req.params.id;
        const userId = req.user._id; // Assuming req.user contains _id field

        const canceledBooking = await cancelBookingById(bookingId, userId);
        return Response.OK(res, canceledBooking, 'Booking canceled successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};
