import profileViewsModel from '../models/profileViews.js';
export const profileViewsServices = async (data) => {
    return await profileViewsModel.create(data);
}

/**
 * Get details of users who viewed a profile
 * @param {ObjectId} userId - The ID of the user whose profile views to fetch
 * @returns {Array} - An array of users who viewed the profile
 */
export const getProfileViewers = async (userId) => {
    try {
        const profileViews = await profileViewsModel.aggregate([
            {
                $match: {
                    user: userId 
                }
            },
            {
                $lookup: {
                    from: 'users', 
                    localField: 'viewBy',
                    foreignField: '_id', 
                    as: 'viewerDetails'
                }
            },
            {
                $unwind: '$viewerDetails' 
            },
            {
                $project: { 
                    viewerId: '$viewerDetails._id',
                    name: '$viewerDetails.name',
                    profileImage: '$viewerDetails.profileImage'
                }
            }
        ]);

        return profileViews; 
    } catch (error) {
        console.error('Error fetching profile viewers:', error);
        throw new Error('Could not fetch profile viewers');
    }
};