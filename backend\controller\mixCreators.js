import axios from 'axios';

// Utility function to extract the username from Instagram URL
const extractUsername = (url) => {
  return url.split('/').filter(Boolean).pop();
};

// Fetch Instagram user data using username
const fetchInstagramDataByUsername = async (username,accessToken) => {
  try {
    const userDataUrl = `https://graph.instagram.com/me?fields=email&access_token=${accessToken}`;
    console.log("userDataUrl",userDataUrl)
    const userDataResponse = await axios.get(userDataUrl);

    const userId = userDataResponse.data.id;
    console.log(`User ID: ${userId}`);

    // Fetch user's media to calculate average likes
    const mediaUrl = `https://graph.instagram.com/${userId}/media?fields=id,like_count&access_token=${accessToken}`;
    const mediaResponse = await axios.get(mediaUrl);

    const media = mediaResponse.data.data || [];
    const totalLikes = media.reduce((acc, item) => acc + (item.like_count || 0), 0);
    const averageLikes = media.length ? totalLikes / media.length : 0;

    return {
      username: userDataResponse.data.username,
      followers: userDataResponse.data.followers_count,
      posts: userDataResponse.data.media_count,
      averageLikes: Math.round(averageLikes),
    };
  } catch (error) {
    console.error('Error fetching Instagram data:', error.response?.data || error.message);
    throw new Error('Failed to fetch Instagram data');
  }
};

// Controller function
export const connectInstagram = async (req, res) => {
  const { instaUrl } = req.body;

  // if (!instaUrl || !accessToken) {
  //   return res.status(400).json({ error: 'Instagram URL or access token is missing' });
  // }

  try {
    const username = extractUsername(instaUrl);
    const instagramData = await fetchInstagramDataByUsername(username, '695807');

    res.status(200).json({
      message: 'Instagram data retrieved successfully',
      data: instagramData,
    });
  } catch (error) {
    console.error('Error connecting to Instagram:', error.message);
    res.status(500).json({ error: 'Failed to connect to Instagram' });
  }
};
