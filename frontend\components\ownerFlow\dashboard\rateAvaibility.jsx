import React from "react";
import { CalendarDays, ChevronRight, ChevronLeft, Square, ChevronDown } from 'lucide-react';


const AddRoom = () => {
  return (
    <div className="">
     
    <div className="w-full">
      <div className="pt-8">
      {/* <div className="flex gap-1 justify-end mb-4">
      <button
                  className="bg-blue-500 text-white px-4 py-3 text-sm rounded"
                 
                >
                  Add Room
                </button> 
                <button
                  className="bg-blue-500 text-white px-4 py-3 text-sm rounded"
                 
                >
                  Edit Rate
                </button> 
      </div> */}
        <div className="calendar-container border border-[#E8F1FD] rounded-lg flex flex-col overflow-hidden">
          <div className="bg-[#F7F9FC]">
            <div className="calendar-header flex">
              <div className="flex w-[200px] gap-2 items-center flex-wrap border-r border-[#EEF0F2] py-3 px-4">
                <h6 className="flex items-center gap-1 text-[#667085] text-sm">
                <CalendarDays />

                  Full month
                </h6>
                    {/* <button className="ml-auto text-[#667085] hover:bg-primary-blue hover:text-white flex items-center justify-center bg-[#E7E9EE] rounded-sm w-6 h-6 text-base"> */}
                    <ChevronLeft className="bg-gray-100 border-none"/>

                    {/* </button> */}
                      {/* <button className="text-[#667085] hover:bg-primary-blue hover:text-white flex items-center justify-center bg-[#E7E9EE] rounded-sm w-6 h-6 text-base"> */}
                      <ChevronRight  className="bg-gray-100"/>
                      
                      {/* </button> */}
                      <div className="calendar-room-header w-full text-sm text-[#667085]">All Rooms</div>
                      </div>
                      <div className="flex-1 py-3 px-4">
                        <div className="text-[#667085] text-base mb-2">Aguste 2023</div>
                        <div className="calendar-dates flex gap-2">
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,01</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,02</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,03</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,04</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,05</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,06</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,07</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,08</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,09</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,10</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,11</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,12</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,13</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,14</button>

                          </div>
                        
                          </div>
                          </div>
                          </div>
                          <div className="calendar-row flex items-center border-b border[#E8F1FD]">
                            <div className="flex calendar-room-name w-[200px] p-4 text-sm text-[#667085] border-r border-[#EEF0F2] justify-between"> 
                              <Square className="text-yellow-700"/>
                                4 Bed Male Dorm     
                              <ChevronDown className="bg-gray-100"/>
                            </div>
                            </div>
                            <div className="calendar-row flex items-center border-b border[#E8F1FD]">
                                <div className="calendar-room-name w-[200px] p-4 text-sm text-[#667085] border-r border-[#EEF0F2]">Standard Room 02</div>
                                <div className="calendar-slots flex flex-[3]">
                                <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                                </div>
                                    </div>
                              <div className="calendar-row flex items-center border-b border[#E8F1FD]">
                                <div className="calendar-room-name w-[200px] p-4 text-sm text-[#667085] border-r border-[#EEF0F2]">Standard Room 02</div>
                                <div className="calendar-slots flex flex-[3]">
                                <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold">$452</div>
                                </div>
                                    </div>
                                    <div className="calendar-row flex items-center border-b border[#E8F1FD]">
                                      <div className="calendar-room-name w-[200px] p-4 text-sm text-[#667085] border-r border-[#EEF0F2]">Standard Room 03</div>
                                        <div className="calendar-slots flex flex-[3]">
                                        <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>

                                            </div>
                                            </div>
                                       
                                                </div>
                                                </div>
      </div>
      <div className="w-full">
      <div className="pt-8">
        <div className="calendar-container border border-[#E8F1FD] rounded-lg flex flex-col overflow-hidden">
          <div className="bg-[#F7F9FC]">
            <div className="calendar-header flex">
              <div className="flex w-[200px] gap-2 items-center flex-wrap border-r border-[#EEF0F2] py-3 px-4">
                <h6 className="flex items-center gap-1 text-[#667085] text-sm">
                <CalendarDays />

                  Full month
                </h6>
                    {/* <button className="ml-auto text-[#667085] hover:bg-primary-blue hover:text-white flex items-center justify-center bg-[#E7E9EE] rounded-sm w-6 h-6 text-base"> */}
                    <ChevronLeft className="bg-gray-100 border-none"/>

                    {/* </button> */}
                      {/* <button className="text-[#667085] hover:bg-primary-blue hover:text-white flex items-center justify-center bg-[#E7E9EE] rounded-sm w-6 h-6 text-base"> */}
                      <ChevronRight  className="bg-gray-100"/>
                      
                      {/* </button> */}
                      <div className="calendar-room-header w-full text-sm text-[#667085]">All Rooms</div>
                      </div>
                      <div className="flex-1 py-3 px-4">
                        <div className="text-[#667085] text-base mb-2">Aguste 2023</div>
                        <div className="calendar-dates flex gap-2">
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,01</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,02</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,03</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,04</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,05</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,06</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,07</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,08</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,09</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,10</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,11</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,12</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,13</button>
                          <button type="button" className="calendar-date p-1 flex-1 text-center border border-[#E8F1FD] rounded-md bg-white text-[#5D6679] font-base hover:bg-primary-blue hover:text-white">Mon,14</button>

                          </div>
                        
                          </div>
                          </div>
                          </div>
                          <div className="calendar-row flex items-center border-b border[#E8F1FD]">
                            <div className="flex calendar-room-name w-[200px] p-4 text-sm text-[#667085] border-r border-[#EEF0F2] justify-between"> 
                              <Square className="text-yellow-700"/>
                                4 Bed Male Dorm     
                              <ChevronDown className="bg-gray-100"/>
                            </div>
                            </div>
                            <div className="calendar-row flex items-center border-b border[#E8F1FD]">
                                <div className="calendar-room-name w-[200px] p-4 text-sm text-[#667085] border-r border-[#EEF0F2]">Standard Room 02</div>
                                <div className="calendar-slots flex flex-[3]">
                                <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                                </div>
                                    </div>
                              <div className="calendar-row flex items-center border-b border[#E8F1FD]">
                                <div className="calendar-room-name w-[200px] p-4 text-sm text-[#667085] border-r border-[#EEF0F2]">Standard Room 02</div>
                                <div className="calendar-slots flex flex-[3]">
                                <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                                </div>
                                    </div>
                                    <div className="calendar-row flex items-center border-b border[#E8F1FD]">
                                      <div className="calendar-room-name w-[200px] p-4 text-sm text-[#667085] border-r border-[#EEF0F2]">Standard Room 03</div>
                                        <div className="calendar-slots flex flex-[3]">
                                        <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold  rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>
                              <div className="calendar-slot p-2 flex-1 text-center m-2 bg-yellow-100 text-yellow-700 font-semibold rounded-lg">$452</div>

                                            </div>
                                            </div>
                                       
                                                </div>
                                                </div>
                                                </div>
    </div>
  );
};

export default AddRoom;
