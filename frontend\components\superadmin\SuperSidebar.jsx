 
 
"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { Box } from "@mui/material";
import Image from "next/image";
import {
  getItemLocalStorage,
  removeItemLocalStorage,
} from "@/utils/browserSetting";
import { useEffect, useRef, useState } from "react";
import { removeFirebaseToken } from "@/services/ownerflowServices";
import { useHeaderOwner } from "../ownerFlow/headerContex";
import { useNavbar } from "../home/<USER>";
import {  FiSettings } from "react-icons/fi";
import {  PiSquaresFourBold } from "react-icons/pi";
import { RxCross2 } from "react-icons/rx";
import ThemeSwitcher from "./ThemeSwitcher";
import { BiLogOutCircle } from "react-icons/bi";
import { FaRegCircleUser, FaRegUser } from "react-icons/fa6";
import {  GiKeyCard, GiNotebook } from "react-icons/gi";
import { RiArrowDropDownLine } from "react-icons/ri";
import { LuBellPlus, LuCalendarDays, LuUserRoundCog } from "react-icons/lu";
import { TbBrandWechat } from "react-icons/tb";
import { MdOutlineRateReview } from "react-icons/md";
import { FaHandHoldingUsd, FaHeadset } from "react-icons/fa";
import "@fontsource/poppins";
import { useRouter } from "next/navigation"; 

const SuperSidebar = ({ isCollapsed, isSidebarOpen, setIsSidebarOpen }) => {
  const router = useRouter();
  const [activeItem, setActiveItem] = useState(""); // State for active menu item
  // eslint-disable-next-line no-unused-vars
  const [flags, setFlags] = useState([]);
  const [isContentOpen, setIsContentOpen] = useState(false);
  const [isManagementOpen, setIsManagementOpen] = useState(false);
  const [isPaymentOpen, setIsPaymentOpen] = useState(false);
  const [isLandingPageOpen, setIsLandingPageOpen] = useState(false);
  const [isOtherContentOpen, setIsOtherContentOpen] = useState(false);
  const pathname = usePathname();
  
  const { updateUserStatus, updateUserRole, updateHopId } = useNavbar();
  // const [activeItem, setActiveItem] = useState({ name: "", url: "" });
  const [activeDropdownItem, setActiveDropdownItem] = useState(""); // Tracks the active dropdown item

  const sidebarRef = useRef(null);
  
  useEffect(() => {
    function handleClickOutside(event) {
      if (sidebarRef.current && !sidebarRef.current.contains(event.target)) {
        setIsSidebarOpen(false);
      }
    }
    if (isSidebarOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isSidebarOpen]);


  const handleDropdownItemClick = (dropdownItem) => {
    setActiveDropdownItem(dropdownItem);
    setActiveItem("Content"); // Keep the parent "Content" option highlighted
  };

  // const handleItemClick = (item) => {
  //   setActiveItem(item);
  //   setActiveDropdownItem(""); // Clear dropdown selection if another main item is selected
  // };

  const handleItemClick = (name) => {
    setActiveItem(name);
    // setActiveItem({ name: itemName, url: itemUrl })// Set the active menu item
    if (name === "Multiple Property") {
      handleOpenMultipleProperty();
      setActiveDropdownItem("");
    }
    if (window.innerWidth < 640) {
      setIsSidebarOpen(false); // Close sidebar on mobile after selecting
    }
  };

  const handleLogout = async () => {
    removeItemLocalStorage("token");
    removeItemLocalStorage("name");
    removeItemLocalStorage("id");
    removeItemLocalStorage("role");
    removeItemLocalStorage("hopid");
    removeItemLocalStorage("email");
    removeItemLocalStorage("contact");
    updateUserStatus("");
    updateUserRole("");
    updateHopId("");

    const payload = {
      token: getItemLocalStorage("FCT"),
      userId: getItemLocalStorage("uid"),
    };

    try {
      await removeFirebaseToken(payload);
      console.log("FCM token removed successfully.");
      removeItemLocalStorage("FCT");
    } catch (error) {
      console.error("Error removing FCM token:", error);
    }
    removeItemLocalStorage("uid");
    router.push("/owner/login");
  };

  // eslint-disable-next-line no-unused-vars
  const [openMultipleProperty, setOpenMultipleProperty] = useState(false);
  const handleOpenMultipleProperty = () => setOpenMultipleProperty(true);

  const { profileData, propertyData } = useHeaderOwner();

  useEffect(() => {
    const fetchFlags = async () => {
      try {
        const response = await fetch("https://restcountries.com/v3.1/all");
        const data = await response.json();

        const filteredFlags = data
          .filter((country) => {
            const commonName = country.name.common;
            return propertyData?.address?.country?.includes(commonName);
          })
          .map((country) => ({
            id: country.cca3,
            img: country.flags.svg,
            name: country.name.common,
          }));

        setFlags(filteredFlags);
      } catch (error) {
        console.error("Error fetching flags:", error);
      }
    };

    if (propertyData?.address?.country) {
      fetchFlags();
    }
  }, []);

  useEffect(() => {
    const setActiveItemFromPath = (path) => {
      const dashboardPath = path.split("/dashboard")[1];

      if (!dashboardPath || dashboardPath === "/") {
        setActiveItem("Dashboard");
        setIsContentOpen(false);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(false);
        setActiveDropdownItem("");
      } else if (dashboardPath.includes("/user")) {
        setActiveItem("User");
        setIsContentOpen(false);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(false);
        setActiveDropdownItem("");
      } else if (dashboardPath.includes("/booking")) {
        setActiveItem("Booking");
        setIsContentOpen(false);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(false);
        setActiveDropdownItem("");
      } else if (dashboardPath.includes("/hostel")) {
        setActiveItem("Hostel");
        setIsContentOpen(false);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(false);
        setActiveDropdownItem("");
      } else if (dashboardPath.includes("/communication")) {
        setActiveItem("Communication");
        setIsContentOpen(false);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(false);
        setActiveDropdownItem("");
      } else if (dashboardPath.includes("/notification")) {
        setActiveItem("Notification");
        setIsContentOpen(false);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(false);
        setActiveDropdownItem("");
      } else if (dashboardPath.includes("/announcement")) {
        setActiveItem("Notification");
        setIsContentOpen(false);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(false);
        setActiveDropdownItem("");
      } 
      else if (dashboardPath.includes("/create-notification")) {
        setActiveItem("Notification");
        setIsContentOpen(false);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(false);
        setActiveDropdownItem("");
      }
      else if (dashboardPath.includes("/bulk-notification")) {
        setActiveItem("Notification");
        setIsContentOpen(false);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(false);
        setActiveDropdownItem("");
      }
      else if (dashboardPath.includes("/feedback")) {
        setActiveItem("Feedback");
        setIsContentOpen(false);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(false);
        setActiveDropdownItem("");
      } else if (dashboardPath.includes("/support")) {
        setActiveItem("Support");
        setIsContentOpen(false);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(false);
        setActiveDropdownItem("");
      } else if (dashboardPath.includes("/profile")) {
        setActiveItem("Profile");
        setIsContentOpen(false);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(false);
        setActiveDropdownItem("");
      }
      // } else if (dashboardPath.includes("/payment")) {
      //   setActiveItem("Payment");
      //   setIsContentOpen(false);
      //   setIsLandingPageOpen(false);
      //   setActiveDropdownItem("");
      // }

      // Handle Content submenu items
      if (dashboardPath.includes("/mobile-homepage")) {
        setActiveItem("Content", "Mobile Home Page");
        setActiveDropdownItem("Mobile Home Page");
        setIsContentOpen(true);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(false);
      } else if (dashboardPath.includes("/content-banner")) {
        setActiveItem("Content", "Banner");
        setActiveDropdownItem("Banner");
        setIsContentOpen(true);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(true);
        setIsOtherContentOpen(false);
      } else if (dashboardPath.includes("/explore-world")) {
        setActiveItem("Content" , "Explore the World");
        setActiveDropdownItem("Explore the World");
        setIsContentOpen(true);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(true);
        setIsOtherContentOpen(false);
      } else if (dashboardPath.includes("/featured-hostel")) {
        setActiveItem("Content", "Featured Hostel");
        setActiveDropdownItem("Featured Hostel");
        setIsContentOpen(true);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(true);
        setIsOtherContentOpen(false);
      } else if (dashboardPath.includes("/travel-activity")) {
        setActiveItem("Content","Travel by Activity");
        setActiveDropdownItem("Travel by Activity");
        setIsContentOpen(true);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(true);
        setIsOtherContentOpen(false);
      } else if (dashboardPath.includes("/split-bill")) {
        setActiveItem("Content" , "Split bill fairfare, Mix mate, Mix ride");
        setActiveDropdownItem("Split bill fairfare, Mix mate, Mix ride");
        setIsContentOpen(true);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(true);
        setIsOtherContentOpen(false);
      } else if (dashboardPath.includes("/event")) {
        setActiveItem("Content" , "Event");
        setActiveDropdownItem("Event");
        setIsContentOpen(true);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(true);
        setIsOtherContentOpen(false);
      } else if (dashboardPath.includes("/content-blog")) {
        setActiveItem("Content", "Landing-Blog");
        setActiveDropdownItem("Landing-Blog");
        setIsContentOpen(true);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(true);
        setIsOtherContentOpen(false);
      } else if (dashboardPath.includes("/about-us")) {
        setActiveItem("Content", "About Us");
        setActiveDropdownItem("About Us");
        setIsContentOpen(true);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(true);
      } else if (dashboardPath.includes("/awards")) {
        setActiveItem("Content" , "Awards");
        setActiveDropdownItem("Awards");
        setIsContentOpen(true);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(true);
      }
      else if (dashboardPath.includes("/help")) {
        setActiveItem("Content", "Help");
        setActiveDropdownItem("Help");
        setIsContentOpen(true);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(true);
      } else if (dashboardPath.includes("/booking-guarantee")) {
        setActiveItem("Content", "Booking Guarantee");
        setActiveDropdownItem("Booking Guarantee");
        setIsContentOpen(true);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(true);
      }else if (dashboardPath.includes("/booking-refund-policy")) {
        setActiveItem("Content", "Booking Refund Policy");
        setActiveDropdownItem("Booking Refund Policy");
        setIsContentOpen(true);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(true);
      }else if (dashboardPath.includes("/privacy-policy")) {
        setActiveItem("Content", "Privacy Policy");
        setActiveDropdownItem("Privacy Policy");
        setIsContentOpen(true);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(true);
      }else if (dashboardPath.includes("/terms&conditions")) {
        setActiveItem("Content", "Terms & Conditions");
        setActiveDropdownItem("Terms & Conditions");
        setIsContentOpen(true);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(true);
      }else if (dashboardPath.includes("/faqs")) {
        setActiveItem("Content", "Faqs");
        setActiveDropdownItem("Faqs");
        setIsContentOpen(true);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(true);
      }
      else if (dashboardPath.includes("/offer-banner")) {
        setActiveItem("Content" , "Offer Banner");
        setActiveDropdownItem("Offer Banner");
        setIsContentOpen(true);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(false);
      } else if (dashboardPath.includes("/blog")) {
        setActiveItem("Content", "Blog");
        setActiveDropdownItem("Blog");
        setIsContentOpen(true);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(false);
      } else if (dashboardPath.includes("/contact")) {
        setActiveItem("Content", "Contact Us");
        setActiveDropdownItem("Contact Us");
        setIsContentOpen(true);
        setIsManagementOpen(false);
        setIsPaymentOpen(false);
        setIsLandingPageOpen(false);
        setIsOtherContentOpen(false);
      }

      // Handle Management submenu items
      if (dashboardPath.includes("/management-admin")) {
        setActiveItem("Management" , "Admin");
        setIsContentOpen(false);
        setIsPaymentOpen(false);
        setIsManagementOpen(true);
        setActiveDropdownItem("Admin");
      } else if (dashboardPath.includes("/management-audit")) {
        setActiveItem("Management" , "Audit Logs");
        setIsContentOpen(false);
        setIsPaymentOpen(false);
        setIsManagementOpen(true);
        setActiveDropdownItem("Audit Logs");
      } else if (dashboardPath.includes("/management-status")) {
        setActiveItem("Management", "Status");
        setIsContentOpen(false);
        setIsPaymentOpen(false);
        setIsManagementOpen(true);
        setActiveDropdownItem("Status");
      }

      // Handle Management submenu items
      if (dashboardPath.includes("/payment-booking")) {
        setActiveItem("Payment", "Bookings");
        setIsPaymentOpen(true);
        setIsContentOpen(false);
        setIsManagementOpen(false);
        setActiveDropdownItem("Bookings");
      } else if (dashboardPath.includes("/payment-subscription")) {
        setActiveItem("Payment", "Subscription");
        setIsPaymentOpen(true);
        setIsContentOpen(false);
        setIsManagementOpen(false);
        setActiveDropdownItem("Subscription");
      } else if (dashboardPath.includes("/payment-event")) {
        setActiveItem("Payment", "Event");
        setIsPaymentOpen(true);
        setIsContentOpen(false);
        setIsManagementOpen(false);
        setActiveDropdownItem("Event");
      }
    };

    setActiveItemFromPath(pathname);
  }, [pathname]);

  return (
    <div className="flex" ref={sidebarRef}>
      <Box
        role="presentation"
        sx={{ width: 327 }}
        className={`bg-white dark:bg-[#0D0D0D] w-[240px]  fixed z-10 transition-transform duration-300 h-[calc(100vh)] overflow-y-auto scrollbar-hide ${
          isSidebarOpen ? "translate-x-0" : "-translate-x-full"
        } sm:translate-x-0`}
      >
        <button
          className="fixed top-4 left-4 z-20 p-2 rounded-md sm:hidden"
          onClick={() => setIsSidebarOpen(false)}
        >
          {isSidebarOpen && <RxCross2 className="bg-transparent text-black dark:text-[#B6B6B6]" />}
        </button>
        <div className="flex items-center justify-center w-full gap-x-2 h-[70px] ">
          <Image
            src={
              profileData?.profileImage?.objectURL
                ? profileData?.profileImage?.objectURL
                : `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/avatar.png`
            }
            alt="Profile Pic"
            className="rounded-lg w-[40px] h-[40px]"
            width={40}
            height={40}
            loading="lazy"
          />

          <Image
            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/logo.png`}
            alt="Mixdorm"
            width={140}
            height={50}
            title="Mixdorm"
            className="object-contain w-fit h-fit max-w-20 max-h-11 flex items-center"
          />
        </div>

        <div className="flex flex-col justify-between h-[1000px]">
          <div className={`w-full ${isCollapsed ? "px-0" : "px-0"}`}>
            <ul className="w-full text-sm  text-black-100 flex flex-col h-auto ">
              <div
                className={`px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4  hover:border-[#50C2FF] ${
                  activeItem === "Dashboard"
                    ? "border-l-4 border-[#50C2FF]"
                    : ""
                }`}
              >
                <li
                  className={`cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white transition duration-300 ease-in-out transform hover:scale-105 mt-0.5 hover:font-semibold ${
                    activeItem === "Dashboard" ? "bg-[#50C2FF] text-white" : ""
                  }`}
                  onClick={() => {handleItemClick("Dashboard"); router.push("/superadmin/dashboard")}}
                >
                  <Link
                    href="/superadmin/dashboard"
                    className={`flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${
                      activeItem === "Dashboard"
                        ? " text-white font-semibold"
                        : ""
                    }`}
                  >
                    <PiSquaresFourBold className="text-xl" /> Dashboard
                  </Link>
                </li>
              </div>
              <div
                className={`px-6 bg-white dark:bg-[#0D0D0D]  hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${
                  activeItem === "User" ? "border-l-4 border-[#50C2FF] " : ""
                }`}
              >
                <li
                  className={`cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white transition duration-300 ease-in-out transform hover:scale-105 mt-0.5 hover:font-semibold ${
                    activeItem === "User" ? "bg-[#50C2FF] text-white" : ""
                  }`}
                  onClick={() => {handleItemClick("User"); router.push("/superadmin/dashboard/user")}}
                >
                  <Link
                    href="/superadmin/dashboard/user"
                    className={`flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${
                      activeItem === "User" ? " text-white font-semibold" : ""
                    }`}
                  >
                    <FaRegUser className="text-xl" /> User
                  </Link>
                </li>
              </div>
              <div
                className={`px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${
                  activeItem === "Content" ? "border-l-4 border-[#50C2FF] " : ""
                }`}
              >
                <li className="mt-0.5  transition duration-300 ease-in-out transform">
                  <button
                    onClick={() => setIsContentOpen(!isContentOpen)}
                    className={`flex items-center gap-2 cursor-pointer font-poppins text-sm h-[48px] pl-8 pr-10 rounded-lg ${
                      activeItem === "Content" || isContentOpen
                        ? "bg-[#50C2FF] text-white font-semibold"
                        : "text-gray-700 dark:text-[#ffffff] hover:bg-[#50C2FF] hover:text-white hover:font-semibold"
                    } transition duration-300 ease-in-out transform hover:scale-105`}
                  >
                    <GiNotebook className="text-xl" /> Content
                    <RiArrowDropDownLine
                      className={`text-2xl float-end transition-transform ${
                        isContentOpen ? "rotate-180" : ""
                      }`}
                    />
                  </button>
                  {isContentOpen && (
                    <ul className="mt-2 space-y-4 ml-12">
                      <li>
                        <Link
                          href="/superadmin/dashboard/mobile-homepage"
                          className={`text-sm font-poppins ${
                            activeDropdownItem === "Mobile Home Page"
                              ? "text-[#50C2FF] font-semibold"
                              : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                          }`}
                          onClick={() => {
                            handleDropdownItemClick("Mobile Home Page");
                            handleItemClick("Mobile Home Page");
                          }}
                        >
                          Mobile Home Page
                        </Link>
                      </li>
                      <li>
                        <button
                          onClick={() =>
                            setIsLandingPageOpen(!isLandingPageOpen)
                          }
                          className={`flex items-center cursor-pointer text-sm font-poppins rounded-lg ${
                            activeItem === "Landing Page" || isLandingPageOpen
                              ? "text-[#50C2FF] font-semibold"
                              : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                          } `}
                        >
                          Landing Page
                          <RiArrowDropDownLine
                            className={`text-2xl float-end transition-transform ${
                              isLandingPageOpen ? "rotate-180" : ""
                            }`}
                          />
                        </button>
                        {isLandingPageOpen && (
                          <ul className="mt-2 ml-4 space-y-4">
                            <li>
                              <Link
                                href="/superadmin/dashboard/content-banner"
                                className={`text-sm font-poppins ${
                                  activeDropdownItem === "Banner"
                                    ? "text-[#50C2FF] font-semibold"
                                    : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                                }`}
                                onClick={() => {
                                  handleDropdownItemClick("Banner");
                                  handleItemClick("Banner");
                                }}
                              >
                                Banner
                              </Link>
                            </li>
                            <li>
                              <Link
                                href="/superadmin/dashboard/explore-world"
                                className={`text-sm font-poppins ${
                                  activeDropdownItem === "Explore the World"
                                    ? "text-[#50C2FF] font-semibold"
                                    : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                                }`}
                                onClick={() => {
                                  handleDropdownItemClick("Explore the World");
                                  handleItemClick("Explore the World");
                                }}
                              >
                                Explore the World
                              </Link>
                            </li>
                            <li>
                              <Link
                                href="/superadmin/dashboard/featured-hostel"
                                className={`text-sm font-poppins ${
                                  activeDropdownItem === "Featured Hostel"
                                    ? "text-[#50C2FF] font-semibold"
                                    : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                                }`}
                                onClick={() => {
                                  handleDropdownItemClick("Featured Hostel");
                                  handleItemClick("Featured Hostel");
                                }}
                              >
                                Featured Hostel
                              </Link>
                            </li>
                            <li>
                              <Link
                                href="/superadmin/dashboard/travel-activity"
                                className={`text-sm font-poppins ${
                                  activeDropdownItem === "Travel by Activity"
                                    ? "text-[#50C2FF] font-semibold"
                                    : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                                }`}
                                onClick={() => {
                                  handleDropdownItemClick("Travel by Activity");
                                  handleItemClick("Travel by Activity");
                                }}
                              >
                                Travel by Activity
                              </Link>
                            </li>

                            <li>
                              <Link
                                href="/superadmin/dashboard/split-bill"
                                className={`text-sm font-poppins ${
                                  activeDropdownItem ===
                                  "Split bill fairfare, Mix mate, Mix ride"
                                    ? "text-[#50C2FF] font-semibold"
                                    : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                                }`}
                                onClick={() => {
                                  handleDropdownItemClick(
                                    "Split bill fairfare, Mix mate, Mix ride"
                                  );
                                  handleItemClick(
                                    "Split bill fairfare, Mix mate, Mix ride"
                                  );
                                }}
                              >
                                Split bill fairfare, Mix mate, Mix ride
                              </Link>
                            </li>
                            <li>
                              <Link
                                href="/superadmin/dashboard/event"
                                className={`text-sm font-poppins ${
                                  activeDropdownItem === "Event"
                                    ? "text-[#50C2FF] font-semibold"
                                    : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                                }`}
                                onClick={() => {
                                  handleDropdownItemClick("Event");
                                  handleItemClick("Event");
                                }}
                              >
                                Event
                              </Link>
                            </li>
                            <li>
                              <Link
                                href="/superadmin/dashboard/content-blog"
                                className={`text-sm font-poppins ${
                                  activeDropdownItem === "Landing-Blog"
                                    ? "text-[#50C2FF] font-semibold"
                                    : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                                }`}
                                onClick={() => {
                                  handleDropdownItemClick("Landing-Blog");
                                  handleItemClick("Landing-Blog");
                                }}
                              >
                                Landing-Blog
                              </Link>
                            </li>
                          </ul>
                        )}
                      </li>
                      <li>
                        <button
                          onClick={() =>
                            setIsOtherContentOpen(!isOtherContentOpen)
                          }
                          className={`flex items-centercursor-pointer text-sm font-poppins rounded-lg ${
                            activeItem === "Other Content" || isOtherContentOpen
                              ? "text-[#50C2FF] font-semibold"
                              : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                          } `}
                        >
                          Other Content
                          <RiArrowDropDownLine
                            className={`text-2xl float-end transition-transform ${
                              isOtherContentOpen ? "rotate-180" : ""
                            }`}
                          />
                        </button>
                        {isOtherContentOpen && (
                          <ul className="mt-2 ml-4 space-y-4">
                            <li>
                              <Link
                                href="/superadmin/dashboard/about-us"
                                className={`text-sm font-poppins ${
                                  activeDropdownItem === "About Us"
                                    ? "text-[#50C2FF] font-semibold"
                                    : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                                }`}
                                onClick={() => {
                                  handleDropdownItemClick("About Us");
                                  handleItemClick("About Us");
                                }}
                              >
                                About Us
                              </Link>
                            </li>
                            <li>
                              <Link
                                href="/superadmin/dashboard/awards"
                                className={`text-sm font-poppins ${
                                  activeDropdownItem === "Awards"
                                    ? "text-[#50C2FF] font-semibold"
                                    : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                                }`}
                                onClick={() => {
                                  handleDropdownItemClick("Awards");
                                  handleItemClick("Awards");
                                }}
                              >
                                Awards
                              </Link>
                            </li>
                            <li>
                              <Link
                                href="/superadmin/dashboard/help"
                                className={`text-sm font-poppins ${
                                  activeDropdownItem === "Help"
                                    ? "text-[#50C2FF] font-semibold"
                                    : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                                }`}
                                onClick={() => {
                                  handleDropdownItemClick("Help");
                                  handleItemClick("Help");
                                }}
                              >
                                Help
                              </Link>
                            </li>
                            <li>
                              <Link
                                href="/superadmin/dashboard/booking-guarantee"
                                className={`text-sm font-poppins ${
                                  activeDropdownItem === "Booking Guarantee"
                                    ? "text-[#50C2FF] font-semibold"
                                    : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                                }`}
                                onClick={() => {
                                  handleDropdownItemClick("Booking Guarantee");
                                  handleItemClick("Booking Guarantee");
                                }}
                              >
                                Booking Guarantee
                              </Link>
                            </li>
                          

                            <li>
                              <Link
                                href="/superadmin/dashboard/booking-refund-policy"
                                className={`text-sm font-poppins ${
                                  activeDropdownItem ===
                                  "Booking Refund Policy"
                                    ? "text-[#50C2FF] font-semibold"
                                    : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                                }`}
                                onClick={() => {
                                  handleDropdownItemClick(
                                    "Booking Refund Policy"
                                  );
                                  handleItemClick(
                                    "Booking Refund Policy"
                                  );
                                }}
                              >
                                Booking Refund Policy
                              </Link>
                            </li>
                            <li>
                              <Link
                                href="/superadmin/dashboard/privacy-policy"
                                className={`text-sm font-poppins ${
                                  activeDropdownItem === "Privacy Policy"
                                    ? "text-[#50C2FF] font-semibold"
                                    : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                                }`}
                                onClick={() => {
                                  handleDropdownItemClick("Privacy Policy");
                                  handleItemClick("Privacy Policy");
                                }}
                              >
                                Privacy Policy
                              </Link>
                            </li>
                            <li>
                              <Link
                                href="/superadmin/dashboard/terms&conditions"
                                className={`text-sm font-poppins ${
                                  activeDropdownItem === "Terms & Conditions"
                                    ? "text-[#50C2FF] font-semibold"
                                    : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                                }`}
                                onClick={() => {
                                  handleDropdownItemClick("Terms & Conditions");
                                  handleItemClick("Terms & Conditions");
                                }}
                              >
                                Terms & Conditions
                              </Link>
                            </li>

                            <li>
                              <Link
                                href="/superadmin/dashboard/faqs"
                                className={`text-sm font-poppins ${
                                  activeDropdownItem === "Faqs"
                                    ? "text-[#50C2FF] font-semibold"
                                    : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                                }`}
                                onClick={() => {
                                  handleDropdownItemClick("Faqs");
                                  handleItemClick("Faqs");
                                }}
                              >
                                Faqs
                              </Link>
                            </li>
                          </ul>
                        )}
                      </li>

                      <li>
                        <Link
                          href="/superadmin/dashboard/offer-banner"
                          className={`text-sm font-poppins ${
                            activeDropdownItem === "Offer Banner"
                              ? "text-[#50C2FF] font-semibold"
                              : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                          }`}
                          onClick={() => {
                            handleDropdownItemClick("Offer Banner");
                            handleItemClick("Offer Banner");
                          }}
                        >
                          Offer Banner
                        </Link>
                      </li>
                      <li>
                        <Link
                          href="/superadmin/dashboard/blog"
                          className={`text-sm font-poppins ${
                            activeDropdownItem === "Blog"
                              ? "text-[#50C2FF] font-semibold"
                              : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                          }`}
                          onClick={() => {
                            handleDropdownItemClick("Blog");
                            handleItemClick("Blog");
                          }}
                        >
                          Blog
                        </Link>
                      </li>
                      <li>
                        <Link
                          href="/superadmin/dashboard/contact"
                          className={`text-sm font-poppins ${
                            activeDropdownItem === "Contact Us"
                              ? "text-[#50C2FF] font-semibold"
                              : "text-gray-600 dark:text-[#ffffff] hover:text-[#50C2FF] dark:hover:text-[#50C2FF]"
                          }`}
                          onClick={() => {
                            handleDropdownItemClick("Contact Us"),
                              handleItemClick("Contact Us");
                          }}
                        >
                          Contact Us
                        </Link>
                      </li>
                    </ul>
                  )}
                </li>
              </div>

              

              <div
                className={`px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${
                  activeItem === "Booking" ? "border-l-4 border-[#50C2FF] " : ""
                }`}
              >
                <li
                  className={`cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${
                    activeItem === "Booking" ? "bg-[#50C2FF] text-white" : ""
                  }`}
                  onClick={() => {handleItemClick("Booking");router.push("/superadmin/dashboard/booking")}}
                >
                  <Link
                    href="/superadmin/dashboard/booking"
                    className={`flex items-center gap-2 hover:text-white text-gray-700  dark:text-[#ffffff] px-3 py-2 rounded-md  ${
                      activeItem === "Booking"
                        ? " text-white font-semibold"
                        : ""
                    }`}
                  >
                    <LuCalendarDays className="text-xl" /> Booking
                  </Link>
                </li>
              </div>
              <div
                className={`px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${
                  activeItem === "Hostel" ? "border-l-4 border-[#50C2FF] " : ""
                }`}
              >
                <li
                  className={`cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${
                    activeItem === "Hostel" ? "bg-[#50C2FF] text-white" : ""
                  }`}
                  onClick={() => {handleItemClick("Hostel"); router.push("/superadmin/dashboard/hostel")}}
                >
                  <Link
                    href="/superadmin/dashboard/hostel"
                    className={`flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${
                      activeItem === "Hostel" ? " text-white font-semibold" : ""
                    }`}
                  >
                    <GiKeyCard className="text-xl" /> Hostel
                  </Link>
                </li>
              </div>

              <div
                className={`px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${
                  activeItem === "Communication"
                    ? "border-l-4 border-[#50C2FF] "
                    : ""
                }`}
              >
                <li
                  className={`cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${
                    activeItem === "Communication"
                      ? "bg-[#50C2FF] text-white"
                      : ""
                  }`}
                  onClick={() => {handleItemClick("Communication"); router.push("/superadmin/dashboard/communication")}}
                >
                  <Link
                    href="/superadmin/dashboard/communication"
                    className={`flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${
                      activeItem === "Communication"
                        ? " text-white font-semibold"
                        : ""
                    }`}
                  >
                    <TbBrandWechat className="text-xl" /> Communication
                  </Link>
                </li>
              </div>
              <div
                className={`px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${
                  activeItem === "Notification"
                    ? "border-l-4 border-[#50C2FF] "
                    : ""
                }`}
              >
                <li
                  className={`cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${
                    activeItem === "Notification"
                      ? "bg-[#50C2FF] text-white"
                      : ""
                  }`}
                  onClick={() => {handleItemClick("Notification"); router.push("/superadmin/dashboard/notifications")}}
                >
                  <Link
                    href="/superadmin/dashboard/notifications"
                    className={`flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${
                      activeItem === "Notification"
                        ? " text-white font-semibold"
                        : ""
                    }`}
                  >
                    <LuBellPlus className="text-xl" /> Notification
                  </Link>
                </li>
              </div>

              <div
                className={`px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${
                  activeItem === "Feedback"
                    ? "border-l-4 border-[#50C2FF] "
                    : ""
                }`}
              >
                <li
                  className={`cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${
                    activeItem === "Feedback" ? "bg-[#50C2FF] text-white" : ""
                  }`}
                  onClick={() => {handleItemClick("Feedback"); router.push("/superadmin/dashboard/feedback")}}
                >
                  <Link
                    href="/superadmin/dashboard/feedback"
                    className={`flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${
                      activeItem === "Feedback"
                        ? " text-white font-semibold"
                        : ""
                    }`}
                  >
                    <MdOutlineRateReview className="text-xl" /> Feedback
                  </Link>
                </li>
              </div>

              <div
                className={`px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${
                  activeItem === "Support" ? "border-l-4 border-[#50C2FF] " : ""
                }`}
              >
                <li
                  className={`cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${
                    activeItem === "Support" ? "bg-[#50C2FF] text-white" : ""
                  }`}
                  onClick={() => {handleItemClick("Support"); router.push("/superadmin/dashboard/support")}}
                >
                  <Link
                    href="/superadmin/dashboard/support"
                    className={`flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${
                      activeItem === "Support"
                        ? " text-white font-semibold"
                        : ""
                    }`}
                  >
                    <FaHeadset className="text-xl" /> Support
                  </Link>
                </li>
              </div>
              

              <div
                className={`px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${
                  activeItem === "Profile" ? "border-l-4 border-[#50C2FF] " : ""
                }`}
              >
                <li
                  className={`cursor-pointer text-sm font-poppins h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${
                    activeItem === "Profile" ? "bg-[#50C2FF] text-white" : ""
                  }`}
                  onClick={() => {handleItemClick("Profile"); router.push("/superadmin/dashboard/profile")}}
                >
                  <Link
                    href="/superadmin/dashboard/profile"
                    className={`flex items-center gap-2 hover:text-white text-gray-700 dark:text-[#ffffff] px-3 py-2 rounded-md  ${
                      activeItem === "Profile"
                        ? " text-white font-semibold"
                        : ""
                    }`}
                  >
                    <FaRegCircleUser className="text-xl" /> Profile
                  </Link>
                </li>
              </div>
  
              <div
                className={`px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${
                  activeItem === "Management"
                    ? "border-l-4 border-[#50C2FF] "
                    : ""
                }`}
              >
                <li className="mt-0.5  transition duration-300 ease-in-out transform">
                  <button
                    onClick={() => setIsManagementOpen(!isManagementOpen)}
                    className={`flex items-center gap-2 cursor-pointer text-sm font-poppins h-[48px] rounded-lg pr-1 pl-[32px] ${
                      activeItem === "Management" || isManagementOpen
                        ? "bg-[#50C2FF] text-white font-semibold"
                        : "text-gray-700 dark:text-[#ffffff] hover:bg-[#50C2FF] hover:text-white hover:font-semibold"
                    } transition duration-300 ease-in-out transform hover:scale-105`}
                  >
                    <LuUserRoundCog className="text-xl" /> Management
                    <RiArrowDropDownLine
                      className={`text-2xl mt-0.5 float-end transition-transform ${
                        isManagementOpen ? "rotate-180" : ""
                      }`}
                    />
                  </button>
                  {isManagementOpen && (
                    <ul className="mt-2 ml-14 space-y-4">
                      <li>
                        <Link
                          href="/superadmin/dashboard/management-admin"
                          className={`text-sm font-poppins ${
                            activeDropdownItem === "Admin"
                              ? "text-[#50C2FF] font-semibold"
                              : "text-gray-600 dark:text-[#ffffff] dark:hover:text-[#50C2FF] hover:text-[#50C2FF]"
                          }`}
                          onClick={() => {
                            handleDropdownItemClick("Admin");
                            handleItemClick("Admin");
                          }}
                        >
                          Admin
                        </Link>
                      </li>

                      <li>
                        <Link
                          href="/superadmin/dashboard/management-audit"
                          className={`text-sm font-poppins ${
                            activeDropdownItem === "Audit Logs"
                              ? "text-[#50C2FF] font-semibold"
                              : "text-gray-600 hover:text-[#50C2FF] dark:text-[#ffffff] dark:hover:text-[#50C2FF]"
                          }`}
                          onClick={() => {
                            handleDropdownItemClick("Audit Logs");
                            handleItemClick("Audit Logs");
                          }}
                        >
                          Audit Logs
                        </Link>
                      </li>
                      <li>
                        <Link
                          href="/superadmin/dashboard/management-status"
                          className={`text-sm font-poppins ${
                            activeDropdownItem === "Status"
                              ? "text-[#50C2FF] font-semibold"
                              : "text-gray-600 hover:text-[#50C2FF] dark:text-[#ffffff] dark:hover:text-[#50C2FF]"
                          }`}
                          onClick={() => {
                            handleDropdownItemClick("Status");
                            handleItemClick("Status");
                          }}
                        >
                          Status
                        </Link>
                      </li>
                    </ul>
                  )}
                </li>
              </div>

              {/* <div
                className={`px-6 bg-white hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${
                  activeItem === "Payment" ? "border-l-4 border-[#50C2FF] " : ""
                }`}
              >
                <li
                  className={`cursor-pointer text-sm h-[48px] flex text-black rounded-lg px-5 hover:bg-[#50C2FF] hover:text-white mt-0.5 hover:font-semibold transition duration-300 ease-in-out transform hover:scale-105 ${
                    activeItem === "Payment" ? "bg-[#50C2FF] text-white" : ""
                  }`}
                  onClick={() => handleItemClick("Payment")}
                >
                  <Link
                    href="/superadmin/dashboard/support"
                    className="flex items-center gap-2 hover:text-white text-gray-700 px-3 py-2 rounded-md"
                  >
                    <FaHandHoldingUsd className="text-xl" /> Payment
                  </Link>
                </li>
              </div> */}

              <div
                className={`px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${
                  activeItem === "Payment" ? "border-l-4 border-[#50C2FF] " : ""
                }`}
              >
                <li className="mt-0.5 ml-1.5  transition duration-300 ease-in-out transform">
                  <button
                    onClick={() => setIsPaymentOpen(!isPaymentOpen)}
                    className={`flex items-center  gap-1.5 cursor-pointer text-sm font-poppins h-[48px] rounded-lg pr-8  pl-[26px] ${
                      activeItem === "Payment" || isPaymentOpen
                        ? "bg-[#50C2FF] text-white font-semibold"
                        : "text-gray-700 hover:bg-[#50C2FF] hover:text-white hover:font-semibold dark:text-[#ffffff]"
                    } transition duration-300 ease-in-out transform hover:scale-105 `}
                  >
                    <FaHandHoldingUsd className="text-xl" /> Payment
                    <RiArrowDropDownLine
                      className={`text-2xl float-end transition-transform ${
                        isPaymentOpen ? "rotate-180" : ""
                      }`}
                    />
                  </button>
                  {isPaymentOpen && (
                    <ul className="mt-2 ml-14 space-y-4">
                      <li>
                        <Link
                          href="/superadmin/dashboard/payment-booking"
                          className={`text-sm font-poppins ${
                            activeDropdownItem === "Bookings"
                              ? "text-[#50C2FF] font-semibold"
                              : "text-gray-600  hover:text-[#50C2FF] dark:text-[#ffffff] dark:hover:text-[#50C2FF]"
                          }`}
                          onClick={() => {
                            handleDropdownItemClick("Bookings");
                            handleItemClick("Bookings");
                          }}
                        >
                          Bookings
                        </Link>
                      </li>

                      <li>
                        <Link
                          href="/superadmin/dashboard/payment-subscription"
                          className={`text-sm font-poppins ${
                            activeDropdownItem === "Subscription"
                              ? "text-[#50C2FF] font-semibold"
                              : "text-gray-600 hover:text-[#50C2FF] dark:text-[#ffffff] dark:hover:text-[#50C2FF]"
                          }`}
                          onClick={() => {
                            handleDropdownItemClick("Subscription");
                            handleItemClick("Subscription");
                          }}
                        >
                          Subscription
                        </Link>
                      </li>
                      <li >
                        <Link
                          href="/superadmin/dashboard/payment-event"
                          className={`text-sm font-poppins ${
                            activeDropdownItem === "Event"
                              ? "text-[#50C2FF] font-semibold"
                              : "text-gray-600 hover:text-[#50C2FF] dark:text-[#ffffff] dark:hover:text-[#50C2FF]"
                          }`}
                          onClick={() => {
                            handleDropdownItemClick("Event");
                            handleItemClick("Event");
                          }}
                        >
                          Event
                        </Link>
                      </li>
                    </ul>
                  )}
                </li>
              </div>
            </ul>
          </div>

          <div className={`mt-auto w-full ${isCollapsed ? "px-0" : "px-0"}`}>
            <hr className="border-1.5 p-0" />
            <ul className="w-full text-sm font-poppins  flex flex-col h-[150px] mt-10 md:mt-5 ">
              <div
                className={`px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${
                  activeItem === "Theme" ? "border-l-4 border-[#50C2FF] " : ""
                }`}
              >
                <li
                  className={`flex items-center px-7 cursor-pointer first:mt-0 text-sm font-poppins h-[48px]  gap-x-2 text-gray-600 dark:text-[#ffffff]  rounded-lg hover:bg-[#50C2FF] hover:text-white hover:font-semibold  ${
                    activeItem === "Theme" ? "bg-[#50C2FF] text-white" : ""
                  }`}
                  
                >
                  <ThemeSwitcher  />
                </li>
              </div>

              <div
                className={`px-6 bg-white dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${
                  activeItem === "Settings"
                    ? "border-l-4 border-[#50C2FF] "
                    : ""
                }`}
              >
                <li
                  className={`flex items-center px-7 cursor-pointer first:mt-0  text-sm font-poppins rounded-lg h-[48px]  gap-x-2  text-gray-600 dark:text-[#ffffff] hover:bg-[#50C2FF] hover:font-semibold hover:text-white  ${
                    isCollapsed ? "justify-center" : "justify-start"
                  }`}
                >
                  <FiSettings className={` ${isCollapsed ? "" : "text-xl"}`} />{" "}
                  {isCollapsed ? "" : "Settings "}
                </li>
              </div>

              <div
                className={`px-6 bg-white  dark:bg-[#0D0D0D] hover:border-l-4 mt-0.5  hover:border-[#50C2FF] ${
                  activeItem === "Logout" ? "border-l-4 border-[#50C2FF] " : ""
                }`}
              >
                <li
                  className={`cursor-pointer text-sm font-poppins h-[48px]  px-7 flex items-center rounded-lg gap-x-2 text-gray-600 dark:text-[#ffffff] hover:bg-[#50C2FF] hover:font-semibold hover:text-white  first:mt-0${
                    isCollapsed ? "justify-center" : "justify-start"
                  }`}
                  onClick={handleLogout}
                >
                  <BiLogOutCircle
                    className={`${isCollapsed ? "" : "text-xl"}`}
                  />
                  {isCollapsed ? "" : "Logout "}
                </li>
              </div>
            </ul>
          </div>
        </div>
      </Box>
    </div>
  );
};

export default SuperSidebar;
