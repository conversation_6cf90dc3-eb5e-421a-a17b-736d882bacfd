{"name": "mixdorm-backend", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon app.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"aws-sdk": "^2.1676.0", "axios": "^1.7.7", "bcrypt": "^5.1.1", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.4.5", "express": "^4.19.2", "express-basic-auth": "^1.2.1", "express-xml-bodyparser": "^0.3.0", "firebase-admin": "^12.5.0", "fs": "^0.0.1-security", "generate-password": "^1.7.1", "http-status-codes": "^2.3.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.30.1", "mongoose": "^8.4.1", "multer": "^1.4.2", "multer-s3": "^2.9.0", "node-cron": "^3.0.3", "nodemailer": "^6.9.13", "nodemon": "^3.1.3", "passport": "^0.7.0", "passport-apple": "^2.0.2", "passport-facebook": "^3.0.0", "passport-google-oauth2": "^0.2.0", "passport-jwt": "^4.0.1", "path": "^0.12.7", "razorpay": "^2.9.4", "slugify": "^1.6.6", "socket.io": "^4.8.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1", "xmlbuilder2": "^3.1.1"}}