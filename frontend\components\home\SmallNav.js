import Image from "next/image";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { MdOutlineNotificationsActive } from "react-icons/md";
import { ChevronDown, Globe, Grip, X } from "lucide-react";
import dynamic from "next/dynamic";
import { motion, AnimatePresence } from "framer-motion";
import { Box, Divider, Drawer, Paper } from "@mui/material";
import { getItemLocalStorage } from "@/utils/browserSetting";
import { useNavbar } from "../home/<USER>";
// import { Toaster, toast } from 'sonner'
import { getPropertyCountApi } from "@/services/webflowServices";
import MenuPopup from "../popup/menuPopup";
import MenuPopupMobile from "../popup/menuPopupMobile";
import toast from "../toast/toast";
import { IoListSharp } from "react-icons/io5";
import { FaRegUser } from "react-icons/fa6";

const CountryModal = dynamic(() => import("../model/countryModel"), {
  ssr: false,
});
const LoginPopup = dynamic(() => import("../popup/loginPopup"), { ssr: false });
// const MenuPopup = dynamic(() => import("../popup/menuPopup"), { ssr: false });
const Noticeboard = dynamic(() => import("../model/noticeboard"), {
  ssr: false,
});
const MyProfile = dynamic(() => import("../model/myProfile"), { ssr: false });

const Navbar = () => {
  const [openCountryModal, setOpenCountryModal] = useState(false);
  const handleOpenCountryModal = () => setOpenCountryModal(true);
  const handleCloseCountryModal = () => setOpenCountryModal(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [showLoginPopup, setShowLoginPopup] = useState(false);
  const [hoveringTop, setHoveringTop] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isMenuOpenMobile, setIsMenuOpenMobile] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [hasToken, setHasToken] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [roles, setRole] = useState(false);
  const [isMyProfileOpen, setIsMyProfileOpen] = useState(false);
  const [flagUrl, setFlagUrl] = useState("");
  const [currencyCode, setCurrencyCode] = useState("");

  const toggleMyProfile = () => setIsMyProfileOpen(!isMyProfileOpen);
  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  // const toggleMenuMobile = () => setIsMenuOpenMobile(!isMenuOpenMobile);
  const toggleMenuMobile = () => {
    if (!token || role !== "user") {
      toast.error("Please Login !", {
        subText: "You need to be Logged In",
      });
    } else {
      setIsMenuOpenMobile(!isMenuOpenMobile);
    }
  };

  const toggleLoginPopup = () => setShowLoginPopup(!showLoginPopup);

  const updateTokenState = () => {
    const token = getItemLocalStorage("token");
    setHasToken(!!token);
  };

  const updateRoleState = () => {
    const role = getItemLocalStorage("role");
    setRole(role);
  };

  const updateCountry = () => {
    const flag = getItemLocalStorage("selectedCountryFlag");
    const code = getItemLocalStorage("selectedCurrencyCode");

    setFlagUrl(flag);
    setCurrencyCode(code);
    updateCountry2(flag, code);
  };

  useEffect(() => {
    updateTokenState();
  }, []);

  useEffect(() => {
    updateRoleState();
  }, []);

  useEffect(() => {
    const handleStorageChange = (event) => {
      if (event.key === "token") {
        updateTokenState();
      }
    };

    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);

  useEffect(() => {
    const handleStorageChange = (event) => {
      if (event.key === "role") {
        updateRoleState();
      }
    };

    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
    };
  }, []);

  useEffect(() => {
    const originalSetItem = localStorage.setItem;
    localStorage.setItem = function (key) {
      const event = new Event("itemInserted");
      originalSetItem.apply(this, arguments);
      if (key === "token") {
        window.dispatchEvent(event);
      }
    };

    const handleItemInserted = () => {
      updateTokenState();
    };

    window.addEventListener("itemInserted", handleItemInserted);

    return () => {
      window.removeEventListener("itemInserted", handleItemInserted);
      localStorage.setItem = originalSetItem;
    };
  }, []);

  useEffect(() => {
    const originalSetItem = localStorage.setItem;
    localStorage.setItem = function (key) {
      const event = new Event("itemInserted");
      originalSetItem.apply(this, arguments);
      if (key === "token") {
        window.dispatchEvent(event);
      }
    };

    const handleItemInserted = () => {
      updateRoleState();
    };

    window.addEventListener("itemInserted", handleItemInserted);

    return () => {
      window.removeEventListener("itemInserted", handleItemInserted);
      localStorage.setItem = originalSetItem;
    };
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 0);
    };

    const handleMouseMove = (event) => {
      setHoveringTop(event.clientY < 85);
    };

    window.addEventListener("scroll", handleScroll);
    window.addEventListener("mousemove", handleMouseMove);

    return () => {
      window.removeEventListener("scroll", handleScroll);
      window.removeEventListener("mousemove", handleMouseMove);
    };
  }, []);

  useEffect(() => {
    const selectedCountryFlag = getItemLocalStorage("selectedCountryFlag");
    const selectedCurrencyCode = getItemLocalStorage("selectedCurrencyCode");

    if (selectedCountryFlag) {
      const url = selectedCountryFlag;
      setFlagUrl(url);
      setCurrencyCode(selectedCurrencyCode);
    }
  }, []);
  // Notice Board Modal
  const [openNoticeBoard, setOpenNoticeBoard] = useState(false);
  const [openNoticeBoardDetails, setOpenNoticeBoardDetails] = useState(false);

  const handleOpenNoticeBoard = async () => {
    if (getItemLocalStorage("token") && role === "user") {
      const propertyCountResponse = await getPropertyCountApi();
      if (propertyCountResponse?.data?.data?.totalBooking > 0) {
        setOpenNoticeBoardDetails(true);
      } else if (propertyCountResponse?.data?.data?.totalBooking === 0) {
        setOpenNoticeBoard(true);
      }
    } else {
      toast.error("Please Login !", {
        subText: "You need to be Logged In",
      });
    }
  };
  const handleCloseNoticeBoard = () => {
    setOpenNoticeBoard(false);
    setOpenNoticeBoardDetails(false);
  };

  // Mobile Drawer
  const [openDrawer, setOpenDrawer] = useState(false);

  const toggleDrawer = (newOpen) => () => {
    setOpenDrawer(newOpen);
  };

  const { updateCountry2, token, role } = useNavbar();

  // Update country when the component is mounted
  useEffect(() => {
    const selectedCountryFlag = getItemLocalStorage("selectedCountryFlag");
    const selectedCurrencyCode = getItemLocalStorage("selectedCurrencyCode");

    if (selectedCountryFlag && selectedCurrencyCode) {
      updateCountry2(selectedCountryFlag, selectedCurrencyCode);
    }
    setFlagUrl(selectedCountryFlag);
    setCurrencyCode(selectedCurrencyCode);
  }, [updateCountry2]);
  return (
    <>
      <section
        className={`w-full duration-300 ease-in-out sticky top-0 z-50 ${
          scrolled && !hoveringTop ? "-top-10" : "sticky top-0 z-50"
        }`}
      >
        {" "}
        <div className='w-full flex lg:hidden bg-white bg-opacity-10 backdrop-blur-sm min-h-[70px] items-center'>
          <div className='relative z-20 flex items-center justify-between py-4 container'>
            <div className='w-[30%] flex items-center gap-x-3'>
              <Link href='/' rel='canonical' prefetch={false}>
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Mixdorm-white.svg`}
                  width={155}
                  height={40}
                  alt='Content Ai'
                  title='Content Ai'
                  className='max-w-[110px] md:max-w-[155px] md:max-h-24 relative z-50 w-fit object-contain bg-blend-color-burn cursor-pointer hover:scale-95 duration-500 ease-in-out'
                  loading='lazy'
                />
              </Link>
            </div>
            <div className='w-[55%] flex justify-end items-center'>
              <ul className='flex items-center justify-center md:gap-x-5 xs:gap-x-3 gap-x-1.5'>
                <li className=''>
                  <Link
                    href='/owner/list-your-hostel'
                    passHref
                    className='xs:text-xs text-[11px] text-center font-manrope min-[350px]:min-w-[100px] min-[350px]:w-full flex items-center justify-center font-bold bg-primary-blue cursor-pointer min-[350px]:rounded-9xl rounded-md text-black duration-300 ease-in-out p-1.5'
                    prefetch={false}
                  >
                    {/* Show text above 350px */}
                    <span className='max-[350px]:hidden'>List Your Hostel</span>

                    {/* Show icon below 350px */}
                    <span className='hidden max-[350px]:block'>
                      <IoListSharp size={16} />
                    </span>
                  </Link>
                </li>
                <li className='relative'>
                  <button
                    // href="0"
                    rel='canonical'
                    aria-label='Open Notice Board'
                    className='text-sm font-manrope items-center font-bold cursor-pointer rounded-9xl text-white duration-300 ease-in-out gap-x-2 flex'
                    onClick={handleOpenNoticeBoard}
                  >
                    <MdOutlineNotificationsActive
                      size={26}
                      className='font-normal xs:h-[26px] xs:w-[26px] h-[22px] w-[22px] text-white'
                    />{" "}
                  </button>
                  <span className='absolute left-4 top-[-10px] min-h-3.5 min-w-3.5 max-h-4.5 max-w-4.5 animate-ping rounded-full bg-primary-blue'></span>
                  <span className='absolute flex items-center justify-center left-4 top-[-10px] min-h-3.5 min-w-3.5 max-h-4.5 max-w-4.5 rounded-full bg-primary-blue text-[10px] font-medium text-black text-center leading-none'>
                    2
                  </span>
                </li>

                <li>
                  {!token ? (
                    <button
                      // href="#"
                      className='text-sm font-manrope flex items-center font-bold cursor-pointer text-white duration-300 ease-in-out gap-x-2'
                      aria-label='Login'
                      onClick={toggleLoginPopup} // Open Login Popup if no token
                    >
                      <FaRegUser
                        size={20}
                        className='font-bold xs:h-5 xs:w-5 h-4 w-4 text-white'
                      />

                      <span className='hidden md:block'>Login</span>
                    </button>
                  ) : token && role === "user" ? (
                    // <button
                    //   // href="#"
                    //   rel="canonical"
                    //   aria-label="Profile"
                    //   className="text-sm font-manrope flex items-center font-bold cursor-pointer text-white duration-300 ease-in-out gap-x-2"
                    //   onClick={toggleMyProfile} // Open MyProfile if token exists
                    // >
                    //   <FaRegUser size={20} />
                    // </button>
                    <div className='relative'>
                      <button
                        className={`text-sm font-manrope flex items-center justify-center font-bold cursor-pointer duration-300 ease-in-out gap-x-2  rounded-full h-7 w-7 bg-primary-blue text-black`}
                        onMouseEnter={() => setShowTooltip(true)}
                        onMouseLeave={() => setShowTooltip(false)}
                      >
                        {getItemLocalStorage("name")?.charAt(0)?.toUpperCase() || 'A'}
                      </button>
                      <AnimatePresence>
                        {showTooltip && (
                          <motion.div
                            initial={{ opacity: 0, y: 5 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: 5 }}
                            className='absolute top-full left-1/2 transform -translate-x-1/2 bg-black text-primary-blue text-xs rounded py-1 px-2 whitespace-nowrap'
                          >
                            {getItemLocalStorage("name")}
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  ) : (
                    <Link
                      href='#'
                      className='text-sm font-manrope flex items-center font-bold cursor-pointer text-white duration-300 ease-in-out gap-x-2 '
                      onClick={toggleLoginPopup} // Open Login Popup if no token
                      prefetch={false}
                    >
                      <FaRegUser size={20} />
                      <span className='hidden md:block'>Login</span>
                    </Link>
                  )}
                </li>
                <li className=''>
                  <button
                    type='button'
                    className='text-sm font-manrope items-center font-bold cursor-pointer text-white duration-300 ease-in-out gap-x-2 flex'
                    onClick={handleOpenCountryModal}
                    aria-label='Select country'
                  >
                    {flagUrl ? (
                      <div className='relative xs:w-6 xs:h-6 w-5 h-5 rounded-full overflow-hidden'>
                        <Image
                          src={flagUrl}
                          alt='Country Flag'
                          fill
                          className='object-cover rounded-full'
                          sizes='24px'
                          loading='lazy'
                        />
                      </div>
                    ) : (
                      <Globe size={20} />
                    )}
                  </button>
                </li>
                <li className='flex'>
                  {/* For mobile screens (0-575px) */}
                  <button
                    className='sm:hidden text-sm font-manrope font-bold cursor-pointer text-white duration-300 ease-in-out'
                    aria-label='Mobile Menu'
                    onClick={toggleMenuMobile}
                  >
                    <Grip size={24} className='xs:w-6 xs:h-6 w-5 h-5' />
                  </button>
                  {/* For larger screens (576px and above) */}
                  <button
                    className='hidden sm:block text-sm font-manrope font-bold cursor-pointer text-white duration-300 ease-in-out'
                    onClick={toggleMenu}
                  >
                    <Grip size={24} />
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      <Drawer
        open={openDrawer}
        onClose={toggleDrawer(false)}
        className='nav-bar-humburger fadeInLeft animated'
        anchor='left'
      >
        <Box
          sx={{ width: 346 }}
          role='presentation'
          borderRadius={10}
          onClick={toggleDrawer(false)}
        >
          <Paper
            sx={{
              width: 326,
              background: "#fff",
              borderRadius: "0 0 10px 10px",
              height: "100vh",
              elevation: 0,
            }}
            elevation={0}
            borderRadius={10}
          >
            <Link
              href='/'
              rel='canonical'
              className='p-4 block'
              prefetch={false}
            >
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/logo.svg`}
                width={155}
                height={40}
                alt='Content Ai'
                title='Content Ai'
                className='max-w-[110px] md:max-w-[155px] md:max-h-24 relative z-50 w-fit object-contain bg-blend-color-burn cursor-pointer hover:scale-95 duration-500 ease-in-out'
                loading='lazy'
              />
            </Link>
            <button
              className='absolute top-4 right-4'
              onClick={toggleDrawer(false)}
            >
              <X size={22} />
            </button>
            <Divider />
            <div className='p-3'>
              <label
                htmlFor=''
                className='text-sm sm:text-base flex items-center gap-4 mb-4'
              >
                <Globe size={20} /> Select Currency
              </label>
              <button
                type='button'
                className='flex items-center gap-x-2 text-sm font-manrope font-bold cursor-pointer text-black duration-300 ease-in-out'
                onClick={handleOpenCountryModal}
              >
                {flagUrl ? (
                  <Image
                    src={flagUrl}
                    alt='Country Flag'
                    width={20}
                    height={20}
                    loading='lazy'
                  />
                ) : (
                  <Globe size={20} />
                )}
                {currencyCode ? currencyCode : "Country"}
                <ChevronDown size={18} />
              </button>
            </div>

            <div className='p-3'>
              <Link
                href='#'
                rel='canonical'
                className='text-sm font-manrope items-center font-bold cursor-pointer rounded-9xl text-black duration-300 ease-in-out gap-x-2 flex'
                onClick={handleOpenNoticeBoard}
                prefetch={false}
              >
                <MdOutlineNotificationsActive
                  size={20}
                  className='font-normal text-black'
                />{" "}
                Noticeboard
              </Link>
            </div>

            <div className='p-3'>
              <Link
                href='/owner/hostel-login'
                rel='canonical'
                className='block px-5 py-3 text-center font-manrope text-base font-bold bg-primary-blue cursor-pointer rounded-4xl text-black duration-300 ease-in-out'
                prefetch={false}
              >
                List Your Hostel
              </Link>
            </div>
          </Paper>
        </Box>
      </Drawer>

      <Noticeboard
        close={handleCloseNoticeBoard}
        open={openNoticeBoard}
        openNoticeBoardDetails={openNoticeBoardDetails}
      />
      {isMenuOpen && (
        <MenuPopup
          isOpen={isMenuOpen}
          toggleMenu={toggleMenu}
          updateTokenState={updateTokenState}
          toggleLoginPopup={toggleLoginPopup}
          updateRoleState={updateRoleState}
        />
      )}
      {isMenuOpenMobile && (
        <MenuPopupMobile
          isOpen={isMenuOpenMobile}
          toggleMenu={toggleMenuMobile}
          updateTokenState={updateTokenState}
          toggleLoginPopup={toggleLoginPopup}
          updateRoleState={updateRoleState}
        />
      )}

      <MyProfile
        isMenuOpen={isMyProfileOpen}
        toggleMenu={toggleMyProfile}
        updateTokenState={updateTokenState}
        updateRoleState={updateRoleState}
      />
      <LoginPopup isOpen={showLoginPopup} onClose={toggleLoginPopup} />

      <CountryModal
        openCountryModal={openCountryModal}
        handleCloseCountryModal={handleCloseCountryModal}
        updateCountry={updateCountry}
      />
    </>
  );
};

export default Navbar;
