import eventBookingModel from '../models/eventBooking.js';
import eventModel from '../models/events.js';

// Service to book an event
export const bookEvent = async (data) => {
    const event = await eventModel.findById(data.event);
    if (!event) {
        throw new Error('Event not found');
    }
    const newBooking = await eventBookingModel.create(data);
    return newBooking;
};

export const listUserBookings = async (userId) => {
    const bookings = await eventBookingModel.find({ user: userId })
        .populate({
            path: 'event',
            select: 'title startDate endDate attachments address location attachment', // Select the fields you need from the event
        })
        .exec();

    return bookings;
};
// Service to cancel a booking
export const cancelBookingById = async (bookingId, userId) => {
    const booking = await eventBookingModel.findOneAndUpdate(
        { _id: bookingId, user: userId },
        { status: 'Cancelled', cancellationDate: new Date() },
        { new: true }
    );
    if (!booking) {
        throw new Error('Booking not found or unauthorized');
    }
    return booking;
};
