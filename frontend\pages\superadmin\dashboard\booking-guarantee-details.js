import Link from "next/link";
import React from "react";

const GuaranteeDetails = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 dark:bg-[#171616] overflow-y-auto scroll-smooth ">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
       Booking Guarantee Details
      </h2>
      <div className="bg-white border rounded-xl mt-5 h-auto dark:bg-black dark:border-none">
        <div className="p-8 flex flex-col gap-y-6">
          <div>
            <div className="flex ">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  Introduction
                </h1>
                <p className=" text-black/55 text-sm font-poppins font-medium dark:text-[#757575]">
                  At MixDorm, we understand the importance of a hassle-free
                  booking experience. OurBooking <PERSON><PERSON>rantee ensures that your
                  reservations are secure and your stay meets your expectations.
                </p>
              </div>
              <div className="">
                <Link href={"/superadmin/dashboard/boking-guarantee"} className="text-white text-sm font-poppins py-1 md:py-2 lg:py-2 px-6 lg:px-8   rounded bg-sky-blue-650">
                  Edit
                </Link>
              </div>
            </div>
            <div className="flex flex-col">
              <div className="flex flex-col w-[90%] gap-y-3">
                <h1 className="text-lg text-black font-bold dark:text-[#B6B6B6] mt-2">
                  Our Guarantee
                </h1>
                <div>
                  <h1 className="text-base text-black font-bold dark:text-[#B6B6B6]">
                    1. Secure Reservation
                  </h1>
                  <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
                    • When you book with MixDorm, your reservation is
                    guaranteed. We use secure and  trusted payment
                    gateways to protect your personal and payment information.
                  </p>

                  <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
                    2. Best Price Assurance
                  </h1>
                  <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
                    • We assure you that the price you see on our website is the
                    best available rate. If you  find a lower price
                    elsewhere, let us know and we will match it.
                  </p>

                  <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
                    3. Confirmed Bookings
                  </h1>
                  <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
                    • Once your booking is confirmed, you will receive an email
                    with all the details of your  reservation. This
                    confirmation guarantees your spot at the chosen MixDorm{" "}
                     property
                  </p>

                  <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
                    4. Flexible Policies
                  </h1>
                  <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
                    • We offer flexible booking policies to accommodate changes
                    in your travel plans.  Please review our Cancellation
                    Policy for more details on how to modify or cancel {" "}
                    your reservation.
                  </p>
                </div>
              </div>
            </div>
            <div className="flex ">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  What to expect
                </h1>
                <div>
                  
                  <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
                    1. Quality Accomodations
                  </h1>
                  <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
                    • MixDorm ensures that all our properties meet high
                    standards of cleanliness, safety,  and comfort. We
                    regularly inspect our hostels to maintain these standards.
                  </p>
                  <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
                    2. 24/7 Customer Support
                  </h1>
                  <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
                    • Our customer support team is available 24/7 to assist you
                    with any queries or  concerns. Whether you need help
                    with your booking or have questions about your  stay,
                    we are here to help.
                  </p>
                  <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
                    3. No Hidden Feses
                  </h1>
                  <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
                    • The price you see at the time of booking is the price you
                    pay. We believe in transparent pricing with no hidden
                    charges or surprise fees.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-start justify-start p-8">
          <Link
            href={"/superadmin/dashboard/booking-guarantee"}
            className="text-white py-2 w-32 max-w-md rounded bg-sky-blue-650 flex items-center justify-center"
          >
            Cancel
          </Link>
        </div>
      </div>
    </div>
  );
};

export default GuaranteeDetails;
