import mongoose from 'mongoose';

const recentSearchSchema = new mongoose.Schema({
    search: {
        type: String,
        required: true
    },
    user:{
        type: mongoose.Types.ObjectId,
        ref: 'users',
    },
    checkIn:{
        type:Date
    },
    checkOut:{
        type:Date
    },
    guest:{
        type:Number
    }
},{
    timestamps:true
});

const recentSearches = mongoose.model('recentSearches', recentSearchSchema);

export default recentSearches;
