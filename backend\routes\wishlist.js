// routes/propertyLike.js
import express from 'express';
import { getUserWishlistProperties, likeOrUnlikeProperty } from '../controller/wishlist.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();
router.post('/like/:propertyId', checkAuth(), likeOrUnlikeProperty);

// Get all wishlist properties
router.get('/', checkAuth(), getUserWishlistProperties);


export default router;
/**
 * @swagger
 * /wishlists/like/{propertyId}:
 *   post:
 *     summary: Like or Unlike a property
 *     tags: [Wishlists]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the property to like or unlike
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               isLike:
 *                 type: boolean
 *                 description: Pass `true` to like, `false` to unlike
 *                 example: true
 *     responses:
 *       200:
 *         description: Success message
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Internal Server Error
 */



/**
 * @swagger
 * /wishlists:
 *   get:
 *     summary: Get all wishlist properties for the logged-in user
 *     tags: [Wishlists]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of liked properties
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   _id:
 *                     type: string
 *                   name:
 *                     type: string
 *                   address:
 *                     type: object
 *                   lowestAveragePricePerNight:
 *                     type: number
 *                   liked:
 *                     type: boolean
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal Server Error
 */