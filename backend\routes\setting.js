import express from 'express';
import { updateSettingByKeyController, getSettingByKeyController } from '../controller/setting.js';
import { checkAuth } from '../middleware/auth.js';


const router = express.Router();

router.put('/:key',  checkAuth("update_settings"), updateSettingByKeyController);
router.get('/:key',  checkAuth("get_settings"), getSettingByKeyController);

export default router;

/**
 * @swagger
 * tags:
 *   name: Setting
 *   description: Setting endpoints
 */

/**
 * @swagger
 * /settings/{key}:
 *   put:
 *     summary: Update setting by key
 *     tags: [Setting]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: key
 *         schema:
 *           type: string
 *         required: true
 *         description: The key of the setting
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               value:
 *                 type: object
 *                 example: {"subKey": "value"}
 *     responses:
 *       200:
 *         description: Setting updated successfully
 *       404:
 *         description: Setting not found
 *       500:
 *         description: Internal Server Error
*/

/**
 * @swagger
 * /settings/{key}:
 *   get:
 *     summary: Get setting by key
 *     tags: [Setting]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: key
 *         schema:
 *           type: string
 *         required: true
 *         description: The key of the setting
 *     responses:
 *       200:
 *         description: Setting retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 _id:
 *                   type: string
 *                 key:
 *                   type: string
 *                 value:
 *                   type: object
 *                   example: {"subKey": "value"}
 *       404:
 *         description: Setting not found
 *       500:
 *         description: Internal Server Error
*/
