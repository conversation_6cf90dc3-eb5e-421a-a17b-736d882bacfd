/* eslint-disable react/no-unescaped-entities */
import Link from "next/link";
import React from "react";

const AboutDetails = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 dark:bg-[#171616] overflow-y-auto scroll-smooth ">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        About Us Details
      </h2>
      <div className="bg-white border rounded-xl mt-5 h-auto dark:bg-black dark:border-none">
        <div className="p-8 flex flex-col gap-y-6">
          <div>
            <div className="flex ">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  Welcome to MixDorm
                </h1>
                <p className=" text-black/55 text-sm font-poppins font-medium dark:text-[#757575]">
                  At MixDorm, we believe that travel is more than just a
                  journey; it's an experience that connects people, cultures,
                  and stories. Founded with a vision to revolutionize the hostel
                  experience, MixDorm is not just a place to stay but a
                  community where travelers from around the world come together
                  to create unforgettable memories
                </p>
              </div>
              <div className="">
                <Link href={"/superadmin/dashboard/about-us-edit"} className="text-white text-sm font-poppins py-1 md:py-2 lg:py-2 px-6 lg:px-8   rounded bg-sky-blue-650">
                  Edit
                </Link>
              </div>
            </div>
            <div className="flex flex-col gap-x-20 ">
              <div className="flex w-[90%] flex-col">
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  Our Story
                </h1>
                <p className="mt-2 text-black/55 text-sm  font-poppins font-medium dark:text-[#757575]">
                  MixDorm was established in [Year] by a group of passionate
                  travelers who recognized the need for affordable, yet
                  high-quality accommodations for young explorers. Frustrated
                  with the lack of community-focused hostels, we set out to
                  create a space that embodies the spirit of adventure,
                  inclusivity, and connection.
                </p>
              </div>
              <div className="flex w-[90%] flex-col ">
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  Our Mission
                </h1>
                <p className="mt-2 text-black/55 text-sm  font-poppins font-medium dark:text-[#757575]">
                  Our mission is to provide exceptional hostel experiences that
                  foster connections and cultural exchanges among travelers. We
                  aim to offer a safe, comfortable, and vibrant environment
                  where our guests can relax, explore, and create lasting
                  friendships.
                </p>
              </div>
              <div className="flex w-[90%] flex-col ">
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  Our Values
                </h1>
                <p className=" text-black/55 text-sm  font-poppins font-medium w-[80%] dark:text-[#757575]">
                  <strong className="text-black dark:text-gray-200">
                    Community :
                  </strong>{" "}
                  We Believe in power of Community and strive to create spaces
                  where travelers can connect and share their stories.
                </p>
                <p className="mt-1 text-black/55 text-sm  font-poppins font-medium dark:text-[#757575]">
                  <strong className="text-black dark:text-gray-200">
                    Quality :
                  </strong>{" "}
                  We are committed to maintaining high standards of cleanliness,
                  safety and comfort across all our properties.
                </p>
                <p className="mt-1 text-black/55 text-sm  font-poppins font-medium dark:text-[#757575]">
                  <strong className="text-black dark:text-gray-200">
                    Innovaion :
                  </strong>{" "}
                  We continuosly seek to inovate and inhance the travel
                  experience through technology and solutions.
                </p>
                <p className="mt-2 text-black/55 text-sm  font-poppins font-medium dark:text-[#757575]">
                  <strong className="text-black dark:text-gray-200">
                    Sustainability :
                  </strong>{" "}
                  We are dedicated to promoting sustainable practices and
                  contributing postivly to the communict we operate in.
                </p>
              </div>
              <div className="flex w-[90%] flex-col ">
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  What We Offer
                </h1>
                <h1 className="text-xl font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  1. Modern Accommodations
                </h1>
                <p className="mt-1 text-black/55 text-sm  font-poppins font-medium dark:text-[#757575]">
                  Our Hosteles are designed with modern amenities to ensure a
                  comfortable stay. From cozy dorms to private rooms, we cater
                  to the diverse needs of our guests.
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-start justify-start p-8">
          <Link
            href={"/superadmin/dashboard/about-us"}
            className="text-white py-2 w-32 max-w-md rounded bg-sky-blue-650 flex items-center justify-center"
          >
            Cancel
          </Link>
        </div>
      </div>
    </div>
  );
};

export default AboutDetails;
