/* eslint-disable react/no-unescaped-entities */
import React from "react";
import dynamic from "next/dynamic";
import Head from "next/head";
const HeroNavbar = dynamic(() => import("@/components/footer/heroNavBar"), {
  ssr: false,
});

const RefundPolicy = () => {
  return (
    <>
      <Head>
        <title>No-Show Policy | Mixdorm</title>
      </Head>
      <HeroNavbar />
      <div className="bg-white pb-4 md:pb-[10rem] font-manrope md:pt-12 pt-8">
        <div className="container md:px-4 lg:px-0 xl:px-0 md:pr-8 lg:pr-10 xl:pr-20">
          <h2 className="font-bold  text-black font-manrope md:mb-8 mb-5 md:text-[35px] sm:text-xl text-xl">
            MixDorm Booking Refund & No-Show Policy
          </h2>
          <p className="text-sm text-gray-500 my-3">
            At
            <strong className="text-gray-900 font-bold"> MixDorm,</strong> we
            aim to create a secure and transparent booking experience for both
            travelers and hostel partners. Below is our official refund and
            no-show policy, applicable to all bookings made through the MixDorm
            platform.
          </p>
          <h3 className="font-bold md:text-2xl text-xl">1. Refund Policy</h3>

          <p className="text-sm text-gray-500 my-3">
            We offer two types of payment options to travelers, each with a
            defined refund structure:
          </p>

          <h3 className="font-bold md:text-xl text-lg">
            A. Standard Booking (15% Deposit Model)
          </h3>
          <ul className="text-sm list-disc text-gray-500 my-4 mx-7">
            <li className="text-sm">
              <strong className="text-gray-900 font-bold">
                {" "}
                Guest pays 15% of the booking amount at the time of booking.
              </strong>{" "}
            </li>
            <li className="text-sm">
              The{" "}
              <strong className="text-gray-900 font-bold">
                remaining 85%{" "}
              </strong>{" "}
              must be paid{" "}
              <strong className="text-gray-900 font-bold">
                {" "}
                directly through MixDorm at least 7 days before check-in{" "}
              </strong>{" "}
              to confirm the booking.
            </li>
            <li className="text-sm">
              <strong className="text-gray-900 font-bold">
                If the remaining balance is not paid within the deadline,
              </strong>{" "}
              the booking is
              <strong className="text-gray-900 font-bold">
                {" "}
                automatically cancelled.
              </strong>{" "}
            </li>
            <li className="text-sm">
              {" "}
              The 15% advance deposit is
              <strong className="text-gray-900 font-bold">
                non-refundable,
              </strong>{" "}
              even in case of no-shows or cancellations.
            </li>
          </ul>
          <h3 className="font-bold md:text-xl text-lg">
            B. Full Payment Booking (100% Prepaid)
          </h3>
          <ul className="text-sm list-disc text-gray-500 my-4 mx-7">
            <li className="text-sm">
              <strong className="text-gray-900 font-bold">
                {" "}
                Guest pays the full 100% of the booking amount upfront.
              </strong>{" "}
            </li>
            <li className="text-sm">
              This ensures a secure and confirmed reservation.
            </li>
          </ul>
          <h3 className="text-lg font-semibold  mb-3">
            Refund structure for full payment bookings:
          </h3>

          <div className="overflow-x-auto mb-6">
            <table className="min-w-full">
              <thead className="">
                <tr>
                  <th className="px-4 py-2 text-left text-lg font-semibold   ">
                    Time of Cancellation
                  </th>
                  <th className="px-4 py-2 text-left text-lg font-semibold  ">
                    Refund to Guest
                  </th>
                  <th className="px-4 py-2 text-left text-lg font-semibold  ">
                    Payment to Hostel
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white ">
                <tr>
                  <td className="px-4 py-2 text-sm text-gray-700 ">
                    More than 7 days before check-in
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-700 ">
                    80% refund
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-700 ">
                    15% MixDorm fee retained
                  </td>
                </tr>
                <tr>
                  <td className="px-4 py-2 text-sm text-gray-700 ">
                    7 to 1 days before check-in
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-700 ">
                    50% refund
                  </td>
                  <td className="px-4 py-2 text-sm text-gray-700 ">
                    50% paid to hostel
                  </td>
                </tr>
                <tr>
                  <td className="px-4 py-2 text-sm text-gray-700 ">
                    Less than 24 hours before check-in
                  </td>
                  <td className="px-4 py-2 text-sm font-bold ">No refund</td>
                  <td className="px-4 py-2 text-sm text-gray-700 ">
                    100% paid to hostel
                  </td>
                </tr>
                <tr>
                  <td className="px-4 py-2 text-sm text-gray-700 ">
                    No-show without cancellation
                  </td>
                  <td className="px-4 py-2 text-sm font-bold ">No refund</td>
                  <td className="px-4 py-2 text-sm text-gray-700 ">
                    100% paid to hostel
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <p className="text-sm text-gray-800 italic my-3">
            ⚠️ Any bank, currency conversion, or payment gateway charges are
            non-refundable.
          </p>

          <h3 className="font-bold md:text-2xl text-xl">2. No-Show Policy</h3>
          <ul className="text-sm list-disc text-gray-500 my-4 mx-7">
            <li className="text-sm">
              A<strong className="text-gray-900 font-bold"> no-show</strong> is
              defined as a guest who fails to arrive by{" "}
              <strong className="text-gray-900 font-bold">
                11:59 PM on the day of check-in,
              </strong>{" "}
              without notifying the property or MixDorm.
            </li>
            <li className="text-sm">
              Groups of{" "}
              <strong className="text-gray-900 font-bold"> 4 or more </strong>{" "}
              may be split across multiple dorms, regardless of request, to
              maintain the social integrity of dorm spaces.
            </li>
            <li className="text-sm">
              In
              <strong className="text-gray-900 font-bold">
                {" "}
                Standard Booking,
              </strong>{" "}
              the 15% deposit is retained by MixDorm and not transferred to the
              hostel.
            </li>

            <li className="text-sm">
              In{" "}
              <strong className="text-gray-900 font-bold">
                Full Payment Booking, 100% of the booking amount is settled with
                the hostel,
              </strong>{" "}
              as per the agreed policy.
            </li>
          </ul>

          <h3 className="font-bold md:text-2xl text-xl">
            3. Exceptions & Force Majeure
          </h3>
          <p className="text-base   text-gray-700 my-3">
            Refunds may be granted under special circumstances such as:
          </p>
          <ul className="text-sm list-disc text-gray-500 my-3 mx-7">
            <li className="text-sm text-gray-500 mb-1">Natural disasters</li>
            <li className="text-sm text-gray-500 mb-1">Health emergencies</li>
            <li className="text-sm text-gray-500 mb-1">Travel bans</li>
          </ul>
          <p className="text-base   text-gray-700 my-3">
            Such cases will be reviewed individually and require written proof.
            MixDorm holds the right to make final decisions.
          </p>
          <h3 className="font-bold md:text-2xl text-xl text-black mb-4">
            4. Settlement with Hostels
          </h3>
          <ul className="text-sm list-disc text-gray-500 my-4 mx-7">
            <li className="text-sm">
              Hostels receive payments for eligible bookings
              <strong className="text-gray-900 font-bold">
                {" "}
                within 7 days after check-in,
              </strong>{" "}
              is minus MixDorm's commission and processing fees.
            </li>
            <li className="text-sm">
              Bank account or PayPal details must be added in the property
              dashboard for timely payments
            </li>

         
          </ul>

          <h3 className="font-bold md:text-2xl text-xl text-black mb-4">
          Contact 
          </h3>
          <p className="text-sm text-gray-500 mt-3 ">
           For refund-related issues or exceptional cases, please reach out to:
          </p>
          <p className="text-sm text-gray-900 font-bold">📩 <EMAIL></p>

        </div>
      </div>
    </>
  );
};

export default RefundPolicy;
