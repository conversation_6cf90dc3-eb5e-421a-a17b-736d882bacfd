/* eslint-disable no-unused-vars */
import React, { useState, useEffect, useRef, Fragment } from "react";
import { IoIosStar } from "react-icons/io";
import Image from "next/image";
import { GetReviewApi } from "@/services/hostemproviderservices";
import ReactStars from "react-rating-stars-component";
import { getItemLocalStorage } from "@/utils/browserSetting";
import dynamic from "next/dynamic";
import Link from "next/link";
import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/react";
import { Trash, MoreVertical, Pencil, Eye } from "lucide-react";
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
  Transition,
} from "@headlessui/react";
import AddReview from "@/components/ownerFlow/dashboard/addReview";
import EditReview from "@/components/ownerFlow/dashboard/editReview";
import ViewReview from "@/components/ownerFlow/dashboard/viewReview";
import { deleteOwnerReviewApi } from "@/services/ownerflowServices";
import Pagination from "@/components/common/commonPagination";
import toast from "react-hot-toast";
import Head from "next/head";
import { useRouter } from "next/router";
import { useHeaderOwner } from "@/components/ownerFlow/headerContex";

const Filter = dynamic(() => import("../../../components/model/reviewFilter"), {
  ssr: false,
});

const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

const headers = [
  "Name",
  "Message",
  "Rating",
  "Country",
  "Date",
  "Like",
  "Dislike",
  "Action",
];

const Review = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const closefilterModal = () => setIsModalOpen(false);
  // Filter Modal
  const [reviews, setReviews] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [ratings, setRatings] = useState({});
  const [totalReviews, setTotalReviews] = useState(0);
  const isFirstRender = useRef(null);
  const [loading, setLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [iseditOpen, setIseditOpen] = useState(false);
  const [isviewOpen, setIsviewOpen] = useState(false);
  // const openModal = () => setIsOpen(true);
  // const openeditModal = () => setIseditOpen(true);
  const closeModal = () => setIsOpen(false);
  const closeeditModal = () => setIseditOpen(false);
  // const openviewModal = () => setIsviewOpen(true);
  const closeviewModal = () => setIsviewOpen(false);
  const [editId, setEditId] = useState("");
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalData, setTotalData] = useState();

  // --- Google Reviews State ---
  const [googleReviews, setGoogleReviews] = useState([]);
  const [isGoogleApiLoaded, setIsGoogleApiLoaded] = useState(false);
  const [isLoadingGoogleReviews, setIsLoadingGoogleReviews] = useState(false);
  const [googleReviewError, setGoogleReviewError] = useState(null);
  const [googleRating, setGoogleRating] = useState();
  const [googleTotalReviews, setGoogleTotalReviews] = useState(0);
  const [googlePlaceId, setGooglePlaceId] = useState(null);
  const { propertyData } = useHeaderOwner();

  // --- Property Info (replace with actual data source if needed) ---
  // You may need to fetch or receive these as props
  // For demo, using localStorage or fallback
  const propertyName = propertyData?.name;
  const propertyLat = parseFloat(propertyData?.location?.coordinates?.[0]);
  const propertyLng = parseFloat(propertyData?.location?.coordinates?.[1]);

  // --- Google Maps API Loader ---
  useEffect(() => {
    if (typeof window !== "undefined") {
      if (window.google && window.google.maps) {
        setIsGoogleApiLoaded(true);
      } else {
        const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
        const script = document.createElement("script");
        script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places`;
        script.async = true;
        script.onload = () => setIsGoogleApiLoaded(true);
        document.head.appendChild(script);
      }
    }
  }, []);

  // --- Fetch Google Reviews ---
  const fetchGoogleReviews = async () => {
    if (
      !propertyName ||
      !isGoogleApiLoaded ||
      isNaN(propertyLat) ||
      isNaN(propertyLng)
    )
      return;
    setIsLoadingGoogleReviews(true);
    setGoogleReviewError(null);
    try {
      const service = new window.google.maps.places.PlacesService(
        document.createElement("div")
      );
      const placeRequest = {
        query: propertyName,
        location: new window.google.maps.LatLng(propertyLat, propertyLng),
        radius: 5000,
      };
      service.textSearch(placeRequest, (results, status) => {
        if (
          status !== window.google.maps.places.PlacesServiceStatus.OK ||
          !results.length
        ) {
          setGoogleReviewError("Place not found");
          setIsLoadingGoogleReviews(false);
          return;
        }
        const placeId = results[0].place_id;
        const placeDetailsRequest = {
          placeId: placeId,
          fields: ["reviews", "photos", "name", "rating", "user_ratings_total"],
        };
        service.getDetails(placeDetailsRequest, (place, status) => {
          if (status !== window.google.maps.places.PlacesServiceStatus.OK) {
            setGoogleReviewError("Failed to get place details");
            setIsLoadingGoogleReviews(false);
            return;
          }
          const photos = place.photos || [];
          const photoUrls = photos.map((photo) =>
            photo.getUrl({ maxWidth: 400, maxHeight: 400 })
          );
          const reviews = place.reviews || [];
          const formattedReviews = reviews.map((review) => {
            const authorPhotoUrl = review.profile_photo_url;
            const reviewPhotos = review.photos || [];
            const reviewPhotoUrls = reviewPhotos.map((photo) =>
              photo.getUrl({ maxWidth: 400, maxHeight: 400 })
            );
            return {
              author_name: review.author_name,
              author_url: review.author_url,
              language: review.language,
              profile_photo_url: authorPhotoUrl,
              rating: review.rating,
              relative_time_description: review.relative_time_description,
              text: review.text,
              time: review.time,
              helpful: 0,
              notHelpful: 0,
              photos: reviewPhotoUrls.length > 0 ? reviewPhotoUrls : photoUrls,
            };
          });
          if (place.rating) setGoogleRating(place.rating);
          if (place.user_ratings_total)
            setGoogleTotalReviews(place.user_ratings_total);
          setGoogleReviews(formattedReviews);
          setGooglePlaceId(placeId);
          setIsLoadingGoogleReviews(false);
        });
      });
    } catch (error) {
      setGoogleReviewError(error.message);
      setIsLoadingGoogleReviews(false);
    }
  };

  useEffect(() => {
    fetchGoogleReviews();
  }, [isGoogleApiLoaded, propertyName, propertyLat, propertyLng]);

  const storedId = getItemLocalStorage("hopid");

  // --- Comment out old fetchReviews and use new combined logic ---
  // const fetchReviews = async (id) => {
  //   setLoading(true);
  //   try {
  //     const response = await GetReviewApi(id, currentPage, itemsPerPage);
  //     const ratingData = response?.data?.data?.ratings;
  //     const nonDeletedReviews = response?.data?.data?.reviews.filter(
  //       (review) => !review.isDeleted
  //     );
  //     setRatings(ratingData);
  //     setTotalReviews(nonDeletedReviews?.length); // Setting total reviews
  //     setReviews(response?.data?.data?.reviews);
  //     setTotalPages(response?.data?.data?.pagination?.totalPages);
  //     setTotalData(response?.data?.data?.pagination?.totalReviews);
  //   } catch (error) {
  //     console.error("Error fetching review data:", error);
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  const fetchReviews = async (id) => {
    setLoading(true);
    await fetchGoogleReviews();
    try {
      const response = await GetReviewApi(id, currentPage, itemsPerPage);
      const ratingData = response?.data?.data?.ratings;
      const nonDeletedReviews = response?.data?.data?.reviews.filter(
        (review) => !review.isDeleted
      );
      setRatings(ratingData);
      setTotalReviews(nonDeletedReviews?.length + googleReviews.length); // Add Google reviews count
      // Combine local and Google reviews
      // eslint-disable-next-line no-unsafe-optional-chaining
      setReviews([...response?.data?.data?.reviews, ...googleReviews]);
      setTotalPages(response?.data?.data?.pagination?.totalPages);
      setTotalData(
        response?.data?.data?.pagination?.totalReviews + googleReviews.length
      );
    } catch (error) {
      console.error("Error fetching review data:", error);
    } finally {
      setLoading(false);
    }
  };
  // ... existing code ...

  useEffect(() => {
    if (!isFirstRender.current) {
      fetchReviews(storedId);
    } else {
      isFirstRender.current = false;
    }
  }, [storedId, currentPage, itemsPerPage]);

  // const handlePageChange = (newPage) => {
  //   setCurrentPage(newPage);
  // };

  // const handleItemsPerPageChange = (newItemsPerPage) => {
  //   setItemsPerPage(newItemsPerPage);
  //   setCurrentPage(1); // Reset to first page when changing items per page
  // };

  // // Calculate percentage of reviews for each rating
  // const getRatingPercentage = (rating) => {
  //   const count = ratings[rating] || 0;
  //   const nonDeletedReviewsCount = reviews.filter(
  //     (review) => !review.isDeleted
  //   ).length; // Count of non-deleted reviews
  //   return nonDeletedReviewsCount > 0
  //     ? (count / nonDeletedReviewsCount) * 100
  //     : 0; // Calculate percentage based on non-deleted reviews
  // };
  // const getRatingPercentage = (rating) => {
  //   const count = ratings[rating] || 0;
  //   const nonDeletedReviewsCount = reviews.filter(
  //     (review) => !review.isDeleted
  //   ).length;

  //   if (nonDeletedReviewsCount === 0) return 0;

  //   const percentage = (count / nonDeletedReviewsCount) * 100;

  //   // Ensure percentage is between 0 and 100 and ignore values < 1%
  //   return Math.max(0, Math.min(100, Math.round(percentage)));
  // };

  const updateReviewList = async () => {
    await fetchReviews(storedId);
  };

  // const calculateOverallRating = (ratings, totalReviews, reviews) => {
  //   if (!ratings || totalReviews === 0) return 0; // Prevent division by zero

  //   let totalScore = 0;
  //   let validReviewsCount = 0; // Count of non-deleted reviews

  //   for (const review of reviews) {
  //     if (!review.isDeleted) {
  //       const count = ratings[review.rating] || 0; // Get the count for the review's rating
  //       totalScore += parseInt(review.rating) * count;
  //       validReviewsCount += count; // Increment valid reviews count
  //     }
  //   }

  //   return validReviewsCount > 0 ? totalScore / validReviewsCount : 0; // Return average if there are valid reviews
  // };

  // const handleDelete = async (id) => {
  //   setLoading(true);
  //   try {
  //     const response = await deleteOwnerReviewApi(id);
  //     if (response?.data?.status) {
  //       toast.success(response?.data?.data);
  //       await fetchReviews(storedId);
  //     }
  //   } catch (error) {
  //     console.log(error);
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  // --- Combine local and Google reviews for table ---
  const mappedGoogleReviews = googleReviews.map((review) => ({
    isGoogle: true,
    name: review.author_name,
    comment: review.text,
    rating: review.rating,
    images: review.photos?.map((url) => ({ url })),
    country: "-",
    createdAt: review.relative_time_description,
    likes: review.helpful || 0,
    dislikes: review.notHelpful || 0,
    // No actions for Google reviews
  }));
  // const allReviews = [
  //   ...reviews.map((r) => ({ ...r, isGoogle: false })),
  //   ...mappedGoogleReviews,
  // ];
  // --- Only show Google reviews in the table ---
  const allReviews = mappedGoogleReviews; // Only Google reviews
  console.log("All Reviews:", allReviews);
  return (
    <>
      <Head>
        <title>Manage Reviews | Mixdorm</title>
      </Head>
      <Loader open={loading} />
      {loading ? (
        <section className="w-full">
          {/* Header Skeleton */}
          <div className="flex justify-between items-center mb-6">
            <div className="h-8 w-32 bg-gray-200 rounded animate-pulse"></div>
            <div className="flex gap-2">
              <div className="h-10 w-24 bg-gray-200 rounded-lg animate-pulse"></div>
              <div className="h-10 w-28 bg-gray-200 rounded-lg animate-pulse"></div>
            </div>
          </div>

          {/* Rating Summary Skeleton */}
          <div className="w-full bg-white p-6 rounded-lg mb-5">
            <div className="flex mb-4 items-start bg-gray-300 w-max sm:p-6 p-3 rounded-lg">
              <div className="space-y-2">
                {/* Rating value skeleton */}
                <div className="h-8 w-12 bg-gray-400 rounded animate-pulse"></div>

                {/* Stars skeleton */}
                <div className="flex space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <div
                      key={i}
                      className="h-6 w-6 bg-gray-400 rounded-sm animate-pulse"
                    ></div>
                  ))}
                </div>

                {/* Reviews link skeleton */}
                <div className="h-4 w-32 bg-gray-400 rounded animate-pulse"></div>
              </div>
            </div>

            {/* Table Skeleton */}
            <div className="w-full mt-5 overflow-x-auto rounded-lg">
              <table className="w-full responsive-table border border-gray-340 rounded-lg font-inter">
                <thead className="rounded-t-lg">
                  <tr>
                    {headers.map((header, index) => (
                      <th
                        key={index}
                        className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left first:rounded-tl-lg last:rounded-tr-lg"
                      >
                        <div className="h-4 w-3/4 bg-gray-300 rounded animate-pulse"></div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {[...Array(5)].map((_, rowIndex) => (
                    <tr key={rowIndex} className="text-[#5D6679]">
                      {/* Name */}
                      <td className="text-xs font-medium py-3.5 px-4 border-b text-black">
                        <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
                      </td>

                      {/* Message */}
                      <td className="text-xs font-medium py-3.5 px-4 border-b text-black">
                        <div className="space-y-1">
                          <div className="h-3 w-full bg-gray-200 rounded animate-pulse"></div>
                          <div className="h-3 w-4/5 bg-gray-200 rounded animate-pulse"></div>
                          <div className="h-3 w-3/4 bg-gray-200 rounded animate-pulse"></div>
                        </div>
                      </td>

                      {/* Rating */}
                      <td className="text-xs font-medium py-3.5 px-4 border-b text-black whitespace-nowrap min-w-[120px]">
                        <div className="flex space-x-1">
                          {[...Array(5)].map((_, starIndex) => (
                            <div
                              key={starIndex}
                              className="h-5 w-5 bg-gray-200 rounded-sm animate-pulse"
                            ></div>
                          ))}
                        </div>
                      </td>

                      {/* Images */}
                      {/* <td className="text-xs font-medium py-3.5 px-4 border-b text-black">
                        <div className="flex gap-2">
                          <div className="w-[54px] h-[36px] bg-gray-200 rounded-sm animate-pulse"></div>
                          <div className="w-[54px] h-[36px] bg-gray-200 rounded-sm animate-pulse"></div>
                        </div>
                      </td> */}

                      {/* Country */}
                      <td className="text-xs font-medium py-3.5 px-4 border-b text-black">
                        <div className="h-4 w-12 bg-gray-200 rounded animate-pulse"></div>
                      </td>

                      {/* Date */}
                      <td className="text-xs font-medium py-3.5 px-4 border-b text-black">
                        <div className="h-4 w-16 bg-gray-200 rounded animate-pulse"></div>
                      </td>

                      {/* Like */}
                      <td className="text-xs font-medium py-3.5 px-4 border-b text-black">
                        <div className="h-4 w-6 bg-gray-200 rounded animate-pulse"></div>
                      </td>

                      {/* Dislike */}
                      <td className="text-xs font-medium py-3.5 px-4 border-b text-black">
                        <div className="h-4 w-6 bg-gray-200 rounded animate-pulse"></div>
                      </td>

                      {/* Action */}
                      <td className="text-xs font-medium py-3.5 px-4 border-b text-gray-500">
                        <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {/* Pagination Skeleton */}
              <div className="flex justify-between items-center mt-4">
                <div className="h-8 w-32 bg-gray-200 rounded animate-pulse"></div>
                <div className="flex gap-2">
                  <div className="h-8 w-8 bg-gray-200 rounded"></div>
                  <div className="h-8 w-8 bg-gray-200 rounded"></div>
                  <div className="h-8 w-8 bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
          </div>
        </section>
      ) : (
        <section className="w-full">
          <div className="flex justify-between items-center">
            <h1 className="page-title">Review</h1>
            <div className="flex sm:gap-2 gap-1">
              <button
                className="sm:flex block items-center text-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-gray-300 cursor-not-allowed  font-medium"
                // onClick={() => setIsModalOpen(true)}
              >
                <Image
                  className="sm:mr-2 mx-auto"
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/filter.svg`}
                  width={20}
                  height={20}
                />
                Filter
              </button>
              <button
                className="flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-gray-300 cursor-not-allowed  font-medium"
                // onClick={openModal}
              >
                Add Review
              </button>
            </div>
          </div>
          <div className="w-full bg-white my-5">
            {/* <div className="flex mb-4 items-start bg-black w-max sm:p-6 p-3 rounded-lg">
            <div className="">
              <span className="block text-white font-semibold md:text-3xl sm:text-2xl text-xl">
                {calculateOverallRating(ratings, totalReviews, reviews).toFixed(
                  1
                )}
              </span>
              <ReactStars
                key={calculateOverallRating(ratings, totalReviews, reviews)}
                count={5}
                size={24}
                value={calculateOverallRating(ratings, totalReviews, reviews)}
                edit={false}
                isHalf={true}
                activeColor="#FFD700"
              />
             
              <Link
                href="#"
                prefetch={false}
                className="text-sm text-white underline hover:text-primary-blue"
              >
                {totalReviews} Customers Reviews
              </Link>
            </div>

            <div className="ml-12">
              {[5, 4, 3, 2, 1].map((rating) => {
                const count = reviews.filter(
                  (review) => !review.isDeleted && review.rating === rating
                ).length;

                return (
                  <div key={rating} className="flex items-center mb-2 gap-2">
                    <div className="flex items-center gap-1.5">
                      <IoIosStar className="text-[#FFD700]" size={17} />
                      <span className="text-white text-sm">{rating}</span>
                    </div>
                    <div className="w-36 bg-[#E3E3E7] h-1.5 rounded-md overflow-hidden">
                      <div
                        className="h-1.5 bg-[#40E0D0] rounded-md"
                        style={{ width: `${getRatingPercentage(rating)}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium text-[#fff]">
                      ({count}){" "}
                    </span>
                  </div>
                );
              })}
            </div>

           
          </div> */}

            {/* --- Google Reviews Section --- */}
            <div className="w-full bg-white my-5">
              <div className="flex mb-4 items-start bg-black w-max sm:p-6 p-3 rounded-lg">
                <div className="">
                  <span className="block text-white font-semibold md:text-3xl sm:text-2xl text-xl">
                    {googleRating ? googleRating.toFixed(1) : "-"}
                  </span>
                  <ReactStars
                    key={googleRating}
                    count={5}
                    size={24}
                    value={googleRating || 0}
                    edit={false}
                    isHalf={true}
                    activeColor="#FFD700"
                  />
                  <Link
                    href="#"
                    prefetch={false}
                    className="text-sm text-white underline hover:text-primary-blue"
                  >
                    {googleTotalReviews} Google Reviews
                  </Link>
                </div>
              </div>
            </div>

            {/* --- End Google Reviews Section --- */}

            {/* --- Old Review Table (Commented Out) --- */}
            {/*
          <div className="w-full bg-white my-5">
            <div className="flex mb-4 items-start bg-black w-max sm:p-6 p-3 rounded-lg">
              ...
            </div>
            <div className="w-full mt-5 text-nowrap overflow-x-auto rounded-lg">
              <table className="w-full responsive-table border border-gray-340 rounded-lg font-inter">
                ...
              </table>
              {reviews?.length > 0 && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  totalItems={totalData || 0}
                  itemsPerPage={itemsPerPage}
                  onPageChange={handlePageChange}
                  onItemsPerPageChange={handleItemsPerPageChange}
                />
              )}
            </div>
          </div>
          */}

            {/* Reviews Table */}
            <div className="w-full mt-5 ">
              <div className="overflow-x-auto rounded-lg ">
              <table className="w-full min-w-[800px] md:min-w-0  border border-gray-340 rounded-lg font-inter">
                <thead className="rounded-t-lg ">
                  <tr>
                    {headers.map((header, index) => (
                      <th
                        key={index}
                        className="py-4 px-4 bg-[#EEEEEE] font-bold text-xs text-black text-left first:rounded-tl-lg last:rounded-tr-lg whitespace-nowrap"
                      >
                        {header}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {allReviews?.map((review, index) => (
                    <tr
                      key={index}
                      className={`text-[#5D6679]  ${
                        review.isDeleted ? "bg-red-200" : ""
                      }`}
                    >
                      <td
                        className="text-xs font-medium py-3.5 px-4 border-b text-black whitespace-nowrap"
                        title="Name"
                      >
                        {review.name}
                      </td>
                      <td
                        className="text-xs font-medium py-3.5 px-4  text-black  overflow-hidden text-ellipsis"
                        title="Message"
                      >
                        {review.comment.split(" ").map((word, index) => (
                          <>
                            {word} {(index + 1) % 9 === 0 && <br />}
                          </>
                        ))}
                      </td>
                      <td
                        className="text-xs font-medium py-3.5 px-4 border-b text-black whitespace-nowrap min-w-[120px]"
                        title="Rating"
                      >
                        <ReactStars
                          key={review?.rating}
                          count={5}
                          size={20}
                          value={parseFloat(review?.rating || 0)}
                          edit={false}
                          isHalf={true}
                          activeColor="#FFD700"
                        />
                      </td>
                      {/* <td
                        className="text-xs font-medium py-3.5 px-4 border-b text-black whitespace-nowrap min-w-[200px] "
                        title="Images"
                      >
                        <div className="flex gap-2">
                          {review.images?.slice(0, 3).map((image, imgIndex) => (
                            <Image
                              key={imgIndex}
                              src={image.url}
                              alt={`Review Image ${imgIndex + 1}`}
                              width={54}
                              height={36}
                              loading="lazy"
                              className="object-cover h-[36px] rounded-sm overflow-hidden"
                            />
                          ))}
                        </div>
                      </td> */}
                      <td
                        className="text-xs font-medium py-3.5 px-4 border-b text-black text-center whitespace-nowrap"
                        title="Country"
                      >
                        {review.country}
                      </td>
                      <td
                        className="text-xs font-medium py-3.5 px-4 border-b text-black whitespace-nowrap"
                        title="Date"
                      >
                        {review.createdAt}
                      </td>
                      <td
                        className="text-xs font-medium py-3.5 px-4 border-b text-black whitespace-nowrap"
                        title="Like"
                      >
                        {review.likes}
                      </td>
                      <td
                        className="text-xs font-medium py-3.5 px-4 border-b text-black whitespace-nowrap"
                        title="Dislike"
                      >
                        {review.dislikes}
                      </td>
                      <td
                        className="text-xs font-medium py-3.5 px-4 border-b text-gray-500 whitespace-nowrap"
                        title="Action"
                      >
                        Google Review
                        {/* No actions for Google reviews */}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {/* {allReviews?.length > 0 && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  totalItems={totalData || 0}
                  itemsPerPage={itemsPerPage}
                  onPageChange={handlePageChange}
                  onItemsPerPageChange={handleItemsPerPageChange}
                />
              )} */}
            </div>
            </div>
          </div>

          {isModalOpen && (
            <Dialog
              open={isModalOpen}
              onClose={() => setIsModalOpen(false)}
              className="relative z-50"
            >
              <DialogBackdrop
                transition
                className="fixed inset-0 bg-[#000000B2] transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
              />

              <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
                <div className="flex min-h-full justify-center p-4 text-center items-center sm:p-0">
                  <DialogPanel
                    transition
                    className="relative transform overflow-hidden rounded-2xl bg-white text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-full sm:max-w-[490px] max-w-full w-full data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95"
                  >
                    <div className="bg-white sm:px-7 sm:pb-7 pt-3 p-3 pb-3">
                      <DialogTitle>
                        <h3 className="text-center text-black font-bold sm:text-lg text-sm">
                          Filter
                        </h3>
                      </DialogTitle>
                      <Filter closefilterModal={closefilterModal} />
                    </div>
                  </DialogPanel>
                </div>
              </div>
            </Dialog>
          )}

          <Transition show={isOpen} as={Fragment}>
            <Dialog as="div" className="relative z-50" onClose={closeModal}>
              {/* Overlay */}
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <div className="fixed inset-0 bg-black/50" />
              </Transition.Child>

              {/* Slide-In Modal */}
              <div className="fixed inset-0 overflow-hidden">
                <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
                  <Transition.Child
                    as={Fragment}
                    enter="transform transition ease-out duration-300"
                    enterFrom="translate-x-full"
                    enterTo="translate-x-0"
                    leave="transform transition ease-in duration-200"
                    leaveFrom="translate-x-0"
                    leaveTo="translate-x-full"
                  >
                    <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[95%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                      {/* Modal Header */}
                      <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                        <h2 className="page-title">Add Review</h2>
                        <button
                          onClick={closeModal}
                          className="text-gray-500 hover:text-gray-800"
                        >
                          &#10005; {/* Close icon */}
                        </button>
                      </div>

                      {/* Modal Content */}
                      <div className="sm:px-6 px-4 pb-10">
                        <AddReview
                          closeModal={closeModal}
                          updateReviewList={updateReviewList}
                        ></AddReview>
                      </div>
                    </Dialog.Panel>
                  </Transition.Child>
                </div>
              </div>
            </Dialog>
          </Transition>

          <Transition show={iseditOpen} as={Fragment}>
            <Dialog as="div" className="relative z-50" onClose={closeeditModal}>
              {/* Overlay */}
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <div className="fixed inset-0 bg-black/50" />
              </Transition.Child>

              {/* Slide-In Modal */}
              <div className="fixed inset-0 overflow-hidden">
                <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
                  <Transition.Child
                    as={Fragment}
                    enter="transform transition ease-out duration-300"
                    enterFrom="translate-x-full"
                    enterTo="translate-x-0"
                    leave="transform transition ease-in duration-200"
                    leaveFrom="translate-x-0"
                    leaveTo="translate-x-full"
                  >
                    <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[95%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                      {/* Modal Header */}
                      <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                        <h2 className="page-title">Edit Review</h2>
                        <button
                          onClick={closeeditModal}
                          className="text-gray-500 hover:text-gray-800"
                        >
                          &#10005; {/* Close icon */}
                        </button>
                      </div>

                      {/* Modal Content */}
                      <div className="sm:px-6 px-4 pb-10">
                        <EditReview
                          closeeditModal={closeeditModal}
                          editId={editId}
                          updateReviewList={updateReviewList}
                        ></EditReview>
                      </div>
                    </Dialog.Panel>
                  </Transition.Child>
                </div>
              </div>
            </Dialog>
          </Transition>

          <Transition show={isviewOpen} as={Fragment}>
            <Dialog as="div" className="relative z-50" onClose={closeviewModal}>
              {/* Overlay */}
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <div className="fixed inset-0 bg-black/50" />
              </Transition.Child>

              {/* Slide-In Modal */}
              <div className="fixed inset-0 overflow-hidden">
                <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
                  <Transition.Child
                    as={Fragment}
                    enter="transform transition ease-out duration-300"
                    enterFrom="translate-x-full"
                    enterTo="translate-x-0"
                    leave="transform transition ease-in duration-200"
                    leaveFrom="translate-x-0"
                    leaveTo="translate-x-full"
                  >
                    <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[95%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                      {/* Modal Header */}
                      <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                        <div className="flex items-center gap-2">
                          <h2 className="page-title">View Review</h2>
                          {reviews.find((review) => review._id === editId)
                            ?.isDeleted && (
                            <span className="px-2 py-1 text-xs font-medium text-white bg-red-500 rounded-md">
                              Deleted
                            </span>
                          )}
                        </div>
                        <button
                          onClick={closeviewModal}
                          className="text-gray-500 hover:text-gray-800"
                        >
                          &#10005; {/* Close icon */}
                        </button>
                      </div>

                      {/* Modal Content */}
                      <div className="sm:px-6 px-4 pb-10">
                        <ViewReview
                          editId={editId}
                          updateReviewList={updateReviewList}
                        ></ViewReview>
                      </div>
                    </Dialog.Panel>
                  </Transition.Child>
                </div>
              </div>
            </Dialog>
          </Transition>

          {/* <Filter close={handleCloseFilter} open={openFilter} /> */}
        </section>
      )}
    </>
  );
};

export default Review;
