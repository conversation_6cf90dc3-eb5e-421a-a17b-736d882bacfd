import Link from "next/link";
import React from "react";

const SubscriptionRevenueDetails = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <div className="flex items-center justify-between w-full ">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Event Revenue Details
        </h2>
      </div>
      <div className="bg-white border rounded-xl mt-5 h-auto dark:bg-black dark:border-none">
        <div className="p-8 flex flex-col gap-y-6">
          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">
              Booking Information
            </h1>
            <div className="flex flex-wrap items-center my-6 gap-6">
              <div className="flex w-full sm:w-[45%] md:w-[45%] lg:w-[15%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  NAME
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  Alexander
                </p>
              </div>
              <div className="flex w-full sm:w-[45%] md:w-[45%] lg:w-[20%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  Event ID
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  #2585
                </p>
              </div>
              <div className="flex w-full sm:w-[45%] md:w-[45%] lg:w-[20%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  Event Name
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  Club Hopping
                </p>
              </div>
            </div>
          </div>

          {/* <div>
            <h1 className="text-xl font-bold font-poppins">Travelling Information</h1>
            <div className="flex flex-wrap items-center gap-x-20 my-6">
              <div className="flex sm:w-[90%] md:w-[33%] lg:w-[10%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins">Country</strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins">India</p>
              </div>
              <div className="flex mt-4 md:mt-0 lg:mt-0 sm:w-[90%] md:w-[40%] lg:w-[15%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins">Place</strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins">Himachal Pradesh</p>
              </div>
            </div>
          </div> */}

          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">
              Payment Information
            </h1>
            <div className="flex flex-wrap items-center gap-6 my-6">
              <div className="flex w-full sm:w-[45%] md:w-[45%] lg:w-[15%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  Method
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  VISA
                </p>
              </div>
              <div className="flex w-full sm:w-[45%] lg:w-[20%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  Amount
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  $99
                </p>
              </div>
              <div className="flex w-full sm:w-[45%] lg:w-[20%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  Date
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  04/02/24
                </p>
              </div>
            </div>
          </div>

          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">
              Status Information
            </h1>
            <div className="flex items-center gap-x-20 my-6">
              <div className="flex w-[15%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  Status
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  Paid
                </p>
              </div>
            </div>
          </div>
          <div className="flex items-start justify-start">
            <Link
              href={"/superadmin/dashboard/payment-event"}
              className="text-white py-2 w-32 rounded bg-sky-blue-650 flex items-center justify-center"
            >
              Cancel
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionRevenueDetails;
