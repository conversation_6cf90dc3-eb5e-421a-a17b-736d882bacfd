import React from "react";
import Link from "next/link";

const Breadcrumb = ({ items }) => {
  return (
    <nav aria-label='breadcrumb' className='text-sm mb-6'>
      <ol className='flex items-center space-x-1 text-gray-500'>
        {items.map((item, index) => (
          <li key={index} className='flex items-center'>
            {index > 0 && (
              <svg
                className='w-4 h-4 text-black mt-0.5'
                fill='currentColor'
                viewBox='0 0 20 20'
              >
                <path
                  fillRule='evenodd'
                  d='M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z'
                  clipRule='evenodd'
                />
              </svg>
            )}
            {item.href ? (
              <Link
                href={item.href}
                className='text-black text-sm font-bold hover:text-primary-blue  font-manrope'
                prefetch={false}
              >
                {item.label}
              </Link>
            ) : item.onClick ? (
              <button
                onClick={item.onClick}
                className='text-black text-sm font-bold hover:text-primary-blue  font-manrope'
              >
                {item.label}
              </button>
            ) : (
              <span className='text-black font-bold text-sm font-manrope ml-2'>
                {item.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default Breadcrumb;
