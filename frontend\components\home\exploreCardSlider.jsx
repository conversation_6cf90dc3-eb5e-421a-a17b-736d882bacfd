import React, { useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import {  Autoplay } from "swiper/modules";
import HostelCard from "./exploreHostelCard";
import countries from "world-countries";

const HostelCardSlider = ({featuredHostelData, onLikeUnlike, loading = false}) => {
  
  const [currencyData, setCurrencyData] = useState({});

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {}; 

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0]; 
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol; 
            }
          }
        });

        setCurrencyData(currencyMap); 

      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData(); 
  }, []); 

  const getCurrencySymbol = (currencyCode) => {
    return currencyData[currencyCode] || currencyCode;
  };
  // const data = [
  //   {
  //     id: 1,
  //     tag: "Top Rated",
  //     title: "Amsterdam, Netherlands",
  //     image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/cultural.jpg`,
  //     guest: "4-6 guest",
  //     time: "2 days 3 nights",
  //     price: "$48.25",
  //     rating: "4.9",
  //     review: "672",
  //     feature: "Pool, rooftop bar, and organized local tours.",
  //   },
  //   {
  //     id: 2,
  //     tag: "Top Rated",
  //     title: "Amsterdam, Netherlands",
  //     image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/cultural.jpg`,
  //     guest: "4-6 guest",
  //     time: "2 days 3 nights",
  //     price: "$48.25",
  //     rating: "4.9",
  //     review: "672",
  //     feature: "Pool, rooftop bar, and organized local tours.",
  //   },
  //   {
  //     id: 3,
  //     tag: "Top Rated",
  //     title: "Amsterdam, Netherlands",
  //     image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/cultural.jpg`,
  //     guest: "4-6 guest",
  //     time: "2 days 3 nights",
  //     price: "$48.25",
  //     rating: "4.9",
  //     review: "672",
  //     feature: "Pool, rooftop bar, and organized local tours.",
  //   },
  //   {
  //     id: 4,
  //     tag: "Top Rated",
  //     title: "Amsterdam, Netherlands",
  //     image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/cultural.jpg`,
  //     guest: "4-6 guest",
  //     time: "2 days 3 nights",
  //     price: "$48.25",
  //     rating: "4.9",
  //     review: "672",
  //     feature: "Pool, rooftop bar, and organized local tours.",
  //   },
  //   {
  //     id: 5,
  //     tag: "Top Rated",
  //     title: "Amsterdam, Netherlands",
  //     image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/cultural.jpg`,
  //     guest: "4-6 guest",
  //     time: "2 days 3 nights",
  //     price: "$48.25",
  //     rating: "4.9",
  //     review: "672",
  //     feature: "Pool, rooftop bar, and organized local tours.",
  //   },
  // ];
  return (
    <>
      <Swiper
        modules={[Autoplay]}
        autoplay={{ delay: 1500 }}
        slidesPerView={18}
        loop
        speed={1000}
        // navigation={featuredHostelData?.length > 3} // Enable navigation if more than 4
        spaceBetween={14}
        className="mySwiper"
        breakpoints={{
          0: {
            slidesPerView: 1.2,
          },
          480: {
            slidesPerView: 1.4,
          },
          640: {
            slidesPerView: 2,
          },
          768: {
            slidesPerView: 2.6,
          },
          1100: {
            slidesPerView: 4,
          },
        }}
      >
        
        {loading || !featuredHostelData.length
        ? [...Array(4)].map((_, index) => (
            <SwiperSlide key={index}>
              <HostelCard loading={true} />
            </SwiperSlide>
          ))
        : featuredHostelData.map((item) => (
            <SwiperSlide key={item.id}>
              <HostelCard
                tag="Top Rated"
                title={`${item?.name},${item?.address?.country}`}
                image={item?.images?.[0]?.objectUrl}
                guest="4-6 guest"
                time="2 days 3 nights"
                feature={item?.freeFacilities}
                price={`${getCurrencySymbol(item?.lowestAveragePricePerNight?.currency)} ${item?.lowestAveragePricePerNight?.value}`}
                rating="4.9"
                review="672"
                hostelId={item?._id}
                liked={item?.liked}
                onLikeUnlike={onLikeUnlike}
              />
            </SwiperSlide>
          ))}

      </Swiper>
    </>
  );
};

export default HostelCardSlider;
