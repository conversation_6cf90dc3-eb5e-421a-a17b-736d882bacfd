import React, { useEffect, useState } from "react";
import Link from "next/link";
import dynamic from "next/dynamic";
import Banner from "@/components/home/<USER>";
import {
  getTopHostelByCountryForExploreWorldApi,
  likeUnlikePropertyApi,
} from "@/services/webflowServices";
// import { ArrowLeft, ArrowRight } from "lucide-react";
import { useNavbar } from "@/components/home/<USER>";
import Head from "next/head";
import { getItemLocalStorage } from "@/utils/browserSetting";
import HostelCardSlider from "@/components/home/<USER>";
import toast from "react-hot-toast";
// const HostelCardSlider = dynamic(
//   () => import("@/components/home/<USER>"),
//   { ssr: true }
// );

const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

const countries = [
  "India",
  "Thailand",
  "Indonesia",
  "Colombia",
  "Spain",
  "Mexico",
  "Italy",
  "Portugal",
  "Brazil",
  "USA",
  "Japan",
  "Vietnam",
  "France",
  "Australia",
  "Peru",
];

const countryTitles = {
  India: "Top Hostel In India",
  Thailand: "Affordable Hostels in Thailand",
  Indonesia: "Best Hostels in Indonesia",
  Colombia: "Explore Hostels in Colombia",
  Spain: "Popular Hostels in Spain",
  Mexico: "Top-Rated Hostels in Mexico",
  Italy: "Affordable Hostels in Italy",
  Portugal: "Great Hostels in Portugal",
  Brazil: "Popular Hostels in Brazil",
  USA: "Cheap Hostels in the USA",
  Japan: "Top Hostels in Japan",
  Vietnam: "Best Hostels in Vietnam",
  France: "Affordable Hostels in France",
  Australia: "Popular Hostels in Australia",
  Peru: "Top-Rated Hostels in Peru",
};

const countryDescriptions = {
  India:
    "Stay at vibrant hostels in India, from the bustling streets of Delhi to the serene beaches of Goa. Explore heritage sites while staying at budget-friendly accommodations",
  Thailand:
    "Experience Thailand's rich culture and stunning landscapes. Find cheap hostels in Bangkok, Chiang Mai, and Phuket, perfect for budget-conscious travelers",
  Indonesia:
    "From Bali's beachside retreats to Jakarta's urban escapes, discover Indonesia's top-rated hostels offering comfort and adventure for every traveler.",
  Colombia:
    "Discover affordable hostels in Colombia's top cities like Bogotá and Medellín. Enjoy local culture while staying in cozy and cheap accommodations.",
  Spain:
    "Book top hostels in Spain, from the vibrant nightlife of Barcelona to the historic beauty of Madrid. Perfect for both solo travelers and groups.",
  Mexico:
    "Explore Mexico's best hostels for backpackers,whether you're visiting the beaches of Tulum or the cultural hub of Mexico City.Affordable and centrally located!",
  Italy:
    "Discover charming hostels in Italy, close to iconic landmarks in cities like Rome, Florence, and Venice. Ideal for travelers looking to explore on a budget.",
  Portugal:
    "Enjoy luxury hostels in Portugal, combining affordability with a touch of comfort in cities like Lisbon and Porto. Perfect for travelers seeking a bit of indulgence.",
  Brazil:
    " Brazil offers the perfect blend of adventure and social atmosphere for solo travelers. Stay in hostels in Rio de Janeiro or São Paulo, designed to meet every budget.",
  USA: "Explore iconic cities like New York, Los Angeles, and San Francisco while staying in budget-friendly hostels. A great option for backpackers and students",
  Japan:
    "Stay at hostels in Japan that offer a cultural experience, from Tokyo's busy streets to Kyoto's traditional temples. Immerse yourself in local culture with a stay at these highly-rated hostels",
  Vietnam:
    "Relax at beachside hostels in Vietnam, perfect for sun-seekers. Stay near the stunning shores of Da Nang or Nha Trang while keeping your budget intact.",
  France:
    "Whether you're exploring the romantic streets of Paris or the scenic beauty of the French Riviera, our handpicked hostels in France offer both charm and convenience.",
  Australia:
    "Australia's best hostels offer cozy stays in cities like Sydney, Melbourne, and Brisbane, perfect for long-term backpackers or those seeking comfort on a budget.",
  Peru: "For thrill-seekers, Peru offers adventure-filled hostels near Cusco, the gateway to Machu Picchu. Book hostels designed for those ready to trek and explore!",
};

const Page = () => {
  const [hostelData, setHostelData] = useState({});
  const [loading, setLoading] = useState(true);
  const { currencyCode2, token } = useNavbar();

  // Like/Unlike handler
  const handleLikeUnlike = async (country, hostelId, liked) => {
    if (!getItemLocalStorage("token")) {
      toast.error("Please login first!!");
      return;
    }
    setLoading(true);
    try {
      const payload = { isLike: !liked };
      const response = await likeUnlikePropertyApi(hostelId, payload);
      const updatedHostel = response?.data?.data || response?.data;
      if (updatedHostel && updatedHostel._id) {
        setHostelData((prev) => {
          const updatedCountryHostels =
            prev[country]?.map((item) =>
              item._id === updatedHostel._id
                ? { ...item, ...updatedHostel }
                : item
            ) || [];
          return { ...prev, [country]: updatedCountryHostels };
        });
      }
    } catch (error) {
      console.error("Error updating like status:", error);
      toast.error("Failed to update like status");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Join all countries with commas for a single API call
        const countriesString = countries.join(",");
        const response = await getTopHostelByCountryForExploreWorldApi(
          countriesString,
          currencyCode2 || "USD"
        );

        // Transform the response data into the required format
        const dataMap = {};
        response?.data?.data?.forEach((countryData) => {
          if (countryData.country && countryData.hostels) {
            dataMap[countryData.country] = countryData.hostels;
          }
        });

        setHostelData(dataMap);
      } catch (error) {
        console.error("Error fetching hostel data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [currencyCode2, token]);

  return (
    <>
      <Head>
        <title>Top Hostels Around the World | Mixdorm</title>
        <meta
          name='description'
          content='Book Top Hostels in 15+ Countries. From Beach Towns to Big Cities, Find Budget Stays for Backpackers and Travelers with Mixdorm.'
        />
      </Head>
      <Loader open={loading} />
      <div
        style={{
          backgroundImage: `url(${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/top-black-bg.jpeg)`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
        className='pb-10 md:pb-14'
      >
        <Banner />{" "}
      </div>
      <section className='pt-8 lg:pb-28 border-t border-gray-200 '>
        {Object.values(hostelData).some((hostels) => hostels.length > 0) && (
          <div className='w-full xxxl:container px-4 lg:px-10'>
            {countries.map(
              (country) =>
                hostelData[country]?.length > 0 && ( // Ensure there are hostels for the country
                  <div
                    className='lg:mb-16 sm:mb-10 mb-1 border-y border-white'
                    key={country}
                  >
                    <div className='hidden sm:flex items-end justify-between mb-6 flex-wrap sm:flex-nowrap w-full gap-x-5'>
                      <div className='flex flex-col gap-y-0 w-full'>
                        <div className='flex justify-between w-full'>
                          {" "}
                          <h2 className='font-semibold text-black font-manrope md:text-3xl sm:text-2xl text-xl mb-3'>
                            {countryTitles[country] ||
                              `Top Hostel In ${country}`}
                          </h2>
                          <div className='flex  h-full items-end justify-end'>
                            <Link
                              href={`/tophostel?country=${country}`}
                              className='text-sm font-semibold text-black bg-primary-blue rounded-4xl py-3 px-10 hover:bg-sky-blue-750'
                              prefetch={false}
                            >
                              See All
                            </Link>
                          </div>
                        </div>
                        <p className='hidden sm:block mt-0 text-base font-medium text-[#737373] font-Poppins mb-1 text-[15px]'>
                          {countryDescriptions[country] ||
                            `Explore popular hostels in ${country} with affordable rates and vibrant locations.`}
                        </p>
                      </div>
                    </div>
                    <div className='flex sm:hidden items-end justify-between mb-6 w-full gap-x-5'>
                      <div className='flex flex-col gap-y-0 w-[100%]'>
                        <h2 className='font-semibold text-black font-manrope md:text-3xl sm:text-2xl text-xl mb-3'>
                          {countryTitles[country] || `Top Hostel In ${country}`}
                        </h2>

                        <p className='mt-0  font-medium text-[#737373] font-Poppins mb-1 text-xs'>
                          {countryDescriptions[country] ||
                            `Explore popular hostels in ${country} with affordable rates and vibrant locations.`}
                        </p>
                      </div>
                    </div>
                    <div className='relative'>
                      <HostelCardSlider
                        featuredHostelData={hostelData[country] || []}
                        onLikeUnlike={(hostelId, liked) =>
                          handleLikeUnlike(country, hostelId, liked)
                        }
                      />
                      {/* {hostelData[country]?.length > 4 && (
                        <div className="absolute top-[-122px] z-10 flex justify-between w-full">
                          <div className="flex items-center justify-center w-8 h-8 rounded-full cursor-pointer bg-[#E4E6E8] absolute right-[38px]">
                            <ArrowLeft
                              size="15px"
                              color="#000000"
                              strokeWidth="1px"
                            />
                          </div>
                          <div className="flex items-center justify-center w-8 h-8 rounded-full cursor-pointer bg-[#E4E6E8] absolute right-[0px]">
                            <ArrowRight
                              size="15px"
                              color="#000000"
                              strokeWidth="1px"
                            />
                          </div>
                        </div>
                      )} */}
                    </div>
                    <div className='flex sm:hidden items-center justify-between my-6 w-full gap-x-5'>
                      <div className='flex w-full items-center justify-center'>
                        <Link
                          href={`/tophostel?country=${country}`}
                          className='text-sm font-semibold text-black bg-primary-blue rounded-4xl sm:py-4 py-3 sm:px-12 px-6 hover:bg-sky-blue-750'
                          prefetch={false}
                        >
                          See All
                        </Link>
                      </div>
                    </div>
                  </div>
                )
            )}
          </div>
        )}
      </section>
    </>
  );
};

export default Page;
