import rideModel from '../models/rides.js';
import Response from '../utills/response.js';

const addRide = async (data) => {
    try {
        // Limit the number of rides per user to 2
        const rideCount = await rideModel.find({ user: data.user });
        // if (rideCount.length >= 2) {
        //     throw new Error('You can only have 2 active rides at a time.');
        // }

        const rideData = {
            ...data,
            //  user: userId  // Associate the ride with the logged-in user
        };
        return await rideModel.create(rideData);
    } catch (error) {
        console.log("eroor")
        throw error
    }
};

const getAllRides = async (filter = {}, page = 1, limit = 10) => {
    const skip = (page - 1) * limit;
    const rides = await rideModel.find(filter,{isDeleted:false}).skip(skip).limit(limit).exec();
    const totalRides = await rideModel.countDocuments(filter);
    return { rides, totalRides };
};

const getRideById = async (rideId) => {
    try {
        const ride = await rideModel.findById(rideId).populate('user', 'name profileImage _id');
        if (!ride) {
            throw new Error('Ride Not Found');
        }
        return ride;
    } catch (error) {
        console.error("Error getting ride:", error.message);
        throw error;
    }
};

const cancelRide = async (rideId) => {
    try {
        const ride = await rideModel.findById(rideId);
        if (!ride) {
            throw new Error('Ride Not Found');
        }

        ride.isCancelled = true;
        await ride.save();
        return ride;
    } catch (error) {
        console.error("Error canceling ride:", error.message);
        throw error;
    }
};

const getUserRides = async (userId, skip, limit) => {
    try {
        const rides = await rideModel
            .find({ user: userId,isDeleted: false})
            .populate('user', 'name avatar') 
            .skip(skip)       // Skip the specified number of documents
            .limit(limit);     // Limit the number of documents returned
        return rides;
    } catch (error) {
        console.error("Error fetching user rides:", error.message);
        throw error;
    }
};

export const deleteRide = async (rideId) => {
    try {
        const ride = await rideModel.findById(rideId);
        if (!ride) {
            throw new Error('Ride Not Found');
        }

        ride.isDeleted = true;
        await ride.save();
        return ride;
    } catch (error) {
        console.error("Error canceling ride:", error.message);
        throw error;
    }
};
export { addRide, getAllRides, getRideById, cancelRide, getUserRides };
