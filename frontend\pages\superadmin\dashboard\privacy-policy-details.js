import Link from "next/link";
import React from "react";

const PrivacyDetails = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 dark:bg-[#171616] overflow-y-auto scroll-smooth ">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        Privacy Details
      </h2>
      <div className="bg-white border rounded-xl mt-5 h-auto dark:bg-black dark:border-none">
        <div className="p-8 flex flex-col gap-y-6">
          <div className="">
            <div className="flex">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  Introduction
                </h1>
                <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                  Welcome to MixDorm! Your privacy is important to us. This
                  Privacy Policy explains how we collect, use, and protect your
                  personal information when you visit our website and use our
                  services.
                </p>
              </div>
              <div className="">
                <Link href={"/superadmin/dashboard/privacy-edit"} className="text-white text-sm font-poppins py-1 md:py-2 lg:py-2 px-6 lg:px-8   rounded bg-sky-blue-650">
                  Edit
                </Link>
              </div>
            </div>
            <div className="flex w-[90%]">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  Information: We collect the following type of information
                </h1>
                <div>
                  <h1 className="text-base text-black font-bold dark:text-[#B6B6B6]">
                    1. Personal Information
                  </h1>
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    This includes your name, email address, phone number, and
                    payment information. We collect this information when
                    you register on our site, book accommodation, or
                    contact us for support.
                  </p>

                  <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
                    2. Non-Personal Information:
                  </h1>
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    This includes your browser type, IP address, device
                    information, and usage data. We collect this
                    information to improve our website and
                    services.
                  </p>

                  <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
                    3. Cookies and Tracking Technologies:
                  </h1>
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    We use cookies to enhance your experience on our site.
                    Cookies are small files stored on your device that
                    help us understand your preferences and usage
                    patterns.
                  </p>
                </div>
              </div>
            </div>
            <div className="flex w-[90%]">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  How we use your information to
                </h1>
                <div>
                  <h1 className="text-base text-black font-bold dark:text-[#B6B6B6]">
                    1. Provide Services
                  </h1>
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    Process your bookings, manage your account, and provide
                    customer support.
                  </p>

                  <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
                    2. Improve Our Services:
                  </h1>
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    Analyze usage data to enhance our website, services, and
                    user experience.
                  </p>

                  <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
                    3. Marketing and Communication:
                  </h1>
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    Send you promotional materials, newsletters, and updates
                    about our services. You can opt out of these
                    communications at any time.
                  </p>

                  <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
                    4. Legal Compliance:
                  </h1>
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    Comply with legal obligations and protect our rights and the
                    rights of our users. Sharing Your Information.
                  </p>
                </div>
              </div>
            </div>
            <div className="flex w-[90%]">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  We do not sell, trade, or rent your personal information to
                  third parties. We may share your information with
                </h1>
                <div>
                  <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
                    1. Service Providers
                  </h1>
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    •Trusted third parties that help us operate our website,
                    process payments, and  provide customer support.
                  </p>
                  <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
                    2. Legal Authorities
                  </h1>
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • When required by law or to protect our rights and the
                    safety of our users.
                  </p>
                  <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
                    • Data Security
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-start justify-start p-8">
          <Link
            href={"/superadmin/dashboard/privacy-policy"}
            className="text-white py-2 w-32 max-w-md rounded bg-sky-blue-650 flex items-center justify-center"
          >
            Cancel
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PrivacyDetails;
