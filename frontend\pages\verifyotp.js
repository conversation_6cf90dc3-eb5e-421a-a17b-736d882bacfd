import React, { useState } from "react";
import dynamic from "next/dynamic";
const OTPInput = dynamic(() => import("otp-input-react"), { ssr: false });
import toast from "react-hot-toast";
import { resendOtpApi, verifyOtp } from "@/services/webflowServices";
import { setItemLocalStorage,setToken } from "@/utils/browserSetting";
import { useRouter } from "next/router";
import { requestForFCMToken } from "@/utils/firebaseConfig";
import { saveFirebaseToken } from "@/services/ownerflowServices";
import { useNavbar } from "@/components/home/<USER>";


const VerifyForm = () => {
  const [email, setEmail] = useState("");
  const [otp, setOtp] = useState("");
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { updateUserStatus,updateUserRole } = useNavbar();


  const handleVerifyOtp = async (e) => {
    e.preventDefault();

    if (!email) {
      toast.error("Please enter your email.");
      return;
    }

    if (otp.length !== 6) {
      toast.error("Please enter a 6-digit OTP.");
      return;
    }

    setLoading(true);

    try {
      const response = await verifyOtp({ email, otp });

      if (response?.data?.status) {
        setItemLocalStorage("name", response?.data?.data?.user?.name.first);
        setItemLocalStorage("id", response?.data?.data?.user?._id);
        setItemLocalStorage("role", response?.data?.data?.user?.role);
        setItemLocalStorage("email", response?.data?.data?.user?.email);
        setItemLocalStorage("contact", response?.data?.data?.user?.contact);
        setToken(response?.data?.data?.token);
        updateUserStatus(response?.data?.data?.token);
        updateUserRole(response?.data?.data?.user?.role);
        toast.success(response?.data?.message || "OTP verified successfully!");
        const fcmToken = await requestForFCMToken();
        if (fcmToken) {
          setItemLocalStorage("FCT", fcmToken);
          await saveFirebaseToken({
            token: fcmToken,
            userId: response?.data?.data?.user?._id,
          });
        }
        router.push("/");
      } else {
        toast.error(response.data.message || "OTP verification failed.");
        setOtp("");
      }
    } catch (error) {
      toast.error(
        error.response?.data?.message ||
          "An error occurred during verification."
      );
      console.error("Verification error:", error);
      setOtp("");
    } finally {
      setLoading(false);
    }
  };

  const resendOTP = async () => {
    if (!email) {
      toast.error("Please enter your email.");
      return;
    }

    try {
      const response = await resendOtpApi({ email });

      if (response?.data?.status) {
        toast.success(`We’ve Resent a verification code to ${email}. Please enter this code to continue.`);
        setOtp("");
      } else {
        toast.error(
          response.data.message || "Failed to resend verification code."
        );
        setOtp("");
      }
    } catch (error) {
      toast.error(
        error.response?.data?.message ||
          "An error occurred while resending OTP."
      );
      console.error("Resend OTP error:", error);
      setOtp("");
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-[#F7F7F7] w-full pb-[10rem] pt-[3rem]">
      <div className="w-full max-w-3xl pb-6 bg-white shadow-md px-14 pt-14 rounded-3xl md:max-w-2xl">
        <form onSubmit={handleVerifyOtp}>
          <div className="mb-6 font-semibold">
            <h4 className="font-semibold text-2xl text-center mb-0">
              Enter Authentication Code
              <span className="block mt-2 text-gray-500 font-normal text-base">
                Two-Factor Authentication (2FA)
              </span>
            </h4>
          </div>

          <div className="mb-10">
            <label htmlFor="" className="text-gray-500 font-normal text-base">
              Enter Email
            </label>
          <div className="mb-6">
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
              placeholder="Enter your email"
              required
            />
          </div>
          </div>

          <div className="mb-10">
            <label htmlFor="" className="text-gray-500 font-normal text-base">
              Enter Code
            </label>
            <OTPInput
              value={otp}
              onChange={(otp) => setOtp(otp)}
              autoFocus
              OTPLength={6}
              otpType="number"
              secure
              className="w-full text-lg input-otp font-light mb-5 py-2.5 mt-1 bg-transparent outline-none focus:outline-none placeholder:text-gray-50/40 text-gray-50"
              inputClassName="w-10 h-10 border border-[#e5e7eb] focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500 bg-white"
            />
          </div>
          <div className="flex justify-center">
            <a
              href="#"
              className="mb-8 text-center text-sm text-primary-blue hover:underline hover:text-sky-blue-750"
              onClick={resendOTP}
            >
              Resend verification code
            </a>
          </div>
          <button
            type="submit"
            className="w-full mb-4 bg-primary-blue font-semibold text-white py-4 rounded-2xl hover:bg-sky-blue-750 hover:text-white transition duration-200"
            disabled={loading}
          >
            {loading ? "Verifying..." : "Verify"}
          </button>
        </form>
      </div>
    </div>
  );
};

export default VerifyForm;
