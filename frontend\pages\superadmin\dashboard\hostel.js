"use client";
import { Plus, Search } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import HostelTable from "@/components/superadmin/TableHostel";
import HostelFilter from "@/components/superadmin/HostelFilter";
import Link from "next/link";
import { hostelListAdmin } from "@/services/adminflowServices";
import toast from "react-hot-toast";
import Pagination from "@/components/common/commonPagination";
import Loader from "@/components/loader/loader";

const Hostel = () => {
  const [hostelsData, setHostelsData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalData, setTotalData] = useState();
  const isFirstRender = useRef(null);

  useEffect(() => {
    if (!isFirstRender.current) {
      fetchList(currentPage);
    } else {
      isFirstRender.current = false;
    }
  }, [currentPage, itemsPerPage]);

  const fetchList = async (page) => {
    setLoading(true);

    try {
      const response = await hostelListAdmin(page, itemsPerPage);

      if (response.status == 200) {
        setHostelsData(response?.data?.data?.properties);
        setTotalPages(response?.data?.data?.pagination?.totalPages || 1);
        setTotalData(response?.data?.data?.pagination?.totalProperties);
      }
      if (response.status !== 200) {
        toast.error(response?.data?.message);
      }
    } catch (error) {
      toast.error(error || "Something went wrong!");
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  return (
    <>
      <Loader open={loading} />
      {/* {editOpen ? (
        <EditHostel onClose={() => setEditOpen(false)} />
      ) : openAddHostel ? (
        <AddHostel onClose={() => setOpenAddHostel(false)} />
      ) : ( */}
      <div className='lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]'>
        <div className='flex items-center justify-between w-full '>
          <h2 className='text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100'>
            Hostel
          </h2>
          <div className='w-[50%] gap-x-5 flex justify-end items-center'>
            <div className='relative w-fit  '>
              {" "}
              <input
                type='search'
                className='relative flex items-center justify-center w-full py-2 pl-10 pr-4 text-xs font-normal border border-gray-200 rounded-full outline-none text-gray-130 h-9 bg-white dark:bg-transparent dark:text-[#757575]'
                placeholder='Search hostel , Country etc..'
              />
              <Search
                className='absolute -translate-y-1/2 top-1/2 left-3 text-gray-130 dark:text-[#757575]'
                size={20}
              />
            </div>
            <Link
              href={"/superadmin/dashboard/hostel-add"}
              className='w-[130px] px-4 py-2 text-xs font-normal text-white rounded relative flex justify-center items-center h-9 bg-sky-blue-650'
            >
              <Plus size={18} className='mr-1' /> Add Hostel
            </Link>
          </div>
        </div>
        <div className='mt-5'>
          <HostelFilter />
        </div>
        <div>
          <HostelTable  hostelsData={hostelsData}/>
          {hostelsData?.length > 0 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalData || 0}
          itemsPerPage={itemsPerPage}
          onPageChange={handlePageChange}
          onItemsPerPageChange={handleItemsPerPageChange}
        />
      )}
        </div>
      </div>
     
    </>
  );
};

export default Hostel;
