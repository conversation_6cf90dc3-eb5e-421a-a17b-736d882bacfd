import mongoose from "mongoose";

const RoomInventorySchema = new mongoose.Schema({
  propertyId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
  },
  roomId: {
    type: Number,
    required: true,
  },
  date: {
    type: Date,
    required: true,
  },
  currency: {
    type: String,
    default: "EUR"
  },
  rate: {
    type: Number
  },
  availableUnits: {
    type: Number
  },
  close: {
    type: Boolean
  },
  closeArrival: {
    type: Boolean
  },
  closeDeparture: {
    type: Boolean
  },
  baseRate: {
    type: Number
  },
  ratePlan: {
    type: Number
  },
  ratePlanName: {
    type: String
  },
  minLos: Number,
  maxLos: Number,
  minAdvanceOffset: Number,
  maxAdvanceOffset: Number,
  numAdultsIncluded: Number,
  numChildrenIncluded: Number,
  restrictions: {
    type: Map,
    of: Number,
    default: {}
  }
}, { timestamps: true });

export default mongoose.model("RoomInventoryARI", RoomInventorySchema, 'RoomInventoryARI');
