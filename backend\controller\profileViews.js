import Response from "../utills/response.js";
import { profileViewsServices,getProfileViewers} from "../services/profileViews.js";

export const profileViews = async (req, res) => {
    try {
        const data = {
            viewBy:req.user._id,
            user:req.params.id
        }
        await profileViewsServices(data)
        return Response.Created(res, data, 'Profile View');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};


export const fetchProfileViewers = async (req, res) => {
    try {
      const viewers = await getProfileViewers(req.user._id);
      return Response.OK(res, viewers, 'Profile viewers data fetched successfully');
    } catch (error) {
      return Response.InternalServerError(res, null, error.message);
    }
  };