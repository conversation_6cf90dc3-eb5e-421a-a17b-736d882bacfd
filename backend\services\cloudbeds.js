import axios from 'axios';

const CLOUD_BEDS_BASE_URL = 'https://hotels.cloudbeds.com/api/v1.1';
const CLIENT_ID = 'YOUR_CLIENT_ID';
const CLIENT_SECRET = 'YOUR_CLIENT_SECRET';

const getAuthHeaders = async () => {
  const token = await getAccessToken();
  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };
};

export const createRoomOnCloudbeds = async (roomData) => {
  const headers = await getAuthHeaders();
  const response = await axios.post(`${CLOUD_BEDS_BASE_URL}/properties/YOUR_PROPERTY_ID/rooms`, roomData, { headers });
  return response.data;
};

export const updateRoomOnCloudbeds = async (roomId, roomData) => {
  const headers = await getAuthHeaders();
  const response = await axios.put(`${CLOUD_BEDS_BASE_URL}/properties/YOUR_PROPERTY_ID/rooms/${roomId}`, roomData, { headers });
  return response.data;
};

export const deleteRoomFromCloudbeds = async (roomId) => {
  const headers = await getAuthHeaders();
  const response = await axios.delete(`${CLOUD_BEDS_BASE_URL}/properties/YOUR_PROPERTY_ID/rooms/${roomId}`, { headers });
  return response.data;
};
