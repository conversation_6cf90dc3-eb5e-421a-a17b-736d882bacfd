import React, { useState, Fragment, useEffect } from "react";
import { editPropertyApi } from "@/services/ownerflowServices";
import toast from "react-hot-toast";
import { Dialog, Transition } from "@headlessui/react";

const HostelRulesNew = ({
  id,
  data,
  updatePropertyData,
  loading,
  setLoading,
}) => {
  // Store rules as a single string
  const [hostelRules, setHostelRules] = useState(
    data?.rules?.thingsToNote || ""
  );
  const [openEditgpolicy, setopenEditgpolicy] = useState(false);
  const [currentDescription, setCurrentDescription] = useState("");
  const closegpolicyeditModal = () => {
    setopenEditgpolicy(false);
    setCurrentDescription("");
  };

  useEffect(() => {
    setHostelRules(data?.rules?.thingsToNote || "");
  }, [data]);

  // Handle input changes for add/edit
  const handleInputChange = (e) => {
    setCurrentDescription(e.target.value);
  };

  // Add new policy
  // No add policy anymore

  // Edit existing policy
  const handleEditPolicy = async () => {
    if (!currentDescription.trim()) {
      toast.error("Description is required.");
      return;
    }
    setLoading(true);
    try {
      const payload = {
        rules: {
          ...data?.rules,
          thingsToNote: currentDescription,
        },
      };
      const res = await editPropertyApi(id, payload);
      if (res?.status === 200) {
        toast.success(res?.data?.message);
        setHostelRules(currentDescription);
        updatePropertyData();
        closegpolicyeditModal();
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setLoading(false);
    }
  };

  // Open edit modal for a specific policy
  // Open edit modal for the description
  const openEditModal = () => {
    setCurrentDescription(hostelRules);
    setopenEditgpolicy(true);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-2">
        <h2 className="sm:text-xl text-base font-medium">House Rules</h2>
        <button
          className="flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-primary-blue font-medium"
          onClick={openEditModal}
        >
          Edit
        </button>
      </div>
      {/* Show description or loading */}
      {loading ? (
        <div className="mt-6">
          <div className="border p-4 rounded-lg relative animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/3 mb-3"></div>
            <div className="space-y-2">
              <div className="h-3 bg-gray-200 rounded w-full"></div>
              <div className="h-3 bg-gray-200 rounded w-5/6"></div>
              <div className="h-3 bg-gray-200 rounded w-4/6"></div>
            </div>
          </div>
        </div>
      ) : (
        <div className="mt-6">
          {hostelRules && hostelRules.trim().length > 0 ? (
            <p className="text-[#888888] sm:text-base text-sm font-normal font-inter mt-5 whitespace-pre-line">
              {hostelRules}
            </p>
          ) : (
            <div className="text-gray-500 text-sm">
              No house rules added yet.
            </div>
          )}
        </div>
      )}

      {/* Edit Modal */}
      <Transition show={openEditgpolicy} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-50"
          onClose={closegpolicyeditModal}
        >
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <h2 className="page-title">Edit House Rules</h2>
                    <button
                      onClick={closegpolicyeditModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005;
                    </button>
                  </div>
                  <div className="sm:px-6 px-4">
                    <div>
                      <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                        Description
                      </label>
                      <textarea
                        className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                        placeholder="Enter house rules description"
                        rows={6}
                        value={currentDescription}
                        onChange={handleInputChange}
                        style={{ outline: "none" }}
                      ></textarea>
                    </div>
                    <div className="xs:flex block  items-center justify-between w-full sm:my-14 my-7 gap-4 col-span-2 py-4 bg-white/80 sticky bottom-0 backdrop-blur-sm">
                      <button
                        type="button"
                        className="hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 px-4 border-black rounded-lg w-full text-sm xs:mb-0 mb-2"
                        onClick={closegpolicyeditModal}
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        onClick={handleEditPolicy}
                        className="bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm"
                      >
                        Save Changes
                      </button>
                    </div>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  );
};

export default HostelRulesNew;
