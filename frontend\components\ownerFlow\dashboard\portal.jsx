// components/Portal.js
import { useEffect, useRef } from "react";
import { createPortal } from "react-dom";

const Portal = ({ children }) => {
  const containerRef = useRef(null);

  if (!containerRef.current && typeof window !== "undefined") {
    const div = document.createElement("div");
    div.style.position = "absolute";
    div.style.zIndex = 9999;
    document.body.appendChild(div);
    containerRef.current = div;
  }

  useEffect(() => {
    return () => {
      if (containerRef.current) {
        document.body.removeChild(containerRef.current);
      }
    };
  }, []);

  return containerRef.current ? createPortal(children, containerRef.current) : null;
};

export default Portal;
