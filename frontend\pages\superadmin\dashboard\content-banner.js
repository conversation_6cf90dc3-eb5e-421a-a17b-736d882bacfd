"use client";
import { Plus } from "lucide-react";
import React from "react";
import { FiEye } from "react-icons/fi";
import { TfiPencilAlt } from "react-icons/tfi";
import { FaRegTrashCan } from "react-icons/fa6";
import Image from "next/image";
import Link from "next/link";

const Banner = () => {
  const landingbannerData = [
    {
      id: 1,
      header: "Header",
      title: "Explore More, Spend Less!",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/home_banner.png`,
    },
  ];

  return (
    <div className="w-full p-7 bg-sky-blue-20 lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px]  float-end  overflow-y-auto scroll-smooth dark:bg-[#171616] h-screen">
      {" "}
      <div className="flex items-center justify-between w-full ">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins  dark:text-gray-100">
          Home Page Banners
        </h2>
        <div className="w-[20%] lg:w-[50%] gap-x-5 flex justify-end items-center">
          <Link
          href={"/superadmin/dashboard/content-banner-add"}
            className={` px-2 lg:px-4 py-2 text-sm font-normal font-poppins text-white rounded relative flex justify-center items-center bg-sky-blue-650 `}
            type="button"
            
          >
            <Plus size={18} className="mr-1" /> Banner
          </Link>
        </div>
      </div>
      <div className="bg-white border-b border-l border-r rounded-xl dark:bg-black dark:border-none">
        <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border-t dark:border-none pb-2">
          <table className="min-w-full divide-y  bg-white rounded-xl divide-gray-200 dark:bg-black">
            <thead>
              <tr className=" ">
                <th className="px-5 py-6 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  HEADER
                </th>

                <th className=" py-6 bg-white text-center text-sm font-poppins  font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  TITLE
                </th>

                <th className="pl-10 lg:pl-0 pr-14 lg:pr-14 py-6  bg-white text-left lg:text-center  text-sm font-poppins  font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  IMAGE
                </th>

                <th className="pr-12 lg:pr-12 py-6  bg-white text-end text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  ACTION
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 border-y text-black/70 dark:text-[#757575]">
              {landingbannerData.map((banner) => (
                <tr key={banner.id}>
                  <td className="whitespace-nowrap px-5 text-sm font-poppins font-medium ">{banner.header}</td>
                  <td className="text-center whitespace-nowrap px-5 text-sm font-poppins font-medium ">{banner.title}</td>
                  <td className="pl-0 lg:pl-36">
                    <button className="whitespace-nowrap font-medium py-1 px-3 rounded">
                      <Image
                        src={banner.image}
                        width={170}
                        height={90}
                        alt="Image"
                        className="w-32 h-12 lg:w-64 lg:h-32 mt-0 lg:mt-4"
                      />
                    </button>
                  </td>

                  <td className=" py-5 pl-10 lg:pl-0 pr-6 flex justify-end">
                    <Link href={"/superadmin/dashboard/content-banner-details"} className=" border p-2 rounded-l-lg text-black/75 hover:text-blue-700 dark:text-[#757575] dark:hover:text-blue-700">
                      <FiEye />
                    </Link>
                    <Link href={"/superadmin/dashboard/content-banner-edit"} className=" border p-2 text-black/75 hover:text-yellow-400 dark:text-[#757575] dark:hover:text-yellow-400">
                      <TfiPencilAlt />
                    </Link>
                    <button className=" p-2 border rounded-r-lg text-red-600">
                      <FaRegTrashCan />
                    </button>
                  </td>
                  
                </tr>
                
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Banner;