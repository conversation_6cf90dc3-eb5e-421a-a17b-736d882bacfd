import React, { useState } from "react";
import Image from "next/image";
import { toast } from "react-hot-toast";
import { editProperty<PERSON><PERSON> } from "@/services/ownerflowServices";

// const facilityIcons = {
//   BREAKFASTINCLUDED: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/breakfast.svg`,
//   LINENINCLUDED: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/wifi.svg`,
//   FREEPARKING: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/parking.svg`,
//   TOWELSINCLUDED: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/towel.svg`,
//   FREEWIFI: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/wifi.svg`,
//   FREEINTERNETACCESS: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/internet.svg`,
//   FREECITYMAPS: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/citymap.svg`,
// };
const facilityIcons = {
    BREAKFASTAVAILABLE: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/breakfast.svg`,
    LINENINCLUDED: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/linen_included.svg`,
    FREEPARKING: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/parking.svg`,
    PARKINGPUBLIC: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/parking.svg`,
    TOWELSONRENT: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/towels_on_rent.svg`,
    FREEWIFI: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/free_wi_fi.svg`,
    FREEINTERNETACCESS: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/internet.svg`,
    FREECITYMAPS: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/citymap.svg`,
    LOCKERS: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/lockers.svg`,
    HOTWATER: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/hot_water.svg`,
    LAUNDRYSERVICE: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/laundry_services.svg`,
    CARDPAYMENTACCEPTED: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/card_payment_accepted.svg`,
    COMMONTELEVISION: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/common_television.svg`,
    WATERDISPENSER: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/water_dispenser.svg`,
    AIRCONDITIONING: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/air_conditioning.svg`,
    RECEPTION24X7: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/24_7_reception.svg`,
    COMMONHANGOUTAREA: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/common_hangout_area.svg`,
    CAFE: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/cafe.svg`,
    INHOUSEACTIVITIES: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/in_house_activities.svg`,
    BEDSIDELAMPS: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/bedside_lamps.svg`,
    STORAGEFACILITY: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/storage_facility.svg`,
    UPIPAYMENTACCEPTED: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/upi_payment_accepted.svg`,
    SHOWER: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/shower.svg`,
    PARKING: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/parking.svg`,
    LAUNDRYSERVICES: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/laundry_services.svg`,
};
const amenitiesList = [
  { key: "BREAKFASTINCLUDED", label: "Free Breakfast" },
  { key: "LINENINCLUDED", label: "Linen Included" },
  { key: "FREEPARKING", label: "Free Parking" },
  { key: "TOWELSINCLUDED", label: "Towels Included" },
  { key: "FREEWIFI", label: "Free WiFi" },
  { key: "FREEINTERNETACCESS", label: "Free Internet Access" },
];

const Amenities = ({ data, id,  loading , updatePropertyData }) => {
  const [amenitiesData, setAmenitiesData] = useState(
    amenitiesList.map((amenity) => ({
      id: amenity.key,
      name: amenity.label,
      checked:
        data?.freeFacilities?.some((facility) => facility.id === amenity.key) ||
        false,
    }))
  );

  // eslint-disable-next-line no-unused-vars
  const [edit, setEdit] = useState(false);

  const handleCheckboxChange = (index) => {
    const newAmenities = [...amenitiesData];
    newAmenities[index].checked = !newAmenities[index].checked;
    setAmenitiesData(newAmenities);
  };

  const checkboxes = [
    {
      key: "lockers",
      label: "Lockers",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/lockers.svg`,
    },
    {
      key: "hot_water",
      label: "Hot water",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/hot_water.svg`,
    },
    {
      key: "laundry_services",
      label: "Laundry Services (Extra)",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/laundry_services.svg`,
    },
    {
      key: "FREEWIFI",
      label: "Free Wi-Fi",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/free_wi_fi.svg`,
    },
    {
      key: "card_payment_accepted",
      label: "Card Payment Accepted",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/card_payment_accepted.svg`,
    },
    {
      key: "common_television",
      label: "Common Television",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/common_television.svg`,
    },
    {
      key: "water_dispenser",
      label: "Water Dispenser",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/water_dispenser.svg`,
    },
    {
      key: "air_conditioning",
      label: "Air-conditioning",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/air_conditioning.svg`,
    },
    {
      key: "reception",
      label: "24/7 Reception",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/24_7_reception.svg`,
    },
    {
      key: "common_hangout_area",
      label: "Common hangout area",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/common_hangout_area.svg`,
    },
    {
      key: "cafe",
      label: "Cafe",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/cafe.svg`,
    },
    {
      key: "in_house_activities",
      label: "In-house Activities",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/in_house_activities.svg`,
    },
    {
      key: "bedside_lamps",
      label: "Bedside Lamps",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/bedside_lamps.svg`,
    },
    {
      key: "BREAKFASTINCLUDED",
      label: "Breakfast (Extra)",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/breakfast.svg`,
    },
    {
      key: "storage_facility",
      label: "Storage Facility",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/storage_facility.svg`,
    },
    {
      key: "TOWELSINCLUDED",
      label: "Towels on rent",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/towels_on_rent.svg`,
    },
    {
      key: "LINENINCLUDED",
      label: "Linen Included",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/linen_included.svg`,
    },
    {
      key: "upi_payment_accepted",
      label: "UPI Payment Accepted",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/upi_payment_accepted.svg`,
    },
    {
      key: "shower",
      label: "Shower",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/shower.svg`,
    },
    {
      key: "FREEPARKING ",
      label: "Parking (public)",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/parking.svg`,
    },
  ];

  const [selected, setSelected] = useState(() => {
    const initialState = {};
    checkboxes.forEach(({ key }) => {
      const normalizedKey = key.trim().toUpperCase().replace(/-|_/g, "");
      initialState[key] =
        data?.freeFacilities?.some(
          (facility) => facility.id === normalizedKey
        ) || false;
    });
    return initialState;
  });

  const toggleCheckbox = async (key) => {
    setSelected((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));

    const selectedAmenities = checkboxes
      .filter(({ key: checkboxKey }) => {
        if (checkboxKey === key) {
          return !selected[key];
        }
        return selected[checkboxKey];
      })
      .map(({ key: checkboxKey, label }) => ({
        id: checkboxKey.trim().toUpperCase().replace(/-|_/g, ""),
        name: label,
      }));

    try {
      const payload = {
        freeFacilities: selectedAmenities,
      };
      const res = await editPropertyApi(id, payload);
      if (res?.status === 200) {
        toast.success(res?.data?.message);
        updatePropertyData();
      }
    } catch (error) {
      console.log("error", error);
      setSelected((prev) => ({
        ...prev,
        [key]: !prev[key],
      }));
    }
  };

  return (
    <>
      {loading ? (
        <div>
          <div className="flex items-center justify-between mb-4">
            <div className="h-6 w-24 bg-gray-200 rounded animate-pulse"></div>
          </div>

          {/* Checkboxes skeleton */}
          <div className="grid md:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-y-4">
            {[...Array(9)].map((_, index) => (
              <div key={index} className="flex items-center gap-3">
                <div className="w-5 h-5 bg-gray-200 rounded-sm animate-pulse"></div>
                <div className="w-5 h-5 bg-gray-200 rounded-full animate-pulse"></div>
                <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
              </div>
            ))}
          </div>

          {/* List skeleton */}
          <div className="mt-6 space-y-3">
            {[...Array(5)].map((_, index) => (
              <div key={index} className="flex items-center">
                <div className="w-4 h-4 bg-gray-200 rounded mr-3 animate-pulse"></div>
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-gray-200 rounded-full mr-2 animate-pulse"></div>
                  <div className="h-4 w-32 bg-gray-200 rounded animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div>
          <div className="flex items-center justify-between mb-4">
            <h2 className="sm:text-xl text-base font-medium">Amenities</h2>
            {/* <div>
          <button
            className="bg-primary-blue text-white px-4 py-2 text-sm rounded-lg mr-2"
            onClick={() => setEdit(!edit)}
          >
            {edit ? "Cancel" : "Edit Amenities"}
          </button>
          {edit && (
            <button
              className="bg-primary-blue text-white px-4 py-2 text-sm rounded-lg"
              onClick={handleSubmit}
            >
              Submit
            </button>
          )}
        </div> */}
          </div>
          <div className="grid md:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-y-2">
            {checkboxes.map(({ key, label, icon }) => (
              <label
                key={key}
                className="flex items-center gap-2 cursor-pointer"
              >
                <input
                  type="checkbox"
                  checked={selected[key]}
                  onChange={() => toggleCheckbox(key)}
                  className="hidden"
                />
                <div
                  className={`sm:w-5 sm:h-5 min-w-5 min-h-5 w-4 h-4 flex items-center justify-center border rounded-sm transition-all ${
                    selected[key] ? "bg-[#40E0D0]" : "border-[#979797]"
                  }`}
                >
                  {selected[key] && (
                    <span className="text-black sm:text-md text-sm">✔</span>
                  )}
                </div>
                <Image src={icon} width={20} height={20} />
                <span className="sm:text-sm text-xs font-medium text-black">
                  {label}
                </span>
              </label>
            ))}
          </div>
          <ul className="list-disc text-base text-[#605959] space-y-2 w-[90%]">
            {edit &&
              amenitiesList.map((amenity, index) => (
                <div key={amenity.key} className="flex items-center mb-2">
                  <input
                    type="checkbox"
                    checked={amenitiesData[index].checked}
                    onChange={() => handleCheckboxChange(index)}
                    className="mr-2"
                  />
                  <label className="text-sm flex items-center space-x-2">
                    {facilityIcons[amenity.key] && (
                      <div className="flex items-center justify-center w-8 h-8 mr-2">
                        <Image
                          src={facilityIcons[amenity.key]}
                          alt={`${amenity.label} Icon`}
                          className="w-[18px] h-[18px]"
                          width={18}
                          height={18}
                          aria-label={amenity.label}
                          loading="lazy"
                        />
                      </div>
                    )}
                    {amenity.label}
                  </label>
                </div>
              ))}
          </ul>
        </div>
      )}
    </>
  );
};

export default Amenities;
