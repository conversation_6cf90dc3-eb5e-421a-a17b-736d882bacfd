import express from 'express';
import { profileViews ,fetchProfileViewers} from '../controller/profileViews.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();

router.post('/:id', checkAuth('profile_view'), profileViews);
router.get('/', checkAuth('profile_view'), fetchProfileViewers);
export default router;
/**
 * @swagger
 * tags:
 *   name: Profile Views
 *   description: API for managing profile views
 * 
 * /profileViews/{id}:
 *   post:
 *     summary: Log a profile view
 *     tags: [Profile Views]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the user whose profile is being viewed
 *     responses:
 *       '201':
 *         description: Profile view logged successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Profile view logged successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                       description: The ID of the user whose profile was viewed
 *                     viewerId:
 *                       type: string
 *                       description: The ID of the user who viewed the profile
 *                     viewedAt:
 *                       type: string
 *                       format: date-time
 *                       description: The timestamp when the profile was viewed
 *       '400':
 *         description: Bad request, such as when a user tries to view their own profile
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Users can't view their own profile."
 *       '404':
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: User not found
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */
/**
 * @swagger
 * tags:
 *   name: Profile Views
 *   description: API for managing profile views
 * 
 * /profileViews:
 *   get:
 *     summary: Get users who have viewed the logged-in user's profile
 *     tags: [Profile Views]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '200':
 *         description: List of users who viewed the profile
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Profile viewers fetched successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         description: User ID
 *                       name:
 *                         type: string
 *                         description: User's name
 *                       profileImage:
 *                         type: string
 *                         description: URL of the user's profile image
 *       '401':
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Unauthorized
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */


