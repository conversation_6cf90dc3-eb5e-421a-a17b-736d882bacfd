import React, { useState, useRef, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { Box, Divider, Drawer } from "@mui/material";
import { usePathname } from "next/navigation";

const HeroNavbar = () => {
  const bannerMenu = [
    {
      name: "About Us",
      link: "/aboutus",
    },
    {
      name: "Awards",
      link: "/awards",
    },
    {
      name: "Help",
      link: "/help",
    },
    {
      name: "Booking Guarantee",
      link: "/bookinggaurantee",
    },
    {
      name: "Booking Refund Policy",
      link: "/refundpolicy",
    },
    {
      name: "Privacy Policy",
      link: "/privacypolicy",
    },
    {
      name: "Terms & Conditions",
      link: "/terms-condition",
    },
    {
      name: "No-Show Policy",
      link: "/no-show",
    },
    {
      name: "FAQs",
      link: "/faqs",
    },
  ];
  // Mobile Drawer
  const [openDrawer, setOpenDrawer] = useState(false);
  const activeRef = useRef(null);
  const pathname = usePathname();

  useEffect(() => {
    // Scroll into view only when ref exists
    if (activeRef.current) {
      activeRef.current.scrollIntoView({
        behavior: "smooth",
        inline: "center", // or "nearest" if smoother
        block: "nearest",
      });
    }
  }, [pathname]);

  const toggleDrawer = (newOpen) => () => {
    setOpenDrawer(newOpen);
  };

  return (
    <>
      <div className="relative">
        <Image
          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/content_bannerBG.jpg`}
          width={1519}
          height={647}
          alt="BgImg"
          className="w-full  h-auto lg:h-[400px]"
          loading="lazy"
        />
        <div className="absolute inset-0 bg-black opacity-50 pointer-events-none z-10 " />
        <div className="py-5 px-0 absolute bottom-0 w-full z-20">
          <div className="container  w-full  flex justify-start gap-x-4 lg:gap-x-0 pb-2 md:justify-around overflow-x-auto hover-scrollbar">
            {bannerMenu.map((element) => {
              const isActive = pathname === element.link;
              return (
                <Link
                  key={element.name}
                  href={element.link}
                  className={`font-semibold text-sm text-white before:content-['•'] before:mr-1 ${
                    isActive ? "no-underline text-primary-blue" : ""
                  }`}
                  prefetch={false}
                  ref={isActive ? activeRef : null}
                >
                  <span
                    className={`${
                      isActive ? "underline underline-offset-4 text-primary-blue" : "hover:underline"
                    } whitespace-nowrap`}
                  >
                    {element.name}
                  </span>
                </Link>
              );
            })}
          </div>
        </div>
      </div>

      <Drawer open={openDrawer} onClose={toggleDrawer(false)}>
        <Box
          sx={{ width: 250 }}
          role="presentation"
          onClick={toggleDrawer(false)}
        >
          <Link href="/" rel="canonical" className="p-4 block" prefetch={false}>
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mixdrom-white.svg`}
              width={155}
              height={40}
              alt="Mixdorm"
              title="Mixdorm"
              className="max-w-[110px] md:max-w-[155px] md:max-h-24 relative z-50 w-fit object-contain bg-blend-color-burn cursor-pointer hover:scale-95 duration-500 ease-in-out"
              loading="lazy"
            />
          </Link>
          <Divider />
          {bannerMenu.map((element) => (
            <Link
              key={element.name}
              href={element.link}
              className="font-medium text-sm text-black block px-4 py-3 hover:text-white hover:bg-primary-blue transition-all"
              prefetch={false}
            >
              {element.name}
            </Link>
          ))}
        </Box>
      </Drawer>
    </>
  );
};

export default HeroNavbar;
