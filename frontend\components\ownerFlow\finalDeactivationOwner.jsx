/* eslint-disable react/no-unknown-property */
 
import React from "react";
import Modal from "@mui/material/Modal";
import { IoCloseCircleOutline } from "react-icons/io5";
import Link from "next/link";
import Image from "next/image";

const Finaldeactivation = ({
  openFinaldeactivationpopup,
  handleCloseFinaldeactivationpopup,
}) => {
  const style = {
    position: "fixed",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: "100%",
    bgcolor: "background.paper",
    border: "2px solid #000",
    boxShadow: 24,
  };

  return (
    <>
      <Modal
        open={openFinaldeactivationpopup}
        onClose={handleCloseFinaldeactivationpopup}
        aria-labelledby='modal-modal-title'
        aria-describedby='modal-modal-description'
      >
        <div sx={style}>
          <div className='bg-white rounded-2xl max-w-[500px] mx-auto left-1/2 absolute w-[95%] top-1/2 -translate-y-1/2 -translate-x-1/2 text-center'>
            <div className='md:p-6 p-4'>
              <div className='flex items-center justify-end'>
                <Link
                  href='/'
                  prefetch={false}
                  onClick={handleCloseFinaldeactivationpopup}
                  className='text-black text-2xl hover:text-primary-blue'
                >
                  <IoCloseCircleOutline />
                </Link>
              </div>
              <div className='flex justify-center'>
                <Image 
                  src='https://mixdorm.s3.ap-south-1.amazonaws.com/front-images/deactivate.png'
                  alt='deactivation'
                />
              </div>
              <p className='text-base text-black font-semibold mt-3'>
                Your account has been deactivated. It will automatically
                reactivate after the selected period
              </p>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default Finaldeactivation;
