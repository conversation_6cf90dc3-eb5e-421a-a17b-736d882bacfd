import { getItemLocalStorage } from "@/utils/browserSetting";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import dayjs from "dayjs";
import { GetAnalyticsApi } from "@/services/ownerflowServices";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

import dynamic from "next/dynamic";
import Head from "next/head";
import Link from "next/link";
const Chart = dynamic(() => import("react-apexcharts"), { ssr: false });
const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

const Page = () => {
  const [analyticsData, setAnalyticsData] = useState({});
  const [loading, setLoading] = useState(false);
  const isFirstRender = useRef(null);
  const [selectedDate, setSelectedDate] = useState(dayjs());
  // eslint-disable-next-line no-unused-vars
  const [currentMonth, setCurrentMonth] = useState(dayjs());
  const currentDate = dayjs();
  const defaultPage = currentDate.date() <= 7 ? 1 : 2;
  // eslint-disable-next-line no-unused-vars
  const [currentPage, setCurrentPage] = useState(defaultPage);
  const storedId = getItemLocalStorage("hopid");

  const [selectedMonth, setSelectedMonth] = useState(dayjs());
  const [datesForCurrentPage, setDatesForCurrentPage] = useState([]);
  const currentDateRef = useRef(null);
  const scrollContainerRef = useRef(null);

  useEffect(() => {
    const timeout = setTimeout(() => {
      if (currentDateRef.current && scrollContainerRef.current) {
        currentDateRef.current.scrollIntoView({
          behavior: "smooth",
          block: "nearest",
          inline: "center",
        });
      }
    }, 100); // Slight delay to ensure everything is mounted

    return () => clearTimeout(timeout);
  }, [datesForCurrentPage]);

  useEffect(() => {
    const fetchAnalyticsData = async (id) => {
      setLoading(true);
      try {
        const formattedDate = selectedDate.format("YYYY-MM-DD"); // Format the selected date
        const response = await GetAnalyticsApi(
          id,
          formattedDate,
          formattedDate
        );
        setAnalyticsData(response?.data?.data);
      } catch (error) {
        console.error("Error fetching review data:", error);
      } finally {
        setLoading(false);
      }
    };

    if (!isFirstRender.current) {
      fetchAnalyticsData(storedId);
    } else {
      isFirstRender.current = false;
    }
  }, [storedId, selectedDate]);

  useEffect(() => {
    const getDatesForMonth = () => {
      const daysInMonth = selectedMonth.daysInMonth();
      const dates = [];
      for (let i = 1; i <= daysInMonth; i++) {
        dates.push(selectedMonth.date(i));
      }
      setDatesForCurrentPage(dates);
    };
    getDatesForMonth();
  }, [selectedMonth]);

  const handleMonthChange = (date) => {
    setSelectedMonth(dayjs(date));
    setShowCalendar(false);
    if (!selectedDate.isSame(date, "month")) {
      setSelectedDate(dayjs(date).startOf("month"));
    }
  };

  // Handle date selection
  const handleDateSelection = (date) => {
    const today = dayjs();
    if (date.isAfter(today, "day")) {
      return; // Don't allow future date selection
    }
    setSelectedDate(date);
  };

  // eslint-disable-next-line no-unused-vars
  const [chartOptions, setChartOptions] = useState({
    chart: {
      type: "bar",
      height: 350,
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "55%",
        borderRadius: 5,
        borderRadiusApplication: "end",
      },
    },
    colors: ["#1366D9", "#F36960", "#F9A63A"],
    dataLabels: {
      enabled: false,
    },
    stroke: {
      show: true,
      width: 0,
      colors: ["transparent"],
    },
    xaxis: {
      categories: [
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
      ],
    },
    yaxis: {
      title: {
        text: "$ (thousands)",
      },
    },
    fill: {
      opacity: 1,
    },
    tooltip: {
      y: {
        formatter: function (val) {
          return "$ " + val + " thousands";
        },
      },
    },
  });

  // eslint-disable-next-line no-unused-vars
  const [chartSeries, setChartSeries] = useState([
    {
      name: "Net Profit",
      data: [44, 55, 57, 56, 61, 58, 63, 60, 66],
    },
    {
      name: "Revenue",
      data: [76, 85, 101, 98, 87, 105, 91, 114, 94],
    },
    {
      name: "Free Cash Flow",
      data: [35, 41, 36, 26, 45, 48, 52, 53, 41],
    },
  ]);

  const [showCalendar, setShowCalendar] = useState(false);
  const calendarRef = useRef(null);

  useEffect(() => {
    function handleClickOutside(event) {
      if (calendarRef.current && !calendarRef.current.contains(event.target)) {
        setShowCalendar(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <>
      <Head>
        <title>Performance Analytics Dashboard | Mixdorm</title>
      </Head>
      <section className="w-full">
        <Loader open={loading} />
        <h1 className="page-title">Analytics & Reports</h1>
        {loading ? (
          <div className="main-content-wrap rounded-lg md:p-6 p-2 mt-7">
 
            <div className="relative mb-8">
              <div className="flex items-center gap-1.5 w-48 h-8 bg-gray-200 rounded animate-pulse"></div>
            </div>


            <div className="flex gap-2.5 overflow-x-auto mb-8 pb-4">
              {[...Array(15)].map((_, i) => (
                <div
                  key={i}
                  className="min-w-16 w-20 sm:h-24 h-20 bg-gray-200 rounded-lg animate-pulse"
                ></div>
              ))}
            </div>

            <div className="grid lg:grid-cols-4 xs:grid-cols-2 grid-cols-1 sm:gap-5 gap-4 mb-5">
              {[...Array(4)].map((_, i) => (
                <div
                  key={i}
                  className="h-28 bg-gray-200 rounded-lg animate-pulse"
                ></div>
              ))}
            </div>

            <div className="grid lg:grid-cols-2 gap-5">

              <div className="border rounded-lg border-[#D0D3D9] p-5">
                <div className="flex mb-4">
                  <div className="w-32 h-6 bg-gray-200 rounded animate-pulse"></div>
                  <div className="ml-auto w-24 h-6 bg-gray-200 rounded animate-pulse"></div>
                </div>
                <div className="h-[350px] bg-gray-200 rounded animate-pulse"></div>
              </div>

           
              <div className="border rounded-lg border-[#D0D3D9] p-5">
                <div className="w-32 h-6 bg-gray-200 rounded mb-4 animate-pulse"></div>
                <div className="grid sm:grid-cols-2 grid-cols-1 gap-4">
                  {[...Array(5)].map((_, i) => (
                    <div
                      key={i}
                      className={`h-28 bg-gray-200 rounded-lg animate-pulse ${
                        i === 4 ? "sm:col-span-2" : ""
                      }`}
                    ></div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="main-content-wrap rounded-lg md:p-6 p-2 mt-7">
            <div className="relative">
              <button
                className="page-title mb-4 cursor-pointer flex gap-1.5 items-center"
                onClick={() => setShowCalendar(!showCalendar)}
              >
                <span>
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/analytics-b.svg`}
                    width={22}
                    height={22}
                    alt="Calendar Icon"
                  />
                </span>
                {dayjs(selectedMonth).format("MMMM, YYYY")}
              </button>

              {showCalendar && (
                <div
                  ref={calendarRef}
                  className="absolute top-10 sm:right-auto right-1/3 z-10 mt-2"
                >
                  <DatePicker
                    className="shadow-md"
                    selected={selectedMonth.toDate()}
                    onChange={handleMonthChange}
                    dateFormat="MMMM, yyyy"
                    showMonthYearPicker
                    inline
                    maxDate={new Date()}
                  />
                </div>
              )}

              <div className="flex justify-between gap-2 items-center mb-4 pb-4">
                <div
                  ref={scrollContainerRef}
                  className="flex gap-2.5 whitespace-nowrap overflow-x-auto w-full no-scrollbar custom-scrollbar"
                  style={{
                    scrollbarWidth: "thin",
                    scrollbarColor: "#40E0D0 #f1f1f1",
                  }}
                >
                  {datesForCurrentPage.map((date) => {
                    const isToday = date.isSame(dayjs(), "day");

                    return (
                      <button
                        key={date.format("DD")}
                        ref={isToday ? currentDateRef : null}
                        type="button"
                        onClick={() => handleDateSelection(date)}
                        disabled={date.isAfter(dayjs(), "day")}
                        className={`flex-1 cursor-pointer whitespace-nowrap border border-slate-200 border-1 rounded-lg min-w-16 w-20 sm:h-24 h-20 text-center flex flex-col items-center transition-all justify-center group ${
                          date.isSame(selectedDate, "day") ? "bg-[#40E0D0]" : ""
                        } ${
                          date.isAfter(dayjs(), "day")
                            ? "opacity-50 cursor-not-allowed"
                            : ""
                        }`}
                      >
                        <h4 className="text-xs uppercase font-semibold mb-3">
                          {date.format("ddd")}
                        </h4>
                        <h5 className="text-xs font-normal text-[#00000080]">
                          {date.format("DD")}
                        </h5>
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>

            <div className="grid lg:grid-cols-4 xs:grid-cols-2 grid-cols-1 sm:gap-5 gap-4 mb-5">
              <Link href="/owner/dashboard/booking">
                <div className="bg-[#FF72AD] rounded-lg p-4 flex items-center">
                  <div>
                    <h4 className="text-white text-lg mb-4">Total Bookings</h4>
                    <h5 className="text-xl sm:text-2xl font-semibold text-white">
                      {analyticsData?.counts?.totalBookings?.length || 0}
                    </h5>
                  </div>
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/analytics1.svg`}
                    width={40}
                    height={40}
                    alt="BgImg"
                    className="ml-auto"
                    loading="lazy"
                  />
                </div>
              </Link>
              <Link href="/owner/dashboard/payment">
                <div className="bg-[#43C666] rounded-lg p-4 flex items-center">
                  <div>
                    <h4 className="text-white text-lg mb-4">Total Revenue</h4>
                    <h5 className="text-xl sm:text-2xl font-semibold text-white">
                      {analyticsData?.counts?.totalRevenue || 0}
                    </h5>
                  </div>
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/analytics2.svg`}
                    width={40}
                    height={40}
                    alt="BgImg"
                    className="ml-auto"
                    loading="lazy"
                  />
                </div>
              </Link>
              <Link href="/owner/dashboard/reviews">
                <div className="bg-[#D092F5] rounded-lg p-4 flex items-center">
                  <div>
                    <h4 className="text-white text-lg mb-4">Total Reviews</h4>
                    <h5 className="text-xl sm:text-2xl font-semibold text-white">
                      {analyticsData?.counts?.totalReviews?.length || 0}
                    </h5>
                  </div>
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/analytics3.svg`}
                    width={40}
                    height={40}
                    alt="BgImg"
                    className="ml-auto"
                    loading="lazy"
                  />
                </div>
              </Link>
              <Link href="/owner/dashboard/booking">
                <div className="bg-[#FF7F50] rounded-lg p-4 flex items-center">
                  <div>
                    <h4 className="text-white text-lg mb-4">New Bookings</h4>
                    <h5 className="text-xl sm:text-2xl font-semibold text-white">
                      {analyticsData?.counts?.newBookings?.length || 0}
                    </h5>
                  </div>
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/analytics4.svg`}
                    width={40}
                    height={40}
                    alt="BgImg"
                    className="ml-auto"
                    loading="lazy"
                  />
                </div>
              </Link>
            </div>

            <div className="grid lg:grid-cols-2 gap-5">
              <div className="border rounded-lg border-[#D0D3D9] p-5">
                <div className="flex mb-4">
                  <h4 className="text-base font-medium">Daily Average</h4>
                  <div className="ml-auto font-medium text-black text-sm">
                    <span className="text-[#FF955A]">+30m</span> this week
                  </div>
                </div>
                {typeof window !== "undefined" && (
                  <Chart
                    options={chartOptions}
                    series={chartSeries}
                    type="bar"
                    height={350}
                  />
                )}
                {/* <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/analytics_graph.jpg`}
              width={517}
              height={241}
              alt="BgImg"
              className="block sm:mt-14 mt-10 mx-auto "
              loading="lazy"
            /> */}
              </div>
              <div className="border rounded-lg border-[#D0D3D9] p-5">
                <h3 className="text-base font-medium mb-4">Room Details</h3>
                <div className="grid sm:grid-cols-2 grid-cols-1 gap-4">
                  <div className="border rounded-lg border-[#D0D3D9] p-4 flex items-center">
                    <div>
                      <h4 className="text-lg mb-3 font-medium">Check In</h4>
                      <h5 className="text-base text-[#858D9D] font-semibold">
                        <span className="text-[#40E0D0] text-2xl">
                          {" "}
                          {analyticsData?.counts?.roomDetails?.totalCheckIn ||
                            0}
                        </span>
                        / day
                      </h5>
                    </div>
                    <Image
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/checkin.svg`}
                      width={40}
                      height={40}
                      alt="BgImg"
                      className="ml-auto"
                      loading="lazy"
                    />
                  </div>
                  <div className="border rounded-lg border-[#D0D3D9] p-4 flex items-center">
                    <div>
                      <h4 className="text-lg mb-3 font-medium">Check Out</h4>
                      <h5 className="text-base text-[#858D9D] font-semibold">
                        <span className="text-[#40E0D0] text-2xl">
                          {analyticsData?.counts?.roomDetails?.totalCheckOut ||
                            0}
                        </span>
                        / day
                      </h5>
                    </div>
                    <Image
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/checkout.svg`}
                      width={40}
                      height={40}
                      alt="BgImg"
                      className="ml-auto"
                      loading="lazy"
                    />
                  </div>
                  <div className="border rounded-lg border-[#D0D3D9] p-4 flex items-center">
                    <div>
                      <h4 className="text-lg mb-3 font-medium">
                        Occupied Room
                      </h4>
                      <h5 className="text-base text-[#858D9D] font-semibold">
                        <span className="text-[#40E0D0] text-2xl">
                          {analyticsData?.counts?.roomDetails
                            ?.totalOccupiedRoom || 0}
                        </span>
                        / day
                      </h5>
                    </div>
                    <Image
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/occupied-bed.svg`}
                      width={40}
                      height={40}
                      alt="BgImg"
                      className="ml-auto"
                      loading="lazy"
                    />
                  </div>
                  <div className="border rounded-lg border-[#D0D3D9] p-4 flex items-center">
                    <div>
                      <h4 className="text-lg mb-3 font-medium">Vacant Room</h4>
                      <h5 className="text-base text-[#858D9D] font-semibold">
                        <span className="text-[#40E0D0] text-2xl">
                          {analyticsData?.counts?.roomDetails
                            ?.totalVacantRoom || 0}
                        </span>
                        / day
                      </h5>
                    </div>
                    <Image
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/vacantRoom.svg`}
                      width={52}
                      height={40}
                      alt="BgImg"
                      className="ml-auto"
                      loading="lazy"
                    />
                  </div>
                  <div className="border rounded-lg border-[#D0D3D9] p-4 flex items-center sm:col-span-2">
                    <div>
                      <h4 className="text-lg mb-3 font-medium">
                        In House Guests
                      </h4>
                      <h5 className="text-base text-[#858D9D] font-semibold">
                        <span className="text-[#40E0D0] text-2xl">
                          {analyticsData?.counts?.roomDetails
                            ?.totalInHouseGuests || 0}
                        </span>
                        / day
                      </h5>
                    </div>
                    <Image
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/guest.svg`}
                      width={40}
                      height={40}
                      alt="BgImg"
                      className="ml-auto"
                      loading="lazy"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </section>
    </>
  );
};

export default Page;
