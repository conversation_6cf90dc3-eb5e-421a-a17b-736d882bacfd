import React, {
  useEffect,
  useRef,
  useState,
  useCallback,
  Fragment,
} from "react";
import { format } from "date-fns";
// import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/react";
import { Trash, MoreVertical, Pencil, Eye, Trash2 } from "lucide-react";
import toast from "react-hot-toast";
import { BookingList<PERSON>pi, DeleteBooking<PERSON><PERSON> } from "@/services/ownerflowServices";
import { getItemLocalStorage } from "@/utils/browserSetting";
import dynamic from "next/dynamic";
import Image from "next/image";
import { isSameDay, isToday } from "date-fns";
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
  Transition,
} from "@headlessui/react";
import countries from "world-countries";
import "react-datepicker/dist/react-datepicker.css";
import Pagination from "@/components/common/commonPagination";
import Head from "next/head";

const AddBooking = dynamic(
  () => import("@/components/ownerFlow/dashboard/addBooking"),
  {
    ssr: false,
  }
);

const EditBooking = dynamic(
  () => import("@/components/ownerFlow/dashboard/editBooking"),
  {
    ssr: false,
  }
);

const ViewBooking = dynamic(
  () => import("@/components/ownerFlow/dashboard/viewBooking"),
  {
    ssr: false,
  }
);

const Filter = dynamic(
  () => import("../../../components/model/bookingFilter"),
  {
    ssr: false,
  }
);

const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

const Booking = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const closefilterModal = () => setIsModalOpen(false);
  const [activeTab, setActiveTab] = useState(0);
  const [openAddBooking, setOpenAddBooking] = useState(false);
  const closebookingModal = () => setOpenAddBooking(false);
  const [openEditBooking, setopenEditBooking] = useState(false);
  const closebookingeditModal = () => setopenEditBooking(false);
  const [openViewBooking, setopenViewBooking] = useState(false);
  const closebookingviewModal = () => setopenViewBooking(false);
  const isFirstRender = useRef(null);

  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showCalendar, setShowCalendar] = useState(false);

  // Filter Modal
  // eslint-disable-next-line no-unused-vars
  const [openFilter, setOpenFilter] = useState(false);

  const tabs = [
    {
      id: 0,
      label: "All Reservations",
      category: "reservations",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/analytics1.svg`,
      icon1: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/analytics-b.svg`,
    },
    {
      id: 1,
      label: "Arrivals",
      category: "arrivals",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/arrivals.svg`,
      icon1: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/arrivals-b.svg`,
    },
    {
      id: 2,
      label: "Departures",
      category: "departures",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/departure.svg`,
      icon1: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/departure-b.svg`,
    },
    {
      id: 3,
      label: "Inhouse Guests",
      category: "inhouse",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/inhouse.svg`,
      icon1: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/inhouse-b.svg`,
    },
  ];

  const headers = [
    "Reservation ID",
    "Name",
    "Room Number",
    "Check in",
    "Check Out",
    "Room Type",
    "Total amount",
    "Amount paid",
    "Balance",
    "Status",
    "",
  ];

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [properties, setProperties] = useState([]);
  const id = getItemLocalStorage("hopid");
  const [loading, setLoading] = useState(false);
  const [editId, setEditId] = useState(null);
  const [currencyData, setCurrencyData] = useState({});
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalData, setTotalData] = useState();

  const [currentMonth, setCurrentMonth] = useState(new Date());
  const calendarRef = useRef(null);
  const inputRef = useRef(null);

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [bookingToDelete, setBookingToDelete] = useState(null);
  // eslint-disable-next-line no-unused-vars
  const [deleting, setDeleting] = useState(false);

  const handlePrevMonth = () => {
    setCurrentMonth(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() - 1, 1)
    );
  };

  const handleNextMonth = () => {
    setCurrentMonth(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() + 1, 1)
    );
  };
  // Generate days for current month
  function generateDays(month) {
    const daysInMonth = new Date(
      month.getFullYear(),
      month.getMonth() + 1,
      0
    ).getDate();
    const firstDayIndex = new Date(
      month.getFullYear(),
      month.getMonth(),
      1
    ).getDay();

    const days = [];

    // Add blank spaces for days of previous month
    for (let i = 0; i < firstDayIndex; i++) {
      days.push(null);
    }

    // Add days of the current month
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(new Date(month.getFullYear(), month.getMonth(), i));
    }

    return days;
  }
  const handleDateClick = (day) => {
    const formattedDate = format(day, "MM-dd-yyyy"); // format the selected date
    setSelectedDate(formattedDate);
    setShowCalendar(false); // close calendar after selecting
  };
  // const handleDateClick = (day) => {
  //   const formattedDate = format(day, "MM-dd-yyyy");
  //   setSelectedDate(formattedDate); // Store formatted string

  //   handleChange({
  //     target: {
  //       name: "date",
  //       value: formattedDate,
  //     },
  //   });

  //   setShowCalendar(false);
  // };

  // Close calendar if clicked outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target) &&
        !inputRef.current.contains(event.target)
      ) {
        setShowCalendar(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const fetchList = useCallback(async () => {
    if (!id) return;

    setLoading(true);
    try {
      const response = await BookingListApi(
        id,
        tabs[activeTab].category,
        currentPage,
        itemsPerPage
      );
      console.log("response booking", response);
      if (response.status === 200) {
        setProperties(response?.data?.data?.bookings || []);
        setTotalPages(response?.data?.data?.pagination?.totalPages || 1);
        setTotalData(response?.data?.data?.pagination?.totalBookings);
      } else {
        toast.error(response?.data?.message);
      }
    } catch (error) {
      console.error("Error fetching booking data:", error);
    } finally {
      setLoading(false);
    }
  }, [id, activeTab, currentPage, itemsPerPage]);

  useEffect(() => {
    if (!isFirstRender.current) {
      fetchList();
    } else {
      isFirstRender.current = false;
    }
  }, [fetchList, itemsPerPage]);

  // const handlePageChange = (newPage) => {
  //   if (newPage >= 1 && newPage <= totalPages) {
  //     setCurrentPage(newPage);
  //   }
  // };

  const handleDelete = async (id) => {
    setBookingToDelete(id);
    setIsDeleteModalOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!bookingToDelete) return;

    setLoading(true);
    try {
      const response = await DeleteBookingApi(bookingToDelete);
      if (response?.data?.status) {
        toast.success(response?.data?.data || response?.data?.message);
        await fetchList();
        setIsDeleteModalOpen(false);
        setBookingToDelete(null);
      }
    } catch (error) {
      console.log(error);
      toast.error("Failed to delete booking");
    } finally {
      setLoading(false);
    }
  };

  const currentData = properties;

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);
      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);

  const getCurrencySymbol = (currencyCode) => {
    return currencyData[currencyCode] || currencyCode;
  };

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const TabContent = ({ data }) => {
    const [dropdownPos, setDropdownPos] = useState({ top: 0, left: 0 });
    const [openIndex, setOpenIndex] = useState(null);
    const buttonRefs = useRef({});
    const dropdownRef = useRef(null);

    const openMenu = (index) => {
      const button = buttonRefs.current[index];
      if (button) {
        const rect = button.getBoundingClientRect();
        setDropdownPos({
          top: rect.top + window.scrollY + button.offsetHeight - 12,
          left: rect.left + window.scrollX + 54 + button.offsetWidth,
        });
        setOpenIndex(index);
      }
    };

    const closeMenu = () => setOpenIndex(null);

    useEffect(() => {
      const handleClickOutside = (event) => {
        if (
          dropdownRef.current &&
          !dropdownRef.current.contains(event.target) &&
          !Object.values(buttonRefs.current).some((btn) =>
            btn?.contains(event.target)
          )
        ) {
          closeMenu();
        }
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    return (
      <tbody>
        {data.map((item, index) => (
          <tr className="border-b" key={index}>
            <td className="p-3 text-xs text-[#000]">{item?.referenceNumber}</td>
            <td className="p-3 text-xs text-[#000] whitespace-nowrap">
              {item?.userDetails?.name?.first} {item?.userDetails?.name?.last}{" "}
              {item?.guestDetails?.name}
            </td>
            <td className="p-3 text-xs text-[#000]">{item?._id}</td>
            <td className="p-3 text-xs text-[#000]">
              {new Date(item.checkInDate).toLocaleDateString("en-GB", {
                day: "2-digit",
                month: "2-digit",
                year: "numeric",
              })}
            </td>
            <td className="p-3 text-xs text-[#000]">
              {new Date(item.checkOutDate).toLocaleDateString("en-GB", {
                day: "2-digit",
                month: "2-digit",
                year: "numeric",
              })}
            </td>
            <td className="p-3 text-xs text-[#000]">
              {item.roomDetails[0]?.type || " "}
            </td>
            <td className="p-3 text-nowrap text-xs text-[#000]">
              {getCurrencySymbol("USD")} {item.totalAmount.toFixed(2)}
            </td>
            <td className="p-3 text-nowrap text-xs text-[#000]">
              {getCurrencySymbol("USD")}{" "}
              {item?.totalAmount.toFixed(2) || "Dummy"}
            </td>
            <td className="p-3 text-nowrap text-xs text-[#000]">
              {getCurrencySymbol("USD")}{" "}
              {item?.totalAmount.toFixed(2) || "Dummy"}
            </td>
            <td className="p-1 text-xs text-[#000]">
              <span
                className={`capitalize inline-block px-2 py-1 text-xs whitespace-nowrap ${
                  index === 0
                    ? "bg-green-100 text-green-700"
                    : index === 1
                    ? "bg-green-100 text-green-700"
                    : "bg-green-100 text-green-700"
                } rounded-full`}
              >
                {item.status}
              </span>
            </td>
            <td className="relative p-4">
              <button
                ref={(el) => (buttonRefs.current[index] = el)}
                onClick={() => openMenu(index)}
                className="p-2"
              >
                <MoreVertical size={16} />
              </button>

              {openIndex === index && (
                <div
                  ref={dropdownRef}
                  style={{
                    position: "fixed",
                    top: `${dropdownPos.top}px`,
                    left: `${dropdownPos.left - 150}px`, // Adjust X offset if needed
                    zIndex: 1000,
                  }}
                  className="w-24 bg-white shadow-lg rounded-md border"
                >
                  <button
                    onClick={() => {
                      setopenViewBooking(true);
                      setEditId(item._id);
                      closeMenu();
                    }}
                    className="w-full px-2 py-1.5 text-left text-sm hover:bg-gray-100"
                  >
                    <Eye size={14} className="inline mr-2" />
                    View
                  </button>
                  <button
                    onClick={() => {
                      setopenEditBooking(true);
                      setEditId(item._id);
                      closeMenu();
                    }}
                    className="w-full px-2 py-1.5 text-left text-sm hover:bg-gray-100"
                  >
                    <Pencil size={14} className="inline mr-2" />
                    Edit
                  </button>
                  <button
                    onClick={() => {
                      handleDelete(item._id);
                      closeMenu();
                    }}
                    className="w-full px-2 py-1.5 text-left text-sm text-red-600 hover:bg-gray-100"
                  >
                    <Trash size={14} className="inline mr-2" />
                    Delete
                  </button>
                </div>
              )}
            </td>
          </tr>
        ))}
      </tbody>
    );
  };

  const updateRoomList = () => {
    fetchList();
  };

  return (
    <>
      <Head>
        <title>Manage Bookings | Mixdorm</title>
      </Head>
      <Loader open={loading} />

      {/* {openAddBooking ? (
        <AddBooking
          onClose={() => setOpenAddBooking(false)}
          updateRoomList={updateRoomList}
        />
      ) : editOpen ? (
        <EditBooking
          onClose={() => setEditOpen(false)}
          updateRoomList={updateRoomList}
          editId={editId}
        />      ) : (
        
      )} */}

      {loading ? (
        <section className="w-full">
          <div className="flex justify-between items-center">
            {/* Heading */}
            <div className="h-6 w-24 bg-gray-200 rounded animate-pulse"></div>

            {/* Button group */}
            <div className="flex gap-2">
              {/* Calendar button */}
              <div className="h-10 w-24 bg-gray-200 rounded animate-pulse"></div>

              {/* Filter button */}
              <div className="h-10 w-24 bg-gray-200 rounded animate-pulse"></div>

              {/* Add Booking button */}
              <div className="h-10 w-24 bg-gray-200 rounded animate-pulse"></div>
            </div>
          </div>

          <div className="w-full mt-5 bg-white shadow-4xl rounded-2xl animate-pulse">
            {/* Tab Skeleton */}
            <div className="mb-4">
              <ul className="grid md:grid-cols-[repeat(3,_1fr)_1fr] grid-cols-2 font-normal text-xs sm:gap-4 gap-2">
                {[...Array(4)].map((_, index) => (
                  <li
                    key={index}
                    className="flex items-center sm:gap-10 lg:gap-4 gap-2 p-2 relative border rounded-lg border-[#D0D3D9] bg-gray-200 h-20"
                  ></li>
                ))}
              </ul>
            </div>

            {/* Table Header Skeleton */}
            <div className="overflow-x-auto mt-4 rounded-md pb-8">
              <table className="min-w-full">
                <thead>
                  <tr className="bg-[#EEEEEE] border-b">
                    {[...Array(7)].map((_, index) => (
                      <th
                        key={index}
                        className="p-4 text-xs font-bold text-[#000000] text-left"
                      >
                        <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {/* Table Rows Skeleton */}
                  {[...Array(4)].map((_, rowIndex) => (
                    <tr className="border-b" key={rowIndex}>
                      {[...Array(7)].map((_, cellIndex) => (
                        <td key={cellIndex} className="p-3">
                          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </section>
      ) : (
        <section className="w-full">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-medium text-gray-800">Booking</h2>
            <div className="flex sm:gap-2 gap-1 relative">
              <button
                onClick={() => setShowCalendar(!showCalendar)}
                className="sm:flex block items-center text-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-primary-blue font-medium"
                ref={inputRef}
              >
                <Image
                  className="sm:mr-2 mx-auto"
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/calendar.svg`}
                  width={20}
                  height={20}
                />
                {/* 18, Sept */}

                {format(selectedDate, "dd, MMM")}
              </button>
              {/* {showCalendar && (
              <div className='absolute top-10 sm:right-auto right-1/3 z-10 mt-2 bg-white border rounded-lg shadow-md'>
                <DatePicker
                  selected={selectedDate}
                  onChange={(date) => {
                    setSelectedDate(date);
                    setShowCalendar(false); // Close calendar after selection
                  }}
                  inline
                />
              </div>
            )} */}
              {showCalendar && (
                <div
                  ref={calendarRef}
                  className="absolute top-full -left-20 bg-white border border-black/50 rounded-lg shadow-lg px-4 py-2 z-40 w-[18rem] mt-0.5"
                >
                  {/* Month navigation */}
                  <div className="flex items-center justify-between mb-4">
                    <button
                      onClick={handlePrevMonth}
                      className="text-lg font-bold px-2"
                    >
                      &#8592;
                    </button>

                    <span className="text-base font-semibold">
                      {format(currentMonth, "MMMM yyyy")}
                    </span>

                    <button
                      onClick={handleNextMonth}
                      className="text-lg font-bold px-2"
                    >
                      &#8594;
                    </button>
                  </div>

                  {/* Weekdays */}
                  <div className="grid grid-cols-7 gap-2 text-center text-sm font-semibold text-gray-600">
                    <div>Su</div>
                    <div>Mo</div>
                    <div>Tu</div>
                    <div>We</div>
                    <div>Th</div>
                    <div>Fr</div>
                    <div>Sa</div>
                  </div>

                  {/* Days */}
                  <div className="grid grid-cols-7 gap-1 mt-2 text-center text-sm">
                    {generateDays(currentMonth).map((day, index) =>
                      day ? (
                        // <button
                        //   key={index}
                        //   className="hover:bg-primary-blue hover:text-white rounded-full p-2"
                        //   onClick={() => handleDateClick(day)}
                        // >
                        //   {day.getDate()}
                        // </button>
                        <button
                          key={index}
                          className={`rounded-full p-2 text-sm flex items-center justify-center ${
                            isToday(day) ? "border-2 border-primary-blue" : ""
                          } ${
                            selectedDate && isSameDay(selectedDate, day)
                              ? "bg-primary-blue text-white"
                              : "hover:bg-primary-blue hover:text-white"
                          }`}
                          onClick={() => handleDateClick(day)}
                        >
                          {day.getDate()}
                        </button>
                      ) : (
                        <div key={index} />
                      )
                    )}
                  </div>
                </div>
              )}
              <button
                className="sm:flex block items-center text-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-gray-300 cursor-not-allowed font-medium"
                // onClick={() => setIsModalOpen(true)}
              >
                <Image
                  className="sm:mr-2 mx-auto"
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/filter.svg`}
                  width={20}
                  height={20}
                />
                Filter
              </button>
              <button
                className="flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-gray-300 cursor-not-allowed font-medium"
                // onClick={() => setOpenAddBooking(true)}
              >
                Add Booking
              </button>
              {/* <button
                className='flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-primary-blue font-medium'
                onClick={() => setopenEditBooking(true)}
              >
                Edit Booking
            </button> */}
            </div>
          </div>

          <div className="w-full mt-5 bg-white shadow-4xl rounded-2xl">
            <div className="mb-4">
              <ul className="grid md:grid-cols-[repeat(3,_1fr)_1fr] grid-cols-2 font-normal text-xs sm:gap-4 gap-2">
                {tabs.map((item) => (
                  <li
                    key={item.id}
                    className={`flex items-center sm:gap-10 lg:gap-4 gap-2 p-2 relative border rounded-lg border-[#D0D3D9] ${
                      activeTab === item.id ? "bg-black" : ""
                    } cursor-pointer`}
                    onClick={() => setActiveTab(item.id)}
                  >
                    <div>
                      <a
                        className={`font-medium ${
                          activeTab === item.id
                            ? "text-[#ffffff]"
                            : "text-[#000000]"
                        } cursor-pointer text-left xl:text-lg text-sm`}
                        href="#"
                      >
                        {item.label}
                      </a>
                      <h5
                        className={`text-base text-[#00000080] font-semibold mt-3 ${
                          activeTab === item.id
                            ? "text-[#FFFFFF80]"
                            : "text-[#00000080]"
                        } cursor-pointer`}
                      >
                        <span
                          className={`lg:text-2xl text-lg ${
                            activeTab === item.id
                              ? "text-[#40E0D0]"
                              : "text-[#000000]"
                          } cursor-pointer`}
                        >
                          {" "}
                          0
                        </span>
                        / day
                      </h5>
                    </div>
                    {activeTab === item.id ? (
                      <Image
                        src={item.icon}
                        width={40}
                        height={40}
                        alt="BgImg"
                        className="ml-auto xl:w-[40px] xl:h-[40px] lg:w-[30px] lg:h-[30px] w-[25px] h-[25px]"
                        loading="lazy"
                      />
                    ) : (
                      <Image
                        src={item.icon1}
                        width={40}
                        height={40}
                        alt="BgImg"
                        className="ml-auto xl:w-[40px] xl:h-[40px] lg:w-[30px] lg:h-[30px] w-[25px] h-[25px]"
                        loading="lazy"
                      />
                    )}
                  </li>
                ))}
              </ul>

              {/* <div className='ml-auto flex gap-4 items-center'>
              <div className='relative'>
                <button
                  type='button'
                  className='absolute text-black top-1/2 left-2.5 transform -translate-y-1/2'
                >
                  <Search size={16} />
                </button>
                <input
                  type='text'
                  className='px-8 py-3 rounded-md w-80 outline-none border text-sm font-light'
                  placeholder='Search Arrivals'
                />
                <button
                  className='absolute text-black top-1/2 right-2.5 transform -translate-y-1/2'
                  type='button'
                  onClick={handleOpenFilter}
                >
                  <SlidersHorizontal size={16} />
                </button>
              </div>
              
            </div> */}
            </div>

            <div className="overflow-x-auto mt-4 rounded-md pb-8">
              <table className="min-w-full ">
                <thead>
                  <tr className="bg-[#EEEEEE] border-b">
                    {headers.map((header, index) => (
                      <th
                        key={index}
                        className="p-4 text-xs font-bold text-[#000000] text-left"
                      >
                        {header}
                      </th>
                    ))}
                  </tr>
                </thead>
                <TabContent data={currentData} />
              </table>
            </div>

            {/* {totalPages > 1 && (
            <div className='flex justify-center mt-4'>
              <CustomPagination
                currentPage={currentPage}
                total={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          )} */}

            {properties?.length > 0 && (
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalData || 0}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleItemsPerPageChange}
              />
            )}
          </div>
        </section>
      )}

      {isModalOpen && (
        <Dialog
          open={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          className="relative z-50"
        >
          <DialogBackdrop
            transition
            className="fixed inset-0 bg-[#000000B2] transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
          />

          <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
            <div className="flex min-h-full justify-center p-4 text-center items-center sm:p-0">
              <DialogPanel
                transition
                className="relative transform overflow-hidden rounded-2xl bg-white text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-full sm:max-w-[490px] max-w-full w-full data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95"
              >
                <div className="bg-white sm:px-7 sm:pb-7 pt-3 p-3 pb-3">
                  <DialogTitle>
                    <h3 className="text-center text-black font-bold sm:text-lg text-sm">
                      Filter
                    </h3>
                  </DialogTitle>
                  <Filter closefilterModal={closefilterModal} />
                </div>
              </DialogPanel>
            </div>
          </div>
        </Dialog>
      )}

      <Transition show={openAddBooking} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closebookingModal}>
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <h2 className="page-title">Add Booking</h2>
                    <button
                      onClick={closebookingModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <AddBooking
                      closebookingModal={closebookingModal}
                      updateRoomList={updateRoomList}
                    ></AddBooking>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      <Transition show={openEditBooking} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-50"
          onClose={closebookingeditModal}
        >
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm">
                    <h2 className="page-title">Edit Booking</h2>
                    <button
                      onClick={closebookingeditModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <EditBooking
                      closebookingeditModal={closebookingeditModal}
                      updateRoomList={updateRoomList}
                      editId={editId}
                    ></EditBooking>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      <Transition show={openViewBooking} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-50"
          onClose={closebookingviewModal}
        >
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm">
                    <h2 className="page-title">View Booking</h2>
                    <button
                      onClick={closebookingviewModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <ViewBooking
                      closebookingviewModal={closebookingviewModal}
                      updateRoomList={updateRoomList}
                      editId={editId}
                    ></ViewBooking>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      {isDeleteModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="text-center">
              <Trash2 className="mx-auto h-12 w-12 text-red-500" />
              <h3 className="mt-4 text-lg font-medium text-gray-900">
                Delete Booking
              </h3>
              <p className="mt-2 text-sm text-gray-500">
                Are you sure you want to delete this booking? This action cannot
                be undone.
              </p>
            </div>
            <div className="mt-6 flex justify-center space-x-4">
              <button
                onClick={() => {
                  setIsDeleteModalOpen(false);
                  setBookingToDelete(null);
                }}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none"
              >
                No, Cancel
              </button>
              <button
                onClick={handleConfirmDelete}
                disabled={deleting}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none disabled:opacity-50"
              >
                {deleting ? "Deleting..." : "Yes, Delete"}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Booking;
