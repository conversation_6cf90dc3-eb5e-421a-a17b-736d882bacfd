import express from 'express';
import { bookEvent<PERSON><PERSON>roller, listUserBookingsController, cancelBookingController } from '../controller/eventBooking.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();

// Route to book an event
router.post('/book', checkAuth('book_event'), bookEventController);

// Route to list user's bookings
router.get('/my-events', checkAuth('view_bookings'), listUserBookingsController);

// Route to cancel a booking
router.put('/cancel/:id', checkAuth('cancel_booking'), cancelBookingController);

export default router;
/**
 * @swagger
 * tags:
 *   name: EventBooking
 *   description: Event Booking Management
 */

/**
 * @swagger
 * /eventBookings/book:
 *   post:
 *     tags: [EventBooking]
 *     summary: Book an event
 *     description: Books an event for a user
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               event:
 *                 type: string
 *                 format: ObjectId
 *                 description: ID of the event to be booked
 *               numberOfAttendees:
 *                 type: integer
 *                 description: Number of attendees
 *               price:
 *                 type: number
 *                 description: Total price for the booking
 *     responses:
 *       201:
 *         description: Event booked successfully
 *       500:
 *         description: Internal Server Error
 */

/**
 * @swagger
 * /eventBookings/my-events:
 *   get:
 *     tags: [EventBooking]
 *     summary: List user's bookings
 *     description: Returns a list of bookings made by the user
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Bookings retrieved successfully
 *       500:
 *         description: Internal Server Error
 */

/**
 * @swagger
 * /eventBookings/cancel/{id}:
 *   put:
 *     tags: [EventBooking]
 *     summary: Cancel a booking
 *     description: Cancels an existing booking
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Booking cancelled successfully
 *       500:
 *         description: Internal Server Error
 */
