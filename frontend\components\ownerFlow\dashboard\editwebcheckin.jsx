/* eslint-disable react/jsx-key */
import React, { useCallback, useEffect, useRef, useState } from "react";
import { CloudUpload } from "lucide-react";
import PhoneInput, { isValidPhoneNumber } from "react-phone-number-input";
import "react-phone-number-input/style.css";
import { Autocomplete } from "@react-google-maps/api";
import Loader from "@/components/loader/loader";
import { editcheckInApi, getcheckInApi } from "@/services/ownerflowServices";
import { BASE_URL } from "@/utils/api";
import toast from "react-hot-toast";
import { getItemLocalStorage } from "@/utils/browserSetting";
import Image from "next/image";
import CustomSelect from "@/components/common/CustomDropdown2";
import FormControlLabel from "@mui/material/FormControlLabel";
import Checkbox from "@mui/material/Checkbox";
import { Typography } from "@mui/material";
import Link from "next/link";
import { format, isToday } from "date-fns";

const Editwebcheckin = ({
  closecheckineditModal,
  editId,
  updateCheckinList,
}) => {
  // eslint-disable-next-line no-unused-vars
  const [calendarOpen, setCalendarOpen] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState("");
  const [phoneNumberError, setPhoneNumberError] = useState("");
  const [autocomplete, setAutocomplete] = useState(null);
  const [autocompleteD, setAutocompleteD] = useState(null);
  const [isApiLoaded, setIsApiLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const [showCalendar, setShowCalendar] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const calendarRef = useRef(null);
  const inputRef = useRef(null);
  const [showFromCalendar, setShowFromCalendar] = useState(false);
  const [currentMonthFrom, setCurrentMonthFrom] = useState(new Date());
  const calendarRefFrom = useRef(null);

  const [formData, setFormData] = useState({
    bookingId: "",
    name: "",
    checkIn: "",
    checkOut: "",
    document: "",
    attachment: null,
    comingFrom: {
      address: "",
      location: {
        type: "Point",
        coordinates: [0, 0],
      },
    },
    goingTo: {
      address: "",
      location: {
        type: "Point",
        coordinates: [0, 0],
      },
    },
    signature: null,
  });
  const [errors, setErrors] = useState({});

  const handlePrevMonth = () => {
    setCurrentMonth(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() - 1, 1)
    );
  };

  const handleNextMonth = () => {
    setCurrentMonth(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() + 1, 1)
    );
  };

  // Functions to move months
  const handlePrevMonthFrom = () => {
    setCurrentMonthFrom(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() - 1, 1)
    );
  };

  const handleNextMonthFrom = () => {
    setCurrentMonthFrom(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() + 1, 1)
    );
  };
  // Generate days for current month
  function generateDays(month) {
    const daysInMonth = new Date(
      month.getFullYear(),
      month.getMonth() + 1,
      0
    ).getDate();
    const firstDayIndex = new Date(
      month.getFullYear(),
      month.getMonth(),
      1
    ).getDay();

    const days = [];

    // Add blank spaces for days of previous month
    for (let i = 0; i < firstDayIndex; i++) {
      days.push(null);
    }

    // Add days of the current month
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(new Date(month.getFullYear(), month.getMonth(), i));
    }

    return days;
  }

  // Close calendar if clicked outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target) &&
        !inputRef.current.contains(event.target)
      ) {
        setShowCalendar(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleChange = (e) => {
    const { name, value, files } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: files ? files[0] : value,
    }));
    if (name === "phoneNumber") {
      setPhoneNumberError("");
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Clear previous errors
    const newErrors = {};
    setErrors({});
    setPhoneNumberError("");

    // Validate required fields
    Object.keys(formData).forEach((key) => {
      if (!formData[key] && key !== "phone") {
        newErrors[key] = "This field is required.";
      }
    });

    // Validate comingFrom and goingTo specifically
    if (!formData?.comingFrom?.address) {
      newErrors.comingFrom = "Please specify where you are coming from.";
    }

    if (!formData?.goingTo?.address) {
      newErrors.goingTo = "Please specify where you are going to.";
    }

    // Validate phone number
    if (!isValidPhoneNumber(phoneNumber)) {
      setPhoneNumberError("Please enter a valid phone number.");
    } else {
      setPhoneNumberError(""); // Clear error if valid
    }

    // Check if there are any errors
    if (Object.keys(newErrors).length > 0 || phoneNumberError) {
      setErrors(newErrors);
      return;
    }

    // Construct the payload
    // const payload = {
    //   checkIn: formData?.checkIn,
    //   checkOut: formData?.checkOut,
    //   guestDetails: {
    //     name: formData?.name,
    //     phone: phoneNumber || "",
    //   },
    //   uploadedDocuments: [
    //     {
    //       url: formData?.document,
    //     },
    //   ],
    //   fromLocation: formData?.comingFrom?.address,
    //   toLocation: formData?.goingTo?.address,
    //   property: "66bd2f7592a0a6fb036f346f", // Replace with dynamic property if needed
    // };

    const payload = {
      user: getItemLocalStorage("uid"), // Replace with the actual user ID
      booking: formData?.bookingId, // Replace with the actual booking ID
      property: getItemLocalStorage("hopid"),
      checkIn: formData?.checkIn,
      checkOut: formData?.checkOut,
      guestDetails: {
        name: formData?.name,
        phone: {
          code: "",
          number: phoneNumber || "",
        },
      },
      uploadedDocuments: formData?.attachment,
      fromLocation: formData?.comingFrom?.address,
      toLocation: formData?.goingTo?.address,
      phone: phoneNumber || "",
    };

    setIsLoading(true);
    try {
      const response = await editcheckInApi(editId, payload);
      if (response?.data?.status) {
        toast.success(response?.data?.message);
        closecheckineditModal();
        updateCheckinList();
      } else {
        toast.error(
          response?.data?.message ||
            "Failed to edit check-in. Please try again."
        );
      }
    } catch (error) {
      console.error("Error adding check-in:", error);
      toast.error("Failed to edit check-in. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const googleMapsApiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

  const loadGoogleMapsApi = () => {
    const script = document.createElement("script");
    script.src = `https://maps.googleapis.com/maps/api/js?key=${googleMapsApiKey}&libraries=places`;
    script.async = true;
    script.onload = () => setIsApiLoaded(true);
    script.onerror = () => setIsApiLoaded(false);
    document.head.appendChild(script);
  };

  useEffect(() => {
    if (window.google && window.google.maps) {
      setIsApiLoaded(true);
    } else {
      loadGoogleMapsApi();
    }
  }, []);

  useEffect(() => {
    fetchData(editId);
  }, [editId]);

  const fetchData = async (editId) => {
    try {
      const res = await getcheckInApi(editId);
      if (res?.status == 200) {
        const data = res?.data?.data;
        setFormData({
          bookingId: data?.booking,
          name: data?.guestDetails?.name,
          checkIn: data?.checkIn.split("T")[0],
          checkOut: data?.checkOut.split("T")[0],
          document: "",
          attachment: data?.uploadedDocuments,
          comingFrom: {
            address: data?.fromLocation,
            location: {
              type: "Point",
              coordinates: [0, 0],
            },
          },
          goingTo: {
            address: data?.toLocation,
            location: {
              type: "Point",
              coordinates: [0, 0],
            },
          },
          signature: null,
        });
        setPhoneNumber(`+${data?.guestDetails?.phone?.number.toString()}`);
      }
    } catch (error) {
      console.log("error", error);
    }
  };

  const handleFAddressChange = useCallback((place) => {
    if (place && place.geometry) {
      setFormData((prevData) => ({
        ...prevData,
        comingFrom: {
          address: place.formatted_address || "",
          location: {
            type: "Point",
            coordinates: [
              place.geometry.location.lng(),
              place.geometry.location.lat(),
            ],
          },
        },
      }));
    } else {
      console.error("Invalid place selected:", place);
    }
  }, []);

  const handleDAddressChange = useCallback((place) => {
    if (place && place.geometry) {
      setFormData((prevData) => ({
        ...prevData,
        goingTo: {
          address: place.formatted_address || "",
          location: {
            type: "Point",
            coordinates: [
              place.geometry.location.lng(),
              place.geometry.location.lat(),
            ],
          },
        },
      }));
    } else {
      console.error("Invalid place selected:", place);
    }
  }, []);

  const handleFromAddressChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      comingFrom: {
        ...prevData.comingFrom,
        [name]: value,
      },
    }));
  };

  const handleDestinationAddressChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      goingTo: {
        ...prevData.goingTo,
        [name]: value,
      },
    }));
  };

  const handleAttachmentChange = async (e) => {
    const { files } = e.target;

    if (files.length === 0) {
      toast.error("At least one file is required");
      return;
    }
    const allowedTypes = ["image/jpeg", "image/png", "image/jpg"];

    // Check if all selected files are of allowed types
    const invalidFiles = Array.from(files).filter(
      (file) => !allowedTypes.includes(file.type)
    );

    if (invalidFiles.length > 0) {
      toast.error("Only JPG, JPEG, and PNG files are allowed.");
      return;
    }

    setIsLoading(true);

    try {
      // Loop through all selected files
      const uploadedImages = await Promise.all(
        Array.from(files).map(async (file) => {
          const formData = new FormData();
          formData.append("files", file);

          // Get presigned URL for each file
          const presignedUrlResponse = await fetch(
            `${BASE_URL}/fileUpload/generate-presigned-url`,
            {
              method: "POST",
              body: formData,
            }
          );

          if (!presignedUrlResponse.ok) {
            throw new Error("Failed to get presigned URL");
          }

          const presignedUrlData = await presignedUrlResponse.json();
          // const { objectURL } = presignedUrlData.data;
          const objectURL = Array.isArray(presignedUrlData.data) && presignedUrlData.data[0]?.path;

          return { title: "Attachment", url: objectURL };
        })
      );

      // Update state with new uploaded image URLs
      setFormData((prevState) => ({
        ...prevState,
        attachment: [...(prevState?.attachment || []), ...uploadedImages],
      }));

      toast.success("Files uploaded successfully.");
    } catch (error) {
      console.error("Error uploading files:", error);
      toast.error("Error uploading files.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignDocChange = async (e) => {
    const { files } = e.target;

    if (files.length === 0) {
      toast.error("At least one file is required");
      return;
    }
    const allowedTypes = ["image/jpeg", "image/png", "image/jpg"];

    // Check if all selected files are of allowed types
    const invalidFiles = Array.from(files).filter(
      (file) => !allowedTypes.includes(file.type)
    );

    if (invalidFiles.length > 0) {
      toast.error("Only JPG, JPEG, and PNG files are allowed.");
      return;
    }

    setIsLoading(true);

    try {
      // Loop through all selected files
      const uploadedImages = await Promise.all(
        Array.from(files).map(async (file) => {
          const formData = new FormData();
          formData.append("files", file);

          // Get presigned URL for each file
          const presignedUrlResponse = await fetch(
            `${BASE_URL}/fileUpload/generate-presigned-url`,
            {
              method: "POST",
              body: formData,
            }
          );

          if (!presignedUrlResponse.ok) {
            throw new Error("Failed to get presigned URL");
          }

          const presignedUrlData = await presignedUrlResponse.json();
          // const { objectURL } = presignedUrlData.data;
          const objectURL = Array.isArray(presignedUrlData.data) && presignedUrlData.data[0]?.path;

          return { title: "Sign Document", url: objectURL };
        })
      );

      // Update state with new uploaded image URLs
      setFormData((prevState) => ({
        ...prevState,
        signature: [...(prevState?.signature || []), ...uploadedImages],
      }));

      toast.success("Files uploaded successfully.");
    } catch (error) {
      console.error("Error uploading files:", error);
      toast.error("Error uploading files.");
    } finally {
      setIsLoading(false);
    }
  };

  const removeAttachmentImage = (indexToRemove) => {
    setFormData((prevState) => ({
      ...prevState,
      attachment: prevState.attachment.filter(
        (_, index) => index !== indexToRemove
      ),
    }));
  };

  const removeSignDocImage = (indexToRemove) => {
    setFormData((prevState) => ({
      ...prevState,
      signature: prevState.signature.filter(
        (_, index) => index !== indexToRemove
      ),
    }));
  };

  if (!isApiLoaded) {
    return <Loader open={true} />;
  }

  return (
    <>
      <Loader open={isLoading} />

      <section className="w-full">
        <div className="max-w-[780px] mx-auto">
          <form onSubmit={handleSubmit}>
            <div className="grid sm:grid-cols-2 gap-4">
              <div className="relative col-span-2">
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Booking Id
                </label>
                <input
                  type="text"
                  name="bookingId"
                  value={formData.bookingId}
                  onChange={handleChange}
                  className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                  placeholder="Enter Booking Id"
                />
                {errors.bookingId && (
                  <p className="text-red-500 text-sm">{errors.bookingId}</p>
                )}
              </div>

              <div>
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Name
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                  placeholder="Enter Name"
                />
                {errors.name && (
                  <p className="text-red-500 text-sm">{errors.name}</p>
                )}
              </div>

              <div>
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Phone Number
                </label>

                <PhoneInput
                  defaultCountry="IN"
                  international
                  withCountryCallingCode
                  value={phoneNumber}
                  onChange={setPhoneNumber}
                  placeholder="Phone Number"
                  className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                />
                {phoneNumberError && (
                  <p className="text-red-500 text-sm">{phoneNumberError}</p>
                )}
              </div>
              <div className="relative w-full max-w-xs">
                <label
                  className="block text-black sm:text-sm text-xs font-medium mb-1.5"
                  htmlFor="toDate"
                >
                  Check In
                </label>

                <input
                  type="text"
                  name="toDate"
                  id="toDate"
                  ref={inputRef}
                  value={
                    formData?.checkIn && format(formData?.checkIn, "MM-dd-yyyy")
                  }
                  placeholder="mm/dd/yyyy"
                  readOnly
                  onClick={() => setShowCalendar(!showCalendar)}
                  className="block w-full p-2 px-4 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-800 placeholder:text-gray-400 cursor-pointer"
                />

                {showCalendar && (
                  <div
                    ref={calendarRef}
                    className="absolute top-full left-0 bg-white border border-black/50 rounded-lg shadow-lg px-4 py-2 z-50 w-full mt-0.5"
                  >
                    {/* Month navigation */}
                    <div className="flex items-center justify-between mb-4">
                      <button
                        onClick={handlePrevMonth}
                        className="text-lg font-bold px-2"
                      >
                        &#8592;
                      </button>

                      <span className="text-base font-semibold">
                        {format(currentMonth, "MMMM yyyy")}
                      </span>

                      <button
                        onClick={handleNextMonth}
                        className="text-lg font-bold px-2"
                      >
                        &#8594;
                      </button>
                    </div>

                    {/* Weekdays */}
                    <div className="grid grid-cols-7 gap-2 text-center text-sm font-semibold text-gray-600">
                      <div>Su</div>
                      <div>Mo</div>
                      <div>Tu</div>
                      <div>We</div>
                      <div>Th</div>
                      <div>Fr</div>
                      <div>Sa</div>
                    </div>

                    {/* Days */}
                    {/* <div className='grid grid-cols-7 gap-1 mt-2 text-center text-sm'>
                      {generateDays(currentMonth).map((day, index) =>
                        day ? (
                          <button
                            key={index}
                            className='hover:bg-primary-blue hover:text-white rounded-full p-2'
                            onClick={() => {
                              handleChange({
                                target: {
                                  name: "checkIn",
                                  value: format(day, "MM-dd-yyyy"),
                                },
                              });
                              setShowCalendar(false);
                            }}
                          >
                            {day.getDate()}
                          </button>
                        ) : (
                          <div key={index} />
                        )
                      )}
                      
                    </div> */}
                    <div className="grid grid-cols-7 gap-1 mt-2 text-center text-sm">
                      {generateDays(currentMonth).map((day, index) => {
                        if (!day) return <div key={index} className="p-2" />;

                        const isSelected =
                          formData?.checkIn &&
                          format(day, "MM-dd-yyyy") ===
                            format(new Date(formData.checkIn), "MM-dd-yyyy");

                        return (
                          <button
                            key={index}
                            className={`rounded-full p-2  flex items-center justify-center ${isToday(day) ? "border-2 border-primary-blue" : ""} ${
                              isSelected
                                ? "bg-primary-blue text-white"
                                : "hover:bg-primary-blue hover:text-white"
                            }`}
                            onClick={() => {
                              handleChange({
                                target: {
                                  name: "checkIn",
                                  value: format(day, "MM-dd-yyyy"),
                                },
                              });
                              setShowCalendar(false);
                            }}
                          >
                            {day.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                )}
                {errors.fromDate && (
                  <p className="text-red-600 text-sm">{errors.toDate}</p>
                )}
              </div>

              <div className="relative w-full max-w-xs">
                <label
                  className="block text-black sm:text-sm text-xs font-medium mb-1.5"
                  htmlFor="fromDate"
                >
                  Check Out
                </label>

                <input
                  type="text"
                  name="fromDate"
                  id="fromDate"
                  value={
                    formData?.checkOut &&
                    format(formData?.checkOut, "MM-dd-yyyy")
                  }
                  placeholder="mm/dd/yyyy"
                  readOnly
                  onClick={() => setShowFromCalendar(!showFromCalendar)}
                  className="block w-full p-2 px-4 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-800 placeholder:text-gray-400 cursor-pointer"
                  required
                />

                {showFromCalendar && (
                  <div
                    ref={calendarRefFrom}
                    className="absolute top-full left-0 mt-0.5 bg-white border border-black/50 rounded-lg shadow-lg px-4 py-2 z-50 w-full"
                  >
                    {/* Month navigation */}
                    <div className="flex items-center justify-between mb-4">
                      <button
                        onClick={handlePrevMonthFrom}
                        className="text-lg font-bold px-2"
                      >
                        &#8592;
                      </button>

                      <span className="text-base font-semibold">
                        {format(currentMonthFrom, "MMMM yyyy")}
                      </span>

                      <button
                        onClick={handleNextMonthFrom}
                        className="text-lg font-bold px-2"
                      >
                        &#8594;
                      </button>
                    </div>

                    {/* Weekdays */}
                    <div className="grid grid-cols-7 gap-2 text-center text-sm font-semibold text-gray-600">
                      <div>Su</div>
                      <div>Mo</div>
                      <div>Tu</div>
                      <div>We</div>
                      <div>Th</div>
                      <div>Fr</div>
                      <div>Sa</div>
                    </div>

                    {/* Days */}
                    <div className="grid grid-cols-7 gap-1 mt-2 text-center text-sm">
                      {generateDays(currentMonth).map((day, index) => {
                        if (!day) return <div key={index} className="p-2" />;

                        const isSelected =
                          formData?.checkOut &&
                          format(day, "MM-dd-yyyy") ===
                            format(new Date(formData.checkOut), "MM-dd-yyyy");

                        return (
                          <button
                            key={index}
                            className={`rounded-full p-2  flex items-center justify-center ${isToday(day) ? "border-2 border-primary-blue" : ""} ${
                              isSelected
                                ? "bg-primary-blue text-white"
                                : "hover:bg-primary-blue hover:text-white"
                            }`}
                            onClick={() => {
                              handleChange({
                                target: {
                                  name: "checkOut",
                                  value: format(day, "MM-dd-yyyy"),
                                },
                              });
                              setShowFromCalendar(false);
                            }}
                          >
                            {day.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                )}

                {errors.fromDate && (
                  <p className="text-red-600 text-sm">{errors.fromDate}</p>
                )}
              </div>

              <div className="relative col-span-2">
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Upload Documents
                </label>
                <CustomSelect
                  name="document"
                  options={[...Array(10)].map((_, index) => ({
                    value: index + 1,
                    label: index + 1,
                  }))} // Generate options dynamically
                  value={
                    formData.document
                      ? {
                          value: formData.document,
                          label: `${formData.document}`,
                        }
                      : null
                  }
                  onChange={(selectedOption) =>
                    handleChange({
                      target: {
                        name: "document",
                        value: selectedOption?.value,
                      },
                    })
                  }
                  placeholder="Select Document"
                />
                {errors.document && (
                  <p className="text-red-500 text-sm">{errors.document}</p>
                )}
              </div>

              <div className="relative col-span-2">
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Add Photo
                </label>
                <input
                  type="file"
                  name="photos"
                  multiple
                  accept=".jpg, .jpeg, .png"
                  onChange={handleAttachmentChange}
                  className="z-10 cursor-pointer block w-full p-2 px-4 text-sm bg-transparent border rounded-lg opacity-0 border-gray-220 focus:outline-none text-slate-320 placeholder:text-gray-320 absolute top-0 left-0 right-0 bottom-0"
                  placeholder="Upload Attachment"
                />
                <div className="w-full px-4 py-2 border border-[#40E0D0] rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500 flex items-center justify-between border-dashed bg-[#40E0D01A] cursor-pointer">
                  Upload Attachment
                  <CloudUpload className="text-[#40E0D0]" size={22} />
                </div>
                {errors.attachment && (
                  <p className="text-red-500 text-sm">{errors.attachment}</p>
                )}
              </div>
              {formData?.attachment?.length > 0 && (
                <div className="col-span-2">
                  <div className="grid grid-cols-3 sm:gap-4 gap-3">
                    {formData?.attachment?.map((image, index) => (
                      <div
                        key={index}
                        className="relative flex items-center justify-between sm:px-4 px-2 sm:py-2.5 py-2 border border-[#40E0D0] border-dashed bg-[#40E0D01A] rounded-lg"
                      >
                        <Image 
                          // src={image?.url}
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/${image?.url}`}
                          alt={`Existing Image ${index + 1}`}
                          className="w-[54px] h-[36px] object-cover rounded-sm"
                          width={54}
                          height={36}
                        />
                        <span
                          className="text-black hover:text-red-500 font-black cursor-pointer text-xl"
                          onClick={() => removeAttachmentImage(index)}
                        >
                          &#10005;
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="col-span-2">
                <button className="bg-[#5730E8] w-full flex items-center justify-center rounded-full hover:shadow-lg">
                  <Image
                    className="w-auto text-center sm:h-[45px] h-[38px]"
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/digi.png`}
                    height={45}
                    width={120}
                  />
                  <Image
                    className="sm:h-[45px] h-[38px]"
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/digi-icon.svg`}
                    height={45}
                    width={41}
                  />
                </button>
              </div>

              <div className="relative">
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Location
                </label>
                {/* <input
                    type='text'
                    name='comingFrom'
                    value={formData.comingFrom}
                    onChange={handleChange}
                    className='w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500'
                    placeholder='Enter Coming From'
                  />
                  {errors.comingFrom && (
                    <p className='text-red-500 text-sm'>
                      {errors.comingFrom}
                    </p>
                  )} */}
                <Autocomplete
                  onLoad={(autocompleteInstance) =>
                    setAutocomplete(autocompleteInstance)
                  }
                  onPlaceChanged={() => {
                    if (autocomplete) {
                      const place = autocomplete.getPlace();
                      if (!place || !place.geometry) {
                        toast.error("Invalid place selected.");
                        return;
                      }
                      handleFAddressChange(place); // Pass the place to your handler
                    } else {
                      toast.error("Autocomplete is not initialized yet.");
                    }
                  }}
                  className="w-full"
                >
                  <input
                    id="address"
                    name="address"
                    type="text"
                    placeholder="Coming From?"
                    value={formData.comingFrom.address}
                    onChange={handleFromAddressChange}
                    className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                  />
                </Autocomplete>
                {errors.comingFrom && (
                  <p className="text-red-500 text-sm">{errors.comingFrom}</p>
                )}
              </div>

              <div className="relative">
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5 invisible">
                  Going To
                </label>
                {/* <input
                    type='text'
                    name='goingTo'
                    value={formData.goingTo}
                    onChange={handleChange}
                    className='w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500'
                    placeholder='Enter Going To'
                  />
                  {errors.goingTo && (
                    <p className='text-red-500 text-sm'>{errors.goingTo}</p>
                  )} */}
                <Autocomplete
                  onLoad={setAutocompleteD}
                  onPlaceChanged={() => {
                    if (autocompleteD) {
                      const place = autocompleteD.getPlace(); // Safely get the place
                      handleDAddressChange(place); // Pass the place to your handler
                    } else {
                      console.error(
                        "Autocomplete instance is not initialized."
                      );
                    }
                  }}
                  className="w-full"
                >
                  <input
                    id="address"
                    name="address"
                    type="text"
                    placeholder="Going To?"
                    value={formData.goingTo.address}
                    onChange={handleDestinationAddressChange}
                    className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                  />
                </Autocomplete>
                {errors.goingTo && (
                  <p className="text-red-500 text-sm">{errors.goingTo}</p>
                )}
              </div>

              <div className="relative col-span-2">
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Sign To Confirm Your details
                </label>
                <input
                  type="file"
                  name="signature"
                  multiple
                  accept=".jpg, .jpeg, .png"
                  onChange={handleSignDocChange}
                  className="z-10 cursor-pointer block w-full p-2 px-4 text-sm bg-transparent border rounded-lg opacity-0 border-gray-220 focus:outline-none text-slate-320 placeholder:text-gray-320 absolute top-0 left-0 right-0 bottom-0"
                />
                <div className="w-full px-3 py-4 rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-[#5D6679] text-sm placeholder:text-gray-500 flex items-center justify-center border border-[#B9BDC7] cursor-pointer h-[113px] text-center">
                  {formData?.signature?.length > 0
                    ? formData?.signature?.map((image, index) => (
                        <Image 
                          // src={image?.url}
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/${image?.url}`}
                          alt={`Existing Image ${index + 1}`}
                          className="object-cover rounded-sm w-full h-[100px]"
                          width={100}
                          height={100}
                        />
                      ))
                    : "Add your signature here"}
                </div>
                {errors.signature && (
                  <p className="text-red-500 text-sm">{errors.signature}</p>
                )}
              </div>
              {/* <div className='grid grid-cols-3 gap-4 mt-4'>
                {formData?.signature?.map((image, index) => (
                  <div key={index} className='relative'>
                    <Image 
                      src={image?.url}
                      alt={`Existing Image ${index + 1}`}
                      className='w-full h-32 object-cover rounded-lg'
                    />
                    <XCircle
                      className='absolute top-1 right-1 text-red-500 cursor-pointer'
                      size={24}
                      onClick={() => removeSignDocImage(index)}
                    />
                  </div>
                ))}
              </div> */}

              <div className="flex items-center justify-between w-full mt-4 gap-4 col-span-2 bg-white/80 sticky bottom-0 backdrop-blur-sm">
                <button
                  className="hover:bg-[#40E0D0] bg-transparent hover:text-black text-[#40E0D0] border-2 font-normal py-2 px-4 border-[#40E0D0] rounded-lg text-sm xs:mb-0 mb-2 sm:w-[152px] w-full"
                  onClick={() => removeSignDocImage()}
                >
                  Cancel
                </button>
                <button className="bg-[#40E0D0] hover:bg-transparent text-black hover:text-[#40E0D0] border-2 font-normal py-2 px-4  border-[#40E0D0] rounded-lg text-sm sm:w-[152px] w-full">
                  Undo
                </button>
              </div>

              <p className="col-span-2">
                <FormControlLabel
                  control={<Checkbox className="py-0" />}
                  sx={{
                    "& .Mui-checked": {
                      color: "#", // Set the checked color
                    },
                  }}
                  className="items-start"
                  label={
                    <Typography className="text-[#6D6D6D] sm:text-base text-sm font-normal">
                      I agreed to the{" "}
                      <Link
                        href="#"
                        style={{
                          color: "#40E0D0",
                          textDecoration: "underline",
                          textTransform: "capitalize",
                        }}
                        prefetch={false}
                      >
                        terms and condition, Privacy policy.
                      </Link>
                    </Typography>
                  }
                />
              </p>

              <div className="xs:flex block  items-center justify-between w-full my-7 gap-4 col-span-2 py-4 bg-white/80 sticky bottom-0 backdrop-blur-sm">
                <button
                  onClick={closecheckineditModal}
                  className="hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 px-4 border-black rounded-lg w-full text-sm xs:mb-0 mb-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm"
                >
                  Edit
                </button>
              </div>
            </div>
          </form>
        </div>
      </section>
    </>
  );
};

export default Editwebcheckin;
