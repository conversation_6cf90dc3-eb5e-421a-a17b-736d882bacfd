"use client";

import React, { useEffect, useRef, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
// import { Pagination, Navigation } from 'swiper/modules';
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/navigation";
import Link from "next/link";
import Image from "next/image";
import { getHomePagePropertyCountApi } from "@/services/webflowServices";
import countries from "world-countries";
// import { motion } from "framer-motion";

const FlagSlider = () => {
  const swiperRef = useRef(null);
  const [activeIndex, setActiveIndex] = useState(0);
  const [flags, setFlags] = useState([]);
  const [popertyCount, setPopertyCount] = useState([]);
  const isFirstRender = useRef(null);
  const [isLoading, setIsLoading] = useState(true);

  const getZIndex = (i) => {
    const distance = Math.abs(i - activeIndex);
    if (distance === 0) return 30;
    if (distance === 1) return 20;
    return 10;
  };

  const getScale = (i) => {
    const distance = Math.abs(i - activeIndex);
    if (distance === 0) return "scale-[1.2]";
    if (distance === 1 || i == 14) return "scale-110";
    return "scale-100";
  };

  // const [coverflowEffect, setCoverflowEffect] = useState({
  //   rotate: 30,
  //   stretch: 0,
  //   depth: 300,
  //   modifier: 1,
  //   slideShadows: false,
  // });

  // useEffect(() => {
  //   const updateEffect = () => {
  //     const isMobile = window.innerWidth < 520;
  //     setCoverflowEffect({
  //       rotate: isMobile ? 50 : 30,
  //       stretch: 0,
  //       depth: isMobile ? 100 : 300,
  //       modifier: isMobile ? 1 : 1,
  //       slideShadows: false,
  //     });
  //   };

  //   updateEffect(); // initial
  //   window.addEventListener("resize", updateEffect);
  //   return () => window.removeEventListener("resize", updateEffect);
  // }, []);

  const countryImages = {
    India: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/india-new.webp`,
    Thailand: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Thailand-new.webp`,
    Indonesia: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Indonesia-new.webp`,
    Colombia: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/colombiaaa.webp`,
    Spain: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/spainnn.webp`,
    Mexico: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mexicooo.webp`,
    Italy: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/italyyy-new.webp`,
    Portugal: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/portugalll.webp`,
    Brazil: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/brazilll.webp`,
    USA: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/usaaa.webp`,
    Japan: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/japannn.webp`,
    Vietnam: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Vietnam-new.webp`,
    France: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/franceee.webp`,
    Australia: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/australiaaa.webp`,
    Peru: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/peruuu.webp`,
  };

  const CountryList = [
    "India",
    "Thailand",
    "Indonesia",
    "Colombia",
    "Spain",
    "Mexico",
    "Italy",
    "Portugal",
    "Brazil",
    "USA",
    "Japan",
    "Vietnam",
    "France",
    "Australia",
    "Peru",
  ];

  useEffect(() => {
    const fetchPropertyCount = async () => {
      try {
        const response = await getHomePagePropertyCountApi({
          countries: CountryList,
        });
        setPopertyCount(response?.data?.data?.propertyCounts || []);
      } catch (error) {
        console.error("Error fetching stay data:", error);
      } finally {
        /* empty */
      }
    };
    if (!isFirstRender.current) {
      fetchPropertyCount();
    } else {
      isFirstRender.current = false;
    }
  }, []);

  const countriesForFlag = [
    "India",
    "Thailand",
    "Indonesia",
    "Colombia",
    "Spain",
    "Mexico",
    "Italy",
    "Portugal",
    "Brazil",
    "United States",
    "Japan",
    "Vietnam",
    "France",
    "Australia",
    "Peru",
  ];

  useEffect(() => {
    const fetchFlags = async () => {
      try {
        let data = countries
          .filter((country) => {
            const commonName = country.name.common;
            return countriesForFlag.includes(commonName);
          })
          .map((country) => {
            const name =
              country.name.common === "United States"
                ? "USA"
                : country.name.common;
            return {
              id: country.cca3,
              name,
              flagImg: `https://flagcdn.com/w320/${country.cca2.toLowerCase()}.png`,
              backgroundImg: countryImages[name] || "", // fallback if no bg found
            };
          });

        // Sort data to match countriesForFlag order
        data.sort((a, b) => {
          const idxA = countriesForFlag.indexOf(a.name === "USA" ? "United States" : a.name);
          const idxB = countriesForFlag.indexOf(b.name === "USA" ? "United States" : b.name);
          return idxA - idxB;
        });

        // Preload flag images before showing them
        await Promise.all(
          data.map(
            (item) =>
              new Promise((resolve) => {
                const img = new window.Image();
                img.src = item.flagImg;
                img.onload = resolve;
                img.onerror = resolve;
              })
          )
        );

        setFlags(data);
        setIsLoading(false); // ✅ Set loading to false after all done
      } catch (error) {
        console.error("Error fetching flags:", error);
        setIsLoading(false); // ✅ Stop loading on error as well
      }
    };

    fetchFlags();
  }, [countries, countriesForFlag, countryImages]);

  // const SkeletonItem = () => (
  //   <div className="flex flex-col items-center animate-pulse opacity-30">
  //     <div className="rounded-full bg-slate-200 xs:min-w-12 xs:min-h-12 xs:w-10 xs:h-10 min-w-10 min-h-10 w-10 h-10 mb-1"></div>
  //     <div className="w-12 h-3 bg-slate-200 rounded"></div>
  //   </div>
  // );

  const [isReady, setIsReady] = useState(false);
  const [isDesktop, setIsDesktop] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia("(min-width: 768px)");
    setIsDesktop(mediaQuery.matches);
    setIsReady(true); // prevent rendering until screen size is detected
  }, []);

  return (
    <div className="relative container z-10 overflow-hidden">
      <div className="container w-full xs:pt-2 xs:px-2 px-0 overflow-hidden">
        {/* Navigation Buttons */}
        {/* <div className="hidden md:flex justify-end items-center gap-2 mb-2 mr-4">
      <button 
        onClick={() => swiperRef.current?.slidePrev()} 
        className="p-2 rounded-full bg-white opacity-20 backdrop-blur-sm shadow-md hover:opacity-30 transition-colors"
        aria-label="Previous slide"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
      </button>
      <button 
        onClick={() => swiperRef.current?.slideNext()} 
        className="p-2 rounded-full bg-white opacity-20 backdrop-blur-sm shadow-md hover:opacity-30 transition-colors"
        aria-label="Next slide"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div> */}
        <div className="hidden md:flex justify-end items-center gap-2 mb-4 mr-4">
          <button
            onClick={() => swiperRef.current?.slidePrev()}
            className="p-2 rounded-full bg-white/20 backdrop-blur-sm shadow-md hover:bg-white/30 transition-colors duration-200"
            aria-label="Previous slide"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 text-white"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
          <button
            onClick={() => swiperRef.current?.slideNext()}
            className="p-2 rounded-full bg-white/20 backdrop-blur-sm shadow-md hover:bg-white/30 transition-colors duration-200"
            aria-label="Next slide"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 text-white"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        </div>
        {/* Flag Indicators */}
        {/* <motion.div
            initial={{ y: 10, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, ease: "easeOut"}}
            viewport={{ once: false }} 
            className="flex justify-center mb-10 pt-8 gap-4">
          
        </motion.div> */}

        {/* <motion.div
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, ease: "easeOut"}}
            viewport={{ once: false }}> */}

        {/* <div className="flex justify-center xs:mb-10 mb-5 pt-8 gap-4 w-full">
             {isLoading
              ? [...Array(15)].map((_, i) => <SkeletonItem key={i} />)
              : [...Array(15)].map((_, i) => {
                  const offset = i - 7;
                  const actualIndex = (activeIndex + offset + flags.length) % flags.length;
                  const flag = flags[actualIndex];

                  return (
                    <div className="w-[80px] h-[100px] flex justify-center items-center" key={`${flag?.id || i}-${actualIndex}`}
                        onClick={() => swiperRef.current?.slideToLoop(actualIndex)}>
                      <button className={`flex flex-col items-center transform-gpu origin-center transition-transform ${
                          offset === 0 ? 'scale-150' : 'opacity-60'
                        }`}
                      >
                        <Image
                          src={flag.flagImg}
                          alt="Country Flag"
                          width={50}
                          height={50}
                          priority
                          className={`xs:min-w-12 xs:min-h-12 xs:w-10 xs:h-10 min-w-10 min-h-10 w-10 h-10 rounded-full mb-1 object-cover ${
                            offset === 0
                              ? 'border-primary-blue border-2 xs:p-1 p-0.5 shadow-md shadow-primary-blue/40'
                              : ''
                          }`}
                        />
                        <span className="text-xs font-medium text-white">{flag.name}</span>
                      </button>
                    </div>
                  );
                })}
          </div> */}

        {/* Swiper */}
        <Swiper
          onSwiper={(swiper) => (swiperRef.current = swiper)}
          onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
          slidesPerView={5}
          breakpoints={{
            0: {
              slidesPerView: 3,
            },
            480: {
              slidesPerView: 3,
            },
            640: {
              slidesPerView: 3,
            },
            768: {
              slidesPerView: 5,
            },
            1024: {
              slidesPerView: 5,
            },
            1280: {
              slidesPerView: 5,
            },
          }}
          centeredSlides={true}
          watchSlidesProgress={true}
          spaceBetween={0}
          pagination={false}
          navigation={false}
          loop={true}
          // autoplay={
          //   window.innerWidth < 640
          //     ? {
          //         delay: 3000,
          //         disableOnInteraction: false,
          //       }
          //     : false
          // }
          modules={[Autoplay]}
          className="mySwiper w-full mx-auto pt-10 2xl:pb-20 sm:pb-10 pb-5 relative z-20 md:px-0 sm:px-[11px] xs:px-[8px] px-[6px] overflow-hidden"
        >
          {isLoading
            ? [...Array(5)].map((_, index) => (
                <SwiperSlide
                  key={`skeleton-${index}`}
                  className={`transition-all duration-300 transform-gpu bg-slate-200 animate-pulse ${getScale(
                    index
                  )} relative`}
                  style={{ zIndex: getZIndex(index) }}
                >
                  <div className="w-full h-[400px] rounded-lg bg-slate-200 animate-pulse relative flex items-center justify-center">
                    <h1 className="text-white font-bold font-manrope text-center mt-8">
                      MixDorm
                    </h1>
                    <div className="absolute bottom-0 w-full bg-gradient-to-t from-black/40 to-transparent p-4 rounded-b-lg">
                      <div className="w-1/2 h-4 bg-slate-200 rounded mb-2"></div>
                      <div className="w-1/3 h-3 bg-slate-200 rounded"></div>
                    </div>
                  </div>
                </SwiperSlide>
              ))
            : isReady &&
              flags.map((country, index) => {
                return (
                  <SwiperSlide
                    key={country.id}
                    className={`transition-all duration-300 transform-gpu ${getScale(
                      index
                    )} relative`}
                    style={{ zIndex: getZIndex(index), width: "auto" }}
                  >
                    <Link
                      className="h-full w-full z-30"
                      href={`/tophostel?country=${country.name}`}
                      prefetch={false}
                    >
                      <div className="overflow-hidden z-30 shadow-lg hover:shadow-primary-blue/40 transition-transform duration-300 relative rounded-lg">
                        <Image
                          src={country.backgroundImg}
                          width={300}
                          height={400}
                          alt="Country"
                          quality={90}
                          sizes="(max-width: 480px) 100vw, (max-width: 768px) 80vw, (max-width: 1024px) 50vw, 300px"
                          {...(isDesktop
                            ? { priority: true }
                            : { loading: "lazy" })}
                          className={`object-cover w-full h-full rounded-lg ${
                            isLoading
                              ? "bg-slate-200 animate-pulse"
                              : "bg-slate-200"
                          }`}
                        />
                        <div className="absolute bottom-0 rounded-lg bg-gradient-to-t xs:from-black/70 from-black/40 to-transparent text-white sm:p-4 p-2 w-full">
                          <p className="sm:text-lg text-sm xs:font-semibold font-medium hidden md:block">
                            Hostel in <br className="md:hidden block" />{" "}
                            {country.name}
                          </p>
                          <p className="sm:text-lg text-sm xs:font-semibold font-medium block md:hidden">
                            {country.name}
                          </p>
                          {popertyCount?.[country.name] && (
                            <p className="mt-1 text-xs 2xl:text-sm font-normal">
                              {popertyCount?.[country.name]
                                ? `${popertyCount[country.name]} Hostels`
                                : "\u00A0"}
                            </p>
                          )}
                        </div>
                      </div>
                    </Link>
                  </SwiperSlide>
                );
              })}
        </Swiper>

        {/* </motion.div> */}
      </div>
    </div>
  );
};

export default FlagSlider;
