import React, { useState } from "react";
import dynamic from "next/dynamic";

const Explore = dynamic(() => import("./explore"), {
    ssr: false,
  });
  const Adventurer = dynamic(() => import("./adventurer"), {
    ssr: false,
  });
  const Nomad = dynamic(() => import("./nomad"), {
    ssr: false,
  });
  const RedeemPoint = dynamic(() => import("./redeemPoint"), {
    ssr: false,
  });
  
const Rewards = () => {
    const [openRedeemPoint, setopenRedeemPoint] = useState(false);
    const handleOpenRedeemPoint = () => setopenRedeemPoint(true);
  return (
    <>
    {!openRedeemPoint &&
      <div className="w-full">
          <div className="flex justify-between">
            <h2 className="text-[#40E0D0] flex gap-1 text-2xl font-bold mb-6">
              My
              <span className="text-black ml-0.5"> Wallet</span>
            </h2>
            <div>
              <button className="rounded-full hover:bg-sky-blue-750 hover:text-white transition-all bg-primary-blue py-2 px-8 text-black font-semibold capitalize mx-auto block"
              onClick={handleOpenRedeemPoint}
              >
                Redeem Points
              </button>
            </div>
          </div>
          <div className="mb-5 flex gap-2">
            <div className="w-[240px] h-[100px] border border-[#FF72AD] rounded-3xl px-6 flex items-center">
              <div className="">
                <p className="text-lg text-black font-semibold">
                  Total Balance
                </p>
                <p className="text-[#FF72AD] font-semibold text-2xl">52,000</p>
              </div>
            </div>
            <div className="w-[240px] h-[100px] border border-[#40E0D0] rounded-3xl px-5 flex items-center">
              <div className="">
                <p className="text-lg text-black font-semibold">Reward Point</p>
                <p className="text-[#40E0D0] font-semibold text-2xl">2,000</p>
              </div>
            </div>
          </div>
          <h4 className="font-extrabold text-primary-blue underline mb-3">
            My Points
          </h4>
          {/* Explore start */}

        <Explore />

        {/* Adventurer start */}
         <Adventurer />

        {/* Nomad Start */}
          <Nomad />
        </div>
    }

       
  {/* Redeem point Start */}
  {openRedeemPoint && (
        <RedeemPoint />
      )}
      
    </>
  
  );
};

export default Rewards;
