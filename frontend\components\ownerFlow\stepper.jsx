 
import React from 'react';
import CheckRoundedIcon from '@mui/icons-material/CheckRounded';

const Stepper = ({ currentStep, onViewClick }) => {
    const steps = [
        'Property Details',
        'Upload Document',
        'Onboarding',
    ];

    return (
        <div className="flex justify-center items-center rounded-[30px] w-full sm:max-w-3xl md:p-8 py-5 px-3  mb-10 mx-auto bg-[#FFFFFF]">
            <div className="flex sm:w-[85%] w-full justify-between">
                {steps.map((step, index) => (
                    <React.Fragment key={index}>
                        <div className="flex flex-col items-center relative">
                            <div className="flex flex-col items-center mb-3 sm:w-36">
                                <div
                                    className={`rounded-full w-7 h-7 flex items-center justify-center text-black ${index <= currentStep ? 'bg-[#40E0D0]' : 'bg-[#D9F9F6]'}`}
                                    aria-label={index <= currentStep ? `Step ${index + 1} completed` : `Step ${index + 1} not completed`}
                                >
                                    {index <= currentStep ? <CheckRoundedIcon /> : index + 1}
                                </div>
                                <span
                                    className={`mt-2 text-sm font-manrope font-semibold ${index <= currentStep ? 'text-black' : 'text-gray-400'}`}
                                >
                                    {step}
                                </span>
                            </div>
                            {(index < currentStep || index === 0) && (
                                <div className="text-black text-sm cursor-pointer">
                                    <span
                                        onClick={() => index <= currentStep && onViewClick(index)}
                                        className="mr-2 underline underline-offset-2"
                                    >
                                        View | Edit
                                    </span>
                                    {/* {index < steps.length - 1 && (
                                        <span
                                            onClick={() => index <= currentStep && onEditClick(index)}
                                            className="cursor-pointer"
                                        >
                                            Edit
                                        </span>
                                    )} */}
                                </div>
                            )}
                            {/* {index === steps.length - 1 && currentStep === steps.length - 1 && (
                                <div className="text-black text-sm cursor-pointer">
                                    {'Review | Edit'}
                                </div>
                            )} */}
                            {index < steps.length - 1 && (
                                <div
                                    className={`sm:w-[9rem] w-[80%] border-t-[3px] mt-[12px] rounded absolute left-[80%] ${index <= currentStep ? 'border-[#40E0D0]' : 'border-[#D9F9F6]'}`}
                                ></div>
                            )}
                        </div>
                        
                    </React.Fragment>
                ))}
            </div>
        </div>
    );
};

export default Stepper;
