function getCountryCode(countryName) {
    const countryCodes = {
        "Afghanistan": "AF",
        "Albania": "AL",
        "Algeria": "DZ",
        "Andorra": "AD",
        "Angola": "AO",
        "Antigua and Barbuda": "AG",
        "Argentina": "AR",
        "Armenia": "AM",
        "Australia": "AU",
        "Austria": "AT",
        "Azerbaijan": "AZ",
        "Bahamas": "BS",
        "Bahrain": "BH",
        "Bangladesh": "BD",
        "Barbados": "BB",
        "Belarus": "BY",
        "Belgium": "BE",
        "Belize": "BZ",
        "Benin": "BJ",
        "Bhutan": "BT",
        "Bolivia": "BO",
        "Bosnia and Herzegovina": "BA",
        "Botswana": "BW",
        "Brazil": "BR",
        "Brunei Darussalam": "BN",
        "Bulgaria": "BG",
        "Burkina Faso": "BF",
        "Burundi": "BI",
        "Cabo Verde": "CV",
        "Cambodia": "KH",
        "Cameroon": "CM",
        "Canada": "CA",
        "Central African Republic": "CF",
        "Chad": "TD",
        "Chile": "CL",
        "China": "CN",
        "Colombia": "CO",
        "Comoros": "KM",
        "Congo": "CG",
        "Costa Rica": "CR",
        "Croatia": "HR",
        "Cuba": "CU",
        "Cyprus": "CY",
        "Czech Republic": "CZ",
        "Denmark": "DK",
        "Djibouti": "DJ",
        "Dominica": "DM",
        "Dominican Republic": "DO",
        "Ecuador": "EC",
        "Egypt": "EG",
        "El Salvador": "SV",
        "Equatorial Guinea": "GQ",
        "Eritrea": "ER",
        "Estonia": "EE",
        "Eswatini": "SZ",
        "Ethiopia": "ET",
        "Fiji": "FJ",
        "Finland": "FI",
        "France": "FR",
        "Gabon": "GA",
        "Gambia": "GM",
        "Georgia": "GE",
        "Germany": "DE",
        "Ghana": "GH",
        "Greece": "GR",
        "Grenada": "GD",
        "Guatemala": "GT",
        "Guinea": "GN",
        "Guinea-Bissau": "GW",
        "Guyana": "GY",
        "Haiti": "HT",
        "Honduras": "HN",
        "Hungary": "HU",
        "Iceland": "IS",
        "India": "IN",
        "Indonesia": "ID",
        "Iran (Islamic Republic of)": "IR",
        "Iraq": "IQ",
        "Ireland": "IE",
        "Israel": "IL",
        "Italy": "IT",
        "Jamaica": "JM",
        "Japan": "JP",
        "Jordan": "JO",
        "Kazakhstan": "KZ",
        "Kenya": "KE",
        "Kiribati": "KI",
        "Korea (Democratic People's Republic of)": "KP",
        "Korea, Republic of": "KR",
        "Kuwait": "KW",
        "Kyrgyzstan": "KG",
        "Lao People's Democratic Republic": "LA",
        "Latvia": "LV",
        "Lebanon": "LB",
        "Lesotho": "LS",
        "Liberia": "LR",
        "Libya": "LY",
        "Liechtenstein": "LI",
        "Lithuania": "LT",
        "Luxembourg": "LU",
        "Madagascar": "MG",
        "Malawi": "MW",
        "Malaysia": "MY",
        "Maldives": "MV",
        "Mali": "ML",
        "Malta": "MT",
        "Marshall Islands": "MH",
        "Mauritania": "MR",
        "Mauritius": "MU",
        "Mexico": "MX",
        "Micronesia (Federated States of)": "FM",
        "Moldova, Republic of": "MD",
        "Monaco": "MC",
        "Mongolia": "MN",
        "Montenegro": "ME",
        "Morocco": "MA",
        "Mozambique": "MZ",
        "Myanmar": "MM",
        "Namibia": "NA",
        "Nauru": "NR",
        "Nepal": "NP",
        "Netherlands": "NL",
        "New Zealand": "NZ",
        "Nicaragua": "NI",
        "Niger": "NE",
        "Nigeria": "NG",
        "North Macedonia": "MK",
        "Norway": "NO",
        "Oman": "OM",
        "Pakistan": "PK",
        "Palau": "PW",
        "Panama": "PA",
        "Papua New Guinea": "PG",
        "Paraguay": "PY",
        "Peru": "PE",
        "Philippines": "PH",
        "Poland": "PL",
        "Portugal": "PT",
        "Qatar": "QA",
        "Romania": "RO",
        "Russian Federation": "RU",
        "Rwanda": "RW",
        "Saint Kitts and Nevis": "KN",
        "Saint Lucia": "LC",
        "Saint Vincent and the Grenadines": "VC",
        "Samoa": "WS",
        "San Marino": "SM",
        "Sao Tome and Principe": "ST",
        "Saudi Arabia": "SA",
        "Senegal": "SN",
        "Serbia": "RS",
        "Seychelles": "SC",
        "Sierra Leone": "SL",
        "Singapore": "SG",
        "Slovakia": "SK",
        "Slovenia": "SI",
        "Solomon Islands": "SB",
        "Somalia": "SO",
        "South Africa": "ZA",
        "South Sudan": "SS",
        "Spain": "ES",
        "Sri Lanka": "LK",
        "Sudan": "SD",
        "Suriname": "SR",
        "Sweden": "SE",
        "Switzerland": "CH",
        "Syrian Arab Republic": "SY",
        "Tajikistan": "TJ",
        "Tanzania, United Republic of": "TZ",
        "Thailand": "TH",
        "Timor-Leste": "TL",
        "Togo": "TG",
        "Tonga": "TO",
        "Trinidad and Tobago": "TT",
        "Tunisia": "TN",
        "Turkey": "TR",
        "Turkmenistan": "TM",
        "Tuvalu": "TV",
        "Uganda": "UG",
        "Ukraine": "UA",
        "United Arab Emirates": "AE",
        "United Kingdom": "GB",
        "United States": "US",
        "Uruguay": "UY",
        "Uzbekistan": "UZ",
        "Vanuatu": "VU",
        "Venezuela (Bolivarian Republic of)": "VE",
        "Viet Nam": "VN",
        "Yemen": "YE",
        "Zambia": "ZM",
        "Zimbabwe": "ZW"
    };

    // Convert countryName to title case (e.g., "united states" to "United States")
    const properName = countryName.toLowerCase().split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');

    return countryCodes[properName] || null;
}

import propertyModel from '../models/properties.js';

// Helper function to generate the property number
export const generatePropertyNumber = async () => {
    // Define the regex to find the latest property number matching the format MDhost####
    const regex = new RegExp(`^MDhost\\d{4}$`, 'i');
    const latestProperty = await propertyModel.findOne({ number: regex }).sort({ createdAt: -1 });

    let nextNumber = 1010; // Start numbering from 1010
    if (latestProperty) {
        // Extract the numeric part from the latest property number
        const latestNumber = parseInt(latestProperty.number.replace('MDhost', ''), 10);
        nextNumber = latestNumber + 1; // Increment the number
    }

    // Return the new property number
    return `MDhost${nextNumber}`;
};


export const generateOtaPropertyNumber = async () => {
    const regex = new RegExp(`^MD-\\d+$`, 'i');
    const latestProperty = await propertyModel.findOne({ number: regex }).sort({ createdAt: -1 });

    let nextNumber = 1;
    if (latestProperty) {
        const latestNumber = parseInt(latestProperty.number.split('-').pop(), 10);
        nextNumber = latestNumber + 1;
    }

    return `MD-${String(nextNumber).padStart(2, '0')}`;
};

import bookingModel from '../models/bookings.js';
import eventBookingModel from '../models/eventBooking.js';

export const generateBookingReferenceNumber = async (country) => {
    const countryCode = getCountryCode(country);
    if (!countryCode) {
        throw new Error('Invalid country specified');
    }

    // Regex to find the latest reference number for the given country code
    const regex = new RegExp(`^MD-${countryCode}-\\d+$`, 'i');
    const latestBooking = await bookingModel.findOne({ referenceNumber: regex }).sort({ createdAt: -1 }).exec();

    let nextNumber = 1;
    if (latestBooking) {
        // Extract the last number part of the reference number
        const latestNumber = parseInt(latestBooking.referenceNumber.split('-').pop(), 10);
        nextNumber = latestNumber + 1;
    }

    return `MD-${countryCode}-${String(nextNumber).padStart(2, '0')}`;
};
export const generateEventNumber = async () => {
    // Define the regex to find the latest property number matching the format MDhost####
    const regex = new RegExp(`^MD-Mix-Event-\\d{4}$`, 'i');
    const latestProperty = await eventBookingModel.findOne({ bookingRefNumber: regex }).sort({ createdAt: -1 });

    let nextNumber = 1010; // Start numbering from 1010
    if (latestProperty) {
        // Extract the numeric part from the latest property number
        const latestNumber = parseInt(latestProperty.number.replace('MD-Mix-Event-', ''), 10);
        nextNumber = latestNumber + 1; // Increment the number
    }

    // Return the new property number
    return `MD-Mix-Event-${nextNumber}`;
};
export const generateRideNumber = async () => {
    // Define the regex to find the latest property number matching the format MDhost####
    const regex = new RegExp(`^MD-Mix-Ride-\\d{4}$`, 'i');
    const latestProperty = await eventBookingModel.findOne({ bookingRefNumber: regex }).sort({ createdAt: -1 });

    let nextNumber = 1010; // Start numbering from 1010
    if (latestProperty) {
        // Extract the numeric part from the latest property number
        const latestNumber = parseInt(latestProperty.number.replace('MD-Mix-Ride-', ''), 10);
        nextNumber = latestNumber + 1; // Increment the number
    }

    // Return the new property number
    return `MD-Mix-Ride-${nextNumber}`;
};
