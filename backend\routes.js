// routes.js
import authRoutes from './routes/auth.js';
import socialAuthRoutes from './routes/socialAuth.js';
import hostelRoutes from './routes/hostel.js';
import roomRoutes from './routes/room.js';
import eventRoutes from './routes/event.js';
import commonRoutes from './routes/front/properties.js';
import bookingRoutes from './routes/booking.js';
import reviewsRoutes from './routes/reviews.js';
import blogRoutes from './routes/blog.js';
import userRoutes from './routes/user.js';
import pageRoutes from './routes/page.js';
import permissionRoutes from './routes/permission.js';
import contactUsRoutes from "./routes/contactUs.js";
import settingsRoute from "./routes/setting.js";
import ridesRoute from "./routes/ride.js";
import paymentRoute from "./routes/payment.js";
import fileUploadRoute from "./routes/fileUpload.js";
import eventBookRoute from "./routes/eventBooking.js";
import firebaseRoute from "./routes/firebase.js";
import profileViewsRoute from "./routes/profileViews.js";
import dashboardRoute from "./routes/dashboard.js";
import transactionsRoute from "./routes/transactions.js";
import eCheckInRoute from "./routes/eCheckIns.js";
import noticeBoard from "./routes/noticeboard.js";
import mixCreatorsRoute from "./routes/mixCreators.js";
import chatRoute from './routes/chats.js';
import chanelManagerRoute from './routes/chanelManagers.js';
import otaPropertiesRoute from './routes/otaProperties.js';
import propertyWishList from "./routes/wishlist.js";
import groupRoute from "./routes/group.js";
import expenseRoute from "./routes/expense.js";
import walletRoute from './routes/wallet.js' 
import cloudRoute from "./routes/clouds.js"
import mixCreatorRoute from "./routes/mixCreators.js"
import subscriptionRoute from './routes/subscription.js' 
import hyperGuestRoute from './routes/hyperGuest.js'
import ezeeRoute from "./routes/ezee.js"
import scripts from "./routes/scripts.js"
export default function setupRoutes(app) {
    app.use('/auth', authRoutes);
    app.use('/socialAuth', socialAuthRoutes);
    app.use('/property', hostelRoutes);
    app.use('/room', roomRoutes);
    app.use('/events', eventRoutes);
    app.use('/', commonRoutes);
    app.use('/otaProperties', otaPropertiesRoute);
    app.use('/booking', bookingRoutes);
    app.use('/reviews', reviewsRoutes);
    app.use('/blog', blogRoutes);
    app.use('/api/payment', paymentRoute);
    app.use('/users', userRoutes);
    app.use('/permissions', permissionRoutes);
    app.use('/fileUpload', fileUploadRoute);
    app.use('/eventBookings', eventBookRoute);
    app.use('/api', firebaseRoute);
    app.use('/contact-us', contactUsRoutes);
    app.use('/settings', settingsRoute);
    app.use('/profileViews', profileViewsRoute);
    app.use("/rides", ridesRoute);
    app.use("/dashboard", dashboardRoute);
    app.use("/transactions", transactionsRoute);
    app.use('/check-in', eCheckInRoute);
    app.use('/noticeBoard', noticeBoard);
    app.use('/creators', mixCreatorsRoute);
    app.use('/chats', chatRoute);
    app.use('/channel-managers', chanelManagerRoute);
    app.use('/wishlists',propertyWishList);
    app.use('/group',groupRoute);
    app.use('/expenses',expenseRoute);
    app.use('/wallets',walletRoute)
    app.use('/mix-creator',mixCreatorRoute)
    app.use('/clouds',cloudRoute)
    // app.use('/subscription',subscriptionRoute)
    app.use('/hyperGuest',hyperGuestRoute)
    app.use('/ezee',ezeeRoute)
    app.use('/scripts',scripts)

}
