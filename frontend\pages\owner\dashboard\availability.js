import React, { useState } from "react";
import { Plus, Search, SlidersHorizontal } from "lucide-react";
import AddRoom from "@/components/ownerFlow/dashboard/addRooms";
import dynamic from "next/dynamic";
import Breadcrumb from "@/components/breadcrumb/breadcrumb";

const Roomlist = dynamic(
  () => import("@/components/ownerFlow/dashboard/roomList"),
  {
    ssr: false,
  }
);

const AddEditRoom = dynamic(
  () => import("@/components/ownerFlow/dashboard/addEditRoom"),
  {
    ssr: false,
  }
);

const RateAvalability = dynamic(
  () => import("@/components/ownerFlow/dashboard/rateAvaibility"),
  {
    ssr: false,
  }
);

const Calendar = dynamic(
  () => import("@/components/ownerFlow/dashboard/calendar"),
  {
    ssr: false,
  }
);

const Availability = () => {
  const [activeTab, setActiveTab] = useState(1);
  const [editRoom, setEditRoom] = useState(null);

  const tabs = [
    {
      id: 1,
      name: "Listed Room",
    },
    {
      id: 2,
      name: "Calendar",
    },
    // {
    //   id: 3,
    //   name: "Room List",
    // },
    {
      id: 3,
      name: "Room Rate",
    },
    // {
    //   id: 5,
    //   name: "Rates Availability",
    // },
  ];
  const renderTabContent = () => {
    switch (activeTab) {
      case 1:
        return (
          <Roomlist setActiveTab={setActiveTab} setEditRoom={setEditRoom} />
        );
      case 2:
        return <Calendar />;
      // case 3:
      //   return (
      //     <Roomlist setActiveTab={setActiveTab} setEditRoom={setEditRoom} />
      //   );
      // case 3:
      //   return <AddEditRoom editRoom={editRoom} />;
      case 3:
        return <RateAvalability />;
      case 4:
        return <AddRoom setActiveTab={setActiveTab} />;
      case 5:
        return <AddEditRoom editRoom={editRoom} setActiveTab={setActiveTab} />;
      default:
        return (
          <Roomlist setActiveTab={setActiveTab} setEditRoom={setEditRoom} />
        );
    }
  };
  return (
    <>
      {activeTab === 1 ? (
        <Breadcrumb
          items={[
            { label: "Dashboard", href: "/owner/dashboard" },
            { label: "Availability" },
          ]}
        />
      ) : activeTab === 2 ? (
        <Breadcrumb
          items={[
            { label: "Dashboard", href: "/owner/dashboard" },
            { label: "Calender" },
          ]}
        />
      ) : activeTab === 3 ? (
        <Breadcrumb
          items={[
            { label: "Dashboard", href: "/owner/dashboard" },
            { label: "RateAvalability" },
          ]}
        />
      ) : activeTab === 4 ? (
        <Breadcrumb
          items={[
            { label: "Dashboard", href: "/owner/dashboard" },
            { label: "Availability",  onClick: () => setActiveTab(1)},
            { label: "Add Room" },
          ]}
        />
      ) : activeTab === 5 ? (
        <Breadcrumb
          items={[
            { label: "Dashboard", href: "/owner/dashboard" },
            { label: "Availability",  onClick: () => setActiveTab(1)},
            { label: "Edit Room" },
          ]}
        />
      ) : null}

      <section className='w-full'>
        <h2 className='text-xl font-medium text-black-230'>Availability</h2>
        <div className='w-full mt-5 bg-white shadow-4xl p-6 rounded-2xl'>
          <div className='flex mb-4'>
            {activeTab !== 4 && activeTab !== 5 && (
              <ul className='flex bg-[#F7F9FC] border border-gray-300 rounded-lg'>
                {tabs.map((tab) => (
                  <li
                    className={`cursor-pointer relative inline-block py-4 px-3 text-sm font-medium ${
                      activeTab === tab.id ? "text-[#2F7BEB]" : "text-[#5D6679]"
                    }`}
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                  >
                    {tab.name}
                    {activeTab === tab.id && (
                      <div className='absolute w-5/6 h-[3px] rounded-full bottom-2 bg-[#2F7BEB] left-0 right-0 mx-auto'></div>
                    )}
                  </li>
                ))}
              </ul>
            )}
            {activeTab === 1 && (
              <div className='ml-auto flex gap-4 items-center'>
                <div className='relative'>
                  <button
                    type='button'
                    className='absolute text-black top-1/2 left-2.5 transform -translate-y-1/2'
                  >
                    <Search size={16} />
                  </button>
                  <input
                    type='text'
                    className='px-8 py-3 rounded-md w-80 outline-none border text-sm font-light'
                    placeholder='Search Arrivals'
                  />
                  <button
                    className='absolute text-black top-1/2 right-2.5 transform -translate-y-1/2'
                    type='button'
                  >
                    <SlidersHorizontal size={16} />
                  </button>
                </div>
                <button
                  className='bg-blue-500 text-white px-4 py-3 text-sm rounded'
                  onClick={() => {
                    setActiveTab(4);
                  }}
                >
                  Add Room
                </button>
                <button
                  className='flex items-center justify-center px-4 py-3 text-sm text-white rounded-lg w-fit bg-primary-blue'
                  onClick={() => {
                    setActiveTab(5);
                  }}
                >
                  <Plus size={18} className='mr-2 text-white' /> Edit Room
                </button>
              </div>
            )}
            {/* {activeTab === 3 && (
            <button
              className='flex items-center justify-center h-10 px-4 py-3  ml-auto text-sm text-white rounded-lg w-fit bg-blue-500'
              onClick={() => {
                setActiveTab(1);
              }}
            >
              <Plus size={18} className='mr-2 text-white' /> Add Room
            </button>
          )} */}
            {activeTab === 2 && (
              <div className='ml-auto flex gap-4 items-center'>
                <div className='relative'>
                  <button
                    type='button'
                    className='absolute text-black top-1/2 left-2.5 transform -translate-y-1/2'
                  >
                    <Search size={16} />
                  </button>
                  <input
                    type='text'
                    className='px-8 py-3 rounded-md w-80 outline-none border text-sm font-light'
                    placeholder='Search Arrivals'
                  />
                  <button
                    className='absolute text-black top-1/2 right-2.5 transform -translate-y-1/2'
                    type='button'
                  >
                    <SlidersHorizontal size={16} />
                  </button>
                </div>
                <button
                  className='bg-blue-500 text-white px-4 py-3 text-sm rounded'
                  onClick={() => {
                    setActiveTab(4);
                  }}
                >
                  Add Room
                </button>
              </div>
            )}
            {activeTab === 3 && (
              <div className='ml-auto flex gap-4 items-center'>
                <div className='relative'>
                  <button
                    type='button'
                    className='absolute text-black top-1/2 left-2.5 transform -translate-y-1/2'
                  >
                    <Search size={16} />
                  </button>
                  <input
                    type='text'
                    className='px-8 py-3 rounded-md w-80 outline-none border text-sm font-light'
                    placeholder='Search Arrivals'
                  />
                  <button
                    className='absolute text-black top-1/2 right-2.5 transform -translate-y-1/2'
                    type='button'
                  >
                    <SlidersHorizontal size={16} />
                  </button>
                </div>
                <button
                  className='bg-blue-500 text-white px-4 py-3 text-sm rounded'
                  onClick={() => {
                    setActiveTab(6);
                  }}
                >
                  Add Rate
                </button>
                <button
                  className='flex items-center justify-center px-4 py-3 text-sm text-white rounded-lg w-fit bg-primary-blue'
                  onClick={() => {
                    setActiveTab(7);
                  }}
                >
                  <Plus size={18} className='mr-2 text-white' /> Edit Rate
                </button>
              </div>
            )}
            {/* {activeTab === 3 && (
            <button
              className='flex items-center justify-center h-10 px-4 py-3  ml-auto text-sm text-white rounded-lg w-fit bg-blue-500'
              onClick={() => {
                setActiveTab(6);
              }}
            >
              <Plus size={18} className='mr-2 text-white' /> Edit Rate
            </button>
          )} */}
          </div>

          <div className='w-full'>{renderTabContent()}</div>
        </div>
      </section>
    </>
  );
};

export default Availability;
