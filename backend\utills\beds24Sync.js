import axios from 'axios';
const token = "EQVWtsMYCBgOBbSgcuc0pvGHg7ruQF41OaZhVFwHnoOZ9609kj2bLyNRoQL2wPWiKdfsmJ1igFlfE5tLqPhINgsQT+okMNaNq7PMzzxmfLqCF0fhGLgFNmrAmE0NNtmiW+svEBjMZZBwcpGZYADMc/G5+UfkKJ3XKRytCk+QUOM="
const fetchBeds24Property = async () => {
    try {
        const response = await axios.get(`https://api.beds24.com/v2/properties}`, {
            headers: {
                'Authorization': `${token}`  // Include the token in the header
            }
        });
        console.log("response.data",response.data)
        if (response.data) {

            return response.data;
        } else {
            throw new Error('No data returned from Beds24 API');
        }
    } catch (error) {
        console.error('Error fetching property from Beds24:', error.message);
        throw error;
    }
};
export default fetchBeds24Property