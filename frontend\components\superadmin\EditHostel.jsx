 
"use client";
import React, { useState } from "react";

const EditHostel = ({ onClose }) => {
  const [activeTab, setActiveTab] = useState(0);

  const handleTab = (index) => {
    setActiveTab(index);
  };
  return (
    <div className="w-full p-7 bg-sky-blue-20">
      <h2 className="text-2xl lg:font-3xl font-poppins font-bold text-black dark:text-white">
        Hostel Edit
      </h2>
      <div className="w-full h-full mt-5 bg-white rounded p-7">
        <div className="flex items-center justify-center mx-auto w-fit gap-x-5">
          <button
            type="button"
            className={`relative flex w-[165px] items-center justify-center h-10 px-5 py-2 text-sm font-semibold font-poppins border  rounded  ${activeTab === 0 ? "bg-sky-blue-650 text-white" : "border-gray-200 text-black-100"}`}
            onClick={() => handleTab(0)}
          >
            Edit Hostel Detail
          </button>
          <button
            type="button"
            className={`relative flex  w-[165px] items-center justify-center h-10 px-5 py-2 text-sm font-semibold  border font-poppins rounded  ${activeTab === 1 ? "bg-sky-blue-650 text-white" : "border-gray-200 text-black-100"}`}
            onClick={() => handleTab(1)}
          >
            Edit Room Detail
          </button>
          <button
            type="button"
            className={`relative flex  w-[165px] items-center justify-center h-10 px-5 py-2 text-sm font-semibold  border font-poppins rounded  ${activeTab === 2 ? "bg-sky-blue-650 text-white" : "border-gray-200 text-black-100"}`}
            onClick={() => handleTab(2)}
          >
            Edit Rate Detail
          </button>
        </div>
        <div className="mt-10 w-[70%] mx-auto">
          {activeTab === 0 && (
            <>
              <div className="flex flex-wrap items-center justify-between w-full gap-y-5">
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Hostel Name
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:font-poppins placeholder:text-sm"
                  />
                </div>
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Hostel Address
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:font-poppins placeholder:text-sm"
                  />
                </div>
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Rate
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:font-poppins placeholder:text-sm"
                  />
                </div>
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Flour
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:font-poppins placeholder:text-sm"
                  />
                </div>
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Room
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:font-poppins placeholder:text-sm"
                  />
                </div>
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Bed
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:font-poppins placeholder:text-sm"
                  />
                </div>
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Country
                  </label>
                  <select className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:font-poppins placeholder:text-sm">
                    <option selected disabled>
                      India
                    </option>
                  </select>
                </div>
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Offer
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:font-poppins placeholder:text-sm"
                  />
                </div>
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Status
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:font-poppins placeholder:text-sm"
                  />
                </div>
              </div>
              <div className="flex items-center justify-between mt-5">
                <button
                  type="button"
                  className={`relative w-[48%] flex  items-center justify-center h-10 px-5 py-2 text-sm font-semibold font-poppins border  rounded  border-gray-200 text-black-100`}
                  onClick={onClose}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className={`relative flex w-[48%] items-center justify-center h-10 px-5 py-2 text-sm font-semibold font-poppins border  rounded  bg-sky-blue-650 text-white `}
                >
                  Save
                </button>
              </div>
            </>
          )}
          {activeTab === 1 && (
            <>
              <div className="flex flex-wrap items-center justify-between w-full gap-y-5">
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Room Name
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:font-poppins placeholder:text-sm"
                  />
                </div>
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Room Type
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:font-poppins placeholder:text-sm"
                  />
                </div>
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Total beds
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:font-poppins placeholder:text-sm"
                  />
                </div>
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Ensuite
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9"
                  />
                </div>
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Rate
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:font-poppins placeholder:text-sm"
                  />
                </div>
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Currency
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:font-poppins placeholder:text-sm"
                  />
                </div>
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Country
                  </label>
                  <div className="relative w-full">
                    <input
                      type="file"
                      className="relative z-30 w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none opacity-0 bg-sky-blue-25 h-9"
                    />
                    <div className="absolute flex justify-start w-full top-2 text-gray-50 gap-x-4 ">
                      <div className="w-[100px] h-12 rounded-lg border border-gray-200"></div>
                      Banana Room.Jpj
                    </div>
                  </div>
                </div>
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Room Description
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:font-poppins placeholder:text-sm"
                  />
                </div>
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Rate Type
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:font-poppins placeholder:text-sm"
                  />
                </div>
              </div>
              <div className="flex items-center justify-between mt-5">
                <button
                  type="button"
                  className={`relative w-[48%] flex  items-center justify-center h-10 px-5 py-2 text-sm font-semibold font-poppins  border  rounded  border-gray-200 text-black-100`}
                  onClick={onClose}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className={`relative flex w-[48%] items-center justify-center h-10 px-5 py-2 text-sm font-semibold font-poppins border  rounded  bg-sky-blue-650 text-white `}
                >
                  Save
                </button>
              </div>
            </>
          )}
          {activeTab === 2 && (
            <>
              <div className="flex flex-wrap items-center justify-between w-full gap-y-5">
                <div className="w-[48%]">
                  <label className="text-sm  font-semibold font-poppins text-gray-500">
                    Room
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:font-poppins placeholder:text-sm"
                  />
                </div>
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Rate Type
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:font-poppins placeholder:text-sm"
                  />
                </div>
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Rate Value
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:font-poppins placeholder:text-sm"
                  />
                </div>
                <div className="w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500">
                    Percentage
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:font-poppins placeholder:text-sm"
                  />
                </div>
              </div>
              <div className="flex items-center justify-between mt-5">
                <button
                  type="button"
                  className={`relative w-[48%] flex  items-center justify-center h-10 px-5 py-2 text-sm font-semibold font-poppins border  rounded  border-gray-200 text-black-100`}
                  onClick={onClose}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className={`relative flex w-[48%] items-center justify-center h-10 px-5 py-2 text-sm font-semibold font-poppins border  rounded  bg-sky-blue-650 text-white `}
                >
                  Save
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default EditHostel;
