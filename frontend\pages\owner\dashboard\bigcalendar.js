"use client";

import React, { useState } from "react";
// import './calendar.css';

const CustomCalendarPage = () => {
  const rooms = [
    { id: 1, name: "Standard Room 01" },
    { id: 2, name: "Standard Room 02" },
    { id: 3, name: "Standard Room 03" },
    { id: 4, name: "Deluxe Room" },
  ];

  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());

  const getDaysInMonth = (year, month) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const dates = Array.from(
    { length: getDaysInMonth(currentYear, currentMonth) },
    (_, i) => i + 1
  ); // Days in the current month

  const bookings = [
    { roomId: 1, guest: "<PERSON>", start: 12, end: 13 },
    { roomId: 2, guest: "<PERSON><PERSON>", start: 12, end: 15 },
    { roomId: 3, guest: "<PERSON>", start: 14, end: 15 },
    { roomId: 4, guest: "<PERSON>", start: 14, end: 15 },
  ];

  const changeMonth = (increment) => {
    setCurrentMonth((prev) => (prev + increment + 12) % 12);
    if (increment === 1 && currentMonth === 11) {
      setCurrentYear((prev) => prev + 1); // Go to next year
    } else if (increment === -1 && currentMonth === 0) {
      setCurrentYear((prev) => prev - 1); // Go to previous year
    }
  };

  return (
    <div className="calendar-container">
      <div className="calendar-header">
        <button onClick={() => changeMonth(-1)}>Previous Month</button>
        <div>{`${currentYear}-${currentMonth + 1}`}</div>
        <button onClick={() => changeMonth(1)}>Next Month</button>
      </div>
      <div className="calendar-room-header">All Rooms</div>

      <div className="calendar-row">
        <div className="calendar-room-name">Room Name</div>
        <div className="calendar-dates">
          {dates.map((date) => (
            <div key={date} className="calendar-date">
              {date}
            </div>
          ))}
        </div>
      </div>

      {rooms.map((room) => (
        <div key={room.id} className="calendar-row">
          <div className="calendar-room-name">{room.name}</div>
          <div className="calendar-slots">
            {dates.map((date) => (
              <div key={date} className="calendar-slot">
                {bookings
                  .filter(
                    (booking) =>
                      booking.roomId === room.id &&
                      booking.start <= date &&
                      booking.end >= date
                  )
                  .map((booking) => {
                    const progressWidth =
                      ((booking.end - booking.start + 1) / dates.length) * 100; // Calculate progress width
                    const progressStart =
                      ((booking.start - 1) / dates.length) * 100; // Calculate the starting position of the progress bar
                    return (
                      <div className="progress-container" key={booking.guest}>
                        <div
                          className="booking-progress"
                          style={{
                            width: `${progressWidth}%`,
                            left: `${progressStart}%`,
                          }}
                        />
                        <div className="guest-name">{booking.guest}</div>
                      </div>
                    );
                  })}
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default CustomCalendarPage;
