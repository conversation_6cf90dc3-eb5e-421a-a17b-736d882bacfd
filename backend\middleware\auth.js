import jwt from 'jsonwebtoken';
import userModel from "../models/auth.js";
import Response from "../utills/response.js";
import mongoose from "mongoose";
import { getUserData, updateLastLogin } from '../services/auth.js'
import roleModel from "../models/roles.js";
import dotenv from 'dotenv';
import hostelLoginModel from '../models/hostelsLogin.js';
dotenv.config();


const checkAuth = (permission) => {
    return async (req, res, next) => {
        try {

            const token = req.headers.authorization?.split(' ')[1];
            if (!token) {
                return Response.Unauthorized(res, null, 'please login')
            }
            const decodedToken = jwt.verify(token, process.env.JWT_SECRET);
            
            let user = await getUserData(new mongoose.Types.ObjectId(decodedToken.userId))
            
            // If not found, try finding in hostelLoginModel
            console.log("decodedToken.userId",decodedToken.userId)
            if (!user) {
                user = await hostelLoginModel.findOne({ _id: decodedToken.userId, isDeleted: false }, { password: 0 });
            }
            if (!user) {
                return res.status(401).json({ message: 'user not found' });
            }
            // Fetch role details from the database
            const roleDetails = await roleModel.findOne({ role: user.role });
            // Check if the user has the required permission
            if(permission){
                if (!roleDetails || !roleDetails.permissions.includes(permission)) {
                    return Response.Unauthorized(res, null, 'Not Authorzied to access')
                }
            }
          //  await updateLastLogin(user)
            req.user = user;
            next();
        } catch (error) {
            console.error('Authentication and permission check error:', error);
            return res.status(401).json({ message: error.message });
        }
    };
};
const checkPremiumUser = () => {
    return async (req, res, next) => {
      try {
        const token = req.headers.authorization?.split(' ')[1];
        if (!token) {
          return Response.Unauthorized(res, null, 'Please login');
        }
  
        const decodedToken = jwt.verify(token, process.env.JWT_SECRET);
        const userId = decodedToken.userId;
  
        if (!userId) {
          return Response.Unauthorized(res, null, 'Invalid token');
        }
  
        const user = await userModel.findById(userId);
  
        if (!user) {
          return res.status(401).json({ message: 'User not found' });
        }
  
        if (!user.isPremiumUser) {
          return Response.Unauthorized(res, null, 'Premium membership required');
        }
  
        req.user = user; // Attach user object to request for further use
        next(); // Proceed to the next middleware or route handler
      } catch (error) {
        console.error('Premium user check error:', error);
        return res.status(401).json({ message: 'Unauthorized' });
      }
    };
};

const ezeeAuthMiddleware = () => {
  return async (req, res, next) => {
    try {
      const authData = req.body?.res_request?.authentication?.[0]; 
      const HotelCode = authData?.hotelcode?.[0]; 
      const AuthCode = authData?.authcode?.[0];

      if (!HotelCode || !AuthCode) {
        return res.status(401).json({ error: "Unauthorized request error.(Invalid auth code or Invalid hotelcode)" });
      }
      if ( AuthCode !== "JX92KLMN87PQWYZ45ABCD") {
        return res.status(401).json({ error: "Unauthorized request error.(Invalid auth code or Invalid hotelcode)" });
      }

      next();
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  };
};

// Utility function for error responses
const sendError = (res, code, message) => {
    const errorResponse = `
        <?xml version="1.0" encoding="UTF-8"?>
        <RES_Response>
            <Errors>
                <ErrorCode>${code}</ErrorCode>
                <ErrorMessage>${message}</ErrorMessage>
            </Errors>
        </RES_Response>
    `;
    res.status(code === 401 ? 401 : 400).set("Content-Type", "application/xml").send(errorResponse);
};

export default ezeeAuthMiddleware;


export { checkAuth,checkPremiumUser,ezeeAuthMiddleware }