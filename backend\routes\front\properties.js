import express from "express";
import { listPropertiesController } from "../../controller/hostel.js";
const router = express.Router();

// list all properties route
router.get("/properties", listPropertiesController);

export default router;

/**
 * @swagger
 * tags:
 *   name: common
 *   description: property management endpoints
 */

/**
 * @swagger
 * /properties:
 *   get:
 *     summary: List all properties with pagination and filter
 *     tags: [common]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         required: false
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 10
 *         required: false
 *         description: Number of items per page
 *       - in: query
 *         name: filter
 *         schema:
 *           type: object
 *           style: deepObject
 *           explode: true
 *         required: false
 *         description: Filter conditions
 *         style: deepObject
 *         explode: true
 *     responses:
 *       '200':
 *         description: List of properties
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 properties:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       address:
 *                         type: object
 *                         properties:
 *                           lineOne:
 *                             type: string
 *                           city:
 *                             type: string
 *                           state:
 *                             type: string
 *                           country:
 *                             type: string
 *                           zipcode:
 *                             type: number
 *                       contact:
 *                         type: number
 *                       location:
 *                         type: object
 *                         properties:
 *                           type:
 *                             type: string
 *                           coordinates:
 *                             type: array
 *                             items:
 *                               type: number
 *                       photos:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             title:
 *                               type: string
 *                             url:
 *                               type: string
 *                       licenceProof:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             title:
 *                               type: string
 *                             url:
 *                               type: string
 *                       addressProof:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             title:
 *                               type: string
 *                             url:
 *                               type: string
 *                       aboutUs:
 *                         type: string
 *                       rules:
 *                         type: object
 *                         properties:
 *                           checkIn:
 *                             type: object
 *                             properties:
 *                               start:
 *                                 type: object
 *                                 properties:
 *                                   hour:
 *                                     type: number
 *                                   min:
 *                               end:
 *                                 type: object
 *                                 properties:
 *                                   hour:
 *                                     type: number
 *                                   min:
 *                           checkOut:
 *                             type: object
 *                             properties:
 *                               start:
 *                                 type: object
 *                                 properties:
 *                                   hour:
 *                                     type: number
 *                                   min:
 *                               end:
 *                                 type: object
 *                                 properties:
 *                                   hour:
 *                                     type: number
 *                                   min:
 *                           cancellation:
 *                             type: string
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     totalProperties:
 *                       type: integer
 *             example:
 *               properties:
 *                 - type: "Apartment"
 *                   name: "Cozy Apartment"
 *                   address:
 *                     lineOne: "123 Main St"
 *                     city: "New York"
 *                     state: "NY"
 *                     country: "USA"
 *                     zipcode: 10001
 *                   contact: 1234567890
 *                   location:
 *                     type: "Point"
 *                     coordinates: [-73.935242, 40.730610]
 *                   photos:
 *                     - title: "Living Room"
 *                       url: "http://example.com/photo1.jpg"
 *                   licenceProof:
 *                     - title: "Licence"
 *                       url: "http://example.com/licence.jpg"
 *                   addressProof:
 *                     - title: "Utility Bill"
 *                       url: "http://example.com/address.jpg"
 *                   aboutUs: "A lovely place to stay"
 *                   rules:
 *                     checkIn:
 *                       start:
 *                         hour: 14
 *                         min: 0
 *                       end:
 *                         hour: 22
 *                         min: 0
 *                     checkOut:
 *                       start:
 *                         hour: 10
 *                         min: 0
 *                       end:
 *                         hour: 12
 *                         min: 0
 *                     cancellation: "24 hours notice required"
 *               pagination:
 *                 page: 1
 *                 limit: 10
 *                 totalPages: 5
 *                 totalProperties: 50
 *       '500':
 *         description: Internal server error
 */