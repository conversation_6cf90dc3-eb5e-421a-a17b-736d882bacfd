 
 
import { useState } from "react";
import toast from "react-hot-toast";
import { BASE_URL } from "@/utils/api";
import { getItemLocalStorage } from "@/utils/browserSetting";
import VerificationPending from "./verificationPending";
import FileUploadButton from "./fileUploadButton";
import Link from "next/link";
import Loader from "../loader/loader";
import {  FileText} from "lucide-react";
import Image from "next/image";


const UploadPropertyDocument = ({
  onContinue,
  data,
  updateData,
}) => {
  const name = getItemLocalStorage("name");

  // console.log("property id",propertyId,onContinue)
  // eslint-disable-next-line no-unused-vars
  const [verificationPending, setVerificationPending] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [formData, setFormData] = useState({
    propertyPhoto: [],
    propertyLicense: [],
    propertyAddress: [],
    termsAccepted: false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState({
    propertyPhoto: null,
    propertyLicense: null,
    propertyAddress: null,
  });

  // const handleFileChange = (e) => {
  //   const { name, files } = e.target;
  //   setFormData((prevData) => ({
  //     ...prevData,
  //     [name]: name === "propertyAddress" ? Array.from(files) : files[0],
  //   }));
  //   setSelectedFiles((prevFiles) => ({
  //     ...prevFiles,
  //     [name]: name === "propertyAddress" ? Array.from(files) : [files[0]],
  //   }));
  // };

  const handleFileChange = async (e) => {
    const { name, files } = e.target;
    const selectedFile = files[0];
    const allowedTypes = {
      propertyPhoto: ["image/jpeg", "image/png", "image/jpg", "image/heic"],
      propertyLicense: [
        "application/pdf",
        "image/jpeg",
        "image/png",
        "image/jpg",
      ],
      propertyAddress: [
        "application/pdf",
        "image/jpeg",
        "image/png",
        "image/jpg",
      ],
    };

    // Validate file type
    if (!allowedTypes[name].includes(selectedFile?.type)) {
      toast.error(
        `Invalid file type for ${name}. Allowed types: ${allowedTypes[
          name
        ].join(", ")}`
      );
      return;
    }

    if (selectedFile) {
      setIsLoading(true);

      try {
        const formData = new FormData();
        formData.append("files", selectedFile);

        const presignedUrlResponse = await fetch(
          `${BASE_URL}/fileUpload/generate-presigned-url`,
          {
            method: "POST",
            body: formData,
          }
        );

        if (!presignedUrlResponse.ok) {
          throw new Error("Failed to get presigned URL");
        }

        const presignedUrlData = await presignedUrlResponse.json();
        // const { objectURL } = presignedUrlData.data;
        const objectURL = Array.isArray(presignedUrlData.data) && presignedUrlData.data[0]?.path;

        setFormData((prevData) => ({
          ...prevData,
          [name]: [...prevData[name], objectURL], // Append the new file to the array
        }));

        updateData({
          ...data,
          [name]: [...data[name], objectURL], // Append the new file to the array
        });

        setSelectedFiles((prevFiles) => ({
          ...prevFiles,
          [name]: [selectedFile],
        }));

        toast.success(`${name} uploaded successfully!`);
      } catch (error) {
        console.error(`Error uploading ${name}:`, error);
        toast.error(`Error uploading ${name}`);
      } finally {
        setIsLoading(false);
      }
    } else {
      toast.error("File is required");
    }
  };

  const handleCheckboxChange = (e) => {
    const { name, checked } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: checked,
    }));
    updateData({
      ...data,
      [name]: checked,
    });
  };



  const handleContinue = async () => {
    if (
      !data.propertyPhoto.length ||
      !data.propertyLicense.length ||
      !data.propertyAddress.length
    ) {
      toast.error("Please upload at least one file for all document types.");
      return;
    }
    if (!data.termsAccepted) {
      toast.error("Please accept the terms and conditions");
      return;
    }
    onContinue(data);

    // setIsLoading(true);

    // try {
    //   const token = getToken();
    //   const response = await axios.put(apiUrl, data, {
    //     headers: {
    //       "Content-Type": "application/json",
    //       Authorization: `Bearer ${token}`, // Add token here
    //     },
    //   });

    //   console.log("Documents uploaded successfully:", response.data);
    //   setVerificationPending(true);
    //   onContinue(propertyId, formData);
    // } catch (error) {
    //   console.error("Error uploading documents:", error);

    //   if (error.response) {
    //     console.error("Response data:", error.response.data);
    //     alert(`Error: ${error.response.data.message}`);
    //   }
    // } finally {
    //   setIsLoading(false);
    // }
  };

  const handleVerify = () => {
    onContinue(); // Move to the next step
  };

  const uploadSections = [
    {
      label: "Property Photo",
      name: "propertyPhoto",
      buttonText: "Upload property photo",
      onChange: handleFileChange,
      multiple: false,
      note: "Allowed types: jpg, jpeg, png, heic",
      accept: ".jpg,.jpeg,.png,.heic",
    },
    {
      label: "Property License",
      name: "propertyLicense",
      buttonText: "Upload property license",
      onChange: handleFileChange,
      multiple: false,
      note: "Allowed types: pdf, jpg",
      accept: ".pdf,.jpg",
    },
    {
      label: "Property Address",
      name: "propertyAddress",
      buttonText: "Upload Bill, Gas, Electricity, Tax document, etc.",
      onChange: handleFileChange,
      multiple: true,
      note: "Allowed types: pdf, jpg",
      accept: ".pdf,.jpg",
    },
  ];

  const handleDeleteFile = (file, fieldName) => {
    setFormData((prevData) => ({
      ...prevData,
      [fieldName]: prevData[fieldName].filter((item) => item !== file),
    }));

    updateData({
      ...data,
      [fieldName]: data[fieldName].filter((item) => item !== file),
    });

    toast.success(`${file} deleted successfully!`);
  };

  console.log("data4632", data, selectedFiles);

  return (
    <>
      <Loader open={isLoading} />
      {!verificationPending ? (
        <div className='min-h-screen flex flex-col items-center bg-[#F7F7F7]'>
          <div className='bg-white rounded-3xl shadow-md w-full max-w-3xl p-6 md:p-14 mb-10 py-4'>
            <h2 className='text-2xl font-bold mb-8'>
              👋 Hi,<span className='text-[#40E0D0]'> {name}</span> Upload
              Property Document..
            </h2>
            <form>
              {uploadSections.map((section, index) => (
                <div className='mb-6' key={index}>
                  <FileUploadButton
                    label={section.label}
                    name={section.name}
                    onChange={section.onChange}
                    buttonText={section.buttonText}
                    multiple={section.multiple}
                    selectedFiles={selectedFiles[section.name]}
                    accept={section.accept}
                  />
               <div>
               <label className=' text-sm py-5 font-bold text-red-500 my-5'>
                    {section.note}
                  </label>
               </div>
                  {data[section.name]?.map((file, fileIndex) => {
                    // Check the file extension
                    const isImage = /\.(jpg|jpeg|png|gif|bmp)$/i.test(file);
                    const isPDF = /\.pdf$/i.test(file);
                    const isDoc = /\.(doc|docx|odt|txt)$/i.test(file);

                    console.log("file", file, isImage, isPDF, isDoc);

                    return (
                      <div
                        key={fileIndex}
                        className='mt-4 relative inline-block mx-2' style={{border:"1px solid #ddd", width:"50px", height:"50px"}}
                      >
                        {isImage ? (
                          <Image 
                            // src={file}
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/${file}`}
                            alt='Thumbnail'
                            style={{
                              maxWidth: "100%",
                              maxHeight: "100%",
                            }}
                            className="w-full h-full object-cover"
                            width={50}
                            height={50}
                          />
                        ) : (
                          <div
                            style={{
                              width: "50px",
                              height: "50px",
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "center",
                              backgroundColor: "#f2f2f2",
                              border: "1px solid #ddd",
                              borderRadius: "4px",
                            }}
                          >
                            {isPDF ? (
                              <>
                              <i
                                className='fas fa-file-pdf text-red-500'
                                style={{ fontSize: "50px" }}
                              ></i>
                               <FileText size={40} color="#E53E3E" />
                               </>
                            ) : isDoc ? (
                              <>
                              <i
                                className='fas fa-file-word text-blue-500'
                                style={{ fontSize: "50px" }}
                              ></i>
                              <FileText size={40} color="#E53E3E" />
                              </>
                            ) : (
                              <>
                              <i
                                className='fas fa-file text-gray-500'
                                style={{ fontSize: "50px" }}
                              ></i>
                              <FileText size={40} color="#E53E3E" />
                              </>
                            )}
                          </div>
                        )}
                        <button
                          type='button'
                          onClick={() => handleDeleteFile(file, section.name)}
                          className='absolute -top-2 -right-2 bg-red-500 text-white rounded-full flex justify-center items-center ' style={{width:"20px", height:"20px"}}
                        >
                          <span className='text-xs'>X</span>
                        </button>
                      </div>
                    );
                  })}
                </div>
              ))}

              <div className='flex items-start mb-6'>
                <div className='flex items-center h-5'>
                  <input
                    id='termsAccepted'
                    name='termsAccepted'
                    type='checkbox'
                    checked={data.termsAccepted}
                    onChange={handleCheckboxChange}
                    className='w-4 h-4 text-primary-blue border-gray-300 rounded-4xl focus:ring-[#40E0D0]'
                  />
                </div>

                <div className='ml-3 text-sm'>
                  <label htmlFor='termsAccepted' className='font-normal font-manrope text-[#6D6D6D]'>
                    I have read and agreed to all{" "}
                    <Link
                      href='/terms-condition'
                      className='text-primary-blue font-normal uppercase underline underline-offset-2'
                      target='_blank'
                      prefetch={false}
                    >
                      TERMS AND CONDITIONS
                    </Link>
                  </label>
                </div>
              </div>
              <button
                type='button'
                onClick={handleContinue}
                className={`w-full bg-[#e5e7eb] my-8 text-black font-semibold py-4 rounded-4xl transition duration-200 hover:bg-primary-blue ${
                  isLoading ? "opacity-50 cursor-not-allowed" : ""
                }`}
                disabled={isLoading}
              >
                {isLoading ? "Submitting..." : "Continue"}
              </button>
            </form>
          </div>
        </div>
      ) : (
        <VerificationPending onVerify={handleVerify} />
      )}
    </>
  );
};

export default UploadPropertyDocument;
