// pages/mixdorm-cloudbeds.tsx
import Image from "next/image";

export default function FaqsOnwer() {
  return (
    <div>
      <div className="min-h-screen bg-gray-100 flex justify-center items-start py-12 px-4 mb-2 md:mb-0">
        <div className="container bg-white  w-full rounded-xl shadow p-8 max-w-6xl">
          <div className="flex items-center justify-center w-full max-w-xs bg-white mb-4 ">
            {/* <div className=" flex items-center justify-between overflow-hidden rounded-lg ">
            
              <div className="flex items-center justify-center">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/cloudbedsfaqs.png`}
                  alt="Cloudbeds"
                  width={160}
                  height={100}
                  className="rounded object-contain"
                />
              </div>

              <div className=" " />
              <div className=" text-gray-600 text-4xl font-bold mb-1.5 mx-3">x</div>

              <div className=" bg-white flex items-center justify-center">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/MixdormBMD.png`}
                  alt="Mixdrom"
                  width={130}
                  height={400}
                  className="rounded object-contain"
                />
              </div>
            </div> */}
            <div className="relative w-80 h-52  mx-5 hidden sm:block">
              {/* Left Box with Image */}
              <div className="absolute left-0 top-10 w-[135px] h-[135px] rotate-45 bg-white shadow-xl z-10 flex items-center justify-center overflow-hidden border-2">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/CBlogofaqs.png`}
                  alt="Cloudbeds"
                  height={100}
                  width={100}
                  className="object-cover -rotate-45 "
                />
              </div>

              {/* Right Box with Image */}
              <div className="absolute right-0.5  top-10 w-[135px] h-[135px] rotate-45 bg-white shadow-xl z-10 flex items-center justify-center overflow-hidden border-2">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mixdormfaqs.png`}
                  alt="Mixdorm"
                  height={80}
                  width={100}
                  className="object-cover -rotate-45 p-2 sm:p-2 md:p-0"
                />
              </div>

              {/* Overlapping Center Box with X */}
              <div className="absolute left-[137px] top-[106px] w-8 h-8 -translate-x-1/2 -translate-y-1/2 rotate-45 bg-white z-20 shadow-md flex items-center justify-center border">
                <span className="-rotate-45 text-2xl font-bold text-gray-800">
                  x
                </span>
              </div>
            </div>
            <div className="relative w-64 h-44 mx-auto sm:w-72 sm:h-48 md:w-80 md:h-52 block sm:hidden">
              {/* Left Box with Image */}
              <div className="absolute left-0 top-6 w-[110px] h-[110px] sm:w-[125px] sm:h-[125px] md:w-[135px] md:h-[135px] rotate-45 bg-white shadow-xl z-10 flex items-center justify-center overflow-hidden border-2">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/CBlogofaqs.png`}
                  alt="Cloudbeds"
                  height={100}
                  width={100}
                  className="object-cover -rotate-45 p-2"
                />
              </div>

              {/* Right Box with Image */}
              <div className="absolute right-6 top-6 w-[110px] h-[110px] sm:w-[125px] sm:h-[125px] md:w-[135px] md:h-[135px] rotate-45 bg-white shadow-xl z-10 flex items-center justify-center overflow-hidden border-2">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mixdormfaqs.png`}
                  alt="Mixdorm"
                  height={80}
                  width={100}
                  className="object-cover -rotate-45 p-3"
                />
              </div>

              {/* Overlapping Center Box with X */}
              <div className="absolute left-[118px] top-20 w-6  h-6 -translate-x-1/2 -translate-y-1/2 rotate-45 bg-white z-20 shadow-md flex items-center justify-center border">
                <span className="-rotate-45 text-lg sm:text-xl md:text-2xl font-bold text-gray-800">
                  x
                </span>
              </div>
            </div>
          </div>

          <h1 className="text-2xl md:text-3xl font-bold text-black mb-4 font-poppins">
            Connecting Mixdorm to Cloudbeds
          </h1>
          <p className="text-gray-700 mb-2 text-lg lg:text-xl">
            This article shows you how to connect your Cloudbeds account to
            <span className="text-primary-blue font-semibold"> Mixdorm</span> so
            that <strong className="">availability, rates</strong> and{" "}
            <strong>reservations</strong> are synced in real-time.
          </p>
          <p className="text-gray-700 my-6 text-lg lg:text-xl">
            The next 8 steps take a few minutes to implement.
          </p>

          <hr className="border-gray-300 mb-6" />

          <ol className="list-decimal space-y-6 pl-5 text-gray-800 text-lg font-manrope">
            <li>
              To connect your Cloudbeds account to Mixdorm, you must first have
              a Mixdorm Host Account and at least a draft Property Listing.
              Contact Partner Support at{" "}
              <a
                href="mailto:<EMAIL>"
                className="text-primary-blue font-bold font-manrope"
              >
                <EMAIL>
              </a>{" "}
              if you need assistance.
            </li>
            <li>
              Once you have an account and listing with Mixdorm, login to your{" "}
              <a
                href="https://www.cloudbeds.com"
                className="text-primary-blue font-bold font-manrope"
                target="_blank"
                rel="noopener noreferrer"
              >
                Cloudbeds
              </a>{" "}
              account and go to the <strong>Channels</strong> page.
            </li>
            <li>
              Click on the <strong>Add Channel</strong> button.
              <div className="mt-4">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/faqsowner1.jpeg`}
                  alt="Add Channel"
                  width={700}
                  height={400}
                  className="rounded border shadow-sm"
                />
              </div>
            </li>

            {/* Step 4 with image */}
            <li>
              Find <strong className="text-primary-blue">Mixdorm</strong> in the
              list of channels and click on the <strong>Not Setup</strong>{" "}
              button.
              <div className="mt-4">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/faqsowner2.jpeg`}
                  alt="Channel List"
                  width={700}
                  height={400}
                  className="rounded border shadow-sm"
                />
              </div>
            </li>

            {/* Step 5 */}
            <li>
              Enter the <strong>Hotel ID</strong> and <strong>Password</strong>{" "}
              that you received in Step 1 from the Mixdorm Partner Support team.
              Click the <strong>Next</strong> button.
              <div className="mt-4">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/faqsowner3.jpeg`}
                  alt="Channel List"
                  width={700}
                  height={400}
                  className="rounded border shadow-sm"
                />
              </div>
            </li>

            {/* Step 6 */}
            <li>
              Map your Room Types from Cloudbeds to your Site Types in Mixdorm
              by selecting the matching site type from each of the dropdown
              fields for the room type listed next to it. Once they are all
              mapped, click the <strong>Next</strong> button.
              <div className="mt-4">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/faqsowner4.jpeg`}
                  alt="Channel List"
                  width={700}
                  height={400}
                  className="rounded border shadow-sm"
                />
              </div>
            </li>
            {/* Step 7 */}
            <li>
              Mixdorm requires your rates to be the same (or lower) as they are
              on your own website or other booking channels, so leave these
              values as <strong>0.00</strong> and click the{" "}
              <strong>Next</strong> button.
              <div className="mt-4">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/faqsowner5.jpeg`}
                  alt="Channel List"
                  width={700}
                  height={400}
                  className="rounded border shadow-sm"
                />
              </div>
            </li>
            {/* Step 8 */}
            <li>
              You&apos;re done! Leave the checkbox checked and click the
              <strong> Back to Overview</strong> button.
              <div className="mt-4">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/faqsowner6.jpeg`}
                  alt="Channel List"
                  width={700}
                  height={400}
                  className="rounded border shadow-sm"
                />
              </div>
            </li>
          </ol>
        </div>
      </div>
    </div>
  );
}
