/* eslint-disable react/no-unescaped-entities */
import React from "react";
import dynamic from "next/dynamic";
import Head from "next/head";
import { getContentSchemaOtherPages } from "@/lib/schema/contentSchemaOtherPages";

const HeroNavbar = dynamic(() => import("@/components/footer/heroNavBar"), {
  ssr: false,
});

const AboutUs = () => {
  const contentSchema = getContentSchemaOtherPages({
    title: "About Mixdorm - Top Hostel Booking Worldwide",
    description:
      "Mixdorm is your Go-to Platform for Hostel Booking, Offering Dormitories, Cheap Hostels, and Backpackers Booking. Find and Book the Best Hostels Worldwide!",
    authorName: "Mixdorm",
    datePublished: "2023-07-17",
  });
  return (
    <>
      <Head>
        <title>About Mixdorm - Top Hostel Booking Worldwide</title>
        <meta
          name='description'
          content='Mixdorm is your Go-to Platform for Hostel Booking, Offering Dormitories, Cheap Hostels, and Backpackers Booking. Find and Book the Best Hostels Worldwide!'
        />
        {contentSchema && (
          <script
            type='application/ld+json'
            dangerouslySetInnerHTML={{ __html: JSON.stringify(contentSchema) }}
          />
        )}
      </Head>
      <HeroNavbar />
      <div className='bg-white pb-4 md:pb-[10rem] font-manrope md:pt-12 pt-8'>
        <div className='container md:px-4 lg:px-0 xl:px-0 md:pr-8 lg:pr-10 xl:pr-20'>
          <h2 className='font-bold  text-black font-manrope md:mb-8 mb-5 md:text-[35px] sm:text-xl text-xl'>
            About Us
          </h2>
          <h4 className='font-bold md:text-xl text-lg text-black'>
            Welcome to MixDorm
          </h4>
          <p className='text-sm text-gray-500 my-4'>
            At MixDorm, we believe that travel is more than just a journey; it's
            an experience that connects people, cultures, and stories. Founded
            with a vision to revolutionize the hostel experience, MixDorm is not
            just a place to stay but a community where travelers from around the
            world come together to create unforgettable memories.
          </p>
          <h4 className='font-bold md:text-xl text-lg text-black'>Our Story</h4>
          <p className='text-sm text-gray-500 my-4'>
            MixDorm was established in 2024 by a group of passionate travelers
            who recognized the need for affordable, yet high-quality
            accommodations for young explorers. Frustrated with the lack of
            community-focused hostels, we set out to create a space that
            embodies the spirit of adventure, inclusivity, and connection.
          </p>
          <h4 className='font-bold md:text-xl text-lg text-black'>
            Our Mission
          </h4>
          <p className='text-sm text-gray-500 my-4'>
            Our mission is to provide exceptional hostel experiences that foster
            connections and cultural exchanges among travelers. We aim to offer
            a safe, comfortable, and vibrant environment where our guests can
            relax, explore, and create lasting friendships.
          </p>

          <h4 className='font-bold md:text-xl text-lg'>Our Values</h4>

          <div className='my-4'>
            <p className='text-sm text-gray-500'>
              <strong className='text-black'>Community:</strong> We believe in
              the power of community and strive to create spaces where travelers
              can connect and share their stories.
            </p>
            <p className='text-sm text-gray-500'>
              <strong className='text-black'>Quality:</strong> We are committed
              to maintaining high standards of cleanliness, safety, and comfort
              across all our properties.
            </p>
            <p className='text-sm text-gray-500'>
              <strong className='text-black'>Innovation:</strong> We continually
              seek to innovate and enhance the travel experience through
              technology and creative solutions.
            </p>
            <p className='text-sm text-gray-500'>
              <strong className='text-black'>Sustainability:</strong> We are
              dedicated to promoting sustainable practices and contributing
              positively to the communities we operate in.
            </p>
          </div>

          <h4 className='font-bold md:text-xl text-lg my-4'>What We Offer</h4>
          <h4 className='font-bold md:text-xl text-lg'>
            1. Modern Accommodations
          </h4>
          <p className='text-sm text-gray-500 my-4'>
            Our hostels are designed with modern amenities to ensure a
            comfortable stay. From cozy dorms to private rooms, we cater to the
            diverse needs of our guests.
          </p>
          <h4 className='font-bold md:text-xl text-lg'>2. Unique Features</h4>

          <div className='my-4'>
            <p className='text-sm text-gray-500 '>
              <strong className='text-black'>Mix Ride:</strong> Share rides with
              fellow travelers and make your journey more fun and economical.
            </p>
            <p className='text-sm text-gray-500'>
              <strong className='text-black'>Mix Creators:</strong> A platform
              for social media influencers to promote hostel properties and
              engage with our community.
            </p>
            <p className='text-sm text-gray-500'>
              <strong className='text-black'>Noticeboard Dashboard:</strong>{" "}
              Stay updated with live announcements, events, and AI-driven
              property suggestions tailored to your preferences.
            </p>
          </div>

          <h4 className='font-bold md:text-xl text-lg'>
            3. Vibrant Community Spaces
          </h4>
          <p className='text-sm text-gray-500 my-4'>
            Our common areas are designed to encourage social interaction and
            cultural exchanges. Whether it’s a game night, a movie marathon, or
            a local tour, there’s always something happening at MixDorm.
          </p>
          <h4 className='font-bold md:text-xl text-lg'>4. Local Experiences</h4>
          <p className='text-sm text-gray-500 my-4'>
            We offer curated local experiences that allow our guests to explore
            the culture, cuisine, and attractions of their destination.
          </p>

          <h4 className='font-bold md:text-xl text-lg'>
            Join the MixDorm Community!
          </h4>
          <p className='text-sm text-gray-500 my-4'>
            With over 500,000 travelers in our community, MixDorm is more than
            just a place to stay. It's a network of like-minded adventurers who
            share a passion for travel and discovery. Whether you’re a solo
            traveler, a group of friends, or a digital nomad, you’ll find a
            welcoming home at MixDorm.
          </p>

          <h4 className='font-bold md:text-xl text-lg'>Contact Us</h4>
          <p className='text-sm text-gray-500 my-4'>
            We’d love to hear from you! For any inquiries or to share your
            travel stories, please reach out to us: MixDorm Email:
            <EMAIL>
          </p>

          <h4 className='font-bold md:text-xl text-lg'>Follow Us</h4>
          <p className='text-sm text-gray-500 my-4'>
            Stay connected with us on social media to get the latest updates,
            travel tips, and special offers:
          </p>

          <div className='my-4'>
            <p className='text-sm '>
              <strong>Instagram:</strong> @mixdorms
            </p>
            <p className='text-sm '>
              <strong>Facebook:</strong> MixDorm
            </p>
            <p className='text-sm '>
              <strong>Twitter:</strong> @mixdorm
            </p>
          </div>

          <p className='text-sm text-gray-500 my-4'>
            Thank you for choosing MixDorm. We look forward to being a part of
            your travel adventures
          </p>
        </div>
      </div>
    </>
  );
};

export default AboutUs;
