import React, { useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation } from "swiper/modules";
import HostelCard from "./hostelCard";
import "swiper/css/navigation";
import countries from "world-countries";
// import { motion } from "framer-motion";

const HostelCardSlider = ({ featuredHostelData, setIsUpdateData, setLoading }) => {

  const [currencyData, setCurrencyData] = useState({});

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);

      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);

  const getCurrencySymbol = (currencyCode) => {
    return currencyData[currencyCode] || currencyCode;
  };

  return (
    <>
      {/* <motion.div
        initial={{ y: 80, opacity: 0 }}
        whileInView={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: false }}
      > */}
      <div>
        <Swiper
          modules={[Autoplay, Navigation]}
          // autoplay={{ delay: 1500, pauseOnMouseEnter: true }}
          slidesPerView={18}
          navigation={{
            prevEl: '.slider-button-prev',
            nextEl: '.slider-button-next',
          }}
          loop
          speed={1000}
          spaceBetween={14}
          className="mySwiper myCustomSwiper mt-0 xs:pt-5 pt-0 discover-event-slider-home overflow-hidden"
          breakpoints={{
            0: {
              slidesPerView: 1.4,
              spaceBetween: 10,
            },
            480: {
              slidesPerView: 1.4,
              spaceBetween: 10,
            },
            640: {
              slidesPerView: 2,
              spaceBetween: 10,
            },

            768: {
              slidesPerView: 2.5,
              spaceBetween: 20,
            },

            1024: { slidesPerView: 3.5 },
            1280: { slidesPerView: 4 },


          }}
        >
          {featuredHostelData.length > 0 ? (
            featuredHostelData.map((item) => (
              <SwiperSlide key={`${item._id}-${Boolean(item.liked)}`} className="h-full">
                <HostelCard
                  tag="Top Rated"
                  title={`${item?.name},${item?.address?.country}`}
                  image={item?.images?.[0]?.objectUrl}
                  imageWidth="300px"
                  imageHeight="200px"
                  guest="4-6 guest"
                  time="2 days 3 nights"
                  feature={item?.freeFacilities}
                  price={`${getCurrencySymbol(
                    item?.lowestAveragePricePerNight?.currency
                  )} ${item?.lowestAveragePricePerNight?.value}`}
                  rating="4.9"
                  review="672"
                  hostelId={item?._id}
                  setIsUpdateData={setIsUpdateData}
                  setLoading={setLoading}
                  liked={Boolean(item?.liked)}
                />
              </SwiperSlide>
            ))
          ) : (
            <SwiperSlide>No</SwiperSlide>
          )}


          {/* <SwiperSlide></SwiperSlide> */}
        </Swiper>
      </div>
      {/* </motion.div> */}
    </>
  );
};

export default HostelCardSlider;
