import contactUsModel from '../models/contactUs.js';

const addQuery = async (data) => {
    try{
        return await contactUsModel.create(data);
    } catch(error){
        return { success: false, message: 'Error saving contact-us data', error };
    }
}

// Function to get a query by ID
const getQueryById = async (queryId) => {
    try {
        const query = await contactUsModel.findById({_id : queryId});
        if (!query) {
            throw new Error('Query Not Found');
        }
        return query;
    } catch (error) {
        console.error("Error getting query:", error.message);
        throw error;
    }
};

// Function to list all query
const listAllQueries = async (filter = {}, page, limit) => {
    const skip = (page - 1) * limit;

    const queries = await contactUsModel.find()
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec();

    const totalQueries = await contactUsModel.countDocuments();

    return { queries, totalQueries};
};

export { addQuery, getQueryById, listAllQueries };