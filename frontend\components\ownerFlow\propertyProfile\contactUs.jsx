/* eslint-disable react/no-unescaped-entities */
import React, { useCallback, useEffect, useState, Fragment } from "react";
import Image from "next/image";
import { GoogleMap, Marker, InfoWindow } from "@react-google-maps/api";
import PhoneInput, { isValidPhoneNumber } from "react-phone-number-input";
import "react-phone-number-input/style.css";
import { Autocomplete } from "@react-google-maps/api";
import { Country, State, City } from "country-state-city";
import Select from "react-select";
import toast from "react-hot-toast";
import { editPropertyApi } from "@/services/ownerflowServices";
import Loader from "@/components/loader/loader";
import { Dialog, Transition } from "@headlessui/react";

const GOOGLE_MAPS_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

const initialFormData = {
  address: {
    lineOne: "",
    lineTwo: "",
    city: "",
    state: "",
    country: "",
    countryName: "",
    zipcode: "",
    latitude: "",
    longitude: "",
    stateName: "",
  },

  phoneNumber: "",
  whatsApp: "",
};

const ContactUs = ({ id, data, updatePropertyData, loading }) => {
  console.log("data", data);
  const [isApiLoaded, setIsApiLoaded] = useState(false);
  const [selectedMarker, setSelectedMarker] = useState(null);
  const [editContact, setEditContact] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState("");
  const [whatsAppNumber, setWhatsAppPhoneNumber] = useState("");
  const [formData, setFormData] = useState(initialFormData);
  const [stateOptions, setStateOptions] = useState([]);
  const [cityOptions, setCityOptions] = useState([]);
  const [autocomplete, setAutocomplete] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const [openAddcontact, setOpenAddcontact] = useState(false);
  const closecontactModal = () => setOpenAddcontact(false);
  const [openEditcontact, setopenEditcontact] = useState(false);
  const closecontacteditModal = () => setopenEditcontact(false);

  const {
    address = {},
    location = { coordinates: [0, 0] },
    contactUs = {},
  } = data || {};

  // eslint-disable-next-line no-unused-vars
  const { country, city, lineOne, state } = address;
  // const [lng, lat] = location.coordinates;

  const lat =
    Number(data?.contactUs?.contactAddress?.latitude) ||
    location.coordinates[1];
  const lng =
    Number(data?.contactUs?.contactAddress?.longitude) ||
    location.coordinates[0];

  const loadGoogleMapsApi = () => {
    const script = document.createElement("script");
    script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&libraries=places`;
    script.async = true;
    script.onload = () => setIsApiLoaded(true);
    document.head.appendChild(script);
  };

  useEffect(() => {
    if (window.google && window.google.maps) {
      setIsApiLoaded(true);
    } else {
      loadGoogleMapsApi();
    }
  }, []);

  const mapContainerStyle = {
    width: "100%",
    height: "100%",
  };

  const getCountryCodeFromName = (countryName) => {
    const country = Country.getAllCountries().find(
      (c) => c.name === countryName
    );
    return country ? country.isoCode : "";
  };

  const getStateCodeFromName = (stateName, countryCode) => {
    const state = State.getStatesOfCountry(countryCode).find(
      (s) => s.name === stateName
    );
    return state ? state.isoCode : "";
  };

  useEffect(() => {
    if (data) {
      const { address, contactUs, location } = data;
      const countryCode = getCountryCodeFromName(
        contactUs?.contactAddress?.country || address?.country
      );
      const stateCode = getStateCodeFromName(
        contactUs?.contactAddress?.state || address?.state,
        countryCode
      );
      setFormData({
        address: {
          lineOne: contactUs?.contactAddress?.lineOne || address?.lineOne,
          lineTwo: contactUs?.contactAddress?.lineTwo || address?.lineTwo,
          city: contactUs?.contactAddress?.city || address?.city,
          state: stateCode,
          stateName: contactUs?.contactAddress?.stateName || address?.state,
          country: countryCode,
          countryName: contactUs?.contactAddress?.country || address?.country,
          zipcode: contactUs?.contactAddress?.zipcode || address?.zipcode || "",
          latitude:
            contactUs?.contactAddress?.latitude || location?.coordinates?.[0],
          longitude:
            contactUs?.contactAddress?.longitude || location?.coordinates?.[1],
        },

        phoneNumber: contactUs?.phoneNumber || "",
        whatsAppNumber: contactUs?.whatsApp || "",
      });

      setPhoneNumber(contactUs?.phoneNumber || "");
      setWhatsAppPhoneNumber(contactUs?.whatsApp || "");

      const states = State.getStatesOfCountry(countryCode);
      setStateOptions(states.map((s) => ({ label: s.name, value: s.isoCode })));

      if (address.state) {
        const cities = City.getCitiesOfState(countryCode, stateCode);
        setCityOptions(cities.map((c) => ({ label: c.name, value: c.name })));
      }
    }
  }, [data, openAddcontact, openEditcontact]);


    useEffect(() => {
    if (window.google) {
      setIsApiLoaded(true);
    } else {
      const checkApi = setInterval(() => {
        if (window.google) {
          setIsApiLoaded(true);
          clearInterval(checkApi);
        }
      }, 100);
      return () => clearInterval(checkApi);
    }
  }, []);


  const handleCountryChange = (selectedOption) => {
    const countryCode = selectedOption.value;
    const countryName = Country.getCountryByCode(countryCode)?.name || "";

    setFormData((prevData) => ({
      ...prevData,
      address: {
        ...prevData.address,
        country: countryCode,
        countryName,
        state: "",
        stateName: "",
        city: "",
      },
    }));

    const states = State.getStatesOfCountry(countryCode);
    setStateOptions(states.map((s) => ({ label: s.name, value: s.isoCode })));
    setCityOptions([]);
  };

  const handleStateChange = (selectedOption) => {
    const stateCode = selectedOption.value;
    const countryCode = formData?.address?.country;
    const stateName =
      State.getStateByCodeAndCountry(stateCode, countryCode)?.name || "";

    setFormData((prevData) => ({
      ...prevData,
      address: {
        ...prevData.address,
        state: stateCode,
        stateName,
        city: "",
      },
    }));

    const cities = City.getCitiesOfState(countryCode, stateCode);
    setCityOptions(cities.map((c) => ({ label: c.name, value: c.name })));
  };

  const handleAddressChange = useCallback((place) => {
    if (!place || !place.address_components) return;

    const addressComponents = place.address_components;

    let country = "";
    let state = "";
    let stateName = "";
    let city = "";
    let postCode = "";

    addressComponents.forEach((component) => {
      const types = component.types;

      if (types.includes("country")) {
        country = component.short_name;
      }
      if (types.includes("administrative_area_level_1")) {
        state = component.short_name;
        stateName = component.long_name;
      }
      if (
        types.includes("locality") ||
        types.includes("administrative_area_level_2") ||
        types.includes("administrative_area_level_3")
      ) {
        city = component.long_name;
      }
      if (types.includes("postal_code")) {
        postCode = component.long_name;
      }
    });

    setFormData((prevData) => ({
      ...prevData,
      address: {
        ...prevData.address,
        lineOne: place.formatted_address,
        latitude: place.geometry.location.lat(),
        longitude: place.geometry.location.lng(),
        country,
        state,
        stateName,
        city,
        zipcode: postCode,
      },
    }));

    const countryName = Country.getCountryByCode(country)?.name || "";
    setFormData((prevData) => ({
      ...prevData,
      address: {
        ...prevData.address,
        country,
        countryName,
      },
    }));

    if (country) {
      const states = State.getStatesOfCountry(country);
      setStateOptions(states.map((s) => ({ label: s.name, value: s.isoCode })));
    }

    if (country && state) {
      const cities = City.getCitiesOfState(country, state);
      setCityOptions(cities.map((c) => ({ label: c.name, value: c.name })));
    }
  }, []);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      address: {
        ...prevData.address,
        [name]: type === "checkbox" ? checked : value,
      },
    }));
  };

  console.log("formData", formData);

  const validateForm = (data) => {
    return (
      // phoneNumber &&
      whatsAppNumber &&
      // isValidPhoneNumber(phoneNumber) &&
      isValidPhoneNumber(whatsAppNumber) &&
      data.lineOne
      // data.zipcode &&
      // data.country &&
      // data.state &&
      // data.city
    );
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm(formData?.address)) {
      toast.error(
        "All fields are required and phone/whatsApp number must be valid."
      );
      return;
    }

    setIsLoading(true);
    try {
      const payload = {
        contactUs: {
          contactAddress: formData?.address,
          phoneNumber,
          whatsApp: whatsAppNumber,
        },
      };

      if (id) {
        const response = await editPropertyApi(id, payload);
        if (response?.data?.status) {
          toast.success(
            response?.data?.message || "Property updated successfully!"
          );
          setEditContact(false);
          updatePropertyData();
          setFormData({
            address: {
              lineOne: "",
              lineTwo: "",
              city: "",
              state: "",
              country: "",
              countryName: "",
              zipcode: "",
              latitude: "",
              longitude: "",
              stateName: "",
            },
            phoneNumber: "",
            whatsApp: "",
          });
          setPhoneNumber("");
          setWhatsAppPhoneNumber("");
          closecontactModal();
          closecontacteditModal();
        }
      } else {
        toast.error("No property data available to update.");
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Failed to submit property details.");
    } finally {
      setIsLoading(false);
    }
  };

  if (!isApiLoaded) {
    return <Loader open={true} />;
  }

  // const fullAddress = [
  //   data?.contactUs?.contactAddress?.lineOne || lineOne,
  //   data?.contactUs?.contactAddress?.city || city,
  //   data?.contactUs?.contactAddress?.state || state,
  //   data?.contactUs?.contactAddress?.country || country,
  // ]
  //   .filter(Boolean)
  //   .join(", ");



if (!isApiLoaded || isLoading || loading) {
    return (
      <div>
        {/* Header Skeleton */}
        <div className="flex justify-between items-center mb-5">
          <div className="animate-pulse bg-gray-200 h-6 w-32 rounded"></div>
          <div className="flex gap-2">
            <div className="animate-pulse bg-gray-200 h-10 w-32 rounded-lg"></div>
          </div>
        </div>

        {/* Content Skeleton */}
        <div className="flex flex-wrap">
          <div className="w-full mb-4 lg:w-1/3 lg:mb-0">
            <div className="p-4 mb-4 border border-gray-200 rounded-lg shadow-md min-h-32 animate-pulse">
              <div className="flex items-center mb-3">
                <div className="bg-gray-200 w-5 h-5 rounded-full mr-2"></div>
                <div className="bg-gray-200 h-4 w-24 rounded"></div>
              </div>
              <div className="space-y-2">
                <div className="bg-gray-200 h-3 w-full rounded"></div>
                <div className="bg-gray-200 h-3 w-3/4 rounded"></div>
                <div className="bg-gray-200 h-3 w-2/3 rounded"></div>
              </div>
            </div>
          </div>
          <div className="w-full lg:w-[45%] px-4 animate-pulse">
            <div className="bg-gray-200 rounded-xl h-[275px] w-full"></div>
          </div>
        </div>
      </div>
     );
  }

  return (
    <>
      <Loader open={isLoading} />
   
        
          <div>
            <div className="flex justify-between items-center mb-5">
              <h2 className="sm:text-xl text-base font-medium">Contact Us</h2>
              <div className="flex sm:gap-2 gap-1">
                <button
                  // onClick={() => setEditContact(true)}
                  onClick={() => {
                    setopenEditcontact(true);
                  }}
                  className="sm:flex block items-center text-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-primary-blue font-medium"
                >
                  Edit Contact Us
                </button>
                {/* <button
              className='sm:flex block items-center text-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-primary-blue font-medium'
              onClick={() => setOpenAddcontact(true)}
            >
              Add Contact Us
            </button> */}
              </div>
            </div>
            {/* <div className='flex items-center justify-between mb-4'>
          <h2 className='text-base font-medium'>Contact Us</h2>
          {editContact ? (
            <button
              className='bg-black text-white px-4 py-2 rounded-lg mr-2 text-sm'
              onClick={() => setEditContact(false)}
            >
              Back
            </button>
          ) : (
            <button
              className='bg-primary-blue text-white px-4 py-2 rounded-lg mr-2 text-sm'
              onClick={() => setEditContact(true)}
            >
              Edit Contact Us
            </button>
          )}
        </div> */}

            {editContact ? (
              <>
                <div className="mx-auto px-6 py-9 max-w-[70%] grid sm:grid-cols-2 gap-4 w-full mt-3">
                  <div>
                    <label className="text-sm font-light text-gray-500 block mb-1">
                      Address
                    </label>
                    <Autocomplete
                      onLoad={(autocompleteInstance) =>
                        setAutocomplete(autocompleteInstance)
                      }
                      onPlaceChanged={() => {
                        if (autocomplete) {
                          handleAddressChange(autocomplete.getPlace());
                        } else {
                          console.error("Autocomplete is not loaded yet.");
                        }
                      }}
                      className="w-full"
                    >
                      <input
                        id="lineOne"
                        name="lineOne"
                        type="text"
                        value={formData?.address?.lineOne}
                        className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
                        placeholder="Address"
                        onChange={handleChange}
                      />
                    </Autocomplete>
                  </div>
                  <div>
                    <label className="text-sm font-light text-gray-500 block mb-1">
                      Country
                    </label>
                    <Select
                      id="country"
                      name="country"
                      options={Country.getAllCountries().map((c) => ({
                        label: c.name,
                        value: c.isoCode,
                      }))}
                      value={{
                        label: formData?.address?.countryName,
                        value: formData?.address?.country,
                      }}
                      onChange={handleCountryChange}
                      className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
                      placeholder="Select Country"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-light text-gray-500 block mb-1">
                      State
                    </label>
                    <Select
                      id="state"
                      name="state"
                      options={stateOptions}
                      value={{
                        label: formData?.address?.stateName,
                        value: formData?.address?.state,
                      }}
                      onChange={handleStateChange}
                      className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
                      placeholder="Select State"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-light text-gray-500 block mb-1">
                      City
                    </label>
                    <Select
                      id="city"
                      name="city"
                      options={cityOptions}
                      value={{
                        label: formData?.address?.city,
                        value: formData?.address?.city,
                      }}
                      onChange={(selectedOption) =>
                        setFormData((prevData) => ({
                          ...prevData,
                          address: {
                            ...prevData.address,
                            city: selectedOption.value,
                          },
                        }))
                      }
                      className="w-full"
                      placeholder="Select City"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-light text-gray-500 block mb-1">
                      Pin Code
                    </label>
                    <input
                      id="zipcode"
                      name="zipcode"
                      type="text"
                      placeholder="Post Code"
                      value={formData?.address?.zipcode}
                      onChange={handleChange}
                      className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-light text-gray-500 block mb-1">
                      Phone Number
                    </label>
                    <PhoneInput
                      defaultCountry="IN"
                      international
                      withCountryCallingCode
                      value={phoneNumber}
                      onChange={setPhoneNumber}
                      placeholder="Phone Number"
                      className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-light text-gray-500 block mb-1">
                      WhatsApp Number
                    </label>
                    <PhoneInput
                      defaultCountry="IN"
                      international
                      withCountryCallingCode
                      value={whatsAppNumber}
                      onChange={setWhatsAppPhoneNumber}
                      placeholder="Phone Number"
                      className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
                    />
                  </div>

                  <div className="flex items-center justify-between w-full mt-11 mx-auto gap-4 col-span-2 xs:w-4/5">
                    <button
                      type="submit"
                      className="bg-primary-blue w-full font-semibold text-white p-4 rounded-full transition duration-200 hover:bg-sky-blue-750 hover:text-white"
                      onClick={handleSubmit}
                      disabled={isLoading}
                    >
                      {isLoading ? "Saving..." : "Save"}
                    </button>
                  </div>
                </div>
              </>
            ) : (
              <div className="flex flex-wrap">
                {/* Address Section */}
                <div className="w-full mb-4 lg:w-1/3 lg:mb-0">
                  <div className="p-4 mb-4 border border-gray-200 rounded-lg shadow-md min-h-32">
                    <div className="flex items-center">
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/address.svg`}
                        width={19}
                        height={19}
                        className="w-[19px] h-[19px] mr-2"
                        alt="Address Icon"
                        loading="lazy"
                      />
                      <h3 className="text-base font-semibold">Address</h3>
                    </div>
                    <p className="text-sm text-[#000000] py-2">
                      {data?.contactUs?.contactAddress?.lineOne || lineOne},{" "}
                      {data?.contactUs?.contactAddress?.lineTwo || lineOne},{" "}
                      {/* {data?.contactUs?.contactAddress?.city || city},{" "} */}
                      {data?.contactUs?.contactAddress?.state || state},{" "}
                      {data?.contactUs?.contactAddress?.country || country}
                    </p>
                  </div>

                  <div className="p-4 border border-gray-200 rounded-lg shadow-md min-h-32">
                    <div className="flex items-center">
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/whatsapp.svg`}
                        width={19}
                        height={19}
                        className="w-[19px] h-[19px] mr-2"
                        alt="WhatsApp Icon"
                        loading="lazy"
                      />
                      <h3 className="text-base font-semibold">
                        What&apos;s App
                      </h3>
                    </div>
                    <p className="text-sm text-[#000000] py-2">
                      Unable to Whatsapp? Call directly
                    </p>
                    <p className="text-sm text-[#000000] font-semibold">
                      {data?.contactUs?.whatsApp ||
                        data?.phoneNumber ||
                        contactUs.phoneNumber}
                      {contactUs.phoneNumber}
                    </p>
                  </div>
                </div>

                <div className="w-full lg:w-[45%] px-4">
                  <div className="overflow-hidden rounded-xl shadow-sm h-[275px] ">
                    {isApiLoaded ? (
                      <GoogleMap
                        mapContainerStyle={mapContainerStyle}
                        center={{
                          lat,
                          lng,
                        }}
                        zoom={14}
                      >
                        <Marker
                          position={{
                            lat,
                            lng,
                          }}
                          onClick={() =>
                            setSelectedMarker({
                              lat,
                              lng,
                            })
                          }
                        />
                        {selectedMarker && (
                          <InfoWindow
                            position={selectedMarker}
                            onCloseClick={() => setSelectedMarker(null)}
                          >
                            <div>
                              <h2>{data.name}</h2>
                            </div>
                          </InfoWindow>
                        )}
                      </GoogleMap>
                    ) : (
                      <p>Loading Google Maps API...</p>
                    )}
                  </div>
                </div>
              </div>
            )}

            <Transition show={openAddcontact} as={Fragment}>
              <Dialog
                as="div"
                className="relative z-50"
                onClose={closecontactModal}
              >
                {/* Overlay */}
                <Transition.Child
                  as={Fragment}
                  enter="ease-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="fixed inset-0 bg-black/50" />
                </Transition.Child>

                {/* Slide-In Modal */}
                <div className="fixed inset-0 overflow-hidden">
                  <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
                    <Transition.Child
                      as={Fragment}
                      enter="transform transition ease-out duration-300"
                      enterFrom="translate-x-full"
                      enterTo="translate-x-0"
                      leave="transform transition ease-in duration-200"
                      leaveFrom="translate-x-0"
                      leaveTo="translate-x-full"
                    >
                      <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                        {/* Modal Header */}
                        <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                          <h2 className="page-title">Add Address</h2>
                          <button
                            onClick={closecontactModal}
                            className="text-gray-500 hover:text-gray-800"
                          >
                            &#10005; {/* Close icon */}
                          </button>
                        </div>

                        {/* Modal Content */}
                        <div className="sm:px-6 px-4">
                          <div>
                            <div className="mb-3">
                              <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                                Address
                              </label>
                              <textarea
                                name="description"
                                className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                                placeholder="Enter address"
                                rows={3}
                                value=""
                                style={{ outline: "none" }}
                              ></textarea>
                            </div>
                            <div className="mb-3">
                              <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                                What's App
                              </label>
                              <textarea
                                name="Whatsapp"
                                className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                                placeholder="Enter address"
                                rows={3}
                                value=""
                                style={{ outline: "none" }}
                              ></textarea>
                            </div>
                            <div>
                              <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                                Location
                              </label>
                              <div className="overflow-hidden rounded-lg shadow-sm">
                                {isApiLoaded ? (
                                  <GoogleMap
                                    mapContainerStyle={mapContainerStyle}
                                    center={{
                                      lat:
                                        Number(formData?.address?.latitude) ||
                                        lat,
                                      lng:
                                        Number(formData?.address?.longitude) ||
                                        lng,
                                    }}
                                    zoom={14}
                                  >
                                    <Marker
                                      position={{
                                        lat:
                                          Number(formData?.address?.latitude) ||
                                          lat,
                                        lng:
                                          Number(
                                            formData?.address?.longitude
                                          ) || lng,
                                      }}
                                      onClick={() =>
                                        setSelectedMarker({
                                          lat:
                                            Number(
                                              formData?.address?.latitude
                                            ) || lat,
                                          lng:
                                            Number(
                                              formData?.address?.longitude
                                            ) || lng,
                                        })
                                      }
                                    />
                                    {selectedMarker && (
                                      <InfoWindow
                                        position={selectedMarker}
                                        onCloseClick={() =>
                                          setSelectedMarker(null)
                                        }
                                      >
                                        <div>
                                          <h2>{data.name}</h2>
                                        </div>
                                      </InfoWindow>
                                    )}
                                  </GoogleMap>
                                ) : (
                                  <p>Loading Google Maps API...</p>
                                )}
                              </div>
                            </div>
                            <div className="xs:flex block  items-center justify-between w-full sm:my-14 my-7 gap-4 col-span-2 py-4 bg-white/80 sticky bottom-0 backdrop-blur-sm">
                              <button
                                type="button"
                                className="hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 px-4 border-black rounded-lg w-full text-sm xs:mb-0 mb-2"
                                onClick={closecontactModal}
                              >
                                Cancel
                              </button>
                              <button
                                type="submit"
                                className="bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm"
                              >
                                Add
                              </button>
                            </div>
                          </div>
                        </div>
                      </Dialog.Panel>
                    </Transition.Child>
                  </div>
                </div>
              </Dialog>
            </Transition>

            <Transition show={openEditcontact} as={Fragment}>
              <Dialog
                as="div"
                className="relative z-50"
                onClose={closecontacteditModal}
              >
                {/* Overlay */}
                <Transition.Child
                  as={Fragment}
                  enter="ease-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="fixed inset-0 bg-black/50" />
                </Transition.Child>

                {/* Slide-In Modal */}
                <div className="fixed inset-0 overflow-hidden">
                  <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
                    <Transition.Child
                      as={Fragment}
                      enter="transform transition ease-out duration-300"
                      enterFrom="translate-x-full"
                      enterTo="translate-x-0"
                      leave="transform transition ease-in duration-200"
                      leaveFrom="translate-x-0"
                      leaveTo="translate-x-full"
                    >
                      <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                        {/* Modal Header */}
                        <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                          <h2 className="page-title">Edit Address</h2>
                          <button
                            onClick={closecontacteditModal}
                            className="text-gray-500 hover:text-gray-800"
                          >
                            &#10005; {/* Close icon */}
                          </button>
                        </div>

                        {/* Modal Content */}
                        <div className="sm:px-6 px-4">
                          <div>
                            <div className="mb-3">
                              <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                                Address
                              </label>
                              <Autocomplete
                                onLoad={(autocompleteInstance) =>
                                  setAutocomplete(autocompleteInstance)
                                }
                                onPlaceChanged={() => {
                                  if (autocomplete) {
                                    handleAddressChange(
                                      autocomplete.getPlace()
                                    );
                                  } else {
                                    console.error(
                                      "Autocomplete is not loaded yet."
                                    );
                                  }
                                }}
                                className="w-full"
                              >
                                <input
                                  id="lineOne"
                                  name="lineOne"
                                  className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                                  placeholder="Enter address"
                                  value={formData?.address?.lineOne}
                                  onChange={handleChange}
                                ></input>
                              </Autocomplete>
                            </div>
                            <div className="mb-3">
                              <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                                What's App
                              </label>
                              <PhoneInput
                                defaultCountry="IN"
                                international
                                withCountryCallingCode
                                value={whatsAppNumber}
                                onChange={setWhatsAppPhoneNumber}
                                placeholder="Phone Number"
                                className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
                              />
                            </div>
                            <div>
                              <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                                Location
                              </label>
                              <div className="overflow-hidden rounded-lg shadow-sm">
                                {isApiLoaded ? (
                                  <GoogleMap
                                    mapContainerStyle={mapContainerStyle}
                                    center={{
                                      lat:
                                        Number(formData?.address?.latitude) ||
                                        lat,
                                      lng:
                                        Number(formData?.address?.longitude) ||
                                        lng,
                                    }}
                                    zoom={14}
                                  >
                                    <Marker
                                      position={{
                                        lat:
                                          Number(formData?.address?.latitude) ||
                                          lat,
                                        lng:
                                          Number(
                                            formData?.address?.longitude
                                          ) || lng,
                                      }}
                                      onClick={() =>
                                        setSelectedMarker({
                                          lat:
                                            Number(
                                              formData?.address?.latitude
                                            ) || lat,
                                          lng:
                                            Number(
                                              formData?.address?.longitude
                                            ) || lng,
                                        })
                                      }
                                    />
                                    {selectedMarker && (
                                      <InfoWindow
                                        position={selectedMarker}
                                        onCloseClick={() =>
                                          setSelectedMarker(null)
                                        }
                                      >
                                        <div>
                                          <h2>{data.name}</h2>
                                        </div>
                                      </InfoWindow>
                                    )}
                                  </GoogleMap>
                                ) : (
                                  <p>Loading Google Maps API...</p>
                                )}
                              </div>
                            </div>
                            <div className="xs:flex block  items-center justify-between w-full sm:my-14 my-7 gap-4 col-span-2 py-4 bg-white/80 sticky bottom-0 backdrop-blur-sm">
                              <button
                                type="button"
                                className="hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 px-4 border-black rounded-lg w-full text-sm xs:mb-0 mb-2"
                                onClick={closecontacteditModal}
                              >
                                Cancel
                              </button>
                              <button
                                type="submit"
                                className="bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm"
                                onClick={handleSubmit}
                              >
                                Save Changes
                              </button>
                            </div>
                          </div>
                        </div>
                      </Dialog.Panel>
                    </Transition.Child>
                  </div>
                </div>
              </Dialog>
            </Transition>
          </div>
       
     
    </>
  );
};

export default ContactUs;
