import express from 'express';
import { getRoomInfo ,pushLinearRate,getBookingToYCS, pushInventory, getNewBookingOneDay, cancelBookingById} from '../controller/ezee.js';
import ezeeAuthMiddleware, { checkAuth } from '../middleware/auth.js';
const router = express.Router();

// Route to send a new message
router.post('/get-room-info', ezeeAuthMiddleware(), getRoomInfo);
router.post('/push-inventory', ezeeAuthMiddleware(),pushInventory)
router.post('/push-linear-rate',ezeeAuthMiddleware(), pushLinearRate)
router.post('/get-booking-to-ycs',ezeeAuthMiddleware(), getBookingToYCS)
router.post('/booking', getNewBookingOneDay)
router.put('/modify-booking', getNewBookingOneDay)
router.post('/cancel-booking',cancelBookingById)


export default router;
