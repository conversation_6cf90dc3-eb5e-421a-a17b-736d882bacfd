import jwt from 'jsonwebtoken';
import Response from "../utills/response.js";
import mongoose from "mongoose";
import { getUserData } from '../services/auth.js'
import roleModel from "../models/roles.js";
import dotenv from 'dotenv';
dotenv.config();

const optionalAuth = (permission = null) => {
    return async (req, res, next) => {
        try {
            const token = req.headers.authorization?.split(' ')[1];

            if (token) {
                // Validate token
                const decodedToken = jwt.verify(token, process.env.JWT_SECRET);

                // Fetch user data
                const user = await getUserData(new mongoose.Types.ObjectId(decodedToken.userId));
                if (!user) {
                    return res.status(401).json({ message: 'User not found' });
                }

                // Fetch role details
                const roleDetails = await roleModel.findOne({ role: user.role });

                // Check permissions if specified
                if (permission) {
                    if (!roleDetails || !roleDetails.permissions.includes(permission)) {
                        return Response.Unauthorized(res, null, 'Not authorized to access');
                    }
                }

                // Attach the user to the request
                req.user = user;
            } else {
                // No token provided, proceed as guest
                req.user = null;
            }

            next();
        } catch (error) {
            console.error('Optional authentication error:', error);
            return res.status(401).json({ message: error.message });
        }
    };
};


export { optionalAuth }
