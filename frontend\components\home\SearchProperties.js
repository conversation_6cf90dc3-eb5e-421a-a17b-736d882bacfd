import React, { useEffect, useRef, useState } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import {
  format,
  // isBefore, isAfter,
  addDays,
  isSameDay,
  differenceInDays,
  addMonths,
  isSameMonth
  // endOfMonth ,
} from "date-fns";
import { CiCalendar } from "react-icons/ci";
import { FaMinus, FaPlus } from "react-icons/fa6";
import { FiUserPlus } from "react-icons/fi";
import { RiProgress5Fill } from "react-icons/ri";
import { Button } from "@mui/material";
import StateAutocomplete from "./StateAutocomplete";
import toast from "react-hot-toast";
import { useRouter } from "next/router";
// import { motion } from "framer-motion";

const SearchProperties = ({
  setState,
  dataa,
  guest,
  setGuest,
  handleChange,
  handleSubmit,
  setHeaderSearch,
  loading,
  size = "small",
}) => {
  const [dates, setDates] = useState([null, null]);
  const [nights, setNights] = useState(0);
  const [calendarMonths, setCalendarMonths] = useState([
    new Date(),               // Left calendar (e.g., June)
    addMonths(new Date(), 1), // Right calendar (e.g., July)
  ]);

  // Optional: track changes if months can change dynamically
  // const handleMonthChange = (date, index) => {
  //   setCalendarMonths((prev) => {
  //     const updated = [...prev];
  //     updated[index] = date;
  //     return updated;
  //   });
  // };
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(typeof window !== "undefined" && window.innerWidth <= 768);
    };

    // Initial check
    checkMobile();

    // Add resize listener
    if (typeof window !== "undefined") {
      window.addEventListener("resize", checkMobile);
      return () => window.removeEventListener("resize", checkMobile);
    }
  }, []);

  useEffect(() => {
    // Set initial dates only on the client side after the component mounts
    const initialDates = [
      dataa?.checkIn ? new Date(dataa.checkIn) : new Date(),
      dataa?.checkOut ? new Date(dataa.checkOut) : addDays(new Date(), 1),
    ];
    setDates(initialDates);
  }, []);

  useEffect(() => {
    if (dates && dates[0] && dates[1]) {
      const bookingData = {
        checkIn: formatDate(dates[0]),
        checkOut: formatDate(dates[1]),
      };
      localStorage.setItem("bookingdata", JSON.stringify(bookingData));
      // Calculate nights when dates change
      const nightsCount = differenceInDays(dates[1], dates[0]);
      setNights(nightsCount);
    }
  }, [dates]);

  const [calendarOpen, setCalendarOpen] = useState(false);
  const [guestNum, setGuestNum] = useState(false);

  const guestDropdownRef = useRef(null);

  useEffect(() => {
    function handleClickOutside(event) {
      if (
        guestDropdownRef.current &&
        !guestDropdownRef.current.contains(event.target)
      ) {
        setGuestNum(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [guestDropdownRef]);

  const handleClear = () => {
    setDates([null, null]);
    setNights(0);
    handleChange({
      target: {
        value: {
          checkIn: "",
          checkOut: "",
        },
        name: "dateRange",
      },
    });
  };

  const incrementGuest = () => {
    if (guest < 8) {
      setGuest(guest + 1);
    } else {
      toast.error("Maximum 8 Guests are allowed !");
    }
  };

  const decrementGuest = () => {
    if (guest > 1) {
      setGuest(guest - 1);
    } else {
      toast.error("Minimum 1 Guest is required !");
    }
  };

  const formatDate = (date) => {
    if (!date) return "";
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-based
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  const handleDateChange = (selectedDates) => {
    // Set the selected start and end dates
    setDates(selectedDates);

    const [checkIn, checkOut] = selectedDates;
    const formattedCheckIn = formatDate(checkIn);
    const formattedCheckOut = checkOut ? formatDate(checkOut) : "";

    handleChange({
      target: {
        value: {
          checkIn: formattedCheckIn,
          checkOut: formattedCheckOut,
        },
        name: "dateRange",
      },
    });
  };

  // Function to format weekdays to 3 letters
  const formatWeekDay = (day) => {
    return day.substring(0, 3).toLowerCase();
  };

  // Custom day class names to handle the range styling

  const getDayClassNames = (date, startDate, endDate) => {
    let classNames = "text-sm flex items-center justify-center";

    const isStart = startDate && isSameDay(date, startDate);
    const isEnd = endDate && isSameDay(date, endDate) && isSameMonth(date, endDate);
    const isInRange = startDate && endDate && date > startDate && date < endDate;

    const isFirstOfMonth = date.getDate() === 1;
    const isLastOfMonth = date.getDate() === new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();

    if (isStart) {
      classNames += " bg-primary-blue !text-black font-semibold rounded-s rounded-e-none";
    } else if (isEnd) {
      classNames += " bg-primary-blue text-black font-semibold rounded-e rounded-s-none";
    } else if (isInRange) {
      classNames += " bg-primary-blue/30 text-black font-semibold rounded-none";

      if (isFirstOfMonth) {
        classNames += " first-date text-black font-semibold rounded-none";
      }

      if (isLastOfMonth) {
        classNames += " last-date text-black font-semibold rounded-none";
      }
    }


    return classNames;
  };


  const DateInputWithPlaceholder = ({ placeholder }) => {
    if (!dates) return null;

    const formattedStartDate = dates[0]
      ? format(dates[0], "dd-MMM")
      : "Check In";
    const formattedEndDate = dates[1]
      ? format(dates[1], "dd-MMM")
      : "Check Out";

    return (
      <>
        <div className="relative">
          {calendarOpen && isMobile && (
            <div
              className="fixed inset-0 bg-black opacity-50 z-40 sm:hidden"
              onClick={() => setCalendarOpen(false)}
            />
          )}
          <div className="flex items-center justify-start sm:justify-center gap-1 cursor-pointer z-30">
            <CiCalendar
              className={`text-xl ${isHomePage
                  ? "text-white md:text-black"
                  : "text-black md:text-black"
                }`}
              onClick={() => setCalendarOpen(true)}
            />
                
            <DatePicker
              calendarClassName="custom-calendar"
              className="w-full cursor-pointer max-w-[720px]"
              selected={dates[0]}
              onChange={handleDateChange}
              dateFormat="dd-MMM-yyyy"
              selectsRange
              startDate={dates[0]}
              endDate={dates[1]}
              placeholderText={placeholder}
              monthsShown={isMobile ? 1 : 2}
              open={calendarOpen}
              openToDate={calendarMonths[0]}
              minDate={new Date()}
              dayClassName={(date) => getDayClassNames(date, dates[0], dates[1])}
              filterDate={(date) => {
                return calendarMonths.some((month) =>
                  isSameMonth(date, month)
                );
              }}
              // maxDate={endOfMonth(calendarMonths[0])}
              renderCustomHeader={({
                decreaseMonth,
                increaseMonth,
                prevMonthButtonDisabled,
                nextMonthButtonDisabled,
                customHeaderCount,
              }) => (
                <div>
                  {customHeaderCount === 0 && (
                    <div className="flex items-center xjustify-between py-2 min-h-[2.5rem] custom-date-picker-header max-w-full sm:max-w-[720px] absolute top-[-41px] left-3 md:left-[20px] w-[95%] mx-auto md:w-[700px] lg:w-[700px] z-50 ">
                      <span className="text-sm flex items-center justify-center text-center gap-2 text-[#636c7d] font-normal bg-transparent w-full">
                        {dates?.[0] && dates?.[1] ? (
                          <>
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width={16}
                              height={16}
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth={2}
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="icon icon-tabler icons-tabler-outline icon-tabler-moon-stars"
                            >
                              <path
                                stroke="none"
                                d="M0 0h24v24H0z"
                                fill="none"
                              />
                              <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
                              <path d="M17 4a2 2 0 0 0 2 2a2 2 0 0 0 -2 2a2 2 0 0 0 -2 -2a2 2 0 0 0 2 -2" />
                              <path d="M19 11h2m-1 -1v2" />
                            </svg>
                            {nights} nights selected
                          </>
                        ) : (
                          <span className="flex items-center gap-2 font-normal">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width={16}
                              height={16}
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth={2}
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="icon icon-tabler icons-tabler-outline icon-tabler-calendar"
                            >
                              <path
                                stroke="none"
                                d="M0 0h24v24H0z"
                                fill="none"
                              />
                              <path d="M4 7a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12z" />
                              <path d="M16 3v4" />
                              <path d="M8 3v4" />
                              <path d="M4 11h16" />
                              <path d="M11 15h1" />
                              <path d="M12 15v3" />
                            </svg>
                            Select a check in date
                          </span>
                        )}
                      </span>
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setCalendarOpen(false);
                        }}
                        className="text-xs md:text-sm sm:pe-0 pe-2 font-semibold text-gray-600 hover:text-gray-800"
                      >
                        OK
                      </button>
                    </div>
                  )}
                  <div className="flex items-center justify-center px-4 py-0 relative">
                    {customHeaderCount === 0 && (
                      <button
                        onClick={() => {
                          decreaseMonth();
                          setCalendarMonths((prev) => [
                            addMonths(prev[0], -1),
                            prev[0],
                          ]);
                        }}
                        disabled={prevMonthButtonDisabled}
                        className="p-2 hover:bg-gray-100 rounded-full disabled:opacity-50 absolute left-[6px]"
                        type="button"
                      >
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        >
                          <path d="M15 18l-6-6 6-6" />
                        </svg>
                      </button>
                    )}
                    <span className="text-lg font-semibold">
                      {format(
                        customHeaderCount === 0
                          ? calendarMonths[0]
                          : calendarMonths[1],
                        "MMMM yyyy"
                      )}
                    </span>
                    {(customHeaderCount === 0 && isMobile) || customHeaderCount === 1 ? (
                      <button
                        onClick={() => {
                          increaseMonth();
                          setCalendarMonths((prev) =>
                            isMobile
                              ? [addMonths(prev[0], 1), addMonths(prev[0], 2)]
                              : [prev[1], addMonths(prev[1], 1)]
                          );
                        }}
                        disabled={nextMonthButtonDisabled}
                        className="p-2 hover:bg-gray-100 rounded-full disabled:opacity-50 absolute right-[6px]"
                        type="button"
                      >
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        >
                          <path d="M9 18l6-6-6-6" />
                        </svg>
                      </button>
                    ) : null}
                  </div>
                </div>
              )}
              formatWeekDay={formatWeekDay}
              onClickOutside={() => setCalendarOpen(false)}
              customInput={
                <div className="flex w-full p-1 items-center justify-between cursor-pointer">
                  <span
                    className={`text-base ${formattedStartDate || formattedEndDate
                        ? isHomePage
                          ? "text-white md:text-black"
                          : "text-black md:text-black"
                          
                        : "text-gray-400"
                      } pointer-events-none font-semibold`}
                  >
                    {formattedStartDate} - {formattedEndDate}
                  </span>
                </div>
              }
            >
              <div className="flex justify-end">
                <button
                  onClick={handleClear}
                  className="text-sm font-semibold text-gray-600 hover:text-gray-800 mt-[-28px] mr-[20px]"
                >
                  Clear
                </button>
              </div>
            </DatePicker>
          </div>
        </div>
      </>
    );
  };

  const NumberInputWithPlaceholder = ({
    placeholder,
    value,
    onChange,
    onFocus,
  }) => {
    return (
      <div className="relative">
        <div className="flex items-center justify-center gap-1 cursor-pointer">
          <FiUserPlus className="sm:text-2xl text-[19px] ml-[4px]" />
          <input
            type="number"
            min={1}
            max={4}
            className={`w-full p-1 placeholder-black cursor-pointer font-semibold ${size === "large" ? "text-base" : "text-xs"
              }`}
            value={value}
            onChange={(e) => {
              const val = parseInt(e.target.value) || 1;
              if (val > 8) {
                toast.error("Maximum 8 Guests are allowed !");
              } else if (val < 1) {
                toast.error("Minimum 1 Guest is required !");
              }
              const finalVal = Math.min(
                Math.max(parseInt(e.target.value) || 1, 1),
                4
              );
              onChange({ target: { value: finalVal } });
            }}
            onFocus={onFocus}
            placeholder={placeholder}
            style={{ outline: "none" }}
          />
        </div>
      </div>
    );
  };

  const router = useRouter();
  const isHomePage = router.pathname === "/" || router.pathname === "/tophostel" || router.pathname === "/exploreworld" || router.pathname === "/featuredhostel" || router.pathname === "/travelactivity" || router.pathname === "/meetbuddies" || router.pathname === "/discover-event" ;
 
    

  const textSizeClasses = size === "large" ? "text-sm" : "text-xs";

  return (
    <>
      {/* <motion.div
        initial={{ y: 80, opacity: 0 }}
        whileInView={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        viewport={{ once: false }} */}
      <div className="xs:px-5">
        <div className="font-manrope bg-white bg-opacity-10 md:bg-opacity-100 border max-w-[1055px] mx-auto w-full sm:flex lg:justify-between items-center border-slate-105 rounded-2xl sm:rounded-full sm:pl-8 sm:pr-4 sm:py-2 p-3 shadow-xl">
          <div
            className={`lg:w-[40%] md:w-[38%] w-full relative flex items-start flex-col sm:border-r border-b sm:border-b-0 border-slate-105 h-full ${size === "large" ? "sm:py-4 py-3" : "px-3 "
              }`}
          >
            {/* <h4 className={`text-neutral-400 mb-2 ${textSizeClasses}`}>
              Location
            </h4> */}
            <div
              data-title="Enter your desired location"
              className={`comman-tooltip before:!top-[-37px] before:!left-[81px] flex justify-start w-full font-bold ${isHomePage
                  ? "text-white md:text-black"
                  : "text-black md:text-black"
                }  ${textSizeClasses}`}
            >
              <StateAutocomplete setState={setState} />
            </div>
          </div>
          <div
            className={`md:w-[33%] lg:w-[30%] flex relative items-start flex-col sm:border-r border-b sm:border-b-0 border-slate-105 h-full ${size === "large"
                ? "sm:px-2 md:px-4 lg:px-9 sm:py-4 py-3"
                : "sm:px-3 "
              }`}
          >
            {/* <h4 className={`text-neutral-400 mb-2 ${textSizeClasses}`}>
              Check-In / Check-Out
            </h4> */}
            <div
              data-title="Select your check-in and check-out dates"
              className={`comman-tooltip before:!top-[-37px] before:!left-[121px] sm:flex w-full font-bold text-black ${textSizeClasses}`}
              onClick={() => setCalendarOpen(true)}
            >
              <DateInputWithPlaceholder placeholder="Select Dates" />
            </div>
          </div>
          <div
            className={`flex-1 flex items-start relative flex-col h-full ${size === "large" ? "sm:px-2 md:px-4 lg:px-9 py-1" : "sm:px-2 "
              }`}
          >
            {/* <h4 className={`text-neutral-400 mb-2 ${textSizeClasses}`}>
              Guests
            </h4> */}
            <div
              data-title="Adjust the number of guests"
              className={`comman-tooltip before:!top-[-37px] before:!left-[91px] relative flex w-full font-bold md:text-black ${isHomePage
                  ? "text-white md:text-black"
                  : "text-black md:text-black"
                } ${textSizeClasses}`}
            >
              <div
                onClick={() => {
                  setGuestNum(!guestNum);
                }}
              >
                <NumberInputWithPlaceholder
                  placeholder="1"
                  value={guest}
                  onChange={(e) => setGuest(Number(e.target.value))}
                />
              </div>
              {guestNum && (
                <>
                  <div className="fixed inset-0 bg-black opacity-50 z-40 sm:hidden"></div>
                  <div
                    ref={guestDropdownRef}
                    className="w-min min-w-max rounded-2xl sm:p-3 py-2 px-3 z-50 bg-white shadow-lg absolute left-0 lg:left-[-30px] sm:top-[65px] top-[-55px] max-w-full"
                  >
                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-x-[6px]">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width={24}
                          height={24}
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth={2}
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="icon icon-tabler icons-tabler-outline icon-tabler-users w-[1.5rem] h-[1.5rem] text-black"
                        >
                          <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                          <path d="M9 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0" />
                          <path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2" />
                          <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                          <path d="M21 21v-2a4 4 0 0 0 -3 -3.85" />
                        </svg>
                        <p className=" w-full font-semibold rounded py-1 base:py-0  font-middle px-1 sm:text-base text-sm text-black">
                          Guests
                        </p>
                      </div>
                      <div className="flex items-center text-sm">
                        <Button
                          className="sm:w-10 w-6 sm:h-10 h-6 min-w-0 p-0 rounded-full flex items-center justify-center border-[#40e0d0] border-2 border-solid bg-transparent text-[#40e0d0]"
                          onClick={decrementGuest}
                        >
                          <FaMinus />
                        </Button>
                        <input
                          type="number"
                          value={guest}
                          className="appearance-none w-6 text-center text-black placeholder:text-black"
                          readOnly
                        />
                        <Button
                          className="sm:w-10 w-6 sm:h-10 h-6 min-w-0 p-0 rounded-full flex items-center justify-center border-[#40e0d0] border-2 border-solid bg-transparent text-[#40e0d0]"
                          onClick={incrementGuest}
                        >
                          <FaPlus />
                        </Button>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
          <Button
            className={`w-full mt-2 sm:mt-0 md:w-[15%] min-w-0 sm:w-12 sm:h-12 mx-auto sm:px-2 px-4 text-black text-xs flex items-center justify-center gap-2 bg-primary-blue md:text-sm outline-none rounded-full font-semibold capitalize ${size === "large" ? "py-3 md:py-5" : "py-3"
              }`}
            onClick={() => {
              if (setHeaderSearch) {
                setHeaderSearch(true);
              }
              handleSubmit();
            }}
          >
            {loading ? <RiProgress5Fill /> : "Search"}
          </Button>
        </div>
      </div>
      {/* </motion.div> */}
    </>
  );
};

export default SearchProperties;
