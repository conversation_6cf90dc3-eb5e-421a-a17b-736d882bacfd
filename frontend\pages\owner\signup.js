import React, { useState } from "react";
import axios from "axios";
import { useRouter } from "next/router";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import VisibilityOffOutlinedIcon from "@mui/icons-material/VisibilityOffOutlined";
import toast, { Toaster } from "react-hot-toast";
import { BASE_URL } from "@/utils/api";
import dynamic from "next/dynamic";
import Link from "next/link";
import PhoneInput, { isValidPhoneNumber } from "react-phone-number-input";
import "react-phone-number-input/style.css";
import Head from "next/head";

const DividerAndSocialIcons = dynamic(
  () => import("@/components/ownerFlow/dividerAndSocialIcons"),
  { ssr: false }
);

const Signup = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showPassword, setShowPassword] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState("");
  const router = useRouter();

  const handleChange = (e) => {
    const { id, value } = e.target;
    setFormData((prevFormData) => ({
      ...prevFormData,
      [id]: value,
    }));
  };

  const handleSignup = async (event) => {
    event.preventDefault();

    // Validate if all fields are filled
    if (
      !formData.name ||
      !formData.email ||
      !formData.password ||
      !phoneNumber
    ) {
      setError("All fields are required.");
      return;
    }

    if (!isValidPhoneNumber(phoneNumber)) {
      setError("Please enter a valid phone number.");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const dataToSend = {
        ...formData,
        contact: phoneNumber, // Add the phone number to the form data
      };
      const response = await axios.post(
        `${BASE_URL}/auth/hostel-provider`,
        dataToSend
      );

      if (response.status === 201) {
        setLoading(false);
        // localStorage.setItem('userData', JSON.stringify(response.data.user));
        toast.success(
          `We’ve sent a verification code to ${formData?.email}. Please enter this code to continue.`
        );
        router.push("/owner/verifyotpowner");
      } else {
        toast.error("Signup failed: " + response.data.message);
      }
    } catch (error) {
      toast.error(
        "Error signing up: " +
          (error.response ? error.response.data.message : "Network error")
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Sign Up for Hostel Booking | Mixdorm</title>
        <meta
          name='description'
          content='Register on Mixdorm for Easy and Budget-Friendly Hostel Booking. Explore the Best Stays for Backpackers. Sign Up in Seconds!'
        />
      </Head>
      <Toaster position='top-center' />
      <div className='min-h-screen flex items-center justify-center bg-[#F7F7F7] w-full pt-[2rem] pb-[8rem]'>
        <div className='w-full max-w-3xl pb-6 bg-white shadow-md px-14 pt-14 rounded-3xl md:max-w-2xl'>
          <h2 className='mb-4 sm:text-[27px] text-lg font-bold text-gray-800'>
            👋 Welcome To <span className='text-primary-blue'>Mix</span>Dorm
          </h2>
          <h4 className='sm:text-base text-sm text-black my-5 '>
            {" "}
            Already have an account?{" "}
            <Link
              href='/owner/login'
              className='text-primary-blue font-medium ml-1 cursor-pointer underline underline-offset-2'
              prefetch={false}
            >
              SIGN IN
            </Link>{" "}
          </h4>
          <form onSubmit={handleSignup}>
            <div className='mb-4'>
              <label className='block text-black sm:text-base text-sm font-semibold mb-1.5'>
                Hostel Name
              </label>
              <input
                type='text'
                id='name'
                placeholder='Hostel Name'
                value={formData.name}
                onChange={handleChange}
                className='w-full px-3 py-4 border border-[#EEEEEE] rounded-full focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-[#888888] placeholder:font-light'
              />
            </div>
            <div className='mb-5'>
              <label className='block text-black sm:text-base text-sm font-semibold mb-1.5'>
                Email
              </label>
              <input
                type='email'
                id='email'
                value={formData.email}
                onChange={handleChange}
                className='w-full px-3 py-4 border border-[#EEEEEE] rounded-full focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-[#888888] placeholder:font-light'
                placeholder='Email'
              />
            </div>
            <div className='mb-5'>
              <label className='block text-black sm:text-base text-sm font-semibold mb-1.5'>
                Phone Number
              </label>
              <PhoneInput
                defaultCountry='IN'
                international
                withCountryCallingCode
                type='contact'
                id='contact'
                value={phoneNumber}
                onChange={setPhoneNumber}
                className='w-full px-3 py-4 border border-[#EEEEEE] rounded-full focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-[#888888] placeholder:font-light'
                placeholder='Contact'
              />
            </div>
            <div className='relative mb-6'>
              <label className='block text-black sm:text-base text-sm font-semibold mb-1.5'>
                Password
              </label>
              <input
                type={showPassword ? "text" : "password"}
                id='password'
                value={formData.password}
                onChange={handleChange}
                className='w-full px-3 py-4 border border-[#EEEEEE] rounded-full focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-[#888888] placeholder:font-light'
                placeholder='Password'
              />
              <button
                type='button'
                className='absolute right-4 top-11 text-gray-300 transform'
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <VisibilityOutlinedIcon />
                ) : (
                  <VisibilityOffOutlinedIcon />
                )}
              </button>
            </div>

            <button
              type='submit'
              className='w-full bg-[#40E0D0] font-semibold text-black py-3 rounded-full hover:bg-[#40E0D0] transition duration-200'
              disabled={loading}
            >
              {loading ? "Signing Up..." : "Sign Up"}
            </button>
            {error && (
              <div className='mb-4 text-center text-red-600'>{error}</div>
            )}
          </form>
          <DividerAndSocialIcons />
        </div>
      </div>
    </>
  );
};

export default Signup;
