// import Head from "next/head";

// export default function StopSell() {
//   return (
//     <>
//       <Head>
//         <title>Calendar Stop Sell</title>
//       </Head>
//       <main className="min-h-screen bg-white px-4 md:px-8">
//         <h2 className="page-title">Stop Sell</h2>
//         <div className="bg-white border border-gray-200 rounded-md p-4 shadow-sm w-full my-4">
//           <form className="flex flex-col md:flex-row md:flex-wrap gap-4 items-center">
//             {/* Source Field */}
//             <div className="flex flex-col w-full md:flex-1 min-w-[250px]">
//               <label
//                 className="text-sm font-medium text-black mb-1"
//                 htmlFor="source"
//               >
//                 Source
//               </label>
//               <select
//                 id="source"
//                 className="border border-gray-300 rounded-md px-4 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-black focus:border-black w-full"
//               >
//                 <option>Channel - CHANNEL</option>
//                 <option>Channel - CHANNEL</option>
//                 <option>Channel - CHANNEL</option>
//                 <option>Channel - CHANNEL</option>
//               </select>
//             </div>

//             {/* Month Field */}
//             <div className="flex flex-col w-full md:flex-1 min-w-[250px]">
//               <label
//                 className="text-sm font-medium text-black mb-1"
//                 htmlFor="month"
//               >
//                 Month
//               </label>
//               <select
//                 id="month"
//                 className="border border-gray-300 rounded-md px-4 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-black focus:border-black w-full"
//               >
//                 <option>Apr -2024</option>
//                 <option>Apr -2024</option>
//                 <option>Apr -2024</option>
//                 <option>Apr -2024</option>
//               </select>
//             </div>

//             {/* Button */}
//             <div className="w-full md:w-auto">
//               <button
//                 type="submit"
//                 className="bg-black text-white px-6 py-2 rounded-md hover:bg-gray-950 transition w-full md:w-auto mt-5"
//               >
//                 Show
//               </button>
//             </div>
//           </form>
//         </div>
//       </main>
//     </>
//   );
// }

import React, { useState } from "react";
import { FaAngleDown, FaAngleLeft, FaAngleRight } from "react-icons/fa";

const StopSell = () => {
  const [showTable, setShowTable] = useState(false);
  const [selectedDays, setSelectedDays] = useState([]);
  const [action, setAction] = useState("stop");

  const days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

  const roomTypes = ["Standard Room", "Double Room", "Room", "Dorm"];

  const dates = [
    { day: "TUE", date: "01", count: 0 },
    { day: "WED", date: "02", count: 0 },
    { day: "THU", date: "03", count: 4 },
    { day: "FRI", date: "04", count: 0 },
    { day: "SAT", date: "05", count: 2 },
    { day: "SUN", date: "06", count: 1 },
    { day: "TUE", date: "01", count: 0 },
    { day: "WED", date: "02", count: 1 },
    { day: "THU", date: "03", count: 0 },
    { day: "FRI", date: "04", count: 0 },
  ];

  const redCells = [[3], [4], [5], [6]];
  const toggleDay = (day) => {
    setSelectedDays((prev) =>
      prev.includes(day) ? prev.filter((d) => d !== day) : [...prev, day]
    );
  };

  return (
    <main className="min-h-screen bg-white px-4 md:px-8 py-6">
      <h2 className="page-title font-bold text-xl mb-4">Stop Sell</h2>

      {/* Input Form */}
      <div className="bg-white border border-gray-200 rounded-md p-4 shadow-sm w-full mb-4">
        <form className="flex flex-col md:flex-row md:flex-wrap gap-4 items-center">
          <div className="flex flex-col w-full md:flex-1 min-w-[250px]">
            <label
              className="text-sm font-medium text-black mb-1"
              htmlFor="source"
            >
              Source
            </label>
            <select
              id="source"
              className="border border-gray-300 rounded-md px-4 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-black focus:border-black w-full"
            >
              <option>Channel - CHANNEL</option>
              <option>Channel - CHANNEL</option>
              <option>Channel - CHANNEL</option>
              <option>Channel - CHANNEL</option>
            </select>
          </div>

          <div className="flex flex-col w-full md:flex-1 min-w-[250px]">
            <label
              className="text-sm font-medium text-black mb-1"
              htmlFor="month"
            >
              Month
            </label>
            <select
              id="month"
              className="border border-gray-300 rounded-md px-4 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-black focus:border-black w-full"
            >
              <option>Apr -2024</option>
              <option>May -2024</option>
              <option>Jun -2024</option>
              <option>Jul -2024</option>
            </select>
          </div>

          <div className="w-full md:w-auto">
            <button
              type="button"
              onClick={() => setShowTable(true)}
              className="bg-black text-white px-6 py-2 rounded-md hover:bg-gray-950 transition w-full md:w-auto mt-5"
            >
              Show
            </button>
          </div>
        </form>
        {showTable && (
          <>
            {/* Filter Controls */}
            <div className="bg-white border border-gray-200 rounded-md p-4 shadow-sm w-full mt-4">
              <div className="font-semibold mb-2">Filter</div>
              <div className="flex flex-wrap items-center gap-4">
                <div className="flex gap-2 items-center">
                  <span className="font-medium text-sm">Days Select</span>
                  {days.map((day) => (
                    <label
                      key={day}
                      className="relative flex items-center gap-2 text-sm cursor-pointer"
                    >
                      <input
                        type="radio"
                        name="day" // important for proper radio behavior
                        checked={selectedDays.includes(day)}
                        onChange={() => toggleDay(day)}
                        className="peer hidden"
                      />
                      <div className="w-4 h-4 border border-gray-500 rounded-md flex items-center justify-center">
                        {selectedDays.includes(day) && (
                          <div className="w-3 h-3 p-0.5 ">
                            <div className="w-full h-full bg-gray-600 rounded-sm" />
                          </div>
                        )}
                      </div>
                      {day}
                    </label>
                  ))}
                </div>
                <button className="bg-black text-white px-4 py-1 rounded-md">
                  Clear
                </button>
                <div className="flex gap-2 items-center text-sm">
                  <span className="font-medium">Change to</span>
                  <label className="flex gap-1 items-center cursor-pointer">
                    <input
                      type="radio"
                      name="action"
                      checked={action === "stop"}
                      onChange={() => setAction("stop")}
                      className="peer hidden"
                    />
                    <div className="w-4 h-4 border border-gray-500 rounded-full relative">
                      {action === "stop" && (
                        <div className="absolute inset-0.5 bg-gray-600 rounded-full"></div>
                      )}
                    </div>
                    Stop Sell
                  </label>

                  <label className="flex gap-1 items-center cursor-pointer">
                    <input
                      type="radio"
                      name="action"
                      checked={action === "start"}
                      onChange={() => setAction("start")}
                      className="peer hidden"
                    />
                    <div className="w-4 h-4 border border-gray-500 rounded-full relative">
                      {action === "start" && (
                        <div className="absolute inset-0.5 bg-gray-600 rounded-full"></div>
                      )}
                    </div>
                    Start Sell
                  </label>
                </div>
                <button className="bg-black text-white px-4 py-1 rounded-md">
                  Save
                </button>
              </div>
            </div>
          </>
        )}
      </div>

      {showTable && (
        <>
          {/* Table Calendar */}
          <div className="mt-6 border rounded-md overflow-x-auto">
            <table className="w-full text-center">
              <thead>
                <tr className="bg-gray-100">
                  <th className="text-left px-4 py-6">
                    <div className="space-y-4">
                      <div className="date-nav flex  justify-between">
                        <div className="btns flex sm:gap-x-6 gap-x-1 items-center">
                          <button className="ml-auto text-gray-700 hover:bg-primary-blue hover:text-white  rounded-sm sm:text-base text-sm">
                            <FaAngleLeft />
                          </button>
                          <h6 className="flex items-center gap-2 text-[#000000] text-xs w-full">
                            {new Date().toLocaleDateString("en-GB", {
                              day: "2-digit",
                              month: "short",
                              year: "numeric",
                            })}
                          </h6>
                          <button className="text-gray-700 hover:bg-primary-blue hover:text-white justify-center rounded-sm sm:text-base text-sm">
                            <FaAngleRight />
                          </button>
                        </div>
                      </div>
                      <div className="calendar-room-header w-full text-sm text-black font-medium flex gap-2">
                        <div className="w-5 h-5 rounded-sm bg-gray-200 flex items-center justify-center">
                          <FaAngleDown
                            className={`transition-transform duration-200 `}
                          />
                        </div>
                        All Room Types
                      </div>
                    </div>
                  </th>
                  {dates.map((d, idx) => (
                    <th key={idx} className="px-3 py-2">
                      <div className="p-2 bg-white rounded-xl">
                        <div className="bg-gray-200  rounded-full w-8 h-6 mx-auto text-sm font-medium text-black flex items-center justify-center">
                          {d.count}
                        </div>
                        <div className="text-xs font-medium text-black mt-1">
                          {d.day} {d.date}
                        </div>
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {roomTypes.map((room, rowIndex) => (
                  <tr key={room} className="border-t">
                    <td className="text-left px-4 py-2 font-medium text-sm">
                      {room}
                    </td>
                    {dates.map((_, colIndex) => (
                      <td key={colIndex} className="px-3 py-2">
                        <div
                          className={`h-10 w-16 rounded-md mx-auto ${
                            redCells[rowIndex]?.includes(colIndex)
                              ? "bg-[#E45639]"
                              : "bg-gray-200"
                          }`}
                        ></div>
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </>
      )}
    </main>
  );
};

export default StopSell;
