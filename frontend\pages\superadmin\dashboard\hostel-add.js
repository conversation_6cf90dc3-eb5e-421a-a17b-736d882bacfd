"use client";
import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";

const AddHostel = () => {
  const [activeTab, setActiveTab] = useState(0);

  const handleTab = (index) => {
    setActiveTab(index);
  };
  return (
    <div className="lg:pl-[255px] md:pl-[180px] sm:pl-[10px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616] h-screen">
      <h2 className="text-2xl lg:text-3xl font-poppins font-bold text-black dark:text-gray-100">
        Add Hostel
      </h2>
      <div className="w-full h-auto mt-5 bg-white rounded p-7 dark:bg-black">
        <div className="flex flex-wrap items-center justify-center mx-auto gap-5">
          <button
            type="button"
            className={`relative flex w-[165px] items-center justify-center h-10 px-5 py-2 text-sm font-semibold font-poppins border rounded ${
              activeTab === 0
                ? "bg-sky-blue-650 text-white"
                : "border-gray-200 text-black-100 dark:text-[#B6B6B6]"
            }`}
            onClick={() => handleTab(0)}
          >
            Add Hostel Detail
          </button>
          <button
            type="button"
            className={`relative flex w-[165px] items-center justify-center h-10 px-5 py-2 text-sm font-semibold font-poppins border rounded ${
              activeTab === 1
                ? "bg-sky-blue-650 text-white"
                : "border-gray-200 text-black-100 dark:text-[#B6B6B6]"
            }`}
            onClick={() => handleTab(1)}
          >
            Add Room Detail
          </button>
          <button
            type="button"
            className={`relative flex w-[165px] items-center justify-center h-10 px-5 py-2 text-sm font-semibold font-poppins border rounded ${
              activeTab === 2
                ? "bg-sky-blue-650 text-white"
                : "border-gray-200 text-black-100 dark:text-[#B6B6B6]"
            }`}
            onClick={() => handleTab(2)}
          >
            Add Rate Detail
          </button>
        </div>

        <div className="mt-10 w-full md:w-[90%] sm:w-[95%] mx-auto ">
          {activeTab === 0 && (
            <>
              <div className="flex flex-wrap items-center justify-between w-full gap-y-5 pl-0 md:pl-2 lg:pl-0 h-auto">
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Hostel Name
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:text-sm placeholder:font-poppins dark:bg-transparent dark:placeholder:text-[#757575]"
                    placeholder="Enter Hostel Name"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Hostel Address
                  </label>
                  <input
                    type="text"
                    placeholder="Enter Hostel Address"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:text-sm placeholder:font-poppins dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Rate
                  </label>
                  <input
                    type="text"
                    placeholder="Rating"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:text-sm placeholder:font-poppins dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Floor
                  </label>
                  <input
                    type="text"
                    placeholder="Floor"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:text-sm placeholder:font-poppins dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Room
                  </label>
                  <input
                    type="text"
                    placeholder="Rooms"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:text-sm placeholder:font-poppins dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Bed
                  </label>
                  <input
                    type="text"
                    placeholder="Beds"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 placeholder-black rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:text-sm placeholder:font-poppins dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Country
                  </label>
                  <select className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 placeholder-black rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:text-sm placeholder:font-poppins dark:bg-transparent dark:placeholder:text-[#757575]">
                    
                    <option className="dark:bg-black dark:text-[#757575]">
                      India
                    </option>
                    <option className="dark:bg-black dark:text-[#757575]">
                      Japan
                    </option>
                  </select>
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Offer
                  </label>
                  <input
                    type="text"
                    placeholder="Offer"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 placeholder-black rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:text-sm placeholder:font-poppins dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Status
                  </label>
                  <input
                    type="text"
                    placeholder="Status"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 placeholder-black rounded outline-none bg-sky-blue-25 h-9 placeholder:font-medium placeholder:text-sm placeholder:font-poppins dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
              </div>

              <div className="flex items-center justify-center gap-4 mt-5">
                <Link
                  href={"/superadmin/dashboard/hostel"}
                  type="button"
                  className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-20 py-2 text-sm font-semibold font-poppins border  rounded  border-gray-200 text-black-100 dark:text-gray-100`}
                >
                  Cancel
                </Link>
                <button
                  type="button"
                  className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-20  py-2 text-sm font-semibold font-poppins border  rounded  bg-sky-blue-650 text-white `}
                >
                  Save
                </button>
              </div>
            </>
          )}
          {activeTab === 1 && (
            <>
              <div className="flex flex-wrap items-center justify-between w-full gap-y-5 pl-0 md:pl-2 lg:pl-0">
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Room Name
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                    placeholder="#banana"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Room Type
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                    placeholder="Room Type"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Total beds
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                    placeholder="Total Beds"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Ensuite
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                    placeholder="Ensuite"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Rate
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                    placeholder="Rating"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Currency
                  </label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                    placeholder="INR"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Room Photo
                  </label>
                  <div className="relative w-full">
                    <input
                      type="file"
                      className="relative z-30 w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none opacity-0 bg-sky-blue-25 h-9 dark:bg-transparent dark:placeholder:text-[#757575]"
                    />
                    <div className="absolute flex items-center justify-between w-full top-2 text-gray-50">
                      <div className="rounded-lg border-2 border-gray-200">
                        <Image
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`}
                          alt="img"
                          width={140}
                          height={50}
                          title="img"
                          className="object-contain w-fit h-fit max-w-24 max-h-36 flex items-center rounded-md"
                        />
                      </div>
                      <span className="text-sm font-semibold pl-0 md:pl-4 lg:pl-0 dark:text-[#757575]">
                        Banana Room.Jpj
                      </span>
                      <span className="text-sm font-semibold dark:text-[#757575]">
                        252 KB
                      </span>
                    </div>
                  </div>
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Room Description
                  </label>
                  <input
                    type="text"
                    placeholder="Description"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
                <div className="w-full sm:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Rate Type
                  </label>
                  <input
                    type="text"
                    placeholder="Type"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
              </div>

              <div className="flex items-center justify-center gap-4 mt-5 ">
                <Link
                  href={"/superadmin/dashboard/hostel"}
                  type="button"
                  className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-20 py-2 text-sm font-semibold font-poppins border  rounded  border-gray-200 text-black-100 dark:text-gray-100`}
                >
                  Cancel
                </Link>
                <button
                  type="button"
                  className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-20  py-2 text-sm font-semibold font-poppins border  rounded  bg-sky-blue-650 text-white `}
                >
                  Save
                </button>
              </div>
            </>
          )}
          {activeTab === 2 && (
            <>
              <div className="flex flex-wrap items-center justify-between w-full gap-y-5 md:gap-x-4 pl-0 md:pl-2 lg:pl-0">
                <div className="w-full md:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Room
                  </label>
                  <input
                    type="text"
                    placeholder="88 Backpackers"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
                <div className="w-full md:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Rate Type
                  </label>
                  <input
                    type="text"
                    placeholder="Dorm"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
                <div className="w-full md:w-[48%]">
                  <label className="text-sm font-semibold font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    Rate Value
                  </label>
                  <input
                    type="text"
                    placeholder="1000"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
                <div className="w-full md:w-[48%]">
                  <label className="text-sm font-medium text-gray-500 dark:text-[#B6B6B6]">
                    Percentage
                  </label>
                  <input
                    type="text"
                    placeholder="10"
                    className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-medium placeholder:font-poppins placeholder:text-sm dark:bg-transparent dark:placeholder:text-[#757575]"
                  />
                </div>
              </div>

              <div className="flex items-center justify-center gap-4 mt-5">
                <Link
                  href={"/superadmin/dashboard/hostel"}
                  type="button"
                  className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-20 py-2 text-sm font-semibold font-poppins border  rounded  border-gray-200 text-black-100 dark:text-gray-100`}
                >
                  Cancel
                </Link>
                <button
                  type="button"
                  className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-20  py-2 text-sm font-semibold font-poppins border  rounded  bg-sky-blue-650 text-white `}
                >
                  Save
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default AddHostel;
