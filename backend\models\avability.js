import mongoose from "mongoose";

const RoomsAvabilitySchema = new mongoose.Schema({
  propertyId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
  },
  roomId: {
    type: Number,
    required: true,
  },
  date: {
    type: Date,
    required: true,
  },
 
  availableUnits: {
    type: Number
  },
  chanelPartner:{
    type:String
  }
}, { timestamps: true });


export default mongoose.model("RoomsAvability", RoomsAvabilitySchema, 'RoomsAvability');
