import React from "react";
import Link from "next/link";
import { BiSolidCrown } from "react-icons/bi";
import { ChevronRight } from "lucide-react";
import { PiLockKeyLight } from "react-icons/pi";
import { RiDiscountPercentFill } from "react-icons/ri";
import { FaCalendarAlt  } from "react-icons/fa";
import { BsFillGiftFill } from "react-icons/bs";
import { PiFireSimpleFill } from "react-icons/pi";
import { FaAward } from "react-icons/fa6";
import Image from "next/image";

const Adventurer = () => {
  
  return (
    <>
       <div>
            <div className="mb-4">
              <ul
                className="flex flex-wrap justify-between"
                data-tabs-toggle="#default-tab-content"
                role="tablist"
              >
                <li
                  className="me-2 cursor-pointer flex items-center"
                  role="presentation"
                >
                  <div className="w-[280px] h-[145px] bg-[url('https://mixdorm.s3.ap-south-1.amazonaws.com/front-images/explorebg.png')] rounded-3xl p-4">
                    <div className="flex justify-between">
                      <h3 className="font-bold text-black text-xl">Explorer</h3>
                      <div>
                        <p className="text-xs font-semibold">
                          Reward <span className="text-sm">Points</span>
                        </p>
                        <p className="text-sm font-semibold text-[#FFAD72] flex justify-end">
                          1,000
                        </p>
                      </div>
                    </div>
                    <div className="mt-12">
                      <div className="mb-1 text-xs font-medium dark:text-white flex justify-between">
                        <span>1000/5000</span> <span>Redeem</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mb-4 dark:bg-gray-700">
                        <div className="bg-[#FFAD72] h-1.5 rounded-full dark:bg-blue-500 w-1/2"></div>
                      </div>
                    </div>
                  </div>
                </li>
                <li
                  className="me-2 cursor-pointer flex items-center"
                  role="presentation"
                >
                  <div className="w-[380px] h-[185px] bg-[url('https://mixdorm.s3.ap-south-1.amazonaws.com/front-images/advanturebg.png')] rounded-3xl border border-[#4C3CB9] p-5">
                    <div className="flex justify-between">
                      <h3 className="font-bold text-black text-2xl">
                        Adventurer
                      </h3>
                      <div>
                        <p className="text-xs font-semibold">
                          Reward <span className="text-sm">Points</span>
                        </p>
                        <p className="text-sm font-semibold text-black flex justify-end">
                          6,000
                        </p>
                      </div>
                    </div>
                    <div className="w-[120px] h-[28px] bg-[#4C3CB9] mt-7 flex px-1 justify-between items-center">
                      <BiSolidCrown size={15} className="text-white" />
                      <p className="text-sm text-white font-semibold">
                        7 Benefits
                      </p>
                      <ChevronRight size={15} className="text-white" />
                    </div>
                    <div className="mt-5">
                      <div className="mb-1 text-sm font-medium text-[#4C3CB9] dark:text-white flex justify-between">
                        <span>6000/12000</span> <span>Redeem</span>
                      </div>
                      <div className="w-full bg-gray-300 rounded-full h-2 mb-4 dark:bg-gray-600">
                        <div className="bg-[#4C3CB9] h-1.5 rounded-full dark:bg-blue-500 w-1/2"></div>
                      </div>
                    </div>
                  </div>
                </li>
                <li
                  className="me-2 cursor-pointer flex items-center"
                  role="presentation"
                >
                  <div className="w-[218px] h-[107px] bg-[#D9D9D9] rounded-3xl border border-[#DDDDDD] p-3">
                    <div className="flex justify-between">
                      <h3 className="font-bold text-black text-lg">Nomad</h3>
                      <div>
                        <p className="text-xs text-[7px] font-semibold">
                          Reward <span className="text-[8px]">Points</span>
                        </p>
                        <p className="text-[12px] font-semibold text-black flex justify-end">
                          6,000
                        </p>
                      </div>
                    </div>
                    <div className="mt-1 flex justify-center">
                      <PiLockKeyLight size={20} />
                    </div>
                    <div className="mt-1">
                      <div className="mb-1 text-[8px] font-medium dark:text-white flex justify-between">
                        <span>6000/12000</span> <span>Redeem</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mb-4 dark:bg-gray-700">
                        <div className="bg-black h-1.5 rounded-full dark:bg-blue-500 w-1/2"></div>
                      </div>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
            <div className="mb-4">
              <p className="text-black text-xl font-normal">
                <span className="text-black font-extrabold">Benefits :</span> 7
                Benefits that add exclusive discounts, subscription perks, and
                event access
              </p>
            </div>
            <div className="mb-4 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
                <RiDiscountPercentFill size={22} />
              </span>
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  1. 5% Discount on Bookings:
                </span>{" "}
                An increased 5% discount on all hostel bookings through Mixdorm.
              </p>
            </div>
            <div className="mb-2 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
                <PiFireSimpleFill size={22} />
              </span>
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  2. Free Mix Mate Subscription (1 Month):
                </span>{" "}
                Access to Mix Mate for one month, helping users find like-minded
                travelers
              </p>
            </div>
            <div className="mb-2 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
                <FaCalendarAlt size={22} />
              </span>
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  3. Priority Access to Mix Events:
                </span>{" "}
                Early access to events and activities listed on Mixdorm, with
                exclusive sign-up options.
              </p>
            </div>
            <div className="mb-2 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
                <FaAward size={22} />
              </span>{" "}
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">  
                  4. 3x Reward Points on Every Booking:
                </span>{" "}
                Triple points on bookings, allowing users to reach rewards
                faster and upgrade to Nomad.
              </p>
            </div>
            <div className="mb-2 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
               <Image src="https://mixdorm.s3.ap-south-1.amazonaws.com/front-images/access.png" className="w-7 h-7" alt="mixsplit"/>
              </span>{" "}
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  5. Free Mix Split:
                </span>{" "}
                Users can access the Mix Split feature for easy bill-splitting
                with travel buddies.
              </p>
            </div>
            <div className="mb-2 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
                <BsFillGiftFill size={22} />
              </span>{" "}
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  6. Special Adventurer-Only Gift Cards:
                </span>{" "}
                Receive gift cards from popular brands (e.g., Amazon, Starbucks)
                for booking milestones or as tier rewards.
              </p>
            </div>
            <div className="mb-2 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
              <Image src="https://mixdorm.s3.ap-south-1.amazonaws.com/front-images/Aiman.png" className="w-8 h-7" alt="Ai"/>
              </span>{" "}
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  7. 1 Free Monthly AI Tool Access:
                </span>{" "}
                Access to a premium AI-powered tool for one month, offering
                recommendations on the best experiences based on personal
                interests.
              </p>
            </div>
            <div className="flex justify-between mt-7 mb-3">
              <div>
                <h4 className="font-extrabold text-black">
                  Wallet History &nbsp;&nbsp;&nbsp;
                  <span className="text-primary-blue underline">
                    My Point History
                  </span>
                </h4>
              </div>

              <div>
                <Link
                  href="#"
                  className="text-lg font-semibold text-black hover:text-sky-blue-750"
                >
                  See All
                </Link>
              </div>
            </div>
            <div className="mb-4">
              <div className="flex py-3 border-b border-grey-600 justify-between">
                <div>
                  <p className="text-[16px] font-bold">
                    Booking Points-{" "}
                    <span className="font-normal">
                      Get 100 Point On booking
                    </span>
                  </p>
                  <p className="text-[16px] font-normal text-[#AAACAE]">
                    5 min ago
                  </p>
                </div>
                <div className="rounded-full bg-[#D9F9F6] text-center p-[12px]">
                  <p className="text-xs text-black font-normal">Points</p>
                  <p className="text-sm text-black font-semibold">100</p>
                </div>
              </div>
              <div className="flex py-3 border-b border-grey-600 justify-between">
                <div>
                  <p className="text-[16px] font-bold">
                    Event Points-{" "}
                    <span className="font-normal">
                      Get 100 Point On booking
                    </span>
                  </p>
                  <p className="text-[16px] font-normal text-[#AAACAE]">
                    5 min ago
                  </p>
                </div>
                <div className="rounded-full bg-[#D9F9F6] text-center p-[12px]">
                  <p className="text-xs text-primary-blue font-normal">
                    Points
                  </p>
                  <p className="text-sm text-primary-blue font-semibold">100</p>
                </div>
              </div>
              <div className="flex py-3 border-b border-grey-600 justify-between">
                <div>
                  <p className="text-[16px] font-bold">
                    Mix Ride Points-{" "}
                    <span className="font-normal">
                      Get 100 Point On booking
                    </span>
                  </p>
                  <p className="text-[16px] font-normal text-[#AAACAE]">
                    5 min ago
                  </p>
                </div>
                <div className="rounded-full bg-[#D9F9F6] text-center p-[12px]">
                  <p className="text-xs text-primary-blue font-normal">
                    Points
                  </p>
                  <p className="text-sm text-primary-blue font-semibold">100</p>
                </div>
              </div>
              <div className="flex py-3 border-b border-grey-600 justify-between">
                <div>
                  <p className="text-[16px] font-bold">
                    Mix Creator Points-{" "}
                    <span className="font-normal">
                      Get 100 Point On booking
                    </span>
                  </p>
                  <p className="text-[16px] font-normal text-[#AAACAE]">
                    5 min ago
                  </p>
                </div>
                <div className="rounded-full bg-[#D9F9F6] text-center p-[12px]">
                  <p className="text-xs text-primary-blue font-normal">
                    Points
                  </p>
                  <p className="text-sm text-primary-blue font-semibold">100</p>
                </div>
              </div>
              <div className="flex py-3 border-b border-grey-600 justify-between">
                <div>
                  <p className="text-[16px] font-bold">
                    Booking Points-{" "}
                    <span className="font-normal">
                      Get 100 Point On booking
                    </span>
                  </p>
                  <p className="text-[16px] font-normal text-[#AAACAE]">
                    5 min ago
                  </p>
                </div>
                <div className="rounded-full bg-[#D9F9F6] text-center p-[12px]">
                  <p className="text-xs text-primary-blue font-normal">
                    Points
                  </p>
                  <p className="text-sm text-primary-blue font-semibold">100</p>
                </div>
              </div>
              <div className="flex py-3 border-b border-grey-600 justify-between">
                <div>
                  <p className="text-[16px] font-bold">
                    Event Points-{" "}
                    <span className="font-normal">
                      Get 100 Point On booking
                    </span>
                  </p>
                  <p className="text-[16px] font-normal text-[#AAACAE]">
                    5 min ago
                  </p>
                </div>
                <div className="rounded-full bg-[#D9F9F6] text-center p-[12px]">
                  <p className="text-xs text-primary-blue font-normal">
                    Points
                  </p>
                  <p className="text-sm text-primary-blue font-semibold">100</p>
                </div>
              </div>
            </div>
          </div> 
  </>
  );
};

export default Adventurer;
