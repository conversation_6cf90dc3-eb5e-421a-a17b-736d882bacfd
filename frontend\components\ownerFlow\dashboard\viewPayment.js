/* eslint-disable no-constant-binary-expression */
import {
  EditPayment<PERSON><PERSON>,
  RoomList<PERSON>pi,
} from "@/services/ownerflowServices";
import { getItemLocalStorage } from "@/utils/browserSetting";
import React, { useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";
import countries from "world-countries";
import Loader from "@/components/loader/loader";

const Viewpayment = ({
  editId,
}) => {
  const [paymentData, setPaymentData] = useState({
    userName: "",
    date: "",
    room: "",
    amount: "",
    currency: "",
  });
  const [roomList, setRoomList] = useState([]);
  const [currencyData, setCurrencyData] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const isFirstRender = useRef(null);
  const id = getItemLocalStorage("hopid");

  const fetchList = async (id) => {
    setIsLoading(true);
    try {
      const response = await RoomList<PERSON>pi(id, 1, 100);
      if (response.status === 200) {
        setRoomList(response.data.data.rooms);
      } else {
        // Handle error response
        toast.error("Failed to fetch rooms");
      }
    } catch (error) {
      console.log("Error fetching rooms:", error);
      toast.error("Error fetching rooms");
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, "0"); // Add leading zero
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Add leading zero, months are 0-indexed
    const year = date.getFullYear();

    return `${year}-${month}-${day}`;
  };

  const fetchPaymentData = async () => {
    setIsLoading(true);
    try {
      const response = await EditPaymentApi(editId);
      if (response.status === 200) {
        setPaymentData({
          userName: response?.data?.data?.gusetUser,
          date: formatDate(response?.data?.data?.paymentDate),
          room: roomList.find(
            (data) => data?._id === response?.data?.data?.room
          )?.name,
          amount: response?.data?.data?.amount,
          currency: response?.data?.data?.currency,
        });
      } else {
        // Handle error response
        toast.error("Failed to fetch paymentData");
      }
    } catch (error) {
      console.log("Error fetching paymentData:", error);
      toast.error("Error fetching paymentData");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      if (id && !isFirstRender.current) {
        try {
          await fetchList(id); // Wait for fetchList to complete
        } catch (error) {
          console.error("Error fetching data:", error);
        }
      } else {
        isFirstRender.current = false;
      }
    };

    fetchData(); // Call the async function
  }, [id]);

  useEffect(() => {
    if (roomList.length > 0 && editId) {
      fetchPaymentData();
    }
  }, [roomList, editId]);

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);
      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);

  const getCurrencySymbol = (currencyCode) => {
    return currencyData[currencyCode] || currencyCode;
  };
  return (
    <>
      <Loader open={isLoading} />
      <div className='max-w-[780px] mx-auto pb-5'>
        <div className='grid sm:grid-cols-[1.4fr_3fr] grid-cols-2 sm:gap-4 gap-3 font-inter'>
          <div>
            <p className='font-bold text-black sm:text-base text-xs mb-5'>
              Name :
            </p>
            <p className='font-bold text-black sm:text-base text-xs mb-5'>
              Date :
            </p>
            <p className='font-bold text-black sm:text-base text-xs mb-5'>
              Room Type :
            </p>
            <p className='font-bold text-black sm:text-base text-xs mb-5'>
              Currency :
            </p>
            <p className='font-bold text-black sm:text-base text-xs mb-5'>
              Amount Paid:
            </p>
          </div>
          <div>
            <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
              {paymentData.userName || " - "}
            </p>
            <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
              {paymentData.date
                ? new Date(paymentData.date)?.toLocaleDateString("en-GB", {
                    day: "2-digit",
                    month: "2-digit",
                    year: "numeric",
                  })
                : "-" || "-"}
            </p>
            <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
              {paymentData?.room?.value || " - "}
            </p>
            <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
              {paymentData.currency || " - "}
            </p>
            <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
              {getCurrencySymbol(paymentData.currency)}{" "}
              {paymentData.amount || " - "}
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default Viewpayment;
