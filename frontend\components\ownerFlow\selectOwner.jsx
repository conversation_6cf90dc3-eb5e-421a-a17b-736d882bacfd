 
import Image from "next/image";
import React, { useState } from "react";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import { useRouter } from "next/router";
import { setItemLocalStorage } from "@/utils/browserSetting";

const SelectOwner = ({propertyId}) => {
  const [selectedProperty, setSelectedProperty] = useState("single");
  const router = useRouter();

  const handlePropertySelect = (type) => {
    setSelectedProperty(type);
  };

  return (
    <div className="min-h-screen flex flex-col items-center bg-[#F7F7F7] sm:pb-[10rem] pb-10">
      <div className="w-full max-w-3xl p-6 mb-10 bg-white shadow-md rounded-3xl md:p-14">
        <h2 className="py-4 mb-8 text-2xl font-semibold text-center">
          👋Hi,<span className="text-primary-blue">Namastey Mumbai</span> Your
          Verification Successfully
        </h2>
        <div className="flex justify-center mb-8">
          <Image
            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/ownertype.svg`}
            alt="Verification Illustration"
            width={349}
            height={222}
            loading="lazy"
          />
        </div>
        <h3 className="mb-8 text-2xl font-bold text-center">
          Select Owner Type
        </h3>
        <div className="flex flex-col justify-center py-6 mb-8 space-y-4 md:flex-row md:space-y-0 md:space-x-10">
          <div
            className={`relative p-4 w-full md:w-48 h-44 flex items-center justify-center rounded-4xl cursor-pointer transition duration-200 ${selectedProperty === "single" ? "bg-[#40E0D0] text-black font-semibold" : "bg-white border text-black font-semibold"}`}
            onClick={() => handlePropertySelect("single")}
          >
            Single Property
            {selectedProperty === "single" && (
              <CheckCircleIcon className="absolute top-[10%] right-[10%] transform translate-x-2 -translate-y-2 text-black" />
            )}
          </div>
          <div
            className={`relative p-4 w-full md:w-48 h-44 flex items-center justify-center rounded-4xl cursor-pointer transition duration-200 ${selectedProperty === "multiple" ? "bg-[#40E0D0] text-black font-semibold" : "bg-white border text-black font-semibold"}`}
            onClick={() => handlePropertySelect("multiple")}
          >
            Multiple Property
            {selectedProperty === "multiple" && (
              <CheckCircleIcon className="absolute top-[10%] right-[10%] transform translate-x-2 -translate-y-2 text-black" />
            )}
          </div>
        </div>
        <div className="flex flex-col items-center space-y-4">
          <button
            className="w-full max-w-xl bg-[#40E0D0] text-black font-semibold py-4 rounded-4xl hover:bg-[#40E0D0] transition duration-200"
            onClick={() => {
              router?.push("/owner/dashboard");
              setItemLocalStorage("hopid", propertyId);
            }}
          >
            Select
          </button>
        </div>
      </div>
    </div>
  );
};

export default SelectOwner;
