// components/HostelCardSkeleton.js
export const HostelCardSkeleton = () => (
  <div className="rounded-3xl w-full border border-slate-105 font-manrope overflow-hidden animate-pulse">
    {/* Image placeholder - matches your 200px height */}
    <div className="w-full h-[200px] bg-gray-200 rounded-t-3xl"></div>
    
    {/* Content placeholder */}
    <div className="xl:p-4 px-4 pb-4 pt-4 rounded-b-3xl h-[170px]">
      {/* Title placeholder */}
      <div className="h-6 w-3/4 bg-gray-200 rounded mb-3"></div>
      
      {/* Location placeholder */}
      <div className="flex items-center mb-4">
        <div className="h-4 w-4 bg-gray-200 rounded-full mr-2"></div>
        <div className="h-3 w-1/2 bg-gray-200 rounded"></div>
      </div>
      
      {/* Features placeholder */}
      <div className="flex items-center mb-4">
        <div className="h-4 w-20 bg-gray-200 rounded mr-2"></div>
        <div className="flex gap-2">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-5 w-5 bg-gray-200 rounded-full"></div>
          ))}
        </div>
      </div>
      
      {/* Price and button placeholder */}
      <div className="flex justify-between items-center mt-4">
        <div className="h-6 w-1/3 bg-gray-200 rounded"></div>
        <div className="h-8 w-1/4 bg-gray-200 rounded-full"></div>
      </div>
    </div>
  </div>
);