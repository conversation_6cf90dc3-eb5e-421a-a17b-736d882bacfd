// "use client";
// import EditHostel from "@/components/superadmin/EditHostel";
// import React, { useState } from "react";
// import { FaChevronDown, FaChevronUp } from "react-icons/fa";
// import Image from "next/image";
// import Link from "next/link";

// const HostelEditForm = ({ onClose }) => {
//   const [activeTab, setActiveTab] = useState(0);
//   const [activeForm, setActiveForm] = useState("editHostel");
//   const handleTab = (index) => {
//     setActiveTab(index);
//   };

//   const [isCountryDropdownOpen, setIsCountryDropdownOpen] = useState(false);
//   const [selectedCountry, setSelectedCountry] = useState("India");

//   const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false);
//   const [selectedStatus, setSelectedStatus] = useState("Verify");

//   const [isOfferDropdownOpen, setIsOfferDropdownOpen] = useState(false);
//   const [selectedOffer, setSelectedOffer] = useState("Discount");

//   // Toggle functions
//   const toggleCountryDropdown = () =>
//     setIsCountryDropdownOpen(!isCountryDropdownOpen);
//   const toggleStatusDropdown = () =>
//     setIsStatusDropdownOpen(!isStatusDropdownOpen);
//   const toggleOfferDropdown = () =>
//     setIsOfferDropdownOpen(!isOfferDropdownOpen);

//   // Option select handlers
//   const handleCountrySelect = (country) => {
//     setSelectedCountry(country);
//     setIsCountryDropdownOpen(false);
//   };

//   const handleStatusSelect = (status) => {
//     setSelectedStatus(status);
//     setIsStatusDropdownOpen(false);
//   };

//   const handleOfferSelect = (offer) => {
//     setSelectedOffer(offer);
//     setIsOfferDropdownOpen(false);
//   };

//   return (
//     <div>
//       <div className="lg:pl-[255px] md:pl-[180px] sm:pl-[10px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth">
//         <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins">
//           Banner Edit
//         </h2>
        
          

//           <div className="mt-10 w-full md:w-[90%] sm:w-[95%] mx-auto ">
           
           
           
//                 <div className="flex flex-wrap items-center justify-between w-full gap-y-5 pl-0 md:pl-2 lg:pl-0">
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Room Name
//                     </label>
//                     <input
//                       type="text"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                       placeholder="#banana"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Room Type
//                     </label>
//                     <input
//                       type="text"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                       placeholder="Standard room"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Total beds
//                     </label>
//                     <input
//                       type="text"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                       placeholder="54"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Ensuite
//                     </label>
//                     <input
//                       type="text"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                       placeholder="14"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Rate
//                     </label>
//                     <input
//                       type="text"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                       placeholder="45"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Currency
//                     </label>
//                     <input
//                       type="text"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                       placeholder="152"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Room Photo
//                     </label>
//                     <div className="relative w-full">
//                       <input
//                         type="file"
//                         className="relative z-30 w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none opacity-0 bg-sky-blue-25 h-9"
//                       />
//                       <div className="absolute flex items-center justify-between w-full top-2 text-gray-50">
//                         <div className="rounded-lg border-2 border-gray-200">
//                           <Image
//                             src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`}
//                             alt="img"
//                             width={140}
//                             height={50}
//                             title="img"
//                             className="object-contain w-fit h-fit max-w-24 max-h-36 flex items-center rounded-md"
//                           />
//                         </div>
//                         <span className="text-sm font-semibold pl-0 md:pl-4 lg:pl-0">
//                           Banana Room.Jpj
//                         </span>
//                         <span className="text-sm font-semibold">252 KB</span>
//                       </div>
//                     </div>
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Room Description
//                     </label>
//                     <input
//                       type="text"
//                       placeholder="Closed"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                     />
//                   </div>
//                   <div className="w-full sm:w-[48%]">
//                     <label className="text-sm font-medium text-gray-500">
//                       Rate Type
//                     </label>
//                     <input
//                       type="text"
//                       placeholder="Verified"
//                       className="w-full px-3 py-2 mt-2 text-sm text-black border border-gray-200 rounded outline-none bg-sky-blue-25 h-9 placeholder-black placeholder:font-semibold"
//                     />
//                   </div>
//                 </div>

//                 <div className="flex items-center justify-center gap-4 mt-5">
//                   <Link
//                     href={"/superadmin/dashboard/hostel"}
//                     className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-20 py-2 text-sm font-semibold  border  rounded  border-gray-200 text-black-100`}
//                   >
//                     Cancel
//                   </Link>
//                   <button
//                     type="button"
//                     className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-20  py-2 text-sm font-semibold  border  rounded  bg-sky-blue-650 text-white `}
//                   >
//                     Save
//                   </button>
//                 </div>
           
            
            
          
//         </div>
//       </div>
//     </div>
//   );
// };

// export default HostelEditForm;



"use client";
import Link from "next/link";
import React from "react";

const addSplitBills = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth h-screen dark:bg-[#171616]">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        Add Split Bills
      </h2>
      <div className="bg-white border flex justify-center mt-5  rounded-xl h-auto  px-4 md:px-5 lg:px-1 py-7 md:py-16 lg:py-8 dark:bg-black dark:border-none">
        <div className="bg-white w-full max-w-3xl dark:bg-black">
          <form>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div className="relative">
                <label className="block font-semibold text-sm font-poppins text-black/40 dark:text-[#B6B6B6]">
                  Headline Text
                </label>
                <input
                  type="text"
                  className="mt-1 p-2 w-full font-medium text-sm font-poppins rounded-md bg-[#EEF9FF] dark:bg-transparent dark:placeholder:text-[#757575]"
                  placeholder="Enter Text"
                />
                
              </div>



              <div className="relative">
                <label className="block font-semibold text-sm font-poppins text-black/40 dark:text-[#B6B6B6]">
                  Image
                </label>
                <input
                  type="text"
                  className="mt-1 p-2 font-medium text-sm font-poppins w-full  rounded-md bg-[#EEF9FF]  dark:bg-transparent dark:placeholder:text-[#757575]"
                  placeholder="Upload Image"
                />
               
              </div>

            </div>

            {/* Buttons */}
            <div className="flex flex-wrap items-center justify-center space-x-3 my-8">
              <Link href={"/superadmin/dashboard/split-bill"}
                type="button"
                className="flex items-center justify-center py-2 border w-40 md:w-56 border-gray-300 text-black font-medium text-sm font-poppins rounded-md mt-2 dark:text-gray-100"
              >
                Cancel
              </Link>
              <button
                type="submit"
                className="px-6 py-2 w-40 md:w-56 bg-sky-blue-650 text-white font-medium text-sm font-poppins rounded-md mt-2"
              >
                Add
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default addSplitBills;
