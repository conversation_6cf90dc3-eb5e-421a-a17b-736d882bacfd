import HomeScreen from "@/components/home";
import { getContentSchemaOtherPages } from "@/lib/schema/contentSchemaOtherPages";
import { getFaqSchema } from "@/lib/schema/faqSchema";
import { faqData } from "@/utils/faqData";
import Head from "next/head";

export default function Home() {
  const contentSchema = getContentSchemaOtherPages({
    title: "Mixdorm | Best Affordable Hostel Booking Worldwide",
    description:
      "Find and book top-rated hostels, dormitories, and budget stays across 15+ countries with Mixdorm. Whether you're a solo backpacker or a budget-conscious traveler, explore vibrant social hostels, featured stays, and event-driven experiences worldwide with real reviews and smart filters.",
    authorName: "Mixdorm",
    datePublished: "2023-07-17",
  });
  const faqsData = faqData["landingPage"] || null;
  const faqSchema = faqsData ? getFaqSchema(faqsData) : null;
  return (
    <>
      <Head>
        {contentSchema && (
          <script
            type='application/ld+json'
            dangerouslySetInnerHTML={{ __html: JSON.stringify(contentSchema) }}
          />
        )}
        {faqSchema && (
          <script
            type='application/ld+json'
            dangerouslySetInnerHTML={{ __html: JSON.stringify(faqSchema) }}
          />
        )}
      </Head>
      <HomeScreen />
    </>
  );
}
