import { saveFcmTokenService ,removeFcmTokenService} from '../services/firebase.js';
import Response from '../utills/response.js'; 
const saveFcmToken = async (req, res) => {
    try {      
        const { token,userId } = req.body;

        if (!token || !userId) {
            return Response.InternalServerError(res, 'Token and userId are required');
        }

        await saveFcmTokenService(token, userId);
        return Response.OK(res, {}, 'Token Save Success');
    } catch (error) {
        return Response.handleError(res, error, error.statusCode);
    }
};
const removeFcmToken = async (req, res) => {
    try {      
        const { token,userId } = req.body;

        if (!token || !userId) {
            return Response.InternalServerError(res, 'Token and userId are required');
        }

        await removeFcmTokenService(token, userId);
        return Response.OK(res, {}, 'Token Remove Success');
    } catch (error) {
        return Response.handleError(res, error, error.statusCode);
    }
};
export { saveFcmToken,removeFcmToken };
