import express from 'express';
import { cloudAdd, cloudGet,
    cloudRedirect,createProperty,
    getRoomType,getRatePlan,ARIUpdate,getBookingList ,GetBookingId} from '../controller/cloud.js';

const router = express.Router();
    router.post('/', cloudRedirect);

// Route to send a new message
router.post('/SetupProperty', cloudAdd);
router.post('/healthcheck', cloudAdd);
router.post('/GetRoomTypes', getRoomType);
router.post('/GetRatePlans', getRatePlan);
router.post('/ARIUpdate', ARIUpdate);
router.post('/GetBookingList', getBookingList);
router.post('/GetBookingId', GetBookingId);
router.post('/CancelBooking', cloudAdd);
router.post('/CreateProperty', createProperty);
router.post('/GetSubProperties', cloudAdd);
router.post('/DeleteRoom', cloudAdd);
router.post('/UpdateRoom', cloudAdd);
router.post('/UpdateTaxes', cloudAdd);
router.post('/DeleteRatePlan', cloudAdd);
router.post('/UpdateRatePlan', cloudAdd);

router.get('/', cloudGet);

export default router;
