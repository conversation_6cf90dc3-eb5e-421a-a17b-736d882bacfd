import React from "react";
import Image from "next/image";

const Hostridedetails = () => {
    return (
      <>
        <section className='w-full'>
          <h2 className='text-xl font-large text-black-230'>Web Check in Details</h2>
          <div className='w-full mt-5 bg-white shadow-4xl p-6 rounded-2xl border'>
            <div className='lg:w-[400px] sm:w-full mx-auto py-8'>
              <div className='grid sm:grid-cols-2 gap-4'>
                <div>
                  <label className='font-semibold'>From Where:</label>
                </div>
                <div>
                  <label className='font-normal'>Mumbai</label>
                </div>
                <div>
                  <label className='font-semibold'>Destination:</label>
                </div>
                <div>
                  <label className='font-normal'>Lonavala</label>
                </div>
                <div>
                  <label className='font-semibold'>Date:</label>
                </div>
                <div>
                  <label className='font-normal'>22/10/2024</label>
                </div>
                <div>
                  <label className='font-semibold'>Time:</label>
                </div>
                <div>
                  <label className='font-normal'>05:00am</label>
                </div>
                <div>
                  <label className='font-semibold'>Passenger:</label>
                </div>
                <div>
                  <label className='font-normal'>4</label>
                </div>
                <div>
                  <label className='font-semibold'>Mode of Transport:</label>
                </div>
                <div>
                <label className='font-normal'>Car</label>
                </div>
                <div>
                  <label className='font-semibold'>Price:</label>
                </div>
                <div>
                  <label className='font-normal'>54</label>
                </div>
                <div className="relative col-span-2">
                  <label className='font-semibold'>Car Details</label>
                </div>
                <div>
                  <label className='font-semibold'>Company:</label>
                </div>
                <div>
                <label className='font-normal'>Mercedes</label>
                </div>
                <div>
                  <label className='font-semibold'>Model:</label>
                </div>
                <div>
                <label className='font-normal'>Mercedes c200</label>
                </div>  
                <div>
                  <label className='font-semibold'>Registration Number:</label>
                </div>
                <div>
                <label className='font-normal'>MH21CE2104</label>
                </div> 
                <div>
                  <label className='font-semibold'>Passenger Capacity:</label>
                </div>
                <div>
                <label className='font-normal'>4</label>
                </div> 
                <div>
                  <label className='font-semibold'>Car Photo:</label>
                </div>
                <div>
                <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/MyStay.jpg`}
                    alt='Car'
                    title='Car'
                    width={64}
                    height={32}
                    className='object-cover'
                    loading='lazy'
                  />
                </div>  
              </div>
            </div>
          </div>
        </section>
      </>
    );
};
export default Hostridedetails;