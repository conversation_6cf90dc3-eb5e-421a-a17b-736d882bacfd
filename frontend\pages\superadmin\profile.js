/* eslint-disable react/no-unescaped-entities */
import React from "react";
import Link from "next/link";
import Image from "next/image";
import { FaPlus } from "react-icons/fa";

const WhoIsUsingMixdorm = () => {
  return (
    <section className="flex flex-col md:flex-row w-full h-screen bg-[#50C2FF] md:bg-white lg:bg-white py-24 md:py-0 lg:py-0">
      {/* Left Section */}
      <div className="w-full md:w-1/2 flex items-center justify-center bg-[#50C2FF] md:bg-white lg:bg-white">
        <div className="flex flex-col w-[90%] md:w-[68%] px-8 py-10 md:px-4 md:py-20 bg-white border shadow-2xl">
          <Link href="/" prefetch={false}>
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/logo.svg`}
              width={155}
              height={40}
              alt="Mixdorm"
              title="Mixdorm"
              className="object-contain w-fit h-fit max-w-[186px] max-h-11"
              loading="lazy"
            />
          </Link>

          <h2 className="mt-8 font-semibold text-black text-xl">
            Who's Using Mixdorm?
          </h2>
          <div className="flex justify-evenly items-center mt-8">
            <div className="flex items-center justify-center flex-col">
              <button className="bg-sky-blue-650 text-white h-12 w-12 rounded-full mb-2">
                A
              </button>
              <h6>Aayush Jain</h6>
            </div>
            <div className="flex items-center justify-center flex-col">
              <button className="border-sky-blue-650 border flex items-center justify-center bg-white text-sky-blue-650 h-12 w-12 rounded-full mb-2">
                <FaPlus />
              </button>
              <h6>Add</h6>
            </div>
          </div>
          <Link
            href=""
            prefetch={false}
            className="text-sky-blue-650 text-center underline my-10"
          >
            Manage Profile
          </Link>
          <span className="flex items-center justify-center mt-16 gap-1">
            You can Create up to{" "}
            <h6 className="text-sky-blue-650">4 Profiles</h6>
          </span>
        </div>
      </div>

      {/* Right Section */}
      <div className="hidden md:flex w-1/2 bg-[#50C2FF] items-end justify-start pt-10 pl-10">
        <Image
          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/superadmin_login_img.png`}
          width={720}
          height={1024}
          loading="lazy"
          alt="Admin dash"
          className="h-full w-full object-cover object-left-top"
        />
      </div>
    </section>
  );
};

export default WhoIsUsingMixdorm;
