/* eslint-disable react/no-unknown-property */
import React from "react";
import Modal from "@mui/material/Modal";
import { IoCloseCircleOutline } from "react-icons/io5";
import Link from "next/link";

const Pendingreview = ({
  openPendingreviewpopup,
  handleClosePendingreviewpopup,
}) => {
  const style = {
    position: "fixed",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: "100%",
    bgcolor: "background.paper",
    border: "2px solid #000",
    boxShadow: 24,
  };

  return (
    <>
      <Modal
        open={openPendingreviewpopup}
        onClose={handleClosePendingreviewpopup}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <div sx={style}>
          <div className="bg-white rounded-2xl max-w-[500px] mx-auto left-1/2 absolute w-[95%] top-1/2 -translate-y-1/2 -translate-x-1/2 text-center">
            <div className="md:p-6 p-4">
              <div className="flex items-center justify-end">
                <Link
                  href="/"
                  prefetch={false}
                  onClick={handleClosePendingreviewpopup}
                  className="text-black text-2xl hover:text-primary-blue"
                >
                  <IoCloseCircleOutline />
                </Link>
              </div>
              <h1 className="text-lg font-semibold text-black mb-4">
                Channel Manager Disconnection Pending Review
              </h1>
              <p className="text-base text-black font-semibold mt-3">
                Your request to disconnect the Channel Manager has been
                received. Please note that the review process may take up to one
                week (maximum) for approval. Once the disconnection status is
                approved, you will be able to proceed with deleting your
                profile.
              </p>
              <button className="bg-gray-300 hover:bg-bg-[#40E0D0] text-black font-medium py-3 px-8 rounded-lg shadow-md mb-4 w-full">
                Thank you for your patience!
              </button>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default Pendingreview;
