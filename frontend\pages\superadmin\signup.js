/* eslint-disable react/no-unescaped-entities */
// "use client";
// import Button from "@/components/superadmin/Button";
// import Image from "next/image";
// import React, { useState, useEffect } from "react";
// import dynamic from "next/dynamic";
// import Input from "@/components/superadmin/Input";
// import Label from "@/components/superadmin/Label";
// import axios from "axios";
// import { BASE_URL } from "../../utils/api";
// import toast, { Toaster } from "react-hot-toast";
// import { BiEdit } from "react-icons/bi";
// import { setToken } from "../../utils/browserSetting";
// const OTPInput = dynamic(() => import("otp-input-react"), { ssr: false });
// const ResendOTP = dynamic(
//   () => import("otp-input-react").then((mod) => mod.ResendOTP),
//   { ssr: false }
// );

// const AdminLogin = () => {
//   const [password, setPassword] = useState("");
//   const [email, setEmail] = useState("");
//   const [authCode, setAuthCode] = useState(false);
//   const [OTP, setOTP] = useState("");
//   const [loading, setLoading] = useState(false);
//   const [view, setView] = useState("login");

//   // useEffect(() => {
//   //   // Ensure this code only runs on the client
//   //   if (typeof window !== "undefined") {
//   //     // Add any client-side specific logic here if needed
//   //   }
//   // }, []);

//   const handleLogin = async (e) => {
//     e.preventDefault();
//     setLoading(true);
//     try {
//       const response = await axios.post(`${BASE_URL}/auth/login`, {
//         email,
//         password,
//       });
//       if (response.status === 201 && response.data.status) {
//         toast.success("OTP sent successfully");
//         setAuthCode(true);
//       } else {
//         toast.error("OTP sent failed: " + response.data.message);
//       }
//     } catch (error) {
//       toast.error(error.response ? error.response.data.message : error.message);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const handleSubmit = async (e) => {
//     e.preventDefault();
//     setLoading(true);
//     try {
//       const response = await axios.post(`${BASE_URL}/auth/verify-otp`, {
//         email,
//         otp: OTP,
//       });
//       if (response.status === 201 && response.data.status) {
//         setToken(response?.data?.data?.token);
//         toast.success("OTP verification successful");
//         setView("whoUse");
//       } else {
//         toast.error("OTP verification failed: " + response.data.message);
//       }
//     } catch (error) {
//       toast.error(
//         "Error during OTP verification: " +
//           (error.response ? error.response.data.message : error.message)
//       );
//     } finally {
//       setLoading(false);
//     }
//   };

//   return (
//     <>
//       <Toaster position="top-center" />
//       <section className="flex w-full h-screen">
//         <div className="bg-white w-1/2 flex items-center justify-center">
//           <div className="flex flex-col w-[68%] px-20 py-20 bg-white border">
//             {/* Left side content here */}
//             <Image
//               src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/logo.svg`}
//               width={155}
//               height={40}
//               alt="Mixdorm"
//               title="Mixdorm"
//               loading="lazy"
//               className={`object-contain w-fit h-fit max-w-[186px] max-h-11 ${
//                 setView === "whoUse" ? "mx-auto" : ""
//               }`}
//             />
//             {view === "login" ? (
//               authCode ? (
//                 <>
//                   <h2 className="mt-4 font-semibold text-black text-xl">
//                     Enter Authentication Code
//                   </h2>
//                   <p className="mt-3 text-sm font-normal text-gray-50/40">
//                     Verify your email
//                   </p>
//                   <form className="mt-4" onSubmit={handleSubmit}>
//                     <Label>Enter Code</Label>
//                     <OTPInput
//                       value={OTP}
//                       onChange={setOTP}
//                       autoFocus
//                       OTPLength={6}
//                       otpType="number"
//                       secure
//                       className="w-full text-xs input-otp font-light mb-5 px-4 py-2.5 mt-1 bg-transparent outline-none focus:outline-none placeholder:text-gray-50/40 text-gray-50"
//                     />
//                     <ResendOTP
//                       className="mt-5 mb-5 text-xs font-medium text-blue-500"
//                       onResendClick={() => console.log("Resend clicked")}
//                     />
//                     <Button
//                       text={
//                         loading ? (
//                           <div className="absolute loader-button">
//                             <div className="loader"></div>
//                           </div>
//                         ) : (
//                           "Submit"
//                         )
//                       }
//                       type="submit"
//                     />
//                   </form>
//                 </>
//               ) : (
//                 <>
//                   <h2 className="mt-4 font-semibold text-black text-xl">
//                     Administration Signup
//                   </h2>
//                   <p className="mt-3 text-sm font-normal text-gray-50/40">
//                     Signup your Account
//                   </p>
//                   <form className="mt-4" onSubmit={handleLogin}>
//                     <Label className="font-medium">Name</Label>
//                     <Input
//                       type="name"
//                       placeholder="Name"
//                       onChange={(e) => setEmail(e.target.value)}
//                     />
//                     <Label className="font-medium">Email</Label>
//                     <Input
//                       type="email"
//                       placeholder="Email"
//                       value={email}
//                       onChange={(e) => setEmail(e.target.value)}
//                     />
//                     <Label className="font-medium">Password</Label>
//                     <Input
//                       type="password"
//                       placeholder="Password"
//                       value={password}
//                       onChange={(e) => setPassword(e.target.value)}
//                     />
//                     <Button
//                       className="font-semibold text-base"
//                       text={
//                         loading ? (
//                           <div className="absolute loader-button">
//                             <div className="loader"></div>
//                           </div>
//                         ) : (
//                           "Super Admin Sign in"
//                         )
//                       }
//                       type="submit"
//                     />
//                   </form>
//                 </>
//               )
//             ) : (
//               <>
//                 <div className="absolute right-2 top-2 border-1.5 border-gray-300 flex justify-center items-center rounded-md p-0.5 font-light cursor-pointer text-gray-50 w-6 h-6">
//                   <BiEdit size={17} />
//                 </div>
//                 <h2 className="mt-4 font-semibold text-black text-xl">
//                   Who's Using Mixdrom?
//                 </h2>
//                 <div className="flex items-center justify-between w-[70%] mx-auto mt-7">
//                   <Link
//                     href="/admin/dashboard"
//                     className="w-fit"
//                     prefetch={false}
//                   >
//                     <div className="flex items-center justify-center text-base font-bold text-white bg-yellow-500 rounded-full w-14 h-14">
//                       A
//                     </div>
//                     <p className="mt-2 text-sm font-normal text-gray-50">
//                       Ayush Jain
//                     </p>
//                   </Link>
//                 </div>
//               </>
//             )}
//           </div>
//         </div>
//         <div className=" w-1/2 bg-[#50C2FF] flex items-end justify-start pt-10 pl-10">
//           <Image
//             src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/superadmin_login_img.png`}
//             width={720}
//             height={1024}
//             loading="lazy"
//             alt="Admin dash"
//             className="h-full w-full object-cover object-left-top"
//           />
//         </div>
//       </section>
//     </>
//   );
// };

// export default AdminLogin;

"use client";
import Button from "@/components/superadmin/Button";
import Image from "next/image";
import React, { useState } from "react";
import dynamic from "next/dynamic";
import Input from "@/components/superadmin/Input";
import Label from "@/components/superadmin/Label";
import axios from "axios";
import { BASE_URL } from "../../utils/api";
import toast, { Toaster } from "react-hot-toast";
import { BiEdit } from "react-icons/bi";
import { setToken } from "../../utils/browserSetting";
import { FiEye, FiEyeOff } from "react-icons/fi";
import Link from "next/link";

const OTPInput = dynamic(() => import("otp-input-react"), { ssr: false });
const ResendOTP = dynamic(
  () => import("otp-input-react").then((mod) => mod.ResendOTP),
  { ssr: false }
);

const AdminLogin = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [password, setPassword] = useState("");
  const [email, setEmail] = useState("");
  const [authCode, setAuthCode] = useState(false);
  const [OTP, setOTP] = useState("");
  const [loading, setLoading] = useState(false);
  const [view, setView] = useState("login");

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      const response = await axios.post(`${BASE_URL}/auth/login`, {
        email,
        password,
      });
      if (response.status === 201 && response.data.status) {
        toast.success("OTP sent successfully");
        setAuthCode(true);
      } else {
        toast.error("OTP sent failed: " + response.data.message);
      }
    } catch (error) {
      toast.error(error.response ? error.response.data.message : error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      const response = await axios.post(`${BASE_URL}/auth/verify-otp`, {
        email,
        otp: OTP,
      });
      if (response.status === 201 && response.data.status) {
        setToken(response?.data?.data?.token);
        toast.success("OTP verification successful");
        setView("whoUse");
      } else {
        toast.error("OTP verification failed: " + response.data.message);
      }
    } catch (error) {
      toast.error(
        "Error during OTP verification: " +
          (error.response ? error.response.data.message : error.message)
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-[#50C2FF] lg:bg-white md:bg-white">
      <Toaster position="top-center" />
      <section className="flex flex-col md:flex-row w-full h-screen py-20 lg:py-0 md:py-0">
        {/* Left Section */}
        <div className="w-full md:w-1/2 bg-white shadow-2xl border-x-8 border-[#50C2FF] md:border-none lg:border-none flex items-center justify-center md:bg-white">
          <div className="flex flex-col w-[90%] md:w-[68%] px-8 py-10 md:px-4 md:py-20  border">
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/logo.svg`}
              width={155}
              height={40}
              alt="Mixdorm"
              title="Mixdorm"
              loading="lazy"
              className={`object-contain w-fit h-fit max-w-[186px] max-h-11 ${
                setView === "whoUse" ? "mx-auto" : ""
              }`}
            />
            {view === "login" ? (
              authCode ? (
                <>
                  <h2 className="mt-4 font-semibold text-black text-xl">
                    Enter Authentication Code
                  </h2>
                  <p className="mt-3 text-sm font-normal text-gray-500">
                    Verify your email
                  </p>
                  <form className="mt-4" onSubmit={handleSubmit}>
                    <Label>Enter Code</Label>
                    <OTPInput
                      value={OTP}
                      onChange={setOTP}
                      autoFocus
                      OTPLength={6}
                      otpType="number"
                      secure
                      className="w-full text-xs input-otp font-light mb-5 px-4 py-2.5 mt-1 bg-transparent outline-none focus:outline-none placeholder:text-gray-500 text-gray-800"
                    />
                    <ResendOTP
                      className="mt-5 mb-5 text-xs font-medium text-blue-500"
                      onResendClick={() => console.log("Resend clicked")}
                    />
                    <Button
                      text={
                        loading ? (
                          <div className="absolute loader-button">
                            <div className="loader"></div>
                          </div>
                        ) : (
                          "Submit"
                        )
                      }
                      type="submit"
                    />
                  </form>
                </>
              ) : (
                <div>
                  <h2 className="mt-4 font-semibold text-black text-xl">
                    Administration Signup
                  </h2>
                  <p className="mt-3 text-sm font-normal text-gray-500">
                    Signup your Account
                  </p>
                  <form className="mt-4" onSubmit={handleLogin}>
                    <Label className="font-medium">Name</Label>
                    <Input
                      type="name"
                      placeholder="Name"
                      // onChange={(e) => setName(e.target.value)}
                    />
                    <Label className="font-medium">Email</Label>
                    <Input
                      type="email"
                      placeholder="Email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                    />
                    <Label className="font-medium">Password</Label>
                    {/* <Input
                      type="password"
                      placeholder="Password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      
                    /> */}
                    <div style={{ position: "relative"}}>
                      <Input
                        type={showPassword ? "text" : "password"}
                        placeholder="Password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        style={{ width: "100%" }}
                      />
                      <span
                        style={{
                          position: "absolute",
                          right: "12px",
                          top: "22px",
                          transform: "translateY(-50%)",
                          cursor: "pointer",
                        }}
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? <FiEye /> : <FiEyeOff />}
                        
                      </span>
                    </div>
                    <Button
                      className="font-semibold text-base"
                      text={
                        loading ? (
                          <div className="absolute loader-button">
                            <div className="loader"></div>
                          </div>
                        ) : (
                          "Super Admin Sign in"
                        )
                      }
                      type="submit"
                    />
                  </form>
                </div>
              )
            ) : (
              <>
                <div className="absolute right-2 top-2 border-1.5 border-gray-300 flex justify-center items-center rounded-md p-0.5 font-light cursor-pointer text-gray-50 w-6 h-6">
                  <BiEdit size={17} />
                </div>
                <h2 className="mt-4 font-semibold text-black text-xl">
                  Who's Using Mixdrom?
                </h2>
                <div className="flex items-center justify-between w-[70%] mx-auto mt-7">
                  <Link
                    href="/admin/dashboard"
                    className="w-fit"
                    prefetch={false}
                  >
                    <div className="flex items-center justify-center text-base font-bold text-white bg-yellow-500 rounded-full w-14 h-14">
                      A
                    </div>
                    <p className="mt-2 text-sm font-normal text-gray-500">
                      Ayush Jain
                    </p>
                  </Link>
                </div>
              </>
            )}
          </div>
        </div>
        {/* Right Section */}
        <div className="hidden md:flex w-1/2 bg-[#50C2FF] items-end justify-start pt-10 pl-10">
          <Image
            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/superadmin_login_img.png`}
            width={720}
            height={1024}
            loading="lazy"
            alt="Admin dash"
            className="h-full w-full object-cover object-left-top"
          />
        </div>
      </section>
    </div>
  );
};

export default AdminLogin;
