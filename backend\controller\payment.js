
import dotenv from 'dotenv';
import Payment from "../models/payment.js";
import Razorpay from "razorpay"
import crypto from 'crypto'
import Booking from "../models/bookings.js"
import property from '../models/properties.js';
import { getNextSubBookingId, saveBooking } from "../services/booking.js"
import { bookEvent } from "../services/eventBooking.js"
import Room from "../models/room.js"
// import PropertyPayments from '../models/propertyPayments.js'; 
import mongoose from 'mongoose';
import Response from '../utills/response.js';
dotenv.config();
import EventBooking from "../models/eventBooking.js"
import { cloudBedsBookingSend, sendToEzeeXmlBooking } from '../utills/chanelManager.js';
import subBookingModel from '../models/subBookings.js';
import RatePlan from '../models/roomRatePlans.js';
// import PDFDocument from 'pdfkit';
// import fs from 'fs';
// import path from 'path';
// // Initialize Razorpay instance
const razorpay = new Razorpay({
    key_id: process.env.RAZORPAY_KEY_ID,
    key_secret: process.env.RAZORPAY_KEY_SECRET
});

// Create an Order
export const createOrder = async (req, res) => {
    const { amount, currency, receipt } = req.body;
    // Step 1: Create a customer in Razorpay
    console.log(req.user)
    // let customerid
    // let existingCustomer = await razorpay.customers.all({ email: '<EMAIL>' });
    // const existing = existingCustomer.items.find(customer => customer.email === '<EMAIL>');

    // console.log("existingCustomer", existingCustomer)
    // if (existing) {
    //     existingCustomer = existingCustomer.items[0]; 
    //     customerid = existingCustomer.id
    // } else {
    //     const newCustomer = await razorpay.customers.create({
    //         name: `${req.user.name.first} ${req.user.name.last}`,
    //         email: '<EMAIL>',
    //     });
    //     customerid = newCustomer.id
    // }
    const options = {
        amount: Math.round(amount * 100), // Ensure amount is an integer
        currency: currency || "INR",
        receipt: "receipt#1",
        //  customer_id: customerid,
        // name: "Mixdorm",
        // description: "Test Transaction",
        notes: {
            // customer_id: customerid,
            name: `${req.user.name.first} ${req.user.name.last}`,
            email: req.user.email,
            contact: req.user.phone,
        },
        // prefill: {
        //     name: 'Priyanka',
        //     email: '<EMAIL>',
        //     contact: '9998538026',
        // },
        payment_capture: 1 // 1 for automatic capture, 0 for manual capture
    };

    try {
        const order = await razorpay.orders.create(options);
        res.status(201).json(order);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
};


export const paymentVerification = async (req, res) => {
    const {
        signature,
        paymentType,
        payment_id,
        amount,
        currency,
        user_id: payloadUserId,
        checkIn,
        checkOut,
        paymentFor,
        order_id,
        rooms, property
    } = req.body;

    const user_id = payloadUserId || req.user._id;
    
    const cinDate = new Date(checkIn)
    const cin =  cinDate.setHours(0, 0, 0, 0);

    const coutDate = new Date(checkOut)
    const cout = coutDate.setHours(0, 0, 0, 0);
    const nights = (cout - cin) / (1000 * 60 * 60 * 24);

    // Step 1: Razorpay Signature Verification
    const hmac = crypto.createHmac('sha256', process.env.RAZORPAY_KEY_SECRET);
    hmac.update(`${order_id}|${payment_id}`);
    const generated_signature = hmac.digest('hex');

    if (generated_signature !== signature) {
        await savePayment({
            user: user_id,
            orderId: order_id,
            paymentId: payment_id,
            signature,
            amount,
            currency,
            status: 'Payment failed',
            paymentType,
            paymentFor
        });

        return res.status(400).json({ status: 'Payment failed' });
    }

    try {
        // Step 2: Calculate Total Room Cost
        const totalRoomAmount = rooms.reduce((sum, room) => sum + Number(room.amount), 0);

        // Step 3: Breakdown
        const advancePercentage = 15;
        const gstPercentage = 3;
        const gatewayChargePercentage = 18;

        const baseAdvance = (totalRoomAmount * advancePercentage) / 100;      // 15%
        const gst = (baseAdvance * gstPercentage) / 100;                      // 3% GST on advance
        const paymentGatewayCharge = (baseAdvance + gst) * gatewayChargePercentage / 100;

        const payableNow = parseFloat((baseAdvance + gst + paymentGatewayCharge).toFixed(2));
        const payOnArrival = parseFloat((totalRoomAmount * 85 / 100).toFixed(2));

        // Step 4: Create Booking
        const latestBooking = await Booking.findOne({}).sort({ bookingId: -1 }).select('bookingId');
        const bookingId = latestBooking?.bookingId ? latestBooking.bookingId + 1 : 1001;

        const propertyId = (await Room.findById(rooms[0].roomId).select('property')).property;
        console.log("payableNow", payableNow)
        console.log("payableOnArrival", payOnArrival)
        console.log("room total", totalRoomAmount)
        console.log("baseAdvance", baseAdvance)
        console.log("gst", gst)
        console.log("paymentGatewayCharge", paymentGatewayCharge)
        const mainBooking = await Booking.create({
            user: user_id,
            bookingId,
            property: propertyId,
            checkInDate: checkIn,
            checkOutDate: checkOut,
            status: 'confirmed',
            paymentStatus: 'paid',
            currency,
            totalAmount: totalRoomAmount,
            paidAmount: payableNow,             // 💰 Now exactly from `amount`
            payOnArrival, // 💰 85% of total
            rooms: rooms.map(r => r.roomId),
            isActive: true,
            baseAdvance,
            gst,
            paymentGatewayCharge,
            nights
        });

        const roomCount = rooms.length;
        const payNowPerRoom = payableNow / roomCount;
        const payOnArrivalPerRoom = payOnArrival / roomCount;
        const totalPerRoom = totalRoomAmount / roomCount;

        mainBooking.subBookings = [];

        for (const room of rooms) {
            console.log("room", room)
            const roomDetails = await Room.findById(room.roomId).populate('property', 'name images');
            const ratePlan = await RatePlan.findOne({ otaRateId: room?.rateTypeId })
            const subBooking = await subBookingModel.create({
                user: user_id,
                bookingId: mainBooking._id,
                subBookingId: await getNextSubBookingId(),
                room: room.roomId,
                roomNumber: roomDetails.roomNumber || '',
                bedType: room.bedType || 'standard',
                beds: room.beds,
                checkInDate: checkIn,
                checkOutDate: checkOut,
                rate: {
                    baseAmount: room.amount,
                    taxAmount: 0,
                    serviceFee: 0,
                    totalAmount: room.amount,
                    currency
                },
                totalAmount: totalPerRoom,
                paidAmount: payNowPerRoom,
                payNow: payNowPerRoom,
                payOnArrival: payOnArrivalPerRoom,
                baseAdvance: baseAdvance / roomCount,
                gst: gst / roomCount,
                paymentGatewayCharge: paymentGatewayCharge / roomCount,
                ratePlan: {
                    name: ratePlan.name || '',
                    id: ratePlan.otaRateId || '',
                },
                status: 'confirmed',
                beds: room.beds,
                totalPaidAmount: payableNow / roomCount,
                property,
                nights
            });

            mainBooking.subBookings.push(subBooking._id);
        }

        await mainBooking.save();

        // Step 6: Save Payment Info
        await savePayment({
            user: user_id,
            bookingId: mainBooking._id,
            orderId: order_id,
            paymentId: payment_id,
            signature,
            amount: payableNow,
            currency,
            status: 'success',
            paymentType,
            paymentFor
        });

        const bookingDetails = await Booking.aggregate([
            {
                $match: { _id: new mongoose.Types.ObjectId(mainBooking._id) }
            },
            {
                $project: {
                    bookingId: 1,
                    checkInDate: 1,
                    checkOutDate: 1,
                    payableNow: 1,
                    payOnArrival: 1,
                    status: 1,
                    currency: 1,
                    paidAmount: 1,
                    totalGuests: 1,
                    baseAdvance: 1,
                    gst: 1,
                    paymentGatewayCharge: 1
                }
            },
            {
                $lookup: {
                    from: 'subBookings',
                    localField: '_id',
                    foreignField: 'bookingId',
                    as: 'subBookings'
                }
            },
            { $unwind: '$subBookings' },
            {
                $lookup: {
                    from: 'rooms',
                    localField: 'subBookings.room',
                    foreignField: '_id',
                    as: 'roomData'
                }
            },
            { $unwind: '$roomData' },
            {
                $group: {
                    _id: '$_id',
                    bookingId: { $first: '$bookingId' },
                    checkInDate: { $first: '$checkInDate' },
                    checkOutDate: { $first: '$checkOutDate' },
                    paidAmount: { $first: '$paidAmount' },
                    status: { $first: '$status' },
                    currency: { $first: '$currency' },
                    totalGuests: { $first: '$totalGuests' },
                    baseAdvance: { $first: '$baseAdvance' },
                    gst: { $first: '$gst' },
                    paymentGatewayCharge: { $first: '$paymentGatewayCharge' },
                    payableNow: { $first: '$payableNow' },
                    payOnArrival: { $first: '$payOnArrival' },
                    subBookings: {
                        $push: {
                            subBookingId: '$subBookings.subBookingId',
                            roomName: '$roomData.name',
                            checkInDate: '$subBookings.checkInDate',
                            checkOutDate: '$subBookings.checkOutDate',
                            totalAmount: '$subBookings.totalAmount',
                            status: '$subBookings.status',
                            totalPaidAmount: '$subBookings.totalPaidAmount',
                            beds: '$subBookings.beds',
                            currency: '$subBookings.currency',
                            guests: '$subBookings.guests',
                            payableNow: '$subBookings.payableNow',
                            payableOnArrival: '$subBookings.payableOnArrival',
                            baseAdvance: '$subBookings.baseAdvance',
                            gst: '$subBookings.gst',
                            paymentGatewayCharge: '$subBookings.paymentGatewayCharge',
                            rate: '$subBookings.rate',
                            ratePlan: '$subBookings.ratePlan'
                        }
                    }

                }
            },
            {
                $project: {
                    _id: 0,
                    bookingId: 1,
                    checkInDate: 1,
                    checkOutDate: 1,
                    paidAmount: 1,
                    status: 1,
                    totalGuests: 1,
                    baseAdvance: 1,
                    gst: 1,
                    paymentGatewayCharge: 1,
                    payableNow: 1,
                    payOnArrival: 1,
                    currency: 1,
                    subBookings: 1
                }
            }
        ]);
        if (process.env.ISLIVE == true) {
            await sendToEzeeXmlBooking(bookingDetails[0])
        }
        return res.json({ status: 'Payment successful', bookingDetails: bookingDetails[0] });

    } catch (error) {
        console.error('Payment verification error:', error);
        return res.status(500).json({
            status: 'Error saving payment information',
            error: error.message
        });
    }
};




// export const createPayPalOrder = async (req, res) => {
//     const { amount, currency } = req.body;

//     const request = new paypal.orders.OrdersCreateRequest();
//     request.prefer("return=representation");
//     request.requestBody({
//         intent: 'CAPTURE',
//         purchase_units: [{
//             amount: {
//                 currency_code: currency || 'USD',
//                 value: amount.toFixed(2)
//             }
//         }]
//     });

//     try {
//         const order = await client.execute(request);
//         res.status(201).json(order.result);
//     } catch (error) {
//         res.status(500).json({ error: error.message });
//     }
// };
// export const capturePayPalPayment = async (req, res) => {
//     const { orderId } = req.body;

//     const request = new paypal.orders.OrdersCaptureRequest(orderId);
//     request.requestBody({});

//     try {
//         const capture = await client.execute(request);
//         // Handle the capture response, store payment info in DB
//         // Example: capture.result.purchase_units[0].payments.captures[0]

//         // Save payment details to the database
//         const payment = new Payment({
//             user: req.body.user_id,
//             property: req.body.property_id,
//             orderId: capture.result.id,
//             paymentId: capture.result.purchase_units[0].payments.captures[0].id,
//             amount: capture.result.purchase_units[0].amount.value,
//             currency: capture.result.purchase_units[0].amount.currency_code,
//             status: 'Payment successful',
//             paymentType: 'PayPal'
//         });

//         await payment.save();

//         res.json({ status: 'Payment successful', capture });
//     } catch (error) {
//         res.status(500).json({ error: error.message });
//     }
// };
const savePayment = async (paymentData) => {
    const payment = new Payment(paymentData);
    await payment.save();
    return payment;
};
const getBookingDetails = async (paymentFor, bookingId) => {
    if (paymentFor === 'roombooking') {
        return await Booking.aggregate([
            { $match: { _id: bookingId } },
            {
                $lookup: {
                    from: 'rooms',  // Collection name for rooms
                    let: { roomId: "$room" },  // But rooms is an array in Booking schema
                    pipeline: [
                        { $match: { $expr: { $eq: ["$_id", "$$roomId"] } } }
                    ],
                    as: 'roomDetails'
                }
            },
            {
                $lookup: {
                    from: 'properties',  // Collection name for properties
                    let: { propertyId: "$property" },
                    pipeline: [
                        { $match: { $expr: { $eq: ["$_id", "$$propertyId"] } } }
                    ],
                    as: 'propertyDetails'
                }
            }
        ]);
    } else if (paymentFor === 'event') {
        return await EventBooking.aggregate([
            { $match: { _id: bookingId } },
            {
                $lookup: {
                    from: 'events',  // Collection name for events
                    let: { eventId: "$event" },
                    pipeline: [
                        { $match: { $expr: { $eq: ["$_id", "$$eventId"] } } },
                        {
                            $project: {
                                createdBy: 0,
                                propertyId: 0,
                                status: 0,
                                isActive: 0,
                                isDeleted: 0,
                                refundable: 0,
                                nonRefundable: 0,
                                cancellation: 0
                            }
                        }
                    ],
                    as: 'eventDetails'
                }
            }
        ]);
    }
};
// Webhook handler
export const handlePaymentWebhook = async (req, res) => {
    try {
        const secret = process.env.RAZORPAY_WEBHOOK_SECRET;

        // Validate Razorpay webhook signature
        const shasum = crypto.createHmac('sha256', secret);
        shasum.update(JSON.stringify(req.body));
        const digest = shasum.digest('hex');

        // Compare the generated signature with Razorpay's signature header
        if (digest !== req.headers['x-razorpay-signature']) {
            return res.status(400).json({ error: 'Invalid signature' });
        }

        // Extract event type from the Razorpay webhook payload
        const event = req.body.event;
        console.log("event is", event)
        switch (event) {
            case 'payment.captured':
                console.log('Payment captured:', req.body);

                // // Extract payment data from the Razorpay webhook payload
                // const paymentCapturedData = req.body.payload.payment.entity;

                // // Create a new payment record in MongoDB
                // const newPayment = await Payment.create({
                //   user: req.body.payload.payment.entity.notes.userId, // assuming you pass userId in notes
                //   property: req.body.payload.payment.entity.notes.propertyId, // assuming you pass propertyId in notes
                //   orderId: paymentCapturedData.order_id,
                //   paymentId: paymentCapturedData.id,
                //   signature: req.headers['x-razorpay-signature'],
                //   amount: paymentCapturedData.amount / 100, // Convert from paise to rupees
                //   currency: paymentCapturedData.currency,
                //   status: paymentCapturedData.status,
                //   paymentType: paymentCapturedData.method,
                //   checkIn: req.body.payload.payment.entity.notes.checkInDate, // Optional, if needed
                //   checkOut: req.body.payload.payment.entity.notes.checkOutDate, // Optional, if needed
                // });

                // // Update booking with payment status
                // const updatedBooking = await Booking.findOneAndUpdate(
                //   { referenceNumber: paymentCapturedData.order_id }, // Assuming referenceNumber is the same as order_id
                //   {
                //     paymentStatus: 'paid',
                //     status: 'confirmed',
                //   },
                //   { new: true }
                // );

                // // Optionally store property payments
                // await PropertyPayments.create({
                //   user: updatedBooking.user,
                //   property: updatedBooking.property,
                //   paymentId: newPayment._id,
                //   amount: newPayment.amount,
                //   currency: newPayment.currency,
                // });

                break;

            case 'payment.failed':
                console.log('Payment failed:', req.body);

                // // Log the failed payment attempt (optional)
                // const paymentFailedData = req.body.payload.payment.entity;
                // await Payment.create({
                //   user: req.body.payload.payment.entity.notes.userId,
                //   property: req.body.payload.payment.entity.notes.propertyId,
                //   orderId: paymentFailedData.order_id,
                //   paymentId: paymentFailedData.id,
                //   amount: paymentFailedData.amount / 100,
                //   currency: paymentFailedData.currency,
                //   status: paymentFailedData.status,
                //   paymentType: paymentFailedData.method,
                // });

                break;

            // Handle other event types as needed
            default:
                console.log('Unhandled event:', req.body);
                break;
        }

        // Respond to Razorpay that the webhook event was handled successfully
        res.status(200).send('Event received');
    } catch (error) {
        console.error('Error handling Razorpay webhook:', error);
        res.status(500).json({ error: 'Internal Server Error' });
    }
};
export const AddCashPayments = async (req, res) => {
    try {
        const { userName, date, room, amount, property, currency } = req.body
        const paymentData = {
            gusetUser: userName,
            paymentFor: 'Property',
            property,
            amount,
            currency,
            status: 'Paid',
            paymentType: 'Cash',
            paymentDate: date,
            room
        }
        const payment = new Payment(paymentData);
        await payment.save();
        return Response.Created(res, payment, 'Payment Added');

    } catch (error) {
        return Response.InternalServerError(res, null, error.message);

    }
};
export const GetPaymentsByProperty = async (req, res) => {
    const { propertyId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    try {
        const filter = { property: propertyId };

        if (req.query.type) {
            filter.type = req.query.type;
        }
        if (req.query.status) {
            filter.status = req.query.status;
        }

        const payments = await Payment.find(filter)
            .skip(skip)
            .limit(limit)
            .sort({ createdAt: -1 }); // Optional: sort by latest

        const total = await Payment.countDocuments(filter);

        return Response.OK(res, {
            payments,
            total,
            page,
            pages: Math.ceil(total / limit)
        }, 'Payment List');

    } catch (error) {
        console.error('Error fetching payments:', error);
        return Response.InternalServerError(res, null, error.message);
    }
}

// Edit payment by payment ID
export const EditCashPayment = async (req, res) => {
    try {
        const { paymentId } = req.params;
        const { userName, date, room, amount, property, currency } = req.body;

        // Find the payment by ID
        const payment = await Payment.findById(paymentId);
        if (!payment) {
            return Response.NotFound(res, null, 'Payment not found');
        }

        // Allow edits only for paymentType: Cash
        if (payment.paymentType !== 'Cash') {
            return Response.BadRequest(res, null, 'Only cash payments can be edited');
        }

        // Update the payment fields
        payment.gusetUser = userName || payment.gusetUser;
        payment.paymentDate = date || payment.paymentDate;
        payment.room = room || payment.room;
        payment.amount = amount || payment.amount;
        payment.property = property || payment.property;
        payment.currency = currency || payment.currency;

        await payment.save();
        return Response.OK(res, payment, 'Payment updated successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

// Soft delete payment
export const DeleteCashPayment = async (req, res) => {
    try {
        const { paymentId } = req.params;

        // Find the payment by ID
        const payment = await Payment.findById(paymentId);
        if (!payment) {
            return Response.NotFound(res, null, 'Payment not found');
        }

        // Allow deletion only for paymentType: Cash
        if (payment.paymentType !== 'Cash') {
            return Response.BadRequest(res, null, 'Only cash payments can be deleted');
        }

        // Perform soft delete
        payment.isDeleted = true;
        await payment.save();

        return Response.OK(res, null, 'Payment deleted successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};


// export const getAllPayment = async (req, res) => {
//     try {
//         // Validate admin role
//         const user = req.user; // Assuming `req.user` is populated via authentication middleware
//         if (!user || user.role !== 'admin') {
//             return Response.Forbidden(res, null, 'Access denied. Admin role required.');
//         }

//         // Destructure and parse query parameters
//         const { propertyId, paymentType, paymentDate, paymentFor, page = 1, limit = 10 } = req.query;
//         const pageNumber = parseInt(page, 10) || 1;
//         const limitNumber = parseInt(limit, 10) || 10;
//         const skip = (pageNumber - 1) * limitNumber;

//         // Initialize filters
//         const filters = {};

//         // Add filters dynamically
//         if (propertyId) {
//             if (!mongoose.Types.ObjectId.isValid(propertyId)) {
//                 return Response.BadRequest(res, null, 'Invalid property ID format.');
//             }
//             filters.property = propertyId;
//         }
//         if (paymentType) filters.paymentType = paymentType;
//         if (paymentFor) filters.paymentFor = paymentFor;
//         if (paymentDate) {
//             const startDate = new Date(paymentDate);
//             const endDate = new Date(paymentDate);
//             endDate.setDate(endDate.getDate() + 1); // Include the full day
//             filters.paymentDate = { $gte: startDate, $lt: endDate };
//         }

//         // Fetch data and count simultaneously
//         const [payments, totalPayments] = await Promise.all([
//             Payment.find(filters)
//                 .skip(skip)
//                 .limit(limitNumber)
//                 .sort({ paymentDate: -1 }), // Sort by paymentDate descending
//             Payment.countDocuments(filters),
//         ]);

//         // Calculate pagination details
//         const totalPages = Math.ceil(totalPayments / limitNumber);

//         // Return response
//         return Response.OK(res, {
//             payments,
//             pagination: {
//                 page: pageNumber,
//                 limit: limitNumber,
//                 totalPages,
//                 totalPayments,
//             },
//         }, 'Payment List');
//     } catch (error) {
//         console.error('Error fetching payments:', error);
//         return Response.InternalServerError(res, null, 'Failed to retrieve payments.');
//     }
// };
// const generateInvoicePDF = async (userId, bookingDetails, paymentData) => {
//     const doc = new PDFDocument();
//     const invoiceDir = path.join(__dirname, 'invoices');
//     if (!fs.existsSync(invoiceDir)) {
//         fs.mkdirSync(invoiceDir);
//     }

//     const invoicePath = path.join(invoiceDir, `invoice_${paymentData.orderId}.pdf`);
//     const stream = fs.createWriteStream(invoicePath);

//     doc.pipe(stream);

//     // Add Invoice Header
//     doc.fontSize(20).text('Invoice', { align: 'center' });
//     doc.moveDown();

//     // Add Payment Details
//     doc.fontSize(12)
//         .text(`Order ID: ${paymentData.orderId}`)
//         .text(`Payment ID: ${paymentData.paymentId}`)
//         .text(`Amount: ${paymentData.amount} ${paymentData.currency}`)
//         .text(`Status: ${paymentData.status}`)
//         .moveDown();

//     // Add Booking Details
//     bookingDetails.forEach((detail, index) => {
//         doc.text(`Booking ${index + 1}:`)
//             .text(`Property: ${detail.property.name}`)
//             .text(`Room: ${detail.room.name}`)
//             .text(`Check-in: ${detail.checkInDate}`)
//             .text(`Check-out: ${detail.checkOutDate}`)
//             .text(`Total Amount: ${detail.totalAmount}`)
//             .moveDown();
//     });

//     // Add Footer
//     doc.moveDown().text('Thank you for booking with MixDorm!', { align: 'center' });

//     doc.end();

//     return new Promise((resolve, reject) => {
//         stream.on('finish', () => resolve(invoicePath));
//         stream.on('error', (error) => reject(error));
//     });
// };