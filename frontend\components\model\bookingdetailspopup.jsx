/* eslint-disable react/no-unknown-property */
import React, { useEffect, useState } from "react";
import Modal from "@mui/material/Modal";
import { IoCloseCircleOutline } from "react-icons/io5";
import { Info } from "lucide-react";
import countries from "world-countries";
import { format } from "date-fns";

const Bookingdetailspopup = ({
  openBookingdetailspopup,
  handleCloseBookingdetailspopup,
  bookingDetailsData,
}) => {
  const style = {
    position: "fixed",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: "100%",
    bgcolor: "background.paper",
    border: "2px solid #000",
    boxShadow: 24,
  };

  const [currencyData, setCurrencyData] = useState({});

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);
      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);

  const getCurrencySymbol = (currencyCode) => {
    return currencyData[currencyCode] || currencyCode;
  };

  return (
    <>
      <Modal
        open={openBookingdetailspopup}
        onClose={handleCloseBookingdetailspopup}
        aria-labelledby='modal-modal-title'
        aria-describedby='modal-modal-description'
      >
        <div sx={style}>
          <div className='bg-white rounded-2xl max-w-[500px] mx-auto left-1/2 absolute w-[95%] top-1/2 -translate-y-1/2 -translate-x-1/2'>
            <div className='flex items-center justify-between bg-gray-100 p-4 rounded-t-2xl'>
              <h2 className='text-xl text-black font-bold'>Booking Details</h2>
              <button
                onClick={handleCloseBookingdetailspopup}
                className='text-black text-2xl hover:text-primary-blue'
              >
                <IoCloseCircleOutline />
              </button>
            </div>
            <div className='md:p-6 p-4'>
              <div className='pb-3'>
                <div className='max-w-sm mx-auto p-4 border border-gray-200 rounded-lg shadow-md'>
                  <div className='text-sm text-gray-500 mb-2'>
                    Booking Reference
                  </div>
                  <div className='text-lg font-semibold text-gray-800 mb-4'>
                    {bookingDetailsData?.subBookingId}
                  </div>

                  <div className='border-t border-dashed border-gray-200 my-2'></div>

                  <div className='flex justify-between items-center'>
                    <div>
                      <div className='text-sm text-gray-500'>Dates</div>
                      <div className='text-md font-semibold text-gray-800'>
                        {bookingDetailsData?.checkInDate &&
                        bookingDetailsData?.checkOutDate
                          ? `${format(
                              new Date(bookingDetailsData?.checkInDate),
                              "dd MMM yyyy"
                            )} - ${format(
                              new Date(bookingDetailsData?.checkOutDate),
                              "dd MMM yyyy"
                            )}`
                          : "N/A"}
                      </div>
                    </div>

                    {/* <div className="flex item-center gap-1">
                        <div className="flex items-center">
                        <Users size={14}/>
                        </div>
                    
                      <div className="text-gray-500 font-normal text-xs">
                         Guests
                         <div className="text-md font-semibold text-gray-800">2</div>
                      </div>
                     
                    </div> */}
                  </div>
                </div>
              </div>
              <p className='text-sm font-normal text-grey-250'>
                {bookingDetailsData?.roomName} (
                {bookingDetailsData?.ratePlan?.name})
              </p>
              <div className='flex justify-between pb-2'>
                <p className='text-sm font-normal text-grey-250'>
                  {getCurrencySymbol(bookingDetailsData?.rate?.currency)}
                  {bookingDetailsData?.rate?.baseAmount.toFixed(2)} X{" "}
                  {bookingDetailsData?.beds || 1}{" "}
                  {bookingDetailsData?.beds === 1 ? "Bed" : "Beds"} ×{" "}
                  {bookingDetailsData?.nights || 1}{" "}
                  {bookingDetailsData?.nights === 1 ? "Night" : "Nights"}
                </p>
                <p className='text-sm font-normal text-grey-250'>
                  {" "}
                  {getCurrencySymbol(bookingDetailsData?.rate?.currency)}
                  {bookingDetailsData?.rate?.totalAmount.toFixed(2)}
                </p>
              </div>
              <hr />
              <div className='py-2'>
                <div className='flex justify-between pb-2'>
                  <p className='text-sm font-normal text-grey-250'>Taxes</p>
                  <p className='text-sm font-normal text-grey-250'>0</p>
                </div>
                <div className='flex justify-between pb-2'>
                  <p className='text-sm font-bold text-black'>Total Price</p>
                  <div className='flex items-center space-x-1 text-sm font-bold text-black'>
                    <span>
                      {getCurrencySymbol(bookingDetailsData?.rate?.currency)}{" "}
                      {(
                        bookingDetailsData?.rate?.baseAmount * 0.85 +
                        bookingDetailsData?.rate?.baseAmount *
                          0.15 *
                          1.18 *
                          1.03
                      ).toFixed(2)}
                    </span>

                    {/* Wrap icon and tooltip in a group */}
                    <div className='relative group cursor-pointer'>
                      <Info className='w-4 h-4 text-gray-500' />

                      {/* Tooltip */}
                      <div className='absolute z-10 hidden group-hover:block bg-black text-white text-xs px-2 py-1 rounded shadow-md w-max top-full left-1/2 -translate-x-1/2 mt-1 whitespace-nowrap'>
                        Tax and Fees Included
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <hr />
              <div className='py-2'>
                <div className='flex justify-between pb-2'>
                  <p className='text-sm font-bold text-primary-blue'>
                    Total Paid
                  </p>
                  <p className='text-sm font-bold text-primary-blue'>
                    {getCurrencySymbol(bookingDetailsData?.rate?.currency)}{" "}
                    {(
                      bookingDetailsData?.rate?.baseAmount *
                      0.15 *
                      1.18 *
                      1.03
                    ).toFixed(2)}
                  </p>
                </div>
                <hr />
                <div className='pt-2'>
                  <div className='flex justify-between'>
                    <p className='text-sm font-bold text-black'>
                      Payable Arrival
                    </p>
                    <p className='text-sm font-bold text-black'>
                      {getCurrencySymbol(bookingDetailsData?.rate?.currency)}{" "}
                      {(bookingDetailsData?.rate?.baseAmount * 0.85).toFixed(2)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default Bookingdetailspopup;
