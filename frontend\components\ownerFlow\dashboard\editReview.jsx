/* eslint-disable no-constant-binary-expression */
import { CloudUpload } from "lucide-react";
import CustomSelect from "@/components/common/CustomDropdown2";
import { useEffect, useRef, useState } from "react";
import Loader from "@/components/loader/loader";
import { BASE_URL } from "@/utils/api";
import toast from "react-hot-toast";
import {
  getOwnerReviewByIdApi,
  updateOwnerReviewByIdApi,
} from "@/services/ownerflowServices";
import { getItemLocalStorage } from "@/utils/browserSetting";
import countries from "world-countries";
import { format, isToday } from "date-fns";
import Image from "next/image";

const EditReview = ({ closeeditModal, editId, updateReviewList }) => {
  const id = getItemLocalStorage("hopid");
  const [formData, setFormData] = useState({
    name: "",
    date: "",
    rating: "",
    photos: [],
    description: "",
    country: "",
  });

  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [country, setCountry] = useState([]);
  const [showCalendar, setShowCalendar] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const calendarRef = useRef(null);
  const inputRef = useRef(null);

  const handlePrevMonth = () => {
    setCurrentMonth(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() - 1, 1)
    );
  };

  const handleNextMonth = () => {
    setCurrentMonth(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() + 1, 1)
    );
  };

  // Generate days for current month
  function generateDays(month) {
    const daysInMonth = new Date(
      month.getFullYear(),
      month.getMonth() + 1,
      0
    ).getDate();
    const firstDayIndex = new Date(
      month.getFullYear(),
      month.getMonth(),
      1
    ).getDay();

    const days = [];

    // Add blank spaces for days of previous month
    for (let i = 0; i < firstDayIndex; i++) {
      days.push(null);
    }

    // Add days of the current month
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(new Date(month.getFullYear(), month.getMonth(), i));
    }

    return days;
  }

  // Close calendar if clicked outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target) &&
        !inputRef.current.contains(event.target)
      ) {
        setShowCalendar(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (editId) {
      fetchData(editId);
    }
  }, [editId]);

  const fetchData = async (editId) => {
    try {
      const res = await getOwnerReviewByIdApi(editId);
      if (res?.status == 200) {
        const data = res?.data?.data;
        setFormData({
          name: data?.name || "",
          date: data?.createdAt.split("T")[0],
          rating: data?.rating,
          photos: data?.images || [],
          description: data?.comment,
          country: data?.country || "",
        });
      }
    } catch (error) {
      console.log("error", error);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setErrors((prev) => ({ ...prev, [name]: "" }));
  };

  const removeAttachmentImage = (indexToRemove) => {
    setFormData((prevState) => ({
      ...prevState,
      photos: prevState.photos.filter((_, index) => index !== indexToRemove),
    }));
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.name) newErrors.name = "Name is required.";
    if (!formData.date) newErrors.date = "Date is required.";
    if (!formData.rating) newErrors.rating = "Rating is required.";
    if (!formData?.country?.value) newErrors.country = "Country is required.";
    if (formData.photos.length === 0)
      newErrors.photos = "At least one photo is required.";
    if (!formData.description)
      newErrors.description = "Description is required.";
    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
    } else {
      try {
        const payload = {
          user: formData?.name || "",
          property: id || "",
          rating: formData?.rating || 1,
          comment: formData?.description || "",
          images: formData?.photos || [],
          country: formData?.country?.value || "",
          likes: 0,
          dislikes: 0,
          isActive: true,
          isDeleted: false,
          createdAt: formData?.date || "",
          updatedAt: formData?.date || "",
        };
        const response = await updateOwnerReviewByIdApi(editId, payload);

        if (response?.data?.status) {
          toast.success(
            response?.data?.message || "Review update successfully!"
          );
          updateReviewList();
          closeeditModal();
        } else {
          toast.error("Failed to update review");
        }
      } catch (error) {
        console.error("Error update review:", error);
        toast.error("Error update review");
      }
    }
  };

  const handlePhotoChange = async (e) => {
    const { files } = e.target;

    if (files.length === 0) {
      toast.error("At least one file is required");
      return;
    }
    const allowedTypes = ["image/jpeg", "image/png", "image/jpg"];

    // Check if all selected files are of allowed types
    const invalidFiles = Array.from(files).filter(
      (file) => !allowedTypes.includes(file.type)
    );

    if (invalidFiles.length > 0) {
      toast.error("Only JPG, JPEG, and PNG files are allowed.");
      return;
    }

    setIsLoading(true);

    try {
      // Loop through all selected files
      const uploadedImages = await Promise.all(
        Array.from(files).map(async (file) => {
          const formData = new FormData();
          formData.append("files", file);

          // Get presigned URL for each file
          const presignedUrlResponse = await fetch(
            `${BASE_URL}/fileUpload/generate-presigned-url`,
            {
              method: "POST",
              body: formData,
            }
          );

          if (!presignedUrlResponse.ok) {
            throw new Error("Failed to get presigned URL");
          }

          const presignedUrlData = await presignedUrlResponse.json();
          // const { objectURL } = presignedUrlData.data;
          const objectURL = Array.isArray(presignedUrlData.data) && presignedUrlData.data[0]?.path;

          return { title: "Attachment", url: objectURL };
        })
      );

      // Update state with new uploaded image URLs
      setFormData((prevState) => ({
        ...prevState,
        photos: [...(prevState?.photos || []), ...uploadedImages],
      }));

      toast.success("Files uploaded successfully.");
    } catch (error) {
      console.error("Error uploading files:", error);
      toast.error("Error uploading files.");
    } finally {
      setIsLoading(false);
    }
  };

  const isCountryFetched = useRef(false);
  useEffect(() => {
    if (!isCountryFetched.current) {
      const fetchCountryFlags = async () => {
        try {
          // Map countries to get only name and flag
          const countryList = countries.map((country) => ({
            name: country.name?.common || "",
            flag:
              `https://flagcdn.com/w320/${country.cca2?.toLowerCase()}.png` ||
              "https://via.placeholder.com/30x25",
          }));

          setCountry(countryList);
        } catch (error) {
          console.error("Error fetching country flags:", error);
        }
      };

      fetchCountryFlags();
      isCountryFetched.current = true;
    }
  }, []);

  console.log("formData", formData);

  return (
    <>
      <Loader open={isLoading} />

      <div className="w-full bg-white rounded-lg">
        <form
          className="space-y-6 max-w-[830px] mx-auto"
          onSubmit={handleSubmit}
        >
          <div>
            <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
              Name
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
              placeholder="Enter Name"
            />
            {errors.name && (
              <p className="text-red-500 text-xs">{errors.name}</p>
            )}
          </div>

          <div>
            <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
              Country
            </label>
            <CustomSelect
              options={country.map((data) => ({
                value: data.name,
                label: (
                  <span className="flex items-center">
                    <Image 
                      src={data.flag || "/placeholder.svg"}
                      alt={`${data.name} Flag`}
                      className="inline-block w-4 h-3 mr-2"
                      width={20}
                      height={15}
                    />
                    {data.name}
                  </span>
                ),
              }))}
              value={formData.country}
              onChange={(selectedOption) =>
                handleChange({
                  target: { name: "country", value: selectedOption },
                })
              }
              placeholder="Select Country"
            />
            {errors.country && (
              <p className="text-red-500 text-xs">{errors.country}</p>
            )}
          </div>

          {/* <div>
            <label className='block text-black sm:text-sm text-xs font-medium mb-1.5'>
              Dated
            </label>
            <input
              type='date'
              name='date'
              value={formData.date}
              onChange={handleChange}
              className='mt-1 block border border-black/50 w-full p-2 px-2 pr-4 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500'
              onClick={(e) => e.target.showPicker()}
            />
            {errors.date && (
              <p className='text-red-500 text-xs'>{errors.date}</p>
            )}
          </div> */}
          <div className="grid mb-3">
            <div className="relative w-full">
              <label
                className="block text-black sm:text-sm text-xs font-medium mb-1.5"
                htmlFor="toDate"
              >
                Date
              </label>

              <input
                type="text"
                name="toDate"
                id="toDate"
                ref={inputRef}
                value={formData?.date && format(formData?.date, "MM-dd-yyyy")}
                placeholder="mm/dd/yyyy"
                readOnly
                onClick={() => setShowCalendar(!showCalendar)}
                className="block w-full p-2 px-4 py-3 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-800 placeholder:text-gray-400 cursor-pointer"
              />

              {showCalendar && (
                <div
                  ref={calendarRef}
                  className="absolute top-full left-0 bg-white border border-black/50 rounded-lg shadow-lg px-4 py-2 z-50 w-full mt-0.5"
                >
                  {/* Month navigation */}
                  <div className="flex items-center justify-between mb-4">
                    <button
                      onClick={handlePrevMonth}
                      className="text-lg font-bold px-2"
                    >
                      &#8592;
                    </button>

                    <span className="text-base font-semibold">
                      {format(currentMonth, "MMMM yyyy")}
                    </span>

                    <button
                      onClick={handleNextMonth}
                      className="text-lg font-bold px-2"
                    >
                      &#8594;
                    </button>
                  </div>

                  {/* Weekdays */}
                  <div className="grid grid-cols-7 gap-2 text-center text-sm font-semibold text-gray-600">
                    <div>Su</div>
                    <div>Mo</div>
                    <div>Tu</div>
                    <div>We</div>
                    <div>Th</div>
                    <div>Fr</div>
                    <div>Sa</div>
                  </div>

                  {/* Days */}
                  <div className="grid grid-cols-7 gap-1 mt-2 text-center text-sm">
                    {generateDays(currentMonth).map((day, index) =>
                      day ? (
                        <button
                          key={index}
                          className={`rounded-full p-2 text-sm flex items-center justify-center ${isToday(day) ? "border-2 border-primary-blue" : ""} ${
                            format(day, "MM-dd-yyyy") === formData.date
                              ? "bg-primary-blue text-white"
                              : "hover:bg-primary-blue hover:text-white"
                          }`}
                          onClick={() => {
                            handleChange({
                              target: {
                                name: "date",
                                value: format(day, "MM-dd-yyyy"),
                              },
                            });
                            setShowCalendar(false);
                          }}
                        >
                          {day.getDate()}
                        </button>
                      ) : (
                        <div key={index} />
                      )
                    )}
                  </div>
                </div>
              )}
              {errors.date && (
                <p className="text-red-500 text-xs">{errors.date}</p>
              )}
            </div>
          </div>
          <div>
            <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
              Rating
            </label>
            <CustomSelect
              name="rating"
              options={[
                { value: "1", label: "1" },
                { value: "2", label: "2" },
                { value: "3", label: "3" },
                { value: "4", label: "4" },
                { value: "5", label: "5" },
              ]}
              value={
                formData.rating
                  ? { value: `${formData.rating}`, label: `${formData.rating}` }
                  : null
              }
              onChange={(selectedOption) =>
                handleChange({
                  target: { name: "rating", value: selectedOption?.value },
                })
              }
              placeholder="Select Rating"
            />
            {errors.rating && (
              <p className="text-red-500 text-xs">{errors.rating}</p>
            )}
          </div>

          <div className="w-full">
            <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
              Upload Photos
            </label>
            <div className="relative w-full">
              <input
                type="file"
                name="photos"
                multiple
                accept=".jpg, .jpeg, .png"
                onChange={handlePhotoChange}
                className="relative z-10 block w-full p-2 px-4 text-sm bg-transparent border rounded-lg opacity-0 border-gray-220 focus:outline-none text-slate-320 placeholder:text-gray-320"
              />
              <div className="flex items-center justify-between w-full p-2 px-4 text-sm border-1.5 border-dashed rounded-lg border-blue-230 bg-blue-230/10 text-slate-320 cursor-pointer absolute top-0 left-0 right-0">
                Upload Photos{" "}
                <CloudUpload className="text-blue-230" size={22} />
              </div>
            </div>
            {errors.photos && (
              <p className="text-red-500 text-xs">{errors.photos}</p>
            )}
          </div>
          {formData?.photos?.length > 0 && (
            <div className="col-span-2">
              <div className="grid grid-cols-3 sm:gap-4 gap-3 mt-4">
                {formData?.photos?.map((image, index) => (
                  <div
                    key={index}
                    className="relative flex items-center justify-between sm:px-4 px-2 sm:py-2.5 py-2 border border-[#40E0D0] border-dashed bg-[#40E0D01A] rounded-lg"
                  >
                    <Image 
                      // src={image?.url}
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/${image?.url}`}
                      alt={`Existing Image ${index + 1}`}
                      className="w-[54px] h-[36px] object-cover rounded-sm"
                      width={54}
                      height={36}
                    />
                    <span
                      className="text-black hover:text-red-500 font-black cursor-pointer text-xl"
                      onClick={() => removeAttachmentImage(index)}
                    >
                      &#10005;
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div>
            <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
              Description
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
              placeholder="Enter Description"
              rows="4"
            ></textarea>
            {errors.description && (
              <p className="text-red-500 text-xs">{errors.description}</p>
            )}
          </div>

          <div className="flex items-center justify-between w-full sm:my-14 my-7 gap-4 col-span-2 py-4 bg-white/60 sticky bottom-0 backdrop-blur-sm">
            <button
              type="button"
              onClick={closeeditModal}
              className="hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 px-4 border-black rounded-lg w-full text-sm xs:mb-0 mb-2"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm"
            >
              Save Changes
            </button>
          </div>
        </form>
      </div>
    </>
  );
};

export default EditReview;
