import dotenv from 'dotenv';
import Razorpay from "razorpay";
import Plan from '../models/plans.js';
import Subscription from '../models/subscription.js';
import User from '../models/auth.js';
import Response from '../utills/response.js';
import { StatusCodes } from 'http-status-codes';

dotenv.config();

// // Initialize Razorpay instance
const razorpay = new Razorpay({
  key_id: 'rzp_test_GyMk8BHHTEDNrH',
  key_secret: 'waq7jopQrLfvQtBtQUEE1lux'
});

export const createPlan = async (req, res) => {
  try {
    const { name, description, amount, currency = 'INR', period, interval } = req.body;

    // Validate required fields
    if (!name || !amount || !period || !interval) {
      throw new Error('Missing required fields: name, amount, interval, or period');
    }

    // Prepare Razorpay plan details
    const planDetails = {
      period,
      interval,
      item: {
        name,
        description,
        amount,
        currency,
      },
    };

    // Create plan in Razorpay
    const razorpayPlan = await razorpay.plans.create(planDetails);

    // Save the plan details to MongoDB
    const savedPlan = await Plan.create({
      name,
      description,
      amount,
      currency,
      period,
      interval,
    });

    res.status(201).json({ razorpayPlan, savedPlan });
  } catch (error) {
    console.error('Error creating plan:', error.message);
    res.status(500).json({ error: error.message });
  }
};

export const fetchPlanById = async (req, res) => {
  try {
    const { id } = req.params;

    // Validate ID
    if (!id) {
      return res.status(400).json({ error: 'Plan ID is required' });
    }

    // Find the plan in the database
    const plan = await Plan.findById(id);

    // If plan not found
    if (!plan) {
      return res.status(404).json({ error: 'Plan not found' });
    }

    // Send response
    return res.status(200).json({
      message: 'Plan fetched successfully',
      plan,
    });
  } catch (error) {
    console.error('Error fetching plan by ID:', error.message);
    return res.status(500).json({ error: error.message });
  }
};

export const fetchAllPlans = async (req, res) => {
  try {
    // Fetch all plans from the database
    const plans = await Plan.find();

    // If no plans exist
    if (plans.length === 0) {
      return res.status(404).json({ message: 'No plans found' });
    }

    // Send response
    return res.status(200).json({
      message: 'Plans fetched successfully',
      plans,
    });
  } catch (error) {
    console.error('Error fetching all plans:', error.message);
    return res.status(500).json({ error: error.message });
  }
};

export const createSubscription = async (req, res) => {
  const {
    plan_id,
    total_count,
    customer_notify,
    start_at,
    addons,
    user_id, // Changed to user_id instead of customer_email and customer_name
  } = req.body;

  // Validate required fields
  if (!plan_id || !user_id) {
    return res.status(400).json({
      error: 'Plan ID and User ID are required.',
    });
  }

  try {
    // Check if customer exists in the local database by user_id
    let customerRecord = await User.findById(user_id);

    let razorpay_customer_id;

    if (!customerRecord) {
      return res.status(404).json({
        error: 'Customer not found in the database.',
      });
    }

    if (!customerRecord.razorpay_customer_id) {
      const existingCustomers = await razorpay.customers.all({ email: customerRecord.email });

      if (existingCustomers.items.length > 0) {
        razorpay_customer_id = existingCustomers.items[0].id;
      } else {
        const newCustomer = await razorpay.customers.create({
          name: `${customerRecord.name.first} ${customerRecord.name.last}`,
          email: customerRecord.email,
        });

        razorpay_customer_id = newCustomer.id;
      }

      customerRecord.razorpay_customer_id = razorpay_customer_id;
      customerRecord.razorpay_plan_id = plan_id;
      await customerRecord.save();
    } else {
      razorpay_customer_id = customerRecord.razorpay_customer_id;
    }

    let startAtTimestamp = start_at || Math.floor(Date.now() / 1000) + 3600;

    if (startAtTimestamp < Math.floor(Date.now() / 1000)) {
      startAtTimestamp = Math.floor(Date.now() / 1000) + 3600;
    }

    const subscriptionPayload = {
      plan_id,
      customer_id: razorpay_customer_id,
      total_count,
      customer_notify: customer_notify ? 1 : 0,
      start_at: startAtTimestamp,
      addons: addons || [],
    };

    const subscription = await razorpay.subscriptions.create(subscriptionPayload);
    console.log("subscription", subscription)

    // Save all dates and status according to Razorpay's response
    const newSubscription = new Subscription({
      user_id,
      plan_id,
      customer_id: razorpay_customer_id,
      razorpay_subscription_id: subscription.id,
      total_count,
      remaining_count: subscription.remaining_count,
      status: subscription.status, // Use Razorpay's status here
      addons: subscription.addons || [],
      start_at: subscription.start_at ? new Date(subscription.start_at * 1000) : null, // Convert Unix timestamp to JS Date
      end_at: subscription.end_at ? new Date(subscription.end_at * 1000) : null,
      current_start: subscription.current_start ? new Date(subscription.current_start * 1000) : null,
      current_end: subscription.current_end ? new Date(subscription.current_end * 1000) : null,
    });

    await newSubscription.save();

    res.status(201).json({
      message: 'Subscription created successfully.',
      subscription_id: subscription.id,
      subscription_status: subscription.status,
      savedSubscription: newSubscription,
    });
  } catch (error) {
    console.error('Error creating subscription:', error);
    res.status(500).json({
      error: 'Failed to create subscription.',
      details: error?.message || 'No additional details provided.',
    });
  }
};

export const getActiveSubscription = async (req, res) => {
  const { user_id } = req.params;  // Get the user ID from the URL parameter

  try {
    // Fetch the user to ensure they exist
    const user = await User.findById(user_id);

    if (!user) {
      return Response.NotFound(res, {}, 'User not found.');
    }

    // Fetch all active subscriptions for the user
    const activeSubscriptions = await Subscription.find({
      user_id: user._id,
      status: 'active',  // Ensure only active subscriptions are fetched
      $or: [
        { end_at: { $gt: new Date() } },  // Subscription should not have ended
        { end_at: null },  // Consider subscriptions with no end date as active
      ],
    });

    if (!activeSubscriptions || activeSubscriptions.length === 0) {
      return Response.NotFound(res, {}, 'No active subscription found.');
    }

    // Populate the plan details for each active subscription
    await Promise.all(activeSubscriptions.map(subscription => subscription.populate('plan_id')));

    // Return the active subscriptions details
    return Response.OK(res, {
      subscriptions: activeSubscriptions.map(activeSubscription => ({
        plan: activeSubscription.plan_id,
        total_count: activeSubscription.total_count,
        remaining_count: activeSubscription.remaining_count,
        status: activeSubscription.status,
        start_at: activeSubscription.start_at,
        end_at: activeSubscription.end_at,
        current_start: activeSubscription.current_start,
        current_end: activeSubscription.current_end,
      }))
    }, 'Active subscriptions fetched successfully.');

  } catch (error) {
    console.error('Error fetching active subscription:', error);
    return Response.InternalServerError(res, {}, error.message || 'No additional details provided.');
  }
};

export const pauseSubscription = async (req, res) => {
  const { subscriptionId } = req.body; // Get the Razorpay subscription ID from the request body

  // Validate required fields
  if (!subscriptionId) {
    return Response.BadRequest(res, {}, 'Subscription ID is required.');
  }

  try {
    // Check if the subscription exists in the local database using razorpay_subscription_id
    const subscription = await Subscription.findOne({ razorpay_subscription_id: subscriptionId });

    if (!subscription) {
      return Response.NotFound(res, {}, 'Subscription not found.');
    }

    // Check if the subscription is already inactive, canceled, or expired
    if (['inactive', 'canceled', 'expired'].includes(subscription.status)) {
      return Response.BadRequest(res, {}, `Cannot pause a subscription in the "${subscription.status}" state.`);
    }

    // Pause the subscription using Razorpay
    const razorpayResponse = await razorpay.subscriptions.pause(subscriptionId, {
      pause_at: 'now', // Options: "now" or "end_billing_cycle"
    });

    // Update the subscription status in the local database
    subscription.status = 'inactive'; // Set status to 'inactive' when paused
    subscription.updated_at = new Date();
    await subscription.save();

    // Send the response
    return Response.OK(res, {
      message: 'Subscription paused successfully.',
      razorpayResponse,
      updatedSubscription: subscription,
    });
  } catch (error) {
    console.error('Error pausing subscription:', error);

    if (error?.error?.code === 'BAD_REQUEST_ERROR') {
      return Response.BadRequest(res, {}, error?.error?.description || 'Invalid request to Razorpay.');
    }

    return Response.InternalServerError(res, {}, error.message || 'Failed to pause subscription.');
  }
};


export const resumeSubscription = async (req, res) => {
  const { subscriptionId } = req.body; // Get the subscription ID from the request body

  // Validate required fields
  if (!subscriptionId) {
    return Response.BadRequest(res, {}, 'Subscription ID is required.');
  }

  try {
    // Check if the subscription exists in the local database
    const subscription = await Subscription.findOne({ razorpay_subscription_id: subscriptionId });
    if (!subscription) {
      return Response.NotFound(res, {}, 'Subscription not found.');
    }

    // Check if the subscription is inactive (only inactive subscriptions can be resumed)
    if (subscription.status !== 'inactive') {
      return Response.BadRequest(res, {}, 'Only inactive subscriptions can be resumed.');
    }

    // Resume the subscription using Razorpay
    const razorpayResponse = await razorpay.subscriptions.resume(subscriptionId, {
      resume_at: 'now', // Options: "now" or "next_billing_cycle"
    });

    // Update the subscription status in the local database
    subscription.status = 'active'; // Set status to 'active' when resumed
    subscription.updated_at = new Date();
    await subscription.save();

    // Send the response
    return Response.OK(res, {
      message: 'Subscription resumed successfully.',
      razorpayResponse,
      updatedSubscription: subscription,
    });
  } catch (error) {
    console.error('Error resuming subscription:', error);
    return Response.InternalServerError(res, {}, error.message || 'Failed to resume subscription.');
  }
};


export const cancelSubscription = async (req, res) => {
  const { subscriptionId, cancel_at } = req.body; // Get subscription ID and cancel_at from request body

  // Validate required fields
  if (!subscriptionId) {
    return Response.BadRequest(res, {}, 'Subscription ID is required.');
  }

  // Default `cancel_at` to "now" if not provided
  const cancelOption = cancel_at || 'now'; // Options: "now" or "end_billing_cycle"

  try {
    // Check if the subscription exists in the local database
    const subscription = await Subscription.findById(subscriptionId);

    if (!subscription) {
      return Response.NotFound(res, {}, 'Subscription not found.');
    }

    // Cancel the subscription using Razorpay
    const razorpayResponse = await razorpay.subscriptions.cancel(subscriptionId, {
      cancel_at: cancelOption,
    });

    // Update the subscription status in the local database
    subscription.status = 'canceled'; // Set status to 'canceled'
    subscription.updated_at = new Date();
    await subscription.save();

    // Send the response
    return Response.OK(res, {
      message: 'Subscription canceled successfully.',
      razorpayResponse,
      updatedSubscription: subscription,
    });
  } catch (error) {
    console.error('Error canceling subscription:', error);
    return Response.InternalServerError(res, {}, error.message || 'Failed to cancel subscription.');
  }
};

export const getSubscriptionHistory = async (req, res) => {
  const {
    startDate,
    endDate,
    userId,
    status,
    page = 1,
    limit = 10,
    sortField = 'created_at',
    sortOrder = 'desc'
  } = req.query;

  try {
    // Initialize the query object (empty initially, filters will be added conditionally)
    const query = {};

    // Apply date filtering if startDate or endDate exists
    if (startDate || endDate) {
      query.created_at = {};
      if (startDate) query.created_at.$gte = new Date(startDate);
      if (endDate) query.created_at.$lte = new Date(endDate);
    }

    // Apply userId filter if provided
    if (userId) query.user_id = userId;

    // Apply status filter if provided
    if (status) query.status = status;

    // Pagination setup
    const skip = (page - 1) * limit;

    // Fetch the subscription data with query, sorting, pagination, and populate
    const subscriptions = await Subscription.find(query)
      .populate('user_id', 'name.first name.last email') // Populate user fields
      .sort({ [sortField]: sortOrder === 'asc' ? 1 : -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // If no subscriptions found, return 404 Not Found
    if (subscriptions.length === 0) {
      return Response.NotFound(res, [], 'No subscriptions found matching your criteria.');
    }

    // Get total count for pagination metadata
    const totalCount = await Subscription.countDocuments(query);

    // Map the subscriptions data to include necessary fields
    const subscriptionDetails = subscriptions.map(sub => ({
      user: `${sub.user_id.name.first} ${sub.user_id.name.last}`, // Full name
      email: sub.user_id.email,
      plan_id: sub.plan_id,
      customer_id: sub.customer_id,
      razorpay_subscription_id: sub.razorpay_subscription_id,
      total_count: sub.total_count,
      remaining_count: sub.remaining_count,
      status: sub.status,
      addons: sub.addons,
      start_at: sub.start_at,
      end_at: sub.end_at,
      current_start: sub.current_start,
      current_end: sub.current_end,
      created_at: sub.created_at,
      updated_at: sub.updated_at,
    }));

    // Return the response with the subscription data and pagination metadata
    return res.status(StatusCodes.OK).json({
      message: 'Subscription history fetched successfully.',
      data: subscriptionDetails,
      meta: {
        total: totalCount,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(totalCount / limit),
      },
    });

  } catch (error) {
    // Catch any error and handle it
    Response.handleError(res, error, StatusCodes.INTERNAL_SERVER_ERROR, 'Failed to fetch subscription history.');
  }
};














