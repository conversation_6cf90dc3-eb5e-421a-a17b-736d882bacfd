 
import { useState } from "react";
import Image from "next/image";
import axios from "axios";

const VerificationPending = () => {
  const [isLoading, setIsLoading] = useState(false);

  const handleVerify = async () => {
    setIsLoading(true);
    try {
      const apiUrl = "https://api.example.com/verify-details";
      const response = await axios.post(apiUrl);
      console.log("Verification successful:", response.data);
    } catch (error) {
      console.error("Error during verification:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center bg-[#F7F7F7] pb-[13rem]">
      <div className="w-full max-w-3xl p-6 mb-10 bg-white shadow-md rounded-3xl md:p-14">
        <h2 className="py-6 mb-4 text-2xl font-bold text-center">
          👋Hi, <span className="text-primary-blue">Namastey Mumbai</span>{" "}
          Verification Pending
        </h2>
        <div className="flex justify-center mb-4">
          <Image
            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/verify.svg`}
            alt="Verification Illustration"
            width={349}
            height={222}
            loading="lazy"
          />
        </div>
        <h3 className="py-6 text-2xl font-bold text-center mb-14">
          Please Verify Your Details
        </h3>
        <div className="flex flex-col items-center space-y-4">
          <button className="w-full max-w-xl bg-[#40E0D0] text-black font-semibold py-4 rounded-4xl hover:bg-[#40E0D0] transition duration-200">
            Review Details
          </button>
          <button
            className={`w-full max-w-xl bg-[#EEEEEE] text-black font-semibold py-4 rounded-4xl hover:bg-[#EEEEEE] transition duration-200 ${
              isLoading ? "opacity-50 cursor-not-allowed" : ""
            }`}
            onClick={handleVerify}
            disabled={isLoading}
          >
            {isLoading ? "Verifying..." : "Verify"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default VerificationPending;
