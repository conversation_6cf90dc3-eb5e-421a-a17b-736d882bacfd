export default function BookingConfirmed() {
    const rooms = [
        { id: "#01", type: "Standard Single Room", bed: "Lower bed", price: "1000 Rupees" },
        { id: "#02", type: "Standard Single Room", bed: "Lower bed", price: "1000 Rupees" },
        { id: "#03", type: "Standard Single Room", bed: "Lower bed", price: "1000 Rupees" },
      ];
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center px-4">
        <div className="bg-white rounded-2xl shadow-lg p-8 max-w-7xl w-full">
          <h1 className="text-2xl font-bold font-manrope mb-4">
            <span role="img" aria-label="wave">👋</span> Hello, 
            <span className="text-primary-blue font-bold ml-1">Rechel</span> Booking Confirmed
          </h1>
  
          <p className="text-sm text-gray-600 mb-6 font-medium font-poppins">
            We are pleased to inform you that your reservation request <br/> has been received and confirmed.
          </p>
  
          {/* Booking Summary */}
          <div className="mb-8">
            <h2 className="mb-2 text-lg font-bold font-manrope">Your Booking Detail</h2>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 text-sm font-medium text-gray-700 mb-1">
              <div className="text-lg font-bold font-manrope text-black">Booking ID</div>
              <div className="text-lg font-bold font-manrope text-black">Check In:</div>
              <div className="text-lg font-bold font-manrope text-black">Check Out:</div>
              <div className="text-lg font-bold font-manrope text-black">Total Payment</div>
              <div className="text-lg font-bold font-manrope text-black">Status Confirmed</div>
            </div>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 text-sm text-gray-800">
              <div className="text-sm font-normal font-manrope">1011</div>
              <div className="text-sm font-normal font-manrope">September 25, 2019</div>
              <div className="text-sm font-normal font-manrope">September 25, 2019</div>
              <div className="text-sm font-normal font-manrope">3000 Rupees</div>
              <div  className="col-span-2 text-sm font-normal font-manrope">Confirmed</div>
            </div>
          </div>
  
          {/* Room Details */}
          <div className="space-y-4 mx-auto">
          <h2 className="mb-2 text-lg font-bold font-manrope">Your Booking Detail</h2>
        {rooms.map((room, index) => (
          <div key={index} className="grid  grid-cols-2 sm:grid-cols-3 lg:grid-cols-6">
            <div>
              <div className="font-semibold">Room ID</div>
              <div className="text-gray-600">{room.id}</div>
            </div>
            <div>
              <div className="font-semibold">Room Type</div>
              <div className="text-gray-600">{room.type}</div>
            </div>
            <div>
              <div className="font-semibold">Bed Preference</div>
              <div className="text-gray-600">{room.bed}</div>
            </div>
            <div className=" pt-5 font-medium text-gray-800">{room.price}</div>
          </div>
        ))}
      </div>
    
  
          {/* Button */}
          <div className="my-10 flex justify-center">
            <button className="bg-primary-blue hover:bg-teal-400 text-black hover:text-white font-semibold py-2 px-12 rounded-full text-sm">
              Go Notice Board
            </button>
          </div>
        </div>
      </div>
    );
  }
  