import mongoose from 'mongoose';

const eventBookingSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Types.ObjectId,
      ref: 'users',
      required: true,
    },
    event: {
      type: mongoose.Types.ObjectId,
      ref: 'events',
      required: true,
    },
    tickets:{
        type:Number
    },
    status: {
      type: String,
      enum: ['Booked', 'Cancelled', 'Completed'],
      default: 'Booked',
    },
    paymentStatus: {
      type: String,
      enum: ['Pending', 'Paid', 'Failed'],
      default: 'Pending',
    },
    currency:{
      type:String
    },
    amountPaid: {
      type: Number,
    },
    paymentMethod: {
      type: String,
      enum: ['Credit Card', 'PayPal', 'Stripe', 'Razorpay'],
    },
    refundStatus: {
      type: String,
      enum: ['Not Applicable', 'Refunded', 'Not Refunded'],
      default: 'Not Applicable',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    bookingRefNumber:{
      type:String
    }
  },
  {
    timestamps: true,
  }
);

const eventBookingModel = mongoose.model('eventBookings', eventBookingSchema);

export default eventBookingModel;
