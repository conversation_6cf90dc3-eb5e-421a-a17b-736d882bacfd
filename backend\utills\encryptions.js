import CryptoJS from 'crypto-js';
export async function encrypt(text) {
  let secretKey = process.env.ENCRYPTION_KEY;
  // secretKey = await modifySecretKey(secretKey)
  const keyutf = CryptoJS.enc.Utf8.parse(secretKey);
  const iv = CryptoJS.enc.Utf8.parse('678025308de70905');
  const encrypted = CryptoJS.AES.encrypt(text, keyutf, {
    iv: iv,
    padding: CryptoJS.pad.Pkcs7,
    mode: CryptoJS.mode.CBC
  }).toString();
  return encrypted;
}
export async function decrypt(text) {
  try {
    let secretKey = process.env.ENCRYPTION_KEY;
    //secretKey = await modifySecretKey(secretKey);
    const keyutf = CryptoJS.enc.Utf8.parse(secretKey);
    const iv = CryptoJS.enc.Utf8.parse('678025308de70905');
    const decrypted = CryptoJS.AES.decrypt(text, keyutf, {
      iv: iv,
      padding: CryptoJS.pad.Pkcs7,
      mode: CryptoJS.mode.CBC
    });
    const decryptedText = decrypted.toString(CryptoJS.enc.Utf8);
    return decryptedText;
  } catch (error) {
    console.error("Error decrypting text:", error);
    throw error;
  }
}

// utils/encryptDecrypt.js
import crypto from 'crypto';

const algorithm = 'aes-256-cbc';
const key = crypto.scryptSync(process.env.ENCRYPTION_SECRET, 'salt', 32); // 32 bytes for aes-256
const iv = crypto.randomBytes(16); // Initialization vector

// Encrypt function
export function encryptedData(data) {
  const iv = crypto.randomBytes(16); // generate a new IV for every encryption
  const cipher = crypto.createCipheriv(algorithm, key, iv);
  const jsonString = JSON.stringify(data);

  let encrypted = cipher.update(jsonString, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  // Return combined iv:encryptedData
  return `${iv.toString('hex')}:${encrypted}`;
}

// Decrypt function
export function decryptData(encryptedString) {
  if (typeof encryptedString !== 'string' || !encryptedString.includes(':')) {
    throw new Error('Invalid encrypted data format');
  }

  const [ivHex, encryptedHex] = encryptedString.split(':');
  const iv = Buffer.from(ivHex, 'hex');
  const encryptedBuffer = Buffer.from(encryptedHex, 'hex');

  const decipher = crypto.createDecipheriv(algorithm, key, iv);
  let decrypted = decipher.update(encryptedBuffer, undefined, 'utf8');
  decrypted += decipher.final('utf8');

  return JSON.parse(decrypted);
}


const excludedPaths = ['/ezee', '/clouds'];

export function responseEncryptMiddleware(req, res, next) {
  // Check if the request path starts with any of the excluded paths
  if (excludedPaths.some(path => req.path.startsWith(path))) {
    return next(); // Skip encryption
  }

  // Override res.json to encrypt the response before sending
  const originalJson = res.json.bind(res);
  res.json = (data) => {
    const encrypted = encryptedData(data);
    return originalJson(encrypted);
  };
  next();
}
