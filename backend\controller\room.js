import Response from "../utills/response.js";
import { addRoom, getRoomsByPropertyId, getRoomBySlug, deleteRoomBySlug, getRoomListByPropertyId, updateRoomById } from "../services/room.js";
import moment from 'moment'
import roomTypesModel from "../models/roomTypes.js";
import roomRatesModel from "../models/rates.js";
const addRoomController = async (req, res) => {
    try {
        const newRoom = await addRoom(req.body, req.user._id);
        return Response.Created(res, newRoom, 'Room Added Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const updateRoomController = async (req, res) => {
    try {
        const { id } = req.params;
        const updatedRoom = await updateRoomById(id, req.body);
        return Response.OK(res, updatedRoom, 'Room Updated Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const getRoomsByPropertyIdController = async (req, res) => {
    try {
        const { propertyId } = req.params;
        let { page, limit, ...filter } = req.query;

        // Convert page and limit to integers
        page = parseInt(page);
        limit = parseInt(limit);

        // If page or limit are not valid numbers, default them
        if (isNaN(page) || page <= 0) {
            page = 1;
        }
        if (isNaN(limit) || limit <= 0) {
            limit = 10;
        }

        const roomsData = await getRoomsByPropertyId(propertyId, filter, page, limit);

        const totalPages = Math.ceil(roomsData.totalRooms / parseInt(limit));
        const pagination = {
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages,
            totalRooms: roomsData.totalRooms
        };

        return Response.OK(res, { rooms: roomsData.rooms, pagination }, 'Rooms Retrieved Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};


const getRoomBySlugController = async (req, res) => {
    try {
        const { slug } = req.params;
        const room = await getRoomBySlug(slug);
        return Response.OK(res, room, 'Room Retrieved Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const deleteRoomController = async (req, res) => {
    try {
        const { slug } = req.params;
        const result = await deleteRoomBySlug(slug);
        return Response.OK(res, result, 'Room Deleted Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const getRoomListByPropertyIdController = async (req, res) => {
    try {
        const { propertyId } = req.params;
        if (!propertyId) {
            return Response.BadRequest(res, null, 'propertyId is required');
        }

        const rooms = await getRoomListByPropertyId(propertyId);
        return Response.OK(res, rooms, 'Rooms Retrieved Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};
const getRoomsRatesControllers = async (req, res) => {
    try {
        const { propertyId, startDate, endDate } = req.body;

        // Validate required fields
        if (!propertyId) {
            return Response.BadRequest(res, null, 'Property ID is required');
        }

        if (!startDate || !endDate) {
            return Response.BadRequest(res, null, 'Start date and end date are required');
        }

        // Convert dates to moment objects
        const start = moment(startDate);
        const end = moment(endDate);

        if (!start.isValid() || !end.isValid()) {
            return Response.BadRequest(res, null, 'Invalid date format');
        }

        if (end.isBefore(start)) {
            return Response.BadRequest(res, null, 'End date cannot be before start date');
        }

        // Fetch all rooms for the given property
        const rooms = await getRoomsByPropertyId(propertyId);

        if (!rooms || rooms.length === 0) {
            return Response.NotFound(res, null, 'No rooms found for the given property');
        }

        // Generate a list of all dates in the range
        const dateRange = [];
        for (let date = start.clone(); date.isSameOrBefore(end); date.add(1, 'days')) {
            dateRange.push({
                date: date.format('YYYY-MM-DD'),
                isWeekend: date.isoWeekday() >= 6, // Weekend if Saturday (6) or Sunday (7)
            });
        }

        // Group rooms by type and calculate rates for each date in the range
        const groupedRooms = rooms.rooms.reduce((acc, room) => {
            const type = room.type || 'Unknown Type';
            if (!acc[type]) {
                acc[type] = [];
            }
            // Add room details with rates for each date
            const roomRates = dateRange.map(({ date, isWeekend }) => ({
                date,
                rate: isWeekend ? room.rate.weekendRate.value : room.rate.weekdayRate.value,
                currency: room.currency,
            }));

            acc[type].push({
                name: room.name,
                capacity: room.capacity,
                rates: roomRates,
                slug: room.slug,
                images: room.images,
            });

            return acc;
        }, {});

        return Response.OK(res, groupedRooms, 'Room rates fetched successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};
const getRoomsTypesController = async (req, res) => {
    try {
        const roomTypes = await roomTypesModel.find()
        return Response.OK(res, { roomTypes }, 'Rooms Types');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const addRoomRate = async (req, res) => {
    try {
        const newRate = await roomRatesModel(req.body, req.user._id);
        return Response.Created(res, newRate, 'Room Added Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};
const getRoomsTypeRatesController = async (req, res) => {
    try {
        const roomRate = await roomRatesModel.find({room:req.query.room})
        return Response.OK(res, { roomRate }, 'Rooms Types');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

export const updateRoomRateController = async (req, res) => {
    try {
        const { roomTypeId, FromDate, ToDate, roomRate } = req.body;

        if (!roomTypeId || !FromDate || !ToDate || !roomRate?.BaseRate) {
            return res.status(400).json({ message: "Missing required fields." });
        }

        const roomType = await roomTypesModel.findOne({ RoomTypeId: roomTypeId })
        if (!roomType) {
            return res.status(404).json({ message: "Room type not found." });
        }

        const normalizeDate = (date) => new Date(date.getFullYear(), date.getMonth(), date.getDate());

        const start = normalizeDate(new Date(FromDate));
        const end = normalizeDate(new Date(ToDate));

        const baseRate = parseFloat(roomRate.BaseRate);
        const weekdayRateValue = roomRate.weekday ? parseFloat(roomRate.weekday) : baseRate;
        const weekendRateValue = roomRate.weekend ? parseFloat(roomRate.weekend) : baseRate;

        for (let dt = new Date(start); dt <= end; dt.setDate(dt.getDate() + 1)) {
            const currentDate = normalizeDate(new Date(dt));

            const existingRate = await roomRatesModel.findOne({
                room: roomType._id,
                fromDate: currentDate,
                toDate: currentDate,
                date: currentDate,
            });

            const isWeekend = [0, 6].includes(currentDate.getDay());
            const averagePrice = (weekdayRateValue + weekendRateValue) / 2;

            const rateData = {
                basePrice: baseRate,
                weekdayRate: { value: weekdayRateValue, updatedAt: new Date() },
                weekendRate: { value: weekendRateValue, updatedAt: new Date() },
                averagePrice: { value: averagePrice, updatedAt: new Date() },
            };

            if (existingRate) {
                existingRate.rate = rateData;
                await existingRate.save();
            } else {
                await roomRatesModel.create({
                    room: roomType._id,
                    fromDate: currentDate,
                    toDate: currentDate,
                    date: currentDate,
                    rate: rateData,
                });
            }
        }

        return res.status(200).json({ message: "Room rates updated successfully." });
    } catch (error) {
        console.error("Error updating room rates:", error);
        return res.status(500).json({ message: "Internal server error." });
    }
};

const getRoomTypesControllers = async (req, res) => {
    try {
        const newRoom = await roomTypesModel.find()
        return Response.Created(res, newRoom, 'Room Added Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};
export {
    addRoomController, updateRoomController, getRoomsByPropertyIdController,
    getRoomBySlugController, deleteRoomController,addRoomRate,
     getRoomListByPropertyIdController, 
     getRoomsRatesControllers,getRoomsTypeRatesController,
     getRoomsTypesController,getRoomTypesControllers
};