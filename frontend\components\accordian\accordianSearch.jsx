import React, {
  useEffect,
  useState,
  useImperativeHandle,
  forwardRef,
  useRef,
} from "react";
import { ChevronDown, ChevronUp, CalendarClock } from "lucide-react";
import Image from "next/image";
import { GoogleMap, Marker, InfoWindow } from "@react-google-maps/api";
// import axios from "axios";

// import { getReviewApi } from "@/services/webflowServices";
import ReactStars from "react-rating-stars-component";
import { getItemLocalStorage } from "@/utils/browserSetting";
import dynamic from "next/dynamic";
import { FaWhatsapp } from "react-icons/fa";
import { PiMapPinAreaLight } from "react-icons/pi";
import { GrMapLocation } from "react-icons/gr";
import { RxCrossCircled } from "react-icons/rx";
const WriteReview = dynamic(() => import("../review/writeReview"), {
  ssr: false,
});

const CustomPagination = dynamic(
  () => import("../customPagination/customPagination"),
  {
    ssr: false,
  }
);

const AccordionHeader = ({ title, isOpen, toggleSection, id }) => (
  <div
    className={`w-full flex justify-between text-white items-center p-4 cursor-pointer bg-black ${
      isOpen ? "rounded-t-xl" : "rounded-xl"
    }`}
    id={id}
    onClick={toggleSection}
  >
    <h3 className="text-lg font-bold">{title}</h3>
    {isOpen ? <ChevronUp size={24} /> : <ChevronDown size={24} />}
  </div>
);

// eslint-disable-next-line no-unused-vars
const AccordianSearch = forwardRef(({ combinedData, hostelIdd }, ref) => {
  const [selectedMarker, setSelectedMarker] = useState(false);
  // const [reviews, setReviews] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  // const [reviewPerPage] = useState(10);
  const [totalReviews, setTotalReviews] = useState(0);
  // eslint-disable-next-line no-unused-vars
  const [hostelId, setHostelId] = useState();
  const [rating, setRating] = useState();
  const [isApiLoaded, setIsApiLoaded] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [totalPages, setTotalPages] = useState(1);
  const [googleReviews, setGoogleReviews] = useState([]);
  const [isLoadingReviews, setIsLoadingReviews] = useState(false);
  const [reviewError, setReviewError] = useState(null);
  // eslint-disable-next-line no-unused-vars
  const [googlePlaceId, setGooglePlaceId] = useState(null);
  const reviewRef = useRef(null);
  const [showMapPopup, setShowMapPopup] = useState(false);
  const mapPopupRef = useRef(null);

  useEffect(() => {
    function handleClickOutside(event) {
      if (mapPopupRef.current && !mapPopupRef.current.contains(event.target)) {
        setShowMapPopup(false);
      }
    }

    if (showMapPopup) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showMapPopup]);

  useImperativeHandle(ref, () => ({
    scrollToReview: () => {
      if (reviewRef.current) {
        reviewRef.current.scrollIntoView({ behavior: "smooth" });
      }
    },
  }));

  const mapContainerStyle = {
    width: "486px",
    height: "330px",
    border: "0px",
    borderRadius: "14px",
  };

  const loadGoogleMapsApi = () => {
    const script = document.createElement("script");
    script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyBv_hPcDOPcrTfHnLrFNduHgJWDwv1pjfU&libraries=places`;
    script.async = true;
    script.onload = () => setIsApiLoaded(true);
    document.head.appendChild(script);
  };

  useEffect(() => {
    if (window.google && window.google.maps) {
      setIsApiLoaded(true);
    } else {
      loadGoogleMapsApi();
    }
  }, []);

  const [isOpen, setIsOpen] = useState({
    hostelRules: false,
    amenities: false,
    contactUs: false,
    reviews: false,
    cancellationPolicy: false,
    generalPolicy: false,
    thingsToNote: false,
  });

  const toggleSection = (section) => {
    setIsOpen((prevState) => ({
      ...prevState,
      [section]: !prevState[section],
    }));
  };

  // const hostel = {
  //   rating: 5.0,
  //   totalReviews: 24567,
  // };

  // const reviews = [
  //   {
  //     author: "Kara Luther (U.K)",
  //     rating: 5,
  //     content:
  //       "After several uses the hair is much thinner and isn&apos;t growing as fast.",
  //     images: [assets.Locker, assets.Wifi],
  //     helpful: 12,
  //     notHelpful: 12,
  //   },
  //   {
  //     author: "Jane Smith",
  //     rating: 4,
  //     content:
  //       "The location is perfect and the facilities are great. Will definitely come back!",
  //     images: [],
  //     helpful: 10,
  //     notHelpful: 3,
  //   },
  // ];
  console.log("propdata", combinedData);

  const facilityIcons = {
    BREAKFASTAVAILABLE: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/breakfast.svg`,
    LINENINCLUDED: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/linen_included.svg`,
    FREEPARKING: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/parking.svg`,
    PARKINGPUBLIC: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/parking.svg`,
    TOWELSONRENT: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/towels_on_rent.svg`,
    FREEWIFI: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/free_wi_fi.svg`,
    FREEINTERNETACCESS: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/internet.svg`,
    FREECITYMAPS: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/citymap.svg`,
    LOCKERS: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/lockers.svg`,
    HOTWATER: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/hot_water.svg`,
    LAUNDRYSERVICE: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/laundry_services.svg`,
    CARDPAYMENTACCEPTED: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/card_payment_accepted.svg`,
    COMMONTELEVISION: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/common_television.svg`,
    WATERDISPENSER: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/water_dispenser.svg`,
    AIRCONDITIONING: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/air_conditioning.svg`,
    RECEPTION24X7: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/24_7_reception.svg`,
    COMMONHANGOUTAREA: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/common_hangout_area.svg`,
    CAFE: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/cafe.svg`,
    INHOUSEACTIVITIES: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/in_house_activities.svg`,
    BEDSIDELAMPS: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/bedside_lamps.svg`,
    STORAGEFACILITY: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/storage_facility.svg`,
    UPIPAYMENTACCEPTED: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/upi_payment_accepted.svg`,
    SHOWER: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/shower.svg`,
    PARKING: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/parking.svg`,
    LAUNDRYSERVICES: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/laundry_services.svg`,
  };

  // const ratings = [
  //   { rating: 5, count: 20525 },
  //   { rating: 4, count: 2205 },
  //   { rating: 3, count: 25 },
  //   { rating: 2, count: 25 },
  //   { rating: 1, count: 12 },
  // ];
  const location = combinedData.property.location.coordinates;
  const photo = combinedData.property.photos[0] || [];
  console.log("photo", photo);

  useEffect(() => {
    const hostelIdLocal = getItemLocalStorage("hostelId");
    if (hostelIdLocal) {
      setHostelId(hostelIdLocal);
    }
  }, []);

  // useEffect(() => {
  //   if (isOpen?.reviews) {
  //     console.log("Fetching reviews");
  //     fetchReviews();
  //   }
  // }, [isOpen?.reviews]);

  // const fetchReviews = async () => {
  //   try {
  //     const response = await getReviewApi(
  //       hostelIdd,
  //       currentPage,
  //       reviewPerPage
  //     );
  //     console?.log("response", response);
  //     if (response.status) {
  //       setReviews(response?.data?.data?.reviews);
  //       setTotalReviews(response?.data?.data?.pagination?.totalReviews);
  //       setRating(response?.data?.data?.ratings);
  //       setTotalPages(response.data.data.pagination.totalPages);
  //     }
  //   } catch (error) {
  //     console.error("Error fetching reviews:", error);
  //   }
  // };

  const onPageChange = (page) => {
    setCurrentPage(page);
  };

  if (!location || location.length < 2) {
    return <p>Loading map...</p>; // Or return null, a spinner, or any other fallback UI
  }

  // Write review Modal
  const [openReview, setOpenReview] = useState(false);
  const handleOpen = () => setOpenReview(true);
  const handleClose = () => setOpenReview(false);

  const fetchGoogleReviews = async () => {
    if (!combinedData?.property || !isApiLoaded) return;

    setIsLoadingReviews(true);
    setReviewError(null);

    try {
      // const fullAddress = `${combinedData.property.address.lineOne}, ${combinedData.property.address.city}, ${combinedData.property.address.state}, ${combinedData.property.address.country}`;

      // Create Places Service
      const service = new window.google.maps.places.PlacesService(
        document.createElement("div")
      );

      // First find the place
      const placeRequest = {
        query: combinedData.property.name,
        location: new window.google.maps.LatLng(
          combinedData.property.location.coordinates[1],
          combinedData.property.location.coordinates[0]
        ),
        radius: 5000,
      };

      // Use textSearch to find the place
      service.textSearch(placeRequest, (results, status) => {
        if (
          status !== window.google.maps.places.PlacesServiceStatus.OK ||
          !results.length
        ) {
          setReviewError("Place not found");
          setIsLoadingReviews(false);
          return;
        }

        const placeId = results[0].place_id;

        // Get place details including reviews and photos
        const placeDetailsRequest = {
          placeId: placeId,
          fields: ["reviews", "photos", "name", "rating", "user_ratings_total"],
        };

        service.getDetails(placeDetailsRequest, (place, status) => {
          if (status !== window.google.maps.places.PlacesServiceStatus.OK) {
            setReviewError("Failed to get place details");
            setIsLoadingReviews(false);
            return;
          }

          // Get all photos
          const photos = place.photos || [];
          const photoUrls = photos.map((photo) =>
            photo.getUrl({ maxWidth: 400, maxHeight: 400 })
          );

          // Get all reviews with their photos
          const reviews = place.reviews || [];
          const formattedReviews = reviews.map((review) => {
            // Get review author's profile photo
            const authorPhotoUrl = review.profile_photo_url;

            // Get review photos if available
            const reviewPhotos = review.photos || [];
            const reviewPhotoUrls = reviewPhotos.map((photo) =>
              photo.getUrl({ maxWidth: 400, maxHeight: 400 })
            );

            return {
              author_name: review.author_name,
              author_url: review.author_url,
              language: review.language,
              profile_photo_url: authorPhotoUrl,
              rating: review.rating,
              relative_time_description: review.relative_time_description,
              text: review.text,
              time: review.time,
              helpful: 0,
              notHelpful: 0,
              photos: reviewPhotoUrls.length > 0 ? reviewPhotoUrls : photoUrls, // Use review photos if available, otherwise use place photos
            };
          });

          // Update the hostel rating and total reviews
          if (place.rating) {
            setRating(place.rating);
          }
          if (place.user_ratings_total) {
            setTotalReviews(place.user_ratings_total);
          }

          setGoogleReviews(formattedReviews);
          setGooglePlaceId(placeId);
          setIsLoadingReviews(false);
        });
      });
    } catch (error) {
      console.error("Error fetching Google reviews:", error);
      setReviewError(error.message);
      setIsLoadingReviews(false);
    }
  };

  useEffect(() => {
    if (isOpen.reviews && isApiLoaded) {
      fetchGoogleReviews();
    }
  }, [isOpen.reviews, combinedData?.property, isApiLoaded]);

  useEffect(() => {
    const handleOpenAccordion = (e) => {
      if (e.detail === "reviews" && !isOpen.reviews) {
        toggleSection("reviews");
      }
      if (e.detail === "contactUs" && !isOpen.contactUs) {
        toggleSection("contactUs");
      }
    };

    window.addEventListener("openAccordion", handleOpenAccordion);

    return () => {
      window.removeEventListener("openAccordion", handleOpenAccordion);
    };
  }, [isOpen.reviews, isOpen.contactUs, toggleSection]);

  return (
    <>
      <div className="md:mt-8 md:space-y-6 space-y-3 py-4 mb-6 lg:mb-10 ">
        {/* Hostel Rules Section */}
        <div>
          <AccordionHeader
            title="Hostel Rules"
            isOpen={isOpen.hostelRules}
            toggleSection={() => toggleSection("hostelRules")}
            id="hostelRules"
          />
          {isOpen.hostelRules && (
            <div className="w-full rounded-b-xl p-4 bg-[#F1F1F1]">
              <div className="xs:flex py-2 border-b border-b-gray-300">
                <div className="flex items-center gap-7">
                  <div className="px-2">
                    <p className="text-sm mb-1 font-bold text-black">
                      Check In
                    </p>
                    <div className="flex">
                      <CalendarClock className="mr-2 text-black" size={18} />
                      <p className="text-sm mb-0 font-bold text-black">
                        {combinedData?.property?.rules?.checkIn?.hour}:
                        {combinedData?.property?.rules?.checkIn?.min}
                      </p>
                    </div>
                  </div>
                  <div className="px-2">
                    <p className="text-sm mb-1 font-bold text-black">
                      Check Out
                    </p>
                    <div className="flex items-center">
                      <CalendarClock className="mr-2 text-black" size={18} />
                      <p className="text-sm mb-0 font-bold text-black">
                        {combinedData?.property?.rules?.checkOut?.hour}:
                        {combinedData?.property?.rules?.checkOut?.min}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="flex-1 xs:text-right xs:ml-0 ml-4 xs:mt-0 mt-3">
                  <p className="font-semibold text-base text-black">
                    Taxes Not Included
                  </p>
                </div>
              </div>
              <div className="py-4 border-b border-b-gray-300">
                <button
                  onClick={() => toggleSection("thingsToNote")}
                  className="w-full text-sm text-left  font-bold text-black flex justify-between items-center"
                >
                  Things to Note (Rules)
                  {isOpen.thingsToNote ? (
                    <ChevronUp size={24} />
                  ) : (
                    <ChevronDown size={24} />
                  )}
                </button>
                {isOpen.thingsToNote && (
                  <div className="mt-4 pl-4">
                    {combinedData?.property?.rules?.thingsToNote?.length > 0 ? (
                      <p className="text-sm text-gray-600 whitespace-pre-line">
                        {combinedData?.property?.rules?.thingsToNote}
                      </p>
                    ) : (
                      <p className="text-sm text-gray-600">
                        No things to note available
                      </p>
                    )}
                  </div>
                )}
              </div>
              <div className="py-4 border-b border-b-gray-300">
                <button
                  className="w-full text-sm text-left font-bold text-black flex justify-between items-center"
                  onClick={() => toggleSection("cancellationPolicy")}
                >
                  Cancellation Policy
                  {isOpen.cancellationPolicy ? (
                    <ChevronUp size={24} />
                  ) : (
                    <ChevronDown size={24} />
                  )}
                </button>
                {isOpen.cancellationPolicy && (
                  <div className="mt-4 pl-4">
                    {combinedData?.property?.rules?.cancellation?.length > 0 ? (
                      <p className="text-sm text-gray-600 whitespace-pre-line">
                        {combinedData?.property?.rules?.cancellation}
                      </p>
                    ) : (
                      <p className="text-sm text-gray-600">
                        No cancellation policies available
                      </p>
                    )}
                  </div>
                )}
              </div>
              <div className="py-4">
                <button
                  className="w-full text-sm text-left  font-bold text-black flex justify-between items-center"
                  onClick={() => toggleSection("generalPolicy")}
                >
                  General Policy
                  {isOpen.generalPolicy ? (
                    <ChevronUp size={24} />
                  ) : (
                    <ChevronDown size={24} />
                  )}
                </button>
                {isOpen.generalPolicy && (
                  <div className="mt-4 pl-4">
                    {combinedData?.property?.rules?.generalPolicy?.length >
                    0 ? (
                      <p className="text-sm text-gray-600 whitespace-pre-line">
                        {combinedData?.property?.rules?.generalPolicy}
                      </p>
                    ) : (
                      <p className="text-sm text-gray-600">
                        No General Policy available
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Amenities Section */}
        <div>
          <AccordionHeader
            title="Amenities"
            isOpen={isOpen.amenities}
            toggleSection={() => toggleSection("amenities")}
            id="amenities"
          />
          {/* {isOpen.amenities && (
            <div className="w-full rounded-b-xl p-4 bg-[#F1F1F1]">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {combinedData?.property?.freeFacilities?.map(
                  (facility, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      {facilityIcons[facility.id] && (
                        <div className="flex items-center justify-center w-8 h-8">
                          <Image
                            src={facilityIcons[facility.id]}
                            alt={`${facility.name} Icon`}
                            width={18}
                            height={18}
                            className="w-[18px] h-[18px]"
                            aria-label={facility.name}
                            loading="lazy"
                          />
                        </div>
                      )}
                      <h6 className="text-sm mb-0 py-1 flex-1 text-black font-semibold">
                        {facility.name}
                      </h6>
                    </div>
                  )
                )}
              </div>
            </div>
          )} */}
          {isOpen.amenities && (
            <div className="w-full rounded-b-xl p-4 bg-[#F1F1F1]">
              {combinedData?.property?.freeFacilities?.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {combinedData.property.freeFacilities.map(
                    (facility, index) => (
                      <div key={index} className="flex items-start space-x-2">
                        {facilityIcons[facility.id] && (
                          <div className="flex items-center justify-center w-8 h-8">
                            <Image
                              src={facilityIcons[facility.id]}
                              alt={`${facility.name} Icon`}
                              width={18}
                              height={18}
                              className="w-[18px] h-[18px]"
                              aria-label={facility.name}
                              loading="lazy"
                            />
                          </div>
                        )}
                        <h6 className="text-sm mb-0 py-1 flex-1 text-black font-semibold">
                          {facility.name}
                        </h6>
                      </div>
                    )
                  )}
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-gray-500 font-manrope">
                    No Amenities Available
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Reviews Section */}

        <div ref={reviewRef}>
          <AccordionHeader
            title="Reviews"
            isOpen={isOpen.reviews}
            toggleSection={() => toggleSection("reviews")}
            id="review_acc"
          />
          {isOpen.reviews && (
            <div className="w-full rounded-b-xl p-6 bg-[#F1F1F1]">
              <div className="flex flex-col md:flex-row md:items-center pb-4 border-b border-b-gray-300">
                <div className="text-center md:text-left">
                  <div className="text-2xl font-medium text-black">
                    {rating?.toFixed(1)}
                  </div>
                  <div className="flex items-center justify-center md:justify-start space-x-1 text-yellow-500">
                    <ReactStars
                      key={rating}
                      count={5}
                      size={20}
                      activeColor="#ffd700"
                      value={rating}
                      edit={false}
                      isHalf={true}
                    />
                  </div>
                  <div className="text-sm text-black font-semibold py-2">
                    {totalReviews} Customers Reviews
                  </div>
                  <button
                    onClick={handleOpen}
                    className="mt-4 bg-black text-white px-4 py-2 rounded-3xl"
                  >
                    Write Review
                  </button>
                </div>

                {/* <div className='mt-4 md:mt-0 ml-[10%]'>
                  {ratings.map(({ rating, count }) => (
                    <div key={rating} className='flex items-center space-y-2'>
                      <span className='font-normal text-sm'>{rating}</span>
                      <div className='relative mx-2'>
                        <div className='flex-1 h-2 bg-gray-300 rounded-full'>
                          <div className='absolute top-0 left-0 w-40 h-1.5 bg-gray-300 rounded-lg'></div>
                          <div
                            className='h-2 bg-gray rounded-full text-black/80'
                            style={{
                              width: `${(count / hostel.totalReviews) * 100}%`,
                            }}
                          />
                        </div>
                      </div>
                      <span className='font-normal ml-[10REM] text-black/80 text-sm'>
                        {" "}
                        ({count})
                      </span>
                    </div>
                  ))}
                </div> */}
              </div>

              {isLoadingReviews ? (
                <div className="flex justify-center items-center py-8">
                  <div className="animate-spin rounded-full h-10 w-10 border-4 border-[#40E0D0] border-t-transparent"></div>
                </div>
              ) : reviewError ? (
                <div className="text-red-500 text-center py-4">
                  Error loading reviews: {reviewError}
                </div>
              ) : (
                <div className="space-y-4">
                  {googleReviews.map((review, index) => (
                    <div key={index} className="py-4 rounded-3xl">
                      <div className="flex flex-col mb-2">
                        <div className="flex items-center gap-2">
                          <Image
                            src={review.profile_photo_url}
                            alt={`${review.author_name}'s profile`}
                            width={40}
                            height={40}
                            className="rounded-full"
                            loading="lazy"
                          />
                          <span className="text-black font-semibold">
                            {review.author_name}
                          </span>
                        </div>
                        <div className="flex items-center py-2 space-x-1 text-yellow-500">
                          <ReactStars
                            key={review?.rating}
                            count={5}
                            size={40}
                            activeColor="#ffd700"
                            value={review.rating}
                            edit={false}
                            isHalf={true}
                          />
                        </div>
                      </div>

                      <p className="text-black text-base mb-2">{review.text}</p>

                      {/* Display photos in a grid */}
                      {/* {review.photos && review.photos.length > 0 && (
                        <div className="grid grid-cols-3 gap-2 mb-4">
                          {review.photos.map((photoUrl, photoIndex) => (
                            <div key={photoIndex} className="relative aspect-square">
                              <Image
                                src={photoUrl}
                                alt={`Review photo ${photoIndex + 1}`}
                                fill
                                className="object-cover rounded-lg"
                                loading="lazy"
                              />
                            </div>
                          ))}
                        </div>
                      )} */}

                      {/* <div className="flex items-center text-black text-base">
                        <span className="mr-2">Is this helpful?</span>
                        <div className="flex items-center space-x-1">
                          <button className="flex items-center space-x-1">
                            <span>{review.helpful}</span>
                          </button>
                          <button className="flex items-center space-x-1">
                            <span>{review.notHelpful}</span>
                          </button>
                        </div>
                      </div> */}
                    </div>
                  ))}
                </div>
              )}

              {totalPages > 1 && (
                <div className="flex justify-center lg:py-10 text-center">
                  <CustomPagination
                    currentPage={currentPage}
                    total={totalPages}
                    onPageChange={onPageChange}
                  />
                </div>
              )}

              {/* {googlePlaceId && (
                <div className='flex justify-center mt-4'>
                  <a
                    href={`https://www.google.com/maps/place/?q=place_id:${googlePlaceId}`}
                    target='_blank'
                    rel='noopener noreferrer'
                    className='bg-black text-white px-4 py-2 rounded-3xl hover:bg-gray-800 transition'
                  >
                    View More Reviews on Google
                  </a>
                </div>
              )} */}
            </div>
          )}
        </div>

        {/* Contact Us Section */}
        <div>
          <AccordionHeader
            title="Address"
            isOpen={isOpen.contactUs}
            toggleSection={() => toggleSection("contactUs")}
            id="contactUs"
          />
          {isOpen.contactUs && (
            <div className="flex  w-full rounded-b-xl py-6 px-2 bg-[#F1F1F1] h-auto">
              {/* Address Section */}
              <div className="w-full mb-4 lg:mb-0  ">
                {" "}
                <div className="mb-4">
                  <div className="flex items-center">
                    <PiMapPinAreaLight className="font-extrabold text-xl" />
                    <h3 className="text-base font-semibold flex items-centerml-1 ml-1.5">
                      {" "}
                      Address
                    </h3>
                  </div>
                  <p className="text-sm text-gray-500 py-2 leading-6 ml-6">
                    {combinedData?.property?.address?.lineOne}
                    {"  "}
                    {combinedData?.property?.address?.lineTwo}{" "}
                    {combinedData?.property?.address?.state}
                    {"  "}
                    {combinedData?.property?.address?.country}
                  </p>
                  <button
                    className="bg-black py-2.5 text-white rounded-3xl text-sm font-manrope px-6 ml-6 flex items-center gap-x-1 mt-2 md:hidden"
                    onClick={() => setShowMapPopup(true)}
                  >
                    <GrMapLocation className="text-lg" />
                    Tap to open map
                  </button>
                  {showMapPopup && (
                    <div className="fixed inset-0 z-[9999] bg-black bg-opacity-60 flex justify-center items-center p-4">
                      <div
                        ref={mapPopupRef}
                        className="relative w-full h-[80vh] max-w-3xl bg-white rounded-lg overflow-hidden"
                      >
                        <button
                          className="absolute top-2 right-2 text-black z-10"
                          onClick={() => setShowMapPopup(false)}
                        >
                          <RxCrossCircled className="text-2xl" />
                        </button>

                        {isApiLoaded ? (
                          <GoogleMap
                            mapContainerStyle={{
                              width: "100%",
                              height: "100%",
                            }}
                            center={{ lat: location[1], lng: location[0] }}
                            zoom={14}
                          >
                            <Marker
                              position={{ lat: location[1], lng: location[0] }}
                              onClick={() =>
                                setSelectedMarker({
                                  lat: location[1],
                                  lng: location[0],
                                })
                              }
                            />
                            {selectedMarker && (
                              <InfoWindow
                                position={selectedMarker}
                                onCloseClick={() => setSelectedMarker(null)}
                              >
                                <div>
                                  <h2>{combinedData.property.name}</h2>
                                </div>
                              </InfoWindow>
                            )}
                          </GoogleMap>
                        ) : (
                          <p className="text-center text-white">
                            Loading Google Maps API...
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                {/* WhatsApp Section */}
                <div className="">
                  <div className="flex items-center">
                    <FaWhatsapp className="font-extrabold text-xl" />
                    <h3 className="text-base font-semibold ml-1.5">
                      What&apos;s App
                    </h3>
                  </div>
                  <p className="text-sm text-gray-500 py-2 ml-6">
                    Unable to Whatsapp? Call directly
                  </p>
                  {combinedData?.property?.phoneNumber && (
                    <button className="bg-black py-2.5 text-white rounded-3xl text-sm font-manrope px-6 ml-6 flex items-center gap-x-1 mt-2">
                      <FaWhatsapp className="text-lg" />
                      {combinedData?.property?.phoneNumber || ""}
                    </button>
                  )}
                </div>
              </div>

              {/* Map Section */}
              <div className="hidden md:block ">
                {isApiLoaded ? (
                  <GoogleMap
                    mapContainerStyle={mapContainerStyle}
                    center={{ lat: location[1], lng: location[0] }}
                    zoom={14}
                  >
                    <Marker
                      position={{ lat: location[1], lng: location[0] }}
                      onClick={() =>
                        setSelectedMarker({
                          lat: location[1],
                          lng: location[0],
                        })
                      }
                    />
                    {selectedMarker && (
                      <InfoWindow
                        position={selectedMarker}
                        onCloseClick={() => setSelectedMarker(null)}
                      >
                        <div>
                          <h2>{combinedData.property.name}</h2>
                        </div>
                      </InfoWindow>
                    )}
                  </GoogleMap>
                ) : (
                  <p>Loading Google Maps API...</p>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
      <WriteReview open={openReview} close={handleClose} />
    </>
  );
});

AccordianSearch.displayName = "AccordianSearch";
export default AccordianSearch;
