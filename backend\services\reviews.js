import reviewModel from '../models/reviews.js';
import mongoose from 'mongoose';

// Function to add a new review
const addReview = async (data) => {
    return await reviewModel.create(data);
};

// Function to update a review by ID
const updateReviewById = async (reviewId, newData) => {
    try {
        const review = await reviewModel.findById({ _id: reviewId });
        if (!review) {
            throw new Error('Review Not Found');
        }

        const updatedReview = await reviewModel.findByIdAndUpdate(reviewId, { ...newData }, { new: true });
        return updatedReview;
    } catch (error) {
        console.error("Error updating review:", error.message);
        throw error;
    }
};

// Function to get a review by ID
const getReviewById = async (reviewId) => {
    try {
        const review = await reviewModel.findById({ _id: reviewId });
        if (!review) {
            throw new Error('Review Not Found');
        }
        return review;
    } catch (error) {
        console.error("Error getting review:", error.message);
        throw error;
    }
};

// Function to list all reviews for a hostel with pagination and filter
const listAllReviewsByProperty = async (propertyId, filter, page, limit) => {
    const skip = (page - 1) * limit;
    const query = { ...filter, property: new mongoose.Types.ObjectId(propertyId), isActive: true };
     // If date range exists in the filter, add it to the query
     if (filter.startDate || filter.endDate) {
        query.createdAt = {};
        if (filter.startDate) {
            query.createdAt.$gte = new Date(filter.startDate);
        }
        if (filter.endDate) {
            query.createdAt.$lte = new Date(filter.endDate);
        }
    }

    const reviews = await reviewModel.find(query)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .populate({
            path: 'user',
            select: 'name'
        })
        .populate({
            path: 'property',
            select: 'name',
            populate: {
                path: 'propertyOwner',
                select: 'name'
            }
        }).exec();

    const totalReviews = await reviewModel.countDocuments(query);

    // Simplify the aggregation pipeline for debugging
    const ratingDistribution = await reviewModel.aggregate([
        { $match: { property: new mongoose.Types.ObjectId(propertyId), isActive: true } },
        {
            $group: {
                _id: '$rating',
                count: { $sum: 1 }
            }
        }
    ]);

    const ratings = {};
    ratingDistribution.forEach(rating => {
        ratings[rating._id] = rating.count;
    });

    return { reviews, totalReviews, totalPages: Math.ceil(totalReviews / limit), ratings };
};


// Function to list all reviews with pagination and filter
const listAllReviews = async (filter, page, limit) => {
    const skip = (page - 1) * limit;
    const query = { ...filter, isActive: true };

    const reviews = await reviewModel.find(query)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .populate({
            path: 'user',
            select: 'name'
        })
        .populate({
            path: 'property',
            select: 'name',
            populate: {
                path: 'propertyOwner',
                select: 'name'
            }
        }).exec();

    const totalReviews = await reviewModel.countDocuments(query);

    // Calculate star rating count per property
    const ratingDistribution = await reviewModel.aggregate([
        { $match: query },
        {
            $group: {
                _id: {
                    property: '$property',
                    rating: '$rating'
                },
                count: { $sum: 1 }
            }
        },
        {
            $group: {
                _id: '$_id.property',
                ratings: {
                    $push: {
                        rating: '$_id.rating',
                        count: '$count'
                    }
                }
            }
        }
    ]);

    const ratings = {};
    ratingDistribution.forEach(rating => {
        ratings[rating._id] = rating.ratings.reduce((acc, cur) => {
            acc[cur.rating] = cur.count;
            return acc;
        }, {});
    });

    return { reviews, totalReviews, totalPages: Math.ceil(totalReviews / limit), ratings };
};

// Function to delete a review by ID
const deleteReviewById = async (reviewId) => {
    try {
        const review = await reviewModel.findById({ _id: reviewId });
        if (!review) {
            throw new Error('Review Not Found');
        }
        await reviewModel.findByIdAndUpdate({ _id: reviewId }, { idActive: false, isDeleted: true });
        return { message: 'Deleted Successfully' };
    } catch (error) {
        console.error("Error deleting review:", error.message);
        throw error;
    }
};

const getRatingDetailsByPropertyId = async (propertyId) => {
    try {
        if (!propertyId) {
            throw new Error('Property ID is required.');
        }

        const reviews = await reviewModel.aggregate([
            {
                $match: {
                    property: new mongoose.Types.ObjectId(propertyId),
                    isActive: true,
                    isDeleted: false
                }
            },
            {
                $group: {
                    _id: "$rating",
                    reviews: { $sum: 1 },
                    totalStars: { $sum: "$rating" }
                }
            },
            {
                $sort: { _id: 1 }
            }
        ]);

        const totalReviews = reviews.reduce((acc, curr) => acc + curr.reviews, 0);
        const totalStars = reviews.reduce((acc, curr) => acc + (curr._id * curr.reviews), 0);
        const avgRating = totalReviews ? (totalStars / totalReviews).toFixed(2) : "0.00"; // Rounded to 2 decimal places

        const reviewDetails = reviews.map(r => ({
            star: r._id,
            reviews: r.reviews
        }));

        return {
            overallRating : avgRating,
            totalReviews,
            reviews: reviewDetails
        };

    } catch (error) {
        console.error("Error fetching rating details by property ID:", error.message);
        throw error;
    }
};

export { addReview, updateReviewById, getReviewById, listAllReviewsByProperty, deleteReviewById, listAllReviews, getRatingDetailsByPropertyId };