/* @import url('https://fonts.googleapis.com/css2?family=Mashiny&display=swap'); */

/* NProgress Customization */
#nprogress .bar {
  background: #40e0d0 !important;
  /* Progress bar color */
  height: 4px !important;
}

#nprogress .peg {
  box-shadow: 0 0 10px #40e0d0, 0 0 5px #40e0d0;
  /* Glowing effect */
}

/* Optionally hide the spinner */
#nprogress .spinner-icon {
  display: none !important;
}

@font-face {
  font-family: 'Mashiny';
  src: url('/fonts/Mashiny2.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

html
{
  scroll-behavior: smooth;
}

body {
  font-size: 16px;
  font-weight: 400;
  color: #000;
  background-color: #fff;
  scroll-behavior: smooth;

}

.swiper-container {
  width: 80px;
  height: 40px;
}

.swiper-slide {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  font-size: 2rem;
  font-family: monospace;
  letter-spacing: 0.08rem;

}



.c1 {
  background-color: indianred;
}

.c2 {
  background-color: teal;
}

.c3 {
  background-color: lightskyblue;
}

.c4 {
  background-color: lightcoral;
}

.c5 {
  background-color: lightgoldenrodyellow;
}

.input-otp input {
  @apply w-6 h-6 text-xs font-light p-1.5 bg-transparent border-1.5 rounded-full border-gray-350 outline-none focus:outline-none placeholder:text-gray-50/40 text-gray-50;
}

/* .swiper-wrapper {
  align-items: center;
} */

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

/* Fancy Scroll */

.fancy_y_scroll::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #f5f5f5;
  border-radius: 20px;
}

.fancy_y_scroll::-webkit-scrollbar {
  width: 6px;
  background-color: #f5f5f5;
}

.fancy_y_scroll::-webkit-scrollbar-thumb {
  background-color: #40e0d0;
  border-radius: 20px;
}

/* Selected date background */
.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus {
  background: #40E0D0 !important;
  border-color: #40E0D0 !important;
  color: white !important;
}

/* Today's date border color */
.flatpickr-day.today {
  border: 1px solid #40E0D0 !important;
}
.flatpickr-day:hover {
  background: #bbf9f3 !important;
  border-color: #40E0D0 !important;
  color: black !important;
}


/* Target react-select's menuList via class prefix */
.react-select__menu-list {
  max-height: 200px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #40e0d0 #ffffff;
}

.react-select__menu-list::-webkit-scrollbar {
  width: 6px;
}

.react-select__menu-list::-webkit-scrollbar-thumb {
  background-color: #40e0d0;
  border-radius: 4px;
}

.react-select__menu-list::-webkit-scrollbar-track {
  background-color: #ffffff;
}


/* Custom Checkbox On Checkout Page */
.custom_radio input+div:before {
  opacity: 0;
}

.custom_radio input:checked+div:before {
  opacity: 1;
}

/* Map Pin Location */
.MuiSelect-select {
  background: transparent !important;
}

.available_calender .MuiInput-root::before {
  display: none;
}

.gm-style .gm-style-iw-c {
  width: 100%;
  padding: 0px 0 0 0px !important;
  border-radius: 20px;
  max-width: 360px !important;
}

.gm-style .gm-style-iw-d {
  overflow: auto !important;
}

.gm-style-iw-chr {
  position: absolute;
  top: 0;
  right: 6px;
}


/* .scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
} */
/* ::-webkit-scrollbar {
  height: 4px;
  width: 4px;
}

::-webkit-scrollbar-thumb {
  background-color: #9ca6b6; 
  border-radius: 2px;
}

::-webkit-scrollbar-track {
  background-color: transparent;
} */




/* @font-face {
  font-family: 'Mashiny';
  src: url('/fonts/Mashiny.woff2') format('woff2'),
    url('/fonts/Mashiny.woff') format('woff'),
    url('/fonts/Mashiny.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
} */


/* Marquee */

.marquee {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  animation: marquee 55s linear infinite;
}

.marquee.paused {
  animation-play-state: paused;
}

@keyframes marquee {
  from {
    transform: translateX(100%);
  }

  to {
    transform: translateX(-100%);
  }
}



















@media screen and (min-width: 1140px) {
  .inner_search input {
    font-size: 0.75rem !important;
  }
}

input {
  @apply bg-transparent;
}

/* 
.react-datepicker__day--in-selecting-range:not(
    .react-datepicker__day--in-range,
    .react-datepicker__month-text--in-range,
    .react-datepicker__quarter-text--in-range,
    .react-datepicker__year-text--in-range
  ),
.react-datepicker__month-text--in-selecting-range:not(
    .react-datepicker__day--in-range,
    .react-datepicker__month-text--in-range,
    .react-datepicker__quarter-text--in-range,
    .react-datepicker__year-text--in-range
  ),
.react-datepicker__day--keyboard-selected:hover,
.react-datepicker__month-text--keyboard-selected:hover,
.react-datepicker__quarter-text--keyboard-selected:hover,
.react-datepicker__year-text--keyboard-selected:hover,
.react-datepicker__day--in-range {
  background-color: #40e0d0 !important;
} */

/* 
.listing .swiper-pagination {
  background-color: #fff;
  font-weight: 600;
  display: flex;
  width: auto;
  padding: 1px 6px;
  border-radius: 4px;
  max-width: 50px;
  left: 50%;
  transform: translateX(-50%);
}
.listing .swiper-button-prev:hover,
.listing .swiper-button-next:hover {
  opacity: 1;
}
.listing .swiper-button-prev,
.listing .swiper-button-next {
  color: #000;
  background: #40e0d0;
  width: 30px;
  height: 30px;
  border-radius: 6px;
  padding: 2px;
  opacity: 0.5;
}
.swiper-button-prev:after,
.swiper-button-next:after {
  font-size: 17px !important;
  font-weight: 600;
} */
.listing .swiper-pagination-bullet {
  background-color: #fff;
}

.react-datepicker-popper .react-datepicker {
  display: flex;
  border: 0;
  box-shadow: 0px 0px 21px 0px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  padding-bottom: 3.8rem;
  position: relative;
}

.react-datepicker-popper .react-datepicker__triangle {
  stroke: none !important;
}

.react-datepicker__day-names {
  display: flex;
  justify-content: space-evenly;
}

.react-datepicker-popper .react-datepicker__day-name {
  font-weight: 400;
  align-items: center;
  color: #636c7d;
  font-size: 0.875rem;
  letter-spacing: 0.00625rem;
  line-height: 1.25rem;
  text-transform: capitalize;
  height: 48px;
  display: flex;
}

.react-datepicker-popper .react-datepicker__header {
  background-color: #fff;
  border-bottom: 0px;
}

.react-datepicker-popper {
  top: 30px !important;
  transform: translate(-192px, 42.4px) !important;
}

.custom-calendar .react-datepicker__day {
  font-weight: 500;
}

.custom-calendar .react-datepicker__day--today {
  background: none;
  font-weight: 600;
  color: #40e0d0 !important;
}

.custom-calendar .react-datepicker__month-container:nth-of-type(1) .react-datepicker__header {
  border-top-left-radius: 20px !important;
}

.custom-calendar .react-datepicker__month-container:nth-of-type(2) .react-datepicker__header {
  border-top-right-radius: 20px !important;
  border-top-left-radius: 0px !important;
}

/* Custom calendar day appearance */
.custom-calendar .react-datepicker__day {
  display: inline-block;
  width: 48px;
  height: 48px;
  line-height: 48px;
  color: #121417;
  font-size: 16px;
  text-align: center;
  margin: 0;
  border-radius: 4px;
  transition: background-color 0.2s ease;

}

/* Highlight start and end dates */
.custom-calendar .react-datepicker__day--in-range.react-datepicker__day--range-start {
  background-color: #40e0d0;
  color: #000 !important;
  border-radius: 4px 0px 0 4px !important;
  /* Rounded on the left side */
  z-index: 999;
}

.custom-calendar .react-datepicker__day--in-selecting-range:not(.react-datepicker__day--in-range,
  .react-datepicker__month-text--in-range,
  .react-datepicker__quarter-text--in-range,
  .react-datepicker__year-text--in-range) {
  background: #40e0d0;
  color: #fff;
}

/* End date - right side rounded */
.custom-calendar .react-datepicker__day--in-range.react-datepicker__day--range-end {
  background-color: #40e0d0 !important;
  color: #fff;
  border-radius: 0 4px 4px 0 !important;
  /* Rounded on the right side */
  z-index: 1;
}

/* Highlight in-range dates */
/* .custom-calendar .react-datepicker__day--in-range {
  background-color: #a9fcf4;
  color: #000;
  border-radius: 4px;
} */

/* Remove selection styles from disabled or blank days */
.custom-calendar .react-datepicker__day--disabled,
.custom-calendar .react-datepicker__day--outside-month {
  background-color: transparent !important;
  color: #ddd !important;
  pointer-events: none;
}

/* Adjust blank days to avoid being affected by styles */
.custom-calendar .react-datepicker__day--outside-month {
  background-color: none !important;
  border: none !important;
}

.custom-calendar .react-datepicker__day--outside-month.react-datepicker__day--disabled {
  background-color: unset !important;
  border: none !important;
}

/* Hover effect */
.react-datepicker.custom-calendar .react-datepicker__day:hover {
  background-color: #40e0d0 !important;
  color: #000 !important;
  font-weight: 600 !important;
  border-radius: 4px !important;
}

.custom-calendar .react-datepicker__day--in-range.react-datepicker__day--range-start {
  border-radius: 4px;
  background-color: #40e0d0;
}

.custom-calendar .react-datepicker__day--range-end.react-datepicker__day--outside-month , .custom-calendar .react-datepicker__day--in-range.react-datepicker__day--outside-month {
  background-color: transparent !important;
  background: unset !important;
  opacity: 0 !important;
  color: #ddd !important;
  border-radius: 0 !important;
  pointer-events: none;
  z-index: 0 !important;
}

.custom-calendar .last-date
{
  background: linear-gradient(90deg, #fff0 70%, #fff), #fbcfc0;
}

.custom-calendar .first-date
{
  background: linear-gradient(90deg, #fff, #fff0 30%), #fbcfc0;
}


/* Inner Calendar */

.booking-block {
  background-color: #f0f0f0;
  /* Add some background for visibility */
  border-radius: 4px;
  padding: 2px 5px;
  margin: 2px 0;
}

.booking-progress {
  transition: width 0.2s ease;
  /* Smooth transition */
}

.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loader-button {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.no-top-border:nth-child(-n+6) {
  border-top: none !important;
}

.no-left-border:nth-child(7) {
  border-left: none !important;
}
@keyframes flicker {
  0% { box-shadow: 0 0 5px rgba(255, 100, 100, 0.5); }
  50% { box-shadow: 0 0 10px rgba(255, 50, 50, 0.8); }
  100% { box-shadow: 0 0 5px rgba(255, 100, 100, 0.5); }
}

.booking-banner {
  animation: flicker 2s infinite;
}


/* Media Querry */

@media screen and (max-width: 768px) {
  .search_map>div+div {
    height: 300px !important;
  }

  .responsive-table thead {
    display: none;
  }

  .responsive-table tbody tr {
    display: grid;
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .responsive-table tbody tr th {
    text-align: left;
    padding-left: 155px;
    position: relative;
    font-size: 14px;
    padding-top: 5px;
    padding-bottom: 5px;
  }

  .responsive-table tbody tr td {
    text-align: left;
    padding-left: 155px;
    position: relative;
    font-size: 14px;
    padding-top: 5px;
    padding-bottom: 5px;
  }

  .responsive-table tbody tr td div,
  .responsive-table tbody tr td svg,
  .responsive-table tbody tr td ul {
    margin: 0;
  }

  .responsive-table tbody tr td ul {
    display: flex;
    justify-content: flex-start;
  }

  .responsive-table tbody tr th::after {
    position: absolute;
    content: attr(title);
    top: 7px;
    left: 44px;
    width: 178px;
    font-size: 14px;
    text-transform: uppercase;
  }

  .responsive-table tbody tr td::after {
    position: absolute;
    content: attr(title);
    top: 8px;
    left: 44px;
    width: 178px;
    font-size: 14px;
    text-transform: uppercase;
  }
}

@media screen and (max-width: 640px) {


    .mobile-left-offset {
    left: -80px !important;
  }
  .custom-calendar .react-datepicker__month-container {
    float: none;
  }

  .react-datepicker-popper .react-datepicker {
    display: block;
  }

  .react-datepicker-popper {
    top: 50px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 9999 !important;
  }
}

/* tooltip */
.comman-tooltip[data-title] {
  position: relative;
  /* cursor: help; */
}

.comman-tooltip[data-title]:hover::before {
  content: attr(data-title) !important;
  position: absolute;
  top: 30px;
  right: 0;
  left: 46px;
  transform: translateX(-50%);
  background-color: #000;
  color: rgb(255 255 255 / 82%);
  font-weight: 500;
  padding: 6px 8px;
  border-radius: 6px;
  font-size: 12px;
  z-index: 1;
  /* border: 1px solid rgba(0, 0, 0, 0.06); */
  width: max-content;
  max-width: 350px;
  line-height: 16px;
  box-shadow: rgb(0 0 0 / 23%) 1px 1px 3px 1px;

}

.comman-tooltip-home[data-title]:hover::before {
  content: attr(data-title) !important;
  position: absolute;
  top: 30px;
  right: 0px;
  left: 0px;
  transform: translateX(-50%);
  background-color: #000;
  color: rgb(255 255 255 / 82%);
  font-weight: 500;
  padding: 6px 8px;
  border-radius: 6px;
  font-size: 12px;
  z-index: 1;
  /* border: 1px solid rgba(0, 0, 0, 0.06); */
  width: 290px;
  line-height: 16px;
  box-shadow: rgb(0 0 0 / 23%) 1px 1px 3px 1px;

}


/* calendar css */
.react-datepicker__day--selected,
.react-datepicker__day--in-selecting-range,
.react-datepicker__day--in-range,
.react-datepicker__month-text--selected,
.react-datepicker__month-text--in-selecting-range,
.react-datepicker__month-text--in-range,
.react-datepicker__quarter-text--selected,
.react-datepicker__quarter-text--in-selecting-range,
.react-datepicker__quarter-text--in-range,
.react-datepicker__year-text--selected,
.react-datepicker__year-text--in-selecting-range,
.react-datepicker__year-text--in-range {
  background-color: transparent !important;
}

.react-datepicker.custom-calendar .react-datepicker__navigation {
  top: 16px;
}

.react-datepicker__current-month,
.react-datepicker-time__header,
.react-datepicker-year-header {
  color: #121417 !important;
  flex-grow: 1;
  font-size: 1rem;
  font-weight: 800;
  letter-spacing: 0;
  line-height: 1.5rem;
  text-align: center;
  text-transform: capitalize;
  padding: 10px 0px !important;
}

.react-datepicker__navigation-icon {
  top: 5px !important
}

.react-datepicker__month .react-datepicker__month-text,
.react-datepicker__month .react-datepicker__quarter-text {
  padding: 10px;
}

.custom-calendar .react-datepicker__week {
  display: flex;
  white-space: normal;
  padding: 0px 12px;
}

.custom-calendar .react-datepicker__year-read-view--down-arrow,
.react-datepicker__month-read-view--down-arrow,
.react-datepicker__month-year-read-view--down-arrow,
.react-datepicker__navigation-icon::before {
  border-color: #000 !important;
  border-width: 2px 2px 0 0 !important;
  height: 10px;
  width: 10px;
}

.react-datepicker.custom-calendar .react-datepicker__day {
  outline: none;
  position: relative;
}

.custom-range-day {
  position: relative;
  /* Add this to create stacking context */
}

.custom-range-day::before {
  background: linear-gradient(275deg, #40e0d05c, #fff0 0%), #40e0d05c;
  border-radius: 0px;
  /* height: 2.5rem; */
  margin: 0px 0px;
  display: block;
  position: absolute;
  z-index: 0;
  /* Change to 0 instead of -1 */
  width: 100%;
  top: 0px;
  left: 0px;
  /* Add this to ensure proper positioning */
  content: "";
}

.custom-calendar .react-datepicker__day--in-range.react-datepicker__day--range-end::before {
  background: transparent !important;
  border-radius: 0px;
  /* height: 2.5rem; */
  margin: 0px 0px;
  display: block;
  position: absolute;
  z-index: 0;
  /* Change to 0 instead of -1 */
  width: 100%;
  top: 0px;
  left: 0px;
  /* Add this to ensure proper positioning */
  content: "";
}

.range-boundary-day {
  color: white !important;
  position: relative;
  /* Add this to create stacking context */
}

.range-boundary-day::after {
  background: linear-gradient(275deg, #40e0d05c, #fff0 0%), #40e0d05c;
  border-radius: 0px;
  /* height: 2.5rem; */
  margin: 0px 0px;
  display: block;
  position: absolute;
  z-index: 0;
  /* Change to 0 instead of -1 */
  width: 42%;
  top: 0px;
  left: 0;
  content: "";
}

.react-datepicker__day--keyboard-selected,
.react-datepicker__month-text--keyboard-selected,
.react-datepicker__quarter-text--keyboard-selected,
.react-datepicker__year-text--keyboard-selected {
  border-radius: 0px;
  background-color: #40e0d0 !important;
  color: rgb(0, 0, 0);
}

.react-datepicker__day--range-end::after {
  background: linear-gradient(275deg, #40e0d05c, #fff0 0%), #40e0d05c;
  border-radius: 0px;
  /* height: 2.5rem; */
  margin: 0px 0px;
  display: block;
  position: absolute;
  z-index: 0;
  /* Change to 0 instead of -1 */
  width: 32%;
  top: 0px;
  left: 0px;
  content: "";
}

.react-datepicker__day--selected {
  position: relative;
  z-index: 1;
  background-color: #40e0d0 !important;
  /* height: 48px !important;
  width: 48px !important; */
  border-radius: 0px !important;
}

/* Add these to ensure proper hover states */
.react-datepicker__day:hover {
  position: relative;
  z-index: 2;
}

/* react-datepicker__day react-datepicker__day--009 */
.discover-event-slider .swiper-button-prev,
.discover-event-slider .swiper-button-next {
  position: absolute;
  top: -95px;
  /* Adjust this to align with your topbar */
  right: 40px;
  /* Adjust this to space between prev and next buttons */
  z-index: 10;
  left: auto;
  height: 32px !important;
  width: 32px !important;
  color: #737373;
  /* Button color */
}

.discover-event-slider-home .swiper-button-prev {
  position: absolute;
  top: -50px;
  /* Adjust this to align with your topbar */
  right: 40px;
  /* Adjust this to space between prev and next buttons */
  z-index: 10;
  left: auto;
  height: 32px !important;
  width: 32px !important;
  color: black !important;
  background-color: white !important;
  /* Button color */
}

.discover-event-slider-home .swiper-button-next {
  position: absolute;
  top: -50px;
  /* Adjust this to align with your topbar */
  right: 40px;
  /* Adjust this to space between prev and next buttons */
  z-index: 10;
  left: auto;
  height: 32px !important;
  width: 32px !important;
  color: black !important;
  background-color: #40e0d0 !important;
  /* Button color */
}

.discover-event-slider .swiper-button-next {
  right: 0;
  /* Aligns the next button to the right side */
}

.discover-event-slider-home .swiper-button-next {
  right: 0;
  /* Aligns the next button to the right side */
}

.myCustomSwiper .swiper-button-next:after,
.myCustomSwiper .swiper-button-prev:after {
  font-size: 10px !important;
  font-weight: bolder;
}

.travelactivity-slider .swiper-button-prev {
  background-color: #E4E6E8 !important;
  top: calc(-100% + 16px);
}

.travelactivity-slider .swiper-button-next {
  top: calc(-100% + 16px);
}

/* width */
body::-webkit-scrollbar,
.modalscroll::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* Track */
::-webkit-scrollbar-track {
  /* box-shadow: inset 0 0 5px grey;  */
  border-radius: 10px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #40e0d0;
  border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #40e0d0;
}

.slider-alignment .image-alignment {
  display: flex;
  align-items: center;
  gap: 20px;
  /* Add spacing between items */
  flex-wrap: nowrap;
  /* Prevent wrapping in the marquee */
}

.slider-alignment .image-alignment li {
  white-space: nowrap;
  /* Prevent text wrapping within items */
  padding: 0 10px;
  /* Add some padding */
}

.slider-alignment .image-alignment .slider::after {
  content: "";
  width: 8px;
  height: 8px;
  background-color: #000;
  display: block;
  border-radius: 100px;
  position: absolute;
  top: 45%;
  left: -6px;
}

.custom-calendar .react-datepicker__day--in-selecting-range:not(.react-datepicker__day--in-range,
  .react-datepicker__month-text--in-range,
  .react-datepicker__quarter-text--in-range,
  .react-datepicker__year-text--in-range) {
  color: #000 !important;
}

/* .custom-date-picker-header + .custom-date-picker-header {
  display: block !important;
} */

.custom-calendar .react-datepicker__day-names {
  margin-top: 5px;
}

.custom-date-picker-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 10;
  background-color: white;
  /* Matches your calendar background */
  /* border-bottom: 1px solid #e5e7eb; */
  /* Optional: Matches your border style */
  padding: 8px 0;
}

.custom-calendar {
  position: relative;
  padding-top: 3rem;
  /* Add space for the "nights selected" header */
}

.custom-calendar .react-datepicker__children-container {
  display: flex;
  align-items: end;
  width: auto;
  height: 41px;
  justify-content: end;
  position: absolute;
  bottom: 6px;
  /* transform: translate(-28px, 10px); */
  right: 0;
  width: 100%;
  left: -7px;
  padding: 0px;
  border-top: 1px solid #ddd;
}

/* Animation */

@keyframes fadeInRight {
  from {
    transform: translate3d(100%, 0, 0);
    /* Start from the right */
    opacity: 0;
    /* Start fully transparent */
  }

  to {
    transform: translate3d(0, 0, 0);
    /* End at the original position */
    opacity: 1;
    /* Fully visible */
  }
}

.animated {
  animation-duration: 1s;
  animation-fill-mode: both;
  -webkit-animation-duration: 1s;
  -webkit-animation-fill-mode: both;
}

.fadeInRight {
  animation-name: fadeInRight;
  -webkit-animation-name: fadeInRight;
  opacity: 1;
}

@keyframes fadeInLeft {
  from {
    transform: translate3d(-100%, 0, 0);
    opacity: 0;
  }

  to {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

.animated {
  animation-duration: 1s;
  animation-fill-mode: both;
  -webkit-animation-duration: 1s;
  -webkit-animation-fill-mode: both;
}

.fadeInLeft {
  animation-name: fadeInLeft;
  -webkit-animation-name: fadeInLeft;
  opacity: 1;
}

.nav-bar-humburger.animated .MuiBackdrop-root.MuiModal-backdrop {
  /* max-width: 326px; */
  background: transparent;
}

.nav-bar-humburger .css-4t3x6l-MuiPaper-root-MuiDrawer-paper {
  border-radius: 0px 20px 20px 0px;
}

@media screen and (max-width: 480px) {
  .custom-calendar .react-datepicker__day {
    width: 40px;
    height: 40px;
    line-height: 40px;
  }
}


@media screen and (max-width: 1280px) {
  .comman-tooltip-home[data-title]:hover::before {
    content: attr(data-title) !important;
    position: absolute;
    top: 30px;
    right: 0px;
    left: 0px;
    transform: translateX(-50%);
    background-color: #000;
    color: rgb(255 255 255 / 82%);
    font-weight: 500;
    padding: 6px 8px;
    border-radius: 6px;
    font-size: 12px;
    z-index: 1;
    /* border: 1px solid rgba(0, 0, 0, 0.06); */
    width: 250px;
    line-height: 16px;
    box-shadow: rgb(0 0 0 / 23%) 1px 1px 3px 1px;

  }
}

@keyframes toastProgress {
  from {
    width: 100%;
  }

  to {
    width: 0%;
  }
}

.animate-toastProgress {
  animation: toastProgress 5s linear forwards;
}

/* Add this new section for handling cross-month range selection */
.react-datepicker__month-container:first-child .react-datepicker__day--in-range.react-datepicker__day--range-end:not(.react-datepicker__day--in-selecting-range) {
  background-color: transparent !important;
  color: #121417 !important;
}

.react-datepicker__month-container:first-child .react-datepicker__day--in-range.react-datepicker__day--range-end:not(.react-datepicker__day--in-selecting-range)::after {
  display: none;
}

/* Modify the existing range-end styles */
.react-datepicker__day--range-end::after {
  background: linear-gradient(275deg, #40e0d05c, #fff0 0%), #40e0d05c;
  border-radius: 0px;
  /* height: 2.5rem; */
  margin: 0px 0px;
  display: block;
  position: absolute;
  z-index: 0;
  width: 32%;
  top: 0px;
  left: 0px;
  content: "";
}

.react-datepicker__month-container:last-child .react-datepicker__day--range-end {
  background-color: #40e0d0 !important;
  color: #fff !important;
}

.zsiq-float
{
  border: 1px solid white;
}