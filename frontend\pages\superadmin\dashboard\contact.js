"use client";
import { Plus } from "lucide-react";
import Link from "next/link";
import React, { useEffect, useRef, useState } from "react";
import { MdKeyboardArrowDown, MdKeyboardArrowUp } from "react-icons/md";

const ContactUs = () => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState("");
  const dropdownRef = useRef(null);

  const toggleDropdown = () => setIsDropdownOpen(!isDropdownOpen);

  const handleOptionClick = (option) => {
    setSelectedOption(option);
    setIsDropdownOpen(false);
  };

  // Function to handle click outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="w-full p-7 bg-sky-blue-20 lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px]  float-end  overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <div className="flex items-center justify-between w-full ">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Contact Us
        </h2>
        <div className="w-[50%] gap-x-5 flex justify-end items-center">
          <Link
            href={"/superadmin/dashboard/contact-edit"}
            className={`px-4 py-2 text-sm font-medium font-poppins text-white rounded relative flex justify-center items-center bg-sky-blue-650`}
            type="button"
          >
            <Plus size={18} className="mr-1" /> Edit Contact Us
          </Link>
        </div>
      </div>
      <div className="flex justify-center items-cente p-10 mt-4 border rounded-xl bg-white dark:bg-black dark:border-none">
        <div className="bg-white  w-full max-w-xl dark:bg-black">
          <h2 className="text-lg font-medium font-poppins mb-6 text-black dark:text-gray-200">
            Contact Us
          </h2>
          <form className="space-y-4">
            <div>
              <label
                htmlFor="email"
                className="block text-gray-400 text-sm font-semibold font-poppins dark:text-[#B6B6B6]"
              >
                Email
              </label>
              <input
                type="email"
                id="email"
                className="w-full mt-1 p-2 text-sm font-poppins font-medium border border-gray-300 rounded-md bg-[#EEF9FF] dark:placeholder:text-[#757575] dark:bg-transparent"
                required
              />
            </div>

            <div>
              <label
                htmlFor="subject"
                className="block text-gray-400 text-sm font-semibold font-poppins dark:text-[#B6B6B6]"
              >
                Subject (optional)
              </label>
              <input
                type="text"
                id="subject"
                className="w-full mt-1 p-2 border text-sm font-poppins font-medium border-gray-300 rounded-md bg-[#EEF9FF] dark:placeholder:text-[#757575] dark:bg-transparent"
              />
            </div>

            <div>
              <label
                htmlFor="categories"
                className="block text-gray-400 text-sm font-poppins font-semibold dark:text-[#B6B6B6]"
              >
                Categories (optional)
              </label>
              {/* <div className="relative w-full" ref={dropdownRef}>
                  <div
                    className="flex mt-1 items-center w-full rounded-md bg-[#EEF9FF] cursor-pointer border border-gray-300 dark:bg-transparent"
                    onClick={toggleDropdown}
                  >
                    <input
                      type="text"
                      className="w-full text-sm font-poppins font-medium border-none bg-[#EEF9FF] dark:placeholder:text-[#757575] dark:bg-transparent"
                      placeholder={selectedOption}
                      readOnly
                    />
                    <span className="ml-2 text-xl mr-1 dark:text-[#757575] ">
                      {isDropdownOpen ? (
                        <MdKeyboardArrowUp className="dark:text-[#757575]"/>
                      ) : (
                        <MdKeyboardArrowDown className="dark:text-[#757575]"/>
                      )}
                    </span>
                  </div>

                  {isDropdownOpen && (
                    <div className="absolute z-10 w-full mt-2 bg-white rounded-md shadow-lg dark:bg-[#171616]">
                      <ul>
                        <li
                          className="px-4 py-2 text-sm font-poppins font-medium hover:bg-gray-100 cursor-pointer dark:hover:bg-[#393939b7] text-[#757575]"
                          onClick={() => handleOptionClick("General")}
                        >
                          General
                        </li>
                        <li
                          className="px-4 py-2 text-sm font-poppins font-medium hover:bg-gray-100 cursor-pointer dark:hover:bg-[#393939b7] text-[#757575]"
                          onClick={() => handleOptionClick("Support")}
                        >
                          Support
                        </li>
                        <li
                          className="px-4 py-2 text-sm font-poppins font-medium hover:bg-gray-100 cursor-pointer dark:hover:bg-[#393939b7] text-[#757575]"
                          onClick={() => handleOptionClick("Feedback")}
                        >
                          Feedback
                        </li>
                      </ul>
                    </div>
                  )}
                </div> */}
              <div
                className={`relative w-full focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 rounded-md`}
                ref={dropdownRef}
                tabIndex={0} // Allows the div to be focusable
              >
                <div
                  className="flex mt-1 items-center w-full rounded-md bg-[#EEF9FF] cursor-pointer border border-gray-300 dark:bg-transparent"
                  onClick={toggleDropdown}
                >
                  <input
                    type="text"
                    className="w-full text-sm font-poppins font-medium border-none bg-[#EEF9FF] dark:placeholder:text-[#757575] dark:bg-transparent focus:outline-none pointer-events-none"
                    placeholder={selectedOption}
                    readOnly
                  />
                  <span className="ml-2 text-xl mr-1 dark:text-[#757575]">
                    {isDropdownOpen ? (
                      <MdKeyboardArrowUp className="dark:text-[#757575]" />
                    ) : (
                      <MdKeyboardArrowDown className="dark:text-[#757575]" />
                    )}
                  </span>
                </div>

                {isDropdownOpen && (
                  <div className="absolute z-10 w-full mt-2 bg-white rounded-md shadow-lg dark:bg-[#171616]">
                    <ul>
                      <li
                        className="px-4 py-2 text-sm font-poppins font-medium hover:bg-gray-100 cursor-pointer dark:hover:bg-[#393939b7] text-[#757575]"
                        onClick={() => handleOptionClick("General")}
                      >
                        General
                      </li>
                      <li
                        className="px-4 py-2 text-sm font-poppins font-medium hover:bg-gray-100 cursor-pointer dark:hover:bg-[#393939b7] text-[#757575]"
                        onClick={() => handleOptionClick("Support")}
                      >
                        Support
                      </li>
                      <li
                        className="px-4 py-2 text-sm font-poppins font-medium hover:bg-gray-100 cursor-pointer dark:hover:bg-[#393939b7] text-[#757575]"
                        onClick={() => handleOptionClick("Feedback")}
                      >
                        Feedback
                      </li>
                    </ul>
                  </div>
                )}
              </div>
            </div>

            <div>
              <label
                htmlFor="description"
                className="block text-gray-400 text-sm font-poppins font-semibold dark:text-[#B6B6B6]"
              >
                Description (optional)
              </label>
              <textarea
                id="description"
                className="w-full mt-1 p-2 border text-sm font-poppins font-medium border-gray-300 bg-[#EEF9FF] rounded-md  h-32 dark:placeholder:text-[#757575] dark:bg-transparent"
              />
            </div>

            <p className="text-gray-500 text-sm font-poppins font-medium dark:text-[#B6B6B6]">
              Please enter the details of your request. A member of our support
              staff will respond as soon as possible. Please ensure that you do
              not enter credit card details/username/passwords in this form.
            </p>

            <button
              type="submit"
              className="w-full bg-sky-blue-650 text-sm font-poppins font-medium text-white py-2 rounded-md  transition duration-200"
            >
              Submit
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ContactUs;
