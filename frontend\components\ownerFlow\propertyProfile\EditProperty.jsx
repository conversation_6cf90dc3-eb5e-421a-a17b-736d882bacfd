 
import React, { useState, useCallback, useEffect } from "react";
import ExpandMoreRoundedIcon from "@mui/icons-material/ExpandMoreRounded";
import PhoneInput, { isValidPhoneNumber } from "react-phone-number-input";
import "react-phone-number-input/style.css";
import { Country, State, City } from "country-state-city";
import Select from "react-select";
import { Autocomplete } from "@react-google-maps/api";
import toast, { Toaster } from "react-hot-toast";
import { editPropertyApi } from "@/services/ownerflowServices";

const initialFormData = {
  name: "",
  type: "",
  email: "",
  address: { lineOne: "", lineTwo: "" },
  postCode: "",
  country: "",
  countryName: "",
  state: "",
  stateName: "",
  city: "",
  termsAccepted: false,
  phoneNumber: "",
  latitude: "",
  longitude: "",
};

const typeOptions = [
  { value: "hostel", label: "Hostel" },
  { value: "homestay", label: "Homestay" },
  { value: "bed_and_breakfast", label: "Bed & Breakfast" },
];

const googleMapsApiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

function EditProperty({ id, data, updatePropertyData }) {
  const [formData, setFormData] = useState(initialFormData);
  const [isLoading, setIsLoading] = useState(false);
  const [phoneNumber, setPhoneNumber] = useState("");
  const [stateOptions, setStateOptions] = useState([]);
  const [cityOptions, setCityOptions] = useState([]);
  const [autocomplete, setAutocomplete] = useState(null);
  const [isApiLoaded, setIsApiLoaded] = useState(false);

  const loadGoogleMapsApi = () => {
    const script = document.createElement("script");
    script.src = `https://maps.googleapis.com/maps/api/js?key=${googleMapsApiKey}&libraries=places`;
    script.async = true;
    script.onload = () => setIsApiLoaded(true);
    script.onerror = () => {
      console.error("Failed to load Google Maps API");
      setIsApiLoaded(false);
    };
    document.head.appendChild(script);
  };

  useEffect(() => {
    if (window.google && window.google.maps) {
      setIsApiLoaded(true);
    } else {
      loadGoogleMapsApi();
    }
  }, []);

  const getCountryCodeFromName = (countryName) => {
    const country = Country.getAllCountries().find(
      (c) => c.name === countryName
    );
    return country ? country.isoCode : "";
  };

  const getStateCodeFromName = (stateName, countryCode) => {
    const state = State.getStatesOfCountry(countryCode).find(
      (s) => s.name === stateName
    );
    return state ? state.isoCode : "";
  };

  useEffect(() => {
    if (data) {
      const {
        address,
        contactUs,
        type,
        name,
        latitude,
        longitude,
        termsAccepted,
        
      } = data;
      const countryCode = getCountryCodeFromName(address.country);
      const stateCode = getStateCodeFromName(address.state, countryCode);
      setFormData({
        name,
        type,
        email: contactUs?.email || "",
        address: { lineOne: address?.lineOne, lineTwo: address?.lineTwo },
        postCode: address?.zipcode || "",
        country: countryCode,
        countryName: address?.country,
        state: stateCode,
        stateName: address?.state,
        city: address?.city,
        termsAccepted,
        phoneNumber: contactUs?.phoneNumber || "",
        latitude,
        longitude,
      });

      setPhoneNumber(contactUs?.phoneNumber || "");

      const states = State.getStatesOfCountry(countryCode);
      setStateOptions(states.map((s) => ({ label: s.name, value: s.isoCode })));

      if (address.state) {
        const cities = City.getCitiesOfState(countryCode, stateCode);
        setCityOptions(cities.map((c) => ({ label: c.name, value: c.name })));
      }
    }
  }, [data]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  const handleCountryChange = (selectedOption) => {
    const countryCode = selectedOption.value;
    const countryName = Country.getCountryByCode(countryCode)?.name || "";

    setFormData({
      ...formData,
      country: countryCode,
      countryName,
      state: "",
      stateName: "",
      city: "",
    });

    const states = State.getStatesOfCountry(countryCode);
    setStateOptions(states.map((s) => ({ label: s.name, value: s.isoCode })));
    setCityOptions([]);
  };

  const handleStateChange = (selectedOption) => {
    const stateCode = selectedOption.value;
    const countryCode = formData.country;
    const stateName =
      State.getStateByCodeAndCountry(stateCode, countryCode)?.name || "";

    setFormData({
      ...formData,
      state: stateCode,
      stateName,
      city: "",
    });

    const cities = City.getCitiesOfState(countryCode, stateCode);
    setCityOptions(cities.map((c) => ({ label: c.name, value: c.name })));
  };

  const handleAddressChange = useCallback((place) => {
    if (!place || !place.address_components) return;

    const addressComponents = place.address_components;

    let country = "";
    let state = "";
    let stateName = "";
    let city = "";
    let postCode = "";

    addressComponents.forEach((component) => {
      const types = component.types;

      if (types.includes("country")) {
        country = component.short_name;
      }
      if (types.includes("administrative_area_level_1")) {
        state = component.short_name;
        stateName = component.long_name;
      }
      if (
        types.includes("locality") ||
        types.includes("administrative_area_level_2") ||
        types.includes("administrative_area_level_3")
      ) {
        city = component.long_name;
      }
      if (types.includes("postal_code")) {
        postCode = component.long_name;
      }
    });

    setFormData((prevData) => ({
      ...prevData,
      address: { ...prevData.address, lineOne: place.formatted_address },
      latitude: place.geometry.location.lat(),
      longitude: place.geometry.location.lng(),
      country,
      state,
      stateName,
      city,
      postCode,
    }));

    const countryName = Country.getCountryByCode(country)?.name || "";
    setFormData((prevData) => ({
      ...prevData,
      country,
      countryName,
    }));

    if (country) {
      const states = State.getStatesOfCountry(country);
      setStateOptions(states.map((s) => ({ label: s.name, value: s.isoCode })));
    }

    if (country && state) {
      const cities = City.getCitiesOfState(country, state);
      setCityOptions(cities.map((c) => ({ label: c.name, value: c.name })));
    }
  }, []);

  const validateForm = (data) => {
    return (
      data.name &&
      data.type &&
      phoneNumber &&
      isValidPhoneNumber(phoneNumber) &&
      data.email &&
      data.address.lineOne &&
      data.postCode &&
      data.country &&
      data.state &&
      data.termsAccepted
    );
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm(formData)) {
      toast.error("All fields are required and phone number must be valid.");
      return;
    }

    setIsLoading(true);
    try {
      const payload = {
        type: formData.type,
        name: formData.name,
        address: {
          lineOne: formData.address.lineOne,
          city: formData.city,
          state: formData.stateName,
          country: formData.countryName,
          zipcode: formData.postCode,
        },
        contactUs: {
          phoneNumber: phoneNumber,
          email: formData.email,
        },
        latitude: formData.latitude,
        longitude: formData.longitude,
      };

      if (id) {
        const response = await editPropertyApi(id, payload);
        if (response?.data?.status) {
          toast.success(
            response?.data?.message || "Property updated successfully!"
          );
          updatePropertyData();
        }
      } else {
        toast.error("No property data available to update.");
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Failed to submit property details.");
    } finally {
      setIsLoading(false);
    }
  };

  if (!isApiLoaded) {
    return <div>Loading...</div>;
  }

  return (
    <div className='max-w-4xl mx-auto'>
      <div className='bg-white p-6 rounded-lg shadow-md'>
        <h2 className='text-2xl font-bold mb-5'>Edit Property</h2>
        <form onSubmit={handleSubmit}>
          <div className='space-y-4'>
            <input
              id='name'
              name='name'
              type='text'
              placeholder='Owner Name'
              value={formData.name}
              onChange={handleChange}
              className='w-full px-4 py-3 mt-2 border rounded-3xl focus:outline-none focus:ring-[#40E0D0] bg-transparent'
            />
            <input
              id='email'
              name='email'
              type='email'
              placeholder='Email'
              value={formData.email}
              onChange={handleChange}
              className='w-full px-4 py-3 mt-2 border rounded-3xl focus:outline-none focus:ring-[#40E0D0] bg-transparent'
            />
            <div className='relative'>
              <select
                id='type'
                name='type'
                value={formData.type}
                onChange={handleChange}
                className='w-full px-4 py-3 mt-2 border rounded-3xl focus:outline-none focus:ring-[#40E0D0] bg-transparent appearance-none'
              >
                <option value='' disabled>
                  Select Property Type
                </option>
                {typeOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <ExpandMoreRoundedIcon className='absolute right-2 top-1/2 transform -translate-y-1/2' />
            </div>
            <PhoneInput
              defaultCountry='IN'
              international
              withCountryCallingCode
              value={phoneNumber}
              onChange={setPhoneNumber}
              placeholder='Phone Number'
              className='w-full px-4 py-3 mt-2 border rounded-3xl focus:outline-none focus:ring-[#40E0D0] bg-transparent'
            />
            <Autocomplete
              onLoad={(autocompleteInstance) =>
                setAutocomplete(autocompleteInstance)
              }
              onPlaceChanged={() => {
                if (autocomplete) {
                  handleAddressChange(autocomplete.getPlace());
                } else {
                  console.error("Autocomplete is not loaded yet.");
                }
              }}
              className='w-full'
            >
              <input
                id='address'
                name='address'
                type='text'
                placeholder='Address'
                value={formData.address.lineOne}
                onChange={handleChange}
                className='w-full px-4 py-3 mt-2 border rounded-3xl focus:outline-none focus:ring-[#40E0D0] bg-transparent'
              />
            </Autocomplete>
            <input
              id='postCode'
              name='postCode'
              type='text'
              placeholder='Post Code'
              value={formData.postCode}
              onChange={handleChange}
              className='w-full px-4 py-3 mt-2 border rounded-3xl focus:outline-none focus:ring-[#40E0D0] bg-transparent'
            />
            <Select
              id='country'
              name='country'
              options={Country.getAllCountries().map((c) => ({
                label: c.name,
                value: c.isoCode,
              }))}
              value={{ label: formData.countryName, value: formData.country }}
              onChange={handleCountryChange}
              className='w-full'
              placeholder='Select Country'
            />
            <Select
              id='state'
              name='state'
              options={stateOptions}
              value={{ label: formData.stateName, value: formData.state }}
              onChange={handleStateChange}
              className='w-full'
              placeholder='Select State'
            />
            <Select
              id='city'
              name='city'
              options={cityOptions}
              value={{ label: formData.city, value: formData.city }}
              onChange={(selectedOption) =>
                setFormData({ ...formData, city: selectedOption.value })
              }
              className='w-full'
              placeholder='Select City'
            />
            <div className='flex items-center mt-4'>
              <input
                id='termsAccepted'
                name='termsAccepted'
                type='checkbox'
                checked={formData.termsAccepted}
                onChange={handleChange}
                className='mr-2'
              />
              <label htmlFor='termsAccepted'>Accept Terms and Conditions</label>
            </div>
            <button
              type='submit'
              className='w-full py-3 mt-6 bg-blue-500 text-white rounded-3xl focus:outline-none hover:bg-blue-600'
              disabled={isLoading}
            >
              {isLoading ? "Updating..." : "Update Property"}
            </button>
          </div>
        </form>
      </div>
      <Toaster />
    </div>
  );
}

export default EditProperty;
