 
"use client"

import { useEffect } from 'react';

const Map = () => {
  useEffect(() => {
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap`;
    script.async = true;
    window.initMap = function () {
      // eslint-disable-next-line no-undef
      new google.maps.Map(document.getElementById('map'), {
        center: { lat: 19.076, lng: 72.8777 },
        zoom: 12,
      });
    };
    document.head.appendChild(script);
    return () => {
      document.head.removeChild(script);
    };
  }, []);

  return <div id="map" className="w-full h-full"></div>;
};

export default Map;