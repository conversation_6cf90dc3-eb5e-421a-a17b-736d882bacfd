/* eslint-disable react/no-unescaped-entities */
"use client";
import Image from "next/image";
import React from "react";
import { FaArrowTrendDown, FaArrowTrendUp } from "react-icons/fa6";
import DashboardGraph from "@/components/superadmin/DashboardGraph";
import { BsThreeDotsVertical } from "react-icons/bs";
import "@fontsource/poppins";

const SuperDashboard = () => {
  const bookings = [
    {
      userName: "Daya",
      hostelName: "Marjolaine Landing",
      mobileNo: "2536253652",
      dateTime: "12.09.2019 - 12:53 PM",
      amount: "34,295",
      status: "Booked",
    },
    {
      userName: "Sanjay",
      hostelName: "Ocean View",
      mobileNo: "9845632563",
      dateTime: "15.10.2020 - 10:00 AM",
      amount: "45,000",
      status: "Pending",
    },
    {
      userName: "Rita",
      hostelName: "Mountain Retreat",
      mobileNo: "7596253652",
      dateTime: "20.11.2021 - 09:30 AM",
      amount: "27,500",
      status: "Cancelled",
    },
    {
      userName: "Ankit",
      hostelName: "City Lights",
      mobileNo: "9786253665",
      dateTime: "05.12.2022 - 02:15 PM",
      amount: "50,000",
      status: "Booked",
    },
  ];

  // Function to determine the status badge color
  const getStatusBadgeColor = (status) => {
    switch (status) {
      case "Booked":
        return "bg-[#00B69B]";
      case "Pending":
        return "bg-yellow-300";
      case "Cancelled":
        return "bg-red-600";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth flex-grow  dark:bg-[#171616]">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">Dashboard</h2>
      {/* <div className="grid grid-cols-2 gap-4 lg:grid-cols-4 md:gap-x-7 py-7">
        <div className=" flex flex-col justify-between p-4 rounded-2xl bg-white w-[262px] h-[161px]">
          <div className="flex justify-between ">
            <div className="py-2">
              <h2 className="text-black/75 text-lg">Total Users</h2>
              <h1 className="font-extrabold text-black text-2xl pt-2">40,689</h1>
            </div>
            <div className="bg-[#E5E4FF] h-12 w-12 mt-2 flex items-center justify-center rounded-2xl">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/User.png`}
                width={16}
                height={24}
                alt="User"
                className="h-5  w-6"
              />
            </div>
          </div>
          <span className="text-teal-500 flex items-center gap-1 text-sm font-semibold ">
            <FaArrowTrendUp />
            8.5%
            <p className="text-black/75 text-sm font-semibold">Up from yesterday</p>
          </span>
        </div>
        <div className="flex flex-col justify-between p-4 rounded-2xl bg-white w-[262px] h-[161px]">
          <div className="flex justify-between">
            <div className="py-2">
              <h2 className="text-black/75 text-lg">New Booking</h2>
              <h1 className="font-extrabold text-black text-2xl pt-2">12112</h1>
            </div>
            <div className="bg-[#FFF3D6] h-12 w-12 mt-2 flex items-center justify-center rounded-2xl">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Booking.png`}
                width={16}
                height={24}
                alt="booking"
                className="h-5 w-6"
              />
            </div>
          </div>
          <span className="text-teal-500 flex items-center gap-1 text-sm font-semibold">
            <FaArrowTrendUp />
            1.3%
            <p className="text-black/75 text-sm font-semibold">Up from past week</p>
          </span>
        </div>
        <div className="flex flex-col justify-between p-4 rounded-2xl bg-white w-[262px] h-[161px]">
          <div className="flex justify-between">
            <div className="py-2">
              <h2 className="text-black/75 text-lg">Total Booking</h2>
              <h1 className="font-bold text-2xl pt-2 text-black">9,000</h1>
            </div>
            <div className="bg-[#D9F7E8] h-12 w-12 mt-2 flex items-center justify-center rounded-2xl">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/TotalBooking.png`}
                width={16}
                height={24}
                alt="Booking"
                className="h-5 w-6"
              />
            </div>
          </div>
          <span className="text-red-600 flex items-center gap-1 text-sm font-semibold">
            <FaArrowTrendDown />
            4.3%
            <p className="text-black/75 text-sm font-semibold">Down from yesterday</p>
          </span>
        </div>
        <div className="flex flex-col justify-between p-4 rounded-2xl bg-white w-[262px] h-[161px] ">
          <div className="flex justify-between">
            <div>
              <h2 className="text-black/75 text-lg">Total Revenue</h2>
              <h1 className="font-bold text-2xl pt-2 text-black">2,52,040</h1>
            </div>
            <div className="bg-[#FFDED1] h-12 w-12 flex items-center justify-center rounded-2xl">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/TotalRevenue.png`}
                width={16}
                height={24}
                alt="Revenue"
                className="h-5 w-6"
              />
            </div>
          </div>
          <span className="text-teal-500 flex items-center gap-1 text-sm font-semibold">
            <FaArrowTrendUp />
            1.8%
            <p className="text-black/75 text-sm font-semibold">Up from yesterday</p>
          </span>
        </div>
      </div> */}
      <div className="flex items-center justify-between py-7 flex-wrap gap-y-6">
        <div className="flex flex-col justify-between p-4 rounded-2xl bg-white w-full sm:w-[262px] h-[161px]  dark:bg-black ">
          <div className="flex justify-between ">
            <div className="py-2">
              <h2 className="text-black/75 text-base font-poppins dark:text-[#757575]">
                Total Users
              </h2>
              <h1 className="font-bold text-black font-poppins text-2xl pt-2 dark:text-gray-100">
                40,689
              </h1>
            </div>
            <div className="bg-[#E5E4FF] h-12 w-12 mt-2 flex items-center justify-center rounded-2xl">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/User.png`}
                width={16}
                height={24}
                alt="User"
                className="h-5 w-6"
              />
            </div>
          </div>
          <span className="text-primary-blue font-poppins flex items-center gap-1 text-sm  font-medium">
            <FaArrowTrendUp />
            8.5%
            <p className="text-black/75 font-poppins text-sm font-medium dark:text-gray-100">
              Up from yesterday
            </p>
          </span>
        </div>
        <div className="flex flex-col justify-between p-4 rounded-2xl bg-white w-full sm:w-[262px] h-[161px]  dark:bg-black">
          <div className="flex justify-between">
            <div className="py-2">
              <h2 className="text-black/75 text-base font-poppins dark:text-[#757575]">
                New Booking
              </h2>
              <h1 className="font-bold text-black font-poppins text-2xl pt-2 dark:text-gray-100">
                12,112
              </h1>
            </div>
            <div className="bg-[#FFF3D6] h-12 w-12 mt-2 flex items-center justify-center rounded-2xl">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Booking.png`}
                width={16}
                height={24}
                alt="Booking"
                className="h-5 w-6"
              />
            </div>
          </div>
          <span className="text-primary-blue font-poppins flex items-center gap-1 text-sm  font-medium">
            <FaArrowTrendUp />
            1.3%
            <p className="text-black/75 font-poppins text-sm font-medium dark:text-gray-100">
              Up from past week
            </p>
          </span>
        </div>
        <div className="flex flex-col justify-between p-4 rounded-2xl bg-white w-full sm:w-[262px] h-[161px]  dark:bg-black">
          <div className="flex justify-between">
            <div className="py-2">
              <h2 className="text-black/75 text-base font-poppins dark:text-[#757575]">
                Total Booking
              </h2>
              <h1 className="font-bold text-2xl pt-2 font-poppins text-black dark:text-gray-100">
                9,000
              </h1>
            </div>
            <div className="bg-[#D9F7E8] h-12 w-12 mt-2 flex items-center justify-center rounded-2xl">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/TotalBooking.png`}
                width={16}
                height={24}
                alt="Booking"
                className="h-5 w-6"
              />
            </div>
          </div>
          <span className="text-red-600 font-poppins flex items-center gap-1 text-sm  font-medium">
            <FaArrowTrendDown />
            4.3%
            <p className="text-black/75 font-poppins text-sm  font-medium dark:text-gray-100">
              Down from yesterday
            </p>
          </span>
        </div>
        <div className="flex flex-col justify-between p-4 rounded-2xl bg-white w-full sm:w-[262px] h-[161px]  dark:bg-black">
          <div className="flex justify-between">
            <div>
              <h2 className="text-black/75 text-base font-poppins dark:text-[#757575]">
                Total Revenue
              </h2>
              <h1 className="font-bold text-2xl font-poppins pt-2 text-black dark:text-gray-100">
                2,52,040
              </h1>
            </div>
            <div className="bg-[#FFDED1] h-12 w-12 flex items-center justify-center rounded-2xl">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/icon-last.png`}
                width={16}
                height={24}
                alt="Revenue"
                className="h-5 w-6"
              />
            </div>
          </div>
          <span className="text-primary-blue font-poppins flex items-center gap-1 text-sm  font-medium">
            <FaArrowTrendUp />
            1.8%
            <p className="text-black/75 text-sm  font-medium font-poppins dark:text-gray-100">
              Up from yesterday
            </p>
          </span>
        </div>
      </div>

      <div className="md:flex gap-7 flex flex-wrap">
        <div className="lg:w-[73%] md:w-[100%] sm:w-[100%] h-auto  rounded-lg ">
          <div className="h-auto  rounded-xl bg-white dark:bg-black">
            {/* <h1 className="pt-7 pb-3 px-10 text-2xl font-semibold text-black">
              Revenue Overview
            </h1> */}
            <div className="flex justify-between items-center mb-4">
              <h2 className="pt-0 md:pt-7 pb-0  md:pb-3 px-10 text-lg md:text-xl lg:text-2xl font-medium font-poppins text-black dark:text-[#B6B6B6]">
                Revenue Overview
              </h2>
              <select className=" flex text-xs font-medium font-poppins items-center justify-around border border-gray-300 rounded  mr-4 py-1 text-gray-700 w-auto dark:bg-[#1F1F1F] dark:text-[#757575] dark:border-none">
                <option className="text-xxs font-medium">October</option>
                <option className="text-xxs font-medium">October</option>
                
              </select>
            </div>
            <DashboardGraph />
          </div>
        </div>
        <div className="lg:w-[24%] md:w-[100%] sm:w-[100%] bg-white rounded-xl p-4  dark:border-none dark:bg-black">
          <div className="flex justify-between">
            <h1 className=" text-base font-medium font-poppins mb-4 text-black/75 dark:text-[#B6B6B6]">
              Recent Activitiy
            </h1>
            <div className="border rounded-md flex h-7 w-7 justify-center items-center dark:border-none dark:bg-[#1F1F1F]">
              <BsThreeDotsVertical className="text-black/60 dark:text-[#757575]" />
            </div>
          </div>
          <div className="flex gap-3">
            <div className="bg-[#FFDED1] h-9 w-12 flex items-center justify-center rounded-full">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Schedule.png`}
                width={24}
                height={24}
                alt="schedule"
                className="h-6 w-6"
              />
            </div>
            <div className="flex  text-black/40 dark:text-[#757575] font-medium mb-2 flex-col">
              <h4 className="text-xs font-poppins">
                Hostel - <span className="text-black dark:text-white">Mad monkey</span> phuket
                hasn't verified profile{" "}
              </h4>
              <p className="text-xxs font-poppins">12:30PM</p>
            </div>
          </div>
          <div className="flex gap-3">
            <div className="bg-[#FFEEC5] h-9 w-12 flex items-center justify-center rounded-full">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/communication.png`}
                width={24}
                height={24}
                alt="communication"
                className="h-6 w-6"
              />
            </div>
            <div className="flex text-black/40 dark:text-[#757575] font-medium mb-2 flex-col">
              <h4 className="text-xs font-poppins">
                Amet minim mollit{" "}
                <span className="text-black dark:text-white">non deserunt</span> ullamco est sit{" "}
              </h4>
              <p className="text-xxs font-poppins">12:30PM</p>
            </div>
          </div>
          <div className="flex gap-3">
            <div className="bg-[#C9F4DE] h-9 w-12 flex items-center justify-center rounded-full">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/feedback.png`}
                width={24}
                height={24}
                alt="feedback"
                className="h-6 w-6"
              />
            </div>
            <div className="flex text-black/40 font-medium mb-2 flex-col dark:text-[#757575]">
              <h4 className="text-xs font-poppins">
                Amet minim mollit{" "}
                <span className="text-black dark:text-white">non deserunt</span> ullamco est sit{" "}
              </h4>
              <p className="text-xxs font-poppins">12:30PM</p>
            </div>
          </div>
          <div className="flex gap-3">
            <div className="bg-[#DAD9FF] h-9 w-12 flex items-center justify-center rounded-full">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/content.png`}
                width={24}
                height={24}
                alt="content"
                className="h-6 w-6"
              />
            </div>
            <div className="flex text-xs text-black/40 dark:text-[#757575] font-medium mb-2 flex-col">
              <h4 className="text-xs font-poppins">
                Amet minim mollit{" "}
                <span className="text-black dark:text-white">non deserunt</span> ullamco est sit{" "}
              </h4>
              <p className="text-xxs font-poppins">12:30PM</p>
            </div>
          </div>
          <div className="flex gap-3">
            <div className="bg-[#FFEEC5] h-9 w-12 flex items-center justify-center rounded-full">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Hostel.png`}
                width={24}
                height={24}
                alt="hostel"
                className="h-6 w-6"
              />
            </div>
            <div className="flex text-xs text-black/40 dark:text-[#757575] font-medium mb-2 flex-col">
              <h4 className="text-xs font-poppins">
                Amet minim mollit{" "}
                <span className="text-black dark:text-white">non deserunt</span> ullamco est sit{" "}
              </h4>
              <p className="text-xxs font-poppins">12:30PM</p>
            </div>
          </div>
          <div className="flex gap-3">
            <div className="bg-[#FFEEC5] h-9 w-12 flex items-center justify-center rounded-full">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/RecentBooking.png`}
                width={24}
                height={24}
                alt="recentBooking"
                className="h-6 w-6"
              />
            </div>
            <div className="flex text-xs text-black/40 dark:text-[#757575] font-medium mb-2 flex-col">
              <h4 className="text-xs font-poppins">
                Amet minim mollit{" "}
                <span className="text-black dark:text-white">non deserunt</span> ullamco est sit{" "}
              </h4>
              <p className="text-xxs font-poppins">12:30PM</p>
            </div>
          </div>
          <div className="flex gap-3">
            <div className="bg-[#FFEEC5] h-9 w-12 flex items-center justify-center rounded-full">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/RecentBooking.png`}
                width={24}
                height={24}
                alt="recentBooking"
                className="h-6 w-6"
              />
            </div>
            <div className="flex text-xs text-black/40 dark:text-[#757575] font-medium mb-2 flex-col">
              <h4 className="text-xs font-poppins">
                Amet minim mollit{" "}
                <span className="text-black dark:text-white">non deserunt</span> ullamco est sit{" "}
              </h4>
              <p className="text-xxs font-poppins">12:30PM</p>
            </div>
          </div>
        </div>
      </div>
      <div className="bg-white p-6 rounded-lg mt-7  dark:border-none dark:bg-black">
        <div className="flex justify-between items-center mb-4 lg:mb-2 ">
        <h2 className="text-lg md:text-xl lg:text-2xl font-medium font-poppins mb-0 lg:mb-4 dark:text-[#B6B6B6]">
          New Booking Details
        </h2>
        <select className=" flex text-xs font-medium font-poppins items-center justify-around border border-gray-300 rounded py-1 text-gray-700 w-auto dark:border-none dark:bg-[#1F1F1F] dark:text-[#B6B6B6]">
        <option className="text-xxs font-medium ">October</option>
        <option className="text-xxs font-medium ">October</option>
        </select>

        </div>
        <div className="overflow-x-auto">
          <table className="w-full bg-white dark:bg-black">
            <thead>
              <tr className="text-left bg-[#F1F4F9] dark:bg-[#3f3e3e] rounded-3xl">
                <th className=" whitespace-nowrap py-3 px-6 text-black dark:text-white text-sm font-medium font-poppins">
                  User Name
                </th>
                <th className="whitespace-nowrap  py-3 px-6 pl-10 text-black dark:text-white text-sm font-medium font-poppins ">
                  Hostel Name
                </th>
                <th className="py-3 px-6 text-black pl-8 dark:text-white text-sm font-medium font-poppins">
                  Mobile No
                </th>
                <th className="whitespace-nowrap py-3 px-6 text-black dark:text-white text-sm font-medium font-poppins pl-12">
                  Date - Time
                </th>
                <th className="py-3 px-6 text-black  dark:text-white text-sm font-medium font-poppins">
                  Amount
                </th>
                <th className="py-3 px-6 text-black dark:text-white text-sm text-end pr-10 font-medium font-poppins">
                  Status
                </th>
              </tr>
            </thead>
            <tbody>
              {bookings.map((booking, index) => (
                <tr className="border-b" key={index}>
                  <td className="whitespace-nowrap py-4 pl-8 text-sm font-normal font-poppins text-black dark:text-white">
                    {booking.userName}
                  </td>
                  <td className="whitespace-nowrap py-4 px-6 text-sm font-normal font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    {booking.hostelName}
                  </td>
                  <td className="py-4 px-6 text-sm font-normal font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    {booking.mobileNo}
                  </td>
                  <td className="whitespace-nowrap py-4 px-6 text-sm font-normal font-poppins text-gray-500 dark:text-[#B6B6B6]">
                    {booking.dateTime}
                  </td>
                  <td className="py-4 px-6 text-black text-sm font-normal font-poppins dark:text-[#B6B6B6] pl-7">
                    {booking.amount}
                  </td>
                  <td className="py-4 px-6 flex justify-end">
                    <span
                      className={`${getStatusBadgeColor(
                        booking.status
                      )} text-white py-1 px-3 text-sm font-bold font-poppins rounded-full`}
                    >
                      {booking.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default SuperDashboard;
