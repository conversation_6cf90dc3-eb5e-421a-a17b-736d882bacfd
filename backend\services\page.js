import pageModel from '../models/page.js';

const savePageData = async (page, data) => {
    try {
      const existingPage = await pageModel.findOne({ page });
  
      if (existingPage) {
        existingPage.data = data;
        await existingPage.save();
      } else {
        const newPage = new pageModel({ page, data });
        await newPage.save();
      }
  
      return { success: true, message: 'Page data saved successfully' };
    } catch (error) {
      return { success: false, message: 'Error saving page data', error };
    }
};

export { savePageData }