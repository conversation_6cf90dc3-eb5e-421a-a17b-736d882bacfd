import express from 'express';
import { connectInstagram } from '../controller/mixCreators.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();

// Route to connect Instagram and retrieve user data
router.post('/connect-instagram', connectInstagram);

// Route to get Instagram follower, likes, and post details
// router.get('/get-instagram-data', checkAuth, getInstagramData);

export default router;

/**
 * @swagger
 * tags:
 *   name: Instagram
 *   description: API endpoints for managing Instagram connections and retrieving data
 */

/**
 * @swagger
 * /mix-creator/connect-instagram:
 *   post:
 *     summary: Connect user Instagram and retrieve basic data
 *     tags: [Instagram]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               instaUrl:
 *                 type: string
 *                 description: The Instagram URL to connect and retrieve data from.
 *     responses:
 *       200:
 *         description: Instagram connected and data retrieved successfully
 *       400:
 *         description: Bad request (e.g., invalid URL)
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /mix-creator/get-instagram-data:
 *   get:
 *     summary: Get Instagram followers, average likes, and post data
 *     tags: [Instagram]
 *     responses:
 *       200:
 *         description: Successfully retrieved Instagram data
 *       400:
 *         description: Bad request (e.g., missing authentication)
 *       500:
 *         description: Internal server error
 */
