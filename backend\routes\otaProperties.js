import express from "express";
import { uploadHostels, updateApprovalStatusController,
     listAllOtaPropertiesController, listOtaPropertyAllRoomsController,
      getOtaPropertiesByStateController, getCountriesAndStatesController,listPropertyName,migrateOtaProperties,
      getPropertyVisitedUsers} from "../controller/otaProperties.js"
const router = express.Router();
import multer from 'multer';
import { checkAuth } from "../middleware/auth.js";
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, 'uploads/');
    },
    filename: (req, file, cb) => {
        cb(null, file.originalname);
    }
});

const upload = multer({ storage }); 
router.post("/upload",  upload.array('files', 10000),  uploadHostels);

router.put('/', checkAuth('update-approval-status'), updateApprovalStatusController);
router.get('/',listAllOtaPropertiesController);
router.get('/list/:name', checkAuth(''), listPropertyName);
router.get("/get-joined-users",checkAuth('property_details'), getPropertyVisitedUsers);
router.get("/property/:id", listOtaPropertyAllRoomsController);
router.get('/countriesAndStates', getCountriesAndStatesController);
router.get('/:state', checkAuth('property_by_state') , getOtaPropertiesByStateController);
router.post('/migrate' , migrateOtaProperties);

//router.get('/management',  getGroupedOtaPropertiesController); // checkAuth('ota_management'),

export default router;
/**
 * @swagger
 * tags:
 *   name: otaProperties
 *   description: Property management endpoints
 */

/**
 * @swagger
 * /otaProperties/upload:
 *   post:
 *     summary: Upload JSON files with properties data
 *     tags: [otaProperties]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               files:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Multiple files
 *     responses:
 *       '201':
 *         description: Properties uploaded and created
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       address:
 *                         type: object
 *                         properties:
 *                           lineOne:
 *                             type: string
 *                           city:
 *                             type: string
 *                           state:
 *                             type: string
 *                           country:
 *                             type: string
 *                           zip:
 *                             type: string
 *                       room:
 *                         type: number
 *                       bed:
 *                         type: number
 *                       offer:
 *                         type: string
 *                       status:
 *                         type: string
 *                       aboutUs:
 *                         type: string
 *                       hostelImages:
 *                         type: array
 *                         items:
 *                           type: string
 *                       rules:
 *                         type: string
 *                       cancellationPolicies:
 *                         type: string
 *                       generalPolicies:
 *                         type: string
 *                       amenities:
 *                         type: array
 *                         items:
 *                           type: string
 *                       isArchived:
 *                         type: boolean
 *                       isDeleted:
 *                         type: boolean
 *                       isActive:
 *                         type: boolean
 *       '400':
 *         description: Bad Request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 */

/**
 * @swagger
 * /otaProperties:
 *   put:
 *     summary: Update the approval status of OTA properties
 *     tags: [otaProperties]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               country:
 *                 type: string
 *                 description: The country to update
 *               state:
 *                 type: string
 *                 description: The state to update
 *               propertyId:
 *                 type: string
 *                 description: The ID of the property to update
 *     responses:
 *       200:
 *         description: Approval status updated successfully
 *         content:
 *           application/json:
 *             example:
 *               message: "Approval status updated successfully"
 *       400:
 *         description: Bad request, missing required fields
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /otaProperties/migrate:
 *   post:
 *     summary: Migrate OTA properties to the property and room collections
 *     tags: [otaProperties]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Migration completed successfully
 *         content:
 *           application/json:
 *             example:
 *               message: "Migration completed successfully"
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /otaProperties:
 *   get:
 *     summary: List all OTA properties with pagination, sorting, and filtering
 *     tags: [otaProperties]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: sortCondition
 *         schema:
 *           type: string
 *           enum: [lowestPrice, highestPrice, bestRated, distFromCC]
 *         description: Sort condition for properties
 *       - in: query
 *         name: filter
 *         schema:
 *           type: object
 *           properties:
 *             minPrice:
 *               type: number
 *               minimum: 0
 *               description: Minimum price filter
 *             maxPrice:
 *               type: number
 *               minimum: 0
 *               description: Maximum price filter
 *     responses:
 *       '200':
 *         description: Successful response
 *         schema:
 *           type: object
 *           properties:
 *             message:
 *               type: string
 *               example: Properties listed successfully
 *             data:
 *               type: object
 *               properties:
 *                 properties:  # Array of OTA properties
 *                   type: array
 *                   items:
 *                     type: object  # Each item in array is an OTA property object
 *                     properties:
 *                       _id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       type:
 *                         type: string
 *                       address:
 *                         type: object
 *                         properties:
 *                           lineOne:
 *                             type: string
 *                           lineTwo:
 *                             type: string
 *                       aboutUs:
 *                         type: string
 *                       location:
 *                         type: object
 *                         properties:
 *                           type:
 *                             type: string
 *                           coordinates:
 *                             type: array
 *                             items:
 *                               type: number
 *                       photos:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             title:
 *                               type: string
 *                             url:
 *                               type: string
 *                       rate:
 *                         type: object
 *                         properties:
 *                           value:
 *                             type: string
 *                           currency:
 *                             type: string
 *                       freeCancellationAvailable:
 *                         type: boolean
 *                       distanceFromCityCenter:
 *                         type: object
 *                         properties:
 *                           value:
 *                             type: number
 *                           unit:
 *                             type: string
 *                       freeFacilities:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             name:
 *                               type: string
 *                             id:
 *                               type: string
 *                       starRating:
 *                         type: number
 *                       overallRating:
 *                         type: object
 *                         properties:
 *                           overall:
 *                             type: number
 *                 pagination:  # Pagination details
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     totalProperties:
 *                       type: integer
 *       '500':
 *         description: Internal server error
 */
/**
 * @swagger
 * /otaProperties/property/{propertyId}:
 *   get:
 *     summary: List OTA property details with all room details
 *     tags: [otaProperties]
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the property to fetch details for
 *       - in: query
 *         name: currency
 *         schema:
 *           type: string
 *         description: Field to sort the rooms by (e.g., 'rate', 'capacity')
 *       - in: query
 *         name: symbol
 *         schema:
 *           type: string
 *         description: Order of sorting (asc or desc)
 *     responses:
 *       '200':
 *         description: Property with room details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Property with room details
 *                 data:
 *                   type: object
 *                   properties:
 *                     property:
 *                       type: object
 *                       properties:
 *                         name:
 *                           type: string
 *                         description:
 *                           type: string
 *                         distance:
 *                           type: number
 *                         photos:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               title:
 *                                 type: string
 *                               url:
 *                                 type: string
 *                         starRating:
 *                           type: number
 *                         overallRating:
 *                           type: number
 *                         facilities:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               name:
 *                                 type: string
 *                               _id:
 *                                 type: string
 *                               facilities:
 *                                 type: array
 *                                 items:
 *                                   type: object
 *                                   properties:
 *                                     name:
 *                                       type: string
 *                                     id:
 *                                       type: string
 *                         latitude:
 *                           type: number
 *                         longitude:
 *                           type: number
 *                     rooms:
 *                       type: object
 *                       properties:
 *                         privateRooms:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               type:
 *                                 type: string
 *                               name:
 *                                 type: string
 *                               capacity:
 *                                 type: number
 *                               rate:
 *                                 type: number
 *                               currency:
 *                                 type: string
 *                               grade:
 *                                 type: string
 *                               basicType:
 *                                 type: string
 *                         dormRooms:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               type:
 *                                 type: string
 *                               name:
 *                                 type: string
 *                               capacity:
 *                                 type: number
 *                               rate:
 *                                 type: number
 *                               currency:
 *                                 type: string
 *                               grade:
 *                                 type: string
 *                               basicType:
 *                                 type: string
 *       '500':
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal Server Error
 *                 error:
 *                   type: string
 */

/**
 * @swagger
 * /otaProperties/{state}:
 *   get:
 *     summary: Get OTA properties by state with search and pagination
 *     tags: [otaProperties]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term to filter properties by name
 *       - in: query
 *         name: state
 *         schema:
 *           type: string
 *         required: true
 *         description: State name to filter properties
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Properties retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 properties:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/OtaProperty'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                       description: The current page number
 *                     limit:
 *                       type: integer
 *                       description: The number of items per page
 *                     totalPages:
 *                       type: integer
 *                       description: The total number of pages
 *                     totalProperties:
 *                       type: integer
 *                       description: The total number of properties
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /otaProperties/countriesAndStates:
 *   get:
 *     summary: Get a list of countries and the states within each country
 *     tags: [otaProperties]
 *     responses:
 *       200:
 *         description: List of countries and states retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   country:
 *                     type: string
 *                     description: The country name
 *                   states:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of states within the country
 *       500:
 *         description: Internal server error
*/


/**
 * @swagger
 * components:
 *   schemas:
 *     OtaProperty:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *         id:
 *           type: number
 *         isPromoted:
 *           type: boolean
 *         hbid:
 *           type: number
 *         name:
 *           type: string
 *         starRating:
 *           type: number
 *         overallRating:
 *           type: object
 *         ratingBreakdown:
 *           type: object
 *         latitude:
 *           type: number
 *         longitude:
 *           type: number
 *         isFeatured:
 *           type: boolean
 *         type:
 *           type: string
 *         address1:
 *           type: string
 *         address2:
 *           type: string
 *         state:
 *           type: string
 *         country:
 *           type: string
 *         freeCancellationAvailable:
 *           type: boolean
 *         freeCancellationAvailableUntil:
 *           type: string
 *           format: date-time
 *         district:
 *           type: string
 *         districts:
 *           type: array
 *           items:
 *             type: string
 *         freeCancellation:
 *           type: object
 *         lowestPricePerNight:
 *           type: object
 *         lowestPrivatePricePerNight:
 *           type: object
 *         lowestDormPricePerNight:
 *           type: object
 *         lowestAveragePricePerNight:
 *           type: object
 *         lowestAverageDormPricePerNight:
 *           type: object
 *         lowestAveragePrivatePricePerNight:
 *           type: object
 *         isNewProperty:
 *           type: boolean
 *         overview:
 *           type: string
 *         isElevate:
 *           type: boolean
 *         hostelworldRecommends:
 *           type: boolean
 *         distance:
 *           type: object
 *         position:
 *           type: number
 *         hwExtra:
 *           type: object
 *         fabSort:
 *           type: object
 *         promotions:
 *           type: array
 *           items:
 *             type: object
 *         stayRuleViolations:
 *           type: array
 *           items:
 *             type: object
 *         veryPopular:
 *           type: boolean
 *         rooms:
 *           type: array
 *           items:
 *             type: object
 *         images:
 *           type: array
 *           items:
 *             type: object
 *         categories:
 *           type: array
 *           items:
 *             type: object
 *         facilities:
 *           type: array
 *           items:
 *             type: object
 *         isApproved:
 *           type: boolean
 *     Room:
 *       type: object
 *       properties:
 *         type:
 *           type: string
 *         name:
 *           type: string
 *         capacity:
 *           type: integer
 *         rate:
 *           type: number
 *         currency:
 *           type: string
 *         grade:
 *           type: string
 *         basicType:
 *           type: string
*/

// /**
//  * @swagger
//  * /otaProperties/management:
//  *   get:
//  *     summary: Get all OTA properties grouped by country and state
//  *     tags: [otaProperties]
//  *     parameters:
//  *       - in: query
//  *         name: page
//  *         schema:
//  *           type: integer
//  *           minimum: 1
//  *           default: 1
//  *         description: Page number for pagination
//  *       - in: query
//  *         name: limit
//  *         schema:
//  *           type: integer
//  *           minimum: 1
//  *           default: 10
//  *         description: Number of items per page
//  *       - in: query
//  *         name: search
//  *         schema:
//  *           type: string
//  *         description: Search term to filter properties by name
//  *     responses:
//  *       200:
//  *         description: Properties grouped by country and state retrieved successfully
//  *         content:
//  *           application/json:
//  *             schema:
//  *               type: object
//  *               properties:
//  *                 properties:
//  *                   type: array
//  *                   items:
//  *                     type: object
//  *                     properties:
//  *                       country:
//  *                         type: string
//  *                         description: The country name
//  *                       states:
//  *                         type: array
//  *                         items:
//  *                           type: object
//  *                           properties:
//  *                             state:
//  *                               type: string
//  *                               description: The state name
//  *                             properties:
//  *                               type: array
//  *                               items:
//  *                                 $ref: '#/components/schemas/OtaProperty'
//  *                 pagination:
//  *                   type: object
//  *                   properties:
//  *                     page:
//  *                       type: integer
//  *                       description: The current page number
//  *                     limit:
//  *                       type: integer
//  *                       description: The number of items per page
//  *                     totalPages:
//  *                       type: integer
//  *                       description: The total number of pages
//  *                     totalDocuments:
//  *                       type: integer
//  *                       description: The total number of documents
//  *       500:
//  *         description: Internal server error
//  */
/**
 * @swagger
 * /otaProperties/list/{name}:
 *   get:
 *     tags: 
 *       - otaProperties
 *     parameters:
 *       - in: path
 *         name: name
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the property to search for
 *     responses:
 *       200:
 *         description: A list of properties matching the provided name
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: The unique identifier of the property
 *                   name:
 *                     type: string
 *                     description: The name of the property
 *                   location:
 *                     type: string
 *                     description: The location of the property
 *                   description:
 *                     type: string
 *                     description: A brief description of the property
 *       400:
 *         description: Invalid input - name parameter missing or malformed
 *       401:
 *         description: Unauthorized - invalid or missing authentication token
 *       404:
 *         description: No properties found with the specified name
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /otaProperties/migrate:
 *   post:
 *     tags: 
 *       - otaProperties
 *     responses:
 *       400:
 *         description: Invalid input - name parameter missing or malformed
 *       401:
 *         description: Unauthorized - invalid or missing authentication token
 *       404:
 *         description: No properties found with the specified name
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /otaProperties/get-joined-users:
 *   get:
 *     summary: Get unique users who visited a property in the last 7 days
 *     tags: [otaProperties]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: propertyId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the property
 *     responses:
 *       '200':
 *         description: Successfully retrieved unique users who visited the property
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 users:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                         example: "6764578ef3b962658baa53d3"
 *                       name:
 *                         type: string
 *                         example: "John Doe"
 *                       email:
 *                         type: string
 *                         example: "<EMAIL>"
 *                       latestBooking:
 *                         type: string
 *                         format: date-time
 *                         example: "2024-11-20T04:29:03.354Z"
 *       '400':
 *         description: Missing or invalid propertyId
 *         content:
 *           application/json:
 *             example:
 *               message: "Invalid propertyId"
 *       '404':
 *         description: No users found for the given property
 *         content:
 *           application/json:
 *             example:
 *               message: "No users found"
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: "Internal server error"
 */
