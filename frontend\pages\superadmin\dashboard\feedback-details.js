import Link from "next/link";
import React from "react";

const FeedbackDetails = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616] h-screen">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        Feedback Details
      </h2>
      <div className="bg-white border rounded-xl mt-5 h-auto dark:bg-black dark:border-none">
        <div className="p-8 flex flex-col gap-y-6">
          <div>
            <div className="flex items-center justify-between">
              <h1 className="text-base md:text-lg lg:text-lg font-bold dark:text-[#B6B6B6]">
                Hostel Information
              </h1>
            </div>
            <div className="flex items-center gap-x-20 my-6">
              <div className="flex whitespace-nowrap w-[15%] flex-col">
                <strong className="text-black/70 text-base dark:text-[#757575]">
                  User Name
                </strong>
                <p className="mt-2 text-black/55 text-base dark:text-[#B6B6B6]">
                  Stuart Coats
                </p>
              </div>
              <div className="flex w-[60%] md:w-[60%] lg:w-[20%] flex-col">
                <strong className="text-black/70 text-base dark:text-[#757575]">
                  Hostel Name
                </strong>
                <p className="mt-2 text-black/55 text-base dark:text-[#B6B6B6]">
                  88 Backpakers
                </p>
              </div>
            </div>
            <div className="flex items-center gap-x-20 my-6">
              <div className="flex w-[15%] flex-col">
                <strong className="text-black/70 text-base dark:text-[#757575]">
                  TIME
                </strong>
                <p className="mt-2 text-black/55 text-base dark:text-[#B6B6B6]">
                  03:50
                </p>
              </div>
              <div className="flex w-[20%] flex-col">
                <strong className="text-black/70 text-base dark:text-[#757575]">
                  Date
                </strong>
                <p className="mt-2 text-black/55 text-base dark:text-[#B6B6B6]">
                  02/04/24
                </p>
              </div>
              <div className="flexw-[30%] flex-col">
                <strong className="text-black/70 text-base dark:text-[#757575]">
                  Rate
                </strong>
                <p className="mt-2 text-black/55 text-base dark:text-[#B6B6B6]">
                  4.5
                </p>
              </div>
            </div>
          </div>

          <div>
            <h1 className="text-lg font-bold dark:text-[#B6B6B6]">
              Feedback Information
            </h1>
            <div className="flex items-center gap-x-20 my-6">
              <div className="flex flex-col">
                <strong className="text-black/75 text-base dark:text-[#757575]">
                  Feedback
                </strong>
                <p className="mt-2 text-black/55 text-base dark:text-[#B6B6B6]">
                  Bed sheet dirty and bed not arranges on time
                </p>
              </div>
            </div>
          </div>
          <div className="flex items-start justify-start">
            <Link
              href={"/superadmin/dashboard/feedback"}
              className="text-white py-2 w-32 rounded bg-sky-blue-650 flex items-center justify-center"
            >
              Cancel
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeedbackDetails;
