import sendEmail from '../commons/mail.js';
import ChannelManager from '../models/chanelManagers.js';
import Response from "../utills/response.js";

// Controller to get channel managers with optional search
export const getChannelManagers = async (req, res) => {
  const { name } = req.query; // Get the 'name' from the query parameters

  try {
    const filter = name ? { name: { $regex: name, $options: 'i' } } : {};
    
    // Find channel managers that match the filter (if 'name' is provided, it will filter by name)
    const channelManagers = await ChannelManager.find(filter);
    return Response.OK(res, channelManagers, 'Chanel Managers List');
  } catch (error) {
    console.error('Error retrieving channel managers:', error);
    res.status(500).json({ error: 'Failed to retrieve channel managers' });
  }
};
export const contractSign = async (req, res) => {
  const { date,hostelName,hostelAddress,name } = req.body; // Get the 'name' from the query parameters

  try {
    
    return Response.OK(res, null, 'Contact Sign Success');
  } catch (error) {
    console.error('Error retrieving channel managers:', error);
    res.status(500).json({ error: 'Failed to retrieve channel managers' });
  }
};
export const contractSignEmail = async (req, res) => {
  const { email,cc,subject,body } = req.body; 
  try {
    await sendEmail("contactEmail", { email: req.body.email, 
      chanel_manager:"HyperGuest",
       name: 'Priyanka',
       hostel_name:"Vasudha Hostel" ,
       hostel_email:"<EMAIL>"});
    return Response.OK(res, null, 'Contact Sign Success');
  } catch (error) {
    console.error('Error retrieving channel managers:', error);
    res.status(500).json({ error: 'Failed to retrieve channel managers' });
  }
};