 
// "use client";
// import React from "react";
// import {
//   AreaChart,
//   Area,
//   XAxis,
//   YAxis,
//   CartesianGrid,
//   Tooltip,
//   ResponsiveContainer,
// } from "recharts";

// const DashboardGraph = () => {
//   const data = [
//     {
//       name: "Jan",
//       uv: 100,
//       pv: 200,
//       amt: 210,
//     },
//     {
//       name: "Feb",
//       uv: 200,
//       pv: 108,
//       amt: 2210,
//     },
//     {
//       name: "Mar",
//       uv: 100,
//       pv: 300,
//       amt: 2290,
//     },
//     {
//       name: "Apr",
//       uv: 700,
//       pv: 508,
//       amt: 2000,
//     },
//     {
//       name: "May",
//       uv: 600,
//       pv: 400,
//       amt: 2181,
//     },
//     {
//       name: "Jun",
//       uv: 250,
//       pv: 400,
//       amt: 2500,
//     },
//     {
//       name: "Jal",
//       uv: 500,
//       pv: 400,
//       amt: 2100,
//     },
//   ];

//   return (
//     <div style={{ padding: "20px" }}>
//       <ResponsiveContainer width="100%" height={300}>
//         <AreaChart
//           data={data}
//           margin={{
//             top: 10,
//             right: 30,
//             left: 0,
//             bottom: 0,
//           }}
//         >
//           <CartesianGrid strokeDasharray="3 3" />
//           <XAxis dataKey="name" axisLine={false} />
//           <YAxis />
//           <Tooltip />
//           <Area type="monotone" dataKey="uv" stroke="#B3E5FC" fill="#E1F5FE" />
//         </AreaChart>
//       </ResponsiveContainer>
//     </div>
//   );
// };

// export default DashboardGraph;


import { useEffect, useRef } from "react";
import { Chart } from "chart.js/auto";

const DashboardGraph = () => {
  const chartRef = useRef(null);

  useEffect(() => {
    const ctx = chartRef.current.getContext("2d");
    const data = {
      labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul"],
      datasets: [
        {
          label: "Revenue",
          data: [1500, 2000, 2500, 4000, 2000, 3000, 4500],
          borderColor: "#3B82F6",
          backgroundColor: "rgba(59, 130, 246, 0.2)",
          borderWidth: 2,
          fill: true,
          tension: 0.4,
        },
      ],
    };

    const options = {
      responsive: true,
      plugins: {
        legend: {
          display: false,
        },
        tooltip: {
          enabled: true,
        },
      },
      scales: {
        y: {
          beginAtZero: true,
          max: 5000,
          ticks: {
            stepSize: 1000,
            color: "#9CA3AF",
          },
          grid: {
            color: "#E5E7EB",
          },
        },
        x: {
          ticks: {
            color: "#9CA3AF",
          },
          grid: {
            display: false,
          },
        },
      },
      animation: {
        duration: 2000, // Slow transition on reload
        easing: "easeInOutQuart",
      },
    };

    const chart = new Chart(ctx, {
      type: "line",
      data,
      options,
    });

    return () => chart.destroy(); // Cleanup on component unmount
  }, []);

  return (
    <div className="bg-white p-6 w-full max-w-3xl mx-auto dark:bg-black">

      <canvas ref={chartRef}></canvas>
    </div>
  );
};

export default DashboardGraph;
