/* eslint-disable react/no-unescaped-entities */
 
import React, { useState, useEffect } from "react";
import {  FaApple, FaFacebook } from "react-icons/fa";
import { Button, Checkbox, FormControlLabel } from "@mui/material";
import toast, { Toaster } from "react-hot-toast";
import { logInApi, resendOtpApi } from "../../services/webflowServices";
import { setItemLocalStorage, setToken } from "@/utils/browserSetting";
import Link from "next/link";
import { requestForFCMToken } from "@/utils/firebaseConfig";
import { saveFirebaseToken } from "@/services/ownerflowServices";
import { useNavbar } from "../home/<USER>";
import { FacebookProvider, LoginButton } from "react-facebook";
import GoogleSocialLogin from "../socialLogin/googleSocialLogin";
import AppleLogin from "react-apple-login";
import { GoogleOAuthProvider } from "@react-oauth/google";
import { useRouter } from "next/router";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import VisibilityOffOutlinedIcon from "@mui/icons-material/VisibilityOffOutlined";
import axios from "axios";
import { BASE_URL } from "@/utils/api";

const SignInPopup = ({ isOpen, onClose, openSignUpForm, closeLoginPopup }) => {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value.trim() });
  };

  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const { updateUserStatus, updateUserRole } = useNavbar();

  const handleSignIn = async (e) => {
    e.preventDefault();
    if (!formData.email || !formData.password) {
      setError("All fields are required.");
      return;
    }

    setLoading(true);
    setError("");

    try {
      const response = await logInApi({
        email: formData.email,
        password: formData.password,
        role: "user",
      });
      console.log("response", response);
      if (response?.status === 403) {
        try {
          const response = await resendOtpApi({ email: formData?.email });
          console.log("response22", response);

          if (response?.data?.status) {
            toast.success(
              `We’ve sent a verification code to ${formData?.email}. Please enter this code to continue.`
            );

            router.push("/verifyotp");
          } else {
            toast.error(
              response.data.message || "Failed to resend verification code."
            );
          }
          onClose();
        } catch (error) {
          toast.error(
            error.response?.data?.message ||
              "An error occurred while resending OTP."
          );
          console.error("Resend OTP error:", error);
        }
      } else if (response?.data?.status) {
        setItemLocalStorage("name", response?.data?.data?.user?.name.first);
        setItemLocalStorage("id", response?.data?.data?.user?._id);
        setItemLocalStorage("role", response?.data?.data?.user?.role);
        setItemLocalStorage("email", response?.data?.data?.user?.email);
        setItemLocalStorage("contact", response?.data?.data?.user?.contact);
        setToken(response?.data?.data?.token);
        updateUserStatus(response?.data?.data?.token);
        updateUserRole(response?.data?.data?.user?.role);
        toast.success(response?.data?.message);
        const fcmToken = await requestForFCMToken();
        if (fcmToken) {
          setItemLocalStorage("FCT", fcmToken);
          await saveFirebaseToken({
            token: fcmToken,
            userId: response?.data?.data?.user?._id,
          });
        }
        onClose(); // Close SignInPopup
        closeLoginPopup(); // Close LoginPopup
      } else {
        toast.error(response.data.message || "An error occurred.");
      }
    } catch (error) {
      setError(
        error.response?.data?.message || "An error occurred, please try again."
      );
      console.error("Login error:", error.response);
    } finally {
      setLoading(false);
    }
  };

  const isDisabled = loading || !formData.email || !formData.password;

  const handleFacebookSuccess = async (response) => {
    try {
      const { accessToken } = response.authResponse;

      // Manually fetch user profile from Facebook using the access token
      const profileRes = await axios.get(
        `https://graph.facebook.com/me?fields=name,email&access_token=${accessToken}`
      );

      const { name, email } = profileRes.data;

      const res = await axios.post(`${BASE_URL}/auth/social/login`, {
        token: accessToken,
        role: "user",
        email,
        name,
      });

      if (res.status === 201 && res.data.status) {
        toast.success("Login successful!");

        setItemLocalStorage("name", name);
        setItemLocalStorage("role", res?.data?.data?.user?.role);
        setToken(res?.data?.data?.token);
        setItemLocalStorage("id", res?.data?.data?.user?._id);
        updateUserStatus(res?.data?.data?.token);
        updateUserRole(res?.data?.data?.user?.role);
        onClose();
        const fcmToken = await requestForFCMToken();
        if (fcmToken) {
          setItemLocalStorage("FCT", fcmToken);
          await saveFirebaseToken({
            token: fcmToken,
            userId: res?.data?.data?.user?._id,
          });
        }
      } else {
        toast.error("Social login failed: " + res.data.message);
      }
    } catch (error) {
      toast.error(error.response?.data.message || error.message);
    }
  };

  const handleFacebookFailure = (error) => {
    toast.error("Facebook login failed: " + error.message);
  };

  const handleAppleSuccess = async (response) => {
    try {
      const { authorization, user } = response;

      const { id_token } = authorization;

      const name = user?.name
        ? `${user.name.firstName} ${user.name.lastName}`
        : null;
      const email = user?.email || null;

      const res = await axios.post(`${BASE_URL}/auth/social/login`, {
        token: id_token,
        role: "user",
        provider: "apple",
        name: name,
        email: email,
      });

      if (res.status === 201 && res.data.status) {
        toast.success("Login successful!");

        setItemLocalStorage(
          "name",
          name ||
            `${res.data.data.user.name.first} ${res.data.data.user.name.last}`
        );
        setItemLocalStorage("role", res?.data?.data?.user?.role);
        setToken(res?.data?.data?.token);
        setItemLocalStorage("id", res?.data?.data?.user?._id);
        updateUserStatus(res?.data?.data?.token);
        updateUserRole(res?.data?.data?.user?.role);

        const fcmToken = await requestForFCMToken();
        if (fcmToken) {
          setItemLocalStorage("FCT", fcmToken);
          await saveFirebaseToken({
            token: fcmToken,
            userId: res?.data?.data?.user?._id,
          });
        }

        router.push("/");
      } else {
        toast.error("Social login failed: " + res.data.message);
      }
    } catch (error) {
      toast.error(error.response?.data.message || error.message);
    }
  };

  const handleAppleFailure = (response) => {
    toast.error("Apple login failed: " + response.error);
  };

  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-50'>
      <Toaster position='top-center' />
      <div className='bg-white rounded-2xl w-[90%] max-w-md p-5 font-manrope'>
        <div className='flex justify-end'>
          <button
            onClick={onClose}
            className='text-black hover:text-gray-600 transition duration-150'
          >
            ✕
          </button>
        </div>
        <div className='flex flex-col justify-center items-center'>
          <span className='text-[#40E0D0] flex text-2xl font-extrabold sm:mb-2'>
            Mix<p className='text-black text-2xl font-extrabold '>Dorm</p>
          </span>
        </div>
        <form onSubmit={handleSignIn} className='mx-1 sm:mx-8'>
          <h2 className='text-[16px] sm:text-xl font-bold text-gray-800 sm:mb-6 mb-4 text-center'>
            👋 Login to your Account
          </h2>

          <div className='sm:mb-5 mb-3'>
            {/* <label
              htmlFor="email"
              className="text-base block font-semibold text-gray-700"
            >
              Email
            </label> */}
            <input
              type='email'
              id='email'
              name='email'
              value={formData.email}
              onChange={handleChange}
              className='w-full px-3 sm:py-4 py-3 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500'
              placeholder='Email'
            />
          </div>
          <div className='relative sm:mb-5 mb-1'>
            {/* <label
              htmlFor="password"
              className="text-base block font-semibold text-gray-700"
            >
              Password
            </label> */}
            <input
              type={showPassword ? "text" : "password"}
              id='password'
              name='password'
              value={formData.password}
              onChange={handleChange}
              className='w-full px-3 sm:py-4 py-3 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500'
              placeholder='Password'
            />
            {isClient && (
              <button
                type='button'
                className='absolute right-4 top-[15px] text-gray-300 transform'
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <VisibilityOutlinedIcon />
                ) : (
                  <VisibilityOffOutlinedIcon />
                )}
              </button>
            )}
          </div>
          <div className='flex justify-between items-center sm:mb-6 mb-1'>
            <FormControlLabel
              control={
                <Checkbox
                  className='text-[12px] sm:text-sm mt-[4px] block sm:mt-0'
                  sx={{
                    color: "rgba(0,0,0,0.4)", // blue-800 color from Tailwind
                    "&.Mui-checked": {
                      color: "#40E0D0", // blue-800 when checked
                    },
                  }}
                />
              }
              label={
                <span className=' text-[12px] sm:text-sm'>Remember me</span>
              }
            />

            <Link
              href='/forgot-password'
              className='text-[#40E0D0] font-medium ml-1 cursor-pointer'
              prefetch={false}
              onClick={onClose}
            >
              <span className='text-[12px] sm:text-sm'> Forgot Passsword?</span>
            </Link>
          </div>
          <button
            type='submit'
            className={`w-full sm:my-3 my-1 sm:text-base text-sm font-semibold text-white sm:py-4 py-3 rounded-4xl transition duration-200 ${
              isDisabled
                ? "bg-gray-400 cursor-not-allowed"
                : "bg-[#40E0D0] hover:bg-teal-500"
            }`}
            disabled={isDisabled}
          >
            {loading ? "Signing in..." : "Sign In"}
          </button>
          {error && (
            <div className='mb-4 text-center text-red-600'>{error}</div>
          )}
        </form>
        <div className='flex items-center justify-center mx-8 space-x-4 sm:my-8 my-3'>
          <span className='flex-1 border-t border-gray-200'></span>
          <span className='text-gray-600 sm:text-base text-sm'>Or</span>
          <span className='flex-1 border-t border-gray-200'></span>
        </div>

        <div className='flex items-center sm:gap-x-4 gap-x-2 justify-center sm:mb-6 mb-2'>
          <FacebookProvider appId={process.env.NEXT_PUBLIC_FACEBOOK_APP_ID}>
            <LoginButton
              scope='public_profile,email'
              onSuccess={handleFacebookSuccess}
              onError={handleFacebookFailure}
              className='min-w-0 p-0 sm:text-3xl text-xl'
            >
              <FaFacebook className="text-[#0866ff]" />
            </LoginButton>
          </FacebookProvider>
          <GoogleOAuthProvider
            clientId={process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID}
          >
            <GoogleSocialLogin role='user' closeLoginPopup={onClose} />
          </GoogleOAuthProvider>

          <AppleLogin
            clientId={process.env.NEXT_PUBLIC_APPLE_CLIENT_ID}
            redirectURI={process.env.NEXT_PUBLIC_APPLE_REDIRECT_URI}
            responseType='code id_token'
            scope='name email'
            render={(renderProps) => (
              <Button
                className='min-w-0 p-0 text-3xl text-black mb-1'
                onClick={renderProps.onClick}
              >
                <FaApple  />
              </Button>
            )}
            onSuccess={handleAppleSuccess}
            onError={handleAppleFailure}
          />
        </div>
        <h4 className='text-basec text-center text-sm text-gray-400'>
          Don't have an account?
          <a
            className='text-[#40E0D0] font-medium ml-1 cursor-pointer'
            onClick={openSignUpForm}
          >
            Sign Up
          </a>
        </h4>
      </div>
    </div>
  );
};

export default SignInPopup;
