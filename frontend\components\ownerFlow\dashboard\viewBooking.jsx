/* eslint-disable no-constant-binary-expression */
import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
import toast from "react-hot-toast";
import { EditBookingDataApi, RoomListApi } from "@/services/ownerflowServices";
import { getItemLocalStorage } from "@/utils/browserSetting";
import countries from "world-countries";
import Loader from "@/components/loader/loader";

const ViewBooking = ({ editId }) => {
  const [bookingData, setBookingData] = useState({
    user: "",
    guestDetails: { name: "", email: "", phone: "" },
    referenceNumber: "",
    property: "",
    room: "",
    roomNumber: "",
    beds: "",
    checkInDate: "",
    checkOutDate: "",
    guests: { adults: 0, children: 0 },
    rate: "",
    currency: "",
    tax: "",
    totalAmount: "",
    balance: "",
    amoutPaid: "",
    isCancel: false,
    cancelledDate: "",
    cancelledBy: "",
    status: "confirmed",
    paymentStatus: "unpaid",
    isActive: true,
    isDeleted: false,
  });
  const id = getItemLocalStorage("hopid");
  const isFirstRender = useRef(null);
  const [roomList, setRoomList] = useState([]);
  const [currencyData, setCurrencyData] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const fetchList = async (id) => {
    setIsLoading(true);
    try {
      const response = await RoomListApi(id, 1, 100);
      if (response.status === 200) {
        setRoomList(response.data.data.rooms);
      } else {
        // Handle error response
        toast.error("Failed to fetch rooms");
      }
    } catch (error) {
      console.log("Error fetching rooms:", error);
      toast.error("Error fetching rooms");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (id && !isFirstRender.current) {
      fetchList(id);
    } else {
      isFirstRender.current = false;
    }
  }, [id]);

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, "0"); // Add leading zero
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Add leading zero, months are 0-indexed
    const year = date.getFullYear();

    return `${year}-${month}-${day}`;
  };

  const fetchRoomData = async () => {
    setIsLoading(true);

    try {
      const response = await EditBookingDataApi(editId);
      if (response.status === 200) {
        setBookingData({
          guestDetails: response?.data?.data?.guestDetails,
          beds: response?.data?.data?.beds,
          room: roomList.find(
            (data) => data?._id === response?.data?.data?.room
          )?.name,
          checkInDate: formatDate(response?.data?.data?.checkInDate),
          checkOutDate: formatDate(response?.data?.data?.checkOutDate),
          currency: response?.data?.data?.currency,
          totalAmount: response?.data?.data?.totalAmount,
          referenceNumber: response?.data?.data?.referenceNumber
        });
      } else {
        // Handle error response
        toast.error("Failed to fetch paymentData");
      }
    } catch (error) {
      console.log("Error fetching paymentData:", error);
      toast.error("Error fetching paymentData");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (roomList.length > 0 && editId) {
      fetchRoomData();
    }
  }, [roomList, editId]);

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);
      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);

  const getCurrencySymbol = (currencyCode) => {
    return currencyData[currencyCode] || currencyCode;
  };

  const [selected, setSelected] = useState({
          wifi: true,
          water: true,
          hangout: true,
        });

  const checkboxes = [
    { key: "wifi", label: "Free Wi-Fi", icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/lockers.svg` },
    { key: "water", label: "Water Dispenser", icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/lockers.svg` },
    { key: "hangout", label: "Common hangout area", icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/lockers.svg` },
  ]

  const toggleCheckbox = (key) => {
    setSelected((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  return (
    <>
      <Loader open={isLoading} />
      <div className='w-full bg-white shadow-4xl rounded-2xl'>
        <div>
          <div className='max-w-[780px] mx-auto pb-5'>
            <div className='grid sm:grid-cols-[1.9fr_3fr] grid-cols-1 sm:gap-4 gap-3 font-inter'>
              <div>
                <Image className="rounded-lg h-[170px]" src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/hotel1.png`} alt="" />
                <button className="bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4 mt-3 border-black rounded-lg w-full text-sm">Room Details</button>
              </div>
              <div>
                <div className="mb-5">
                  <p className='font-bold text-black sm:text-base text-xs'>
                    {bookingData?.guestDetails?.name || " - "} 
                  </p>
                  <p className='font-normal text-[#00000080] sm:text-base text-xs'>Id :{bookingData?.referenceNumber || " - "} </p>
                </div>
                <div className="flex justify-between mb-5">
                  <div>
                    <p className='font-bold text-black sm:text-base text-xs'>
                      Check in :
                    </p>
                    <p className='font-normal text-[#00000080] sm:text-base text-xs'>
                      {bookingData?.checkInDate ? new Date(bookingData?.checkInDate)?.toLocaleDateString(
                      "en-GB",
                      {
                        day: "2-digit",
                        month: "2-digit",
                        year: "numeric",
                      }
                    ) : '-' || "-"}
                    </p>
                  </div>
                  <div>
                    <p className='font-bold text-black sm:text-base text-xs'>
                      Check out :
                    </p>
                    <p className='font-normal text-[#00000080] sm:text-base text-xs'>
                        {bookingData?.checkOutDate ? new Date(bookingData?.checkOutDate)?.toLocaleDateString(
                        "en-GB",
                        {
                          day: "2-digit",
                          month: "2-digit",
                          year: "numeric",
                        }
                      ) : '-' || "-"}
                    </p>
                  </div>
                </div>
                <div className="flex justify-between mb-5">
                  <div>
                    <p className='font-bold text-black sm:text-base text-xs'>
                      Guest :
                    </p>
                    <p className='font-normal text-[#00000080] sm:text-base text-xs'>
                      1
                    </p>
                  </div>
                  <div>
                    <p className='font-bold text-black sm:text-base text-xs'>
                      Room No :
                    </p>
                    <p className='font-normal text-[#00000080] sm:text-base text-xs'>
                        1
                    </p>
                  </div>
                </div>
                <div className="flex justify-between mb-5">
                  <div>
                    <p className='font-bold text-black sm:text-base text-xs'>
                      Room Type :
                    </p>
                    <p className='font-normal text-[#00000080] sm:text-base text-xs'>
                      4 bed mixdorm
                    </p>
                  </div>
                  <div>
                    <p className='font-bold text-black sm:text-base text-xs'>
                      Room No :
                    </p>
                    <p className='font-normal text-[#00000080] sm:text-base text-xs'>
                        1
                    </p>
                  </div>
                </div>
                <div>
                  <p className='font-bold text-black sm:text-base text-xs mb-3'>
                    Guest Provide
                  </p>
                  <div className="">
                      {checkboxes.map(({ key, label, icon }) => (
                        <label key={key} className="flex items-center gap-2 cursor-pointer mb-2">
                          <input
                            type="checkbox"
                            checked={selected[key]} 
                            onChange={() => toggleCheckbox(key)}
                            className="hidden"
                          />
                          <div
                            className={`sm:w-5 sm:h-5 min-w-5 min-h-5 w-4 h-4 flex items-center justify-center border rounded-sm transition-all ${
                              selected[key] ? "bg-[#40E0D0]" : "border-[#979797]"
                            }`}
                          >
                            {selected[key] && <span className="text-black sm:text-md text-sm">✔</span>}
                          </div>
                          <Image src={icon} width={20} height={20} />
                          <span className="sm:text-sm text-xs font-medium text-black">{label}</span>
                        </label>
                      ))}
                  </div>
                </div>
                <p className='font-bold text-black sm:text-base text-xs mb-3 mt-4'>
                  Payment Summary
                </p>
                <div className="flex justify-between mb-5">
                  <p className='font-bold text-black sm:text-base text-xs'>
                    Total Amount 
                  </p>
                  <p className='font-normal text-[#00000080] sm:text-base text-xs'>
                    {getCurrencySymbol(bookingData?.currency)}{" "}
                    {bookingData?.totalAmount || " - "}
                  </p>
                </div>
                
                <div className="flex justify-between mb-5">
                  <p className='font-bold text-black sm:text-base text-xs'>
                  Amount Piad
                  </p>
                  <p className='font-normal text-[#00000080] sm:text-base text-xs'>
                  {getCurrencySymbol(bookingData?.currency)}{" "}
                  {bookingData?.totalAmount || " - "}
                  </p>
                </div>
                <div className="flex justify-between mb-5">
                  <p className='font-bold text-black sm:text-base text-xs'>
                  Balance
                  </p>
                  <p className='font-normal text-[#00000080] sm:text-base text-xs'>
                  {getCurrencySymbol(bookingData?.currency)}{" "}
                  {bookingData?.totalAmount || " - "}
                  </p>
                </div>  
              </div>
              {/* <div>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {bookingData?.guestDetails?.email || " - "}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {bookingData?.guestDetails?.phone || " - "}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {bookingData?.guestDetails?.name || " - "}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {bookingData?.checkInDate ? new Date(bookingData?.checkInDate)?.toLocaleDateString(
                    "en-GB",
                    {
                      day: "2-digit",
                      month: "2-digit",
                      year: "numeric",
                    }
                  ) : '-' || "-"}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                {bookingData?.checkOutDate ? new Date(bookingData?.checkOutDate)?.toLocaleDateString(
                    "en-GB",
                    {
                      day: "2-digit",
                      month: "2-digit",
                      year: "numeric",
                    }
                  ) : '-' || "-"}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {bookingData.roomNumber || " - "}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {bookingData?.room?.value || " - "}
                </p>
              </div>
              
              <div>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {bookingData?.beds || " - "}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {getCurrencySymbol(bookingData?.currency)}{" "}
                  {bookingData?.totalAmount || " - "}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {bookingData?.currency || " - "}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {bookingData?.amoutPaid || " - "}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {bookingData?.balance || " - "}
                </p>
              </div> */}
            </div>
            <div className='flex justify-between my-10 gap-4 py-4 bg-white/60 sticky bottom-0 backdrop-blur-sm'>
              <button
                className=' bg-[#F9A63A33] text-[#F9A63A] font-medium py-2 px-5 rounded-lg text-sm border-0'
              >
                Confirm Booking
              </button>
              <button
                className='bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-5 border-black rounded-lg text-sm'
              >
                Booking Reminder
              </button>
          </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ViewBooking;
