import express from 'express';
import { addEvent<PERSON>ontroller, updateEventController, 
    getEventByIdController, listAllEventsController, 
    deleteEventController,eventCheckout,addEventReviews,getEvenetsMembers } from '../controller/event.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();

router.post('/', checkAuth('add_event'), addEventController);
router.put('/:id', checkAuth('update_event'), updateEventController);
router.get('/view-members',checkAuth('event_review'), getEvenetsMembers)
router.get('/:id', getEventByIdController);
router.get('/',listAllEventsController);
router.delete('/:id', checkAuth('delete_event'), deleteEventController);
router.post('/checkout/:eventId', checkAuth('get_checkout_page'), eventCheckout);
router.post('/reviews/:eventId',checkAuth('event_review'), addEventReviews)


export default router;

/**
 * @swagger
 * tags:
 *   name: Event
 *   description: Event Management
*/

/**
 * @swagger
 * /events:
 *   post:
 *     tags: [Event]
 *     summary: Create a new event
 *     description: Creates a new event
 *     security:
 *       - bearerAuth: []
 *     consumes:
 *       - application/json
 *     produces:
 *       - application/json
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/definitions/Event'
 *     responses:
 *       201:
 *         description: Event Created Successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/EventResponse'
 *       500:
 *         description: Internal Server Error
 */

/**
 * @swagger
 * /events/{id}:
 *   put:
 *     tags: [Event]
 *     summary: Update event by ID
 *     description: Updates an existing event
 *     security:
 *       - bearerAuth: []
 *     consumes:
 *       - application/json
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/definitions/Event'
 *     responses:
 *       200:
 *         description: Event Updated Successfully
 *         content:
 *           application/json:
 *             schema:
 *               example:
 *                 message: 'Event updated successfully'
 *       500:
 *         description: Internal Server Error
 */

/**
 * @swagger
 * /events/{id}:
 *   get:
 *     tags: [Event]
 *     summary: Get event by ID
 *     description: Returns a single event
 *     security:
 *       - bearerAuth: []
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Event Retrieved Successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/EventResponse'
 *       500:
 *         description: Internal Server Error
 */

/**
 * @swagger
 * /events:
 *   get:
 *     tags: [Event]
 *     summary: List all events
 *     description: Returns a list of all events
 *     security:
 *       - bearerAuth: []
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: page
 *         in: query
 *         required: false
 *         type: integer
 *         default: 1
 *       - name: limit
 *         in: query
 *         required: false
 *         type: integer
 *         default: 10
 *       - name: filter
 *         in: query
 *         required: false
 *         schema:
 *           type: object
 *           style: deepObject
 *           explode: true
 *     responses:
 *       200:
 *         description: Events Retrieved Successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 events:
 *                   type: array
 *                   items:
 *                     $ref: '#/definitions/EventResponse'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     totalEvents:
 *                       type: integer
 *       500:
 *         description: Internal Server Error
 */

/**
 * @swagger
 * /events/{id}:
 *   delete:
 *     tags: [Event]
 *     summary: Delete event by ID
 *     description: Soft deletes an event
 *     security:
 *       - bearerAuth: []
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Event Deleted Successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       500:
 *         description: Internal Server Error
 */

/**
 * @swagger
 * definitions:
 *   Event:
 *     type: object
 *     required:
 *       - title
 *       - startDate
 *       - duration
 *       - hours
 *       - minutes
 *       - price
 *       - address
 *       - location
 *       - createdBy
 *       - propertyId
 *     properties:
 *       title:
 *         type: string
 *         description: Title of the event
 *       startDate:
 *         type: string
 *         format: date-time
 *         description: Start date and time of the event
 *       endDate:
 *         type: string
 *         format: date-time
 *         description: End date and time of the event (optional)
 *       duration:
 *         type: number
 *         description: Duration of the event in hours
 *       hours:
 *         type: number
 *         description: Event duration in hours
 *       minutes:
 *         type: number
 *         description: Event duration in minutes
 *       price:
 *         type: number
 *         description: Price for the event
 *       address:
 *         type: string
 *         description: Address of the event location
 *       location:
 *         type: object
 *         description: Geographical location of the event
 *         properties:
 *           type:
 *             type: string
 *             enum: [Point]
 *             description: Type of the geographical location
 *           coordinates:
 *             type: array
 *             items:
 *               type: number
 *             description: Coordinates [longitude, latitude]
 *       attachment:
 *         type: object
 *         description: Event attachment URL (optional)
 *         properties:
 *           url:
 *             type: string
 *             description: URL of the attachment
 *       description:
 *         type: string
 *         description: Detailed description of the event (optional)
 *       tags:
 *         type: array
 *         items:
 *           type: string
 *         description: Tags associated with the event
 *       createdBy:
 *         type: string
 *         format: ObjectId
 *         description: User who created the event
 *       propertyId:
 *         type: string
 *         format: ObjectId
 *         description: Property associated with the event
 *       status:
 *         type: string
 *         enum: [Upcoming, Ongoing, Completed]
 *         description: Status of the event
 *       isActive:
 *         type: boolean
 *         description: Whether the event is active
 *       isDeleted:
 *         type: boolean
 *         description: Whether the event is deleted
 *       refundable:
 *         type: boolean
 *         description: Whether the event is refundable
 *       nonRefundable:
 *         type: boolean
 *         description: Whether the event is non-refundable
 *       cancellation:
 *         type: boolean
 *         description: Whether the event has a cancellation policy
 *       createdAt:
 *         type: string
 *         format: date-time
 *         description: Timestamp when the event was created
 *       updatedAt:
 *         type: string
 *         format: date-time
 *         description: Timestamp when the event was last updated
*/

/**
 * @swagger
 * definitions:
 *   EventResponse:
 *     type: object
 *     required:
 *       - title
 *       - startDate
 *       - duration
 *       - hours
 *       - minutes
 *       - price
 *       - address
 *       - location
 *     properties:
 *       title:
 *         type: string
 *         description: Title of the event
 *       startDate:
 *         type: string
 *         format: date-time
 *         description: Start date and time of the event
 *       endDate:
 *         type: string
 *         format: date-time
 *         description: End date and time of the event (optional)
 *       duration:
 *         type: number
 *         description: Duration of the event in hours
 *       hours:
 *         type: number
 *         description: Event duration in hours
 *       minutes:
 *         type: number
 *         description: Event duration in minutes
 *       price:
 *         type: number
 *         description: Price for the event
 *       address:
 *         type: string
 *         description: Address of the event location
 *       location:
 *         type: object
 *         description: Geographical location of the event
 *         properties:
 *           type:
 *             type: string
 *             enum: [Point]
 *             description: Type of the geographical location
 *           coordinates:
 *             type: array
 *             items:
 *               type: number
 *             description: Coordinates [longitude, latitude]
 *       attachment:
 *         type: object
 *         description: Event attachment URL (optional)
 *         properties:
 *           url:
 *             type: string
 *             description: URL of the attachment
 *       description:
 *         type: string
 *         description: Detailed description of the event (optional)
 *       tags:
 *         type: array
 *         items:
 *           type: string
 *         description: Tags associated with the event
 *       createdBy:
 *         type: string
 *         format: ObjectId
 *         description: User who created the event
 *       propertyId:
 *         type: string
 *         format: ObjectId
 *         description: Property associated with the event
 *       status:
 *         type: string
 *         enum: [Upcoming, Ongoing, Completed]
 *         description: Status of the event
 *       isActive:
 *         type: boolean
 *         description: Whether the event is active
 *       isDeleted:
 *         type: boolean
 *         description: Whether the event is deleted
 *       refundable:
 *         type: boolean
 *         description: Whether the event is refundable
 *       nonRefundable:
 *         type: boolean
 *         description: Whether the event is non-refundable
 *       cancellation:
 *         type: boolean
 *         description: Whether the event has a cancellation policy
 *       createdAt:
 *         type: string
 *         format: date-time
 *         description: Timestamp when the event was created
 *       updatedAt:
 *         type: string
 *         format: date-time
 *         description: Timestamp when the event was last updated
*/
/**
 * @swagger
 * /events/checkout/{eventId}:
 *   post:
 *     tags: [Event]
 *     summary: Checkout event
 *     description: Retrieves event checkout details based on the event ID and number of people.
 *     parameters:
 *       - in: path
 *         name: eventId
 *         required: true
 *         description: ID of the event to checkout.
 *         schema:
 *           type: string
 *       - in: body
 *         name: body
 *         required: true
 *         description: Number of people attending the event.
 *         schema:
 *           type: object
 *           required:
 *             - person
 *           properties:
 *             person:
 *               type: number
 *               example: 2
 *     responses:
 *       201:
 *         description: Event Checkout details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 title:
 *                   type: string
 *                 startDate:
 *                   type: string
 *                   format: date-time
 *                 endDate:
 *                   type: string
 *                   format: date-time
 *                 duration:
 *                   type: string
 *                 price:
 *                   type: number
 *                 address:
 *                   type: string
 *                 location:
 *                   type: string
 *                 description:
 *                   type: string
 *                 totalPrice:
 *                   type: number
 *                   description: Total price for the event based on the number of people.
 *       400:
 *         description: Invalid person Number
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/Error'
 *       404:
 *         description: Event not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/Error'
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/Error'
 */
/**
 * @swagger
 * /events/reviews/{eventId}:
 *   post:
 *     tags: [Event]
 *     summary: Add a review for an event
 *     description: Allows users to add a review for a specific event.
 *     parameters:
 *       - in: path
 *         name: eventId
 *         required: true
 *         description: ID of the event to review.
 *         schema:
 *           type: string
 *       - in: requestBody
 *         name: body
 *         required: true
 *         description: Review data for the event.
 *         schema:
 *           type: object
 *           required:
 *             - rating
 *           properties:
 *             rating:
 *               type: number
 *               description: Rating of the event (0-5).
 *               example: 4
 *             comment:
 *               type: string
 *               description: Comment about the event.
 *               example: "Great event!"
 *             images:
 *               type: array
 *               description: Optional images related to the review.
 *               items:
 *                 type: object
 *                 properties:
 *                   url:
 *                     type: string
 *                     description: URL of the image.
 *                     example: "http://example.com/image.jpg"
 *                   title:
 *                     type: string
 *                     description: Title or description of the image.
 *                     example: "Front view"
 *             likes:
 *               type: number
 *               description: Number of likes for the review.
 *               example: 10
 *             dislikes:
 *               type: number
 *               description: Number of dislikes for the review.
 *               example: 2
 *     responses:
 *       201:
 *         description: Review added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 _id:
 *                   type: string
 *                   description: ID of the created review.
 *                 user:
 *                   type: string
 *                   description: ID of the user who created the review.
 *                 event:
 *                   type: string
 *                   description: ID of the event being reviewed.
 *                 rating:
 *                   type: number
 *                 comment:
 *                   type: string
 *                 images:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       url:
 *                         type: string
 *                       title:
 *                         type: string
 *                 likes:
 *                   type: number
 *                 dislikes:
 *                   type: number
 *                 isActive:
 *                   type: boolean
 *                 isDeleted:
 *                   type: boolean
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *                 updatedAt:
 *                   type: string
 *                   format: date-time
 *       400:
 *         description: Bad request, invalid input
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Invalid input provided"
 *       404:
 *         description: Event not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Event not found"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */
/**
 * @swagger
 * /events/view-members:
 *   get:
 *     tags: [Event]
 *     summary: View members of an event
 *     description: Retrieves a list of members associated with an event.
 *     security:
 *       - bearerAuth: []
 *     produces:
 *       - application/json
 *     responses:
 *       200:
 *         description: Members Retrieved Successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 members:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                       email:
 *                         type: string
 *                       joinedAt:
 *                         type: string
 *                         format: date-time
 *       500:
 *         description: Internal Server Error
 */
