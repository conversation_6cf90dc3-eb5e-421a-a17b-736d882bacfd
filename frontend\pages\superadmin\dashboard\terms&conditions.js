"use client";
import { Plus } from "lucide-react";
import Link from "next/link";
import React from "react";
import { FaRegTrashCan } from "react-icons/fa6";
import { FiEye } from "react-icons/fi";
import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight,
} from "react-icons/md";
import { TfiPencilAlt } from "react-icons/tfi";

const splitTextByWords = (text, wordsPerLine) => {
  const words = text.split(" ");
  const lines = [];
  for (let i = 0; i < words.length; i += wordsPerLine) {
    lines.push(words.slice(i, i + wordsPerLine).join(" "));
  }
  return lines;
};
const TermsConditons = () => {
  const termsData = [
    {
      id: 1,
      questions: (
        <div>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Introduction</span>
          
          
        </div>
      ),

      answers:
        "Welcome to MixDorm! These Terms and Conditions (Terms) govern your use of our website, services, and products provided by MixDorm (we, us, our). By accessing or using our website and services, you agree to comply with and be bound by these Terms. If you do not agree with these Terms, please do not use our services.",
    },
    {
      id: 2,
      questions: (
        <div>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Eligibility</span>
          
        </div>
      ),

      answers:
        "You must be at least 18 years of age to use our services and make a booking.By using our services, you represent and warrant that you meet the age requirementand have the legal capacity to enter into these Terms..",
    },
    {
      id: 3,
      questions: (
        <div>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Reservation</span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">and payments</span>
          
        </div>
      ),

      answers:
        " All reservations are subject to availability and confirmation.Advance payments made at the time of booking are non-refundable.Full prepayment is require check-in.Prices and availability are subject to change without notice.",
    },
    {
      id: 4,
      questions: (
        <div>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Use of Services</span>

        </div>
      ),

      answers:
        "We accept major credit cards, debit cards, and other online payment methods. Payment is processed securely through our payment gateway.",
    },
    {
      id: 5,
      questions: (
        <div>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Check-in and Check-</span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">out</span>
        </div>
      ),

      answers:
        "Yes, once you receive a confirmation email, your booking is guaranteed. We use secure payment methods to ensure your reservation is safe",
    },
    {
      id: 6,
      questions: (
        <div>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Group</span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">bookings</span>
        </div>
      ),

      answers:
        "Yes, you can modify or cancel your booking based on our Cancellation Policy. Please visit our website or contact our customer support team for assistance with changes.",
    },

  ];

  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616] ">
      <div className="flex items-center justify-between w-full ">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Terms and conditions
        </h2>
        <div className="w-[50%] gap-x-5 flex justify-end items-center">
          <Link
            href={"/superadmin/dashboard/terms&conditions-add"}
            className={` px-4 py-2 text-sm font-medium  font-poppins text-white rounded relative flex justify-center items-center bg-sky-blue-650 `}
            type="button"
          >
            <Plus size={18} className="mr-1" /> Terms and conditions
          </Link>
        </div>
      </div>
      <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border dark:border-none">
        <table className="w-full max-w-full divide-y bg-white rounded-xl divide-gray-200 dark:bg-black">
          <thead>
            <tr className="">
              <th className="py-6 bg-white text-left pl-6 text-sm font-semibold font-poppins text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                HEADING
              </th>

              <th className=" py-6 pr-20  bg-white  text-sm font-semibold text-center font-poppins text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                SUBTEXT
              </th>

              <th className="py-6 pl-6  bg-white text-center text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                ACTION
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 border-x border-y text-black/70 dark:border-x-0 dark:text-[#757575]">
            {termsData.map((termsItem) => {
              // const { firstPart, secondPart } = splitMessage(
              //   aboutItem.answers
              // );
              return (
                <tr key={termsItem.id} className="">
                  <td className=" whitespace-nowrap  px-5 text-gray-500 text-sm font-medium font-poppins dark:text-[#757575]">
                    {termsItem.questions}
                  </td>

                  <td className="table-cell sm:table-cell lg:hidden whitespace-nowrap px-5 pl-10 lg:pl-28 py-4 dark:text-[#757575]">
                    {splitTextByWords(termsItem.answers, 6).map(
                      (line, index) => (
                        <p
                          key={index}
                          className="text-base leading-7 text-gray-500 font-medium font-poppins dark:text-[#757575]"
                        >
                          {line}
                        </p>
                      )
                    )}
                  </td>

                  <td className="hidden lg:table-cell whitespace-nowrap px-5 pl-10 lg:pl-28 py-4">
                    {splitTextByWords(termsItem.answers, 12).map(
                      (line, index) => (
                        <p
                          key={index}
                          className="text-sm leading-7 text-gray-500 font-medium font-poppins dark:text-[#757575]"
                        >
                          {line}
                        </p>
                      )
                    )}
                  </td>

                  <td className="py-28 md:py-28 lg:py-14 pl-8 lg:pl-8 px-2 flex justify-end">
                    <Link
                      href={"/superadmin/dashboard/terms&conditions-details"}
                      className=" border p-2 rounded-l-lg text-black/75 hover:text-blue-700 dark:text-[#757575] dark:hover:text-blue-700"
                    >
                      <FiEye />
                    </Link>

                    <Link
                      href={"/superadmin/dashboard/terms&conditions-edit"}
                      className=" border p-2 text-black/75 hover:text-yellow-400 dark:text-[#757575] dark:hover:text-yellow-400"
                    >
                      <TfiPencilAlt />
                    </Link>

                    <button className=" p-2 border rounded-r-lg text-red-600">
                      <FaRegTrashCan />
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
          <div className="flex justify-between items-center mt-5">
            <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
            <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
              <a
                href="#"
                className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
              >
                <span className="sr-only">Next Page</span>
                <MdOutlineKeyboardArrowLeft />
              </a>
    
              <a
                href="#"
                className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
              >
                <span className="sr-only">Next Page</span>
                <MdOutlineKeyboardArrowRight />
              </a>
            </div>
          </div>
    </div>
  );
};

export default TermsConditons;
