import PropertyPayments from "../models/propertyPayments.js"
import Response from '../utills/response.js';
import mongoose from "mongoose";
const generateUniquePaymentId = async (propertyId) => {
  const lastPayment = await PropertyPayments.findOne({ property: propertyId })
    .sort({ createdAt: -1 }) // Get the latest payment for the property
    .select('paymentId');

  let lastNumber = 0;

  if (lastPayment && lastPayment.paymentId) {
    const match = lastPayment.paymentId.match(/#Md-(\d+)/);
    if (match) {
      lastNumber = parseInt(match[1]);
    }
  }

  const newNumber = lastNumber + 1;
  return `#Md-${newNumber}`;
};


export const addPayment = async (req, res) => {
  try {
    const { user, name, property, room, amount, currency, checkIn, checkOut, type } = req.body;

    // Validate required fields
    if (!property || !amount || !currency || !type) {
      return Response.BadRequest(res, null, 'Property, amount, currency, and type are required.');
    }
    const newPaymentId = await generateUniquePaymentId(property);

    // Create a new payment
    const newPayment = new PropertyPayments({
      user,
      name,
      property,
      paymentId: newPaymentId,
      amount,
      currency,
      checkIn,
      checkOut,
      type,
    });

    await newPayment.save();

    return Response.Created(res, newPayment, 'Payment added successfully.');
  } catch (error) {
    console.error('Error adding payment:', error);
    return Response.InternalServerError(res, null, error.message);
  }
};
export const getAllPayments = async (req, res) => {
  try {
    const { propertyId, startDate, endDate } = req.query;

    // Validate propertyId
    if (!propertyId || !mongoose.Types.ObjectId.isValid(propertyId)) {
      return Response.BadRequest(res, null, 'Invalid or missing property ID.');
    }

    // Build filter object
    const filter = { property: propertyId };

    // Optionally add date filters
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) {
        filter.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        filter.createdAt.$lte = new Date(endDate);
      }
    }

    // Fetch payments with the applied filters
    const payments = await PropertyPayments.find(filter, { createdAt: 0, updatedAt: 0 });
    // Calculate totalRevenue, totalReceived, totalPending
    const totalRevenue = payments.reduce((acc, payment) => acc + payment.amount, 0);
    const totalReceived = payments
      .filter(payment => payment.status === 'paid')
      .reduce((acc, payment) => acc + payment.amount, 0);
    const totalPending = payments
      .filter(payment => payment.status === 'pending')
      .reduce((acc, payment) => acc + payment.amount, 0);

    return Response.OK(res, {payments,totalRevenue,totalPending,totalReceived},'Payments retrieved successfully.');
  } catch (error) {
    console.error('Error fetching payments:', error);
    return Response.InternalServerError(res, null, error.message);
  }
};
