import mongoose from 'mongoose';

const planSchema = new mongoose.Schema({
  name: { 
    type: String, 
    required: true 
},                 
  description: { 
    type: String 
},                          
  amount: { 
    type: Number, 
    required: true 
},               
  currency: { 
    type: String, 
    default: 'INR' 
},             
  interval: { 
    type: Number, 
    required: true 
},             
  period: { 
    type: String, 
    required: true 
},              
  created_at: { 
    type: Date, 
    default: Date.now 
},         
});

const plan = mongoose.model('Plan', planSchema);

export default plan;


