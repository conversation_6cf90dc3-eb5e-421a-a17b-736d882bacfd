// config/passport.js
import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth2';
import { Strategy as FacebookStrategy } from 'passport-facebook';
import { Strategy as AppleStrategy } from 'passport-apple';
import { Strategy as JwtStrategy, ExtractJwt } from 'passport-jwt';
import jwt from 'jsonwebtoken';
import User from '../models/User.js';
import dotenv from 'dotenv';
dotenv.config();

const generateToken = (user) => jwt.sign({ id: user._id, role: user.role }, 'your_jwt_secret', { expiresIn: '1h' });

passport.use(new GoogleStrategy({
  clientID: process.env.CLIENT_ID,
  clientSecret: process.env.CLIENT_SECRET,
  callbackURL: 'http://localhost:3000/socialAuth/google/callback',
  scope: ['email', 'profile']
}, async (accessToken, refreshToken, profile, done) => {
  const { id, displayName, emails } = profile;
  try {
    let user = await User.findOne({ email: emails[0].value });
    if (!user) {
      user = new User({
        name : {
          first : displayName
        },
        email: emails[0].value
      });
      await user.save();
    }
    const token = generateToken(user);
    return done(null, { user, token });
  } catch (error) {
    return done(error, false);
  }
}));

const opts = {
  jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
  secretOrKey: 'your_jwt_secret',
};

passport.use(new JwtStrategy(opts, async (jwt_payload, done) => {
  try {
    const user = await User.findById(jwt_payload.id);
    if (user) {
      return done(null, user);
    }
    return done(null, false);
  } catch (error) {
    return done(error, false);
  }
}));


passport.use(new FacebookStrategy({
  clientID: process.env.CLIENT_ID,
  clientSecret: process.env.CLIENT_SECRET,
  callbackURL: 'http://localhost:3000/socialAuth/facebook/callback',
  profileFields: ['id', 'displayName', 'emails']
}, async (accessToken, refreshToken, profile, done) => {
  const { id, displayName, emails } = profile;
  try {
    let user = await User.findOne({ email: emails[0].value });
    if (!user) {
      user = new User({
        name : {
          first : displayName
        },
        email: emails[0].value
      });
      await user.save();
    }
    const token = generateToken(user);
    return done(null, { user, token });
  } catch (error) {
    return done(error, false);
  }
}));

passport.use(new AppleStrategy({
  clientID: 'YOUR_APPLE_CLIENT_ID',
  teamID: 'YOUR_APPLE_TEAM_ID',
  callbackURL: 'http://localhost:3000/socialAuth/apple/callback',
  keyID: 'YOUR_APPLE_KEY_ID',
  privateKeyString: 'YOUR_APPLE_PRIVATE_KEY_STRING',
  passReqToCallback: true,
}, async (req, accessToken, refreshToken, idToken, profile, done) => {
  const { id, displayName, emails } = profile;
  try {
    let user = await User.findOne({ email: emails[0].value });
    if (!user) {
      user = new User({
        name : {
          first : displayName
        },
        email: emails[0].value
      });
      await user.save();
    }
    const token = generateToken(user);
    return done(null, { user, token });
  } catch (error) {
    return done(error, false);
  }
}));