import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import { CiLocationOn } from "react-icons/ci";
import { LuCalendarDays } from "react-icons/lu";
import dynamic from "next/dynamic";
import { getMyRideApi } from "@/services/webflowServices";

const CustomPagination = dynamic(
  () => import("../customPagination/customPagination"),
  {
    ssr: false,
  }
);


const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});
const Bookingdetailspopup = dynamic(
  () => import("../model/bookingdetailspopup"),
  { ssr: false }
);
const Cancelpopup = dynamic(() => import("../model/cancelpopup"), {
  ssr: false,
});

const MyRide = () => {
  const [rideData, setRideData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const limit = 10;
  const isFirstRender = useRef(null);
  const [openBookingdetailspopup, setopenBookingdetailspopup] = useState(false);
  const handleCloseBookingdetailspopup = () =>
    setopenBookingdetailspopup(false);
  const [openCancelpopup, setopenCancelpopup] = useState(false);
  const handleCloseCancelpopup = () => setopenCancelpopup(false);

  useEffect(() => {
    const fetchMyRideData = async () => {
      setLoading(true);
      try {
        const response = await getMyRideApi(currentPage, limit);
        setRideData(response?.data?.data || []);
        setTotalPages(response?.data?.pagination?.totalPages || 1);
      } catch (error) {
        console.error("Error fetching stay data:", error);
      } finally {
        setLoading(false);
      }
    };
    if (!isFirstRender.current) {
      fetchMyRideData();
    } else {
      isFirstRender.current = false;
    }
  }, [currentPage]);

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    });
  };

  return (
    <>
      <Loader open={loading} />
      <h2 className="text-[#40E0D0] flex gap-1 text-2xl font-bold mb-6">
        My
        <span className="text-black ml-0.5"> Ride</span>
      </h2>

      <div className="flex flex-col gap-5">
        {rideData?.length > 0
          ? rideData?.map((data, index) => (
              <div
                key={index}
                className="flex items-center border rounded-3xl relative overflow-hidden"
              >
                <Image
                  src={`${data?.photos?.[0]?.url}`}
                  alt="stay"
                  className="object-cover"
                  width={317}
                  height={204}
                />

                <div className="py-2 px-4">
                  <h2 className="text-xl font-bold mb-3">
                    {data?.name || "Hostel Musooriee"}{" "}
                  </h2>
                  <p className="flex items-center gap-1 text-sm mb-2">
                    <CiLocationOn className="text-[#40E0D0] " />
                    With {data?.passenger} People
                  </p>
                  <p className="flex items-center gap-1 text-sm mb-2">
                    <CiLocationOn className="text-[#40E0D0] " />
                    {data?.destination?.address ||
                      "Laxman Jhula, Rishikesh UK india"}
                  </p>
                  <p className="flex items-center gap-1 text-sm mb-5">
                    <LuCalendarDays className="text-[#40E0D0] " />
                    {data?.date ? formatDate(data.date) : "12 Sep 2024"}
                  </p>
                </div>
                <div className="ml-auto pr-1 grid gap-1">
                  {/* <button
                    className="bg-white text-primary-blue text-sm font-bold text-center"
                    onClick={handleOpenBookingdetailspopup}
                  >
                    View Booking
                  </button>
                  <button
                    className="bg-red-600 text-white px-4 py-2 rounded-full text-sm"
                    onClick={handleOpenCancelpopup}
                  >
                    Cancel
                  </button> */}
                  <button className="bg-black text-white px-4 py-2 rounded-full text-sm">
                    Leave Review
                  </button>
                </div>
              </div>
            ))
          : !loading && <p>No Rides available.</p>}
      </div>
      {totalPages > 1 && (
        <div className="flex justify-center mt-4">
          <CustomPagination
            currentPage={currentPage}
            total={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
      <Bookingdetailspopup
        openBookingdetailspopup={openBookingdetailspopup}
        handleCloseBookingdetailspopup={handleCloseBookingdetailspopup}
      />
      <Cancelpopup
        openCancelpopup={openCancelpopup}
        handleCloseCancelpopup={handleCloseCancelpopup}
      />
    </>
  );
};

export default MyRide;
