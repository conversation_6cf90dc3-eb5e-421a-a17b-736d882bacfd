import mongoose from 'mongoose';
import slugify from 'slugify'; // You must install this using: npm install slugify

const blogSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
      unique: true,
    },
    slug: {
      type: String,
      unique: true,
    },
    categoryId: {
      type: mongoose.Types.ObjectId,
      ref: "blogsCategories",
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: "users",
    },
    description: {
      type: String,
    },
    images: [{
      type: String,
    }],
    videoLink: {
      type: String,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    keywords: {
      type: Array,
    },
    html:{
      type:String
    }
  },
  {
    timestamps: true,
  },
);

// Pre-save hook to generate slug from title
blogSchema.pre('save', async function (next) {
  if (this.isModified('title') || !this.slug) {
    this.slug = slugify(this.title, { lower: true, strict: true });
  }
  next();
});

const blogModel = mongoose.model('blogs', blogSchema);

export default blogModel;
