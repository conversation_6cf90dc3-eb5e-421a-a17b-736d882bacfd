import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import {  Autoplay } from "swiper/modules";
import HostelCard from "./exploreHostelCard";

const HostelCardSlider = () => {
  const data = [
    {
      id: 1,
      tag: "Top Rated",
      title: "Amsterdam, Netherlands",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/cultural.jpg`,
      guest: "4-6 guest",
      time: "2 days 3 nights",
      price: "$48.25",
      rating: "4.9",
      review: "672",
      feature: "Pool, rooftop bar, and organized local tours.",
    },
    {
      id: 2,
      tag: "Top Rated",
      title: "Amsterdam, Netherlands",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/cultural.jpg`,
      guest: "4-6 guest",
      time: "2 days 3 nights",
      price: "$48.25",
      rating: "4.9",
      review: "672",
      feature: "Pool, rooftop bar, and organized local tours.",
    },
    {
      id: 3,
      tag: "Top Rated",
      title: "Amsterdam, Netherlands",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/cultural.jpg`,
      guest: "4-6 guest",
      time: "2 days 3 nights",
      price: "$48.25",
      rating: "4.9",
      review: "672",
      feature: "Pool, rooftop bar, and organized local tours.",
    },
    {
      id: 4,
      tag: "Top Rated",
      title: "Amsterdam, Netherlands",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/cultural.jpg`,
      guest: "4-6 guest",
      time: "2 days 3 nights",
      price: "$48.25",
      rating: "4.9",
      review: "672",
      feature: "Pool, rooftop bar, and organized local tours.",
    },
    {
      id: 5,
      tag: "Top Rated",
      title: "Amsterdam, Netherlands",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/cultural.jpg`,
      guest: "4-6 guest",
      time: "2 days 3 nights",
      price: "$48.25",
      rating: "4.9",
      review: "672",
      feature: "Pool, rooftop bar, and organized local tours.",
    },
  ];
  return (
    <>
      <Swiper
        modules={[Autoplay]}
        autoplay={{ delay: 1500 }}
        slidesPerView={18}
        loop
        speed={1000}
        spaceBetween={14}
        className="mySwiper"
        breakpoints={{
          0: {
            slidesPerView: 1.2,
          },
          480: {
            slidesPerView: 1.4,
          },
          640: {
            slidesPerView: 2,
          },
          768: {
            slidesPerView: 3,
          },
          1100: {
            slidesPerView: 4,
          },
        }}
      >
        {data.map((item) => (
          <SwiperSlide key={item.id}>
            <HostelCard
              tag={item.tag}
              title={item.title}
              image={item.image}
              guest={item.guest}
              time={item.time}
              feature={item.feature}
              price={item.price}
              rating={item.rating}
              review={item.review}
            />
          </SwiperSlide>
        ))}
      </Swiper>
    </>
  );
};

export default HostelCardSlider;
