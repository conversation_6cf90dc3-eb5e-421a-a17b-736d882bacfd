import Response from "../utills/response.js";
import cloudsModel from "../models/clouds.js";
import axios from 'axios';
import https from 'https';
import rateTypesModel from "../models/ratePlans.js";
import roomTypesModel from "../models/roomTypes.js";
import property from "../models/properties.js";
import newRoomModel from "../models/newRooms.js";
import roomModel from "../models/room.js";
import RatePlan from "../models/roomRatePlans.js";
import { getConversionRates } from "../services/otaProperties.js";
import avability from "../models/avability.js";
import ARI from "../models/ARI.js";

export const cloudAdd = async (req, res) => {
    try {
        console.log("data received from cloud..", req.body)

        await cloudsModel.create({ resp: req.body, url: req.url, finalResponse: req.body })
        const propertyExist = await property.findOneAndUpdate(
            {
                $or: [
                    { otaId: req.body?.ota_property_id },
                    { name: req.body?.Property?.name }
                ]
            },
            {
                isAddByCloudbeds: true,
                cloudbedsId: req.body?.mya_property_id,
                isPropertyLive: true
            },
        );

        return res.status(200).json({
            success: true,
            data: req.body,
            message: "Success"
        })
        // return Response.Created(res, req.body, 'Success');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

export const cloudGet = async (req, res) => {
    try {
        console.log("cloud data received", req.query)
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};


export const cloudRedirect = async (req, res) => {
    try {
        console.log("Data received from cloud:", req.body);

        // Save the incoming request data to the database
        await cloudsModel.create({ resp: req.body, url: req.url });

        // Extract the verb and determine the target URL
        const { verb } = req.body;

        if (!verb) {
            return Response.BadRequest(res, null, 'Verb is missing in the request body');
        }

        // Define the target API endpoint
        const apiUrl = `https://dev-api.mixdorm.com/clouds/${verb}`;
        const url = new URL(apiUrl);

        // Determine protocol (http or https)
        const protocol = url.protocol === 'https:' ? https : http;

        // Prepare options for the request
        const options = {
            hostname: url.hostname,
            path: url.pathname,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(JSON.stringify(req.body)),
            },
        };

        // Create the request using the appropriate protocol
        const apiRequest = protocol.request(options, (apiResponse) => {
            let responseData = '';

            // Collect the API response data
            apiResponse.on('data', (chunk) => {
                responseData += chunk;
            });

            apiResponse.on('end', () => {
                try {
                    // Attempt to parse the response as JSON
                    let parsedData;
                    try {
                        parsedData = JSON.parse(responseData);
                        console.log("parsedData", parsedData)
                        console.log("apiResponse", apiResponse)
                    } catch (parseError) {
                        console.warn('Non-JSON response received:', responseData);
                        parsedData = {
                            status: apiResponse.statusCode === 200,
                            data: apiResponse.data,
                            message: responseData.trim(),
                        };
                    }

                    // Transform the response to the desired format
                    const transformedData = {
                        success: parsedData.status,
                        data: parsedData,
                        message: parsedData.message,
                    };

                    // Send the transformed response
                    return res.status(apiResponse.statusCode).json(transformedData);
                } catch (error) {
                    console.error('Error processing API response:', error);
                    return Response.InternalServerError(res, null, error.message);
                }
            });
        });

        // Handle request errors
        apiRequest.on('error', (error) => {
            console.error('Error during API redirection:', error);
            return Response.InternalServerError(res, null, error.message);
        });

        // Write the request body and end the request
        apiRequest.write(JSON.stringify(req.body));
        apiRequest.end();
    } catch (error) {
        console.error('Unexpected error in cloudRedirect:', error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};

export const createProperty = async (req, res) => {
    try {
        console.log("data received from cloud..", req.body)
        console.log("req.body?.Property?.name", req.body?.Property?.name)
        const propertyExist = await property.findOneAndUpdate(
            { name: req.body?.Property?.name },
            { isAddByCloudbeds: true, cloudbedsId: req.body?.mya_property_id },
            { upsert: false, new: true }
        );
        if (!propertyExist) {
            const newProperty = new property({
                name: req.body?.Property?.name,
                address: {
                    lineOne: req.body?.Property?.address?.address_line_1,
                    lineTwo: req.body?.Property?.address?.address_line_2,
                    city: req.body?.Property?.address?.city,
                    state: req.body?.Property?.address?.state,
                    country: req.body?.Property?.address?.country,
                    zipcode: req.body?.Property?.address?.zip,
                },
                contact: req.body?.Property?.address?.phone
                    ? Number(req.body.Property.address.phone.replace(/\D/g, ''))
                    : undefined,
                contactUs: {
                    email: req.body?.Property?.email_default,
                    contactAddress: {
                        lineOne: req.body?.Property?.business_contact?.address_line_1,
                        lineTwo: req.body?.Property?.business_contact?.address_line_2,
                        city: req.body?.Property?.business_contact?.city,
                        state: req.body?.Property?.business_contact?.state,
                        country: req.body?.Property?.business_contact?.country,
                        zipcode: req.body?.Property?.business_contact?.zip,
                    },
                    phoneNumber: req.body?.Property?.address?.phone,
                },
                photos: (req.body?.Property?.images || []).map(img => ({
                    filename: img.filename,
                    path: img.path,
                    objectURL: img.objectURL,
                    url: img.url,
                })),
                isAddByCloudbeds: true,
                cloudbedsId: req.body?.mya_property_id
            });

            const createdProperty = await newProperty.save();
            if (req.body?.Property?.rooms?.length > 0) {
                const propertyId = createdProperty._id;


                for (const room of req.body.Property.rooms) {
                    // Get the current highest booking number (optional: filter by day or other logic)
                    const lastRoom = await newRoomModel.findOne({})
                        .sort({ createdAt: -1 })
                        .select('room_id') // Only fetch bookingId
                        .lean();

                    let nextNumber = 1001;

                    if (lastRoom && lastRoom.room_id) {
                        const match = lastRoom.room_id.match(/rm(\d+)/);
                        if (match) {
                            nextNumber = parseInt(match[1]) + 1;
                        }
                    }

                    const room_id = `rm${nextNumber}`;

                    let avaliable_units = room?.units;
                    if (room?.dormitory) {
                        avaliable_units = room?.beds * room?.units;
                    }
                    await newRoomModel.findOneAndUpdate(
                        { cloud_beds_room_id: room?.mya_room_id, property: propertyId },
                        {
                            property: propertyId,
                            name: room?.label,
                            dormitory: room?.dormitory,
                            description: room?.description || '',
                            cloud_beds_room_id: room?.mya_room_id,
                            beds: room?.beds,
                            units: room?.units,
                            gender: room?.gender,
                            room_id,
                            images: (room?.images || []).map(img => ({
                                filename: null,
                                path: null,
                                objectURL: null,
                                url: img.url,
                                description: img?.description
                            })),
                            ratePlans: (room?.rateplans || []).map(plan => ({
                                myaRateId: plan?.mya_rate_id,
                                labelPublic: plan?.label_public,
                                labelPrivate: plan?.label_private
                            })),
                            avaliable_units,
                            is_add_by_cloudbeds: true
                        },
                        { upsert: true, new: true }
                    );
                }
            }


        }
        const response = {
            "success": true,
            "ota_property_id": req.body.ota_property_id,
            "ota_property_password": req.body.ota_property_password,
            "instruction_text": "It worked!\n\nNow click the link.",
            "instruction_link": "https://mixdorm.com/cloudbeds/channel-connection",
            // "room_mappings": [
            //   {
            //     "ota_room_id": "11111",
            //     "mya_room_id": 45829,
            //     "ota_rate_id": "rp6623",
            //     "mya_rate_id": 0
            //   },
            //   {
            //     "ota_room_id": "11111",
            //     "mya_room_id": 45829,
            //     "ota_rate_id": "rp6624",
            //     "mya_rate_id": 850
            //   },
            //   {
            //     "ota_room_id": "11111",
            //     "mya_room_id": 45829,
            //     "ota_rate_id": "rp6624",
            //     "mya_rate_id": 851
            //   },
            //   {
            //     "ota_room_id": "22222",
            //     "mya_room_id": 290,
            //     "ota_rate_id": "rp6625",
            //     "mya_rate_id": 0
            //   },
            //   {
            //     "ota_room_id": "22222",
            //     "mya_room_id": 290,
            //     "ota_rate_id": "rp6626",
            //     "mya_rate_id": 853
            //   }
            // ]
        }
        await cloudsModel.create({ resp: req.body, url: req.url, finalResponse: response })
        return res.status(200).json(response)
        // return Response.Created(res, req.body, 'Success');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};
export const getRoomType = async (req, res) => {
    try {
        console.log("data received from cloud..", req.body)
        const propertyId = await property.findOne({ otaId: req.body?.ota_property_id });
        // 2. Get rooms of the property
        const rooms = await roomModel.find({ property: propertyId._id });
        // 3. Format response
        const formattedRooms = rooms.map(room => ({
            ota_room_id: room.roomId || room._id, // fallback to _id if otaRoomId not set
            title: room.name,
            occupancy: room.capacity || 1,
            dorm: !!room.dormitory,
        }));
        const response = {
            "success": true,
            "Rooms": formattedRooms
        }
        // const response = {
        //     "success": true,
        //     "Rooms": [
        //         {
        //             "ota_room_id": 138120,
        //             "title": "Private Standard Room",
        //             "occupancy": 2,
        //             "dorm": false
        //         },
        //         {
        //             "ota_room_id": 138124,
        //             "title": "6-bed shared dorm",
        //             "occupancy": 4,
        //             "dorm": true
        //         },
        //         {
        //             "ota_room_id": 138104,
        //             "title": "8 Bed Female Dorm",
        //             "occupancy": 8,
        //             "dorm": false
        //         }
        //     ]
        // }
        await cloudsModel.create({ resp: req.body, url: req.url, finalResponse: response })
        return res.status(200).json(response)
        // return Response.Created(res, req.body, 'Success');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};
export const getRatePlan = async (req, res) => {
    try {
        console.log("Fetching rate plans from database...");
        // Step 1: Get all rooms belonging to the property
        const propertyId = await property.findOne({ otaId: req.body?.ota_property_id });
        const rooms = await roomModel.find({ property: propertyId._id });

        // 2. Get rooms of the property
        const roomIds = rooms.map(room => room._id);
        // Step 2: Get all rate plans for those rooms
        const ratePlans = await RatePlan.find({ room: { $in: roomIds } }).populate('room');

        // const ratePlans = await RatePlan.find({}).populate('room'); // populate room details if needed
        const response = {
            success: true,
            RatePlans: ratePlans.map(rate => ({
                ota_rate_id: rate.otaRateId,
                title: rate.name,
                ota_room_id: rate.room?.otaRoomId || null, // from ObjectId in 'room' field
            })),
        };

        // // Fetch active rate plans and room types
        // const ratePlans = await rateTypesModel.find({ isActive: true, isDeleted: false });
        // const roomTypes = await roomTypesModel.find({ isActive: true, isDeleted: false });

        // // Check if data exists
        // if (!ratePlans.length || !roomTypes.length) {
        //     return res.status(404).json({ success: false, message: "No rate plans or room types found" });
        // }

        // Map data to response format
        // const response = {
        //     success: true,
        //     RatePlans: ratePlans.map(rate => ({
        //         ota_rate_id: rate.RateTypeId,
        //         title: rate.RateType,
        //         ota_room_id: roomTypes.length > 0 ? roomTypes[0].RoomTypeId : null, // Assign first roomTypeId (modify as needed)
        //     }))
        // };
        await cloudsModel.create({ resp: req.body, url: req.url, finalResponse: response })

        return res.status(200).json(response);
    } catch (error) {
        console.error("Error fetching rate plans:", error);
        return res.status(500).json({ success: false, message: "Internal Server Error", error: error.message });
    }
};

export const ARIUpdate = async (req, res) => {
    try {
        console.log("data received from cloud..", req.body);

        // Save raw request for logs/troubleshooting
        await cloudsModel.create({ resp: req.body, url: req.url });

        const inventories = req.body.Inventory || [];
        const fromCurrency = req.body.currency || "IDR";
        const conversionRate = await getConversionRates(fromCurrency, "EUR");

        for (const item of inventories) {
            const {
                ota_room_id,
                start_date,
                end_date,
                rate,
                units,
                close,
                closearr,
                closedep,
                min_los,
                max_los,
                min_advanced_offset,
                max_advanced_offset,
                num_adults_included,
                num_children_included
            } = item;

            // Find matching room
            const roomDoc = await roomModel.findOne({ roomId: ota_room_id });
            if (!roomDoc) {
                console.warn(`Room not found for otaRoomId: ${ota_room_id}`);
                continue;
            }

            const rateInEUR = parseFloat(rate || 0) / conversionRate;
            const availableCount = parseInt(units) || 0;

            const start = new Date(start_date);
            const end = new Date(end_date);

            for (let dt = new Date(start); dt <= end; dt.setDate(dt.getDate() + 1)) {
                const currentDate = new Date(dt);

                // Update RoomInventory
                await ARI.updateOne(
                    {
                        roomId: roomId,
                        date: currentDate
                    },
                    {
                        $set: {
                            propertyId: roomDoc.property,
                            currency,
                            rate,
                            // Convert to EUR
                            baseRate: (() => {
                                const conversionRate = currencyRates.rates?.[currency];
                                if (!conversionRate || isNaN(conversionRate)) {
                                    console.warn(`No valid conversion rate found for currency: ${currency}`);
                                    return null; // or 0 if you want default
                                }
                                // Since currencyRates.base is EUR, divide to get EUR value
                                return parseFloat((rate / conversionRate).toFixed(2));
                            })(),
                            availableUnits: availableCount,
                            close: item.close || false,
                            closeArrival: item.closearr || false,
                            closeDeparture: item.closedep || false,
                            minLos: item.min_los || 1,
                            maxLos: item.max_los || 0,
                            minAdvanceOffset: item.min_advanced_offset || 0,
                            maxAdvanceOffset: item.max_advanced_offset || 0,
                            numAdultsIncluded: item.num_adults_included || 1,
                            numChildrenIncluded: item.num_children_included || 0,
                            updatedAt: new Date()
                        }
                    },
                    { upsert: true }
                );

                // Update RoomAvailability
                await avability.updateOne(
                    {
                        roomId: ota_room_id,
                        date: currentDate
                    },
                    {
                        $set: {
                            propertyId: roomDoc.property,
                            availableUnits: availableCount
                        }
                    },
                    { upsert: true }
                );
            }
        }

        return res.status(200).json({
            success: true,
            message: "ARI updates processed and stored successfully"
        });
    } catch (error) {
        console.error("ARI update error", error);
        return Response.InternalServerError(res, null, error.message);
    }
};

export const getBookingList = async (req, res) => {
    try {
        console.log("data received from cloud..", req.body)
        const response =
        {
            "success": true,
            "Bookings": [
                {
                    "booking_id": "85357-387039116-47307",
                    "version": "1"
                }
            ]
        }
        await cloudsModel.create({ resp: req.body, url: req.url, finalResponse: response })
        return res.status(200).json(response)
        // return Response.Created(res, req.body, 'Success');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};
export const GetBookingId = async (req, res) => {
    try {
        console.log("Data received from cloud:", req.body);

        // Return exactly the required response structure.
        const response = {
            success: true,
            Booking: {
                OrderId: "85357-387039116-47307",
                OrderDate: "2025-03-07",
                OrderTime: "11:29:58",
                IsCancellation: 1,
                TotalCurrency: "INR",
                TotalPrice: 501.00,
                IsModification: 1,
                Customers: [
                    {
                        CustomerCountry: "IN",
                        CustomerEmail: "<EMAIL>",
                        CustomerFName: "Priyanka",
                        CustomerLName: "Patel"
                    }
                ],
                Rooms: [
                    {
                        ChannelRoomType: 138124,
                        Currency: "INR",
                        DayRates: [
                            {
                                Date: "2025-03-11",
                                Description: "Refundable Rate",
                                Rate: 250.50,
                                Currency: "INR",
                                RateId: "13649"
                            }
                        ],
                        StartDate: "2025-03-11",
                        EndDate: "2025-03-11",
                        Price: 501.00,
                        Units: 2
                    }
                ]
            }
        };
        await cloudsModel.create({ resp: req.body, url: req.url, finalResponse: response })
        return res.status(200).json(response);
    } catch (error) {
        console.error("Error in GetBookingId:", error);
        return res.status(500).json({ success: false, error: error.message });
    }
};