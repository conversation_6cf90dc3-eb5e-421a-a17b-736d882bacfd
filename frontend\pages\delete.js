import React from "react";

const Delete = () => {
  return (
    <>
      <div className="bg-white md:pb-16 font-manrope md:pt-12 py-8 px-8">
        <h2 className="text-black flex gap-1 text-2xl font-bold mb-6 justify-center">
          Delete or Deactivate your account
        </h2>
        <div className="px-4 py-3">
          <div className="max-w-md mx-auto p-4 bg-white shadow-md border border-gray-200 rounded-lg space-y-4">
            <div className="flex items-start space-x-3">
              <input
                type="radio"
                name="accountOption"
                id="deactivate"
                className="mt-1 w-5 h-5 text-blue-600 border-gray-300 focus:ring-blue-500"
                checked
              />
              <div>
                <label
                  htmlFor="deactivate"
                  className="block text-lg font-semibold text-gray-900"
                >
                  Deactivate account
                </label>
                <p className="text-sm text-gray-700">
                  Deactivating your account is <strong>temporary</strong>, and
                  it means your profile will be hidden on MixDorm until you
                  reactivate it through Accounts Center or by logging in to your
                  MixDorm account.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <input
                type="radio"
                name="accountOption"
                id="delete"
                className="mt-1 w-5 h-5 text-blue-600 border-gray-300 focus:ring-blue-500"
              />
              <div>
                <label
                  htmlFor="delete"
                  className="block text-lg font-semibold text-gray-900"
                >
                  Delete account
                </label>
                <p className="text-sm text-gray-700">
                  Deleting your account is <strong>permanent</strong>. When you
                  delete your MixDorm account, your profile will be permanently
                  removed. If you’d just like to take a break, you can
                  temporarily deactivate your account.
                </p>
              </div>
             </div>
            <div>
              <input
                type="text"
                className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
                placeholder="Write reason"
              />
              </div>
            <div className="flex gap-1 justify-end">
              <button className="bg-black text-white px-4 py-2 rounded-full text-sm hover:bg-primary-blue">
                Yes
              </button>
              <button className="bg-tranparent border border-black text-black px-4 py-2 rounded-full text-sm hover:bg-red-600 hover:text-white hover:border-red-600">
                No
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Delete;
