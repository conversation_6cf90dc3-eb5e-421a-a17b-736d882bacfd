/* eslint-disable react/display-name */
import React , {useCallback, useEffect, useState } from "react";
import { Building2 } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { getItemLocalStorage } from "@/utils/browserSetting";
import toast from "react-hot-toast";
import { likeUnlikePropertyApi } from "@/services/webflowServices";
import { FaWifi, FaMapMarkedAlt, FaBed, FaDumbbell } from "react-icons/fa"; // Import relevant icons
import { FaGlobe, FaHeart, FaStar } from "react-icons/fa6";
import { FaCarAlt } from "react-icons/fa"; // For parking and towels
import { PiForkKnifeBold, PiTowel } from "react-icons/pi";
import { TbAirConditioning } from "react-icons/tb";
import { MdOutlineElevator, MdPool } from "react-icons/md";
// import { RxMix } from "react-icons/rx";

const HostelCard = ({
  tag,
  title,
  image,
  feature,
  price,
  hostelId,
  setIsUpdateData,
  setLoading,
  liked,
}) => {
  const [localLiked, setLocalLiked] = useState(liked);

  useEffect(() => {
    setLocalLiked(liked);
  }, [liked]);

  const getImageUrl = (url) => {
    return url && url.startsWith("http") ? url : `https://${url}`;
  };

  const HandleLike = async () => {
    if (getItemLocalStorage("token")) {
      setLoading(true);
      try {
        const payload = {
          isLike: !liked,
        };
        const response = await likeUnlikePropertyApi(hostelId, payload);
        console.log("Like/Unlike response:", response);
        
        // Handle both like and unlike response structures
        if (response?.data?.data || response?.data) {
          // Update the UI directly with the response data
          const updatedData = {
            ...(response?.data?.data || response?.data),
            liked: response?.data?.data?.liked || response?.data?.liked // Use the opposite of current liked state
          };
          console.log("Setting update data:", updatedData);
          setIsUpdateData(prevState => ({
            ...prevState,
            [hostelId]: updatedData
          }));
        }
      } catch (error) {
        console.error("Error fetching stay data:", error);
        toast.error("Failed to update like status");
      } finally {
        setLoading(false);
      }
    } else {
      toast.error("Please login first!!");
    }
  };



  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageFailed, setImageFailed] = useState(false);

  useEffect(() => {
      if (!image) return;

      const preload = new window.Image();
      preload.src = getImageUrl(image);

      preload.onload = () => {
        setImageLoaded(true);
      };

      // Optional: fallback after 3 seconds if image fails silently
      const timeout = setTimeout(() => {
        setImageLoaded(true);
      }, 3000);

      return () => clearTimeout(timeout);
    }, [image]);
      // const showContent = imageLoaded;

  const iconClass = "text-gray-800 text-xl h-[16px] w-[16px]";

  const facilityIcons = {
    FREEWIFI: <FaWifi className={iconClass} />,
    FREECITYMAPS: (
      <FaMapMarkedAlt className={iconClass} />
    ),
    LINENINCLUDED: (
      <FaBed className={iconClass} />
    ),
    FREEINTERNETACCESS: (
      <FaGlobe className={iconClass} />
    ),
    BREAKFASTINCLUDED: (
      <PiForkKnifeBold className={iconClass} />
    ),
    FREEPARKING: (
      <FaCarAlt className={iconClass} />
    ),
    TOWELSINCLUDED: (
      <PiTowel className={iconClass} />
    ),
    FREECITYTOUR: (
      <Building2 className={iconClass} />
    ),
    PARKING: <FaCarAlt className={iconClass} />,
    AIRCONDITIONING: (
      <TbAirConditioning className="text-gray-800 text-xl  " />
    ),
    SWIMMINGPOOL: (
      <MdPool className="text-gray-800 text-xl " />
    ),
    ELEVATOR: <MdOutlineElevator className="text-gray-800 text-xl " />,
    FITNESSCENTRE: (
      <FaDumbbell className="text-gray-800 text-xl " />
    ),
 
  };

  const FacilityIcon = React.memo(({ id }) => {
    return facilityIcons[id] || facilityIcons["FREEWIFI"];
  });

  const [showTooltip, setShowTooltip] = useState(false);

  const handleMouseEnter = useCallback(() => setShowTooltip(true), []);
  const handleMouseLeave = useCallback(() => setShowTooltip(false), []);


  return (
    <>

    <div className="w-full h-full border-4 border-slate-105 font-manrope overflow-hidden bg-white shadow-md lg:hover:shadow-xl lg:hover:scale-105 lg:hover:-translate-y-1 lg:transition-all lg:duration-300">
      <div className="relative w-full tracking-normal">
        <Link href={`/hostels-detail/${hostelId}`} prefetch={false}>
          {!imageLoaded || imageFailed ? (
            <div className="w-full xs:h-[200px] h-[150px] bg-slate-200 animate-pulse flex justify-center items-center">
              <h1 className="text-white font-bold font-manrope">MixDorm</h1>  
            </div>
          ) : (
            <Image
              src={getImageUrl(image)}
              alt={title}
              title={title}
              width={200}
              height={150}
              quality={90}
              loading="lazy"
              sizes="(max-width: 480px) 100vw, (max-width: 768px) 50vw, 200px"
              className="w-full xs:h-[200px] h-[150px] object-cover transition-opacity duration-500"
              onError={() => setImageFailed(true)}
            />
          )}
        </Link>

        {/* Tag */}
        {imageLoaded   && (
          <div className="absolute flex items-center justify-center px-3 py-1 text-xs font-semibold text-black bg-white rounded-4xl xs:top-5 top-3 xs:left-5 left-3 font-manrope">
            {tag}
          </div>
        )}

        {/* Like button */}
        {imageLoaded  && (
          <button
            type="button"
            aria-label="Like UnLike"
            onClick={HandleLike}
            className={`absolute flex items-center justify-center p-1 rounded-full xs:w-7 xs:h-7 w-6 h-6 xs:top-5 top-3 xs:right-5 right-3 font-manrope ${
              localLiked ? "bg-white text-red-600" : "text-black bg-white"
            } hover:text-red-600 hover:bg-white`}
          >
            <FaHeart size={18} />
          </button>
        )}
      </div>
      {imageLoaded  ? (
        <div className="xl:p-4 xs:px-4 px-2 pb-4 pt-4 tracking-normal bg-white flex flex-col justify-between relative gap-1">
          <div className="flex flex-col justify-between gap-1">
            <h2 className=" leading-4">
              <Link
                href={`/hostels-detail/${hostelId}`}
                className=" sm:text-[18px] xs:text-[16px] text-[14px] xs:min-h-[48px] min-h-[40px] font-manrope xs:leading-6 leading-5 font-bold cursor-pointer text-black duration-300 ease-in-out hover:text-[#40E0D0] line-clamp-2 mt-2"
                prefetch={false}
              >
                {title}
              </Link>
            </h2>
            <div className="flex flex-wrap gap-3 font-manrope text-[#737373]">

              <div className="flex items-center text-sm gap-x-2 pt-1">
                <span className="xs:text-[14px] text-xs font-manrope font-medium cursor-pointer text-gray duration-300 ease-in-out">
                  Key Features:{" "}
                </span>
                <div className="flex items-center justify-start gap-x-2">
                  {feature.map((facility) => (
                    <div
                      key={facility.id}
                      className="relative group  flex items-center justify-center"
                      onMouseEnter={handleMouseEnter}
                      onMouseLeave={handleMouseLeave}
                    >
                      <FacilityIcon id={facility.id} />
                      {showTooltip && (
                      <span className="absolute bottom-6 left-[-100%] transform  text-xs text-white bg-black px-2 py-1 rounded opacity-0 hidden min-w-[max-content] max-w-[max-content]  group-hover:opacity-100 group-hover:block duration-200">
                        {facility.name}
                      </span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <div className="absolute -top-4 z-10 ">
              <span className="text-xs font-bold flex items-center bg-gray-100 px-3 py-1.5 rounded-3xl shadow-lg border">
                <FaStar className="text-yellow-400 mr-1" />
                4.5
                <span className="text-[#888888] font-normal ml-1">Reviews</span>
              </span>
            </div>
          </div>

          <div className="sm:block items-center justify-between">
            <div className="flex items-center font-bold text-black text-sm lg:text-sm 2xl:text-lg gap-1 font-manrope">
              <Link
                href={`/hostels-detail/${hostelId}`}
                prefetch={false}
                className="hover:text-[#40E0D0] group"
              >
                {price}
                <span className="text-xs text-[#737373] font-normal ml-1 group-hover:text-[#40E0D0]">
                  / Night
                </span>
              </Link>

              <div className="ml-auto">
                <Link
                  href={`/hostels-detail/${hostelId}`}
                  prefetch={false}
                  className="bg-black flex items-center hover:bg-[#40E0D0] text-white font-semibold rounded-3xl xs:px-4 px-2  font-manrope  xs:h-[30px] h-[25px] text-[10px] xs:leading-[16.5px] leading-none hover:text-black hover:font-bold "
                >
                  Book Now
                </Link>
              </div>
            </div>
          </div>
        </div>
      ) : (
        // Skeleton for text and button
        <div className="p-4 space-y-2">
          <div className="h-4 w-3/4 bg-slate-200 animate-pulse rounded" />
          <div className="h-3 w-1/2 bg-slate-200 animate-pulse rounded" />
          <div className="h-8 w-full bg-slate-200 animate-pulse rounded mt-4" />
        </div>
      )}
    </div>

    </>
  );
};

export default HostelCard;
