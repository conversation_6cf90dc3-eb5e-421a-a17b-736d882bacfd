/* eslint-disable react/no-unescaped-entities */
import React, { useState } from "react";
import dynamic from "next/dynamic";
import Link from "next/link";

const Confirmdelete = dynamic(
  () => import("../components/model/confirmdelete"),
  {
    ssr: false,
  }
);


const Confirmdeactivation = dynamic(
  () => import("../components/model/confirmdeactivation"),
  {
    ssr: false,
  }
);
const AccountDelete = () => {
  const [openConfirmdeletepopup, setopenConfirmdeletepopup] = useState(false);
  const [selectedReason, setSelectedReason] = useState("");
  const [selectedPeriod, setSelectedPeriod] = useState("");
  const [comment, setComment] = useState("");
  const handleOpenConfirmdeletepopup = () => setopenConfirmdeletepopup(true);
  const handleCloseConfirmdeletepopup = () => setopenConfirmdeletepopup(false);
  const [openConfirmdeactivationpopup, setopenConfirmdeactivationpopup] =
    useState(false);
  const handleOpenConfirmdeactivationpopup = () =>
    setopenConfirmdeactivationpopup(true);
  const handleCloseConfirmdeactivationpopup = () =>
    setopenConfirmdeactivationpopup(false);

  const reasons = [
    "Not finding what I’m looking for",
    "Concerns with privacy or security",
    "Technical issues with the app",
    "Found another platform",
    "Just taking a break",
    "Other (with an optional text field that appears if selected)",
  ];

  const deactivateOptions = ["7", "15", "30"];

  const handleReasonChange = (reason) => {
    setSelectedReason(reason);
    if (
      reason !== "Other (with an optional text field that appears if selected)"
    ) {
      setComment(""); // Clear comment if "Other" is not selected
    }
  };

  console.log("data", selectedReason, selectedPeriod, comment);
  return (
    <>
      <div className="bg-white pb-4 md:pb-20 pt-12">
        <div className="container md:px-8 lg:px-12 xl:px-16">
          <div className="w-[300px] flex flex-col items-center justify-start pr-6"></div>
          <div className="flex-1 border-l border-[#EEEEEE] pl-11 min-h-80">
            <h2 className="text-[#40E0D0] text-3xl font-bold mb-1">
              Delete <span className="text-black">Account</span>
            </h2>
            <p className="text-base text-black font-semibold">
              We're sorry to see you go. If you’re having issues or need a
              break, let us know
            </p>
            <div className="mt-3 p-5 border border-grey-600 rounded-md">
              <h4 className="font-bold mb-4 text-[20px]">
                Why Delete Mixdorm?
              </h4>
              <p className="text-base text-black font-semibold mt-3">
                Why are you considering deleting your Mixdorm account?
              </p>
              <p className="text-base text-black/80 font-normal">
                "We’d love to improve !
              </p>
              <p className="text-base text-black/80 font-normal">
                Please tell us the reason for leaving so we can make Mixdorm
                better for you and others.
              </p>
            </div>
            <p className="text-base text-black font-semibold mt-5">
              Select a reason for deleting your account
            </p>
            <div className="p-2 md:w-[600px] w-[300px] border border-grey-600 rounded-md mt-3">
              {reasons.map((reason, index) => (
                <div className="w-full" key={index}>
                  <input id={`reason-${index}`} type="radio" name="reason" className="w-4 h-4 border-gray-300 accent-[#00a394]"
                  onChange={() => handleReasonChange(reason)}
                  />
               
                  <label htmlFor={`reason-${index}`} className="ms-2 text-[18px] text-black/80 font-normal text-base"> {reason}</label>
                  
                </div>
              ))}
             
            </div>
            {selectedReason ===
              "Other (with an optional text field that appears if selected)" && (
              <>
                <p className="text-base text-black font-bold mt-5">
                  Additional Comments
                </p>
                <p className="text-base text-black/80 font-normal">
                  We value your feedback. Let us know if there’s anything we can
                  improve.
                </p>
                <textarea
                  className="w-full mt-2 p-3 border border-grey-600 rounded-md"
                  rows="4"
                  placeholder="Your comments..."
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                />
              </>
            )}
            <p className="text-base text-black font-bold mt-5">
              Consider Deactivating Your Account
            </p>
            <p className="text-base text-black/80 font-normal">
              If you just need a break, you can temporarily deactivate your
              account instead. Choose the time period, and your account will
              automatically reactivate after it expires.
            </p>
            <p className="text-base text-black font-bold mt-5">
              Deactivate Period Option
            </p>
            <div className="flex py-1 gap-3">
              {deactivateOptions.map((option, index) => (
                <div className="relative custom_radio" key={index}>
                 <div className="w-full" key={index}>
                  <input id={`deactivatePeriod-${index}`} type="radio" name="deactivatePeriod" className="w-4 h-4 border-gray-300 accent-[#00a394]"
                 onChange={() => setSelectedPeriod(option)}
                  />
               
                  <label htmlFor={`reason-${index}`} className="ms-2 text-[18px] text-black/80 font-normal text-base">{option} days</label>
                  
                </div>
                </div>
              ))}
            </div>
            <div className="p-4 border border-grey-600 rounded-md mt-5 bg-[#FFD7D7] border border-[#DE0000]">
              <h4 className="font-semibold mb-2 text-[20px]">
                Account Deletion Warning
              </h4>
              <ul className="list-disc pl-5">
                <li className="text-black/80 font-normal text-[16px]">
                  Once you delete your account, all your data will be
                  permanently removed, and this action cannot be undone
                </li>
                <li className="text-black/80 font-normal text-[16px]">
                  Your account will be deactivated temporarily and will
                  automatically reactivate after the chosen period
                </li>
              </ul>
            </div>
            <div className="grid mt-7 gap-2 w-full lg:flex">
              <button
                className="bg-red-600 text-white font-semibold py-2 px-5 rounded-full text-sm lg:text-md"
                onClick={handleOpenConfirmdeletepopup}
              >
                Delete My Account
              </button>
              <button
                className="bg-primary-blue text-black font-semibold py-2 px-5 rounded-full text-sm lg:text-md"
                onClick={handleOpenConfirmdeactivationpopup}
              >
                Deactivate My Account Temporarily
              </button>
              <Link
                href="/"
                prefetch={false}
                className="bg-[#eeeeee] text-black font-semibold py-2 px-5 rounded-full text-sm lg:text-md"
              >
                Cancel
              </Link>
            </div>
          </div>
        </div>
      </div>
      <Confirmdelete
        openConfirmdeletepopup={openConfirmdeletepopup}
        handleCloseConfirmdeletepopup={handleCloseConfirmdeletepopup}
        selectedReason={selectedReason}
        selectedPeriod={selectedPeriod}
        comment={comment}
      />
      <Confirmdeactivation
        openConfirmdeactivationpopup={openConfirmdeactivationpopup}
        handleCloseConfirmdeactivationpopup={
          handleCloseConfirmdeactivationpopup
        }
        selectedReason={selectedReason}
        selectedPeriod={selectedPeriod}
        comment={comment}
      />
    </>
  );
};

export default AccountDelete;
