import express from 'express';
import { addPayment, getAllPayments} from '../controller/transactions.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();

router.post('/', checkAuth('add_manual_payments'),addPayment);
router.get('/properties', checkAuth('get_manual_payments'),getAllPayments);

export default router;

/**
 * @swagger
 * tags:
 *   name: Payments Hostel Owner
 *   description: Property payments APIs
 */

/**
 * @swagger
 * /transactions:
 *   post:
 *     summary: Add a new payment
 *     tags: [Payments Hostel Owner]
 *     description: Creates a new payment record for a property.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: The name of the payer (optional).
 *               property:
 *                 type: string
 *                 format: objectId
 *                 description: The ID of the property for which the payment is being made.
 *               room:
 *                 type: string
 *                 format: objectId
 *                 description: The ID of the related payment record (optional).
 *               amount:
 *                 type: number
 *                 format: double
 *                 description: The amount to be paid.
 *               currency:
 *                 type: string
 *                 description: The currency of the payment (e.g., USD).
 *               checkIn:
 *                 type: string
 *                 format: date
 *                 description: Check-in date related to the payment (optional).
 *               checkOut:
 *                 type: string
 *                 format: date
 *                 description: Check-out date related to the payment (optional).
 *               type:
 *                 type: string
 *                 enum: [cash, card]
 *                 description: The payment method (cash or card).
 *             required:
 *               - property
 *               - amount
 *               - currency
 *               - type
 *     responses:
 *       201:
 *         description: Payment added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 _id:
 *                   type: string
 *                   description: The ID of the newly created payment.
 *                 user:
 *                   type: string
 *                   format: objectId
 *                 name:
 *                   type: string
 *                 property:
 *                   type: string
 *                   format: objectId
 *                 paymentId:
 *                   type: string
 *                   format: objectId
 *                 amount:
 *                   type: number
 *                   format: double
 *                 currency:
 *                   type: string
 *                 checkIn:
 *                   type: string
 *                   format: date
 *                 checkOut:
 *                   type: string
 *                   format: date
 *                 type:
 *                   type: string
 *                   enum: [cash, card]
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *                 updatedAt:
 *                   type: string
 *                   format: date-time
 *       400:
 *         description: Bad request (missing required fields)
 *       500:
 *         description: Internal Server Error
 */

/**
 * @swagger
 * /transactions/properties:
 *   get:
 *     summary: Get all payments for a property
 *     tags: [Payments Hostel Owner]
 *     description: Retrieves all payments associated with the specified property ID, optionally filtered by date range.
 *     parameters:
 *       - in: query
 *         name: propertyId
 *         schema:
 *           type: string
 *           format: objectId
 *         required: true
 *         description: The ID of the property to filter payments.
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *           example: "2024-09-23"  # Sample date format
 *         required: false
 *         description: Start date to filter payments (optional).
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *           example: "2024-12-31"  # Sample date format
 *         required: false
 *         description: End date to filter payments (optional).
 *     responses:
 *       200:
 *         description: A list of payments for the specified property.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   _id:
 *                     type: string
 *                     format: objectId
 *                   user:
 *                     type: string
 *                     format: objectId
 *                   name:
 *                     type: string
 *                   property:
 *                     type: string
 *                     format: objectId
 *                   paymentId:
 *                     type: string
 *                     format: objectId
 *                   amount:
 *                     type: number
 *                   currency:
 *                     type: string
 *                   checkIn:
 *                     type: string
 *                     format: date
 *                   checkOut:
 *                     type: string
 *                     format: date
 *                   type:
 *                     type: string
 *                     enum: [cash, card]
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *                   updatedAt:
 *                     type: string
 *                     format: date-time
 *       400:
 *         description: Bad request (missing required fields)
 *       500:
 *         description: Internal Server Error
 */
