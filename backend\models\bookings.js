import mongoose from 'mongoose';


const subBookingSchema = new mongoose.Schema({
  room: {
    type: mongoose.Types.ObjectId,
    ref: 'rooms',
  },
  checkInDate: { type: Date, },
  checkOutDate: { type: Date },
  guests: {
    adults: { type: Number },
    children: { type: Number },
  },
  paidAmount: {
    type: Number, // This will be calculated as totalAmount / totalRooms
  },
  nights: { type: Number },

});

const bookingSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Types.ObjectId,
      ref: 'users',
    },
    guestDetails: {
      name: { type: String, required: function () { return !this.user; } },
      email: { type: String },
      phone: { type: String },
    },
    bookingId: {
      type: Number,
      required: true,
      unique: true,
    },
    property: {
      type: mongoose.Types.ObjectId,
      ref: 'properties',
      required: true,
    },
    checkInDate: { type: Date, required: true },
    checkOutDate: { type: Date, required: true },

    totalRooms: { type: Number },
    totalGuests: {
      adults: { type: Number },
      children: { type: Number },
    },
    paidAmount: { type: Number },
    // 💸 Breakdown
    baseAdvance: { type: Number },             // 525
    gst: { type: Number },                     // 15.75
    paymentGatewayCharge: { type: Number },    // 97.335

    payableNow: { type: Number },              // 638.09
    payOnArrival: { type: Number },        // 2975

    currency: { type: String, default: 'INR' },

    subBookings: [subBookingSchema], // Added subBookings array

    isCancel: { type: Boolean, default: false },
    cancelledDate: { type: Date },
    cancelledBy: { type: mongoose.Types.ObjectId, ref: 'users' },

    status: {
      type: String,
      enum: ['pending', 'confirmed', 'cancelled'],
      default: 'confirmed',
    },
    paymentStatus: {
      type: String,
      enum: ['unpaid', 'paid'],
      default: 'paid',
    },
    isActive: { type: Boolean, default: true },
    isDeleted: { type: Boolean, default: false },
  },
  { timestamps: true }
);
const bookingModel = mongoose.model('bookings', bookingSchema);
export default bookingModel;
