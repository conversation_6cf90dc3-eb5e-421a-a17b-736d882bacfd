import httpServices from "./httpServices";

export const logInAdminApi = (payload) => {
  return httpServices.post(`/auth/login-user`,payload)
}

export const sendOtpAdminApi = (payload) => {
  return httpServices.post(`/auth/verify-email`,payload)
}

export const verifyOtpAdmin = (payload) => {
  return httpServices.post(`/auth/verify-otp/`,payload)
}

export const userListAdmin = (currentPage, memberPerpage) => {
  return httpServices.get(`/auth/users?page=${currentPage}&limit=${memberPerpage}`
  );
}

export const hostelListAdmin = (currentPage, hostelPerpage) => {
  return httpServices.get(`/property/all?page=${currentPage}&limit=${hostelPerpage}`
  );
}

export const blogListAdmin = (currentPage, blogPerpage) => {
  return httpServices.get(`/blog?page=${currentPage}&limit=${blogPerpage}`
  );
}

export const userProfileListAdmin = () => {
  return httpServices.get(`/auth/admins`);
}

export const addSubAdmin = (payload) => {
  return httpServices.post(`/auth/add-sub-admin`,payload);
}

export const deleteSubAdmin = (id) => {
  return httpServices.delete(`/auth/${id}`);
}

export const getSubAdminDetails = (id) => {
  return httpServices.get(`/auth/${id}`);
}

export const updateSubAdminDetails = (id,payload) => {
  return httpServices.put(`/auth/update-sub-admin/${id}`,payload);
}