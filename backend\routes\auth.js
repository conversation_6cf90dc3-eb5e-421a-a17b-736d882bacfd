import express from "express";
import { users,login ,register,forgotPassword,resetPassword,profile,updateProfile, changePassword,
    verifyEmail, addSubAdmin, listAllAdminAndSubAdmin,
     deleteSubAdmin, updateSubAdmin, addHostelOwner,verifyOtp,getUserDataById,
     loginUser,
     registerUser,
     getUsers,socialLogin,deleteSelfAccount,loginOrRegister,
     activteProfile,
     acticateAccount} from "../controller/auth.js";
import {checkAuth} from '../middleware/auth.js'
const router = express.Router();

router.post("/register", register);
router.post("/login", login);
router.post("/social/login", socialLogin);
router.post("/verify-email", verifyEmail);
router.post("/verify-otp", verifyOtp);
router.post("/register-user", registerUser);
router.post("/login-user", loginUser);
router.get('/users', checkAuth('get_users'), getUsers);
router.get("/profile",checkAuth('user_profile') ,profile);
router.put("/profile", checkAuth('update_user_profile'), updateProfile);
router.put("/active-profile", checkAuth('update_user_profile'), activteProfile);

router.post("/forgot-password", forgotPassword);
router.put("/reset-password", resetPassword);
router.put("/change-password", checkAuth('change_password'), changePassword);

router.post("/add-sub-admin", checkAuth('add_sub_admin'), addSubAdmin);
router.put('/update-sub-admin/:id', checkAuth('update_sub_admin'), updateSubAdmin);
router.get('/admins',checkAuth('get_admins'), listAllAdminAndSubAdmin);
router.post('/remove-account', checkAuth(''), deleteSelfAccount);
router.delete('/:id', checkAuth('delete_sub_admin'), deleteSubAdmin);
router.post('/activate-account', acticateAccount);

router.post('/hostel-provider', addHostelOwner);
router.get('/:id',checkAuth('get_user_details'), getUserDataById);

router.post('/loginOrRegister', loginOrRegister);


export default router;
/**
 * @swagger
 * tags:
 *   name: Auth
 *   description: User authentication endpoints
 */
/**
 * @swagger
 * /auth/register:
 *   post:
 *     summary: Register a new user
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: User's full name
 *               email:
 *                 type: string
 *                 description: User email
 *               password:
 *                 type: string
 *                 description: User password
 *               contact:
 *                 type: string
 *                 description: User contact number (optional)
 *             required:
 *               - name
 *               - email
 *               - password
 *     responses:
 *       '201':
 *         description: Registration successful
 *         content:
 *           application/json:
 *             example:
 *               message: 'User created and email verification sent successfully!'
 *       '400':
 *         description: Bad request, validation error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Name, Email and Password are mandatory'
 *       '409':
 *         description: Conflict, user already exists
 *         content:
 *           application/json:
 *             example:
 *               message: 'EMAIL_EXISTS'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */
/**
 * @swagger
 * /auth/login:
 *   post:
 *     summary: Login user with email and password
 *     tags: [Auth]
 *     x-isPublic: true
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 description: User email
 *               password:
 *                 type: string
 *                 description: User password
 *             required:
 *               - email
 *               - password
 *     responses:
 *       '201':
 *         description: Login successful
 *         content:
 *           application/json:
 *             example:
 *               message: 'Email Verification Sent'
 *       '401':
 *         description: Invalid email or password or email not verified
 *         content:
 *           application/json:
 *             example:
 *               message: 'Invalid email or password'
 *       '404':
 *         description: User not found
 *         content:
 *           application/json:
 *             example:
 *               message: 'User Not Found'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */
/**
 * @swagger
 * /auth/verify-otp:
 *   post:
 *     summary: Verify OTP and update email verification status
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 description: User email
 *               otp:
 *                 type: string
 *                 description: OTP sent to email
 *             required:
 *               - email
 *               - otp
 *     responses:
 *       '201':
 *         description: Email verification successful
 *         content:
 *           application/json:
 *             example:
 *               user:
 *                 type: object
 *                 properties:
 *                   _id:
 *                     type: string
 *                   name:
 *                     type: object
 *                     properties:
 *                       first:
 *                         type: string
 *                       last:
 *                         type: string
 *                   email:
 *                     type: string
 *                   phone:
 *                     type: string
 *               token: 'String Token'
 *       '404':
 *         description: Invalid or expired OTP
 *         content:
 *           application/json:
 *             example:
 *               message: 'Invalid OTP'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */

/**
 * @swagger
 * /auth/forgot-password:
 *   post:
 *     summary: Send a password reset email
 *     tags: [Auth]
 *     produces:
 *       - application/json
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *               reset_link:
 *                 type: string
 *     responses:
 *       '200':
 *         description: Password reset email sent successfully
 *         content:
 *           application/json:
 *             example:
 *               message: "Password reset email sent successfully"
 *       '404':
 *         description: User not found
 *         content:
 *           application/json:
 *             example:
 *               message: "User not found"
 *       '500':
 *         description: Server error
 *         content:
 *           application/json:
 *             example:
 *               message: "Internal server error"
 */

/**
 * @swagger
 * /auth/reset-password:
 *   put:
 *     summary: Reset user password
 *     tags: [Auth]
 *     produces:
 *       - application/json
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               newPassword:
 *                 type: string
 *               email:
 *                 type: string
 *     responses:
 *       '200':
 *         description: Password reset successfully
 *         content:
 *           application/json:
 *             example:
 *               message: "Password reset successfully"
 *       '401':
 *         description: Account deactivated
 *         content:
 *           application/json:
 *             example:
 *               message: "Your account is deactivated. Please contact admin"
 *       '404':
 *         description: User not found
 *         content:
 *           application/json:
 *             example:
 *               message: "User not found"
 *       '500':
 *         description: Server error
 *         content:
 *           application/json:
 *             example:
 *               message: "Internal server error"
 */
/**
 * @swagger
 * /auth/{id}:
 *   get:
 *     summary: Get user data by ID
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The user ID
 *     responses:
 *       '200':
 *         description: Successfully retrieved user data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 user:
 *                   type: object
 *                   description: The user data
 *                   properties:
 *                     _id:
 *                       type: string
 *                       description: User ID
 *                     name:
 *                       type: string
 *                       description: User name
 *                     email:
 *                       type: string
 *                       description: User email
 *             example:
 *               user:
 *                 _id: '60d0fe4f5311236168a109ca'
 *                 name: 'John Doe'
 *                 email: '<EMAIL>'
 *       '404':
 *         description: User not found
 *         content:
 *           application/json:
 *             example:
 *               message: 'USER_NOT_FOUND'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */

/**
 * @swagger
 * /auth/add-sub-admin:
 *   post:
 *     summary: Create a new sub-admin
 *     tags: [Sub Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             name: John Doe
 *             email: <EMAIL>
 *             phone: "************"
 *             password: yourpassword
 *     responses:
 *       '201':
 *         description: The created sub-admin
 *         content:
 *           application/json:
 *             example:
 *               id: 12345
 *               name: John Doe
 *               email: <EMAIL>
 *               phone: "************"
 *               createdAt: 2023-06-08T12:34:56Z
 *               updatedAt: 2023-06-08T12:34:56Z
 *       '409':
 *         description: Conflict
 *       '500':
 *         description: Internal Server Error
 */

/**
 * @swagger
 * /auth/update-sub-admin/{id}:
 *   put:
 *     summary: Update sub-admin details
 *     tags: [Sub Admin]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID of the sub-admin to update
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: object
 *                 properties:
 *                   prefix:
 *                     type: string
 *                   first:
 *                     type: string
 *                     description: The sub-admin's first name
 *                     example: John
 *                   middle:
 *                     type: string
 *                   last:
 *                     type: string
 *                     description: The sub-admin's last name
 *                     example: Doe
 *                   disp_name:
 *                     type: string
 *               gender:
 *                 type: string
 *               dob:
 *                 type: string
 *                 format: date
 *               address:
 *                 type: object
 *                 properties:
 *                   lineOne:
 *                     type: string
 *                   lineTwo:
 *                     type: string
 *               mobile:
 *                 type: number
 *               phone:
 *                 type: string
 *               role:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *               resetPasswordToken:
 *                 type: string
 *               resetPasswordExpires:
 *                 type: string
 *                 format: date-time
 *               otp:
 *                 type: number
 *               otpExpires:
 *                 type: string
 *                 format: date-time
 *               isArchived:
 *                 type: boolean
 *               isDeleted:
 *                 type: boolean
 *               isActive:
 *                 type: boolean
 *               lastAccess:
 *                 type: string
 *                 format: date-time
 *     responses:
 *       '200':
 *         description: Sub-admin details updated successfully
 *       '403':
 *         description: Forbidden
 *       '404':
 *         description: Not Found
 *       '500':
 *         description: Internal Server Error
 */

/**
 * @swagger
 * /auth/admins:
 *   get:
 *     summary: Get a list of all admin and sub-admin
 *     tags: [Sub Admin]
 *     responses:
 *       '200':
 *         description: A list of all admin and sub-admin
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: The user ID
 *                   name:
 *                     type: object
 *                     properties:
 *                       prefix:
 *                         type: string
 *                       first:
 *                         type: string
 *                         description: The user's first name
 *                       middle:
 *                         type: string
 *                       last:
 *                         type: string
 *                         description: The user's last name
 *                       disp_name:
 *                         type: string
 *                   gender:
 *                     type: string
 *                   dob:
 *                     type: string
 *                     format: date
 *                   address:
 *                     type: object
 *                     properties:
 *                       lineOne:
 *                         type: string
 *                       lineTwo:
 *                         type: string
 *                   mobile:
 *                     type: number
 *                   phone:
 *                     type: string
 *                   role:
 *                     type: string
 *                     description: The user's role (e.g., 'admin' or 'sub-admin')
 *                   email:
 *                     type: string
 *                     format: email
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *                   updatedAt:
 *                     type: string
 *                     format: date-time
 *       '500':
 *         description: Internal Server Error
 */
/**
 * @swagger
 * /auth/profile:
 *   put:
 *     summary: Update User Profile
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: object
 *                 properties:
 *                   first:
 *                     type: string
 *                   last:
 *                     type: string
 *               email:
 *                 type: string
 *               password:
 *                 type: string
 *               languages:
 *                 type: array
 *                 items:
 *                   type: string
 *               status:
 *                 type: string
 *               business:
 *                 type: string
 *     responses:
 *       '200':
 *         description: User profile updated successfully
 *         content:
 *           application/json:
 *             example:
 *               message: "User profile updated successfully"
 *       '401':
 *         description: Unauthorized request
 *       '500':
 *         description: Server error
 *         content:
 *           application/json:
 *             example:
 *               message: "Internal server error"
 */

/**
 * @swagger
 * /auth/verify-email:
 *   post:
 *     summary: Send email verification OTP
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 description: User email
 *             required:
 *               - email
 *     responses:
 *       '201':
 *         description: Email verification OTP sent
 *         content:
 *           application/json:
 *             example:
 *               message: 'Email Verification Sent'
 *       '404':
 *         description: User not found
 *         content:
 *           application/json:
 *             example:
 *               message: 'USER_NOT_FOUND'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */

/**
 * @swagger
 * /auth/change-password:
 *   put:
 *     summary: Change user password
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *                   old_password:
 *                     type: string
 *                   new_password:
 *                     type: string
 *                   confirm_password:
 *                     type: string
 *     responses:
 *       '200':
 *         description: Password changed successfully
 *         content:
 *           application/json:
 *             example:
 *               data: {}
 *               message: "Password changed successfully"
 *       '401':
 *         description: Unauthorized request
 *       '500':
 *         description: Server error
 *         content:
 *           application/json:
 *             example:
 *               message: "Internal server error"
 */


/**
 * @swagger
 * /auth/{id}:
 *   delete:
 *     summary: Delete a sub-admin
 *     tags: [Sub Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         description: ID of the sub-admin to delete
 *         schema:
 *           type: string
 *     responses:
 *       '200':
 *         description: Sub-admin deleted successfully
 *       '403':
 *         description: Forbidden
 *       '404':
 *         description: Not Found
 *       '500':
 *         description: Internal Server Error
 */

/**
 * @swagger
 * /auth/hostel-provider:
 *   post:
 *     summary: Add a new hostel owner
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 required: true
 *               email:
 *                 type: string
 *                 format: email
 *                 required: true
 *               password:
 *                 type: string
 *                 format: password
 *                 required: true
 *     responses:
 *       '201':
 *         description: Hostel owner created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 _id:
 *                   type: string
 *                 name:
 *                   type: string
 *                 email:
 *                   type: string
 *                 role:
 *                   type: string
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *                 updatedAt:
 *                   type: string
 *                   format: date-time
 *       '400':
 *         description: Missing required fields or invalid data
 *         content:
 *           application/json:
 *             example:
 *               message: 'MISSING_FIELDS'
 *       '401':
 *         description: Unauthorized, user is not an admin
 *         content:
 *           application/json:
 *             example:
 *               message: 'Not a valid user'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */

/**
 * @swagger
 * /auth/register-user:
 *   post:
 *     summary: Register a new user
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - email
 *               - password
 *             properties:
 *               name:
 *                 type: object
 *                 properties:
 *                   first:
 *                     type: string
 *                     description: First name
 *                   last:
 *                     type: string
 *                     description: Last name
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email address
 *               password:
 *                 type: string
 *                 format: password
 *                 description: Password
 *               contact:
 *                 type: string
 *                 description: Contact number
 *     responses:
 *       '201':
 *         description: User created, and email verification sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: User created, and email verification sent successfully!
 *       '409':
 *         description: Email already exists
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: EMAIL_EXISTS
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */

/**
 * @swagger
 * /auth/login-user:
 *   post:
 *     summary: Log in a user
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email address
 *               password:
 *                 type: string
 *                 format: password
 *                 description: Password
 *     responses:
 *       '201':
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 user:
 *                   type: object
 *                   properties:
 *                     _id:
 *                       type: string
 *                     name:
 *                       type: object
 *                       properties:
 *                         first:
 *                           type: string
 *                         last:
 *                           type: string
 *                     email:
 *                       type: string
 *                     role:
 *                       type: string
 *                       enum: [admin, sub_admin, user, hostel_owner]
 *                     contact:
 *                       type: string
 *                     isEmailVerified:
 *                       type: boolean
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                 token:
 *                   type: string
 *                   description: JWT token
 *                 message:
 *                   type: string
 *                   example: Login Success
 *       '404':
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: User Not Found
 *       '401':
 *         description: Invalid email or password
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Invalid email or password
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */

/**
 * @swagger
 * /auth/profile:
 *   get:
 *     summary: Get user data by ID
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '200':
 *         description: Successfully retrieved user data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 user:
 *                   type: object
 *                   description: The user data
 *                   properties:
 *                     _id:
 *                       type: string
 *                       description: User ID
 *                     name:
 *                       type: string
 *                       description: User name
 *                     email:
 *                       type: string
 *                       description: User email
 *                     profileImage:
 *                       type: string
 *                       description: User Profile Image url
 *             example:
 *               user:
 *                 _id: '60d0fe4f5311236168a109ca'
 *                 name: 'John Doe'
 *                 email: '<EMAIL>'
 *                 profileImage: 'www.profile-image.com'
 *       '404':
 *         description: User not found
 *         content:
 *           application/json:
 *             example:
 *               message: 'USER_NOT_FOUND'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */


/**
 * @swagger
 * tags:
 *   name: Users
 *   description: User management endpoints
 */

/**
 * @swagger
 * /auth/users:
 *   get:
 *     summary: Get a list of users with pagination and filter
 *     tags: [Users]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         required: false
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 10
 *         required: false
 *         description: Number of items per page
 *       - in: query
 *         name: filter
 *         schema:
 *           type: object
 *           style: deepObject
 *           explode: true
 *         required: false
 *         description: Filter conditions
 *     responses:
 *       '200':
 *         description: List of users
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 users:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                       name:
 *                         type: object
 *                         properties:
 *                           first:
 *                             type: string
 *                           last:
 *                             type: string
 *                       email:
 *                         type: string
 *                       contact:
 *                         type: string
 *                       role:
 *                         type: string
 *                       isEmailVerified:
 *                         type: boolean
 *                       profileImage:
 *                         type: string
 *                       aboutMe:
 *                         type: string
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     totalUsers:
 *                       type: integer
 *             example:
 *               users:
 *                 - _id: "60f7c1d2b4d6c91654b8d6d2"
 *                   name:
 *                     first: "John"
 *                     last: "Doe"
 *                   email: "<EMAIL>"
 *                   contact: "**********"
 *                   role: "user"
 *                   isEmailVerified: true
 *                   profileImage: "http://example.com/image.jpg"
 *                   aboutMe: "I love traveling."
 *               pagination:
 *                 page: 1
 *                 limit: 10
 *                 totalPages: 5
 *                 totalUsers: 50
 *       '500':
 *         description: Internal server error
 */
/**
 * @swagger
 * /auth/social/login:
 *   post:
 *     summary: Login or register user with social login (e.g., Google, Facebook)
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 description: User email retrieved from the social provider
 *               name:
 *                 type: string
 *                 description: Full name of the user retrieved from the social provider
 *               contact:
 *                 type: string
 *                 description: Optional user contact number
 *             required:
 *               - email
 *               - name
 *     responses:
 *       '201':
 *         description: Social login successful (user logged in or registered)
 *         content:
 *           application/json:
 *             example:
 *               message: 'Login Successful'
 *               token: 'jwt_token_here'
 *               user: 
 *                 _id: 'user_id_here'
 *                 email: '<EMAIL>'
 *                 name: 
 *                   first: 'John'
 *                   last: 'Doe'
 *                 role: 'user'
 *                 contact: '**********'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */

/**
 * @swagger
 * /auth/remove-account:
 *   post:
 *     summary: Delete a sub-admin
 *     tags: [Sub Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *                 description: Reason for deactivating or deleting the account
 *                 example: "Not finding what I’m looking for"
 *               deactivate_period:
 *                 type: integer
 *                 description: Number of days to deactivate the account before automatic reactivation. Required if `type` is `deactivate`.
 *                 example: 7
 *               comment:
 *                 type: string
 *                 description: Additional comments about the request
 *                 example: "I may return later"
 *               type:
 *                 type: string
 *                 enum: [deactivate, delete]
 *                 description: Action to perform on the account - deactivate or delete
 *                 example: "deactivate"
 *             required:
 *               - type
 *     responses:
 *       '200':
 *         description: Account action performed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Account deactivated for 7 day(s)"
 *       '400':
 *         description: Invalid input
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "INVALID_DEACTIVATE_PERIOD"
 *       '403':
 *         description: Forbidden
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Access forbidden"
 *       '404':
 *         description: Account not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "USER_NOT_FOUND"
 *       '422':
 *         description: Unprocessable Entity
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Deactivation period must be greater than 0"
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */

/**
 * @swagger
 * /auth/loginOrRegister:
 *   post:
 *     summary: Login or register a user
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               password:
 *                 type: string
 *                 format: password
 *                 example: "securepassword"
 *               name:
 *                 type: string
 *                 example: "John Doe"
 *                 description: Optional name for new user registration
 *             required:
 *               - email
 *               - password
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Login successful"
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       type: object
 *                       properties:
 *                         _id:
 *                           type: string
 *                           example: "123456"
 *                         name:
 *                           type: object
 *                           properties:
 *                             first:
 *                               type: string
 *                               example: "John Doe"
 *                         email:
 *                           type: string
 *                           format: email
 *                           example: "<EMAIL>"
 *                         role:
 *                           type: string
 *                           example: "user"
 *                     token:
 *                       type: string
 *                       example: "jwt-token"
 *       201:
 *         description: User registered successfully and logged in
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User registered successfully and logged in"
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       type: object
 *                       properties:
 *                         _id:
 *                           type: string
 *                           example: "789012"
 *                         name:
 *                           type: object
 *                           properties:
 *                             first:
 *                               type: string
 *                               example: "Default User"
 *                         email:
 *                           type: string
 *                           format: email
 *                           example: "<EMAIL>"
 *                         role:
 *                           type: string
 *                           example: "user"
 *                     token:
 *                       type: string
 *                       example: "jwt-token"
 *       400:
 *         description: Missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Email and Password are mandatory"
 *       401:
 *         description: Unauthorized (invalid credentials or email not verified)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid email or password"
 *       403:
 *         description: Forbidden (role mismatch)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Access restricted to user role"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */

/**
 * @swagger
 * /auth/active-profile:
 *   put:
 *     summary: Update User Profile
 *     tags: [Auth]
 *     responses:
 *       '200':
 *         description: User profile updated successfully
 *         content:
 *           application/json:
 *             example:
 *               message: "User profile updated successfully"
 *       '401':
 *         description: Unauthorized request
 *       '500':
 *         description: Server error
 *         content:
 *           application/json:
 *             example:
 *               message: "Internal server error"
 */
/**
 * @swagger
 * /auth/activate-account:
 *   post:
 *     summary: Activate User account           
 *     tags: [Auth]
 *     x-isPublic: true
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 description: User email
 *             required:
 *               - email
 *     responses:
 *       '201':
 *         description: Activate Success
 *         content:
 *           application/json:
 *             example:
 *               message: 'User Profile activated'
 *       '401':
 *         description: Invalid email or password or email not verified
 *         content:
 *           application/json:
 *             example:
 *               message: 'Invalid email or password'
 *       '404':
 *         description: User not found
 *         content:
 *           application/json:
 *             example:
 *               message: 'User Not Found'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */
