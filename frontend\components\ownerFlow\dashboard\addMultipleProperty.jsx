 
import Box from "@mui/material/Box";
import { FaCircleCheck } from "react-icons/fa6";
import Modal from "@mui/material/Modal";
import Image from "next/image";
import { RxPlus } from "react-icons/rx";

const style = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 430,
  bgcolor: "background.paper",
  borderRadius: "30px",
  boxShadow: 24,
};

const AddMultipleProperty = ({
  openMultipleProperty,
  handleCloseMultipleProperty,
}) => {
  return (
    <>
      <Modal
        open={openMultipleProperty}
        onClose={handleCloseMultipleProperty}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box sx={style}>
          <div className="p-8 border-b border-[#D6D6D6]">
            <h4 className="text-[#383E49] text-center font-semibold text-lg mb-6">
              Add Multiple Property
            </h4>
            <div className="flex items-center gap-4">
              <div className="max-w-[49px] relative">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/avatar.png`}
                  alt="Profile Pic"
                  className="rounded-full w-[49px] h-[49px] relative top-[5px] right-[10px]"
                  width={49}
                  height={49}
                  loading="lazy"
                />
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/profile_pic_circle.png`}
                  alt="Profile Pic"
                  className=" absolute top-[1px] right-[5px] w-[69px] h-[57px]"
                  width={69}
                  height={57}
                  loading="lazy"
                />
                <div className="absolute flex items-center justify-center w-5 h-5 rounded-full cursor-pointer sm:w-4 sm:h-4 -bottom-1 right-1 bg-primary-blue">
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                    alt="Flag"
                    className="w-full h-full rounded-full"
                    width={16}
                    height={16}
                    loading="lazy"
                  />
                </div>
              </div>
              <h5 className="font-semibold text-black text-base">
                Namastey Mumbai Backpakers,
              </h5>
              <span className="ml-auto">
                <FaCircleCheck className="text-primary-blue text-2xl" />
              </span>
            </div>
          </div>
          <div className="p-8">
            <button className="flex gap-4 items-center font-bold text-base hover:text-primary-blue">
              <span className="w-12 h-12 rounded-full border border-dotted border-[#D6D6D6] flex items-center justify-center text-[#a7a7a7] text-3xl">
                <RxPlus />
              </span>
              Add another Property
            </button>
          </div>
        </Box>
      </Modal>
    </>
  );
};

export default AddMultipleProperty;
