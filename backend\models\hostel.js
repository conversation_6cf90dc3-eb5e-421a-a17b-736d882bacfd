import mongoose from 'mongoose';

const hostelSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    address: {
        lineOne: {
            type: String,
            required: true
        },
        lineTwo: {
            type: String,
        },
        city: {
            type: String,
        },
        state: {
            type: String,
        },
        zip: {
            type: String,
        },
        country: {
            type: String,
        }
    },
    room: {
        type: Number,
        required: false
    },
    bed: {
        type: Number,
        required: false
    },
    offer: {
        type: String,
    },
    status: {
        type: String,
        required: true,
        default : 0
    },
    aboutUs: {
        type: String,
    },
    hostelImages: [
            {
                type: String,
                required: false
            },
    ],
    rules: {
        type: String,
    },
    cancellationPolicies: {
        type: String,
    },
    generalPolicies: {
        type: String,
    },
    amenities: [
        {
            type: String,
        }
    ],
    isArchived: {
        type: Boolean,
        default: false
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});

const hostelModel = mongoose.model('hostels', hostelSchema);
export default hostelModel;