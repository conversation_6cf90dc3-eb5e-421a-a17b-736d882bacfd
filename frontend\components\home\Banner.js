/* eslint-disable react/no-unescaped-entities */
import React, { useState, useCallback, useEffect } from "react";
import SearchProperties from "./SearchProperties";
import { useRouter } from "next/router";
import { addDays } from "date-fns";
import CountryModal from "../model/countryModel";
import { getItemLocalStorage } from "@/utils/browserSetting";
import { useNavbar } from "./navbarContext";
import toast from "../toast/toast";
// import toast from "react-hot-toast";
import { motion } from "framer-motion";

const Banner = ({ country }) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [dataa, setData] = useState({
    checkIn: "",
    checkOut: "",
  });
  const [guest, setGuest] = useState(1);
  const [state, setState] = useState("");
  const [openCountryModal, setOpenCountryModal] = useState(false);
  const [isCountrySelected, setIsCountrySelected] = useState(false);
  const [heading, setHeading] = useState("");

  console.log("data", dataa)

  const handleOpenCountryModal = () => setOpenCountryModal(true);
  const handleCloseCountryModal = () => {
    setOpenCountryModal(false);
    const selectedCountry = getItemLocalStorage("selectedCountry");
    const selectedCurrencyCode = getItemLocalStorage("selectedCurrencyCode");
    const selectedCountryFlag = getItemLocalStorage("selectedCountryFlag");
    const selectedCurrencySymbol = getItemLocalStorage(
      "selectedCurrencySymbol"
    );

    if (
      selectedCountry &&
      selectedCurrencyCode &&
      selectedCountryFlag &&
      selectedCurrencySymbol
    ) {
      setIsCountrySelected(true);
    }
  };

  useEffect(() => {
    if (isCountrySelected) {
      handleSubmit();
    }
  }, [isCountrySelected]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleSubmit = useCallback(async () => {
    const selectedCountry = getItemLocalStorage("selectedCountry");
    const selectedCurrencyCode = getItemLocalStorage("selectedCurrencyCode");
    const selectedCountryFlag = getItemLocalStorage("selectedCountryFlag");
    const selectedCurrencySymbol = getItemLocalStorage(
      "selectedCurrencySymbol"
    );

    if (
      !selectedCountry ||
      !selectedCurrencyCode ||
      !selectedCountryFlag ||
      !selectedCurrencySymbol
    ) {
      handleOpenCountryModal();
      return;
    }

    if (guest < 1) {
      toast.error("Guests !!", {
        subText: "Please Select Number Of Guests",
      });
      return;
    }

    if (state === "") {
      toast.error("Location !!", {
        subText: "Please select the Location",
      });
      return;
    }

    try {
      setLoading(true);
      // const checkIn = dataa?.checkIn || new Date();
      // const checkOut = dataa?.checkOut || addDays(new Date(), 1);
      const checkIn = dataa?.dateRange?.checkIn || new Date();
      const checkOut = dataa?.dateRange?.checkOut || addDays(new Date(), 1);
      const bookingData = { state, checkIn, checkOut, guest };
      localStorage.setItem("bookingdata", JSON.stringify(bookingData));
      router.push("/search");
    } catch (error) {
      console.log("error", error);
    } finally {
      setLoading(false);
    }
  }, [state, dataa, guest, router]);

  const { updateCountry2 } = useNavbar();

  const updateCountry = () => {
    const flag = getItemLocalStorage("selectedCountryFlag");
    const code = getItemLocalStorage("selectedCurrencyCode");
    updateCountry2(flag, code);
  };

  useEffect(() => {
    const path = router.pathname;
    if (path === "/exploreworld") {
      setHeading(
        <motion.div
          initial={{ x: -100, opacity: 0 }}
          whileInView={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
          viewport={{ once: false }}
          className=" text-white"
        >
          <div className=" text-white">
            Explore the World with 
            <span className="text-primary-blue font-mashiny xs:text-4xl text-3xl md:text-5xl font-normal">Mixdorm</span>
          </div>
        </motion.div>
      );
    } else if (path === "/tophostel") {
      setHeading(
        <motion.div
          initial={{ x: -100, opacity: 0 }}
          whileInView={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
          viewport={{ once: false }}
          className=" text-white"
        >
          {country !== "India" && "Top"} Hostels in
          <span className="text-primary-blue font-mashiny xs:text-4xl text-3xl md:text-5xl font-normal"> {country}</span>
        </motion.div>
      );
    } else if (path === "/featuredhostel") {
      setHeading(
        <motion.div
          initial={{ x: -100, opacity: 0 }}
          whileInView={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
          viewport={{ once: false, amount: 0.5 }}
          className=" text-white"
        >
          Featured <span className="text-primary-blue xs:text-4xl text-3xl md:text-5xl font-normal font-mashiny">Hostels</span> On Mix
          <span className="text-primary-blue ">dorm</span></motion.div>
        
      );
    } else if (path === "/travelactivity") {
      setHeading(
         <motion.div
          initial={{ x: -100, opacity: 0 }}
          whileInView={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
          viewport={{ once: false, amount: 0.5 }}
          className=" text-white"
        >
          Travel by <span className="text-primary-blue xs:text-4xl text-3xl md:text-5xl font-normal font-mashiny">Activities</span>
     </motion.div>
      );
    } else if (path === "/meetbuddies") {
      setHeading(
        <motion.div
          initial={{ x: -100, opacity: 0 }}
          whileInView={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
          viewport={{ once: false, amount: 0.5 }}
          className=" text-white"
        >
          Meet Buddies, Split Bills, Share Rides, and Explore with <br />{" "}
          AI-powered <span className="text-primary-blue font-mashiny xs:text-4xl text-3xl md:text-5xl font-normal">Ease </span>!"
        </motion.div>
      );
    } else if (path === "/discover-event") {
      setHeading(
        // <motion.div
        //   initial={{ x: -100, opacity: 0 }}
        //   whileInView={{ x: 0, opacity: 1 }}
        //   transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
        //   viewport={{ once: false, amount: 0.5 }}
        <div
          className=" text-white"
        >
          Discover Events and Activities with 
          <span className="text-primary-blue font-mashiny xs:text-4xl text-3xl md:text-5xl font-normal
          ">Mixdorm</span>
        </div>
        // </motion.div>
      );
    } else {
      setHeading(
        // <motion.div
        //   initial={{ y: 100, opacity: 0 }}
        //   whileInView={{ y: 0, opacity: 1 }}
        //   transition={{ duration: 0.8, ease: "easeOut" }}
        //   viewport={{ once: false }}
        <h1
          className="xs:text-3xl md:text-4xl text-2xl text-white mt-5 md:mt-0"
          style={{
            fontDisplay: "swap", 
            fontWeight: 700,     
          }}
        >
          "Experience{" "}
          <span className="text-primary-blue font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl">
            Hostels
          </span>
          <br className="flex md:hidden" /> Like <br className="hidden" /> Never Before"
        </h1>
        // </motion.div>
      );
    }
  }, [router.pathname]);

   const isTopHostelPage = router.pathname === "/tophostel" || router.pathname === "/exploreworld" || router.pathname === "/featuredhostel" || router.pathname === "/travelactivity" || router.pathname === "/meetbuddies" || router.pathname === "/discover-event" ;

  return (
    <>
      <CountryModal
        openCountryModal={openCountryModal}
        handleCloseCountryModal={handleCloseCountryModal}
        updateCountry={updateCountry}
        className="z-10"
      />

      <section className="w-full relative z-20 lg:pt-16 pt-0 lg:px-14 px-6">
        <div className="w-full sm:pt-12 pt-5 md:pb-14 pb-8">
          <div className="container relative">
            <div
              className={`text-center lg:text-4xl md:text-3xl text-2xl font-extrabold ${isTopHostelPage ? "mt-10 lg:mt-0" : "mt-0"
                }`}
            >{heading}</div>
          </div>
        </div>
        <div className="min-h-[81px]">
          <SearchProperties
            setState={setState}
            dataa={dataa}
            guest={guest}
            loading={loading}
            setGuest={setGuest}
            handleChange={handleChange}
            handleSubmit={handleSubmit}
            size="large"
          />
        </div>
      </section>
    </>
  );
};

export default Banner;
