// models/ratePlanModel.js

import mongoose from 'mongoose';

const ratePlanSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    otaRateId: {
      type: Number,
      required: true,
      unique: true,
    },
    room: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'rooms',
      required: true,
    },
    rate: {
      type: Number,
      required: true,
      default:0
    },
    currency: {
      type: String,
      default: 'INR',
    },
    restrictions: {
      closed: {
        type: Boolean,
        default: false,
      },
      minStay: {
        type: Number,
        default: 0,
      },
      maxStay: {
        type: Number,
        default: 365,
      },
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    property: {
      type: mongoose.Types.ObjectId,
      ref: 'properties',
      required: true,
    },
    ezeeId: {
      type: String,
    }
  },
  { timestamps: true } // Automatically adds createdAt and updatedAt
);

const RatePlan = mongoose.model('RatePlans', ratePlanSchema, "RatePlans");

export default RatePlan;
