import Link from "next/link";
import React from "react";

const BookingDetails = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <div className="flex items-center justify-between w-full ">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins  dark:text-gray-100">
          Booking Details
        </h2>

      </div>
      <div className="bg-white border rounded-xl mt-5 h-auto dark:bg-black dark:border-none">
        <div className="p-8 flex flex-col gap-y-6">
          {/* <div>
            <h1 className="text-xl font-bold">Booking Information</h1>
            <div className="flex items-center gap-x-20 my-6">
              <div className="flex w-[15%] flex-col">
                <strong className="text-black/75">NAME</strong>
                <p className="mt-2 text-black/65"><PERSON></p>
              </div>
              <div className="flex w-[15%] flex-col">
                <strong className="text-black/75">Booking Reference</strong>
                <p className="mt-2 text-black/65">jaypal yadav</p>
              </div>
              <div className="flexw-[30%] flex-col">
                <strong className="text-black/75">People</strong>
                <p className="mt-2 text-black/65">2</p>
              </div>
            </div>
          </div> */}
          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">Booking Information</h1>
            <div className="flex flex-wrap items-center my-6 gap-6">
              <div className="flex w-full sm:w-[45%] md:w-[45%] lg:w-[15%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">NAME</strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">Christine Brooks</p>
              </div>
              <div className="flex w-full sm:w-[45%] md:w-[45%] lg:w-[20%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">Booking Reference</strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">jaypal yadav</p>
              </div>
              <div className="flex w-full sm:w-[90%] md:w-[30%] lg:w-[20%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">People</strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">2</p>
              </div>
            </div>
          </div>

          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">Travelling Information</h1>
            <div className="flex flex-wrap items-center gap-x-20 my-6">
              <div className="flex sm:w-[90%] md:w-[33%] lg:w-[10%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">Country</strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">India</p>
              </div>
              <div className="flex mt-4 md:mt-0 lg:mt-0 sm:w-[90%] md:w-[40%] lg:w-[15%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">Place</strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">Himachal Pradesh</p>
              </div>
            </div>
          </div>

          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">Hostel Information</h1>
            <div className="flex flex-wrap items-center gap-6 my-6">
              <div className="flex w-full sm:w-[45%] md:w-[45%] lg:w-[15%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">Hostel Name</strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">88 Backpackers</p>
              </div>
              <div className="flex w-full sm:w-[45%] lg:w-[20%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">Check in date</strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">04/02/24</p>
              </div>
              <div className="flex w-full sm:w-[45%] lg:w-[15%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">Check out date</strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">10/02/24</p>
              </div>
              <div className="flex w-full sm:w-[90%] lg:w-[40%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">Hostel Address</strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  2118 Thornridge Cir. Syracuse, Connecticut 35624
                </p>
              </div>
            </div>
          </div>

          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">Payment Information</h1>
            <div className="flex items-center gap-x-20 mt-6">
              <div className="flex w-[15%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">Payment</strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">2500/5000</p>
              </div>
            </div>
          </div>
          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">Status Information</h1>
            <div className="flex items-center gap-x-20 my-6">
              <div className="flex w-[15%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">Status</strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">Booked</p>
              </div>
            </div>
          </div>
          <div className="flex items-start justify-start">
            <Link
              href={"/superadmin/dashboard/booking"}
              className="text-white py-2 w-32 rounded bg-sky-blue-650 flex items-center justify-center"
            >
              Cancel
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingDetails;
