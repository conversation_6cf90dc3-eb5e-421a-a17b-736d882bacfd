 
"use client";
import { useState } from "react";
import { Plus } from "lucide-react";
import Link from "next/link";
import { FaRegTrashCan } from "react-icons/fa6";
import { FiEye } from "react-icons/fi";
import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight,
} from "react-icons/md";
import { TfiPencilAlt } from "react-icons/tfi";

// Function to split text into lines with a specific number of words per line
const splitTextByWords = (text, wordsPerLine) => {
  if (typeof text !== "string") return [text]; // Wrap non-string content in an array
  const words = text.split(" ");

  // If no words are found, return an empty array to avoid `.map` errors
  if (words.length === 0) return [];

  return words
    .reduce((acc, word, index) => {
      if (index % wordsPerLine === 0) acc.push([]);
      acc[acc.length - 1].push(word);
      return acc;
    }, [])
    .map((line) => line.join(" "));
};



const Help = () => {
  // eslint-disable-next-line no-unused-vars
  const [wordsPerLine, setWordsPerLine] = useState(13);

  const helpData = [
    {
      id: 1,
      name: (
        <div className="">
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">How we can</span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Assist you?</span>
        </div>
      ),
      feedback:
        "At MixDorm, we’re  here to make your experience as smooth and enjoyable as possible. Whether you need help with a booking, have questions about our services, or need support during your stay, our Help page provides resources and contact information to get you the assistance you need.",
    },
    {
      id: 2,
      name: (
        <div className="">
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Common</span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">issue and</span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">solutions</span>
        </div>
      ),
      feedback: (
        <div>
          <h1 className="text-base text-black font-bold dark:text-[#B6B6B6]">
            1. Booking Assistance
          </h1>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            <strong className="text-base text-black font-semibold dark:text-gray-200">
              Problem:
            </strong>
            I need to modify or cancel my Booking.
          </p>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            <strong className="text-base text-black font-semibold dark:text-gray-200">
              Solution:
            </strong>
            You can modify or cancel your booking by logging
            into your account on our <br/> website or app. For further assistance,
            please contact our customer support team.
          </p>
          <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
            2. Check-In And Check-Out
          </h1>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            <strong className="text-base text-black font-semibold dark:text-gray-200">
              Problem:
            </strong>
            I have a question about check-in or check-out times.
          </p>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            <strong className="text-base text-black font-semibold dark:text-gray-200">
              Solution:
            </strong>
            Standard check-in is at 12 noon, and check-out is at 10
            AM. Early check-in or <br/> late check-out may be available upon request.
            Contact the property directly for <br/> availability.
          </p>
          <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
            3. Lost And Found
          </h1>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            <strong className="text-base text-black font-semibold dark:text-gray-200">
              Problem:
            </strong>
            I lost something during my stay.
          </p>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            <strong className="text-base text-black font-semibold dark:text-gray-200">
              Solution:
            </strong>
            Report lost
            items to our customer support team as soon as possible. We will <br/> make
            every effort to locate and return lost belongings.
          </p>
          <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
          4. Technical
          Issues
          </h1>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            <strong className="text-base text-black font-semibold dark:text-gray-200">
              Problem:
            </strong>
            I’m having trouble using the website or app.
          </p>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            <strong className="text-base text-black font-semibold dark:text-gray-200">
              Solution:
            </strong>
            Try clearing your browser cache or updating the app. If
            the issue persists, <br/> contact our technical support team for help.
          </p>
        </div>
      ),
    },
    {
      id: 3,
      name: (
        <div>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Contact Us</span>
        </div>
      ),
      feedback:
        (
          <div> 
            <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]"> If you need help that isn’t covered here, our customer support team is ready to assist <br/> you:
            </p>
            <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">Email: <EMAIL></p>
          </div>
        )
    },
    {
      id: 4,
      name: (
        <div>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            Support Hours
          </span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            our customer
          </span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            support team
          </span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            is available
          </span>
        </div>
      ),
      feedback:
        <div>
          <p className="block text-gray-500 font-semibold text-sm dark:text-[#757575]">Monday to Friday: 9 AM - 6 PM
          <p className="block text-gray-500 font-semibold text-sm dark:text-[#757575]">Saturday and Sunday: 10 AM - 4 PM</p>

          <p className="block text-gray-500 font-semibold mt-2 text-sm dark:text-[#757575]">For urgent matters outside of these hours, please leave a message or send an email, <br/> and
          we will respond as soon as possible.</p>
</p>
        </div>
    },
  ];

  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <div className="flex items-center justify-between w-full">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Help
        </h2>
        <div className="w-[50%] gap-x-5 flex justify-end items-center">
          <Link
            href={"/superadmin/dashboard/help-add"}
            className="px-4 py-2 text-sm font-medium font-poppins text-white rounded flex items-center bg-sky-blue-650"
          >
            <Plus size={18} className="mr-1" /> Help
          </Link>
        </div>
      </div>

      <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border dark:border-none">
        <table className="w-full divide-y bg-white rounded-xl divide-gray-200 dark:bg-black">
          <thead>
            <tr className="w-full items-center justify-between">
              <th className="pr-14 py-6 bg-white text-center text-sm font-semibold font-poppins text-black uppercase dark:bg-black dark:text-[#B6B6B6]">
                TITLE TEXT
              </th>
              <th className="py-6 pr-10 md:pr-8 lg:pr-10 bg-white text-sm font-semibold text-center font-poppins text-black uppercase dark:bg-black dark:text-[#B6B6B6]">
                SUB TEXT
              </th>
              <th className="py-6  bg-white text-center text-sm font-poppins font-semibold text-black uppercase dark:bg-black dark:text-[#B6B6B6]">
                ACTION
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 border-x border-y dark:border-x-0 text-black/70 dark:text-[#757575]">
            {helpData.map((helpItem) => (
              <tr key={helpItem.id} className="w-full">
                <td className="whitespace-nowrap text-gray-500 text-sm font-medium font-poppins px-6 py-8 dark:text-[#757575]">
                  {helpItem.name}
                </td>
                <td className="pl-6 lg:pl-32 px-5 text-sm text-gray-500 font-medium font-poppins py-4 dark:text-[#757575]">
                  {splitTextByWords(helpItem.feedback, wordsPerLine)?.map(
                    (line, index) => (
                      <div key={index} className="block">
                        {line}
                      </div>
                    )
                  )}
                </td>
                <td className="py-6 md:py-6 lg:py-10 flex justify-end px-6">
                  <Link
                    href={"/superadmin/dashboard/help-details"}
                    className="border p-2 rounded-l-lg text-black/75 hover:text-blue-700 dark:text-[#757575] dark:hover:text-blue-700"
                  >
                    <FiEye />
                  </Link>
                  <Link
                    href={"/superadmin/dashboard/help-edit"}
                    className="border p-2 text-black/75 hover:text-yellow-400 dark:text-[#757575] dark:hover:text-yellow-400"
                  >
                    <TfiPencilAlt />
                  </Link>
                  <button className="p-2 border rounded-r-lg text-red-600">
                    <FaRegTrashCan />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

       <div className="flex justify-between items-center mt-5">
                    <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
                    <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
                      <a
                        href="#"
                        className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                      >
                        <span className="sr-only">Next Page</span>
                        <MdOutlineKeyboardArrowLeft />
                      </a>
            
                      <a
                        href="#"
                        className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                      >
                        <span className="sr-only">Next Page</span>
                        <MdOutlineKeyboardArrowRight />
                      </a>
                    </div>
                  </div>
    </div>
  );
};

export default Help;
