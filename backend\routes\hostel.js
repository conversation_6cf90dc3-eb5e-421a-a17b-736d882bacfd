import express from "express";
import { addNewHostel, listAllHostels, 
    editHostel, softDelete, addNewProperty, editProperty, 
    listOwnerProperties, checkPropertyExistsController, listPropertiesController ,getPropertyDetails,
    uploadPropertyFilesController, listFilteredPropertiesController,myPropertyCounts ,getTravellersVisited,
    } from "../controller/hostel.js";
import { checkAuth } from '../middleware/auth.js';
import upload from "../utills/upload.js";
const router = express.Router();

    // property apis
    router.post("/", checkAuth('add_property'), addNewProperty);
    router.put("/:id", checkAuth('update_property'), editProperty);
    router.get("/my-properties", checkAuth('list_my_properties'), listOwnerProperties);
    router.get("/my-properties-counts", checkAuth(''), myPropertyCounts);
    router.get('/travellers-visited/:propertyId', checkAuth('travellers_visited'), getTravellersVisited);
    router.get('/all', checkAuth("list_all_properties"), listFilteredPropertiesController);
    router.get("/list-all", listAllHostels);
    router.get("/:id",checkAuth('property_details'), getPropertyDetails);

    router.post('/check-property', checkAuth('check_property'), checkPropertyExistsController);
    router.get("/", checkAuth('list_properties'), listPropertiesController);
    router.post("/:id/document-add", upload, uploadPropertyFilesController);


    export default router;

    /**
     * @swagger
     * tags:
     *   name: property
     *   description: property management endpoints
     */

    /**
     * @swagger
     * /add:
     *   post:
     *     summary: Add a new hostel
     *     tags: [property]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               name:
     *                 type: string
     *                 required: true
     *               address:
     *                 type: object
     *                 properties:
     *                   lineOne:
     *                     type: string
     *                     required: true
     *                   lineTwo:
     *                     type: string
     *                   city:
     *                     type: string
     *                     required: true
     *                   state:
     *                     type: string
     *                     required: true
     *                   zip:
     *                     type: string
     *                     required: true
     *                   country:
     *                     type: string
     *                     required: true
     *               room:
     *                 type: number
     *                 required: true
     *               bed:
     *                 type: number
     *                 required: true
     *               offer:
     *                 type: string
     *               status:
     *                 type: string
     *               aboutUs:
     *                 type: string
     *               hostelImages:
     *                 type: array
     *                 items:
     *                   type: string
     *               rules:
     *                 type: string
     *               cancellationPolicies:
     *                 type: string
     *               generalPolicies:
     *                 type: string
     *               amenities:
     *                 type: array
     *                 items:
 *                   type: string
 *               isArchived:
 *                 type: boolean
 *               isDeleted:
 *                 type: boolean
 *               isActive:
 *                 type: boolean
 *     responses:
 *       '201':
 *         description: Hostel created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 name:
 *                   type: string
 *                 address:
 *                   type: object
 *                   properties:
 *                     lineOne:
 *                       type: string
 *                     lineTwo:
 *                       type: string
 *                     city:
 *                       type: string
 *                     state:
 *                       type: string
 *                     zip:
 *                       type: string
 *                     country:
 *                       type: string
 *                 room:
 *                   type: number
 *                 bed:
 *                   type: number
 *                 offer:
 *                   type: string
 *                 status:
 *                   type: string
 *                 aboutUs:
 *                   type: string
 *                 hostelImages:
 *                   type: array
 *                   items:
 *                     type: string
 *                 rules:
 *                   type: string
 *                 cancellationPolicies:
 *                   type: string
 *                 generalPolicies:
 *                   type: string
 *                 amenities:
 *                   type: array
 *                   items:
 *                     type: string
 *                 isArchived:
 *                   type: boolean
 *                 isDeleted:
 *                   type: boolean
 *                 isActive:
 *                   type: boolean
 *       '400':
 *         description: Missing required fields
 *         content:
 *           application/json:
 *             example:
 *               message: 'MISSING_FIELDS'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */

/**
 * @swagger
 * /property/update/{id}:
 *   put:
 *     tags:
 *       - Hostel
 *     summary: Update a hostel
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *           example: "60d0fe4f5311236168a109ca"
 *     requestBody:
 *       description: Hostel data to update (all fields are optional)
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 example: "Hostel Name"
 *               address:
 *                 type: object
 *                 properties:
 *                   lineOne:
 *                     type: string
 *                     example: "123 Main St"
 *                   lineTwo:
 *                     type: string
 *                     example: "Apt 4"
 *                   city:
 *                     type: string
 *                     example: "New York"
 *                   state:
 *                     type: string
 *                     example: "NY"
 *                   zip:
 *                     type: string
 *                     example: "10001"
 *                   country:
 *                     type: string
 *                     example: "USA"
 *               room:
 *                 type: integer
 *                 example: 5
 *               bed:
 *                 type: integer
 *                 example: 10
 *               offer:
 *                 type: string
 *                 example: "Summer Special"
 *               status:
 *                 type: string
 *                 example: "Open"
 *               aboutUs:
 *                 type: string
 *                 example: "We offer the best accommodation."
 *               hostelImages:
 *                 type: array
 *                 items:
 *                   type: string
 *                   example: "image_url"
 *               rules:
 *                 type: string
 *                 example: "No smoking"
 *               cancellationPolicies:
 *                 type: string
 *                 example: "Free cancellation within 24 hours"
 *               generalPolicies:
 *                 type: string
 *                 example: "No pets allowed"
 *               amenities:
 *                 type: array
 *                 items:
 *                   type: string
 *                   example: "Free Wi-Fi"
 *               isArchived:
 *                 type: boolean
 *                 example: false
 *               isDeleted:
 *                 type: boolean
 *                 example: false
 *               isActive:
 *                 type: boolean
 *                 example: true
 *     responses:
 *       200:
 *         description: Hostel updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Hostel'
 *       404:
 *         description: Hostel not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 * components:
 *   schemas:
 *     Hostel:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *         name:
 *           type: string
 *         address:
 *           type: object
 *           properties:
 *             lineOne:
 *               type: string
 *             lineTwo:
 *               type: string
 *             city:
 *               type: string
 *             state:
 *               type: string
 *             zip:
 *               type: string
 *             country:
 *               type: string
 *         room:
 *           type: integer
 *         bed:
 *           type: integer
 *         offer:
 *           type: string
 *         status:
 *           type: string
 *         aboutUs:
 *           type: string
 *         hostelImages:
 *           type: array
 *           items:
 *             type: string
 *         rules:
 *           type: string
 *         cancellationPolicies:
 *           type: string
 *         generalPolicies:
 *           type: string
 *         amenities:
 *           type: array
 *           items:
 *             type: string
 *         isArchived:
 *           type: boolean
 *         isDeleted:
 *           type: boolean
 *         isActive:
 *           type: boolean
 *         createdAt:
 *           type: string
 *         updatedAt:
 *           type: string
 *     Error:
 *       type: object
 *       properties:
 *         message:
 *           type: string
 *         status:
 *           type: integer
 */

/**
 * @swagger
 * /property/list-all:
 *   get:
 *     summary: List all hostels with pagination and filter
 *     tags: [property]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         required: true
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 10
 *         required: true
 *         description: Number of items per page
 *       - in: query
 *         name: filter
 *         schema:
 *           type: object
 *         required: false
 *         description: Filter conditions
 *     responses:
 *       '200':
 *         description: List of hostels
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       address:
 *                         type: object
 *                         properties:
 *                           lineOne:
 *                             type: string
 *                           lineTwo:
 *                             type: string
 *                           city:
 *                             type: string
 *                           state:
 *                             type: string
 *                           zip:
 *                             type: string
 *                           country:
 *                             type: string
 *                       room:
 *                         type: number
 *                       bed:
 *                         type: number
 *                       offer:
 *                         type: string
 *                       status:
 *                         type: string
 *                       aboutUs:
 *                         type: string
 *                       hostelImages:
 *                         type: array
 *                         items:
 *                           type: string
 *                       rules:
 *                         type: string
 *                       cancellationPolicies:
 *                         type: string
 *                       generalPolicies:
 *                         type: string
 *                       amenities:
 *                         type: array
 *                         items:
 *                           type: string
 *                       isArchived:
 *                         type: boolean
 *                       isDeleted:
 *                         type: boolean
 *                       isActive:
 *                         type: boolean
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *               pagination:
 *                 type: object
 *                 properties:
 *                   page:
 *                     type: integer
 *                   limit:
 *                     type: integer
 *                   totalPages:
 *                     type: integer
 *                   totalEvents:
 *                     type: integer
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */

/**
 * @swagger
 * /property/{id}:
 *   get:
 *     summary: Get a hostel by ID
 *     tags: [Property]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Hostel ID
 *     responses:
 *       '200':
 *         description: Hostel data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 _id:
 *                   type: string
 *                 name:
 *                   type: string
 *                 address:
 *                   type: object
 *                   properties:
 *                     lineOne:
 *                       type: string
 *                     lineTwo:
 *                       type: string
 *                     city:
 *                       type: string
 *                     state:
 *                       type: string
 *                     zip:
 *                       type: string
 *                     country:
 *                       type: string
 *                 room:
 *                   type: number
 *                 bed:
 *                   type: number
 *                 offer:
 *                   type: string
 *                 status:
 *                   type: string
 *                 aboutUs:
 *                   type: string
 *                 hostelImages:
 *                   type: array
 *                   items:
 *                     type: string
 *                 rules:
 *                   type: string
 *                 cancellationPolicies:
 *                   type: string
 *                 generalPolicies:
 *                   type: string
 *                 amenities:
 *                   type: array
 *                   items:
 *                     type: string
 *                 isArchived:
 *                   type: boolean
 *                 isDeleted:
 *                   type: boolean
 *                 isActive:
 *                   type: boolean
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *                 updatedAt:
 *                   type: string
 *                   format: date-time
 *       '404':
 *         description: Hostel not found
 *         content:
 *           application/json:
 *             example:
 *               message: 'HOSTEL_NOT_FOUND'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */

/**
 * @swagger
 * /property/delete/{id}:
 *   put:
 *     summary: Soft delete a hostel by ID
 *     tags: [property]
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: Hostel ID
 *     responses:
 *       '200':
 *         description: Hostel soft deleted
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: 'HOSTEL_SOFT_DELETED'
 *       '404':
 *         description: Hostel not found
 *         content:
 *           application/json:
 *             example:
 *               message: 'HOSTEL_NOT_FOUND'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */

/**
 * @swagger
 * tags:
 *   name: Property
 *   description: Property management endpoints
 */

/**
 * @swagger
 * /property:
 *   post:
 *     summary: Add a new property
 *     tags: [Property]
 *     x-isPublic: true
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/definitions/Property'
 *     responses:
 *       '201':
 *         description: Property created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/PropertyResponse'
 *       '403':
 *         description: Not authorized
 *         content:
 *           application/json:
 *             example:
 *               message: 'Not authorized'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */

/**
 * @swagger
 * /property/{id}:
 *   put:
 *     summary: Update a property
 *     tags: [Property]
 *     x-isPublic: true
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *           example: "60d0fe4f5311236168a109ca"
 *     requestBody:
 *       description: Property data to update (all fields are optional)
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/definitions/Property'
 *     responses:
 *       '200':
 *         description: Property updated successfully
 *         content:
 *           application/json:
 *             example:
 *               message: 'Property updated successfully'
 *       '404':
 *         description: Property not found
 *         content:
 *           application/json:
 *             example:
 *               message: 'Property not found'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */

/**
 * @swagger
 * /property/my-properties:
 *   get:
 *     summary: List all properties by owner with pagination and filter
 *     tags: [Property]
 *     x-isPublic: true
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         required: false
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 10
 *         required: false
 *         description: Number of items per page
 *       - in: query
 *         name: filter
 *         schema:
 *           type: object
 *           style: deepObject
 *           explode: true
 *         required: false
 *         description: Filter conditions
 *     responses:
 *       '200':
 *         description: List of properties
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 properties:
 *                   type: array
 *                   items:
 *                     $ref: '#/definitions/PropertyResponse'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     totalProperties:
 *                       type: integer
 *       '500':
 *         description: Internal server error
 */

/**
 * @swagger
 * /property/check-property:
 *   post:
 *     summary: Check if a property exists and add it
 *     tags: [Property]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 example: "Sample Property"
 *             required:
 *               - name
 *     responses:
 *       '200':
 *         description: Property added
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/PropertyResponse'
 *       '404':
 *         description: Property not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Property not found
 *       '500':
 *         description: Internal server error
 */

/**
 * @swagger
 * /property:
 *   get:
 *     summary: List all properties with pagination and filter
 *     tags: [Property]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         required: false
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 10
 *         required: false
 *         description: Number of items per page
 *       - in: query
 *         name: filter
 *         schema:
 *           type: object
 *           style: deepObject
 *           explode: true
 *         required: false
 *         description: Filter conditions
 *     responses:
 *       '200':
 *         description: List of properties
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 properties:
 *                   type: array
 *                   items:
 *                     $ref: '#/definitions/PropertyResponse'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     totalProperties:
 *                       type: integer
 *       '500':
 *         description: Internal server error
 */

/**
 * @swagger
 * /property/all:
 *   get:
 *     summary: List properties with specific filters
 *     tags: [Property]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: rate
 *         schema:
 *           type: number
 *         description: Minimum star rating
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [verified, unverified]
 *         description: Property verification status
 *       - in: query
 *         name: country
 *         schema:
 *           type: string
 *         description: Country filter
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           enum: [new hostel, avgRating]
 *         description: Sort properties by 'new hostel' (recently added) or 'avgRating' (highest average rating)
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of properties per page
 *     responses:
 *       200:
 *         description: A list of properties
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 properties:
 *                   type: array
 *                   items:
 *                     $ref: '#/definitions/PropertyResponse'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     totalProperties:
 *                       type: integer
 *       500:
 *         description: Internal Server Error
*/

// schema 

/**
 * @swagger
 * definitions:
 *   Property:
 *     type: object
 *     required:
 *       - propertyOwner
 *       - type
 *       - name
 *       - address
 *     properties:
 *       propertyOwner:
 *         type: string
 *         description: User ID reference of the property owner
 *       type:
 *         type: string
 *         description: Type of the property
 *       number:
 *         type: string
 *         description: Property number
 *       name:
 *         type: string
 *         description: Property name
 *       address:
 *         type: object
 *         properties:
 *           lineOne:
 *             type: string
 *             description: Address line one
 *           lineTwo:
 *             type: string
 *             description: Address line two
 *           city:
 *             type: string
 *             description: City
 *           state:
 *             type: string
 *             description: State
 *           country:
 *             type: string
 *             description: Country
 *           zipcode:
 *             type: number
 *             description: Zip code
 *       contact:
 *         type: number
 *         description: Contact number
 *       location:
 *         type: object
 *         properties:
 *           type:
 *             type: string
 *             description: Geo type
 *           coordinates:
 *             type: array
 *             items:
 *               type: number
 *             description: Coordinates array [longitude, latitude]
 *       photos:
 *         type: array
 *         items:
 *           type: object
 *           properties:
 *             title:
 *               type: string
 *               description: Photo title
 *             url:
 *               type: string
 *               description: Photo URL
 *       aboutUs:
 *         type: string
 *         description: About us information
 *       licenceProof:
 *         type: array
 *         items:
 *           type: object
 *           properties:
 *             title:
 *               type: string
 *               description: Licence proof title
 *             url:
 *               type: string
 *               description: Licence proof URL
 *       addressProof:
 *         type: array
 *         items:
 *           type: object
 *           properties:
 *             title:
 *               type: string
 *               description: Address proof title
 *             url:
 *               type: string
 *               description: Address proof URL
 *       isPropertyVerified:
 *         type: boolean
 *         description: Is the property verified
 *       verifiedDate:
 *         type: string
 *         format: date
 *         description: Date when the property was verified
 *       verifiedBy:
 *         type: string
 *         description: User ID of the person who verified the property
 *       contactUs:
 *         type: object
 *         properties:
 *           email:
 *             type: string
 *             description: Contact email
 *           contactAddress:
 *             type: object
 *             properties:
 *               latitude:
 *                 type: string
 *                 description: Latitude of contact address
 *               longitude:
 *                 type: string
 *                 description: Longitude of contact address
 *           phoneNumber:
 *             type: string
 *             description: Contact phone number
 *           whatsApp:
 *             type: string
 *             description: WhatsApp number
 *       rating:
 *         type: object
 *         description: Rating of the property
 *       amenities:
 *         type: array
 *         items:
 *           type: string
 *           description: List of amenities
 *       isRejected:
 *         type: boolean
 *         description: Is the property rejected
 *       rejectionDate:
 *         type: string
 *         format: date
 *         description: Date when the property was rejected
 *       isDeleted:
 *         type: boolean
 *         description: Is the property deleted
 *       isActive:
 *         type: boolean
 *         description: Is the property active
 *       rules:
 *         type: object
 *         description: Property rules
 *       createdAt:
 *         type: string
 *         format: date-time
 *         description: Timestamp when the property was created
 *       updatedAt:
 *         type: string
 *         format: date-time
 *         description: Timestamp when the property was last updated
 */

/**
 * @swagger
 * definitions:
 *   PropertyResponse:
 *     type: object
 *     required:
 *       - propertyOwner
 *       - type
 *       - name
 *       - address
 *     properties:
 *       propertyOwner:
 *         type: string
 *         description: User ID reference of the property owner
 *       type:
 *         type: string
 *         description: Type of the property
 *       number:
 *         type: string
 *         description: Property number
 *       name:
 *         type: string
 *         description: Property name
 *       address:
 *         type: object
 *         properties:
 *           lineOne:
 *             type: string
 *             description: Address line one
 *           lineTwo:
 *             type: string
 *             description: Address line two
 *           city:
 *             type: string
 *             description: City
 *           state:
 *             type: string
 *             description: State
 *           country:
 *             type: string
 *             description: Country
 *           zipcode:
 *             type: number
 *             description: Zip code
 *       contact:
 *         type: number
 *         description: Contact number
 *       location:
 *         type: object
 *         properties:
 *           type:
 *             type: string
 *             description: Geo type
 *           coordinates:
 *             type: array
 *             items:
 *               type: number
 *             description: Coordinates array [longitude, latitude]
 *       photos:
 *         type: array
 *         items:
 *           type: object
 *           properties:
 *             title:
 *               type: string
 *               description: Photo title
 *             url:
 *               type: string
 *               description: Photo URL
 *       aboutUs:
 *         type: string
 *         description: About us information
 *       licenceProof:
 *         type: array
 *         items:
 *           type: object
 *           properties:
 *             title:
 *               type: string
 *               description: Licence proof title
 *             url:
 *               type: string
 *               description: Licence proof URL
 *       addressProof:
 *         type: array
 *         items:
 *           type: object
 *           properties:
 *             title:
 *               type: string
 *               description: Address proof title
 *             url:
 *               type: string
 *               description: Address proof URL
 *       isPropertyVerified:
 *         type: boolean
 *         description: Is the property verified
 *       verifiedDate:
 *         type: string
 *         format: date
 *         description: Date when the property was verified
 *       verifiedBy:
 *         type: string
 *         description: User ID of the person who verified the property
 *       contactUs:
 *         type: object
 *         properties:
 *           email:
 *             type: string
 *             description: Contact email
 *           contactAddress:
 *             type: object
 *             properties:
 *               latitude:
 *                 type: string
 *                 description: Latitude of contact address
 *               longitude:
 *                 type: string
 *                 description: Longitude of contact address
 *           phoneNumber:
 *             type: string
 *             description: Contact phone number
 *           whatsApp:
 *             type: string
 *             description: WhatsApp number
 *       rating:
 *         type: object
 *         description: Rating of the property
 *       amenities:
 *         type: array
 *         items:
 *           type: string
 *           description: List of amenities
 *       isRejected:
 *         type: boolean
 *         description: Is the property rejected
 *       rejectionDate:
 *         type: string
 *         format: date
 *         description: Date when the property was rejected
 *       isDeleted:
 *         type: boolean
 *         description: Is the property deleted
 *       isActive:
 *         type: boolean
 *         description: Is the property active
 *       rules:
 *         type: object
 *         description: Property rules
 *       createdAt:
 *         type: string
 *         format: date-time
 *         description: Timestamp when the property was created
 *       updatedAt:
 *         type: string
 *         format: date-time
 *         description: Timestamp when the property was last updated
 */
/**
 * @swagger
 * /property/my-properties-counts:
 *   get:
 *     summary: Get the count of properties owned by the authenticated user
 *     tags: [Property]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       '200':
 *         description: Number of properties owned by the user
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 data:
 *                   type: number
 *                   example: 10
 *       '401':
 *         description: Unauthorized user or invalid token
 *         content:
 *           application/json:
 *             example:
 *               message: 'Unauthorized'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */
/**
 * @swagger
 * tags:
 *   name: Property
 *   description: Property-related APIs
 */
/**
 * @swagger
 * /property/travellers-visited/{propertyId}:
 *   get:
 *     summary: Get count of travelers who visited a specific property
 *     tags: [Property]
 *     description: Returns the count of unique travelers who visited the property based on completed bookings.
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *           format: ObjectId
 *         description: The ID of the property to get the visitor count for.
 *     responses:
 *       200:
 *         description: Successful response
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 visitorCount:
 *                   type: integer
 *                   example: 15
 *       400:
 *         description: Invalid property ID
 *       500:
 *         description: Internal Server Error
 */
