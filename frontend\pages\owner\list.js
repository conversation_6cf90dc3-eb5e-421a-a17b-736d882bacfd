import React, { useState, useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/router";
import { getItemLocalStorage } from "@/utils/browserSetting";
import ExistingList from "@/components/ownerFlow/existingListing";


const Listing = () => {
  const router = useRouter();

  const [existList, setExistList] = useState(false);
  const [userName, setUserName] = useState("");

  useEffect(() => {
    const name = getItemLocalStorage("name");
    if (name) setUserName(name);
  }, []);

  const handleList = () => {
    setExistList(true);
  };

  return (
    <>
      {!existList ? (
        <div className="min-h-screen flex items-center justify-center bg-[#F7F7F7] w-full sm:pb-[10rem] pt-[3rem]">
          <div className="w-[95%] max-w-3xl p-6 mb-10 bg-white shadow-md rounded-3xl md:max-w-2xl md:p-14">
            <h2 className="py-8 text-2xl font-bold text-gray-800">
              👋 Hi <span className="text-primary-blue">{userName}</span>
            </h2>
            <h4 className="font-manrope font-normal text-base"> Manage all your listings here. </h4>

            <div className="sm:my-10 my-5">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/addlist.svg`}
                alt="Illustration"
                width={349}
                height={222}
                className="mx-auto sm:w-[349px] sm:h-[222px] w-[250px] h-auto"
                loading="lazy"
              />
            </div>
            <div className="flex justify-center">
              <h1 className="my-8 sm:text-[30px] xs:text-2xl text-lg font-bold text-center text-black font-manrope sm:w-[70%] w-full leading-normal">
                No Listing, Please <span className="text-[#40E0D0]">Add</span>{" "}
                Your Listing
              </h1>
            </div>
            <button
              className="w-full mt-5 sm:mb-7 mb-5 bg-[#40E0D0] font-semibold text-black py-3 rounded-4xl hover:bg-[#40E0D0] transition duration-200 sm:text-base text-sm"
              onClick={handleList}
            >
              Existing OTA Listing
            </button>
            <button
              className={`w-full bg-[#EEEEEE] text-black font-semibold py-3 rounded-4xl sm:text-base text-sm`}
              onClick={() => router.push("/owner/add-property")}
            >
              Add New Listing
            </button>
          </div>
        </div>
      ) : (
        <ExistingList />
      )}
    </>
  );
};

export default Listing;
