/* eslint-disable react/jsx-key */
import React, { useEffect, useRef, useState, Fragment } from "react";
import Image from "next/image";
import dynamic from "next/dynamic";
import { deletecheckInApi, webCheckInApi } from "@/services/ownerflowServices";
import { getItemLocalStorage } from "@/utils/browserSetting";
import toast from "react-hot-toast";
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
  Transition,
} from "@headlessui/react";
import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/react";
import { Trash, MoreVertical, Pencil, Eye } from "lucide-react";
import Pagination from "@/components/common/commonPagination";
import Head from "next/head";

const Manualwebcheckin = dynamic(
  () => import("@/components/ownerFlow/dashboard/manualwebcheckin"),
  {
    ssr: false,
  }
);

const Editwebcheckin = dynamic(
  () => import("@/components/ownerFlow/dashboard/editwebcheckin"),
  {
    ssr: false,
  }
);

const Viewchecking = dynamic(
  () => import("@/components/ownerFlow/dashboard/viewChecking"),
  {
    ssr: false,
  }
);

const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

const Filter = dynamic(() => import("../../../components/model/filter"), {
  ssr: false,
});

const Webcheckin = () => {
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [checkInData, setCheckInData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [editId, setEditId] = useState(null);
  const storedId = getItemLocalStorage("hopid");
  const isFirstRender = useRef(null);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalData, setTotalData] = useState();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const closefilterModal = () => setIsModalOpen(false);
  const [openAddcheckin, setOpenAddcheckin] = useState(false);
  const closecheckinModal = () => setOpenAddcheckin(false);
  const [openEditcheckin, setopenEditcheckin] = useState(false);
  const closecheckineditModal = () => setopenEditcheckin(false);
  const [openViewcheckin, setopenViewcheckin] = useState(false);
  const closecheckinviewModal = () => setopenViewcheckin(false);

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const fetchcheckInData = async (id) => {
    setLoading(true);
    try {
      const response = await webCheckInApi(id, currentPage, itemsPerPage);
      setCheckInData(response?.data?.data?.checkInDetails);
      setTotalPages(response?.data?.data?.pagination?.totalPages);
      setTotalData(response?.data?.data?.pagination?.totalDocuments);
    } catch (error) {
      console.error("Error fetching review data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!isFirstRender.current) {
      fetchcheckInData(storedId);
    } else {
      isFirstRender.current = false;
    }
  }, [storedId, currentPage, itemsPerPage]);

  const handleDelete = async (id) => {
    setLoading(true);
    try {
      const response = await deletecheckInApi(id);
      if (response?.data?.status) {
        toast.success(response?.data?.message);
        fetchcheckInData(storedId);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const updateCheckinList = () => {
    fetchcheckInData(storedId);
  };

  return (
    <>
      <Head>
        <title>Web Check-In Management | Mixdorm</title>
      </Head>
      <Loader open={loading} />

      <section className="w-full">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-medium text-gray-800">Web e checking</h2>
          <div className="flex sm:gap-2 gap-1 relative">
            <button
              className="sm:flex block items-center text-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-primary-blue font-medium"
              onClick={() => setIsModalOpen(true)}
            >
              <Image
                className="sm:mr-2 mx-auto"
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/filter.svg`}
                width={20}
                height={20}
              />
              Filter
            </button>
            <button
              className="flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-primary-blue font-medium"
              onClick={() => setOpenAddcheckin(true)}
            >
              Add Checking
            </button>
          </div>
        </div>
        <div className="w-full mt-5">
          <div className="w-full">
            <div className="overflow-x-auto mt-4 rounded-md">
              <table className="min-w-full border">
                <thead>
                  <tr className="bg-gray-100 border-b text-nowrap">
                    <th className="p-4 text-xs font-bold text-[#000000] text-left">
                      Room Number
                    </th>
                    <th className="p-4 text-xs font-bold text-[#000000] text-left">
                      Name
                    </th>
                    <th className="p-4 text-xs font-bold text-[#000000] text-left">
                      Check In
                    </th>
                    <th className="p-4 text-xs font-bold text-[#000000] text-left">
                      Check Out
                    </th>
                    <th className="p-4 text-xs font-bold text-[#000000] text-left">
                      Aadhar Card
                    </th>
                    <th className="p-4 text-xs font-bold text-[#000000] text-left">
                      Coming From
                    </th>
                    <th className="p-4 text-xs font-bold text-[#000000] text-left">
                      Going For
                    </th>
                    <th className="p-4 text-xs font-bold text-[#000000] text-left">
                      Status
                    </th>
                    <th className="p-4 text-xs font-bold text-[#000000] text-left">
                      Action
                    </th>
                  </tr>
                </thead>
                <tbody className="border">
                  {checkInData?.map((data, index) => (
                    <tr key={index}>
                      <td className="p-3 text-xs text-[#000]">
                        {data?.refNumber}
                      </td>
                      <td className="p-3 text-xs text-[#000]">
                        {data?.guestDetails?.name}
                      </td>
                      <td className="p-3 text-xs text-[#000]">
                        {data?.checkIn
                          ? (() => {
                              const date = new Date(data?.checkIn);
                              const day = String(date.getDate()).padStart(
                                2,
                                "0"
                              );
                              const month = String(
                                date.getMonth() + 1
                              ).padStart(2, "0");
                              const year = date.getFullYear();
                              return `${day}/${month}/${year}`;
                            })()
                          : ""}
                      </td>
                      <td className="p-3 text-xs text-[#000]">
                        {data?.checkOut
                          ? (() => {
                              const date = new Date(data?.checkOut);
                              const day = String(date.getDate()).padStart(
                                2,
                                "0"
                              );
                              const month = String(
                                date.getMonth() + 1
                              ).padStart(2, "0");
                              const year = date.getFullYear();
                              return `${day}/${month}/${year}`;
                            })()
                          : ""}
                      </td>
                      <td className="p-3 text-xs text-[#000]">
                        <Image
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/profile.png`}
                          alt="profile"
                          title="profile"
                          width={32}
                          height={32}
                          className="object-cover"
                          loading="lazy"
                        />
                      </td>
                      <td className="p-3 text-xs text-[#000]">
                        {data?.fromLocation}
                      </td>
                      <td className="p-3 text-xs text-[#000]">
                        {data?.toLocation}
                      </td>
                      <td className="p-3 text-xs text-[#000]">
                        <button className="inline-block px-2 py-1 text-xs whitespace-nowrap rounded-full bg-[#FEEDD8] text-[#F9A63A] capitalize">
                          {data?.status}
                        </button>
                      </td>
                      <td className="p-4 relative">
                        <Menu
                          as="div"
                          className="relative inline-block text-left"
                        >
                          <div>
                            <MenuButton>
                              <MoreVertical
                                aria-hidden="true"
                                size={16}
                              ></MoreVertical>
                            </MenuButton>
                          </div>

                          <MenuItems
                            // transition
                            // ref={(el) => {
                            //   if (el) {
                            //     const rect = el.getBoundingClientRect();
                            //     const windowHeight = window.innerHeight;
                            //     if (rect.bottom > windowHeight) {
                            //       el.classList.add("bottom-full", "mb-2"); // Open upwards
                            //       el.classList.remove("mt-2"); // Remove default margin if needed
                            //     } else {
                            //       el.classList.remove("bottom-full", "mb-2"); // Default downwards
                            //       el.classList.add("mt-2"); // Maintain default spacing
                            //     }
                            //   }
                            // }}
                            transition
                            // ref={(el) => {
                            //   if (el) {
                            //     const rect = el.getBoundingClientRect();
                            //     const windowHeight = window.innerHeight;
                            //     const isLastItem =
                            //       index === checkInData.length - 1;
                            //     const isOnlyItem = checkInData.length === 1;

                            //     // Clear any previously applied classes
                            //     el.classList.remove(
                            //       "bottom-full",
                            //       "mb-2",
                            //       "mt-2",
                            //       "top-1/2",
                            //       "-translate-y-1/2"
                            //     );

                            //     if (isOnlyItem) {
                            //       // Center the dropdown vertically in the viewport
                            //       el.classList.add(
                            //         "top-1/2",
                            //         "-translate-y-1/2"
                            //       );
                            //     } else if (
                            //       isLastItem ||
                            //       rect.bottom > windowHeight
                            //     ) {
                            //       el.classList.add("bottom-full", "mb-2");
                            //     } else {
                            //       el.classList.add("mt-2");
                            //     }
                            //   }
                            // }}
                            ref={(el) => {
                              if (el) {
                                const rect = el.getBoundingClientRect();
                                const windowHeight = window.innerHeight;
                                const isLastItem =
                                  index === checkInData.length - 1;
                                const isSecondLastItem =
                                  index === checkInData.length - 2;
                                const isOnlyItem = checkInData.length === 1;
                                const hasMoreThanTwoItems =
                                  checkInData.length > 2;

                                // Clear previous classes
                                el.classList.remove(
                                  "bottom-full",
                                  "mb-2",
                                  "mt-2",
                                  "top-1/2",
                                  "-translate-y-1/2"
                                );

                                if (isOnlyItem) {
                                  el.classList.add(
                                    "top-1/2",
                                    "-translate-y-1/2"
                                  );
                                } else if (
                                  isLastItem ||
                                  (hasMoreThanTwoItems && isSecondLastItem) ||
                                  rect.bottom > windowHeight
                                ) {
                                  el.classList.add("bottom-full", "mb-2");
                                } else {
                                  el.classList.add("mt-2");
                                }
                              }
                            }}
                            className="absolute right-0 z-10 mt-2 w-max origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
                          >
                            <div>
                              <MenuItem>
                                <button
                                  href="#"
                                  className="px-4 py-2 text-sm w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-t-md"
                                  onClick={() => {
                                    setopenViewcheckin(true);
                                    setEditId(data?._id);
                                  }}
                                >
                                  <Eye size={16}></Eye>
                                  View
                                </button>
                              </MenuItem>
                              <MenuItem>
                                <button
                                  href="#"
                                  className="px-4 py-2 text-sm w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-t-md"
                                  onClick={() => {
                                    setopenEditcheckin(true);
                                    setEditId(data?._id);
                                  }}
                                >
                                  <Pencil size={16}></Pencil>
                                  Edit
                                </button>
                              </MenuItem>
                              <MenuItem>
                                <button
                                  onClick={() => {
                                    handleDelete(data?._id);
                                  }}
                                  className="px-4 py-2 text-sm text-red-600 w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-b-md"
                                >
                                  <Trash size={16} className="text-red-600" />{" "}
                                  <span>Delete</span>
                                </button>
                              </MenuItem>
                            </div>
                          </MenuItems>
                        </Menu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {/* {totalPages > 1 && (
                <div className='flex justify-center mt-4'>
                  <CustomPagination
                    currentPage={currentPage}
                    total={totalPages}
                    onPageChange={handlePageChange}
                  />
                </div>
              )} */}

              {checkInData?.length > 0 && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  totalItems={totalData || 0}
                  itemsPerPage={itemsPerPage}
                  onPageChange={handlePageChange}
                  onItemsPerPageChange={handleItemsPerPageChange}
                />
              )}
            </div>
          </div>
        </div>
      </section>

      {isModalOpen && (
        <Dialog
          open={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          className="relative z-50"
        >
          <DialogBackdrop
            transition
            className="fixed inset-0 bg-[#000000B2] transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
          />

          <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
            <div className="flex min-h-full justify-center p-4 text-center items-center sm:p-0">
              <DialogPanel
                transition
                className="relative transform overflow-hidden rounded-2xl bg-white text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-full sm:max-w-[490px] max-w-full w-full data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95"
              >
                <div className="bg-white sm:px-7 sm:pb-7 pt-3 p-3 pb-3">
                  <DialogTitle>
                    <h3 className="text-center text-black font-bold sm:text-lg text-sm">
                      Filter
                    </h3>
                  </DialogTitle>
                  <Filter closefilterModal={closefilterModal} />
                </div>
              </DialogPanel>
            </div>
          </div>
        </Dialog>
      )}

      <Transition show={openAddcheckin} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closecheckinModal}>
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <h2 className="page-title">Add Checking</h2>
                    <button
                      onClick={closecheckinModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <Manualwebcheckin
                      closecheckinModal={closecheckinModal}
                      updateCheckinList={updateCheckinList}
                    ></Manualwebcheckin>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      <Transition show={openEditcheckin} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-50"
          onClose={closecheckineditModal}
        >
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <h2 className="page-title">Edit Checking</h2>
                    <button
                      onClick={closecheckineditModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <Editwebcheckin
                      closecheckineditModal={closecheckineditModal}
                      updateCheckinList={updateCheckinList}
                      editId={editId}
                    ></Editwebcheckin>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      <Transition show={openViewcheckin} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-50"
          onClose={closecheckinviewModal}
        >
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <h2 className="page-title">View Checking</h2>
                    <button
                      onClick={closecheckinviewModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <Viewchecking
                      updateCheckinList={updateCheckinList}
                      editId={editId}
                    ></Viewchecking>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
};

export default Webcheckin;
