import mongoose from 'mongoose';

const paymentSchema = new mongoose.Schema({
  gusetUser:{
    type:String
  },
  user: {
    type: mongoose.Types.ObjectId,
  },
  eventId:{
    type: mongoose.Types.ObjectId,
  },
  paymentFor:{
    type:String
  },
  property:{
    type: mongoose.Types.ObjectId,
  },
  room:{
    type: mongoose.Types.ObjectId,
    ref:'rooms'
  },
  orderId: {
    type: String
  },
  paymentId: {
    type: String
  },
  signature: {
    type: String
  },
  amount: {
    type: Number
  },
  currency: {
    type: String
  },
  status: {
    type: String
  },
  paymentType: {
    type: String,
    required:true
  },
  checkIn:{
    type:Date
  },
  checkOut:{
    type:Date
  },
  isDeleted: { 
    type: Boolean, 
    default: false 
  }, 
  paymentDate:{
    type:Date
  }
}, {
  timestamps: true
});

const Payment = mongoose.model('Payment', paymentSchema);
export default Payment;
