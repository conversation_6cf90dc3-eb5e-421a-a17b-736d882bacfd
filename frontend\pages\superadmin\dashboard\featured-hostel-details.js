import Image from "next/image";
import Link from "next/link";
import React from "react";

const MobileDetails = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616] h-screen">
      <div className="flex items-center justify-between w-full ">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Featured Hostel Details
        </h2>
        
      </div>
      <div className="bg-white border rounded-xl mt-5 h-auto dark:bg-black dark:border-none">
        <div className="p-8 flex flex-col gap-y-6">
          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">
              Hostel Information
            </h1>
            <div className="flex flex-wrap items-center my-6 gap-6">
              <div className="flex w-full sm:w-[45%] md:w-[45%] lg:w-[15%] flex-col mb-0  lg:mb-5 ">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  HOSTEL NAME
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  Amsterdam
                </p>
              </div>

              <div className="flex w-full sm:w-[45%] md:w-[45%] lg:w-[15%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  Image
                </strong>
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`}
                  alt="Image"
                  className="w-20 h-12 mt-2 lg:sm-1 rounded-lg"
                  width={0}
                  height={0}
                />
              </div>
            </div>
          </div>

          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">
              Travelling Information
            </h1>
            <div className="flex flex-wrap items-center gap-x-20 my-6">
              <div className="flex sm:w-[90%] md:w-[33%] lg:w-[10%] flex-col mb-0 md:mb-3 lg:mb-4">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  Country
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  Netherlands
                </p>
              </div>
              <div className="flex mt-4 md:mt-0 lg:mt-0 mb-3 md:mb-0 lg:mb-4 sm:w-[90%] md:w-[80%] lg:w-[40%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  Key Features
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  On-Site Bar, Rooftop Terrace, Dorms & Private Rooms
                </p>
              </div>
            </div>
          </div>

          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">
              Payment Information
            </h1>
            <div className="flex items-center gap-x-20 mt-6">
              <div className="flex w-[15%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  Price
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  $28/Person
                </p>
              </div>
            </div>
          </div>
          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">
              Status Information
            </h1>
            <div className="flex items-center gap-x-20 mt-6">
              <div className="flex w-[15%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  Status
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  Top rated
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-start justify-start">
            <Link
              href={"/superadmin/dashboard/featured-hostel"}
              className="text-white py-2 w-32 rounded bg-sky-blue-650 flex items-center justify-center"
            >
              Cancel
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileDetails;
