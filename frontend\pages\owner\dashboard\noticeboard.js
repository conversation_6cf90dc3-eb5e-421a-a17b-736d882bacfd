/* eslint-disable no-irregular-whitespace */
import React, { useEffect, useRef, useState } from "react";
import { getNoticeA<PERSON> } from "@/services/ownerflowServices";
import Image from "next/image";

// import { IoCameraOutline } from "react-icons/io5";
// import { IoMdImages } from "react-icons/io";

import dynamic from "next/dynamic";
import { FaStar } from "react-icons/fa6";
import Link from "next/link";
import { CalendarDays } from "lucide-react";
import { PiUsersThree } from "react-icons/pi";
import { FaCarAlt } from "react-icons/fa";
import Head from "next/head";

const Chat = dynamic(() => import("@/components/ownerFlow/dashboard/chat"), {
  ssr: false,
});

const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

const Noticeboard = () => {
  const [chat, setChat] = useState(false);
  const [notices, setNotices] = useState([]);
  const [propertyReviewNotices, setPropertyReviewNotices] = useState([]);
  const [rideNotices, setRideNotices] = useState([]);
  const [eventNotices, setEventNotices] = useState([]);

  const [loading, setLoading] = useState(false);
  const isFirstRender = useRef(null);

  const handleChat = () => {
    setChat(true);
  };

  useEffect(() => {
    const propertyReviewData = notices.filter(
      (item) => item.type === "propertyReview"
    );
    const rideData = notices.filter((item) => item.type === "rides");
    const eventData = notices.filter((item) => item.type === "events");

    setPropertyReviewNotices(propertyReviewData);
    setRideNotices(rideData);
    setEventNotices(eventData);
  }, [notices]);

  console.log(
    "notices",
    notices,
    propertyReviewNotices,
    rideNotices,
    eventNotices
  );

  useEffect(() => {
    const fetchNotices = async () => {
      setLoading(true);
      try {
        const response = await getNoticeApi();
        setNotices(response?.data?.data);
      } catch (error) {
        console.error("Error fetching payment data:", error);
      } finally {
        setLoading(false);
      }
    };
    if (!isFirstRender.current) {
      fetchNotices();
    } else {
      isFirstRender.current = false;
    }
  }, []);

  return (
    <>
      <Head>
        <title>Manage Noticeboard Posts | Mixdorm</title>
      </Head>
      <Loader open={loading} />
      <div className=''>
        <section className='w-full'>
          <div className='flex items-center  justify-between'>
            <h2 className='text-xl font-medium text-gray-800'>Noticeboard</h2>
            <button
              className='flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-primary-blue font-medium'
              onClick={handleChat}
            >
              Chat
            </button>
          </div>
          {chat && (
            <div className='w-full mt-4 bg-white rounded-lg border border-[#EEEEEE]'>
              <Chat />
            </div>
          )}
        </section>

        {/* Noticeboard new Design  */}
        {!chat && (
          <section className='w-full bg-white rounded-lg mt-2 mb-5 border border-[#EEEEEE]'>
            {notices.map((notice) => (
              <>
                {notice?.type === "propertyReview" && (
                  <div className='px-4 py-6 pb-9 border-gray-200'>
                    <div className='flex gap-3'>
                      <div>
                        <Image 
                          src={notice?.actionBy?.profileImage}
                          width={56}
                          height={56}
                          alt={notice?.actionBy?.name?.first}
                          className='max-w-[44px] max-h-[44px] rounded-full'
                        />
                      </div>
                      <div>
                        <div>
                          <div className='bg-[#E6E6E6] px-5 py-1 text-black font-normal text-xs rounded-md w-fit'>
                            Review{" "}
                          </div>
                        </div>
                        <div className='mt-2'>
                          <div className='text-black font-bold text-sm flex gap-1 items-center'>
                            {" "}
                            {notice?.message} -{" "}
                            <p className='text-gray-400 font-normal flex gap-1 items-center text-sm'>
                              <FaStar className='text-yellow-300' size={14} />{" "}
                              {notice?.dynamicData?.rating}
                            </p>
                          </div>
                        </div>
                        <div>
                          <div className='relative bg-[#40E0D038] border border-[#40E0D0] sm:w-[400px] w-full max-w-[600px] p-5 rounded-xl mt-2.5'>
                            <div className='flex items-center mb-3.5'>
                              <Image 
                                src={notice?.actionBy?.profileImage}
                                alt='User Image'
                                className='w-[31px] h-[31px] rounded-full'
                              />
                              <div className='ml-3'>
                                <h3 className='text-[#2C2C2C] font-semibold text-xs'>
                                  {notice?.actionBy?.name?.first}
                                </h3>
                                <div className='flex items-center'>
                                  <p className='text-[#777777] text-xs'>
                                    Marketing Manager
                                  </p>
                                  <div className='ml-auto flex items-center'>
                                    {Array(5)
                                      .fill(false)
                                      .map((_, index) => (
                                        <span
                                          key={index}
                                          className={`text-sm ${
                                            index < notice?.dynamicData?.rating
                                              ? "text-[#FFC700]"
                                              : "text-[#FFEEC1]"
                                          }`}
                                        >
                                          ★
                                        </span>
                                      ))}
                                  </div>
                                </div>
                              </div>
                            </div>

                            <p className='text-black text-[13px] font-normal font-roboto'>
                              {notice?.dynamicData?.comment}{" "}
                              <Link
                                href='#'
                                className='text-black underline font-semibold font-roboto'
                              >
                                View Review
                              </Link>
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                {notice?.type === "rides" && (
                  <div className='sm:px-8 px-4 py-4 border-b border-gray-200'>
                    <div className='xs:flex block gap-3 relative'>
                      <div>
                        <Image 
                          src={notice?.actionBy?.profileImage}
                          width={56}
                          height={56}
                          alt={notice?.actionBy?.name?.first}
                          className='max-w-[44px] max-h-[44px] rounded-full'
                        />
                      </div>
                      <div className='w-full'>
                        <div className='w-full flex justify-between'>
                          <div className='bg-[#E6E6E6] px-5 py-1 text-black font-normal text-xs rounded-md w-fit'>
                            Mix Ride{" "}
                          </div>
                          <p className='text-[#AAACAE] text-sm font-normal'>
                            5m
                          </p>
                        </div>
                        <div className='mt-2'>
                          <div className='text-black font-bold text-[14px] flex gap-1 mb-1 items-center'>
                            {notice?.message}
                          </div>
                        </div>
                        <div className='mt-2 border border-[#D9D9D9] rounded-2xl w-[300px]'>
                          <div className='p-2 border-b border-gray-300 flex justify-between items-center'>
                            <div className='flex gap-2 items-center'>
                              <FaCarAlt
                                className='bg-[#4C3CB9] text-white p-[5px] rounded-lg'
                                size={36}
                              />
                              <Image 
                                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/profile.png`}
                                alt=''
                                className='w-[32px] h-[32px]'
                              />
                              <div>
                                <p className='text-[12px] text-black font-bold flex gap-1 items-center'>
                                  Rechel Sah{" "}
                                  <Image
                                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/green-check.svg`}
                                    width={12}
                                    height={12}
                                  />
                                </p>
                                <p className='text-[10px] text-gray-400 font-normal flex items-center'>
                                  <FaStar
                                    className='text-yellow-300'
                                    size={12}
                                  />{" "}
                                  <p className='mb-0 text-[10px] leading-loose ml-0.5'>
                                    4.5
                                  </p>
                                </p>
                              </div>
                            </div>
                            <div>
                              <p className='text-[15px] text-black font-bold'>
                                RS.{notice?.dynamicData?.price}
                              </p>
                            </div>
                          </div>
                          <div className='mt-2 p-2'>
                            <ol className=' overflow-hidden space-y-4'>
                              <li className='relative flex-1'>
                                <a
                                  href='https://pagedone.io/'
                                  className='flex font-medium w-full gap-2.5'
                                >
                                  <p className='font-bold text-[12.15px] min-w-[36px]'>
                                    {notice?.dynamicData?.time?.start}
                                  </p>
                                  <span className='w-4 h-4 bg-tranparent border-2 border-[#4C3CB9] rounded-full flex justify-center items-center text-sm text-white lg:w-5 lg:h-5 circle'></span>
                                  <div className='block'>
                                    <h4 className='text-black text-[12.15px] font-medium'>
                                      {
                                        notice?.dynamicData?.fromAddress
                                          ?.address
                                      }
                                    </h4>
                                    <span className='text-[12.15px] text-gray-300 font-light'>
                                      Kasol{" "}
                                    </span>
                                  </div>
                                </a>
                                <div className='absolute top-[20px] left-[55px] bg-[#00000080] w-[1px] h-[63px]'></div>
                              </li>
                              <span className='font-medium text-black/30 text-[12.15px] font-roboto'>
                                4h20
                              </span>
                              <li className='relative flex flex-1'>
                                <a className='flex font-medium w-max gap-2.5 text-nowrap'>
                                  <p className='font-bold text-[12.15px] min-w-[36px]'>
                                    {notice?.dynamicData?.time?.end}
                                  </p>
                                  <span className='w-4 h-4 border-2 border-[#FF7F50] rounded-full flex justify-center items-center text-sm text-indigo-600 lg:w-5 lg:h-5 circle'></span>
                                  <div className='block'>
                                    <h4 className='text-[12.15px] font-medium text-black'>
                                      {
                                        notice?.dynamicData?.destination
                                          ?.address
                                      }
                                    </h4>
                                    <span className='text-[12.15px] text-gray-300 font-light'>
                                      Bir{" "}
                                    </span>
                                  </div>
                                </a>
                                <div className='flex gap-1.5 w-full justify-end'>
                                  <Image
                                    className='w-[32px] h-[32px]'
                                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/review.svgphone.svg`}
                                    width={31}
                                    height={31}
                                  />
                                  <Image
                                    className='w-[32px] h-[32px]'
                                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/review.svgmessage.svg`}
                                    width={31}
                                    height={31}
                                  />
                                </div>
                              </li>
                            </ol>
                          </div>
                        </div>
                        <div className='mt-2 sm:absolute relative bottom-0 right-0'>
                          <button className='border border-[#40E0D0] bg-transparent text-black rounded px-3 py-2 text-[12px] font-normal hover:bg-[#40E0D0]'>
                            View Ride Detail
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                {notice?.type === "events" && (
                  <div>
                    <div className='relative'>
                      <Image
                        src={notice?.actionBy?.profileImage}
                        alt={notice?.actionBy?.name?.first}
                        width={56}
                        height={56}
                        className='w-14 h-14 object-cover rounded-full'
                        loading='lazy'
                      />
                    </div>
                    <div className='flex-1 pl-7'>
                      <h6 className='text-sm font-semibold text-black mb-2'>
                        {notice?.actionBy?.name?.first} - {notice?.title}
                        <span className='block text-[#7F7F7F] font-normal'>
                          {(() => {
                            const formattedDate = new Date(notice?.createdAt);
                            const day = String(
                              formattedDate.getDate()
                            ).padStart(2, "0");
                            const month = String(
                              formattedDate.getMonth() + 1
                            ).padStart(2, "0");
                            const year = formattedDate.getFullYear();

                            let hours = formattedDate.getHours();
                            const minutes = String(
                              formattedDate.getMinutes()
                            ).padStart(2, "0");
                            const ampm = hours >= 12 ? "PM" : "AM"; // Determine AM/PM

                            hours = hours % 12; // Convert to 12-hour format
                            hours = hours
                              ? String(hours).padStart(2, "0")
                              : "12"; // Adjust for '12 AM' case

                            return `${day}/${month}/${year} ${hours}:${minutes} ${ampm}`;
                          })()}
                        </span>
                      </h6>
                    </div>
                  </div>
                )}
                <div className='sm:px-8 px-4 py-4 border-b border-gray-200'>
                  <div className='xs:flex block gap-3 relative'>
                    <div>
                      <Image 
                        src='https://mixdorm-live.s3.ap-south-1.amazonaws.com/front-images/avatar.png'
                        alt=''
                        className='max-w-[44px] max-h-[44px] rounded-full'
                      />
                    </div>
                    <div className='w-full'>
                      <div className='w-full flex justify-between'>
                        <div className='bg-[#E6E6E6] px-2 py-1 text-black font-normal text-xs rounded-md w-fit'>
                          Mix Creator{" "}
                        </div>
                        <p className='text-[#AAACAE] text-sm font-normal'>5m</p>
                      </div>
                      <div className='mt-2'>
                        <div className='text-black font-bold text-[14px] flex gap-1 mb-1 items-center'>
                          Alex Lee - <span>Mix</span>{" "}
                          <span className='text-primary-blue'> Creator</span>{" "}
                          <p className='font-normal flex gap-1 items-center text-sm'>
                            Sent offer request to made monkey
                          </p>
                        </div>
                        <div className='text-black font-medium text-[14px] flex gap-1 items-center'>
                          {" "}
                          Service Offered :<span> Story Instagram </span>
                        </div>
                      </div>
                      <div className='mt-2 border border-gray-200 rounded-2xl w-max pr-3.5'>
                        <div className='flex gap-3'>
                          <div>
                            <Image 
                              className='rounded-s-2xl w-[120px] h-[138px]'
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/profile-full.png`}
                              alt='mixcreatorimg'
                            />
                          </div>
                          <div className='grid grid-cols-2 gap-2'>
                            <div className='py-3'>
                              <div className='border border-gray-300 w-full p-2 rounded-md'>
                                <p className='text-black text-[10px] font-bold font-roboto'>
                                  Followers
                                </p>
                                <p className='text-[#40E0D0] text-[14px] font-bold'>
                                  158.k
                                </p>
                              </div>
                              <div className='border border-gray-300 w-full p-2 rounded-md mt-2'>
                                <p className='text-black text-[10px] font-bold font-roboto'>
                                  Post
                                </p>
                                <p className='text-[#40E0D0] text-[14px] font-bold'>
                                  525462
                                </p>
                              </div>
                            </div>
                            <div className='py-3'>
                              <div className='border border-gray-300 w-full p-2 rounded-md'>
                                <p className='text-black text-[10px] font-bold font-roboto'>
                                  Average Likes
                                </p>
                                <p className='text-[#40E0D0] text-[14px] font-bold'>
                                  158.k
                                </p>
                              </div>
                              <div className='border border-gray-300 w-full p-2 rounded-md mt-2'>
                                <p className='text-black text-[10px] font-bold font-roboto'>
                                  Engagement rate
                                </p>
                                <p className='text-[#40E0D0] text-[14px] font-bold'>
                                  50%
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className='flex gap-2 mt-2 sm:absolute relative bottom-0 right-0'>
                        <button className='border border-[#40E0D0] bg-transparent text-black rounded px-3 py-2 text-[12px] font-normal hover:bg-[#40E0D0]'>
                          View Offer
                        </button>
                        <button className='border border-[#40E0D0] bg-transparent text-black rounded px-3 py-2 text-[12px] font-normal hover:bg-[#40E0D0]'>
                          View Profile
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                <div className='sm:px-8 px-4 py-4 border-b border-gray-200'>
                  <div className='xs:flex block gap-3 relative'>
                    <div>
                      <Image 
                        src='https://mixdorm-live.s3.ap-south-1.amazonaws.com/front-images/avatar.png'
                        alt=''
                        className='max-w-[44px] max-h-[44px] rounded-full'
                      />
                    </div>
                    <div>
                      <div>
                        <div className='bg-[#40E0D0] px-5 py-1 text-black font-normal text-xs rounded-md w-fit'>
                          Check in{" "}
                        </div>
                      </div>
                      <div className='mt-2'>
                        <div className='text-black font-bold text-[14px] flex gap-1 mb-1 items-center'>
                          Ravina - <span>Group</span>{" "}
                          <span className='font-normal'> check-in guest</span>{" "}
                        </div>
                        <div className='flex sm:gap-5 gap-3'>
                          <p className='text-[12px] font-normal text-gray-400'>
                            Booking Id{" "}
                            <span className='font-bold text-black'>
                              {" "}
                              #41263
                            </span>
                          </p>
                          <p className='text-[12px] font-normal text-gray-400'>
                            Total Payment{" "}
                            <span className='font-bold text-black'>
                              {" "}
                              Rs 5000
                            </span>
                          </p>
                          <p className='text-[12px] font-normal text-gray-400'>
                            Check In{" "}
                            <span className='font-bold text-black'> 14:32</span>
                          </p>
                          <p className='text-[12px] font-normal text-gray-400'>
                            OTA ( Source ){" "}
                            <span className='font-bold text-black'>
                              {" "}
                              Booking.com
                            </span>
                          </p>
                        </div>
                        <div className='mt-3'>
                          <div className='flex items-center relative'>
                            <div>
                              <div className='relative'>
                                <Image 
                                  className='w-[42px] h-[42px] rounded-full border-2 border-white'
                                  src='https://mixdorm-live.s3.ap-south-1.amazonaws.com/front-images/avatar.png'
                                  alt='Profile 1'
                                />
                                <Image 
                                  className='absolute -bottom-1 right-1 w-4 h-4 rounded-full object-cover'
                                  src='https://flagcdn.com/us.svg'
                                  alt='UK Flag'
                                />
                              </div>
                            </div>

                            <div className=''>
                              <div className='relative -ml-1.5'>
                                <Image 
                                  className='w-[42px] h-[42px] rounded-full border-2 border-white'
                                  src='https://mixdorm-live.s3.ap-south-1.amazonaws.com/front-images/avatar.png'
                                  alt='Profile 2'
                                />
                                <Image 
                                  className='absolute -bottom-1 right-1 w-4 h-4 rounded-full object-cover'
                                  src='https://flagcdn.com/in.svg'
                                  alt='India Flag'
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className='sm:px-8 px-4 py-4 border-b border-gray-200'>
                  <div className='flex gap-3'>
                    <div>
                      <Image 
                        src='https://mixdorm-live.s3.ap-south-1.amazonaws.com/front-images/avatar.png'
                        alt=''
                        className='max-w-[44px] max-h-[44px] rounded-full'
                      />
                    </div>
                    <div>
                      <div>
                        <div className='bg-[#40E0D0] px-5 py-1 text-black font-normal text-xs rounded-md w-fit'>
                          Check Out{" "}
                        </div>
                      </div>
                      <div className='mt-2'>
                        <div className='text-black font-bold text-[14px] flex gap-1 mb-1 items-center'>
                          Ravina - <span>Group</span>{" "}
                          <span className='font-normal'> check-out guest</span>{" "}
                        </div>
                        <div className='flex sm:gap-5 gap-3'>
                          <p className='text-[12px] font-normal text-gray-400'>
                            Booking Id{" "}
                            <span className='font-bold text-black'>
                              {" "}
                              #41263
                            </span>
                          </p>
                          <p className='text-[12px] font-normal text-gray-400'>
                            Payment Status{" "}
                            <span className='font-bold text-black'> Done</span>
                          </p>
                          <p className='text-[12px] font-normal text-gray-400'>
                            Check out{" "}
                            <span className='font-bold text-black'>
                              {" "}
                              11:00 AM
                            </span>
                          </p>
                        </div>
                        <div className='mt-3'>
                          <div className='flex items-center relative'>
                            <div>
                              <div className='relative'>
                                <Image 
                                  className='w-[42px] h-[42px] rounded-full border-2 border-white'
                                  src='https://mixdorm-live.s3.ap-south-1.amazonaws.com/front-images/avatar.png'
                                  alt='Profile 1'
                                />
                                <Image 
                                  className='absolute -bottom-1 right-1 w-4 h-4 rounded-full object-cover'
                                  src='https://flagcdn.com/us.svg'
                                  alt='UK Flag'
                                />
                              </div>
                            </div>

                            <div className=''>
                              <div className='relative -ml-1.5'>
                                <Image 
                                  className='w-[42px] h-[42px] rounded-full border-2 border-white'
                                  src='https://mixdorm-live.s3.ap-south-1.amazonaws.com/front-images/avatar.png'
                                  alt='Profile 2'
                                />
                                <Image 
                                  className='absolute -bottom-1 right-1 w-4 h-4 rounded-full object-cover'
                                  src='https://flagcdn.com/in.svg'
                                  alt='India Flag'
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className='sm:px-8 px-4 py-4 border-b border-gray-200'>
                  <div className='flex gap-3'>
                    <div>
                      <Image 
                        src='https://mixdorm-live.s3.ap-south-1.amazonaws.com/front-images/avatar.png'
                        alt=''
                        className='max-w-[44px] max-h-[44px] rounded-full'
                      />
                    </div>
                    <div>
                      <div>
                        <div className='bg-[#40E0D0] px-5 py-1 text-black font-normal text-xs rounded-md w-fit'>
                          New Booking{" "}
                        </div>
                      </div>
                      <div className='mt-2'>
                        <div className='text-black font-bold text-[14px] flex gap-1 mb-1 items-center'>
                          Elena Sah - <span>Recently</span>{" "}
                          <span className='font-normal'>
                            {" "}
                            Booked via Hostelworld
                          </span>{" "}
                        </div>
                        <div className='mt-2 border border-gray-300 p-2.5 rounded w-fit'>
                          <div className='flex gap-3'>
                            <Image 
                              className='w-[54px] h-[54px] rounded'
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/booking.png`}
                              alt=''
                            />
                            <div>
                              <p className='text-black font-bold text-sm'>
                                Elena Sah
                              </p>
                              <p className='mt-1 text-gray-400 font-normal text-[10px] flex gap-1 items-center'>
                                <CalendarDays
                                  className='text-primary-blue'
                                  size={10}
                                />{" "}
                                Jun 05, 2024, 2 nights
                              </p>
                              <p className='mt-1 text-gray-400 font-normal text-[10px] flex gap-1 items-center'>
                                <PiUsersThree
                                  className='text-primary-blue'
                                  size={10}
                                />
                                3 Guests
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className='sm:px-8 px-4 py-4 border-b border-gray-200'>
                  <div className='xs:flex block gap-3 relative'>
                    <div>
                      <Image 
                        src='https://mixdorm-live.s3.ap-south-1.amazonaws.com/front-images/avatar.png'
                        alt=''
                        className='max-w-[44px] max-h-[44px] rounded-full'
                      />
                    </div>
                    <div>
                      <div className='mt-2'>
                        <div className='bg-[#40E0D0] px-5 py-1 text-black font-normal text-xs rounded-md w-fit'>
                          Complaint{" "}
                        </div>
                        <div className='text-black font-bold text-[14px] flex gap-1 mb-1 items-center'>
                          Alex lee -{" "}
                          <span className='font-normal'>
                            {" "}
                            Your private room bed is not cleaned, please make it
                            clean and also change the bed.{" "}
                          </span>{" "}
                        </div>
                        <div className='mt-2 w-fit'>
                          <Image 
                            src='https://mixdorm.s3.ap-south-1.amazonaws.com/front-images/featured-hostel.png'
                            alt=''
                            className='w-[200px] h-[75px] rounded object-cover'
                          />
                        </div>
                        <div className='flex gap-2 mt-2 sm:absolute relative bottom-0 right-0'>
                          <button className='border border-primary-blue bg-transparent text-primary-blue rounded-lg px-3 py-1 text-[12px] 85px]'>
                            View complaint
                          </button>
                          <button className='border border-primary-blue bg-transparent text-primary-blue rounded-lg px-3 py-1 text-[12px] '>
                            Resolved
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            ))}
          </section>
        )}
      </div>

      <div className='fixed inset-0 bg-white/70 justify-center items-center top-20 left-28 hidden lg:flex'>
        <div className='bg-white p-6 rounded-2xl shadow-xl w-[650px] h-[466px] text-center  flex flex-col items-center justify-around z-20'>
          <Image
            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Coming-Soon.png`}
            alt='Coming Soon'
            width={300}
            height={200}
            className='w-[455px] h-[288px]'
          />
          <h2 className='text-lg font-medium mt-4 font-inter px-10'>
            Hang tight! This section is getting a refresh. We’ll be back soon
            with something{" "}
            <span className='text-primary-blue font-serif text-xl font-bold'>
              amazing!
            </span>
          </h2>
        </div>
      </div>
      <div className='lg:hidden fixed inset-0 bg-white/70 flex justify-center items-center md:left-48 p-4'>
        <div className='bg-white p-4 md:p-6 rounded-2xl shadow-xl w-full max-w-[90%] md:max-w-[500px] lg:w-[650px] lg:h-[466px] text-center flex flex-col items-center justify-around z-20'>
          <Image
            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Coming-Soon.png`}
            alt='Coming Soon'
            width={300}
            height={200}
            className='w-[300px] h-[180px] md:w-[400px] md:h-[250px] lg:w-[455px] lg:h-[288px]'
          />
          <h2 className='text-base md:text-lg font-medium mt-4 font-inter px-4 md:px-10'>
            Hang tight! This section is getting a refresh. We’ll be back soon
            with something{" "}
            <span className='text-primary-blue font-serif text-lg md:text-xl font-bold'>
              amazing!
            </span>
          </h2>
        </div>
      </div>
    </>
  );
};

// const TabList = ({ tabs, activeTab, handleClick }) => (
//   <div className="flex items-center justify-between mb-4">
//     <ul className="flex bg-[#F7F9FC] border border-gray-300 flex-wrap gap-x-2  rounded-lg">
//       {tabs.map((item) => (
//         <li key={item.id} className="relative ">
//           <a
//             className={`inline-block py-4 px-3 text-sm ${
//               activeTab === item.id ? "text-[#2F7BEB]" : "text-[#5D6679]"
//             } cursor-pointer`}
//             onClick={() => handleClick(item.id)}
//             href="#"
//           >
//             {item.label}
//           </a>
//           {activeTab === item.id && (
//             <div className="absolute w-5/6 h-[3px] rounded-full bottom-2 bg-[#2F7BEB] left-0 right-0 mx-auto"></div>
//           )}
//         </li>
//       ))}
//     </ul>
//   </div>
// );

// const Section = ({ title, children, showCircle }) => (
//   <div>
//     <h3 className="flex items-center mb-3 text-sm font-medium">{title}</h3>
//     <div>{children}</div>
//   </div>
// );

// const NoticeItem = ({ item, showCircle }) => (
//   <div className="flex items-center justify-between border border-[#E8F1FD] p-3 rounded-lg mb-2">
//     <div className="flex flex-wrap items-center gap-y-2">
//       {showCircle && (
//         <span className="w-2.5 h-2.5 bg-blue-500 rounded-full mr-2"></span>
//       )}
//       <Image
//         src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/profile.svg`}
//         width={40}
//         height={40}
//         alt={item.name}
//         className="w-10 h-10 mr-4 rounded-full"
//       />
//       <div className="flex flex-col">
//         <h4 className="text-sm font-semibold">{item.name}</h4>
//         <p className="text-xs text-gray-600">{item.details}</p>
//         <p className="text-xs text-gray-600">{item.status}</p>
//       </div>
//     </div>
//     <p className="text-sm text-[#2B303466]">{item.time}</p>
//   </div>
// );

export default Noticeboard;
