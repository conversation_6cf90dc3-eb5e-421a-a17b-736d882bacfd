import * as permissionService from '../services/permission.js';

// Create a new permission
const createPermissionController = async (req, res) => {
    try {
        const permission = await permissionService.createPermission(req.body);
        res.status(201).json({ success: true, data: permission });
    } catch (error) {
        res.status(500).json({ success: false, message: 'Error creating permission', error });
    }
};

// Get all permissions
const getAllPermissionsController = async (req, res) => {
    try {
        const permissions = await permissionService.getAllPermissions();
        res.status(200).json({ success: true, data: permissions });
    } catch (error) {
        res.status(500).json({ success: false, message: 'Error fetching permissions', error });
    }
};

// Get a specific permission by ID
const getPermissionByIdController = async (req, res) => {
    try {
        const permission = await permissionService.getPermissionById(req.params.id);
        if (!permission) {
            return res.status(404).json({ success: false, message: 'Permission not found' });
        }
        res.status(200).json({ success: true, data: permission });
    } catch (error) {
        res.status(500).json({ success: false, message: 'Error fetching permission', error });
    }
};

// Update a permission by ID
const updatePermissionController = async (req, res) => {
    try {
        const updatedPermission = await permissionService.updatePermission(req.params.id, req.body);
        if (!updatedPermission) {
            return res.status(404).json({ success: false, message: 'Permission not found' });
        }
        res.status(200).json({ success: true, data: updatedPermission });
    } catch (error) {
        res.status(500).json({ success: false, message: 'Error updating permission', error });
    }
};

// Delete a permission by ID
const deletePermissionController = async (req, res) => {
    try {
        const deletedPermission = await permissionService.deletePermission(req.params.id);
        if (!deletedPermission) {
            return res.status(404).json({ success: false, message: 'Permission not found' });
        }
        res.status(200).json({ success: true, message: 'Permission deleted' });
    } catch (error) {
        res.status(500).json({ success: false, message: 'Error deleting permission', error });
    }
};

export { createPermissionController, getAllPermissionsController, getPermissionByIdController, updatePermissionController, deletePermissionController };