import Image from "next/image";
import Link from "next/link";
import React from "react";

const BannerDetails = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616] h-screen">
      <div className="flex items-center justify-between w-full">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Banner Details
        </h2>
      </div>
      <div className="bg-white border rounded-xl mt-5 h-auto dark:bg-black dark:border-none">
        <div className="p-8 flex flex-col gap-y-6">
          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">
              Home Page Information
            </h1>
            <div className="flex flex-wrap items-center my-6 gap-6">
              <div className="flex w-full sm:w-[45%] md:w-[30%] lg:w-[30%] flex-col mb-0  lg:mb-5 ">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  Header
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  Header
                </p>
              </div>
              <div className="flex w-full sm:w-[45%] md:w-[53%] lg:w-[33%] flex-col  ">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  Title
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  Download app now banner
                </p>
              </div>

              <div className="flex w-full sm:w-[45%] md:w-[60%] lg:w-[50%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  Image
                </strong>
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`}
                  alt="Image"
                  className="w-32 h-32 mt-2 lg:sm-1 rounded-lg"
                  width={0}
                  height={0}
                />
              </div>
            </div>
          </div>

          <div className="flex items-start justify-start">
            <Link
              href={"/superadmin/dashboard/content-banner"}
              className="text-white py-2 w-32 rounded bg-sky-blue-650 flex items-center justify-center"
            >
              Cancel
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BannerDetails;
