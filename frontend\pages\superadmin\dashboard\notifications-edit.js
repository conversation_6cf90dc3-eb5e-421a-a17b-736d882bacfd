"use client";
import Link from "next/link";
import React from "react";


const editNotification = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth h-screen dark:bg-[#171616] ">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        Edit Notifications
      </h2>
      <div className="bg-white border flex justify-center mt-5  rounded-xl h-auto md:h-auto lg:h-[873px] px-4 md:px-5 lg:px-1 py-7 md:py-16 lg:py-8 dark:lg:h-auto dark:bg-black dark:border-none">
        <div className="bg-white w-full max-w-3xl dark:bg-black">
          <form>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div className="relative">
                <label className="block font-semibold text-sm font-poppins text-black/40 dark:text-[#B6B6B6]">
                Title
                </label>
                <input
                  type="text"
                  className="mt-1 p-2 w-full font-medium text-sm font-poppins rounded-md bg-[#EEF9FF] placeholder:text-black placeholder:text-medium dark:bg-transparent dark:placeholder:dark:text-[#757575]"
                  placeholder="River rafting in rishikesh"
                />
              </div>

              <div className="relative">
                <label className="block font-semibold text-sm font-poppins text-black/40 dark:text-[#B6B6B6]">
                  Message
                </label>
                <input
                  type="text"
                  className="mt-1 p-2 w-full font-medium text-sm font-poppins rounded-md bg-[#EEF9FF] placeholder:text-black placeholder:text-medium dark:bg-transparent dark:placeholder:dark:text-[#757575]"
                  placeholder="Amet minium mollet...."
                />
              </div>
              <div className="relative">
                <label className="block font-semibold text-sm font-poppins text-black/40 dark:text-[#B6B6B6]">
                  Time
                </label>
                <input
                  type="text"
                  className="mt-1 p-2 w-full font-medium text-sm font-poppins rounded-md bg-[#EEF9FF] placeholder:text-black placeholder:text-medium dark:bg-transparent dark:placeholder:dark:text-[#757575]"
                  placeholder="03:50"
                />
              </div>
              <div className="relative">
                <label className="block font-semibold text-sm font-poppins text-black/40 dark:text-[#B6B6B6]">
                  Date
                </label>
                <input
                  type="text"
                  className="mt-1 p-2 w-full font-medium text-sm font-poppins rounded-md bg-[#EEF9FF] placeholder:text-black placeholder:text-medium dark:bg-transparent dark:placeholder:dark:text-[#757575]"
                  placeholder="04/12/2024"
                />
              </div>


              <div className="relative">
                <label className="block font-semibold text-sm font-poppins text-black/40 dark:text-[#B6B6B6]">
                  Status
                </label>
                <input
                  type="text"
                  className="mt-1 p-2 w-full font-medium text-sm font-poppins rounded-md bg-[#EEF9FF] placeholder:text-black placeholder:text-medium dark:bg-transparent dark:placeholder:dark:text-[#757575]"
                  placeholder="Sent"
                />
              </div>
            </div>

            {/* Buttons */}
            <div className="flex flex-wrap items-center justify-center space-x-3 my-8">
              <Link href={"/superadmin/dashboard/notifications"}
                type="button"
                className="flex items-center justify-center py-2 border w-40 md:w-56 border-gray-300 text-black font-medium text-sm font-poppins rounded-md mt-2 dark:text-gray-200"
              >
                Cancel
              </Link>
              <button
                type="submit"
                className="px-6 py-2 w-40 md:w-56 bg-sky-blue-650 text-white font-medium text-sm font-poppins rounded-md mt-2"
              >
                Add
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default editNotification;
