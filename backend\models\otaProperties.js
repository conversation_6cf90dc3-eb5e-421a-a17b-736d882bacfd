import mongoose from 'mongoose';

const otaPropertiesSchema = new mongoose.Schema({
    id: Number,
    isPromoted: Boolean,
    hbid: Number,
    name: String,
    starRating: Number,
    overallRating: Object,
    ratingBreakdown: Object,
    latitude: Number,
    longitude: Number,
    isFeatured: Boolean,
    type: String,
    address1: String,
    address2: String,
    state: String,
    country: String,
    freeCancellationAvailable: Boolean,
    freeCancellationAvailableUntil: Date,
    freeCancellation: Object,
    lowestPricePerNight: Object,
    lowestPrivatePricePerNight: Object,
    lowestDormPricePerNight: Object,
    lowestAveragePricePerNight: Object,
    lowestAverageDormPricePerNight: Object,
    lowestAveragePrivatePricePerNight: Object,
    isNewProperty: Boolean, // Changed from isNew
    overview: String,
    isElevate: Boolean,
    hostelworldRecommends: Boolean,
    distance: Object,
    position: Number,
    hwExtra: Object,
    fabSort: Object,
    promotions: Array,
    stayRuleViolations: Array,
    veryPopular: Boolean,
    rooms: Array,
    images: Array,
    categories: Array,
    facilities: Array,
    state:String,
    country:String,
    isApproved:{
        type:Boolean,
        default:false
    }
}, {
    timestamps: true
});

const otaProperties = mongoose.model('otaProperties', otaPropertiesSchema);
export default otaProperties;
