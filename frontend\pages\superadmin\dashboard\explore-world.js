"use client";
import React from "react";
import { Plus } from "lucide-react";
import { FiEye } from "react-icons/fi";
import { TfiPencilAlt } from "react-icons/tfi";
import { FaRegTrashCan } from "react-icons/fa6";
import Image from "next/image";
import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight,
} from "react-icons/md";
import Link from "next/link";

const Explore = () => {
  const countryData = [
    {
      id: 1,
      country: "Japan",
      listedhostels: "252 Hostels",
      imageF: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`,
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`,
    },
    {
      id: 2,
      country: "Japan",
      listedhostels: "252 Hostels",
      imageF: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`,
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`,
    },
    {
      id: 3,
      country: "Japan",
      listedhostels: "252 Hostels",
      imageF: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`,
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`,
    },
    {
      id: 4,
      country: "Japan",
      listedhostels: "252 Hostels",
      imageF: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`,
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`,
    },
    {
      id: 5,
      country: "Japan",
      listedhostels: "252 Hostels",
      imageF: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`,
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`,
    },
    {
      id: 6,
      country: "Japan",
      listedhostels: "252 Hostels",
      imageF: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`,
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`,
    },
    {
      id: 7,
      country: "Japan",
      listedhostels: "252 Hostels",
      imageF: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`,
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`,
    },
    {
      id: 8,
      country: "Japan",
      listedhostels: "252 Hostels",
      imageF: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`,
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`,
    },
    {
      id: 9,
      country: "Japan",
      listedhostels: "252 Hostels",
      imageF: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`,
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`,
    },
    {
      id: 10,
      country: "Japan",
      listedhostels: "252 Hostels",
      imageF: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`,
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`,
    },
  ];

  return (
    <div className="w-full p-7 bg-sky-blue-20 lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px]  float-end  overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <div className="flex items-center justify-between w-full ">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Explore The World
        </h2>
        <div className="w-[30%] lg:w-[50%] gap-x-5 flex justify-end items-center">
          <Link href={"/superadmin/dashboard/explore-world-add"}
            className={` px-2 lg:px-4 py-2 text-sm font-normal text-white rounded relative flex justify-center items-center bg-sky-blue-650 `}
            type="button"
            
          >
            <Plus size={18} className="mr-1" /> Explore
          </Link>
        </div>
      </div>
      {/* <div className="bg-white border-b border-l border-r rounded-xl">
        <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border-y">
          <table className="min-w-full divide-y  bg-white rounded-xl divide-gray-200">
            <thead>
              <tr className=" ">
                <th className="px-5 pt-4 pb-6 bg-white text-left text-base font-semibold text-black uppercase tracking-wider">
                  COUNTRY NAME
                </th>

                <th className="pr-4 pt-4 pb-6 bg-white text-left text-base font-semibold text-black uppercase tracking-wider">
                  TOTAL LISTED HOSTELS
                </th>

                <th className=" pt-4 pb-6 bg-white text-center text-base font-semibold text-black uppercase tracking-wider">
                  Flag
                </th>

                <th className=" pl-8 pt-4 pb-6 bg-white text-left text-base font-semibold text-black uppercase tracking-wider">
                  IMAGE
                </th>

                <th className="pl-2 pt-4 pb-6 bg-white text-left text-base font-semibold text-black uppercase tracking-wider">
                  ACTION
                </th>
              </tr>
            </thead>


            <tbody className="divide-y divide-gray-200 border text-black/70">
              {countryData.map((banner) => (
                <tr key={banner.id}>
                  <td className="whitespace-nowrap px-5">{banner.country}</td>
                  <td className="whitespace-nowrap px-5">
                    {banner.listedhostels}
                  </td>
                  <td>
                    <button className=" font-medium py-1 rounded px-4 ml-3 ">
                      <Image
                        src={banner.imageF}
                        alt="Image"
                        className=" w-[52px] h-[52px]"
                        width={62}
                        height={62}
                      />
                    </button>
                  </td>
                  <td>
                    <button className="font-medium py-1 px-4 rounded ">
                      <Image
                        src={banner.image}
                        alt="Image"
                        className=" w-28 h-14"
                        width={110}
                        height={53}
                      />
                    </button>
                  </td>

                  <td className=" py-5 px-2 flex">
                    <button className=" border p-2 rounded-l-lg text-black/75 hover:text-blue-700">
                      <FiEye />
                    </button>
                    <button className=" border p-2 text-black/75 hover:text-yellow-300">
                      <TfiPencilAlt />
                    </button>
                    <button className=" p-2 border rounded-r-lg text-red-600">
                      <FaRegTrashCan />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div> */}
      <div className="bg-white border-b border-l border-r rounded-xl dark:bg-black border-none">
        <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border-y border-none">
          <table className="min-w-full divide-y bg-white rounded-xl divide-gray-200 dark:bg-black">
            <thead>
              <tr>
                <th className="px-5 pt-4 pb-6 bg-white text-left text-sm  font-semibold font-poppins text-black uppercase tracking-wider whitespace-nowrap dark:bg-black dark:text-[#B6B6B6]">
                  COUNTRY NAME
                </th>
                <th className="pr-4 pl-6 md:pl-4 lg:pl-1 pt-4 pb-6 bg-white text-left text-sm  font-semibold font-poppins text-black uppercase tracking-wider whitespace-nowrap dark:bg-black dark:text-[#B6B6B6]">
                  TOTAL LISTED HOSTELS
                </th>
                <th className="pt-4 pl-8 pr-2 md:pr-2 lg:pr-20 md:pl-8 lg:pl-0 pb-6 bg-white text-center text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  FLAG
                </th>
                <th className="pl-12 md:pl-8 lg:pl-16 pr-4 md:pr-0 lg:pr-0 pt-4 pb-6 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  IMAGE
                </th>
                <th className="pr-8 pt-4 pb-6 bg-white text-end text-sm font-semibold font-poppins text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  ACTION
                </th>
              </tr>
            </thead>

            <tbody className="divide-y divide-gray-200 border-y border-x dark:border-x-0 text-black/70">
              {countryData.map((banner) => (
                <tr key={banner.id}>
                  {/* Country Name */}
                  <td className="pl-12 lg:pl-10 whitespace-nowrap px-5 text-sm font-poppins font-medium dark:text-[#757575]">{banner.country}</td>

                  {/* Total Listed Hostels */}
                  <td className="whitespace-nowrap pl-12  lg:pl-10 pr-5 md:pr-5  lg:pr-5 text-sm font-poppins font-medium dark:text-[#757575]">
                    {banner.listedhostels}
                  </td>

     
                  <td className="text-center pr-0 pl-6 lg:pl-0 lg:pr-20">
                    <div className="flex justify-center items-center">
                      <Image
                        src={banner.imageF}
                        alt="Flag"
                        className="w-10 h-10  md:w-12 md:h-12 object-cover rounded-full"
                        width={52}
                        height={52}
                      />
                    </div>
                  </td>

                  {/* Main Image */}
                  <td className="">
                    <div className="flex justify-start items-center ml-4 sm:ml-6 pl-6 md:pl-2 lg:pl-0">
                      <Image
                        src={banner.image}
                        alt="Main Image"
                        className="w-28 h-14 sm:w-36 sm:h-20 rounded-md py-2"
                        width={110}
                        height={53}
                      />
                    </div>
                  </td>

                  {/* Action Buttons */}
                  <td className="py-5 px-2 pl-10 md:pl-10 lg:pl-0 flex justify-end">
                    <Link href={"/superadmin/dashboard/explore-world-details"} className="border p-2 rounded-l-lg text-black/75 hover:text-blue-700 dark:hover:text-blue-700 dark:text-[#757575]">
                      <FiEye />
                    </Link>
                    <Link href={"/superadmin/dashboard/explore-world-edit"} className="border p-2 text-black/75 hover:text-yellow-300 dark:hover:text-yellow-300 dark:text-[#757575]">
                      <TfiPencilAlt />
                    </Link>
                    <button className="p-2 border rounded-r-lg text-red-600">
                      <FaRegTrashCan />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* <div className="flex justify-between items-center mt-5">
        <div className="text-black/75 text-sm font-poppins font-medium">Showing 1-09 of 78</div>
        <div className="inline-flex items-center justify-center border rounded-xl bg-white">
          <a
            href="#"
            className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180"
          >
            <span className="sr-only">Next Page</span>
            <MdOutlineKeyboardArrowLeft />
          </a>

          <a
            href="#"
            className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180"
          >
            <span className="sr-only">Next Page</span>
            <MdOutlineKeyboardArrowRight />
          </a>
        </div>
      </div> */}
            <div className="flex justify-between items-center mt-5">
              <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
              <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
                <a
                  href="#"
                  className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                >
                  <span className="sr-only">Next Page</span>
                  <MdOutlineKeyboardArrowLeft />
                </a>
      
                <a
                  href="#"
                  className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                >
                  <span className="sr-only">Next Page</span>
                  <MdOutlineKeyboardArrowRight />
                </a>
              </div>
            </div>
    </div>
  );
};

export default Explore;
