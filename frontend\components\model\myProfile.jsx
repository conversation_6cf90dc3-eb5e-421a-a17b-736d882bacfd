import React, { useEffect, useRef, useState } from "react";
import Link from "next/link";
import {
  User,
  LogOut,
  Wallet,
  Edit,
  HelpCircle,
  List,
  ChevronDown,
  ChevronUp,
} from "lucide-react"; // Icons
import toast from "react-hot-toast";
import {
  getItemLocalStorage,
  removeItemLocalStorage,
} from "@/utils/browserSetting";
import { removeFirebaseToken } from "@/services/ownerflowServices";
import { useNavbar } from "../home/<USER>";
import { useRouter } from "next/router";
import { MdCardMembership } from "react-icons/md";

const MyProfile = ({
  isMenuOpen,
  toggleMenu,
  updateTokenState,
  updateRoleState,
}) => {
  const { updateUserStatus, updateUserRole } = useNavbar();
  const modalRef = useRef(null);

  // State to manage the open/close state of each section
  const [openSections, setOpenSections] = useState({
    services: false,
    company: false,
    help: false,
    account: false,
  });
  const router = useRouter();

  // Toggle function for each section
  const toggleSection = (section) => {
    setOpenSections((prevState) => ({
      ...prevState,
      [section]: !prevState[section],
    }));
  };
  // const openContactPopup = async() => {
  //   setIsContactPopupOpen(true);
  // };

  const handleLogout = async () => {
    removeItemLocalStorage("token");
    updateUserStatus("");
    updateUserRole("");
    removeItemLocalStorage("name");
    removeItemLocalStorage("role");
    removeItemLocalStorage("email");
    removeItemLocalStorage("contact");
    toggleMenu();
    updateTokenState();
    updateRoleState();
    toast.success("Logged out successfully");
    const payload = {
      token: getItemLocalStorage("FCT"),
      userId: getItemLocalStorage("id"),
    };
    try {
      await removeFirebaseToken(payload);
      console.log("FCM token removed successfully.");
      removeItemLocalStorage("FCT");
    } catch (error) {
      console.error("Error removing FCM token:", error);
    }
    removeItemLocalStorage("id");
    router.push("/");
  };

  // Close modal when clicking outside
  useEffect(() => {
    const handleOutsideClick = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        toggleMenu();
      }
    };

    if (isMenuOpen) {
      document.addEventListener("mousedown", handleOutsideClick);
    }

    return () => {
      document.removeEventListener("mousedown", handleOutsideClick);
    };
  }, [isMenuOpen, toggleMenu]);

  if (!isMenuOpen) return null;

  return (
    <div
      className={`fixed w-full h-[98%] sm:h-full sm:w-[400px] top-2 sm:top-[114px] right-0 sm:right-[120px] sm:left-auto left-2 
       lg:bottom-0 rounded-tl-2xl rounded-bl-2xl pl-[2px]   z-50 flex items-start justify-end sm:bg-transparent bg-black
        bg-opacity-[70%] animated ${
          isMenuOpen ? "sm:animate-none fadeInRight" : ""
        }`}
      // className="fixed w-[400px] top-[120px] right-[120px] left-auto z-50 flex items-start justify-end"
    >
      <div
        ref={modalRef}
        className="bg-white rounded-2xl w-[100%] sm:h-[83%] h-[99.7%]  max-w-full p-5 shadow-lg font-manrope"
      >
        <div className="flex flex-col items-center fancy_y_scroll overflow-y-scroll h-[97%] sm:h-[542px]">
          {/* Header with Brand */}
          <div className="flex justify-between items-center w-full mb-4">
            <span className="text-[#40E0D0] flex text-2xl font-extrabold mb-2">
              Mix<p className="text-black text-2xl font-extrabold ">Dorm</p>
            </span>
            <button
              onClick={toggleMenu}
              className="text-black hover:text-gray-600 transition duration-150 mr-[20px]"
            >
              ✕
            </button>
          </div>

          {/* Menu Items */}
          <ul className="space-y-5 text-left w-full">
            <li className="">
              <Link
                className="flex items-center gap-x-2 p-3 bg-[#D9F9F6] rounded-full hover:bg-primary-blue hover:text-white text-sm sm:text-base font-semibold cursor-pointer text-black"
                href="/my-profile?section=profile"
                onClick={toggleMenu}
              >
                <User size={20} /> My Profile
              </Link>
            </li>
          
            <li className="">
              <Link
                href="/my-profile?section=edit"
                className="flex items-center gap-x-2 p-3  bg-[#D9F9F6] rounded-full hover:bg-primary-blue hover:text-white text-sm sm:text-base font-semibold cursor-pointer text-black "
                onClick={toggleMenu}
              >
                <Edit size={20} /> Edit Details
              </Link>
            </li>
            <li className="">
              <Link
                href="/my-profile?section=membership"
                className="flex items-center gap-x-2 p-3  bg-[#D9F9F6] rounded-full hover:bg-primary-blue hover:text-white text-sm sm:text-base font-semibold cursor-pointer text-black "
                onClick={toggleMenu}
              >
                <MdCardMembership size={20} /> Membership
              </Link>
            </li>
            
            
            <li className="">
              <Link
                href="/my-profile?section=trips"
                className="flex items-center gap-x-2 p-3 bg-[#D9F9F6] rounded-full hover:bg-primary-blue hover:text-white text-sm sm:text-base font-semibold cursor-pointer text-black "
                onClick={toggleMenu}
              >
                <List size={20} /> My Trips
              </Link>
            </li>
            <li className=" ">
              <Link
                href="/my-profile?section=wallet"
                className="flex items-center gap-x-2 p-3 bg-[#D9F9F6] rounded-full hover:bg-primary-blue hover:text-white text-sm sm:text-base font-semibold cursor-pointer text-black"
                onClick={toggleMenu}
              >
                <Wallet size={20} /> My Wallet
              </Link>
            </li>
            {/* <li className="">
              <Link
                href="/my-profile"
                className="flex items-center gap-x-2 p-3 rounded-full hover:bg-primary-blue hover:text-white text-base text-semibold cursor-pointer text-black"
                onClick={toggleMenu}
              >
                <HelpCircle size={20} /> Help
              </Link>
            </li> */}

            {/* Services */}
            <li>
              <div
                className={`flex justify-between hover:bg-primary-blue  bg-[#D9F9F6] text-black hover:text-white items-center cursor-pointer  py-3 px-4 rounded-full ${
                  openSections.services && "bg-[#D9F9F6] text-black"
                }`}
                onClick={() => toggleSection("services")}
              >
                <Link
                  href="/services"
                  className="text-sm sm:text-base font-semibold"
                  prefetch={false}
                >
                  Services
                </Link>
                {openSections.services ? <ChevronUp /> : <ChevronDown />}
              </div>
              {openSections.services && (
                <ul className="text-base font-medium">
                  <li>
                    <Link
                      href="/services/noticeboard"
                      className="text-sm block py-2 px-4 text-black font-medium hover:text-primary-blue"
                      prefetch={false}
                    >
                      Noticeboard
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/services/mixride"
                      className="text-sm block py-2 px-4 text-black font-medium hover:text-primary-blue"
                      prefetch={false}
                    >
                      Mix Ride
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/services/mixcreators"
                      className="text-sm block py-2 px-4 text-black font-medium hover:text-primary-blue"
                      prefetch={false}
                    >
                      Mix Creators
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/services/mixmate"
                      className="text-sm block py-2 px-4 text-black font-medium hover:text-primary-blue"
                      prefetch={false}
                    >
                      Mix Mate
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/services/events"
                      className="text-sm block py-2 px-4 text-black font-medium hover:text-primary-blue"
                      prefetch={false}
                    >
                      Events
                    </Link>
                  </li>
                </ul>
              )}
            </li>

            {/* Company */}
            <li>
              <div
                className={`flex justify-between bg-[#D9F9F6] hover:bg-primary-blue text-black hover:text-white items-center cursor-pointer  py-3 px-4 rounded-full ${
                  openSections.company && "bg-[#D9F9F6] text-black"
                }`}
                onClick={() => toggleSection("company")}
              >
                <Link
                  href="/company"
                  className="text-sm sm:text-base font-semibold "
                  prefetch={false}
                >
                  Company
                </Link>
                {openSections.company ? <ChevronUp /> : <ChevronDown />}
              </div>
              {openSections.company && (
                <ul className="text-sm sm:text-base font-medium">
                  <li>
                    <Link
                      className="text-sm block py-2 px-4 text-black font-medium hover:text-primary-blue"
                      href="/aboutus"
                      prefetch={false}
                    >
                      About Us
                    </Link>
                  </li>
                  <li>
                    <Link
                      className="text-sm block py-2 px-4 text-black font-medium hover:text-primary-blue"
                      href="/company/rewards"
                      prefetch={false}
                    >
                      Rewards
                    </Link>
                  </li>
                  <li>
                    <Link
                      className="text-sm block py-2 px-4 text-black font-medium hover:text-primary-blue"
                      href="blog"
                      prefetch={false}
                    >
                      Blogs
                    </Link>
                  </li>
                  <li>
                    <button
                      // onClick={openContactPopup}
                      className="text-sm block py-2 px-4 text-black font-medium hover:text-primary-blue"
                    >
                      Contact Us
                    </button>
                  </li>
                </ul>
              )}
            </li>

            {/* Help */}
            <li>
              <div
                className={`flex justify-between bg-[#D9F9F6] hover:bg-primary-blue text-black hover:text-white items-center cursor-pointer  py-3 px-4 rounded-full ${
                  openSections.help && "bg-[#D9F9F6]"
                }`}
                onClick={() => toggleSection("help")}
              >
                <Link
                  href="/my-profile?section=help"
                  className="text-sm sm:text-base font-semibold flex items-center gap-x-2"
                  prefetch={false}
                >
                  <HelpCircle size={20} className="" />
                  Help
                </Link>
                {openSections.help ? <ChevronUp /> : <ChevronDown />}
              </div>
              {openSections.help && (
                <ul className="text-sm sm:text-base font-medium">
                  <li>
                    <Link
                      className="text-sm block py-2 px-4 text-black font-medium hover:text-primary-blue"
                      href="faqs"
                      prefetch={false}
                    >
                      FAQs
                    </Link>
                  </li>
                  <li>
                    <Link
                      className="text-sm block py-2 px-4 text-black font-medium hover:text-primary-blue"
                      href="privacypolicy"
                      prefetch={false}
                    >
                      Privacy Policy
                    </Link>
                  </li>
                  <li>
                    <Link
                      className="text-sm block py-2 px-4 text-black font-medium hover:text-primary-blue"
                      href="terms-condition"
                      prefetch={false}
                    >
                      Terms and Conditions
                    </Link>
                  </li>
                </ul>
              )}
            </li>

            <li
              className="flex items-center gap-x-2 p-3 bg-[#D9F9F6] text-black  rounded-full hover:bg-primary-blue hover:text-white text-sm sm:text-base font-semibold cursor-pointer"
              onClick={handleLogout}
            >
              <LogOut size={20} />
              <span>Logout</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default MyProfile;
