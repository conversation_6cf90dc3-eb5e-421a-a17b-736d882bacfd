/* eslint-disable react/no-unknown-property */
import React from "react";
import Modal from "@mui/material/Modal";
import { IoCloseCircleOutline } from "react-icons/io5";

const Termandcondition = ({ openTermandconditionpopup, handleCloseTermandconditionpopup }) => {
  const style = {
    position: "fixed",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: "100%",
    bgcolor: "background.paper",
    border: "2px solid #000",
    boxShadow: 24,
  };

  return (
    <>
      <Modal
        open={openTermandconditionpopup}
        onClose={handleCloseTermandconditionpopup}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <div sx={style}>
          <div className="bg-white rounded-2xl max-w-[870px] mx-auto left-1/2 absolute w-[95%] top-1/2 -translate-y-1/2 -translate-x-1/2">
            <div className="flex items-center p-2 pr-5 pt-5 justify-end">
              <button
                onClick={handleCloseTermandconditionpopup}
                className="text-black text-2xl hover:text-primary-blue"
              >
                <IoCloseCircleOutline size={30} />
              </button>
            </div>
            <div className="md:p-6 p-4">
                <div className="mb-4">
                <h2 className="text-2xl font-bold text-black mb-1 text-center">
                  Terms And Conditions
                </h2>
                <p className="text-sm text-black/80 font-normal text-center">Please review the terms before proceeding with your account creation.</p>
                </div>
                <h3 className="font-semibold md:text-xl text-lg text-black">
                Property Listing Agreement
          </h3>
          <ul className="text-sm list-disc text-black/80 my-3 mx-7">
            <li className="text-base text-black/80 mb-1">
            Hostel owners agree to provide accurate property details and images.
            </li>
            <li className="text-base text-black/80 mb-1">
            Listings will be verified by Mixdorm to maintain authenticity.
            </li>
          </ul>
          <h3 className="font-semibold md:text-xl text-lg text-black">
          Commission Policy
          </h3>
          <ul className="text-sm list-disc text-black/80 my-3 mx-7">
            <li className="text-base text-black/80 mb-1">
            Acommission of [x%] will be charged on each booking made through the
 Mixdorm platform.
            </li>
            <li className="text-base text-black/80 mb-1">
            Commissions are deducted from the total booking amount before payouts.
            </li>
          </ul>
          <h3 className="font-semibold md:text-xl text-lg text-black">
          Payment Terms
          </h3>
          <ul className="text-sm list-disc text-black/80 my-3 mx-7">
            <li className="text-base text-black/80 mb-1">
            Payouts are processed weekly to the bank account provided.
            </li>
            <li className="text-base text-black/80 mb-1">
            Hostel owners are responsible for providing correct payout information.
            </li>
          </ul>
          <h3 className="font-semibold md:text-xl text-lg text-black">
          Cancellation and Refunds
          </h3>
          <ul className="text-sm list-disc text-black/80 my-3 mx-7">
            <li className="text-base text-black/80 mb-1">
            Cancellations are handled according to the policy chosen during the property
            setup.
            </li>
            <li className="text-base text-black/80 mb-1">
            Mixdorm reserves the right to mediate disputes between travelers and hostel
            owners.
            </li>
          </ul>
          <h3 className="font-semibold md:text-xl text-lg text-black">
          Prohibited Activities
          </h3>
          <ul className="text-sm list-disc text-black/80 my-3 mx-7">
            <li className="text-base text-black/80 mb-1">
            Listings must not contain false information, illegal services, or discriminatory
 practices.
            </li>
          </ul>
          <h3 className="font-semibold md:text-xl text-lg text-black">
          Privacy Policy
          </h3>
          <ul className="text-sm list-disc text-black/80 my-3 mx-7">
            <li className="text-base text-black/80 mb-1">
            Hostel owner data will only be used for Mixdorm operations and marketing
            purposes as per the privacy policy.
            </li>
          </ul>
          <div className="flex justify-end">
            <button className="bg-teal-500 text-white font-bold rounded-3xl px-5 py-2">
                    I Agree
            </button>
          </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default Termandcondition;
