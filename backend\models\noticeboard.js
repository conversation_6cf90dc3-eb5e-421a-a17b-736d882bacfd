import mongoose from "mongoose";
const NoticeboardSchema = new mongoose.Schema({
    isGlobal:{
        type:Boolean,
        default:false
    },
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'users' 
    },
    title: {
        type: String,
    },
    message: {
        type: String,
    },
    data: {
        type: Object,
    },
    fcmToken:  [{ type: String }],
    status: {
        type: String,
        enum: ['sent', 'failed'],
        default: 'sent'
    },
    error: {
        type: String
    },
    type: {
        type: String,
    },
    isRead: {
        type: Boolean,
        default: false
    },
    actionBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'users' 
    },
    action:{
        type:String
    },
    dynamicId: {
        ride: { type: mongoose.Schema.Types.ObjectId, ref: 'rides' },
        event: { type: mongoose.Schema.Types.ObjectId, ref: 'events' },
        property: { type: mongoose.Schema.Types.ObjectId, ref: 'properties' },
        review: { type: mongoose.Schema.Types.ObjectId, ref: 'reviews' },

    },
    dynamicData: {
      type:Object
    },
    social_interactions:{
        comment:String,
        videos:[String],
        photos:[String],
        experience:{
            title:{type:String},
            description:{type:String},
            city:{type:String},
            country:{type:String},
            videos:[{type:String}],
            photos:[{type:String}]
        }
    }
}, {
    timestamps: true
});

const Noticeboard = mongoose.model('Noticeboard', NoticeboardSchema);
export default Noticeboard;
