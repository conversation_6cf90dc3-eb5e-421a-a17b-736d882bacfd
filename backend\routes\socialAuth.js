// routes/socialAuth.js
import express from 'express';
import passport from 'passport';
import { googleLogin, googleCallback, facebookLogin, facebookCallback, appleLogin, appleCallback } from '../controller/socialAuth.js';

const router = express.Router();

router.get('/google', googleLogin);

router.get('/google/callback',
  passport.authenticate('google', { failureRedirect: '/' }),
  googleCallback);

router.get('/facebook', facebookLogin);

router.get('/facebook/callback',
  passport.authenticate('facebook', { failureRedirect: '/' }),
  facebookCallback);

router.get('/apple', appleLogin);

router.post('/apple/callback',
  passport.authenticate('apple', { failureRedirect: '/' }),
  appleCallback);

export default router;

/**
 * @swagger
 * /socialAuth/google:
 *   get:
 *     summary: Google login
 *     description: Authenticate using Google OAuth 2.0
 *     responses:
 *       302:
 *         description: Redirect to Google for authentication
*/

/**
 * @swagger
 * /socialAuth/google/callback:
 *   get:
 *     summary: Google login callback
 *     description: Google OAuth 2.0 callback URL
 *     responses:
 *       302:
 *         description: Redirect after successful authentication
*/

/**
 * @swagger
 * /socialAuth/facebook:
 *   get:
 *     summary: Facebook login
 *     description: Authenticate using Facebook
 *     responses:
 *       302:
 *         description: Redirect to Facebook for authentication
*/

/**
 * @swagger
 * /socialAuth/facebook/callback:
 *   get:
 *     summary: Facebook login callback
 *     description: Facebook callback URL
 *     responses:
 *       302:
 *         description: Redirect after successful authentication
*/

/**
 * @swagger
 * /socialAuth/apple:
 *   get:
 *     summary: Apple login
 *     description: Authenticate using Apple
 *     responses:
 *       302:
 *         description: Redirect to Apple for authentication
*/

/**
 * @swagger
 * /socialAuth/apple/callback:
 *   post:
 *     summary: Apple login callback
 *     description: Apple callback URL
 *     responses:
 *       302:
 *         description: Redirect after successful authentication
*/