"use client";
import { Plus } from "lucide-react";
import React from "react";
import { useState } from "react";

import Sidebar from "@/components/superadmin/ChatSidebar";
import ChatWindow from "@/components/superadmin/ChatWindow";

const Communication = () => {
  const [isChatOpen, setIsChatOpen] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [openAddHostel, setOpenAddHostel] = useState(false);

  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
      {/* Header */}
      <div className="flex items-center justify-between w-full">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">Communication</h2>
        <div className="w-[50%] gap-x-5 flex justify-end items-center">
          <button
            className="px-4 py-2 text-sm font-normal text-white rounded relative flex justify-center items-center bg-sky-blue-650"
            type="button"
            onClick={() => setOpenAddHostel(true)}
          >
            <Plus size={18} className="mr-1" /> Add New User
          </button>
        </div>
      </div>

      {/* Filter Buttons */}
      <div className="flex gap-3 md:gap-4 lg:gap-4 my-4">
        <button
          className="px-6 md:px-12 lg:px-12 py-2 text-sm font-semibold font-nunito text-white rounded relative flex justify-center items-center bg-sky-blue-650 "
          type="button"
          
        >
          User
        </button>
        <button
          className="px-5 py-2 text-sm font-semibold font-nunito text-black rounded relative flex justify-center items-center bg-white border dark:text-[#B6B6B6] dark:bg-transparent"
          type="button"
          
        >
          Hostel Owners
        </button>
        <button
          className="px-5 py-2 text-sm font-semibold font-nunito text-black rounded relative flex justify-center items-center bg-white border dark:text-[#B6B6B6] dark:bg-transparent"
          type="button"
          
        >
          Premium User
        </button>
      </div>

      {/* Chat Section */}
      <div className="flex lg:hidden">
        <div className="flex lg:flex-row flex-col gap-4">
          {/* Sidebar - Always visible on large screens, toggles on small screens */}
          <div
            className={`lg:w-[30%] lg:max-w-[30%] w-full ${
              isChatOpen ? "hidden sm:hidden" : "flex"
            }`}
          >
            <Sidebar onSelectUser={() => setIsChatOpen(true)} />
          </div>

          {/* Chat Window - Always visible on large screens, toggles on small screens */}
          <div
            className={`lg:w-[70%] lg:max-w-[70%] w-full ${
              isChatOpen ? "flex" : "hidden sm:hidden lg:flex"
            }`}
          >
            <ChatWindow onClose={() => setIsChatOpen(false)} />
          </div>
        </div>
      </div>
      <div className="hidden lg:flex gap-4">
        <div className="w-[30%] max-w-[30%]">
          <Sidebar />
        </div>
        <div className="w-[70%]  max-w-[70%]">
          <ChatWindow />
        </div>
      </div>
    </div>
  );
};

export default Communication;
