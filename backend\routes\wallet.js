import express from 'express';
import {balanceAddInWallet,getUserBalance } from '../controller/wallet.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();

// Route to connect Instagram and retrieve user data
router.post('/add-balance', checkAuth('wallet_add'),balanceAddInWallet);
router.get('/balance/:userId', checkAuth('wallet_view'), getUserBalance);

export default router;
/**
 * @swagger
 * tags:
 *   name: Wallet
 *   description: API endpoints for managing wallet balances
 */

/**
 * @swagger
 * /wallets/add-balance:
 *   post:
 *     summary: Add balance to the user's wallet
 *     tags: [Wallet]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               amount:
 *                 type: number
 *                 description: The amount to add to the wallet.
 *                 example: 500
 *               currency:
 *                 type: string
 *                 description: The currency of the transaction.
 *                 example: "INR"
 *               orderId:
 *                 type: string
 *                 description: The order id get from payment.
 *               paymentId:
 *                 type: string
 *                 description: The order id get from payment.
 *               signature:
 *                 type: string
 *                 description: The order id get from payment.
 *     responses:
 *       200:
 *         description: Wallet updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Wallet updated successfully."
 *                 wallet:
 *                   type: object
 *                   properties:
 *                     user:
 *                       type: string
 *                       example: "64b8b9f1c9d8760012a4a123"
 *                     amount:
 *                       type: number
 *                       example: 1500
 *                     currency:
 *                       type: string
 *                       example: "INR"
 *                 transaction:
 *                   type: object
 *                   properties:
 *                     user:
 *                       type: string
 *                       example: "64b8b9f1c9d8760012a4a123"
 *                     amount:
 *                       type: number
 *                       example: 500
 *                     currency:
 *                       type: string
 *                       example: "INR"
 *                     type:
 *                       type: string
 *                       example: "credit"
 *                     transaction_id:
 *                       type: string
 *                       example: "txn_12345abcde"
 *                     payment_status:
 *                       type: string
 *                       example: "paid"
 *                     payment_details:
 *                       type: object
 *                       example: { "method": "UPI", "gateway": "Razorpay" }
 *       400:
 *         description: Bad request (e.g., missing required fields or invalid data)
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /wallets/balance/{userId}:
 *   get:
 *     summary: Get the wallet balance of a user
 *     tags: [Wallet]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the user whose wallet balance is to be retrieved.
 *     responses:
 *       200:
 *         description: Wallet balance retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 balance:
 *                   type: number
 *                   example: 1500
 *                 currency:
 *                   type: string
 *                   example: "INR"
 *                 message:
 *                   type: string
 *                   example: "Wallet balance retrieved successfully"
 *       404:
 *         description: Wallet not found for the user
 *       500:
 *         description: Internal server error
 */