import Head from "next/head";

export default function StopSell() {
  return (
    <>
      <Head>
        <title>Stop Sell</title>
      </Head>
      <main className="min-h-screen bg-white">
        <div className="bg-white border border-gray-200 rounded-md p-4 shadow-sm w-full ">
          <form className="flex flex-col md:flex-row md:items-end gap-4">
            {/* Source Field */}
            <div className="flex flex-col">
              <label className="text-sm font-medium text-black mb-1" htmlFor="source">
                Source
              </label>
              <select
                id="source"
                className="border border-gray-300 rounded-md px-4 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-black focus:border-black w-[436px] h-[40px]"
              >
                <option>Channel - CHANNEL</option>
                <option>Channel - CHANNEL</option>
                <option>Channel - CHANNEL</option>
                <option>Channel - CHANNEL</option>
              </select>
            </div>

            {/* Month Field */}
            <div className="flex flex-col w-full ">
              <label className="text-sm font-medium text-black mb-1" htmlFor="month">
                Month
              </label>
              <select
                id="month"
                className="border border-gray-300 rounded-md px-4 py-2 text-gray-700 focus:outline-none focus:ring-2 focus:ring-black focus:border-black w-[436px] h-[40px]"
              >
                <option>Apr -2024</option>
                <option>Apr -2024</option>
                <option>Apr -2024</option>
                <option>Apr -2024</option>
              </select>
            </div>

            {/* Button */}
            <div className="w-full">
              <button
                type="submit"
                className="bg-black text-white px-6 py-2 rounded-md hover:bg-gray-800 transition"
              >
                Show
              </button>
            </div>
          </form>
        </div>
      </main>
    </>
  );
}
