import { createServer } from 'http';
import { Server } from 'socket.io';

const socketPort = 8090;
const socketServer = createServer();

export const io = new Server(socketServer, {
    cors: {
        origin: "*", // Consider restricting in production
        methods: ['GET', 'POST'],
        credentials: true,
    },
    transports: ['websocket'], // Only allow WebSocket transport
});

const ROOM_PREFIX = 'user_';

io.on('connection', (socket) => {
    console.log(`✅ User connected: ${socket.id}`);

    // Handle room joining
    socket.on('joinUserRoom', (data) => {
        const userId = data?.userId;
        if (userId) {
            const roomName = `${ROOM_PREFIX}${userId}`;

            // Leave any previously joined user room
            const existingRooms = Array.from(socket.rooms).filter(r => r.startsWith(ROOM_PREFIX));
            existingRooms.forEach(room => socket.leave(room));

            socket.join(roomName);
            console.log(`🚪 User ${userId} joined room ${roomName}`);
        } else {
            console.warn('⚠️ No userId provided in joinUserRoom event');
        }
    });

    // Handle sending messages
    socket.on('sendMessage', (data) => {
        const userId = data?.userId;
        const message = data?.message;
        if (userId && message) {
            const roomName = `${ROOM_PREFIX}${userId}`;
            io.to(roomName).emit('receiveMessage', { ...data, from: socket.id });
            console.log(`📨 Message sent to room ${roomName}:`, message);
        } else {
            console.warn('⚠️ Invalid data received in sendMessage:', data);
        }
    });

    // Optional ping-pong for connection checks
    socket.on('ping', () => {
        socket.emit('pong');
    });

    socket.on('disconnect', (reason) => {
        console.log(`❌ User disconnected: ${socket.id} (reason: ${reason})`);
    });

    socket.on('error', (error) => {
        console.error('❗ Socket error:', error);
    });
});

socketServer.listen(socketPort, () => {
    console.log(`🚀 Socket.io server running on port ${socketPort}`);
});

export default socketServer;
