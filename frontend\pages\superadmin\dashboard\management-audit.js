import React from "react";
import AuditLogsFilter from "@/components/superadmin/AuditLogsFilter";
import { FaEllipsisV } from "react-icons/fa";
import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight,
} from "react-icons/md";
import Link from "next/link";

const Audit = () => {
  const auditLogs = [
    {
      userName: "Product Admin",
      userId: 1,
      entityName: "Content Management",
      actionName: "Service Update",
      ipAddress: "127.0.0.1",
      timestamp: "30-05-2024 17:05 PM",
    },
    {
      userName: "Product Admin",
      userId: 1,
      entityName: "Notification Management",
      actionName: "Service Update",
      ipAddress: "127.0.0.1",
      timestamp: "30-05-2024 17:05 PM",
    },
    {
      userName: "Product Admin",
      userId: 1,
      entityName: "Communication Management",
      actionName: "Service Update",
      ipAddress: "127.0.0.1",
      timestamp: "30-05-2024 17:05 PM",
    },
    {
      userName: "Product Admin",
      userId: 1,
      entityName: "Feedback & support Management",
      actionName: "Service Update",
      ipAddress: "127.0.0.1",
      timestamp: "30-05-2024 17:05 PM",
    },
    {
      userName: "Product Admin",
      userId: 1,
      entityName: "Payment Management",
      actionName: "Service Update",
      ipAddress: "127.0.0.1",
      timestamp: "30-05-2024 17:05 PM",
    },
    {
      userName: "Product Admin",
      userId: 1,
      entityName: "User Management",
      actionName: "Service Update",
      ipAddress: "127.0.0.1",
      timestamp: "30-05-2024 17:05 PM",
    },
    {
      userName: "Product Admin",
      userId: 1,
      entityName: "Booking Management",
      actionName: "Service Update",
      ipAddress: "127.0.0.1",
      timestamp: "30-05-2024 17:05 PM",
    },
  ];

  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">Audit Logs</h2>
      <div className="mt-5">
        <AuditLogsFilter />
      </div>
      <div className="overflow-x-auto mt-5 rounded-xl bg-white border dark:border-none dark:bg-black ">
        <table className="min-w-full bg-white mb-10 dark:border-none dark:bg-black">
          <thead>
            <tr className="w-full dark:border-none ">
              <th className="pb-6 pt-4 px-5 pl-8 lg:pl-8  text-sm font-poppins font-semibold  text-left text-black/75 dark:text-[#B6B6B6] dark:bg-black">
                USER NAME
              </th>
              <th className="pb-6 pt-4 px-5 pl-16 lg:pl-14   text-sm font-poppins font-semibold   text-left text-black/75 dark:text-[#B6B6B6] dark:bg-black">
                ENTITY NAME
              </th>
              <th className="pb-6 pt-4 px-5 pl-8 md:pl-5  text-sm font-poppins font-semibold   text-left text-black/75 dark:text-[#B6B6B6] dark:bg-black">
                ACTION NAME
              </th>
              <th className="pb-6 pt-4 px-5 pl-8 md:pl-5  text-sm font-poppins font-semibold   text-left text-black/75 dark:text-[#B6B6B6] dark:bg-black">
                IP ADDRESS
              </th>
              <th className="pb-6 pt-4 px-5 pl-16 lg:pl-14   text-sm font-poppins font-semibold   text-left text-black/75 dark:text-[#B6B6B6] dark:bg-black">
                TIMESTAMP 
              </th>
              <th className="pb-6 pt-4 px-5 pl-6 lg:pl-8   text-sm font-poppins font-semibold   text-left text-black/75 dark:text-[#B6B6B6] dark:bg-black">
                ACTION
              </th>
            </tr>
          </thead>
          <tbody>
            {auditLogs.map((log, index) => (
              <tr key={index} className="w-full dark:bg-black border-x border-y dark:border-x-0">
                <td className="py-4 px-8 md:px-5  text-black/65">
                  <div className=" md:text-sm whitespace-nowrap text-sm font-poppins font-medium dark:text-[#B6B6B6]">{log.userName}</div>
                  <div className="text-sm font-poppins font-medium text-gray-500 dark:text-[#757575]">
                    User Id:{log.userId}
                  </div>
                </td>
                <td className="py-4 px-8 md:px-5 whitespace-nowrap">
                  <span className="text-primary-blue text-sm font-poppins font-medium bg-teal-100 rounded-md px-3 py-1.5 dark:bg-[#4fadad49]">
                    {log.entityName}
                  </span>
                </td>
                <td className="py-4 px-8 md:px-5  text-sm font-poppins font-medium text-black/65 whitespace-nowrap dark:text-[#757575] ">
                  {log.actionName}
                </td>
                <td className="py-4 px-8 md:px-5 ">
                  <span className="text-purple-600 bg-purple-200 rounded-md px-4 text-sm py-1.5 font-poppins font-medium dark:bg-[#9c5eb549]">
                    {log.ipAddress}
                  </span>
                </td>
                <td className="py-4 px-8 md:px-5  whitespace-nowrap">
                <span className="text-primary-blue text-sm font-poppins font-medium bg-teal-100 rounded-md px-3 py-1.5 dark:bg-[#4fadad49]">
                    {log.timestamp}
                  </span>
                </td>

                <td className="py-4 px-8 md:px-5  text-center">
                  <button className="text-gray-500 bg-slate-50 border p-1.5 rounded-md dark:bg-[#282727] dark:border-none">
                    {" "}
                    <Link href={"/superadmin/dashboard/management-audit-details"}>
                    <FaEllipsisV className="text-gray-400 hover:text-gray-500 dark:text-[#757575] " />
                    </Link>
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="flex justify-between items-center mt-5">
                   <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
                   <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
                     <a
                       href="#"
                       className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                     >
                       <span className="sr-only">Next Page</span>
                       <MdOutlineKeyboardArrowLeft />
                     </a>
           
                     <a
                       href="#"
                       className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                     >
                       <span className="sr-only">Next Page</span>
                       <MdOutlineKeyboardArrowRight />
                     </a>
                   </div>
                 </div>
    </div>
  );
};

export default Audit;
