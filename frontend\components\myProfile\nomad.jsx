import React from "react";
import Link from "next/link";
import { BiSolidCrown } from "react-icons/bi";
import { ChevronRight } from "lucide-react";
import { PiFireSimpleFill } from "react-icons/pi";
import { RiDiscountPercentFill, RiCustomerService2Line, RiPoliceBadgeFill   } from "react-icons/ri";
import { FaCalendarAlt  } from "react-icons/fa";
import { BsFillGiftFill } from "react-icons/bs";
import { FaAward } from "react-icons/fa6";
import Image from "next/image";

const Nomad = () => {
  
  return (
    <>
       <div>
            <div className="mb-4">
              <ul
                className="flex flex-wrap justify-between"
                data-tabs-toggle="#default-tab-content"
                role="tablist"
              >
                <li
                  className="me-2 cursor-pointer flex items-center"
                  role="presentation"
                >
                  <div className="w-[218px] h-[107px] bg-[url('https://mixdorm.s3.ap-south-1.amazonaws.com/front-images/explorebg.png')] rounded-3xl p-3">
                    <div className="flex justify-between">
                      <h3 className="font-bold text-black text-lg">Explorer</h3>
                      <div>
                        <p className="text-[7px] font-semibold">
                          Reward <span className="text-[8px]">Points</span>
                        </p>
                        <p className="text-[12px] font-semibold text-[#FFAD72] flex justify-end">
                          1,000
                        </p>
                      </div>
                    </div>
                    <div className="mt-7">
                      <div className="mb-1 text-[8px] font-medium dark:text-white flex justify-between">
                        <span>1000/5000</span> <span>Redeem</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mb-4 dark:bg-gray-700">
                        <div className="bg-[#FFAD72] h-1.5 rounded-full dark:bg-blue-500 w-1/2"></div>
                      </div>
                    </div>
                  </div>
                </li>
                <li
                  className="me-2 cursor-pointer flex items-center"
                  role="presentation"
                >
                  <div className="w-[280px] h-[145px] bg-[url('https://mixdorm.s3.ap-south-1.amazonaws.com/front-images/advanturebg.png')] rounded-3xl p-4">
                    <div className="flex justify-between">
                      <h3 className="font-bold text-black text-xl">
                        Adventurer
                      </h3>
                      <div>
                        <p className="text-xs font-semibold">
                          Reward <span className="text-sm">Points</span>
                        </p>
                        <p className="text-sm font-semibold text-black flex justify-end">
                          6,000
                        </p>
                      </div>
                    </div>
                    <div className="mt-12">
                      <div className="mb-1 text-xs font-medium text-[#4C3CB9] dark:text-white flex justify-between">
                        <span>6000/12000</span> <span>Redeem</span>
                      </div>
                      <div className="w-full bg-gray-300 rounded-full h-2 mb-4 dark:bg-gray-600">
                        <div className="bg-[#4C3CB9] h-1.5 rounded-full dark:bg-blue-500 w-1/2"></div>
                      </div>
                    </div>
                  </div>
                </li>
                <li
                  className="me-2 cursor-pointer flex items-center"
                  role="presentation"
                >
                  <div className="w-[380px] h-[185px] bg-[url('https://mixdorm.s3.ap-south-1.amazonaws.com/front-images/nomadbg.png')] rounded-3xl border border-[#72ADFF] p-5">
                    <div className="flex justify-between">
                      <h3 className="font-bold text-black text-2xl">Nomad</h3>
                      <div>
                        <p className="text-xs font-semibold">
                          Reward <span className="text-lg">Points</span>
                        </p>
                        <p className="text-sm font-semibold text-black flex justify-end">
                          6,000
                        </p>
                      </div>
                    </div>
                    <div className="w-[120px] h-[28px] bg-[#72ADFF] mt-5 flex px-1 justify-between items-center">
                      <BiSolidCrown size={15} className="text-white" />
                      <p className="text-sm text-white font-semibold">
                        11 Benefits
                      </p>
                      <ChevronRight size={15} className="text-white" />
                    </div>
                    <div className="mt-5">
                      <div className="mb-1 text-sm font-medium text-[#72ADFF] dark:text-white flex justify-between">
                        <span>6000/12000</span> <span>Redeem</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mb-4 dark:bg-gray-700">
                        <div className="bg-[#72ADFF] h-1.5 rounded-full w-1/2"></div>
                      </div>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
            <div className="mb-4">
              <p className="text-black text-xl font-normal">
                <span className="text-black font-extrabold">Benefits :</span> 11
                Benefits for dedicated users, including all Mixdorm features,
                highest discounts, and premium access.
              </p>
            </div>
            <div className="mb-4 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
                <RiDiscountPercentFill size={22} />
              </span>
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  1. 7% Discount on Bookings:
                </span>{" "}
                The highest discount level for all bookings on Mixdorm.
              </p>
            </div>
            <div className="mb-2 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
                <PiFireSimpleFill size={22} />
              </span>
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  2. Free Mix Mate Subscription (3 Months):
                </span>{" "}
                Unlock a 3-month subscription to Mix Mate, perfect for meeting
                new people along the journey.
              </p>
            </div>
            <div className="mb-2 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
                <FaCalendarAlt size={22} />
              </span>
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  3. VIP Access to Mix Events and Mix Creator:
                </span>{" "}
                Guaranteed spots and VIP status for events and access to Mix
                Creator features for sharing and creating travel content
              </p>
            </div>
            <div className="mb-2 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
              <FaAward size={22} />
              </span>{" "}
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  4. 4x Reward Points on Every Booking:
                </span>{" "}
                Maximum reward points on bookings, allowing Nomads to
                continually earn even more perks.
              </p>
            </div>
            <div className="mb-2 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
                <BsFillGiftFill size={22} />
              </span>{" "}
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  5. Gift Cards on All Major Milestones:
                </span>{" "}
                Earn gift cards for every major milestone (e.g., 10th, 20th,
                30th booking), making travel even more rewarding.
              </p>
            </div>
            <div className="mb-2 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
              <Image src="https://mixdorm.s3.ap-south-1.amazonaws.com/front-images/access.png" className="w-7 h-7" alt="mixsplit"/>
              </span>{" "}
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  6. Mix Split and Mix Ride Unlimited Access:
                </span>{" "}
                Full access to Mix Split and Mix Ride for seamless travel
                arrangements and expense sharing.
              </p>
            </div>
            <div className="mb-2 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
                <FaCalendarAlt size={22} />
              </span>{" "}
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  7. Discounted Mix Events:
                </span>{" "}
                Nomad members get exclusive discounts on Mixdorm-sponsored
                events and activities
              </p>
            </div>
            <div className="mb-2 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
              <Image src="https://mixdorm.s3.ap-south-1.amazonaws.com/front-images/Aiman.png" className="w-8 h-7" alt="Ai"/>
              </span>{" "}
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  8. 2 Free Monthly AI Tool Accesses:
                </span>{" "}
                Access to two AI-powered tools per month to receive trip advice,
                budget planning, and personalized itineraries
              </p>
            </div>{" "}
            <div className="mb-2 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
              <Image src="https://mixdorm.s3.ap-south-1.amazonaws.com/front-images/travelshopping.png" className="w-7 h-7" alt="travelshopping"/>
              </span>{" "}
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  9. Exclusive Travel Merchandise:
                </span>{" "}
                Receive special travel merchandise, such as branded accessories,
                gear, or apparel, available only to Nomad members.
              </p>
            </div>{" "}
            <div className="mb-2 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
                <RiPoliceBadgeFill size={22} />
              </span>{" "}
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  10. Special Nomad Badge:
                </span>{" "}
                Receive a distinctive badge on the Mixdorm profile, highlighting
                elite status to friends and travel mates.
              </p>
            </div>{" "}
            <div className="mb-2 flex border border-grey-600 rounded-xl px-3 py-3 gap-2 items-center">
              <span>
                <RiCustomerService2Line size={22} />
              </span>{" "}
              <p className="text-black text-[16px] font-normal">
                <span className="text-black font-extrabold">
                  11. Exclusive Customer Support:
                </span>{" "}
                Priority support with a dedicated service line for Nomad
                members, helping resolve issues quickly and efficiently.
              </p>
            </div>
            <div className="flex justify-between mt-7 mb-3">
              <div>
                <h4 className="font-extrabold text-black">
                  <span className="text-primary-blue underline">
                    Wallet History
                  </span>{" "}
                  &nbsp;&nbsp;&nbsp;My Point History
                </h4>
              </div>

              <div>
                <Link
                  href="#"
                  className="text-lg font-semibold text-black hover:text-sky-blue-750"
                >
                  See All
                </Link>
              </div>
            </div>
            <div className="mb-4">
              <div className="flex py-3 border-b border-grey-600 justify-between">
                <div>
                  <p className="text-[16px] font-bold">
                    Booking Points-{" "}
                    <span className="font-normal">
                      Get 100 Point On booking
                    </span>
                  </p>
                  <p className="text-[16px] font-normal text-[#AAACAE]">
                    5 min ago
                  </p>
                </div>
                <div className="rounded-full bg-[#D9F9F6] text-center p-[12px]">
                  <p className="text-xs text-primary-blue font-normal">
                    Points
                  </p>
                  <p className="text-sm text-primary-blue font-semibold">100</p>
                </div>
              </div>
              <div className="flex py-3 border-b border-grey-600 justify-between">
                <div>
                  <p className="text-[16px] font-bold">
                    Event Points-{" "}
                    <span className="font-normal">
                      Get 100 Point On booking
                    </span>
                  </p>
                  <p className="text-[16px] font-normal text-[#AAACAE]">
                    5 min ago
                  </p>
                </div>
                <div className="rounded-full bg-[#D9F9F6] text-center p-[12px]">
                  <p className="text-xs text-primary-blue font-normal">
                    Points
                  </p>
                  <p className="text-sm text-primary-blue font-semibold">100</p>
                </div>
              </div>
              <div className="flex py-3 border-b border-grey-600 justify-between">
                <div>
                  <p className="text-[16px] font-bold">
                    Mix Ride Points-{" "}
                    <span className="font-normal">
                      Get 100 Point On booking
                    </span>
                  </p>
                  <p className="text-[16px] font-normal text-[#AAACAE]">
                    5 min ago
                  </p>
                </div>
                <div className="rounded-full bg-[#D9F9F6] text-center p-[12px]">
                  <p className="text-xs text-primary-blue font-normal">
                    Points
                  </p>
                  <p className="text-sm text-primary-blue font-semibold">100</p>
                </div>
              </div>
              <div className="flex py-3 border-b border-grey-600 justify-between">
                <div>
                  <p className="text-[16px] font-bold">
                    Mix Creator Points-{" "}
                    <span className="font-normal">
                      Get 100 Point On booking
                    </span>
                  </p>
                  <p className="text-[16px] font-normal text-[#AAACAE]">
                    5 min ago
                  </p>
                </div>
                <div className="rounded-full bg-[#D9F9F6] text-center p-[12px]">
                  <p className="text-xs text-primary-blue font-normal">
                    Points
                  </p>
                  <p className="text-sm text-primary-blue font-semibold">100</p>
                </div>
              </div>
              <div className="flex py-3 border-b border-grey-600 justify-between">
                <div>
                  <p className="text-[16px] font-bold">
                    Booking Points-{" "}
                    <span className="font-normal">
                      Get 100 Point On booking
                    </span>
                  </p>
                  <p className="text-[16px] font-normal text-[#AAACAE]">
                    5 min ago
                  </p>
                </div>
                <div className="rounded-full bg-[#D9F9F6] text-center p-[12px]">
                  <p className="text-xs text-primary-blue font-normal">
                    Points
                  </p>
                  <p className="text-sm text-primary-blue font-semibold">100</p>
                </div>
              </div>
              <div className="flex py-3 border-b border-grey-600 justify-between">
                <div>
                  <p className="text-[16px] font-bold">
                    Event Points-{" "}
                    <span className="font-normal">
                      Get 100 Point On booking
                    </span>
                  </p>
                  <p className="text-[16px] font-normal text-[#AAACAE]">
                    5 min ago
                  </p>
                </div>
                <div className="rounded-full bg-[#D9F9F6] text-center p-[12px]">
                  <p className="text-xs text-primary-blue font-normal">
                    Points
                  </p>
                  <p className="text-sm text-primary-blue font-semibold">100</p>
                </div>
              </div>
            </div>
          </div>
  </>
  );
};

export default Nomad;
