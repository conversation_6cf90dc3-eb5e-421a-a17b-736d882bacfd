// controllers/socialAuthController.js
import passport from 'passport';

export const googleLogin = passport.authenticate('google', { scope: ['profile', 'email'] });

export const googleCallback = (req, res) => {
  const { user, token } = req.user;
  res.json({ user, token });
};

export const facebookLogin = passport.authenticate('facebook', { scope: ['email'] });

export const facebookCallback = (req, res) => {
  const { user, token } = req.user;
  res.json({ user, token });
};

export const appleLogin = passport.authenticate('apple');

export const appleCallback = (req, res) => {
  const { user, token } = req.user;
  res.json({ user, token });
};
