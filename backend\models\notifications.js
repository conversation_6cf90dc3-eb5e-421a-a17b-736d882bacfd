import mongoose from "mongoose";
const notificationSchema = new mongoose.Schema({
    isGlobal:{
        type:Boolean,
        default:false
    },
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
    },
    title: {
        type: String,
        required: true
    },
    message: {
        type: String,
    },
    data: {
        type: Object,
    },
    fcmToken:  [{ type: String }],
    status: {
        type: String,
        enum: ['sent', 'failed'],
        default: 'sent'
    },
    error: {
        type: String
    },
    type: {
        type: String,
        required: true
    },
    isRead: {
        type: Boolean,
        required: true,
        default: false
    },
    actionBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'users'  // Reference to the User model
    },
    action:{
        type:String
    },
    dynamicId: {
        ride: { type: mongoose.Schema.Types.ObjectId, ref: 'rides' },
        event: { type: mongoose.Schema.Types.ObjectId, ref: 'events' }

    },
}, {
    timestamps: true
});

const Notification = mongoose.model('Notifications', notificationSchema);
export default Notification;
