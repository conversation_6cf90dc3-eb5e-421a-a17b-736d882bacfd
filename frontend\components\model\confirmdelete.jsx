/* eslint-disable react/no-unknown-property */
import React, { useState } from "react";
import Modal from "@mui/material/Modal";
import dynamic from "next/dynamic";
import { deleteAccountApi } from "@/services/webflowServices";
import toast from "react-hot-toast";
import {
  getItemLocalStorage,
  removeItemLocalStorage,
} from "@/utils/browserSetting";
import { useNavbar } from "../home/<USER>";
import { removeFirebaseToken } from "@/services/ownerflowServices";

const Finaldelete = dynamic(
  () => import("../../components/model/finaldelete"),
  {
    ssr: false,
  }
);

const Confirmdelete = ({
  openConfirmdeletepopup,
  handleCloseConfirmdeletepopup,
  selectedReason,
  selectedPeriod,
  comment,
}) => {
  const [openFinaldeletepopup, setopenFinaldeletepopup] = useState(false);
  const handleCloseFinaldeletepopup = () => setopenFinaldeletepopup(false);
  const style = {
    position: "fixed",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: "100%",
    bgcolor: "background.paper",
    border: "2px solid #000",
    boxShadow: 24,
  };
  const { updateUserStatus,updateUserRole } = useNavbar();

  const handleOpenFinaldeletepopup = async () => {
    const payload = {
      reason: selectedReason || "",
      deactivatePeriod: selectedPeriod || "",
      comment: comment || "",
      type: "delete",
    };

    try {
      const response = await deleteAccountApi(payload);
      console.log("response", response);
      if (response?.data?.status) {
        handleCloseConfirmdeletepopup();
        setopenFinaldeletepopup(true);
        removeItemLocalStorage("token");
        updateUserStatus("");
        updateUserRole("");
        removeItemLocalStorage("name");
        removeItemLocalStorage("role");
        removeItemLocalStorage("email");
        removeItemLocalStorage("contact");
        const payload = {
          token: getItemLocalStorage("FCT"),
          userId: getItemLocalStorage("id"),
        };
        try {
          await removeFirebaseToken(payload);
          console.log("FCM token removed successfully.");
          removeItemLocalStorage("FCT");
        } catch (error) {
          console.error("Error removing FCM token:", error);
        }
        removeItemLocalStorage("id");
      } else {
        handleCloseConfirmdeletepopup();
        toast.error("There is some issue in Delete your account..");
      }
    } catch (error) {
      console.error("Failed to delete account:", error);
      // Optionally, display an error message to the user here
    }
  };

  return (
    <>
      <Modal
        open={openConfirmdeletepopup}
        onClose={handleCloseConfirmdeletepopup}
        aria-labelledby='modal-modal-title'
        aria-describedby='modal-modal-description'
      >
        <div sx={style}>
          <div className='bg-white rounded-2xl max-w-[700px] mx-auto left-1/2 absolute w-[95%] top-1/2 -translate-y-1/2 -translate-x-1/2 text-center'>
            <div className='md:p-6 p-4'>
              <h3 className='text-black text-2xl font-semibold mb-1 mt-5'>
                Deletion <span className='text-[#40E0D0]'>Confirmation</span>
              </h3>
              <h4 className='text-black text-2xl font-bold mt-5'>
                Are you sure you want to delete your
                <div>account?</div>
              </h4>
              <p className='text-base text-black font-semibold mt-1'>
                This action is irreversible. Please confirm if you want to
                permanently
                <div>delete your Mixdorm account</div>
              </p>
              <div className='flex justify-center gap-2 mt-4'>
                <div>
                  <button
                    className='bg-[#eeeeee] text-black font-semibold py-2 px-5 rounded-full text-sm lg:text-md'
                    onClick={handleCloseConfirmdeletepopup}
                  >
                    Go back
                  </button>
                </div>
                <div>
                  <button
                    className='bg-red-600 text-white font-semibold py-2 px-5 rounded-full text-sm lg:text-md'
                    onClick={handleOpenFinaldeletepopup}
                  >
                    Confirm Deletion
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Modal>
      <Finaldelete
        openFinaldeletepopup={openFinaldeletepopup}
        handleCloseFinaldeletepopup={handleCloseFinaldeletepopup}
      />
    </>
  );
};

export default Confirmdelete;
