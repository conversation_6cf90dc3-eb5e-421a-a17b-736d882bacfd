/* eslint-disable react/no-unknown-property */
import React, { useEffect, useRef, useState } from "react";
import {
  getAllReviewsListApi,
  getTravellersVisitedApi,
} from "@/services/ownerflowServices";
// import toast from "react-hot-toast";
import { useRouter } from "next/router";
import Link from "next/link";
import { IoIosArrowForward, IoIosListBox } from "react-icons/io";
import { FaCalendarAlt } from "react-icons/fa";
import { FaChartPie } from "react-icons/fa6";
import { MdNotificationsActive } from "react-icons/md";
import { RiShieldUserFill } from "react-icons/ri";
import dynamic from "next/dynamic";
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
} from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { Checkbox, FormControlLabel } from "@mui/material";
import Head from "next/head";

const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

const Profile = () => {
  const [selectedId, setSelectedId] = useState(null);
  const [redirectHandled, setRedirectHandled] = useState(false);
  // const toastShownRef = useRef(false);
  const [travellersCount, setTravellersCount] = useState(0);
  // eslint-disable-next-line no-unused-vars
  const [uploading, setUploading] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [reviews, setReviews] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [currentPage, setCurrentPage] = useState(1);
  const [reviewPerPage] = useState(10); // Set the number of reviews per page
  // eslint-disable-next-line no-unused-vars
  const [totalReviews, setTotalReviews] = useState(0);
  // eslint-disable-next-line no-unused-vars
  const [rating, setRating] = useState();

  const isFirstRender = useRef(true);

  const router = useRouter();

  // const tabs = [
  //   {
  //     id: 1,
  //     name: "Edit Information",
  //   },
  //   {
  //     id: 2,
  //     name: "About Us",
  //   },
  //   {
  //     id: 3,
  //     name: "Hostel Photos",
  //   },
  //   {
  //     id: 4,
  //     name: "Hostel Rules",
  //   },
  //   {
  //     id: 5,
  //     name: "Cancellation Policy",
  //   },
  //   {
  //     id: 6,
  //     name: "General Policy",
  //   },
  //   {
  //     id: 7,
  //     name: "Amenities",
  //   },
  //   {
  //     id: 8,
  //     name: "Contact US",
  //   },
  //   {
  //     id: 9,
  //     name: "Channel Partner",
  //   },
  // ];

  useEffect(() => {
    const storedId = localStorage.getItem("hopid");
    if (!storedId && !redirectHandled) {
      // if (!toastShownRef.current) {
      //   toast.error("Please select at least one property.");
      //   toastShownRef.current = true;
      // }
      router.push("/owner/login");
      setRedirectHandled(true);
      return;
    }
    setSelectedId(storedId);
  }, [redirectHandled, router]);

  useEffect(() => {
    if (!isFirstRender.current && selectedId) {
      fetchTravellersCount(selectedId);
      fetchReviews(selectedId);
    } else {
      isFirstRender.current = false;
    }
  }, [selectedId]);

  // const fetchUserData = async () => {
  //   try {
  //     const response = await getProfileApi();

  //     if (response?.status === 200) {
  //       setProfileData(response?.data?.data);
  //     }
  //   } catch (error) {
  //     console.error("Error fetching profile:", error.message);
  //   }
  // };

  const fetchTravellersCount = async (id) => {
    try {
      const response = await getTravellersVisitedApi(id);

      if (response?.status === 200) {
        setTravellersCount(response?.data?.data);
      }
    } catch (error) {
      console.error("Error fetching profile:", error.message);
    }
  };

  const fetchReviews = async (id) => {
    try {
      const response = await getAllReviewsListApi(
        id,
        currentPage,
        reviewPerPage
      );
      console?.log("response", response);
      if (response.status) {
        setReviews(response?.data?.data?.reviews);
        setTotalReviews(response?.data?.data?.pagination?.totalReviews);
        setRating(response?.data?.data?.ratings);
        // setTotalPages(response.data.data.pagination.totalPages);
      }
    } catch (error) {
      console.error("Error fetching reviews:", error);
    }
  };

  // const updatePropertyData = () => {
  //   fetchPropertiesById(selectedId);
  // };

  // const renderTabContent = () => {
  //   switch (activeTab) {
  //     case 1:
  //       return (
  //         <EditProperty
  //           id={selectedId}
  //           data={data}
  //           updatePropertyData={updatePropertyData}
  //         />
  //       );
  //     case 2:
  //       return (
  //         <AboutUs
  //           id={selectedId}
  //           data={data}
  //           updatePropertyData={updatePropertyData}
  //         />
  //       );
  //     case 3:
  //       return (
  //         <HostelPhotos
  //           id={selectedId}
  //           data={data}
  //           updatePropertyData={updatePropertyData}
  //         />
  //       );
  //     case 4:
  //       return (
  //         <HostelRules
  //           id={selectedId}
  //           data={data}
  //           updatePropertyData={updatePropertyData}
  //         />
  //       );
  //     case 5:
  //       return (
  //         <CancellationPolicy
  //           id={selectedId}
  //           data={data}
  //           updatePropertyData={updatePropertyData}
  //         />
  //       );
  //     case 6:
  //       return (
  //         <GeneralPolicy
  //           id={selectedId}
  //           data={data}
  //           updatePropertyData={updatePropertyData}
  //         />
  //       );
  //     case 7:
  //       return (
  //         <Amenities
  //           id={selectedId}
  //           data={data}
  //           updatePropertyData={updatePropertyData}
  //         />
  //       );
  //     case 8:
  //       return <ContactUs id={selectedId} data={data} />;
  //     case 9:
  //       return <ChannelPartner id={selectedId} data={data} />;
  //     default:
  //       return <AboutUs id={selectedId} data={data} />;
  //   }
  // };

  console.log("datatata", travellersCount);

  const [activeSection, setActiveSection] = useState("default");
  const [isModalOpen, setIsModalOpen] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [isFadingOut, setIsFadingOut] = useState(false);

  const handleSecondSectionClick = () => {
    setActiveSection("disconnectchannel");
  };

  const handleModalClick = () => {
    setIsFadingOut(true); // Start fade-out effect
    setTimeout(() => {
      setIsModalOpen(false);
      setActiveSection("confirmdelete");
      setIsFadingOut(false); // Reset fade-out state
    }, 1000); // Matches fade animation duration
  };

  return (
    <>
      <Head>
        <title>Account Settings | Mixdorm</title>
      </Head>
      <Loader open={uploading} />
      {activeSection === "default" && (
        <section className="w-full">
          {uploading ? (
            <>
              <div className="h-8 w-32 bg-gray-200 rounded animate-pulse mb-6"></div>
              <div className="border border-gray-300 rounded-xl p-5 mt-5">
                <div className="lg:w-[50%] md:w-[70%] sm:w-[80%] space-y-4">
                  {[1, 2, 3, 4, 5, 6].map((item) => (
                    <div key={item} className="flex items-center gap-4 py-3">
                      <div
                        className={`w-12 h-12 rounded-lg ${
                          item === 1
                            ? "bg-[#FF72AD33]"
                            : item === 2
                            ? "bg-[#FFAD7233]"
                            : item === 3
                            ? "bg-[#D092F533]"
                            : item === 4
                            ? "bg-[#40E0D033]"
                            : item === 5
                            ? "bg-[#E3EFFF]"
                            : "bg-[#DE000033]"
                        } animate-pulse`}
                      ></div>
                      <div className="flex-1">
                        <div className="h-5 w-3/4 bg-gray-200 rounded animate-pulse"></div>
                      </div>
                      <div className="w-5 h-5 bg-gray-200 rounded-full animate-pulse"></div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          ) : (
            <>
              <h2 className="page-title">Setting</h2>
              <div className="border border-gray-300 rounded-xl p-5 mt-5">
                <div className="lg:w-[50%] md:w-[70%] sm:w-[80%]">
                  <div className="flex items-center gap-4 mb-5 cursor-pointer">
                    <span className="w-12 h-12 rounded-lg bg-[#FF72AD33] text-[#FF72AD] flex items-center justify-center text-2xl">
                      <IoIosListBox width={20} height={20} />
                    </span>
                    <h4 className="mb-0">
                      <Link
                        href="/owner/dashboard/roommanagement"
                        className="text-base font-bold mb-0 font-manrope"
                        prefetch={false}
                      >
                        Manage Listings
                      </Link>
                    </h4>
                    <Link href="" className="ml-auto" prefetch={false}>
                      <IoIosArrowForward />
                    </Link>
                  </div>
                  <div className="flex items-center gap-4 mb-5 cursor-pointer">
                    <span className="w-12 h-12 rounded-lg bg-[#FFAD7233] text-[#FFAD72] flex items-center justify-center text-2xl">
                      <FaCalendarAlt width={20} height={20} />
                    </span>
                    <h4 className="mb-0">
                      <Link
                        href="/owner/dashboard/calendar"
                        className="text-base font-bold mb-0 font-manrope"
                        prefetch={false}
                      >
                        Availability Calendar
                      </Link>
                    </h4>
                    <Link href="" className="ml-auto" prefetch={false}>
                      <IoIosArrowForward />
                    </Link>
                  </div>
                  <div className="flex items-center gap-4 mb-5 cursor-pointer">
                    <span className="w-12 h-12 rounded-lg bg-[#D092F533] text-[#D092F5] flex items-center justify-center text-2xl">
                      <FaChartPie width={20} height={20} />
                    </span>
                    <h4 className="mb-0">
                      <Link
                        href="/owner/dashboard/analytics"
                        className="text-base font-bold mb-0 font-manrope"
                        prefetch={false}
                      >
                        Analytics & Insights
                      </Link>
                    </h4>
                    <Link href="" className="ml-auto" prefetch={false}>
                      <IoIosArrowForward />
                    </Link>
                  </div>
                  <div className="flex items-center gap-4 mb-5 cursor-pointer">
                    <span className="w-12 h-12 rounded-lg bg-[#40E0D033] text-[#40E0D0] flex items-center justify-center text-2xl">
                      <MdNotificationsActive width={20} height={20} />
                    </span>
                    <h4 className="mb-0">
                      <Link
                        // href='/owner/dashboard/noticeboard'
                        href="#"
                        className="text-base font-bold mb-0 font-manrope"
                        prefetch={false}
                      >
                        Noticeboard
                      </Link>
                    </h4>
                    <Link href="" className="ml-auto" prefetch={false}>
                      <IoIosArrowForward />
                    </Link>
                  </div>
                  <div className="flex items-center gap-4 mb-5 cursor-pointer">
                    <span className="w-12 h-12 rounded-lg bg-[#E3EFFF] text-[#72ADFF] flex items-center justify-center text-2xl">
                      <RiShieldUserFill width={20} height={20} />
                    </span>
                    <h4 className="mb-0">
                      <Link
                        href=""
                        className="text-base font-bold mb-0 font-manrope"
                        prefetch={false}
                      >
                        Account Security
                      </Link>
                    </h4>
                    <Link href="" className="ml-auto" prefetch={false}>
                      <IoIosArrowForward />
                    </Link>
                  </div>
                  <div className="flex items-center gap-4 mb-5 cursor-pointer">
                    <span className="w-12 h-12 rounded-lg bg-[#DE000033] text-[#DE0000] flex items-center justify-center text-2xl">
                      <RiShieldUserFill width={20} height={20} />
                    </span>
                    <h4 className="mb-0">
                      <span
                        onClick={handleSecondSectionClick}
                        target="_blank"
                        className="text-base font-bold mb-0 font-manrope"
                        prefetch={false}
                      >
                        Delete Account / Disconnect Channel
                      </span>
                    </h4>
                    <Link href="" className="ml-auto" prefetch={false}>
                      <IoIosArrowForward />
                    </Link>
                  </div>
                </div>
              </div>
            </>
          )}
        </section>
      )}

      {activeSection === "disconnectchannel" && (
        <section className="w-full">
          {uploading ? (
            <>
              {" "}
              <div className="h-8 w-48 bg-gray-200 rounded animate-pulse"></div>
              {/* Main Content Skeleton */}
              <div className="h-[calc(100vh-175px)] border border-gray-300 rounded-xl sm:p-5 p-3 mt-5 flex items-center justify-center">
                <div className="text-center lg:w-[60%] sm:w-[80%] space-y-6">
                  {/* First text line skeleton */}
                  <div className="h-6 w-3/4 mx-auto bg-gray-200 rounded animate-pulse"></div>

                  {/* Second paragraph skeleton (3 lines) */}
                  <div className="space-y-3">
                    <div className="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-4 w-4/5 mx-auto bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-4 w-3/4 mx-auto bg-gray-200 rounded animate-pulse"></div>
                  </div>

                  {/* Button skeleton */}
                  <div className="h-10 w-[50%] mx-auto bg-gray-200 rounded-full animate-pulse"></div>
                </div>
              </div>
            </>
          ) : (
            <>
              <h2 className="page-title">Disconnect channel manager</h2>
              <div className="h-[calc(100vh-175px)] border border-gray-300 rounded-xl sm:p-5 p-3 mt-5 flex items-center justify-center">
                <div className="text-center lg:w-[60%] sm:[80%]">
                  <p className="sm:text-xl text-base sm:pb-5 pb-3">
                    Disconnect Channel Manager Before Deleting Profile
                  </p>
                  <p className="sm:text-base text-sm sm:pb-8 pb-6 text-center">
                    To proceed with account deletion, you must first disconnect
                    your channel manager integration. This ensures that no
                    active connections remain
                  </p>
                  <button
                    className="text-white font-bold font-inter text-sm h-10 bg-[#DE0000] rounded-full sm:w-[50%] px-5"
                    onClick={() => setIsModalOpen(true)}
                  >
                    Disconnect Channel Manager
                  </button>
                </div>
              </div>{" "}
            </>
          )}
        </section>
      )}

      {/* Channel Manager */}
      <Dialog
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        className="relative z-50"
      >
        <DialogBackdrop
          transition
          className="fixed inset-0 bg-[#000000B2] transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
        />

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full justify-center p-4 text-center items-center sm:p-0">
            <DialogPanel
              transition
              className="relative transform overflow-hidden rounded-2xl bg-white text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-full sm:max-w-md max-w-full w-full data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95"
            >
              <button
                onClick={() => setIsModalOpen(false)}
                className="absolute top-4 right-4 text-gray-500 hover:text-gray-900"
              >
                <XMarkIcon className="h-6 w-6 text-black font-bold hover:text-slate-400	" />
              </button>

              <div className="bg-white sm:px-7 sm:pb-7 pt-5 p-3 pb-3">
                <div className="mt-4">
                  <div className="mt-3 text-center sm:mt-0 sm:text-left">
                    <DialogTitle
                      as="h3"
                      className="font-semibold text-black sm:text-[22px] text-lg"
                    >
                      Channel Manager Disconnection Pending Review
                    </DialogTitle>
                    <div className="mt-3.5">
                      <p
                        className="text-sm font-normal mb-5"
                        style={{ color: "#888888" }}
                      >
                        Your request to disconnect the Channel Manager has been
                        received. Please note that the review process may take
                        up to one week (maximum) for approval. Once the
                        disconnection status is approved, you will be able to
                        proceed with deleting your profile.
                      </p>
                      <button
                        className="h-10 rounded-full text-center bg-[#EEEEEE] w-full text-sm font-semibold"
                        onClick={handleModalClick}
                      >
                        Thank you for your patience!
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </div>
        </div>
      </Dialog>

      {activeSection === "confirmdelete" && (
        <section className="w-full">
          <h2 className="page-title">Confirm Profile Deletion</h2>
          <div className="h-[calc(100vh-175px)] border border-gray-300 rounded-xl sm:p-5 p-3 mt-5 flex items-center justify-center">
            <div className="text-center lg:w-[60%] sm:[80%]">
              <p className="sm:text-[27px] text-[20px] sm:pb-5 pb-3">
                Confirm Profile Deletion
              </p>
              <p className="sm:text-base text-sm text-center">
                Deleting your profile will permanently remove all data,
                including bookings, settings, and events
              </p>
              <FormControlLabel
                className="flex items-center justify-center gap-2 text-[#6D6D6D] text-base pl-2.5 mt-2"
                control={
                  <Checkbox
                    className="p-0"
                    sx={{
                      "&.Mui-checked": {
                        color: "#40E0D0", // Set the checked color
                      },
                    }}
                  />
                }
                label="I agree to the terms and conditions"
              />
              <button
                className="text-white font-bold font-inter text-sm h-10 bg-[#DE0000] rounded-full sm:w-[50%] px-5 sm:my-8 my-6 "
                onClick={() => setIsModalOpen(true)}
              >
                Disconnect Channel Manager
              </button>
              <p className="text-black font-semibold sm:text-base text-sm">
                Cancellation Option
              </p>
              <p className="sm:text-[27px] text-[20px] sm:pb-5 pb-3">
                Changed Your Mind?
              </p>
              <p className="sm:text-base text-sm text-center">
                You can keep your profile active and explore other features of
                Mixdorm. If you need assistance, contact support
              </p>
              <button className="text-black font-bold font-inter text-sm h-10 bg-[#40E0D0] rounded-full sm:w-[50%] px-5 sm:my-8 my-6">
                Disconnect Channel Manager
              </button>
            </div>
          </div>
        </section>
      )}
    </>
  );
};

export default Profile;
