 
 
import { createContext, useState, useContext, useEffect, useRef } from "react";
import { getItemLocalStorage } from "@/utils/browserSetting";
import {
  getProfileApi,
  propertyDetailsApi,
} from "@/services/ownerflowServices";
import { useRouter } from "next/router";
import Loader from "../loader/loader";

const HeaderContext = createContext();

export const HeaderProvider = ({ children }) => {
  const [profileData, setProfileData] = useState(null);
  const [propertyData, setPropertyData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const isFirstRender = useRef(null);

  const router = useRouter();

  useEffect(() => {
    const storedId = getItemLocalStorage("hopid");
    console.log("storedId", storedId);
    if (storedId && router.pathname.includes("/owner/dashboard")) {
      fetchPropertiesById(storedId);
    }
  }, [router.pathname]);

  const fetchPropertiesById = async (id) => {
    setIsLoading(true);
    try {
      const response = await propertyDetailsApi(id);
      if (response?.status === 200) {
        setPropertyData(response?.data?.data);
      }
    } catch (error) {
      console.error("Error fetching properties:", error.message);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const storedId = getItemLocalStorage("hopid");
    const token = getItemLocalStorage("token");
    const role = getItemLocalStorage("role");

    if (
      !isFirstRender.current &&
      router.pathname.includes("/owner/dashboard") &&
      storedId &&
      token &&
      role === "hostel_owner"
    ) {
      fetchUserData();
    } else {
      isFirstRender.current = false;
    }
  }, [router.pathname]);

  const fetchUserData = async () => {
    setIsLoading(true);
    try {
      const response = await getProfileApi();
      if (response?.status === 200) {
        setProfileData(response?.data?.data);
      }
    } catch (error) {
      console.error("Error fetching profile:", error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const updateuserData = (data) => {
    setProfileData(data);
  };

  return (
    <HeaderContext.Provider
      value={{
        profileData,
        propertyData,
        updateuserData,
      }}
    >
      <Loader open={isLoading} />
      {children}
    </HeaderContext.Provider>
  );
};

export const useHeaderOwner = () => useContext(HeaderContext);
