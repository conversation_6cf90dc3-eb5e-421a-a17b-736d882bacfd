import mongoose from 'mongoose';
const Schema = mongoose.Schema;

const expenseSchema = new Schema({
  description: { 
    type: String, 
    required: true 
  },
  amount: { 
    type: Number, 
    required: true 
  },
  category: { 
    type: String, 
    required: true 
  },
  group: { 
    type: Schema.Types.ObjectId, 
    ref: 'Group', 
    required: true 
  },
  paidBy: { 
    type: Schema.Types.ObjectId, 
    ref: 'User', 
    required: true 
  },
  participants: [
    {
      userId: { 
        type: Schema.Types.ObjectId, 
        ref: 'User', 
        required: true 
      },
      share: { 
        type: Number, 
        required: true 
      },
    },
  ],
  createdAt: { 
    type: Date, 
    default: Date.now 
  },
});

const expenseModel = mongoose.model('Expense', expenseSchema);

export default expenseModel;
