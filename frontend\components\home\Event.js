"use client";

import Image from "next/image";
import { motion, useInView } from "framer-motion";
import { useRef } from "react";
import Link from "next/link";

const EventsSpotlightPage = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: false });

  return (
    <div className="container  flex  justify-center items-center px-4  relative my-16">
      <div className="absolute bottom-1/4 right-[12%] w-40 h-40 bg-cyan-400 rounded-full blur-2xl opacity-30"></div>
      <div className="absolute bottom-1/3 left-1/4 w-40 h-40 bg-pink-400 rounded-full blur-2xl opacity-30 "></div>
      <div className=" w-full flex flex-col-reverse lg:flex-row items-center gap-10 justify-evenly py-20 md:py-0">
        {/* Left Section */}
        <div className="text-left pt-4 lg:pt-0 min-w-[40%]">
          <h2 className="font-mashiny font-normal text-4xl md:text-5xl">
            <span className="text-primary-blue">Events</span> Spotlight
          </h2>
          <p className="text-gray-600 mt-6 text-sm leading-relaxed">
            Travel isn’t just about the destination—it’s about the experiences
            you have along the way. With Mixdorm’s Events feature, you can
            explore exciting hostel activities and city-wide events wherever you
            are. From social gatherings to cultural festivals, Mixdorm connects
            you to events that make your trip unforgettable
          </p>

          <div className="mt-6   p-0">
            <div className="flex items-start space-x-3 ">
              <span className="mt-4 h-16 lg:h-12 w-20 lg:w-14 ">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/smart-event.webp`}
                  width={327}
                  height={670}
                  alt="Mobile UI Mockup"
                  className=""
                  loading="lazy"
                />
              </span>
              <div>
                <h3 className="font-semibold text-gray-800">
                  Smart Event Suggestions
                </h3>
                <p className="text-gray-600 text-sm">
                  Get AI-powered recommendations for events happening near you,
                  from local festivals to exclusive hostel gatherings, all based
                  on your travel interests and location.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3 ">
              <span className="mt-4 h-16 lg:h-12 w-16 lg:w-12 ">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/event-map.webp`}
                  width={327}
                  height={670}
                  alt="Mobile UI Mockup"
                  className=""
                  loading="lazy"
                />
              </span>
              <div>
                <h3 className="font-semibold text-gray-800">
                  Interactive Event Map
                </h3>
                <p className="text-gray-600 text-sm">
                  Easily explore events around you with our real-time event map.
                  Filter by category, date, and distance to find the best
                  experiences nearby.
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <span className="mt-4 h-16 lg:h-12 w-16 lg:w-14 ">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/event-bookings.webp`}
                  width={327}
                  height={670}
                  alt="Mobile UI Mockup"
                  className=""
                  loading="lazy"
                />
              </span>
              <div>
                <h3 className="font-semibold text-gray-800">
                  Instant Event Bookings
                </h3>
                <p className="text-gray-600 text-sm">
                  Reserve your spot for exciting events directly through
                  Mixdorm. Whether it’s a city tour, music festival, or a hostel
                  social night, secure your entry hassle-free!
                </p>
              </div>
            </div>
          </div>
          <Link href={"/discover-event"} aria-label="See all discoverable events" prefetch={false}>
            <button className="mt-6 bg-primary-blue text-black text-xs font-bold font-manrope px-10 py-3 rounded-full shadow-md hover:bg-teal-400">
              See All
            </button>
          </Link>
        </div>
        
        <motion.div ref={sectionRef} className="min-w-[60%] w-full">
          <div className="relative flex gap-4 justify-center items-start -top-10">
            {/* Icon */}
            <div className="absolute -top-2 md:top-4 left-36 md:left-20 2xl:left-36 text-3xl">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/club-icon.webp`}
                width={327}
                height={670}
                alt="icon"
                className="h-12 w-12 md:h-16 md:w-16"
                loading="lazy"
              />
            </div>

            {/* First Card */}
            <motion.div
              className="md:w-96 md:h-96 w-80 h-60 relative left-12 z-10"
              initial={{ opacity: 0, scale: 0.5, y: 200, rotate: -25 }}
              animate={
                isInView ? { opacity: 1, scale: 1, y: 0, rotate: 0 } : {}
              }
              transition={{
                duration: 2,
                ease: "easeOut",
                delay: 2,
              }}
            >
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Club-hostels2.webp`}
                alt="Club hostel event"
                layout="fill"
                className="rounded-lg object-cover"
                loading="lazy"
              />
            </motion.div>

            {/* Second Card */}
            <motion.div
              className="md:w-96 md:h-96 w-80 h-60 relative z-30 right-12 top-6 md:top-12"
              initial={{ opacity: 0, scale: 0.5, y: 200, rotate: -25 }}
              animate={isInView ?{ opacity: 1, scale: 1, y: 0, rotate: 0 }:{}}
              transition={{
                duration: 4,
                ease: "easeOut",
                delay: 3.5,
              }}
            >
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Club-hostels4.webp`}
                alt="Club hostel event"
                layout="fill"
                loading="lazy"
                className="object-cover"
              />
            </motion.div>

            {/* Third Card */}
            <motion.div
              className="md:w-96 md:h-96 w-80 h-60 absolute bottom-20 z-0"
              initial={{ opacity: 0, scale: 0.5, y: 200, rotate: -25 }}
              animate={isInView ?{ opacity: 1, scale: 1, y: 0, rotate: 0 }:{}}
              transition={{
                duration: 1,
                ease: "easeOut",
                delay: 0.5,
              }}
            >
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Club-hostels1.webp`}
                alt="Club hostel event"
                layout="fill"
                loading="lazy"
                className="object-cover"
              />
            </motion.div>

            {/* Fourth Card */}
            <motion.div
              className="md:w-96 md:h-96 w-80 h-60 absolute -bottom-24 md:-bottom-36 z-20 rotate-[4deg]"
              initial={{ opacity: 0, scale: 0.5, y: 200, rotate: -40 }}
              animate={isInView ?{ opacity: 1, scale: 1, y: 0, rotate: 0 }:{}}
              transition={{
                duration: 3,
                ease: "easeOut",
                delay: 4,
              }}
            >
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Club-hostels3.webp`}
                alt="Club hostel event"
                layout="fill"
                loading="lazy"
                className="object-cover"
              />
            </motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default EventsSpotlightPage;
