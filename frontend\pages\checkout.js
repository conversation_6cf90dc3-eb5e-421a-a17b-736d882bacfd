/* eslint-disable react/no-unescaped-entities */
/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
import { AiOutlineQuestionCircle } from "react-icons/ai";
import {
  checkout<PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateOrder,
  getRoomData,
  PaymentVarification,
} from "@/services/webflowServices";
import toast from "react-hot-toast";
import Link from "next/link";
import { useRouter } from "next/router";
import {
  getItemLocalStorage,
  setItemLocalStorage,
  setToken,
} from "@/utils/browserSetting";
import dynamic from "next/dynamic";
import Loader from "@/components/loader/loader";
import { useNavbar } from "@/components/home/<USER>";
import { requestForFCMToken } from "@/utils/firebaseConfig";
import { saveFirebaseToken } from "@/services/ownerflowServices";
import countries from "world-countries";
import Head from "next/head";
import { IoCalendarOutline } from "react-icons/io5";
import { FaUsers, FaWhatsapp } from "react-icons/fa";
import { GiNightSleep } from "react-icons/gi";
import { LiaBedSolid } from "react-icons/lia";
import { MdOutlineBathroom } from "react-icons/md";
import { Checkbox, FormControlLabel } from "@mui/material";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import VisibilityOffOutlinedIcon from "@mui/icons-material/VisibilityOffOutlined";
import { RxCross2 } from "react-icons/rx";

// const Confirmation = dynamic(
//   () => import("@/components/checkout/confirmation"),
//   {
//     ssr: false,
//   }
// );

const Confirmation = dynamic(() => import("@/components/thankyou"), {
  ssr: false,
});

const Checkout = () => {
  const [isPaymentDone, setIsPaymentDone] = useState(false);
  const [data, setData] = useState(null);
  const [roomData, setRoomData] = useState(null);
  const [contactNumber, setContactNumber] = useState(
    () => getItemLocalStorage("contact") || ""
  );
  const [isEditing, setIsEditing] = useState(false);
  const [showpayment, setshowPayment] = useState(false);
  const daata = getItemLocalStorage("bookingdata");
  const [userId, setUserId] = useState("");
  const [userName, setUserName] = useState("");
  const [paymentSuccessResponse, setPaymentSuccessResponse] = useState("");
  const [loading, setLoading] = useState(true);
  const [bookingData, setBookingData] = useState(null);
  const [currency, setCurrency] = useState(null);
  const [selectedRoomsData, setSelectedRoomsData] = useState(null);
  const [showLoginPopup, setShowLoginPopup] = useState(false);
  const [isDataLoaded, setIsDataLoaded] = useState(false);
  const [currencySymbol, setCurrencySymbol] = useState("€");
  const [paymentVerificationResponse, setPaymentVerificationResponse] =
    useState("");
  const [currencyData, setCurrencyData] = useState({});

  const { flagUrl2, currencyCode2 } = useNavbar();
  const { updateUserStatus, updateUserRole, token, role } = useNavbar();

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
  });

  const [errors, setErrors] = useState({
    name: "",
    email: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);

  const [isOpen, setIsOpen] = useState(false);
  const popupRef = useRef(null);

  const [imagesLoaded, setImagesLoaded] = useState({});

  // useEffect(() => {
  //   localStorage.setItem("contactNumber", contactNumber);
  // }, [contactNumber]);

  const handleChangeContact = (e) => {
    setContactNumber(e.target.value);
  };

  const handleEditClick = () => {
    setIsEditing((prev) => !prev);
  };

  // Function to handle image load
  const handleImageLoad = (itemId, imgIndex) => {
    setImagesLoaded((prev) => ({
      ...prev,
      [`${itemId}-${imgIndex}`]: true,
    }));
  };

  const handlePopup = () => {
    setIsOpen(true);
  };

  // Close popup when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  useEffect(() => {
    if (isOpen) {
      document.body.classList.add("overflow-hidden");
    } else {
      document.body.classList.remove("overflow-hidden");
    }

    // Cleanup function to remove class when component unmounts
    return () => {
      document.body.classList.remove("overflow-hidden");
    };
  }, [isOpen]);

  const validateFields = () => {
    let valid = true;
    const newErrors = { name: "", email: "", password: "" };


    if (!formData.email.trim()) {
      newErrors.email = "Email is required.";
      valid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Enter a valid email.";
      valid = false;
    }

    if (!formData.password.trim()) {
      newErrors.password = "Password is required.";
      valid = false;
    }

    setErrors(newErrors);
    return valid;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateFields()) return;
    setLoading(true);
    try {
      const response = await checkoutLoginApi(formData);
      if (response?.data?.status) {
        setItemLocalStorage("name", response?.data?.data?.user?.name.first);
        setItemLocalStorage("id", response?.data?.data?.user?._id);
        setItemLocalStorage("role", response?.data?.data?.user?.role);
        setItemLocalStorage("email", response?.data?.data?.user?.email);
        setItemLocalStorage("contact", response?.data?.data?.user?.contact);
        setContactNumber(response?.data?.data?.user?.contact || "");
        setToken(response?.data?.data?.token);
        updateUserStatus(response?.data?.data?.token);
        updateUserRole(response?.data?.data?.user?.role);
        toast.success(response?.data?.message);
        const fcmToken = await requestForFCMToken();
        if (fcmToken) {
          setItemLocalStorage("FCT", fcmToken);
          await saveFirebaseToken({
            token: fcmToken,
            userId: response?.data?.data?.user?._id,
          });
        }
      }
      console.log("API Response:", response.data);

      // Add success handling logic here (e.g., redirect, show message)
    } catch (error) {
      console.error("API Error:", error.response?.data || error.message);

      // Add error handling logic here (e.g., show error message)
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const storedBookingData = JSON.parse(localStorage.getItem("bookingdata"));
    const storedCurrency = localStorage.getItem("selectedCurrencyCode");
    const storedSelectedRoomsData = JSON.parse(
      localStorage.getItem("selectedRoomsData")
    );
    const selectedCurrencyCode = localStorage.getItem("selectedCurrencyCode");

    setBookingData(storedBookingData);
    setCurrency(storedCurrency);
    setSelectedRoomsData(storedSelectedRoomsData);
    if (selectedCurrencyCode) {
      setCurrencySymbol(selectedCurrencyCode);
    }
  }, []);

  const router = useRouter();

  useEffect(() => {
    const name = getItemLocalStorage("name");
    const id = getItemLocalStorage("id");

    setUserId(id || "");
    setUserName(name || "");
  }, [router]);

  useEffect(() => {
    const name = getItemLocalStorage("name");
    const id = getItemLocalStorage("id");
    setUserId(id || "");
    setUserName(name || "");
  }, []);

  useEffect(() => {
    if (daata) {
      try {
        const parsedData = JSON.parse(daata);
        setData(parsedData);
        setLoading(false);
      } catch (error) {
        console.error("Failed to parse booking data", error);
        setLoading(false);
      }
    }
  }, [daata]);

  console.log("dadasdasd", bookingData, currency, selectedRoomsData);

  useEffect(() => {
    const fetchRoomData = async () => {
      setLoading(true); // Set loading to true while fetching

      if (bookingData && currencyCode2 && selectedRoomsData) {
        const payload = {
          checkIn: bookingData.checkIn,
          checkOut: bookingData.checkOut,
          currency: currencyCode2,
          roomSelections: selectedRoomsData.map((room) => ({
            bedType: room.selectedBed === "Lower Bed" ? "lower" : "",
            roomId: room.roomId,
            beds: room?.count,
            RateTypeId: room?.ratePlanId, // Assuming one bed is selected for each room
          })),
        };

        try {
          // Call your room data API
          const response = await getRoomData(payload);
          if (response?.data?.status) {
            toast.success(response?.data?.message);
            setRoomData(response?.data?.data); // Assuming response.data contains the room data
          } else {
            toast.error(response?.data?.message || "No Data For Checkout");
            router.push("/");
          }
        } catch (error) {
          console.error("Error fetching room data:", error);
          router.push("/");
        } finally {
          setLoading(false); // Set loading to false after fetching

          setIsDataLoaded(true); // Set flag to true after the API call is finished
        }
      }
    };

    // Only trigger fetchRoomData when all dependencies are defined
    if (bookingData && currencyCode2 && selectedRoomsData) {
      fetchRoomData();
    }
  }, [bookingData, currencyCode2, selectedRoomsData]); // Add all necessary dependencies here

  console.log("flagUrl2", flagUrl2);

  // useEffect(() => {
  //   // Check if the component has already mounted
  //   console.log("dasdasdasd",roomData )
  //   if (hasMounted.current) {
  //     setLoading(true);
  //     setItemLocalStorage("selectedRoomsData", null);
  //     router.push(`/hostels-detail/${roomData?.propertyId}`);
  //     toast.error("You changed currency, please add room again!!");
  //   } else {
  //     // Set hasMounted to true after the first render
  //     hasMounted.current = true;
  //   }
  // }, [flagUrl2]);
  // const initialCurrencyCode = useRef(currencyCode2);

  // useEffect(() => {
  //   // Skip the effect if this is the first render or currencyCode2 has not changed
  //   if (currencyCode2 && currencyCode2 !== initialCurrencyCode.current) {
  //     setItemLocalStorage("selectedRoomsData", null);
  //     router.push(`/hostels-detail/${roomData?.propertyId}`);
  //     toast.error("You changed currency, please add room again!!");

  //     // Update the initialCurrencyCode ref to the new value
  //     initialCurrencyCode.current = currencyCode2;
  //   }
  // }, [currencyCode2]);

  console?.log("romasdas", roomData);

  const paymentVarification = async (
    signature,
    paymentType,
    paymentId,
    orderId
  ) => {
    const payload = {
      signature: signature,
      paymentType: paymentType,
      payment_id: paymentId,
      property: roomData?.propertyData?._id,
      amount: roomData?.payableNow,
      currency: roomData?.currency,
      user_id: userId,
      checkIn: data?.checkIn,
      checkOut: data?.checkOut,
      paymentFor: "roombooking",
      order_id: orderId,
      phoneNumber: contactNumber,
      rooms: roomData?.roomsData.map((room) => ({
        roomId: room.id,
        amount: room.roomTotalPrice,
        bedType: room.bedType,
        beds: room.totalBeds,
        rateTypeId: room?.RateTypeId,
      })),
    };
    // const response = await PaymentVarification(payload);
    // if(response?.data?.status === 200){
    //     setIsPaymentDone(true);
    //     setItemLocalStorage("selectedRoomsData", null);
    // }
    // else{
    //   toast.error(response?.data?.data?.error)
    // }

    try {
      const response = await PaymentVarification(payload);
      console.log("varification response", response);

      if (response?.status === 201 || response?.status === 200) {
        setPaymentVerificationResponse(response?.data?.bookingDetails);
        toast.success(response?.data?.message || "Order Placed Successfully");
        setIsPaymentDone(true);
        setItemLocalStorage("selectedRoomsData", null);
      } else {
        toast.error(
          response?.data?.data?.error || "Payment Fail or Something went Wrong"
        );
        router.push("/checkout");
      }
    } catch (error) {
      console.error("Error fetching room data:", error);
      router.push("/checkout");
    }
  };

  const handlePayment = async () => {
    if (!token || role !== "user") {
      toast.error("Please Login first");
      scrollToLoginForm();
      return;
    }
    if (!contactNumber) {
      toast.error("Please enter your contact number before proceeding.");
      return;
    }
    // Load Razorpay script
    const loadRazorpayScript = () => {
      return new Promise((resolve) => {
        const script = document.createElement("script");
        script.src = "https://checkout.razorpay.com/v1/checkout.js";
        script.onload = () => resolve(true);
        script.onerror = () => resolve(false);
        document.body.appendChild(script);
      });
    };

    const res = await loadRazorpayScript();
    if (!res) {
      alert(
        "Failed to load Razorpay SDK. Please check your internet connection."
      );
      return;
    }

    try {
      const payload = {
        amount: roomData?.payableNow, // Amount in INR
        currency: roomData?.currency,
      };
      // Request to create an order
      const response = await CreateOrder(payload);
      const { data } = response;

      const options = {
        key: process.env.NEXT_PUBLIC_RAZORPAY_KEY, // Replace with your Razorpay Key ID
        amount: roomData?.payableNow,
        currency: roomData?.currency,
        name: "Mixdorm",
        description: "Test Transaction",
        order_id: data.id,
        handler: function (response) {
          // toast.success("Payment successful");
          console.log("response successful", response);
          setPaymentSuccessResponse(response);
          paymentVarification(
            response?.razorpay_signature,
            "razorpay",
            response?.razorpay_payment_id,
            data?.id
          );

          // router?.push('/paymentsuccess')

          // Optionally send payment details to your server here
        },
        prefill: {
          name: userName,
          email: getItemLocalStorage("email") || "",
          contact: { contactNumber },
        },
        theme: {
          color: "#3399cc",
        },
      };

      const paymentObject = new window.Razorpay(options);
      paymentObject.open();
    } catch (error) {
      alert("Failed to create order. Please try again.");
      console.error(error);
    }
  };

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);
      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);

  const getCurrencySymbol = (currencyCode) => {
    return currencyData[currencyCode] || currencyCode;
  };

  const loginFormRef = useRef(null); // Ensure useRef is always called

  const scrollToLoginForm = () => {
    if (loginFormRef.current) {
      loginFormRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  if (!loading && !roomData) {
    if (!currency) {
      toast.error(
        "You have not selected any currency type. Please select one!"
      );
      router.push(`/`);
    }
    return <Loader open={true} />;
  }

  const handlePaymentClick = () => {
    if (!token || role !== "user") {
      setShowLoginPopup(true);
      return;
    }
     else if(contactNumber) {
      setshowPayment(true);
      handlePayment();
    }
  };

  return (
    <>
      <Head>
        <title>Secure Checkout | Confirm Your Booking | Mixdorm</title>
      </Head>
      {isPaymentDone ? (
        <div className=' h-screen overflow-y-auto '>
          <div className=' '>
            <Confirmation
              successRes={paymentSuccessResponse}
              data={data}
              roomData={roomData}
              name={userName}
              paymentVerificationResponse={paymentVerificationResponse}
            />
          </div>
        </div>
      ) : (
        <>
          <Loader open={loading} />
          {loading ? (
            <div className='md:pt-16 md:pb-28 py-9 bg-[#F7F7F7]'>
              <div className='container px-4 lg:px-14'>
                <div className='flex flex-col lg:flex-row gap-0.5 h-auto'>
                  {/* Left Column Skeleton */}
                  <div className='w-full lg:w-3/5 2xl:w-[60%]'>
                    <div className='bg-white rounded-r-3xl lg:rounded-r-none rounded-l-3xl'>
                      <div className='max-w-4xl mx-auto bg-white p-6 rounded-lg shadow-sm'>
                        <div className='h-8 w-1/3 bg-gray-200 rounded mb-6 animate-pulse'></div>

                        {/* Gallery Skeleton */}
                        <div className='flex space-x-3 overflow-x-auto mb-6'>
                          {[...Array(4)].map((_, i) => (
                            <div
                              key={i}
                              className='w-24 h-24 rounded-xl bg-gray-200 animate-pulse flex-shrink-0'
                            ></div>
                          ))}
                        </div>

                        {/* Booking Info Skeleton */}
                        <div className='mb-6'>
                          <div className='h-6 w-2/3 bg-gray-200 rounded mb-2 animate-pulse'></div>
                          <div className='h-4 w-1/2 bg-gray-200 rounded mb-1 animate-pulse'></div>
                          <div className='h-4 w-1/2 bg-gray-200 rounded animate-pulse'></div>
                        </div>

                        {/* Bed Details Skeleton */}
                        <div className='h-6 w-1/4 bg-gray-200 rounded mb-4 animate-pulse'></div>
                        {[...Array(2)].map((_, i) => (
                          <div key={i} className='flex flex-col mb-6'>
                            <div className='w-24 h-24 rounded-xl bg-gray-200 animate-pulse mb-2'></div>
                            <div className='h-4 w-3/4 bg-gray-200 rounded mb-2 animate-pulse'></div>
                            <div className='space-y-2'>
                              <div className='h-3 w-full bg-gray-200 rounded animate-pulse'></div>
                              <div className='h-3 w-4/5 bg-gray-200 rounded animate-pulse'></div>
                              <div className='h-3 w-3/4 bg-gray-200 rounded animate-pulse'></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Right Column Skeleton */}
                  <div className='max-w-[480px] mx-auto lg:max-w-full lg:w-2/5 2xl:w-[40%] bg-white rounded-r-3xl rounded-l-3xl lg:rounded-l-none p-7'>
                    <div className='py-4 border-b'>
                      {[...Array(2)].map((_, i) => (
                        <div key={i} className='mb-4'>
                          <div className='h-5 w-1/2 bg-gray-200 rounded mb-3 animate-pulse'></div>
                          <div className='flex justify-between mb-2'>
                            <div className='h-4 w-2/3 bg-gray-200 rounded animate-pulse'></div>
                            <div className='h-4 w-1/4 bg-gray-200 rounded animate-pulse'></div>
                          </div>
                          <div className='h-4 w-1/2 bg-gray-200 rounded animate-pulse'></div>
                        </div>
                      ))}
                      <div className='flex justify-between'>
                        <div className='h-4 w-1/4 bg-gray-200 rounded animate-pulse'></div>
                        <div className='h-4 w-1/6 bg-gray-200 rounded animate-pulse'></div>
                      </div>
                    </div>

                    <div className='py-4 border-b flex justify-between'>
                      <div className='h-4 w-3/4 bg-gray-200 rounded animate-pulse'></div>
                      <div className='h-4 w-4 bg-gray-200 rounded-full animate-pulse'></div>
                    </div>

                    <div className='py-4'>
                      <div className='flex justify-between mb-4'>
                        <div className='h-5 w-1/2 bg-gray-200 rounded animate-pulse'></div>
                        <div className='h-4 w-1/4 bg-gray-200 rounded animate-pulse'></div>
                      </div>
                      <div className='h-12 w-full bg-gray-200 rounded-full animate-pulse'></div>
                    </div>

                    <div className='flex justify-between mb-6'>
                      <div className='h-5 w-1/4 bg-gray-200 rounded animate-pulse'></div>
                      <div className='h-4 w-1/4 bg-gray-200 rounded animate-pulse'></div>
                    </div>

                    <div className='mb-4'>
                      <div className='h-5 w-1/2 bg-gray-200 rounded mb-3 animate-pulse'></div>
                      <div className='relative'>
                        <div className='h-12 w-full bg-gray-200 rounded-full animate-pulse'></div>
                        <div className='absolute right-1 top-1 h-10 w-24 bg-gray-300 rounded-full animate-pulse'></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className='md:pt-16 md:pb-28 py-9 bg-[#F7F7F7]'>
              <div className='container px-4 lg:px-14'>
                {(!token || role !== "user") && (
                  <p className='py-3 px-5 bg-white text-black rounded-3xl mb-5 text-sm'>
                    Returning customer?{" "}
                    <Link
                      href='#'
                      className='text-black hover:text-primary-blue font-bold'
                      prefetch={false}
                      onClick={(e) => {
                        e.preventDefault(); // Prevent default link behavior
                        scrollToLoginForm();
                        handlePaymentClick();
                      }}
                    >
                      Click here to login
                    </Link>
                  </p>
                )}
                <div className='flex flex-col lg:flex-row gap-0.5 h-auto'>
                  <div className='w-full lg:w-3/5 2xl:w-[60%]'>
                    <div className='bg-white rounded-r-3xl lg:rounded-r-none rounded-l-3xl '>
                      <div className='max-w-4xl mx-auto bg-white p-6 rounded-lg shadow-sm'>
                        <h2 className='text-lg font-manrope font-bold mb-4'>
                          Your Booking Details
                        </h2>

                        {/* Gallery */}
                        {/* <div className="flex space-x-3 overflow-x-auto mb-4"  style={{
                     scrollbarWidth: 'thin',
                     scrollbarColor: '#40E0D0 #f1f1f1', 
                     }}>
                        {roomData?.propertyData?.images?.map((img, i) => (
                          <div
                            key={i}
                            className="w-24 h-24 rounded-xl overflow-hidden flex-shrink-0 "
                          >
                            <Image
                              src={img?.objectUrl}
                              alt={`Image ${i + 1}`}
                              width={96}
                              height={96}
                              className="object-cover w-full h-full"
                              loading="lazy"
                            />
                          </div>
                        ))}
                      </div> */}
                        <div
                          className='flex space-x-3 overflow-x-auto mb-4'
                          style={{
                            scrollbarWidth: "thin",
                            scrollbarColor: "#40E0D0 #f1f1f1",
                            paddingBottom: "6px",
                          }}
                        >
                          {roomData?.propertyData?.images?.length ? (
                            roomData.propertyData.images.map((img, i) => {
                              const isLoaded =
                                imagesLoaded[`${roomData._id}-${i}`];
                              return (
                                <div
                                  key={i}
                                  className='w-24 h-24 rounded-xl overflow-hidden flex-shrink-0 relative'
                                >
                                  {!isLoaded && (
                                    <div className='w-full h-full bg-gray-200 animate-pulse absolute flex items-center justify-center'>
                                      <h1 className='text-gray-400 font-bold text-xl'>
                                        Mixdorm
                                      </h1>
                                    </div>
                                  )}
                                  <Image
                                    src={img?.objectUrl}
                                    alt={`Image ${i + 1}`}
                                    width={96}
                                    height={96}
                                    className={`object-cover w-full h-full ${
                                      !isLoaded ? "opacity-0" : "opacity-100"
                                    }`}
                                    loading={i === 0 ? "eager" : "lazy"}
                                    quality={85}
                                    priority={i === 0}
                                    onLoadingComplete={() =>
                                      handleImageLoad(roomData._id, i)
                                    }
                                  />
                                </div>
                              );
                            })
                          ) : (
                            <div className='w-24 h-24 rounded-xl overflow-hidden flex-shrink-0 bg-gray-200 animate-pulse flex items-center justify-center'>
                              <h1 className='text-gray-400 font-bold text-xl'>
                                Mixdorm
                              </h1>
                            </div>
                          )}
                        </div>

                        {/* Booking Info */}
                        <div className='mb-6'>
                          <h3 className='text-lg font-manrope font-bold'>
                            {roomData?.propertyData?.name}
                          </h3>
                          <div className='text-gray-600 text-sm flex items-center mt-1'>
                            <span className='mr-2 flex text-xs font-manrope text-black'>
                              <IoCalendarOutline className='text-primary-blue text-sm font-bold mr-1' />{" "}
                              {roomData?.checkIn}, {roomData?.noOfDays}{" "}
                              {roomData?.noOfDays === 1 ? "Night" : "Nights"}
                            </span>
                          </div>
                          <div className='text-gray-600 text-sm flex items-center mt-1'>
                            <span className='mr-2 flex text-xs font-manrope text-black'>
                              <FaUsers className='text-primary-blue text-sm font-bold mr-1' />{" "}
                              {data?.guest}{" "}
                              {data?.guest === 1 ? "Guest" : "Guests"}
                            </span>
                          </div>
                        </div>

                        {/* Bed Details */}
                        <h3 className='text-base font-manrope font-bold mb-2'>
                          Bed Details
                        </h3>
                        {roomData?.roomsData?.map((room, index) => (
                          <div key={index} className='flex flex-col mb-4'>
                            <div className='w-24 h-24 rounded-xl overflow-hidden'>
                              <Image
                                src={
                                  roomData?.propertyData?.images[1]?.objectUrl
                                }
                                alt='Room Image'
                                width={96}
                                height={96}
                                className='object-cover w-full h-full'
                                loading='lazy'
                              />
                            </div>
                            <div className='flex-1'>
                              <p className='font-manrope font-bold text-base mt-2'>
                                {room?.roomName} (
                                {{
                                  EP: "Room Only",
                                  CP: "With Breakfast",
                                  MAP: "With Breakfast + L/D",
                                  AP: "All Meals",
                                }[room?.ratePlanName] || room?.ratePlanName}
                                )
                              </p>
                              <ul className='text-gray-600 text-sm mt-1 space-y-1'>
                                <li className='mr-2 flex text-xs font-manrope text-black'>
                                  <GiNightSleep className='text-primary-blue text-sm font-bold mr-1' />{" "}
                                  Sleeps {roomData?.noOfDays}
                                </li>
                                <li className='mr-2 flex text-xs font-manrope text-black'>
                                  <LiaBedSolid className='text-primary-blue text-sm font-bold mr-1' />{" "}
                                  {room?.bedType}
                                </li>
                                <li className='mr-2 flex text-xs font-manrope text-black'>
                                  <MdOutlineBathroom className='text-primary-blue text-sm font-bold mr-1' />{" "}
                                  Shared Bathroom
                                </li>
                              </ul>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className='max-w-[480px] mx-auto lg:max-w-full lg:w-2/5 2xl:w-[40%] bg-white rounded-r-3xl rounded-l-3xl lg:rounded-l-none p-7'>
                    {/* <h4 className="mb-5 text-lg font-bold">
                    Your Booking Summary
                  </h4>
                  <div className="flex items-center pb-4 border-b gap-4">
                    <Image
                      src={`https://${roomData?.propertyFirstImage?.url}`}
                      width={10}
                      height={10}
                      alt="Room"
                      className="w-24 h-24 rounded-xl object-cover"
                      loading="lazy"
                    />
                    <div className="flex-1">
                      <p className="text-sm font-bold text-black mb-2">
                        {roomData?.propertyName}
                      </p>
                      <p className="text-[12px] font-medium flex items-center gap-1 text-black mb-1">
                        <span className="text-primary-blue text-base">
                          <LuCalendarDays />
                        </span>{" "}
                        {roomData?.checkIn} ,{roomData?.noOfDays} nights
                      </p>
                      <p className="text-[12px] font-medium flex items-center gap-1 text-black">
                        <span className="text-primary-blue text-base">
                          <TbUsersGroup />
                        </span>{" "}
                        {data?.guest} guests
                      </p>
                    </div>
                  </div> */}
                    <div className='py-4 border-b'>
                      {roomData?.roomsData.map((room) => (
                        <div key={room?.roomId} className=''>
                          <p className='text-sm font-bold text-black font-manrope'>
                            {room.roomName} (
                            {{
                              EP: "Room Only",
                              CP: "With Breakfast",
                              MAP: "With Breakfast + L/D",
                              AP: "All Meals",
                            }[room?.ratePlanName] || room?.ratePlanName}
                            )
                          </p>
                          <div className='py-2 flex items-center justify-between '>
                            <p className='py-1 text-sm font-medium font-manrope text-black'>
                              {getCurrencySymbol(roomData?.currency)}{" "}
                              {room?.perDayRoomPrice} x {room?.totalBeds || 1}{" "}
                              {room?.totalBeds === 1
                                ? "Bed/Room"
                                : "Beds/Rooms"}{" "}
                              x {roomData?.noOfDays || 1}{" "}
                              {roomData?.noOfDays === 1 ? "Night" : "Nights"}
                            </p>
                            <p className='text-sm font-medium text-black pt-1 font-manrope'>
                              {getCurrencySymbol(roomData?.currency)}{" "}
                              {room?.roomTotalPrice}
                            </p>
                          </div>
                          {room?.bedType === "lower" && (
                            <div className='py-2 flex items-center justify-between '>
                              <p className='py-1 text-sm font-medium text-black'>
                                Lower Bed Service
                              </p>
                              <p className='text-sm font-medium text-black'>
                                {getCurrencySymbol(roomData?.currency)}{" "}
                                {room?.lowerBedSurcharge}
                              </p>
                            </div>
                          )}
                        </div>
                      ))}
                      <div className='flex items-center justify-between'>
                        <p className='text-sm font-medium font-manrope'>
                          Taxes & Fees
                        </p>
                        <p className='text-sm font-medium font-manrope'>{getCurrencySymbol(roomData?.currency)} 0
                        </p>
                      </div>
                    </div>

                    {/* <div className='py-4 border-b flex items-center justify-between'>
                      <p className='text-xs font-manrope font-bold text-black'>
                        Full Payment Confirmed Booking / Partial Payment Confirm
                        Booking
                      </p>
                      <span>
                        <AiOutlineQuestionCircle />
                      </span>
                    </div> */}

                    <div className='py-4'>
                      <div className='flex items-center justify-between'>
                        <p className='text-base text-black font-bold font-manrope'>
                          Payable on Arrival
                        </p>
                        <p className='text-sm font-medium font-manrope'>
                          {roomData?.payableOnArrival}{" "}
                          {getCurrencySymbol(roomData?.currency)}
                        </p>
                      </div>
                      <div className='py-4'>
                        <button
                          className='w-full font-semibold py-3 bg-primary-blue hover:bg-sky-blue-750 hover:text-white rounded-full'
                          onClick={() => {
                            setshowPayment(true);
                            handlePayment();
                            handlePaymentClick();
                          }}
                          // onClick={handlePopup}
                        >
                          Payable Now - {roomData?.payableNow}{" "}
                          {getCurrencySymbol(roomData?.currency)}
                        </button>
                        {showLoginPopup && !token && (
                          <div
                            className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 '
                            onClick={() => setShowLoginPopup(false)}
                          >
                            <div
                              className='bg-white rounded-xl px-6 py-2 max-w-md w-full mx-4 relative'
                              onClick={(e) => e.stopPropagation()}
                            >
                              <div ref={loginFormRef} className=' py-6'>
                                {!token && (
                                  <div ref={loginFormRef} className=' '>
                                    <div className='flex justify-between items-center pb-8'>
                                      <h3 className='text-xl font-bold'>
                                        Login In or Sign Up to Book
                                      </h3>
                                      <button
                                        onClick={() => setShowLoginPopup(false)}
                                        className='text-gray-500 hover:text-gray-700 absolute right-2 top-2'
                                      >
                                        <RxCross2 />
                                      </button>
                                    </div>
                                    <form onSubmit={handleSubmit}>
                                      <div className='mb-5'>
                                        <input
                                          type='email'
                                          name='email'
                                          value={formData.email}
                                          onChange={handleChange}
                                          className='w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500'
                                          placeholder='Email'
                                        />
                                        {errors.email && (
                                          <p className='text-red-500 text-sm'>
                                            {errors.email}
                                          </p>
                                        )}
                                      </div>
                                      <div className='relative mb-5'>
                                        <input
                                          type={
                                            showPassword ? "text" : "password"
                                          }
                                          name='password'
                                          value={formData.password}
                                          onChange={handleChange}
                                          className='w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500'
                                          placeholder='Password'
                                        />
                                        <button
                                          type='button'
                                          className='absolute right-4 top-[15px] text-gray-300 transform'
                                          onClick={() =>
                                            setShowPassword(!showPassword)
                                          }
                                        >
                                          {showPassword ? (
                                            <VisibilityOffOutlinedIcon />
                                          ) : (
                                            <VisibilityOutlinedIcon />
                                          )}
                                        </button>
                                        {errors.password && (
                                          <p className='text-red-500 text-sm'>
                                            {errors.password}
                                          </p>
                                        )}
                                      </div>
                                      <div className='flex justify-between items-center mb-8'>
                                        <FormControlLabel
                                          control={
                                            <Checkbox
                                              className='text-sm'
                                              sx={{
                                                color: "rgba(0,0,0,0.4)",
                                                "&.Mui-checked": {
                                                  color: "#40E0D0",
                                                },
                                              }}
                                            />
                                          }
                                          label={
                                            <span className='text-sm text-black'>
                                              Remember me
                                            </span>
                                          }
                                        />
                                        <Link
                                          href='/forgot-password'
                                          className='text-[#40E0D0] font-normal ml-1 cursor-pointer text-sm'
                                          prefetch={false}
                                        >
                                          Forgot password?
                                        </Link>
                                      </div>
                                      <button
                                        type='submit'
                                        className='w-full bg-[#40E0D0] font-semibold text-white py-4 rounded-full hover:bg-[#40E0D0] transition duration-200'
                                      >
                                        Sign In
                                      </button>
                                    </form>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        )}
                        {isOpen && (
                          <div className=''>
                            <div
                              ref={popupRef}
                              className='fixed inset-0  justify-center items-center top-20 left-16 hidden lg:flex z-50 '
                            >
                              <div className=' p-6 rounded-2xl shadow-xl w-[650px] h-[466px] text-center  flex flex-col items-center justify-around z-20 bg-slate-900'>
                                <Image
                                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Coming-Soon.png`}
                                  alt='Coming Soon'
                                  width={300}
                                  height={200}
                                  className='w-[455px] h-[288px]'
                                />
                                <h2 className='text-lg font-medium mt-4 font-inter px-10 text-white '>
                                  Hang tight! This section is getting a refresh.
                                  We'll be back soon with something{" "}
                                  <span className='text-white font-serif text-xl font-bold'>
                                    AMAZING!
                                  </span>
                                </h2>
                              </div>
                            </div>
                            <div className='lg:hidden fixed inset-0  flex justify-center items-center md:left-48 p-4 z-50'>
                              <div
                                ref={popupRef}
                                className='  p-4 md:p-6 rounded-2xl shadow-xl w-full max-w-[90%] md:max-w-[500px] lg:w-[650px] lg:h-[466px] text-center flex flex-col items-center justify-around z-20 bg-slate-900'
                              >
                                <Image
                                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Coming-Soon.png`}
                                  alt='Coming Soon'
                                  width={300}
                                  height={200}
                                  className='w-[300px] h-[180px] md:w-[400px] md:h-[250px] lg:w-[455px] lg:h-[288px]'
                                />
                                <h2 className='text-base md:text-lg font-medium mt-4 font-inter px-4 md:px-10 text-white '>
                                  Hang tight! This section is getting a refresh.
                                  We'll be back soon with something{" "}
                                  <span className='text-white font-serif text-lg md:text-xl font-bold'>
                                    AMAZING!
                                  </span>
                                </h2>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className=' flex items-center justify-between pb-8'>
                      <p className='text-base font-bold'>Total</p>
                      <p className='text-sm font-medium'>
                        {roomData?.totalAmount}{" "}
                        {getCurrencySymbol(roomData?.currency)}
                      </p>
                    </div>

                    {/* {
                        showpayment ? <> */}
                    <div>
                      <label
                        htmlFor='couponCode'
                        className='text-base font-medium block mb-2 font-manrope'
                      >
                        Contact Number
                      </label>
                      <div className='relative'>
                        <input
                          type="tel"
                          id='contact'
                          placeholder='Enter Contact Number'
                          value={contactNumber}
                          onChange={handleChangeContact}
                          disabled={!isEditing}
                          className={`w-full p-3 pr-24 border rounded-3xl text-black placeholder:text-gray-400 font-light placeholder:text-sm placeholder:font-manrope ${
                            !isEditing ? "bg-gray-100 cursor-not-allowed" : ""
                          }`}
                        />
                        <button
                          onClick={handleEditClick}
                          className='absolute right-1 top-2 h-9 w-9 bg-black text-white rounded-full font-manrope hover:bg-primary-blue hover:text-black text-sm flex items-center justify-center'
                        >
                          {isEditing ? "✔" : "✎"}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </>
  );
};

export default Checkout;
