import React, { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import toast, { Toaster } from "react-hot-toast";
import { verifyPropetyApi } from "@/services/ownerflowServices";
import { setItemLocalStorage } from "@/utils/browserSetting";
import { searchGetApi } from "@/services/hostemproviderservices";

const ExistingList = () => {
  const [hostelname, setHostelname] = useState("");
  const [hostelId, setHostelId] = useState("");
  const [hostelSuggestions, setHostelSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isTyping, setIsTyping] = useState(false); 
  const debounceTimeout = useRef(null);
  const router = useRouter();

  console.log("hostelname",hostelname)

  useEffect(() => {
    if (isTyping && hostelname) {
      debounceFetchHostelSuggestions(hostelname);
    } else {
      setHostelSuggestions([]);
      setIsDropdownOpen(false);
    }
  }, [hostelname, isTyping]); 

  const debounceFetchHostelSuggestions = (query) => {
    if (debounceTimeout.current) clearTimeout(debounceTimeout.current);

    debounceTimeout.current = setTimeout(() => {
      fetchHostelSuggestions(query);
    }, 500);
  };

  const fetchHostelSuggestions = async (query) => {
    try {
      const res = await searchGetApi(query);
      if (res?.status === 200) {
        const suggestions = res?.data?.data?.hostels?.map((hostel) => ({
          id: hostel._id,
          name: hostel.name,
        }));
        setHostelSuggestions(suggestions);
        setIsDropdownOpen(true); 
      }
    } catch (error) {
      console.error("Error fetching hostel suggestions:", error);
      toast.error("Failed to fetch hostel suggestions. Please try again.");
    }
  };

  const handleHostelSelect = (id,name) => {
    setIsTyping(false); 
    setHostelname(name);
    setHostelId(id);
    setIsDropdownOpen(false);
  };

  const handleInputChange = (e) => {
    setHostelname(e.target.value);
    setIsTyping(true); 
  };

  const handleVerify = async (e) => {
    e.preventDefault();
  
    if (!hostelname) {
      alert("Please select a hostel");
      return;
    }
  
    setLoading(true);
  
    try {
      const payload = { id: hostelId }; 
  
      const response = await verifyPropetyApi(payload);
      console.log("response",response)
      if(response?.status === 409){
        toast.error(response?.data?.message);
      }
      if (response?.data?.status) {
        toast.success(response?.data?.message);
        setItemLocalStorage("hopid", response?.data?.data?._id);
        router.push("/owner/dashboard");
      }
    } catch (error) {
      console.error("Verification failed:", error);
      toast.error(error.response?.data?.message || "Verification failed");
      router.push("/owner/add-property");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    return () => {
      if (debounceTimeout.current) clearTimeout(debounceTimeout.current);
    };
  }, []);

  return (
    <>
      <Toaster position="top-center" />
      <div className="h-auto flex items-center justify-center bg-[#F7F7F7] w-full pt-[3rem] pb-[20rem]">
        <div className="bg-white rounded-3xl shadow-md w-full max-w-3xl p-6 md:p-14 mb-10 py-4">
          <h2 className="text-2xl font-bold text-gray-800 py-6">
            👋 Welcome To <span className="text-primary-blue">Mix</span>Dorm
          </h2>
          <div className="py-3">
            <h4>
              Already have an account? In any OTA! You can directly verify here!
            </h4>
          </div>
          <form onSubmit={handleVerify}>
            <div className="py-4">
              <label
                htmlFor="hostelname"
                className="block font-semibold text-gray-700"
              >
                Hostel Name
              </label>
              <div className="relative">
                <input
                  id="hostelname"
                  name="hostelname"
                  value={hostelname}
                  onChange={handleInputChange} 
                  onFocus={() => setIsDropdownOpen(true)} 
                  className="w-full px-4 py-3 mt-2 border rounded-3xl focus:outline-none focus:ring-[#40E0D0] text-[#888888] appearance-none"
                  style={{ background: "none", paddingRight: "2.5rem" }}
                  aria-controls="hostel-suggestions"
                  aria-expanded={isDropdownOpen}
                />
                {isDropdownOpen && hostelSuggestions.length > 0 && (
                  <ul
                    id="hostel-suggestions"
                    className="absolute left-0 right-0 z-10 bg-white border border-gray-300 rounded-sm mt-2 max-h-80 overflow-y-auto p-0"
                  >
                    {hostelSuggestions.map((hostel) => (
                      <li
                        key={hostel.id}
                        className="px-4 py-2 cursor-pointer text-base hover:bg-[#40E0D0] hover:text-white font-semibold"
                        onClick={() => handleHostelSelect(hostel.id,hostel.name)}
                      >
                        {hostel.name}
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </div>
            <button
              type="submit"
              className="w-full mb-4 bg-[#40E0D0] mt-4 font-semibold text-black py-3 rounded-4xl hover:bg-[#40E0D0] transition duration-200"
              disabled={loading} 
            >
              {loading ? "Verifying..." : "Verify"}
            </button>
          </form>
        </div>
      </div>
    </>
  );
};

export default ExistingList;
