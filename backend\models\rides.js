import mongoose from 'mongoose';

const rideSchema = new mongoose.Schema({
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
    fromAddress: {
        address: {
            type: String
        },
        location: {
            type: { type: String },
            coordinates: { type: Array, default: [] },
        },
    },
    destination: {
        address: {
            type: String
        },
        location: {
            type: { type: String },
            coordinates: { type: Array, default: [] },
        },
    },
    date: {
        type: Date
    },
    time: {
        start: {
            type: String
        },
        end: {
            type: String
        }
    },
    passenger: {
        type: Number
    },
    transportMode: {
        type: String,
        enum: ['bus', 'private_car', 'cab', 'bike']
    },
    name: {
        type: String
    },
    model: {
        type: String
    },
    number: {
        type: String
    },
    capacity: {
        type: Number
    },
    photos: [{
        filename: {
            type: String
        },
        path: {
            type: String
        },
        url: {
            type: String
        }
    }],
    isCancelled: {
        type: Boolean,
        default: false,
    },
    isDeleted: {
        type: Boolean,
        default: false,
    },
    price:
    {
        type:Number
    },
    currency:{
        type:String
    }
},{
    timestamps:true
});

const rideModel = mongoose.model('rides', rideSchema);

export default rideModel;
