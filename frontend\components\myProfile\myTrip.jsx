import Image from "next/image";
import React from "react";
import Link from "next/link";


const MyTripsPage = () => {
  return (
    <>
      <h2 className="text-[#40E0D0] flex gap-1 text-2xl font-bold mb-6">
        My
        <span className="text-black ml-0.5"> Trip</span>
      </h2>

      <div className=" max-w-xs  mx-auto">
        <div className="flex flex-col items-center justify-center">
          <Image
            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Roadtrip1.jpg`}
            // src={"/Roadtrip1.jpg"}
            width={320}
            height={245}
            alt="trip"
            className="mb-4"
            loading="lazy"
          />

           <p className=" mt-1 font-semibold text-center text-primary-blue">
         No Trip Yet? 
          </p>
          <p className=" font-semibold text-center">
          Let’s Hit the Road !
          </p>
          <Link
            href=""
            className="bg-primary-blue hover:bg-sky-blue-750 hover:text-white text-black px-4 py-4 mt-4 rounded-full w-full text-center font-semibold"
            prefetch={false}
          >
            Go To Search Trip
          </Link>
        </div>
      </div>
    </>
  );
};

export default MyTripsPage;
