export const getContentSchema = ({
  title,
  description,
  authorName,
  datePublished,
  dateModified,
  imageUrl,
  url,
}) => ({
  "@context": "https://schema.org",
  "@type": "Article",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": url
  },
  "headline": title,
  "description": description,
  "image": [imageUrl],
  "author": {
    "@type": "Person",
    "name": authorN<PERSON>
  },
  "publisher": {
    "@type": "Organization",
    "name": "Mixdorm",
    "logo": {
      "@type": "ImageObject",
      "url": `${process.env.NEXT_PUBLIC_S3_URL_FE}/mixdorm-logo.jpg`
    }
  },
  "datePublished": datePublished,
  "dateModified": dateModified || datePublished
});
