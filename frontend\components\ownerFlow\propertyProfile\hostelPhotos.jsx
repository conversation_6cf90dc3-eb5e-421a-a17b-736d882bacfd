import React, { useState, Fragment, useRef } from "react";
import axios from "axios";
import { FaRegTrashCan } from "react-icons/fa6";
import toast from "react-hot-toast";
import { editPropertyApi } from "@/services/ownerflowServices";
import { BASE_URL } from "@/utils/api";
import { getToken } from "@/utils/browserSetting";
import Loader from "@/components/loader/loader";
import { Dialog, Transition } from "@headlessui/react";
import CustomSelect from "@/components/common/CustomDropdown";
import { CloudUpload } from "lucide-react";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import { CircleChevronLeft, CircleChevronRight } from "lucide-react";

// Separate component for photo items to avoid hooks violation
const PhotoItem = ({ photo, index, openEdithostel, handleDelete, setPreviewIndex, setActiveIndex, setImagePreviewModal }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  return (
    <div
      className="relative max-w-[252px] min-h-[168px] max-h-[168px] w-full"
      onClick={() => {
        setPreviewIndex(index);
        setActiveIndex(index);
        setImagePreviewModal(true);
      }}
    >
      {(isLoading || hasError || !photo?.objectUrl) && (
        <div className="absolute inset-0 bg-gray-200 rounded-lg flex items-center justify-center">
          <span className="text-base font-bold text-gray-400">
            MixDorm
          </span>
        </div>
      )}

      {photo?.objectUrl && (
        /\.(mp4|webm|ogg|mov|m4v)$/i.test(photo.objectUrl) ? (
          <video
            className={`object-cover w-full h-full rounded-lg transition-transform duration-1000 ease-in-out group-hover:scale-110  ${
              isLoading || hasError ? "opacity-0" : "opacity-100"
            }`}
            src={`${photo?.objectUrl}`}
            muted
            playsInline
            loop
            autoPlay
            onLoadedData={() => {
              setIsLoading(false);
              setHasError(false);
            }}
            onError={() => {
              setIsLoading(false);
              setHasError(true);
            }}
          />
        ) : (
          <Image
            className={`object-cover w-full h-full rounded-lg transition-transform duration-1000 ease-in-out group-hover:scale-110  ${
              isLoading || hasError ? "opacity-0" : "opacity-100"
            }`}
            // src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/${photo?.url}`}
            src={`${photo?.objectUrl}`}
            alt="hostel photo"
            title={photo?.title}
            width={252}
            height={168}
            onLoad={() => {
              setIsLoading(false);
              setHasError(false);
            }}
            onError={() => {
              setIsLoading(false);
              setHasError(true);
            }}
          />
        )
      )}

      {openEdithostel && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleDelete(photo._id);
          }}
          className="absolute top-2 right-0 translate-x-[-10%] bg-red-600 text-white p-1 rounded-full h-8 w-8 flex items-center justify-center"
        >
          <FaRegTrashCan />
        </button>
      )}
    </div>
  );
};

const HostelPhotos = ({ id, data, updatePropertyData, loading }) => {

  // eslint-disable-next-line no-unused-vars
  const [selectedFiles, setSelectedFiles] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [presignedUrls, setPresignedUrls] = useState([]);
  // const [edit, setEdit] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [openAddhostel, setOpenAddhostel] = useState(false);
  const closehostelModal = () => setOpenAddhostel(false);
  const [openEdithostel, setopenEdithostel] = useState(false);


    const [imagePreviewModal, setImagePreviewModal] = useState(false);
  const [previewIndex, setPreviewIndex] = useState(0);
  const [activeIndex, setActiveIndex] = useState(0);

  const mainSwiperRef = useRef(null);
  const mainThumbRef = useRef(null);
  // const closehosteleditModal = () => setopenEdithostel(false);

  const [areas, setAreas] = useState([
    { areaName: "", files: [], presignedUrls: [] }, // Initial field
  ]);

  const handleFileChange = async (e, index) => {
    const { files } = e.target;
    const newFiles = Array.from(files);

    if (newFiles.length > 0) {
      setIsLoading(true);
      try {
        const uploadPromises = newFiles.map(async (file) => {
          const formData = new FormData();
          formData.append("files", file);

          const presignedUrlResponse = await fetch(
            `${BASE_URL}/fileUpload/generate-presigned-url`,
            {
              method: "POST",
              body: formData,
            }
          );

          if (!presignedUrlResponse.ok) {
            throw new Error("Failed to get presigned URL");
          }

          const presignedUrlData = await presignedUrlResponse.json();
          return {
            file,
            objectURL:
              Array.isArray(presignedUrlData.data) &&
              `${process.env.NEXT_PUBLIC_S3_URL_FE}/${ presignedUrlData.data[0]?.path}`
          };
        });

        const results = await Promise.all(uploadPromises);

        // Update the specific area's files and URLs
        setAreas((prevAreas) => {
          const updatedAreas = [...prevAreas];
          const currentArea = updatedAreas[index];

          updatedAreas[index] = {
            ...currentArea,
            files: [
              ...(currentArea.files || []),
              ...results.map((r) => r.file),
            ],
            presignedUrls: [
              ...(currentArea.presignedUrls || []),
              ...results.map((r) => r.objectURL),
            ],
          };

          return updatedAreas;
        });

        toast.success(
          `${newFiles.length} ${
            newFiles.length === 1 ? "file" : "files"
          } added successfully`
        );
      } catch (error) {
        console.error("Error getting presigned URLs:", error);
        toast.error("Error preparing files for upload.");
      } finally {
        setIsLoading(false);
        e.target.value = "";
      }
    }
  };

  console.log("presignedUrl", presignedUrls, selectedFiles);

  const handleUpload = async () => {
    const hasFiles = areas.some(
      (area) => area.presignedUrls && area.presignedUrls.length > 0
    );

    if (!hasFiles) {
      toast.error("No files selected for upload.");
      return;
    }

    setIsLoading(true);
    try {
      // Prepare payload with area information
      const photos = areas.reduce((acc, area) => {
        if (area.presignedUrls && area.presignedUrls.length > 0) {
          const areaPhotos = area.presignedUrls.map((url) => ({
            action: "add",
            objectUrl: url,
            // areaName: area.areaName || "hostel", // Default to hostel if no area selected
          }));
          return [...acc, ...areaPhotos];
        }
        return acc;
      }, []);

      const payload = { images: photos };

      await axios.put(`${BASE_URL}/property/${id}`, payload, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });

      toast.success("Photos uploaded successfully!");
      // Reset the form
      setAreas([{ areaName: "", files: [], presignedUrls: [] }]);
      setOpenAddhostel(false);
      updatePropertyData();
    } catch (error) {
      console.error("Error uploading files:", error);
      toast.error("Error uploading photos.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (_id) => {
    setIsLoading(true);
    try {
      const payload = {
        images: [
          {
            _id: _id,
            action: "delete",
          },
        ],
      };

      if (id) {
        const response = await editPropertyApi(id, payload);
        if (response?.data?.status) {
          toast.success(
            response?.data?.message || "Property updated successfully!"
          );

          await updatePropertyData();
        }
      } else {
        toast.error("No property data available to update.");
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Failed to submit property details.");
    } finally {
      setIsLoading(false);
    }
  };

  // const handleAddMore = () => {
  //   setAreas([...areas, { areaName: "", files: [], presignedUrls: [] }]);
  // };

  // const handleRemove = (index) => {
  //   const updatedAreas = areas.filter((_, i) => i !== index);
  //   setAreas(updatedAreas);
  // };

  // Add a function to remove individual files from an area
  const removeFileFromArea = (areaIndex, fileIndex) => {
    const updatedAreas = [...areas];
    updatedAreas[areaIndex] = {
      ...updatedAreas[areaIndex],
      files: updatedAreas[areaIndex].files.filter((_, i) => i !== fileIndex),
      presignedUrls: updatedAreas[areaIndex].presignedUrls.filter(
        (_, i) => i !== fileIndex
      ),
    };
    setAreas(updatedAreas);
  };

  return (
    <>
      <Loader open={isLoading} />
      {loading || isLoading ? (
        <>
          <div>
            <div className="flex justify-between items-center mb-2">
              <div className="h-6 w-32 bg-gray-200 rounded animate-pulse"></div>
              <div className="flex sm:gap-2 gap-1">
                <div className="h-8 w-24 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-8 w-24 bg-gray-200 rounded animate-pulse"></div>
              </div>
            </div>

            {/* Photos Grid Skeleton */}
            <div className="grid md:grid-cols-4 sm:grid-cols-3 grid-cols-2 sm:gap-5 gap-3">
              {[...Array(8)].map((_, index) => (
                <div
                  key={index}
                  className="relative max-w-[252px] min-h-[168px] max-h-[168px] w-full"
                >
                  <div className="w-full h-full bg-gray-200 rounded-lg animate-pulse"></div>
                </div>
              ))}
            </div>
          </div>
        </>
      ) : (
        <div>
          <div className="flex justify-between items-center mb-2">
            <h1 className="sm:text-xl text-base font-medium">Hostel Photos</h1>
            <div className="flex sm:gap-2 gap-1">
              {!openEdithostel ? (
                <>
                  <button
                    className="sm:flex block items-center text-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-primary-blue font-medium"
                    onClick={() => setopenEdithostel(true)}
                  >
                    Delete Photo & Video
                  </button>
                  <button
                    className="flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-primary-blue font-medium"
                    onClick={() => setOpenAddhostel(true)}
                  >
                    Add Photo & Video
                  </button>
                </>
              ) : (
                <button
                  className="sm:flex block items-center text-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-red-500 font-medium"
                  onClick={() => setopenEdithostel(false)}
                >
                  Cancel
                </button>
              )}
            </div>
          </div>

          {/* <div className='flex flex-wrap items-center justify-between mt-5 gap-y-5'>
          {photos?.map((photo) => (
            <div
              key={photo._id}
              className='relative w-full h-full border border-gray-300 rounded-md max-w-[252px] min-h-[168px] max-h-[168px]'
            >
              <Image
                src={`https://${photo.url}`}
                alt={photo.title}
                title={photo.title}
                layout='fill'
                className='object-cover w-full h-full rounded-md'
                loading='lazy'
              />

              {edit && (
                <button
                  onClick={() => handleDelete(photo._id)}
                  className='absolute top-2 right-2 bg-red-600 text-white p-1 rounded-full h-8 w-8 flex items-center justify-center'
                >
                  <FaRegTrashCan />
                </button>
              )}
            </div>
          ))}
        </div> */}

          {/* hotes photos */}
          {/* <div className="grid md:grid-cols-4 sm:grid-cols-3 grid-cols-2 sm:gap-5 gap-3">
            {data?.images?.map((photo) => (
              <div
                key={photo._id}
                className="relative max-w-[252px] min-h-[168px] max-h-[168px] w-full"
              >
                <Image
                  className="object-cover w-full h-full rounded-lg border border-red-600"
                  // src={`${
                  //   photo?.objectUrl?.startsWith("https") ? "" : "https://"
                  // }${photo?.url}`}
                  src={photo?.objectUrl}
                  // src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/${photo?.url}`}
                  alt="hostel photo"
                  title={photo?.title}
                  width={252}
                  height={168}
                />
                {openEdithostel && (
                  <button
                    onClick={() => handleDelete(photo._id)}
                    className="absolute top-2 right-0 translate-x-[-10%] bg-red-600 text-white p-1 rounded-full h-8 w-8 flex items-center justify-center"
                  >
                    <FaRegTrashCan />
                  </button>
                )}
              </div>
            ))}
          </div> */}
          <div className="grid md:grid-cols-4 sm:grid-cols-3 grid-cols-2 sm:gap-5 gap-3">
            {data?.images?.map((photo, index) => (
              <PhotoItem
                key={photo._id}
                photo={photo}
                index={index}
                openEdithostel={openEdithostel}
                handleDelete={handleDelete}
                setPreviewIndex={setPreviewIndex}
                setActiveIndex={setActiveIndex}
                setImagePreviewModal={setImagePreviewModal}
              />
            ))}
          </div>
          <Transition show={openAddhostel} as={Fragment}>
            <Dialog
              as="div"
              className="relative z-50"
              onClose={closehostelModal}
            >
              {/* Overlay */}
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0"
                enterTo="opacity-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <div className="fixed inset-0 bg-black/50" />
              </Transition.Child>

              {/* Slide-In Modal */}
              <div className="fixed inset-0 overflow-hidden">
                <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
                  <Transition.Child
                    as={Fragment}
                    enter="transform transition ease-out duration-300"
                    enterFrom="translate-x-full"
                    enterTo="translate-x-0"
                    leave="transform transition ease-in duration-200"
                    leaveFrom="translate-x-0"
                    leaveTo="translate-x-full"
                  >
                    <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                      {/* Modal Header */}
                      <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                        <h2 className="page-title">Add Hostel Photo</h2>
                        <button
                          onClick={closehostelModal}
                          className="text-gray-500 hover:text-gray-800"
                        >
                          &#10005; {/* Close icon */}
                        </button>
                      </div>

                      {/* Modal Content */}
                      <div className="sm:px-6 px-4">
                        <div>
                          {areas.map((area, index) => (
                            <div
                              key={index}
                              className="py-4 first:border-t-0 border-t"
                            >
                              {/* Area Name */}
                              <div>
                                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                                  Area Name
                                </label>
                                <CustomSelect
                                  name={`areaName-${index}`}
                                  options={[
                                    { value: "", label: "Select Area" },
                                    { value: "hostel", label: "Hostel" },
                                    { value: "interior", label: "Interior" },
                                  ]}
                                  onChange={(selectedOption) => {
                                    const updatedAreas = [...areas];
                                    updatedAreas[index].areaName =
                                      selectedOption;
                                    setAreas(updatedAreas);
                                  }}
                                  value={area.areaName}
                                  placeholder="Day/Week"
                                />
                              </div>

                              {/* Add Photo */}
                              <div className="relative mt-3">
                                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                                  Add Photo & Video
                                </label>

                                <input
                                  type="file"
                                  name={`images-${index}`}
                                  onChange={(e) => handleFileChange(e, index)}
                                  multiple
                                  accept="image/*,video/*"
                                  className="z-10 cursor-pointer block w-full p-2 px-4 text-sm bg-transparent border rounded-lg opacity-0 absolute top-0 left-0 right-0 bottom-0"
                                />

                                <div className="w-full px-4 py-2 border border-[#40E0D0] rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500 flex items-center justify-between border-dashed bg-[#40E0D01A] cursor-pointer">
                                  {area.files.length > 0
                                    ? `${area.files.length} files selected`
                                    : "Upload Photos & Videos"}
                                  <CloudUpload
                                    className="text-[#40E0D0]"
                                    size={22}
                                  />
                                </div>
                              </div>

                              {/* Preview & Remove Button */}
                              {area.files.length > 0 && (
                                <div className="grid grid-cols-3 gap-4 mt-3">
                                  {area.files.map((file, fileIndex) => (
                                    <div
                                      key={fileIndex}
                                      className="flex items-center justify-between px-4 py-2 border border-[#40E0D0] border-dashed bg-[#40E0D01A] rounded-lg"
                                    >
                                      {file.type && file.type.startsWith("video/") ? (
                                        <video
                                          src={URL.createObjectURL(file)}
                                          className="w-[54px] h-[36px] object-cover rounded-sm"
                                          muted
                                          playsInline
                                        />
                                      ) : (
                                        <Image
                                          src={URL.createObjectURL(file)}
                                          alt={`Preview ${fileIndex + 1}`}
                                          className="w-[54px] h-[36px] object-cover rounded-sm"
                                          width={54}
                                          height={36}
                                        />
                                      )}
                                      <span
                                        className="text-black hover:text-red-500 font-black cursor-pointer text-xl"
                                        onClick={() =>
                                          removeFileFromArea(index, fileIndex)
                                        }
                                      >
                                        &#10005;
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          ))}

                          {/* Add More Button */}
                          {/* <div className="flex items-center justify-end w-full mt-4 gap-4 col-span-2 bg-white/80 bottom-0 backdrop-blur-sm">
                            <button
                              type="button"
                              onClick={handleAddMore}
                              className="bg-[#40E0D0] hover:bg-transparent text-black hover:text-[#40E0D0] border-2 font-medium py-2 px-6 border-[#40E0D0] rounded-lg text-sm sm:w-[152px] w-full"
                            >
                              Add More
                            </button>
                          </div> */}
                        </div>
                        <div className="xs:flex block  items-center justify-between w-full sm:my-14 my-7 gap-4 col-span-2 py-4 bg-white/80 sticky bottom-0 backdrop-blur-sm">
                          <button
                            type="button"
                            className="hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 px-4 border-black rounded-lg w-full text-sm xs:mb-0 mb-2"
                            onClick={closehostelModal}
                          >
                            Cancel
                          </button>
                          <button
                            type="submit"
                            className="bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm"
                            onClick={handleUpload}
                          >
                            Add
                          </button>
                        </div>
                      </div>
                    </Dialog.Panel>
                  </Transition.Child>
                </div>
              </div>
            </Dialog>
          </Transition>
        </div>
      )}
      {imagePreviewModal && (
        <div className="fixed inset-0 bg-black bg-opacity-90 z-[999] flex flex-col items-center justify-center px-4">
          {/* Close Button */}
          <button
            onClick={() => setImagePreviewModal(false)}
            className="absolute top-4 right-4 text-white text-3xl font-bold z-50"
          >
            &times;
          </button>

          {/* Main Swiper */}
          <div className="relative w-full max-w-4xl h-[70vh] mb-4">
            <Swiper
              key={previewIndex}
              slidesPerView={1}
              centeredSlides={true}
              initialSlide={previewIndex}
              onSlideChange={(swiper) => {
                setActiveIndex(swiper.realIndex);
                if (mainThumbRef.current?.swiper) {
                  mainThumbRef.current.swiper.slideTo(swiper.realIndex);
                }
              }}
              className="h-full"
              ref={mainSwiperRef}
              spaceBetween={10}
            >
              {data?.images?.map((img, idx) => (
                <SwiperSlide key={idx}>
                  <div className="w-full h-full relative">
                    {/\.(mp4|webm|ogg|mov|m4v)$/i.test(img.objectUrl) ? (
                      <video
                        src={img.objectUrl}
                        className="rounded-xl object-contain w-full h-full"
                        controls
                        playsInline
                      />
                    ) : (
                      <Image
                        src={img.objectUrl}
                        alt={`Preview Image ${idx + 1}`}
                        layout="fill"
                        className="rounded-xl object-contain"
                      />
                    )}
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>

            {/* Navigation Arrows */}
            <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 z-10 flex space-x-16">
              <div
                className="flex items-center justify-center w-8 h-8 shadow-md cursor-pointer"
                onClick={() => mainSwiperRef.current.swiper.slidePrev()}
              >
                <CircleChevronLeft size={24} color="#fff" />
              </div>
              <div
                className="flex items-center justify-center w-8 h-8 shadow-md cursor-pointer"
                onClick={() => mainSwiperRef.current.swiper.slideNext()}
              >
                <CircleChevronRight size={24} color="#fff" />
              </div>
            </div>
          </div>

          {/* Thumbnail Swiper */}
          <div className="w-full max-w-5xl mt-4 relative">
            <Swiper
              slidesPerView={5}
              spaceBetween={10}
              centeredSlides={true}
              className="h-full"
              ref={mainThumbRef}
              initialSlide={previewIndex}
            >
              {data?.images?.map((img, idx) => (
                <SwiperSlide key={idx}>
                  <div
                    className={`relative w-full h-32 rounded-md cursor-pointer border ${
                      idx === activeIndex ? "border-white" : "border-transparent"
                    }`}
                    onClick={() => mainSwiperRef.current.swiper.slideTo(idx)}
                  >
                    {/\.(mp4|webm|ogg|mov|m4v)$/i.test(img.objectUrl) ? (
                      <video
                        src={img.objectUrl}
                        className="rounded-md object-cover w-full h-full"
                        muted
                        playsInline
                        preload="metadata"
                      />
                    ) : (
                      <Image
                        src={img.objectUrl}
                        alt={`Thumb ${idx + 1}`}
                        layout="fill"
                        className="rounded-md object-cover"
                      />
                    )}
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        </div>
      )}
    </>
  );
};

export default HostelPhotos;
