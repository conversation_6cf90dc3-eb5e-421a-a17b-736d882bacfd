import Response from '../utills/response.js';
import { updateSettingByKey, getSettingByKey } from '../services/setting.js';

// Controller to update settings by key
const updateSettingByKeyController = async (req, res) => {
    try {
        const { key } = req.params;
        const { value } = req.body;

        const updatedSetting = await updateSettingByKey(key, value);

        return Response.OK(res, 'Setting updated successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

// Controller to get settings by key
const getSettingByKeyController = async (req, res) => {
    try {
        const { key } = req.params;

        const setting = await getSettingByKey(key);

        return Response.OK(res, setting, 'Setting retrieved successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

export { updateSettingByKeyController, getSettingByKeyController };
