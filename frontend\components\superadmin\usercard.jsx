 
import React from "react";
import Image from "next/image";

function UserCard({ name, status, image, message, unseenmessage }) {
  return (
    <div className="flex p-2 my-2 border-b cursor-pointer hover:bg-[#EEF9FF] dark:hover:bg-[#393939b7]">
      <Image src={image} alt={name} className="w-10 h-10 mr-4 rounded-full" />
      <div className="flex flex-col">
        <h4 className="text-sm font-poppins font-semibold uppercase dark:text-[#B6B6B6]">{name}</h4>
        <p className="text-xxs font-poppins text-gray-500 mt-1 dark:text-[#757575]">{message}</p>
      </div>
      <div className="flex flex-col items-center">
        <p className="text-xs text-gray-500 whitespace-nowrap text-xxs font-poppins dark:text-[#757575]">{status}</p>
        <div className="text-xs text-[#6226EF] whitespace-nowrap mt-3 bg-[#E0D4FC] h-6 w-6  flex items-center justify-center rounded-full dark:bg-[#6e4eb755]">
          {unseenmessage}
        </div>
      </div>
    </div>
  );
}

export default UserCard;
