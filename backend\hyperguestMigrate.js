const axios = require('axios');
const mongoose = require('mongoose');

// MongoDB Connection Setup
const mongoURI = 'mongodb://localhost:27017/mixdorm'; // Replace with your MongoDB URI
mongoose.connect(mongoURI, { useNewUrlParser: true, useUnifiedTopology: true })
  .then(() => console.log('Connected to MongoDB'))
  .catch(err => console.error('MongoDB connection error:', err));

// MongoDB Schema and Model
const HyperguestOtaSchema = new mongoose.Schema({}, { strict: false }); // Allow dynamic storage of any property

const HyperguestOta = mongoose.model('HyperguestOta', HyperguestOtaSchema);

// Function to fetch hotels and store data
const fetchAndStoreHotels = async () => {
  const token = '8f3005ff52454a2fa3ece5e0c5fd0d48'; // Replace with your actual Bearer token
  const hotelsApiUrl = 'https://hg-static.hyperguest.com/hotels.json';

  try {
    // Fetch hotel list
    const hotelsResponse = await axios.get(hotelsApiUrl, {
      headers: { Authorization: `Bearer ${token}` }
    });

    const hotels = hotelsResponse.data;

    for (const hotel of hotels) {
      const propertyApiUrl = `https://hg-static.hyperguest.com/${hotel.hotel_id}/property-static.json`;
      try {
        // Fetch property details for each hotel
        const propertyResponse = await axios.get(propertyApiUrl, {
          headers: { Authorization: `Bearer ${token}` }
        });

        const propertyData = propertyResponse.data;

        // Save to MongoDB
        const hotelData = new HyperguestOta(propertyData); // Store the entire property details directly

        await hotelData.save();
        console.log(`Saved hotel ID: ${hotel.hotel_id}`);
      } catch (propertyError) {
        console.error(`Error fetching property data for hotel ID ${hotel.hotel_id}:`, propertyError.message);
      }
    }
  } catch (hotelsError) {
    console.error('Error fetching hotels list:', hotelsError.message);
  } finally {
    mongoose.connection.close();
    console.log('MongoDB connection closed.');
  }
};

// Execute the function
fetchAndStoreHotels();
