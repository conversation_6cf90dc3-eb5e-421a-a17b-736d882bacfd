import hostelModel from '../models/hostel.js';
import propertyModel from '../models/properties.js';
import reviewModel from '../models/reviews.js';
import { getUserData } from './auth.js';
import { generatePropertyNumber } from '../utills/helper.js';
import mongoose from 'mongoose';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import bookingModel from '../models/bookings.js';
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create a new hostel
const createHostel = async (data) => {
    return await hostelModel.create(data);
};

// Update hostel data by ID
const updateHostel = async (id, newData) => {
    try {
        const hostel = await hostelModel.findOne({ _id: id });
        // Check if the hostel exists
        if (!hostel) {
            throw new Error('HOSTEL_NOT_FOUND');
        }

        const updatedHostel = await hostelModel.findByIdAndUpdate({ _id: id }, { $set: newData });
        return updatedHostel;
    } catch (error) {
        // Handle errors
        console.error("Error updating hostel data:", error.message);
        throw error;
    }
};

const getPropertyDetailsById = async (id) => {
    try {
        const propertyDetails = await propertyModel.aggregate([
            {
                $match: {
                    _id: new mongoose.Types.ObjectId(id)
                }
            },
            // {
            //     $lookup: {
            //         from: 'rooms',
            //         localField: '_id',
            //         foreignField: 'property',
            //         as: 'rooms'
            //     }
            // },
            {
                $lookup: {
                    from: 'users',
                    let: { ownerId: '$propertyOwner' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ['$_id', '$$ownerId']
                                }
                            }
                        },
                        {
                            $project: {
                                name: 1,
                                email: 1
                            }
                        }
                    ],
                    as: 'owner'
                }
            },
            {
                $addFields: {
                    owner: { $arrayElemAt: ['$owner', 0] } // Unwrap the array if only one owner is expected
                }
            }
        ]);
        return propertyDetails.length ? propertyDetails[0] : null;
    } catch (error) {
        console.error('Error fetching property details:', error);
        throw error;
    }
};



// List all hostels with pagination and filter
const listHostels = async (filter, page, limit) => {
    const skip = (page - 1) * limit;
    const hostels = await hostelModel.find({ ...filter, isDeleted: false, isArchived: false }).skip(skip).limit(limit).sort({ createdAt: -1 }).lean();
    const totalHostels = await hostelModel.countDocuments(filter);
    return {
        data: hostels,
        totalHostels,
        totalPages: Math.ceil(totalHostels / limit)
    };
};

const softDeleteHostel = async (id) => {
    try {
        const updatedHostel = await hostelModel.findByIdAndUpdate(id, { isDeleted: true, isActive: false });

        return updatedHostel;
    } catch (error) {
        throw new Error(error.message);
    }
};

// Create a new property
// const createProperty = async (data) => {
//     return await propertyModel.create(data);
// };

// Function to add a new property
const createProperty = async (data) => {
    try {
        // Ensure country is specified
        const country = data.address.country;
        if (!country) {
            throw new Error('Country is required');
        }

        // Generate the property number without needing the country argument
        const propertyNumber = await generatePropertyNumber();
        data.number = propertyNumber; // Assign the generated property number

        // Initialize contactUs if it doesn't exist
        if (!data.contactUs) {
            data.contactUs = {}; // Ensure contactUs is an object
        }

        // Get the user data of the property owner (assuming you have a function for this)
        const user = getUserData(data.propertyOwner);

        // Set contact details
        data.contactUs.email = data.email;
        data.contactUs.phoneNumber = data.contact;
        data.contactUs.whatsApp = data.contact;

        // Add location if latitude and longitude are provided
        if (data.latitude && data.longitude) {
            data.location = {
                type: "Point",
                coordinates: [data.longitude, data.latitude] // [longitude, latitude] for GeoJSON
            };

            // Also add coordinates to contactUs.contactAddress if needed
            if (!data.contactUs.contactAddress) {
                data.contactUs.contactAddress = {};
            }
            data.contactUs.contactAddress.latitude = data.latitude;
            data.contactUs.contactAddress.longitude = data.longitude;
        } else {
            throw new Error('Latitude and Longitude are required');
        }

        // Create the new property entry
        const newProperty = await propertyModel.create(data);
        return newProperty;
    } catch (error) {
        console.error('Error creating property:', error.message);
        throw error;
    }
};

// Update property data by ID
// const updateProperty = async (id, newData) => {
//     try {
//         const property = await propertyModel.findOne({ _id: id });
//         if (!property) {
//             throw new Error('Property not found');
//         }

//         const updatedProperty = await propertyModel.findByIdAndUpdate(id, { $set: newData });
//         return updatedProperty;
//     } catch (error) {
//         console.error("Error updating property data:", error.message);
//         throw error;
//     }
// };



const updateProperty = async (id, newData) => {
    try {
        const property = await propertyModel.findById(id);
        if (!property) throw new Error("Property not found");

        const updateOps = {};
        // if(newData?.generalPolicy){
        //     updateOps.generalPolicies = newData?.generalPolicy
        // }
        /** Handle images updates */
        if (Array.isArray(newData.images) && newData.images.length > 0) {
            const imageIdsToDelete = [];
            const imagesToAdd = [];

            for (const image of newData.images) {
                if (image.action === "delete" && Array.isArray(image.id) && image.id.length > 0) {
                    imageIdsToDelete.push(...image.id);
                } else if (image.action === "delete" && image._id) {
                    imageIdsToDelete.push(image._id);
                } else if (image.action === "add") {
                    imagesToAdd.push({
                        _id: image._id || new mongoose.Types.ObjectId(),
                        s3Uri: image.s3Uri || null,
                        objectUrl: image.objectUrl || null,
                        s3ObjectUrl: image.s3ObjectUrl || null,
                    });
                }
            }

            if (imageIdsToDelete.length > 0) {
                updateOps.$pull = {
                    ...(updateOps.$pull || {}),
                    images: { _id: { $in: imageIdsToDelete } },
                };
            }

            if (imagesToAdd.length > 0) {
                updateOps.$push = {
                    ...(updateOps.$push || {}),
                    images: { $each: imagesToAdd },
                };
            }

            // ❗ Prevent overwriting whole array
            delete newData.images;
        }

        /** Handle other fields */
        if (Object.keys(newData).length > 0) {
            updateOps.$set = { ...(updateOps.$set || {}), ...newData };
        }

        /** Update in one go */
        console.log("updateOps",updateOps)
        const updatedProperty = await propertyModel.findByIdAndUpdate(id, updateOps, { new: true });
        return updatedProperty;

    } catch (error) {
        console.error("Error updating property data:", error.message);
        throw error;
    }
};

// List all properties by owner
const listPropertiesByOwner = async (ownerId, filter, page, limit) => {
    console.log("ownerId", ownerId)
    const skip = (page - 1) * limit;
    const query = { propertyOwner: ownerId, isDeleted: false, isActive: true, ...filter };
    const properties = await propertyModel.find(query)
        .skip(skip)
        .limit(limit)
        .exec();

    const totalProperties = await propertyModel.countDocuments(query);

    return { properties, totalProperties };
};

// List all properties
const listProperties = async (filter, page, limit) => {
    const skip = (page - 1) * limit;
    const query = { isPropertyVerified: true, isDeleted: false, isActive: true, ...filter };
    const properties = await propertyModel.find(query)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec();

    const totalProperties = await propertyModel.countDocuments(query);

    return { properties, totalProperties };
};

const checkProperty = async (id, owner) => {
    try {
        // Check if the property exists by name (case-insensitive search)
        return await propertyModel.findOne({ _id: new mongoose.Types.ObjectId(id) });

        if (propertyCheck.propertyOwner) {
            return
        }


        return propertyUpdate;
    } catch (error) {
        console.error("Error finding or updating property data:", error.message);
        throw error;
    }
};

// Get a property by ID
const getProperty = async (id) => {
    return await propertyModel.findOne({ _id: id, isDeleted: false, isActive: true });
};

const softDeleteProperty = async (id) => {
    try {
        const updatedProperty = await propertyModel.findByIdAndUpdate(id, { isDeleted: true, isActive: false });

        return updateProperty;
    } catch (error) {
        throw new Error(error.message);
    }
};

const uploadPropertyFiles = async (propertyId, files) => {
    const property = await propertyModel.findById(propertyId);
    if (!property) {
        throw new Error('Property not found');
    }

    // Define the allowed file types
    const allowedFileTypes = ['image/jpeg', 'image/png', 'application/pdf'];

    // Prepare the upload directory
    const projectRoot = path.resolve(__dirname, '../');
    const uploadDir = path.join(projectRoot, 'public', propertyId.toString());
    if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
    }

    // Validate and save a single file
    const saveFile = (field, file) => {
        // Validate file type
        if (!allowedFileTypes.includes(file.mimetype)) {
            throw new Error(`Invalid file type for field "${field}". Allowed types: ${allowedFileTypes.join(', ')}`);
        }

        // Generate file path and save the file
        const fileName = `${field}_${Date.now()}${path.extname(file.originalname)}`;
        const filePath = path.join(uploadDir, fileName);
        fs.writeFileSync(filePath, file.buffer);

        // Construct file metadata
        const url = `/public/${propertyId}/${fileName}`;
        const fileData = { title: file.originalname, url };

        // Append to the appropriate field in the property object
        switch (field) {
            case 'photos':
                property.photos.push(fileData);
                break;
            case 'licenceProof':
                property.licenceProof.push(fileData);
                break;
            case 'addressProof':
                property.addressProof.push(fileData);
                break;
            default:
                throw new Error('Invalid field');
        }
    };

    // Validate and process incoming files
    if (files) {
        for (const field in files) {
            if (!['photos', 'licenceProof', 'addressProof'].includes(field)) {
                throw new Error(`Invalid field "${field}" in file upload.`);
            }
            files[field].forEach(file => saveFile(field, file));
        }
    }

    // Save the updated property
    await property.save();

    return property;
};



// for admin
const listFilteredProperties = async (filters, page, limit) => {
    const { rate, status, country, sort } = filters; // Added sort in filters
    const skip = (page - 1) * limit;

    // Base query
    const query = { isDeleted: false, isActive: true };

    // Status filter (Verified/Unverified)
    if (status === "verified") {
        query.isPropertyVerified = true;
    }
    if (status === "unverified") {
        query.isPropertyVerified = false;
    }

    // Country filter
    if (country) {
        query['address.country'] = country;
    }

    // Get properties matching base query
    let properties = await propertyModel.find(query)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec();

    // Apply rate filter if specified
    if (rate) {
        const propertyIds = properties.map(prop => prop._id);
        const ratedPropertyIds = await reviewModel.aggregate([
            { $match: { property: { $in: propertyIds }, isDeleted: false, isActive: true } },
            {
                $group: {
                    _id: '$property',
                    averageRating: { $avg: '$rating' },
                }
            },
            { $match: { averageRating: { $gte: Number(rate) } } },
            { $project: { _id: 1 } }
        ]).exec();

        const ratedPropertyIdSet = new Set(ratedPropertyIds.map(item => item._id.toString()));
        properties = properties.filter(prop => ratedPropertyIdSet.has(prop._id.toString()));
    }

    // Sorting logic based on the provided sort option
    if (sort === 'new hostel') {
        properties.sort((a, b) => b.createdAt - a.createdAt); // Sort by creation date, newest first
    } else if (sort === 'avgRating') {
        // Fetch average ratings
        const ratings = await reviewModel.aggregate([
            { $match: { property: { $in: properties.map(prop => prop._id) }, isDeleted: false, isActive: true } },
            {
                $group: {
                    _id: '$property',
                    averageRating: { $avg: { $round: ["$rating", 1] } }, // Round to 1 decimal place
                }
            },
            { $sort: { averageRating: -1 } } // Sort by average rating, highest first
        ]).exec();

        // Create a map for easy access
        const ratingMap = new Map(ratings.map(item => [item._id.toString(), item.averageRating]));

        // Sort properties by their average rating
        properties.sort((a, b) => (ratingMap.get(b._id.toString()) || 0) - (ratingMap.get(a._id.toString()) || 0));
    }

    const totalProperties = properties.length;

    return { properties, totalProperties };
};
export const getVisitorsCounts = async (filter) => {
    const visitors = await bookingModel.aggregate([
        { $match: filter },
        {
            $group: {
                _id: "$user"
            }
        },
        {
            $lookup: {
                from: "users",           // Name of the user collection
                localField: "_id",
                foreignField: "_id",
                as: "userDetails"
            }
        },
        {
            $unwind: "$userDetails"
        },
        {
            $project: {
                password: 0,
            }
        }
    ]);

    return visitors;
};
export {
    createHostel, updateHostel, listHostels, softDeleteHostel, createProperty, updateProperty,
    listPropertiesByOwner, listProperties, checkProperty, getProperty, softDeleteProperty,
    getPropertyDetailsById, uploadPropertyFiles, listFilteredProperties
};