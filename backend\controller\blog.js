import Response from "../utills/response.js";
import { addBlog, updateBlogById, getBlogById, listAllBlogs, deleteBlogById } from "../services/blog.js";
import jwt from 'jsonwebtoken';
import wishlists from "../models/wishLists.js";
import blogsCategoriesModel from "../models/blogCategories.js";
const addBlogController = async (req, res) => {
    try {
        const blogData = { ...req.body, createdBy: req.user._id };
        const newBlog = await addBlog(blogData);
        return Response.Created(res, newBlog, 'Blog Added Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const updateBlogController = async (req, res) => {
    try {
        const { id } = req.params;
        const updatedBlog = await updateBlogById(id, req.body);
        return Response.OK(res, updatedBlog, 'Blog Updated Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const getBlogByIdController = async (req, res) => {
    try {
        const { slug } = req.params;
        const blog = await getBlogById(slug);
        return Response.OK(res, blog, 'Blog Retrieved Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const listAllBlogsController = async (req, res) => {
    try {
        let { page, limit, ...filter } = req.query;

        page = parseInt(page);
        limit = parseInt(limit);

        if (isNaN(page) || page <= 0) page = 1;
        if (isNaN(limit) || limit <= 0) limit = 10;

        const blogsData = await listAllBlogs(filter, page, limit);
        let userId;

        if (req?.headers?.authorization) {
            const token = req.headers.authorization?.split(' ')[1];
            const decodedToken = jwt.verify(token, process.env.JWT_SECRET);
            userId = decodedToken.userId;
        }

        console.log("blogsData", blogsData);
        console.log("userId", userId);

        if (blogsData && blogsData.blogs.length) {
            if (userId) {
                for (const blog of blogsData.blogs) {
                    // Convert each blog to a plain object if it is not already
                    const blogObj = blog.toObject ? blog.toObject() : blog;
                    const liked = await wishlists.findOne({
                        user: userId,
                        blog: blog._id
                    });
                    blogObj.liked = !!liked; // Set liked status
                }
            }
        }

        const totalPages = Math.ceil(blogsData.totalBlogs / limit);
        const pagination = {
            page,
            limit,
            totalPages,
            totalBlogs: blogsData.totalBlogs
        };

        return Response.OK(res, { blogs: blogsData.blogs, pagination }, 'Blogs Retrieved Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};


const deleteBlogController = async (req, res) => {
    try {
        const { id } = req.params;
        const result = await deleteBlogById(id);
        return Response.OK(res, result, 'Blog Deleted Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};
export const addBlogCategory = async (req, res) => {
    try {
        const newBlog = await blogsCategoriesModel.create({title:req?.body?.title})
        return Response.Created(res, newBlog, 'Blog Added Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

export const listBlogCategory = async (req, res) => {
    try {
        const newBlog = await blogsCategoriesModel.find({}).sort({_id:-1})
        return Response.Created(res, newBlog, 'Blog Added Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

export { addBlogController, updateBlogController, getBlogByIdController, listAllBlogsController, deleteBlogController };