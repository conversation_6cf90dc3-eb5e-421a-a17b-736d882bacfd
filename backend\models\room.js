import mongoose from 'mongoose';
import slugify from 'slugify';

const roomSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    slug: {
      type: String,
      unique: true,
    },
    type: {
      type: String,
      required: true,
    },
    property: {
      type: mongoose.Types.ObjectId,
      ref: 'properties',
      required: true,
    },
    roomNumber: {
      type: String,
      required: false
    },
    beds: {
      type: Number,
      required: false,
    },
    capacity: {
      type: Number,
      required: false
    },
    ensuite: {
      type: Boolean,
      required: false,
    },
    rate: {
      weekdayRate: {
        value: Number,
        updatedAt: Date
      },
      weekendRate: {
        value: Number,
        updatedAt: Date
      },
      averagePrice: {
        value: Number,
        updatedAt: Date
      }
    },
    currency: {
      type: String,
      required: true,
      default:"INR"
    },
    images: [
      {
        s3Uri: { type: String },
        objectUrl: { type: String },
        s3ObjectUrl: { type: String }
      }
    ],
    description: {
      type: String,
      required: false,
    },
    grade: {
      type: String
    },
    discount: {
      discountPercentage: Number,
      startDate: Date,
      endDate: Date,
      standardRate: Number,
      nonRefundableRate: Number
    },
    bedAndBreakfast: {
      type: Boolean,
      default: false,
    },
    nonRefundable: {
      type: Boolean,
      default: false,
    },
    freeCancellation: {
      type: Boolean,
      default: false,
    },
    dormitory: {
      type: Boolean,
      default: false,
    },
    privateRoom: {
      type: Boolean,
      default: false
    },
    tags: [String],
    isActive: {
      type: Boolean,
      default: true,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    addedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'users',
      required: false,
    },
    channelManager: {
      channelId: {
        type: String,
        required: false,
      },
      isConnected: {
        type: Boolean,
        default: false,
      },
    },
    cloudbeds: {
      roomId: {
        type: Number,
        required: false,
      },
      isConnected: {
        type: Boolean,
        default: false,
      },
    },
    otaRoomId: {
      type: Number,
      unique: true
    },
    roomId: {
      type: Number,
      unique: true
    },
    units: {
      type: Number
    },
    availableUnits: {
      type: Number
    },
    ezeeId:{
      type: String,
    }
  },
  {
    timestamps: true,
  }
);

roomSchema.statics.generateSlug = async function (name, currentRoomId = null) {
  let newSlug = slugify(name, { lower: true, strict: true });
  let existingRoom = await this.findOne({ slug: newSlug });

  // Check for slug conflicts
  while (existingRoom && (!currentRoomId || existingRoom._id.toString() !== currentRoomId.toString())) {
    const randomSuffix = Math.floor(100000 + Math.random() * 900000).toString();
    newSlug = `${newSlug}-${randomSuffix}`;
    existingRoom = await this.findOne({ slug: newSlug });
  }

  return newSlug;
};

roomSchema.pre('save', async function (next) {
  if (this.isModified('name')) {
    this.slug = await this.constructor.generateSlug(this.name, this._id);
  }
  next();
});

const roomModel = mongoose.model('rooms', roomSchema);

export default roomModel;