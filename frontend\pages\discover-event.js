import Link from "next/link";
import Image from "next/image";
import { GiCheckMark } from "react-icons/gi";
import Banner from "@/components/home/<USER>";
import Head from "next/head";
import { useEffect, useState } from "react";

const Devent = () => {
  const [loading, setloading] = useState(false);
  const [imagesLoaded, setImagesLoaded] = useState({
    event1: false,
    event2: false,
  });
  const [imagesError, setImagesError] = useState({
    event1: false,
    event2: false,
  });

  useEffect(() => {
    const timer = setTimeout(() => {
      setloading(true);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);
  return (
    <>
      <Head>
        <title>Explore Hostel Events & City Life | Mixdorm</title>
        <meta
          name='description'
          content='From Rooftop Parties to Cultural Festivals, Mixdorm Connects Travelers with Top Hostel and City Events. Socialize, Explore, and Live Your Best Travel Life.'
        />
      </Head>
      <div
        style={{
          backgroundImage: `url(${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/top-black-bg.jpeg)`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
        className='pb-10 md:pb-14'
      >
        <Banner />{" "}
      </div>
      {/* <section className='lg:pt-12 pt-8 lg:pb-16 border-t border-gray-200'>
        <div className='container px-4 lg:px-14'>
          <div className='grid lg:grid-cols-2 items-center lg:gap-10 gap-4 md:mb-12 mb-8'>
            <div className='flex items-center justify-center'>
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/discoverEvent1.jpg`}
                width={591}
                height={681}
                alt='BgImg'
                className='rounded-4xl'
                loading='lazy'
              />
            </div>
            <div>
              <h3 className='font-bold lg:text-3xl text-xl text-black lg:mb-5 mb-3'>
                Join Hostel Events
              </h3>
              <p className='text-[#737373] text-sm'>
                Mixdorm partners with hostels worldwide that offer a variety of
                activities, perfect for solo travelers, backpackers, and groups
                alike. Whether it’s a rooftop party in Bangkok, a yoga class in
                Bali, or a live music night in Barcelona, you’ll find plenty of
                ways to meet fellow travelers and make lasting memories.
              </p>
              <ul className='lg:my-10 my-6'>
                <li className='flex lg:mb-6 mb-4'>
                  <GiCheckMark className='mr-2 text-[#34D674] w-4 pt-1' />
                  <p className='text-black text-base flex-1'>
                    <b>Hostel-hosted events:</b> Discover hostel-organized
                    events like pub crawls, cooking classes, and movie nights.
                  </p>
                </li>
                <li className='flex lg:mb-6 mb-4'>
                  <GiCheckMark className='mr-2 text-[#34D674] w-4 pt-1' />
                  <p className='text-black text-base flex-1'>
                    <b>Socialize and connect:</b> Make new friends and create
                    shared experiences with travelers from all over the world.
                  </p>
                </li>
              </ul>
              <Link
                href='#'
                className='text-sm inline-block font-semibold text-black bg-primary-blue rounded-4xl py-4 px-12 hover:bg-sky-blue-750'
                prefetch={false}
              >
                Join Now
              </Link>
            </div>
            <div className='lg:order-2 flex items-center justify-center'>
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/discoverEvent2.jpg`}
                width={591}
                height={681}
                alt='BgImg'
                className='rounded-4xl'
                loading='lazy'
              />
            </div>
            <div className='lg:order-1'>
              <h3 className='font-bold lg:text-3xl text-xl text-black lg:mb-5 mb-3'>
                Explore City Events
              </h3>
              <p className='text-[#737373] text-sm'>
                Looking for something beyond the hostel? Check out city wide
                events happening near your location! Whether it s a street
                festival in Colombia, a local market in Italy, or an art
                exhibition in Japan, Mixdorm brings together a curated list of
                events that will help you dive deeper into the local culture.
              </p>
              <ul className='lg:my-10 my-6'>
                <li className='flex lg:mb-6 mb-4'>
                  <GiCheckMark className='mr-2 text-[#34D674] w-4 pt-1' />
                  <p className='text-black text-base flex-1'>
                    <b>Cultural experiences:</b> Attend festivals, concerts, and
                    traditional celebrations in cities across the globe.
                  </p>
                </li>
                <li className='flex lg:mb-6 mb-4'>
                  <GiCheckMark className='mr-2 text-[#34D674] w-4 pt-1' />
                  <p className='text-black text-base flex-1'>
                    <b>Local insights:</b> Get recommendations for hidden gems
                    and unique events you won t find in guidebooks.
                  </p>
                </li>
              </ul>
              <Link
                href='#'
                className='text-sm inline-block font-semibold text-black bg-primary-blue rounded-4xl py-4 px-12 hover:bg-sky-blue-750'
                prefetch={false}
              >
                Explore Now
              </Link>
            </div>
          </div>

          <h2 className='font-extrabold text-black font-manrope md:text-3xl sm:text-2xl text-xl mb-4'>
            How to <span className='text-primary-blue'> Join</span>?
          </h2>
          <p className='text-[#737373] text-sm mb-5'>
            Simply browse through upcoming events in your hostel or city, and
            sign up directly through the Mixdorm app! It’s the easiest way to
            plan your itinerary, meet new people, and make the most of your
            travels
          </p>
        </div>
      </section> */}
      <section className='lg:pt-12 pt-8 lg:pb-16 border-t border-gray-200'>
        <div className='container px-4 lg:px-14'>
          {!loading ? (
            <div className='space-y-8'>
              <div className='grid lg:grid-cols-2 items-center lg:gap-10 gap-4 md:mb-12 mb-8'>
                <div className='h-[400px] lg:h-[681px] bg-gray-200 animate-pulse rounded-4xl flex items-center justify-center'>
                  <div className='text-white font-bold text-2xl'>Mixdorm</div>
                </div>

                <div className='space-y-4'>
                  <div className='h-8 w-3/4 bg-gray-200 rounded animate-pulse'></div>
                  <div className='h-4 w-full bg-gray-200 rounded animate-pulse'></div>
                  <div className='h-4 w-5/6 bg-gray-200 rounded animate-pulse'></div>
                  <div className='h-4 w-2/3 bg-gray-200 rounded animate-pulse'></div>

                  <div className='space-y-6'>
                    {[...Array(2)].map((_, i) => (
                      <div key={i} className='flex'>
                        <div className='h-5 w-5 bg-gray-200 rounded-full mr-2'></div>
                        <div className='flex-1 space-y-2'>
                          <div className='h-4 w-3/4 bg-gray-200 rounded'></div>
                          <div className='h-4 w-full bg-gray-200 rounded'></div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className='h-12 w-40 bg-gray-200 rounded-full animate-pulse'></div>
                </div>

                <div className='lg:order-2 h-[400px] lg:h-[681px] bg-gray-200 animate-pulse rounded-4xl flex items-center justify-center'>
                  <div className='text-white font-bold text-2xl'>Mixdorm</div>
                </div>

                <div className='lg:order-1 space-y-4'>
                  <div className='h-8 w-3/4 bg-gray-200 rounded animate-pulse'></div>
                  <div className='h-4 w-full bg-gray-200 rounded animate-pulse'></div>
                  <div className='h-4 w-5/6 bg-gray-200 rounded animate-pulse'></div>
                  <div className='h-4 w-2/3 bg-gray-200 rounded animate-pulse'></div>

                  <div className='space-y-6'>
                    {[...Array(2)].map((_, i) => (
                      <div key={i} className='flex'>
                        <div className='h-5 w-5 bg-gray-200 rounded-full mr-2'></div>
                        <div className='flex-1 space-y-2'>
                          <div className='h-4 w-3/4 bg-gray-200 rounded'></div>
                          <div className='h-4 w-full bg-gray-200 rounded'></div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className='h-12 w-40 bg-gray-200 rounded-full animate-pulse'></div>
                </div>
              </div>

              <div className='space-y-4'>
                <div className='h-8 w-1/2 bg-gray-200 rounded animate-pulse'></div>
                <div className='h-4 w-full bg-gray-200 rounded animate-pulse'></div>
                <div className='h-4 w-3/4 bg-gray-200 rounded animate-pulse'></div>
              </div>
            </div>
          ) : (
            <>
              <div className='grid lg:grid-cols-2 items-center lg:gap-10 gap-4 md:mb-12 mb-8'>
                <div className='flex items-center justify-center'>
                  <div className='relative w-full h-[400px] lg:h-[681px]'>
                    {(!imagesLoaded["event1"] || imagesError["event1"]) && (
                      <div className='absolute inset-0 bg-gray-200 animate-pulse rounded-4xl flex items-center justify-center'>
                        <h1 className='text-white font-bold text-2xl'>
                          Mixdorm
                        </h1>
                      </div>
                    )}

                    {!imagesError["event1"] && (
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/discoverEvent1.jpg`}
                        width={591}
                        height={681}
                        alt='Hostel Events'
                        className={`rounded-4xl object-cover w-full h-full transition-opacity duration-300 ${
                          !imagesLoaded["event1"] ? "opacity-0" : "opacity-100"
                        }`}
                        loading='lazy'
                        onLoadingComplete={() =>
                          setImagesLoaded((prev) => ({ ...prev, event1: true }))
                        }
                        onError={() =>
                          setImagesError((prev) => ({ ...prev, event1: true }))
                        }
                      />
                    )}
                  </div>
                </div>

                <div>
                  <h3 className='font-bold lg:text-3xl text-xl text-black lg:mb-5 mb-3'>
                    Join Hostel Events
                  </h3>
                  <p className='text-[#737373] text-sm'>
                    Mixdorm partners with hostels worldwide that offer a variety
                    of activities, perfect for solo travelers, backpackers, and
                    groups alike. Whether it&apos;s a rooftop party in Bangkok,
                    a yoga class in Bali, or a live music night in Barcelona,
                    you&apos;ll find plenty of ways to meet fellow travelers and
                    make lasting memories.
                  </p>
                  <ul className='lg:my-10 my-6'>
                    <li className='flex lg:mb-6 mb-4'>
                      <GiCheckMark className='mr-2 text-[#34D674] w-4 pt-1' />
                      <p className='text-black text-base flex-1'>
                        <b>Hostel-hosted events:</b> Discover hostel-organized
                        events like pub crawls, cooking classes, and movie
                        nights.
                      </p>
                    </li>
                    <li className='flex lg:mb-6 mb-4'>
                      <GiCheckMark className='mr-2 text-[#34D674] w-4 pt-1' />
                      <p className='text-black text-base flex-1'>
                        <b>Socialize and connect:</b> Make new friends and
                        create shared experiences with travelers from all over
                        the world.
                      </p>
                    </li>
                  </ul>
                  <Link
                    href='#'
                    className='text-sm inline-block font-semibold text-black bg-primary-blue rounded-4xl py-4 px-12 hover:bg-sky-blue-750'
                    prefetch={false}
                  >
                    Join Now
                  </Link>
                </div>

                <div className='lg:order-2 flex items-center justify-center'>
                  <div className='relative w-full h-[400px] lg:h-[681px]'>
                    {(!imagesLoaded["event2"] || imagesError["event2"]) && (
                      <div className='absolute inset-0 bg-gray-200 animate-pulse rounded-4xl flex items-center justify-center'>
                        <h1 className='text-white font-bold text-2xl'>
                          Mixdorm
                        </h1>
                      </div>
                    )}

                    {!imagesError["event2"] && (
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/discoverEvent2.jpg`}
                        width={591}
                        height={681}
                        alt='City Events'
                        className={`rounded-4xl object-cover w-full h-full transition-opacity duration-300 ${
                          !imagesLoaded["event2"] ? "opacity-0" : "opacity-100"
                        }`}
                        loading='lazy'
                        onLoadingComplete={() =>
                          setImagesLoaded((prev) => ({ ...prev, event2: true }))
                        }
                        onError={() =>
                          setImagesError((prev) => ({ ...prev, event2: true }))
                        }
                      />
                    )}
                  </div>
                </div>

                <div className='lg:order-1'>
                  <h3 className='font-bold lg:text-3xl text-xl text-black lg:mb-5 mb-3'>
                    Explore City Events
                  </h3>
                  <p className='text-[#737373] text-sm'>
                    Looking for something beyond the hostel? Check out city wide
                    events happening near your location! Whether it&apos;s a
                    street festival in Colombia, a local market in Italy, or an
                    art exhibition in Japan, Mixdorm brings together a curated
                    list of events that will help you dive deeper into the local
                    culture.
                  </p>
                  <ul className='lg:my-10 my-6'>
                    <li className='flex lg:mb-6 mb-4'>
                      <GiCheckMark className='mr-2 text-[#34D674] w-4 pt-1' />
                      <p className='text-black text-base flex-1'>
                        <b>Cultural experiences:</b> Attend festivals, concerts,
                        and traditional celebrations in cities across the globe.
                      </p>
                    </li>
                    <li className='flex lg:mb-6 mb-4'>
                      <GiCheckMark className='mr-2 text-[#34D674] w-4 pt-1' />
                      <p className='text-black text-base flex-1'>
                        <b>Local insights:</b> Get recommendations for hidden
                        gems and unique events you won&apos;t find in
                        guidebooks.
                      </p>
                    </li>
                  </ul>
                  <Link
                    href='#'
                    className='text-sm inline-block font-semibold text-black bg-primary-blue rounded-4xl py-4 px-12 hover:bg-sky-blue-750'
                    prefetch={false}
                  >
                    Explore Now
                  </Link>
                </div>
              </div>

              <h2 className='font-extrabold text-black font-manrope md:text-3xl sm:text-2xl text-xl mb-4'>
                How to <span className='text-primary-blue'> Join</span>?
              </h2>
              <p className='text-[#737373] text-sm mb-5'>
                Simply browse through upcoming events in your hostel or city,
                and sign up directly through the Mixdorm app! It&apos;s the
                easiest way to plan your itinerary, meet new people, and make
                the most of your travels
              </p>
            </>
          )}
        </div>
      </section>
    </>
  );
};

export default Devent;
