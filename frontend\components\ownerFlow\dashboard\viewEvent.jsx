/* eslint-disable no-constant-binary-expression */
import Loader from "@/components/loader/loader";
import { GetEventApi } from "@/services/ownerflowServices";
import { useEffect, useState } from "react";
import countries from "world-countries";

const ViewEvent = ({ editId }) => {
  const [formData, setFormData] = useState({
    title: "",
    startDate: "",
    duration: 0,
    durationType: "", // Selected duration type
    numberOfUnits: "", // Number of days/weeks
    hours: "", // Selected hours
    minutes: "", // Selected minutes
    price: 0,
    address: "",
    location: "",
    // attachment: null,
    description: "",
    refundable: false,
    nonRefundable: false,
    cancellation: false,
    currency: "",
  });
  const [currencyData, setCurrencyData] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (editId) {
      fetchData(editId);
    }
  }, [editId]);

  const fetchData = async (editId) => {
    setIsLoading(true);
    try {
      const res = await <PERSON><PERSON>vent<PERSON><PERSON>(editId);
      console.log("res", res?.data?.data?.rating);
      if (res?.status == 200) {
        const data = res?.data?.data;
        setFormData({
          title: data.title,
          startDate: data.startDate.split("T")[0],
          duration: data.duration,
          durationType: data.duration.split(" ")[1], // Selected duration type
          numberOfUnits: data.duration.split(" ")[0],
          hours: data.hours,
          minutes: data.minutes,
          price: data.price,
          address: data?.address,
          location: data.location
            ? `${data.location.coordinates[0]}, ${data.location.coordinates[1]}`
            : "",
          // attachment: data.attachment || "",
          currency: data.currency || "",
          description: data.description,
          refundable: data.refundable === "true",
          nonRefundable: data.nonRefundable === "true",
          cancellation: data.cancellation === "true",
          status: data.status || "",
        });
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);
      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);

  const getCurrencySymbol = (currencyCode) => {
    return currencyData[currencyCode] || currencyCode;
  };

  return (
    <>
      <Loader open={isLoading} />
      <section className='w-full'>
        <div className='w-full'>
          <div className='max-w-[780px] mx-auto pb-5'>
            <div className='grid sm:grid-cols-[1.4fr_3fr] grid-cols-2 sm:gap-4 gap-3 font-inter'>
              <div>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Title :
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Date of Arrival :
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Duration :
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Hours :
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Minutes:
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Price :
                </p>
              </div>
              <div>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData?.title}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData?.startDate
                    ? new Date(formData?.startDate)?.toLocaleDateString(
                        "en-GB",
                        {
                          day: "2-digit",
                          month: "2-digit",
                          year: "numeric",
                        }
                      )
                    : "-" || "-"}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData?.duration}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData?.hours}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData?.minutes}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {getCurrencySymbol(formData?.currency)}{" "}
                  {formData?.price ?? " - "}
                </p>
              </div>
              <div>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Description :
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Add On :
                </p>
              </div>
              <div>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData?.description}{" "}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {" "}
                  {formData?.bedAndBreakfast && "Bed and Breakfast"}
                  {formData?.refundable && "Non Refundable"}
                  {(formData?.cancellation && "Free Cancellation") ||
                    " - "}{" "}
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default ViewEvent;
