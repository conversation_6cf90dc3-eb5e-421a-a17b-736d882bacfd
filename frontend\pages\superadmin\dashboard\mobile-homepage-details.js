import Image from "next/image";
import Link from "next/link";
import React from "react";

const MobileDetails = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616] h-screen">
      <div className="flex items-center justify-between w-full ">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Mobile Details
        </h2>

      </div>
      <div className="bg-white border rounded-xl mt-5 h-auto dark:bg-black dark:border-none">
        <div className="p-8 flex flex-col gap-y-6">
          {/* <div>
            <h1 className="text-xl font-bold">Booking Information</h1>
            <div className="flex items-center gap-x-20 my-6">
              <div className="flex w-[15%] flex-col">
                <strong className="text-black/75">NAME</strong>
                <p className="mt-2 text-black/65">Christine Brooks</p>
              </div>
              <div className="flex w-[15%] flex-col">
                <strong className="text-black/75">Booking Reference</strong>
                <p className="mt-2 text-black/65">jaypal yadav</p>
              </div>
              <div className="flexw-[30%] flex-col">
                <strong className="text-black/75">People</strong>
                <p className="mt-2 text-black/65">2</p>
              </div>
            </div>
          </div> */}
          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">
              Hostel Information
            </h1>
            <div className="flex flex-wrap items-center my-6 gap-6">
              <div className="flex w-full sm:w-[45%] md:w-[45%] lg:w-[15%] flex-col mb-0  lg:mb-5 ">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#A1A1A1]">
                  HOSTEL NAME
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#757575]">
                  Monkey Ubud
                </p>
              </div>

              <div className="flex w-full sm:w-[45%] md:w-[45%] lg:w-[15%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#A1A1A1]">
                  Image
                </strong>
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`}
                  alt="Image"
                  className="w-20 h-12 mt-2 lg:sm-1 rounded-lg"
                  width={0}
                  height={0}
                />
              </div>
            </div>
          </div>

          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">
              Travelling Information
            </h1>
            <div className="flex flex-wrap items-center gap-x-20 my-6">
              <div className="flex sm:w-[90%] md:w-[33%] lg:w-[10%] flex-col mb-0 md:mb-3 lg:mb-4">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#A1A1A1]">
                  Country
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#757575]">
                  Bali
                </p>
              </div>
              <div className="flex mt-4 md:mt-0 lg:mt-0 mb-3 md:mb-0 lg:mb-4 sm:w-[90%] md:w-[40%] lg:w-[15%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#A1A1A1]">
                  Place
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#757575]">
                  Indonesia
                </p>
              </div>
              <div className="flex mt-4 md:mt-4 lg:mt-0 sm:w-[90%] md:w-[40%] lg:w-[15%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#A1A1A1]">
                  Flag
                </strong>
                <p className="mt-2">
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                    width={48}
                    height={48}
                    alt="Flag"
                    className="w-10 h-10"
                  />
                </p>
              </div>
            </div>
          </div>

     

          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">
              Payment Information
            </h1>
            <div className="flex items-center gap-x-20 mt-6">
              <div className="flex w-[15%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#A1A1A1]">
                  Price
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#757575]">
                  $250
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-start justify-start">
            <Link
              href={"/superadmin/dashboard/mobile-homepage"}
              className="text-white py-2 w-32 rounded bg-sky-blue-650 flex items-center justify-center"
            >
              Cancel
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileDetails;
