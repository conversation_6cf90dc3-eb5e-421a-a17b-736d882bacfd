import mongoose from 'mongoose';

const rateTypeSchema = mongoose.Schema({
    RateTypeId: {
        type: String,
        required: true
    },
    RateType: {
        type: String,
        required: true
    },
    isDeleted: {
        type: Boolean,
        default: false,
    },
    isActive: {
        type: Boolean,
        default: true
    },
},
    {
        timestamps: true
    }
);

const rateTypesModel = mongoose.model("rateTypes", rateTypeSchema);
export default rateTypesModel;