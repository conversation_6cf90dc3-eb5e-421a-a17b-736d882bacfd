/* eslint-disable react/no-unescaped-entities */
// import { useState } from 'react';

// export default function eContract() {
//   const [effectiveDate, setEffectiveDate] = useState('Effective Date');
//   const [hotelName, setHotelName] = useState('Hotel Name');
//   const [hotelAddress, setHotelAddress] = useState('Hotel Address');

//   return (
//     <div className="min-h-screen bg-gray-100 py-8 px-4">
//       <div className="max-w-5xl mx-auto bg-white shadow-lg rounded-lg p-8">
//         <h1 className="text-3xl font-bold mb-6 text-center">Hostel Mixdorm Private Limited E-Contract Agreement</h1>

//         <div className="grid grid-cols-2 gap-6 mb-6">
//           <div>
//             <label className="block text-gray-700 font-medium mb-2" htmlFor="date">Date</label>
//             <input
//               type="date"
//               id="date"
//               className="border border-gray-300 rounded-lg px-4 py-2 w-full"
//               onChange={(e) => setEffectiveDate(e.target.value)}
//             />
//           </div>

//           <div>
//             <label className="block text-gray-700 font-medium mb-2" htmlFor="hotelName">Hotel Name</label>
//             <input
//               type="text"
//               id="hotelName"
//               className="border border-gray-300 rounded-lg px-4 py-2 w-full"
//               placeholder="Enter Hotel Name"
//               onChange={(e) => setHotelName(e.target.value)}
//             />
//           </div>
//         </div>

//         <div className="mb-6">
//           <label className="block text-gray-700 font-medium mb-2" htmlFor="hotelAddress">Hotel Address</label>
//           <input
//             type="text"
//             id="hotelAddress"
//             className="border border-gray-300 rounded-lg px-4 py-2 w-full"
//             placeholder="Enter Hotel Address"
//             onChange={(e) => setHotelAddress(e.target.value)}
//           />
//         </div>

//         <div className="border-t border-gray-300 mt-8 pt-6">
//           <p className="text-gray-800 mb-4">
//             This agreement ("Agreement") is entered into as of the date of electronic signing (
//             <span className="text-red-500">{effectiveDate}</span>) by and between:
//           </p>

//           <p className="text-gray-800 mb-4">
//             Hostel Name: <span className="text-red-500">{hotelName}</span>
//           </p>

//           <p className="text-gray-800 mb-4">
//             Hostel Address: <span className="text-red-500">{hotelAddress}</span>
//           </p>

//           <p className="text-gray-800 mb-6">Terms and conditions apply as stated below.</p>

//           <h2 className="text-xl font-semibold mb-4">Terms and Conditions</h2>
//           <ul className="list-disc list-inside text-gray-700 mb-4">
//             <li>General commission terms as outlined.</li>
//             <li>Payment and cancellation policies.</li>
//             <li>Liabilities and indemnification clauses.</li>
//             <li>Termination and dispute resolution processes.</li>
//           </ul>

//           <button className="bg-blue-500 text-white px-6 py-2 rounded-lg">Submit</button>
//         </div>

//         <footer className="mt-8 border-t border-gray-300 pt-4 text-center text-gray-600">
//           <p>Contact Information</p>
//           <p>MixDorm Support Team</p>
//           <p>Phone: +91-************</p>
//         </footer>
//       </div>
//     </div>
//   );
// }
// pages/index.js
import { useState } from "react";
import React, { useRef } from "react";
import SignatureCanvas from "react-signature-canvas";

export default function eContract() {
  const [effectiveDate, setEffectiveDate] = useState("Effective Date");
  const [hostelName, setHostelName] = useState("Hostel Name");
  const [hostelAddress, setHostelAddress] = useState("Hostel Address");

  const sigCanvas = useRef(null);

  const clearSignature = () => {
    sigCanvas.current.clear();
  };

  const saveSignature = () => {
    const dataURL = sigCanvas.current.toDataURL("image/png");
    const link = document.createElement("a");
    link.href = dataURL;
    link.download = "signature.png";
    link.click();
  };

  return (
    <div className="max-w-6xl w-[1174px] bg-white float-right shadow-lg rounded-lg p-8 border-4 border-blue-800">
      <h1 className="text-xl font-semibold mb-6 text-start">
        Hostel Mixdorm Private Limited E-Contract Agreement
      </h1>
      <div className="flex items-center justify-center">
        <div className="w-[198px] h-[146px] border">123</div>
      </div>
      {/* Agreement Section */}
      <div className="pt-6">
        <p className="text-gray-500 mb-4 text-sm">
          This agreement ("Agreement") is entered into as of the date of
          electronic signing ("
          <span className="text-red-500 font-semibold">{effectiveDate}</span>")
          by and between:
        </p>
        <div className="w-44">
          <label className="block text-black font-medium mb-2" htmlFor="date">
            Date
          </label>
          <input
            type="date"
            id="date"
            className="border border-gray-300 text-gray-500 rounded-lg px-4 py-2 w-full"
            onChange={(e) => setEffectiveDate(e.target.value)}
          />
        </div>
        <p className="text-gray-500 my-4 text-sm leading-loose">
          Hostel Mixdorm Private Limited ("MixDorm"), having its registered
          office at 47/7 Baskheda Main Road Kareli, Madhya Pradesh, India
          (hereinafter referred to as "Service Provider"),and [
          <span className="text-red-500 font-semibold">{hostelName}</span>]
          ("Hostel Partner"), having its registered address at [
          <span className="text-red-500 font-semibold">{hostelAddress}</span>]
          (hereinafter referred to as "Partner")
        </p>

        {/* Input Section */}
        <div className="grid grid-cols-2 gap-6">
          <div>
            <label
              className="block text-black font-medium mb-2"
              htmlFor="hotelName"
            >
              Hostel Name
            </label>
            <input
              type="text"
              id="hotelName"
              className="border border-gray-300 rounded-lg px-4 py-2 w-full"
              placeholder="Enter Hotel Name"
              onChange={(e) => setHostelName(e.target.value)}
            />
          </div>
          <div className="mb-8">
            <label
              className="block text-black font-medium mb-2"
              htmlFor="hotelAddress"
            >
              Hostel Address
            </label>
            <input
              type="text"
              id="hotelAddress"
              className="border border-gray-300 rounded-lg px-4 py-2 w-full"
              placeholder="Enter Hotel Address"
              onChange={(e) => setHostelAddress(e.target.value)}
            />
          </div>
        </div>

        {/* <h2 className="text-xl font-bold mb-4">Terms and Conditions</h2>
        <ol className="list-decimal list-inside text-gray-700 mb-6">
          <li>General commission terms as outlined.</li>
          <li>Payment and cancellation policies apply as specified.</li>
          <li>Liabilities and indemnification clauses are binding.</li>
          <li>Termination and dispute resolution processes are enforceable.</li>
        </ol> */}

        {/* <p className="text-gray-700 mb-6">
          By signing this agreement, the parties acknowledge having read,
          understood, and agreed to the terms and conditions stated herein.
        </p> */}

        {/* <div className="grid grid-cols-2 gap-6">
          <div>
            <label
              className="block text-gray-700 font-medium mb-2"
              htmlFor="signature"
            >
              Enter your name
            </label>
            <input
              type="text"
              id="signature"
              className="border border-gray-300 rounded-lg px-4 py-2 w-full"
              placeholder="Your Name"
            />
          </div>

          <div>
            <label className="block text-gray-700 font-medium mb-2">
              Draw your signature
            </label>
            <div className="border border-gray-300 rounded-lg px-4 py-8 bg-gray-50 text-center">
              Signature Box
            </div>
          </div>
        </div> */}
        <h1 className="text-xl font-bold mb-3 text-black">
          Terms and Conditions
        </h1>

        {/* Sections */}
        <div className="space-y-3">
          {/* Objective */}
          <section>
            <h2 className="text-sm font-bold text-black mb-1">1. Objective</h2>
            <p className="text-gray-500 text-sm">
              This Agreement sets forth the terms and conditions under which the
              Partner will utilize MixDorm's platform and services to facilitate
              online booking of its hostel accommodations.
            </p>
          </section>

          {/* Commission Structure */}
          <section>
            <h2 className="text-sm font-bold text-black mb-1">
              2. Commission Structure
            </h2>
            <ul>
              <li className="text-sm text-black font-bold">
                1. Standard Commission:
              </li>
              <li className="list-disc text-sm text-gray-500 ml-5">
                MixDorm will retain a commission of 15% on the total booking
                value for all bookings facilitated through the MixDorm platform.
              </li>
              <li className="text-sm text-black font-bold">
                2. Exclusive Top Featured Property Commission:
              </li>
              <li className="list-disc text-sm text-gray-500 ml-5">
                For properties opting to be exclusively featured in top search
                results and promoted listings, a commission of 20% shall be
                applicable on the total booking value.
              </li>
            </ul>
          </section>

          {/* Payment Terms */}
          <section>
            <h2 className="text-sm text-black font-bold mb-1">
              3. Payment Terms
            </h2>
            <ul className="list-decimal ml-4 text-gray-500 text-sm">
              <li className="text-sm">
                Commissions will be automatically deducted from the booking
                amount before the payout is transferred to the Partner.
              </li>
              <li className="text-sm">
                Payments to the Partner will be disbursed on a weekly basis,
                subject to the deduction of applicable commissions and taxes.
              </li>
              <li className="text-sm">
                MixDorm will provide a detailed invoice for all bookings and
                commissions via the Partner dashboard.
              </li>
            </ul>
          </section>
          {/* Partner obligations */}
          <section>
            <h2 className="text-sm text-black font-bold mb-1">
              4. Partner Obligations
            </h2>
            <ul className="list-decimal ml-4 text-gray-500 text-sm">
              <li className="text-sm">
                Ensure accurate and up-to-date property information, including
                availability, pricing, and images, on the MixDorm platform.
              </li>
              <li className="text-sm">
                Honor all bookings made through MixDorm and provide quality
                service to Customers.
              </li>
              <li className="text-sm">
                Notify MixDorm immediately for any discrepansancies or
                cancellations.
              </li>
            </ul>
          </section>

          {/* Term and Termination */}
          <section>
            <h2 className="text-sm font-bold text-black mb-1">
              5. Term and Termination
            </h2>
            <ul className="list-decimal ml-4 text-gray-500">
              <li className="text-sm">
                This Agreement shall remain in effect unless terminated by
                either party with a 30-day prior written notice.
              </li>
              <li className="text-sm">
                MixDorm may terminate this Agreement immediately in case of
                breach of terms or fraudulent activity by the Partner.
              </li>
            </ul>
          </section>
          <section>
            <h2 className="text-sm font-bold text-black mb-1">
              6. Dispute Resolution
            </h2>
            <p className="ml-4 text-sm text-gray-500">
              In the event of any dispute arising under these agreement, the
              parties agree to resolve the dispute through mutual negotiation.
              If unresolved, the dispute shall be referred to arbitration under
              the Indian Arbitration and Conciliation Act, 1996.
            </p>
          </section>

          <section>
            <h2 className="text-sm font-bold text-black mb-1">
              7. Liability and Indemnity
            </h2>
            <ul className="list-decimal ml-4 text-gray-500">
              <li className="text-sm">
                MixDorm is not liable for any direct, indirect or concequential
                damages resulting from the partner's failure to fullfill
                bookings.
              </li>
              <li className="text-sm">
                The Partner agrees to idemnify and hold Mixdorm harmless for any
                claims arising out of the partner's non-compliance with laws or
                regulations.
              </li>
            </ul>
          </section>
          <section>
            <h2 className="text-sm font-bold text-black mb-1">
              8. Confidentiality
            </h2>
            <p className="ml-4 text-sm text-gray-500">
              Both parties agree to maintain confidentiality regarding sensitive
              business and customer information and shall not disclose such
              information without prior written consent prior consent.
            </p>
          </section>
          <section>
            <h2 className="text-sm font-bold text-black mb-1">
              9. Governing Law
            </h2>
            <p className="ml-4 text-sm text-gray-500">
              These agreement shall be governed by and construed in accordance
              with the laws of India.
            </p>
          </section>

          {/* Signature */}
          <section>
            <h2 className="text-sm font-bold text-black mb-1">
              Agreement and Acceptance
            </h2>
            <p className="text-sm mb-4 text-gray-500">
              By signing this Agreement, the Partner acknowledges having read,
              understood, and agreed to the terms and conditions stated herein.
            </p>
          </section>
        </div>
      </div>

      {/* Agreement Section */}
      {/* <div className=" border-gray-300 pt-6">
        <h2 className="text-sm font-bold text-black mb-1">
          E-Signature options
        </h2>
        
          <div>
            <label
              className="block text-black font-semibold mb-2"
              htmlFor="signature"
            >
              Type your name
            </label>
            <input
              type="text"
              id="signature"
              className="border border-gray-300 px-4 py-2 w-1/4"
              placeholder="Your Name"
            />
          </div>

          <div>
            <label className="block text-gray-700 font-medium mb-2">
              Draw your signature
            </label>
            <div className="border border-gray-300 w-1/4 rounded-lg px-4 py-8 bg-gray-50 text-center">
              Signature Box
            </div>
          </div>
        

        <div className="mt-6 text-center">
          <button className="bg-blue-500 text-white px-6 py-2 rounded-lg">
            Submit
          </button>
        </div>
      </div> */}
        <h1 className="text-2xl font-bold mb-4">Draw Your Signature</h1>
        <div className="border-2 border-gray-300 rounded-md bg-white p-2 shadow-lg">
          <SignatureCanvas
            ref={sigCanvas}
            canvasProps={{
              className:
                "w-[400px] h-[200px] border-2 border-gray-300 rounded-md",
            }}
          />
        </div>
        <div className="mt-4 flex gap-4">
          <button
            onClick={clearSignature}
            className="px-4 py-2 bg-red-500 text-white font-medium rounded-md hover:bg-red-600"
          >
            Clear
          </button>
          <button
            onClick={saveSignature}
            className="px-4 py-2 bg-green-500 text-white font-medium rounded-md hover:bg-green-600"
          >
            Save
          </button>
        </div>
      
      {/* Footer Section */}
      <footer className="mt-8 border-t border-gray-300 pt-4 text-center text-gray-600">
        <p>Contact Information</p>
        <p>MixDorm Support Team</p>
        <p>Phone: +91-************</p>
      </footer>
    </div>
  );
}
