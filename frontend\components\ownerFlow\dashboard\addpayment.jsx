/* eslint-disable no-constant-binary-expression */
import { addPayment<PERSON>pi, RoomList<PERSON>pi } from "@/services/ownerflowServices";
import { getItemLocalStorage } from "@/utils/browserSetting";
import React, { useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";
import CustomSelect from "@/components/common/CustomDropdown2";
import countries from "world-countries";
import { format, isSameDay, isToday } from "date-fns";
import Image from "next/image";

const Addpayment = ({ updatePaymentList, closebookingModal }) => {
  const [paymentData, setPaymentData] = useState({
    userName: "",
    date: "",
    room: "",
    amount: "",
    currency: "",
  });
  const [roomList, setRoomList] = useState([]);
  const [currencies, setCurrencies] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [weekdayRate, setWeekdayRate] = useState(null);
  const [selectedRoomId, setSelectedRoomId] = useState(null);
  const isFirstRender = useRef(null);
  const isCurrenciesFetched = useRef(false);
  const [showCalendar, setShowCalendar] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(null);
  const calendarRef = useRef(null);
  const inputRef = useRef(null);
  const id = getItemLocalStorage("hopid");
  

  const handlePrevMonth = () => {
    setCurrentMonth(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() - 1, 1)
    );
  };

  const handleNextMonth = () => {
    setCurrentMonth(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() + 1, 1)
    );
  };

  // Generate days for current month
  function generateDays(month) {
    const daysInMonth = new Date(
      month.getFullYear(),
      month.getMonth() + 1,
      0
    ).getDate();
    const firstDayIndex = new Date(
      month.getFullYear(),
      month.getMonth(),
      1
    ).getDay();

    const days = [];

    // Add blank spaces for days of previous month
    for (let i = 0; i < firstDayIndex; i++) {
      days.push(null);
    }

    // Add days of the current month
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(new Date(month.getFullYear(), month.getMonth(), i));
    }

    return days;
  }

  // Close calendar if clicked outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target) &&
        !inputRef.current.contains(event.target)
      ) {
        setShowCalendar(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const fetchList = async (id) => {
    try {
      const response = await RoomListApi(id, 1, 100);
      if (response.status === 200) {
        setRoomList(response.data.data.rooms);
      } else {
        // Handle error response
        toast.error("Failed to fetch rooms");
      }
    } catch (error) {
      console.log("Error fetching rooms:", error);
      toast.error("Error fetching rooms");
    }
  };

  useEffect(() => {
    if (id && !isFirstRender.current) {
      fetchList(id);
    } else {
      isFirstRender.current = false;
    }
  }, [id]);

  const getWeekdayRateByName = (roomName) => {
    const room = roomList.find((item) => item.name === roomName?.label);
    if (room && room.rate && room.rate.weekdayRate) {
      setWeekdayRate(room.rate.weekdayRate.value);
      setPaymentData({
        ...paymentData,
        amount: room?.rate?.weekdayRate?.value,
        currency: room?.currency,
        room: roomName,
      });
      setSelectedRoomId(room?._id);
    } else {
      console.error("Room or weekdayRate not found.");
    }
  };

  // useEffect(() => {
  //   if (!isCurrenciesFetched.current) {
  //     const fetchCurrenciesWithFlags = async () => {
  //       try {
  //         // Fetch currency rates
  //         const exchangeRateResponse = await axios.get(
  //           "https://api.exchangerate-api.com/v4/latest/USD"
  //         );

  //         if (exchangeRateResponse.data) {
  //           const currencyRates = exchangeRateResponse.data.rates;

  //           // Map currency codes to country data using the world-countries package
  //           const currencyList = Object.keys(currencyRates).map(
  //             (currencyCode) => {
  //               const country = countries.find((country) =>
  //                 country.currencies
  //                   ? Object.keys(country.currencies).includes(currencyCode)
  //                   : false
  //               );

  //               return {
  //                 code: currencyCode,
  //                 name:
  //                   country?.currencies?.[currencyCode]?.name || currencyCode, // Fallback to the currency code if no name is found
  //                 symbol: country?.currencies?.[currencyCode]?.symbol || "", // Fallback to an empty string if no symbol is found
  //                 flag:
  //                   `https://flagcdn.com/w320/${country?.cca2?.toLowerCase()}.png` ||
  //                   "https://via.placeholder.com/30x25", // Fallback to a placeholder if no flag is found
  //               };
  //             }
  //           );

  //           setCurrencies(currencyList);
  //         }
  //       } catch (error) {
  //         console.error("Error fetching currencies or rates:", error);
  //       }
  //     };

  //     fetchCurrenciesWithFlags();
  //     isCurrenciesFetched.current = true; // Ensure the fetch happens only once
  //   }
  // }, []);

  useEffect(() => {
    if (!isCurrenciesFetched.current) {
      const fetchCountryCurrencyCodes = async () => {
        try {
          const countryData = countries.map((country) => {
            // Safely extract currency code and symbol, falling back to defaults
            const currencyCode =
              country?.currencies && Object.keys(country?.currencies)[0]
                ? Object.keys(country?.currencies)[0]
                : "N/A";
            const currencySymbol =
              country?.currencies && country?.currencies[currencyCode]?.symbol
                ? country?.currencies[currencyCode]?.symbol
                : "€";

            // Get the flag code (ISO 3166-1 alpha-2) for the flag
            const flagCode = country.cca2 ? country.cca2.toLowerCase() : "xx"; // Default to 'xx' if cca2 is missing

            // Construct the flag image URL or use a placeholder
            const flag =
              `https://flagcdn.com/w320/${flagCode}.png` ||
              "https://via.placeholder.com/30x25";

            return {
              name: country?.name?.common || "Unknown", // Country name, fallback to "Unknown" if not found
              code: currencyCode, // Currency code
              symbol: currencySymbol, // Currency symbol
              flag: flag, // Flag image URL
            };
          });

          setCurrencies(countryData); // Store the country data
        } catch (error) {
          console.error("Error fetching country data:", error);
        }
      };
      fetchCountryCurrencyCodes();
      isCurrenciesFetched.current = true;
    }
  }, []);

  const handleInputChange = (e) => {

      const dateValue = e.target.value;
  setSelectedDate(new Date(dateValue));

    const { name, value } = e.target;
    setPaymentData({ ...paymentData, [name]: value });
    if (name === "room") {
      getWeekdayRateByName(value);
    }
  };

  const handleValidation = () => {
    const { userName, date, room, amount, currency } = paymentData;
    if (!userName.trim()) {
      toast.error("Name is required");
      return false;
    }
    if (!date.trim()) {
      toast.error("Date is required");
      return false;
    }
    if (!room) {
      toast.error("Room type is required");
      return false;
    }
    if (!amount || isNaN(amount) || amount <= 0) {
      toast.error("Valid amount is required");
      return false;
    }
    if (!currency) {
      toast.error("Currency is required");
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!handleValidation()) {
      return;
    }
    try {
      const payload = {
        userName: paymentData?.userName || "",
        date: paymentData?.date || "",
        amount: paymentData?.amount || 0,
        room: selectedRoomId || "",
        property: id || "",
        currency: paymentData?.currency?.value
          ? paymentData?.currency?.value
          : paymentData?.currency,
      };
      const response = await addPaymentApi(payload);

      if (response?.data?.status) {
        toast.success(response?.data?.message || "Payment added successfully!");
        updatePaymentList();
        closebookingModal();
      } else {
        toast.error("Failed to add payment");
      }
    } catch (error) {
      console.error("Error submitting payment:", error);
      toast.error("Error submitting payment");
    }
  };

  console.log("paymentData", paymentData);

  return (
    <>
      <div className="max-w-[780px] mx-auto">
        <div className="grid mb-3">
          <div>
            <label
              className="block text-black sm:text-sm text-xs font-medium mb-1.5"
              htmlFor="username"
            >
              Name
            </label>
            <input
              type="text"
              name="userName"
              value={paymentData.userName}
              onChange={handleInputChange}
              className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
              placeholder="Enter Name"
            />
          </div>
        </div>
        <div className="grid mb-3">
          <div className="relative w-full">
            <label
              className="block text-black sm:text-sm text-xs font-medium mb-1.5"
              htmlFor="toDate"
            >
              Date
            </label>

            <input
              type="text"
              name="toDate"
              id="toDate"
              ref={inputRef}
              value={paymentData.date}
              placeholder="mm/dd/yyyy"
              readOnly
              onClick={() => setShowCalendar(!showCalendar)}
              className="block w-full p-2 px-4 py-3 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-800 placeholder:text-gray-400 cursor-pointer"
            />

            {showCalendar && (
              <div
                ref={calendarRef}
                className="absolute top-full left-0 bg-white border border-black/50 rounded-lg shadow-lg px-4 py-2 z-50 w-full mt-0.5"
              >
                {/* Month navigation */}
                <div className="flex items-center justify-between mb-4">
                  <button
                    onClick={handlePrevMonth}
                    className="text-lg font-bold px-2"
                  >
                    &#8592;
                  </button>

                  <span className="text-base font-semibold">
                    {format(currentMonth, "MMMM yyyy")}
                  </span>

                  <button
                    onClick={handleNextMonth}
                    className="text-lg font-bold px-2"
                  >
                    &#8594;
                  </button>
                </div>

                {/* Weekdays */}
                <div className="grid grid-cols-7 gap-2 text-center text-sm font-semibold text-gray-600">
                  <div>Su</div>
                  <div>Mo</div>
                  <div>Tu</div>
                  <div>We</div>
                  <div>Th</div>
                  <div>Fr</div>
                  <div>Sa</div>
                </div>

                {/* Days */}
                <div className="grid grid-cols-7 gap-1 mt-2 text-center text-sm">
                  {generateDays(currentMonth).map((day, index) =>
                    day ? (
                      <button
                        key={index}
                        className={`rounded-full p-2 flex items-center justify-center  ${isToday(day) ? "border-2 border-primary-blue" : ""} ${
                          isSameDay(day, selectedDate)
                            ? "bg-primary-blue text-white"
                            : "hover:bg-primary-blue hover:text-white"
                        }`}
                        onClick={() => {
                          handleInputChange({
                            target: {
                              name: "date",
                              value: format(day, "MM-dd-yyyy"),
                            },
                          });
                          setShowCalendar(false);
                        }}
                      >
                        {day.getDate()}
                      </button>
                    ) : (
                      <div key={index} />
                    )
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="grid mb-3">
          <div>
            <label
              className="block text-black sm:text-sm text-xs font-medium mb-1.5"
              htmlFor="username"
            >
              Room Type
            </label>
            {/* <select
              name='room'
              value={paymentData.room}
              onChange={handleInputChange}
              className='w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500'
            >
              <option disabled value=''>
                Select Room
              </option>
              {roomList.map((roomData, index) => (
                <option key={index} value={roomData?.name}>
                  {roomData?.name}
                </option>
              ))}
            </select> */}

            <CustomSelect
              name="room"
              options={roomList.map((roomData) => ({
                value: roomData?._id,
                label: roomData?.name,
              }))}
              value={
                paymentData.room
                  ? {
                      value: paymentData?.room?.value,
                      label: `${paymentData.room?.label}`,
                    }
                  : null
              }
              onChange={(selectedOption) =>
                handleInputChange({
                  target: { name: "room", value: selectedOption },
                })
              }
              placeholder="Select Room"
            />
          </div>
        </div>
        <div className="mb-3">
          <label
            className="block text-black sm:text-sm text-xs font-medium mb-1.5"
            htmlFor="username"
          >
            Currency
          </label>
          {/* <select
            name='currency'
            value={paymentData?.currency}
            onChange={handleInputChange}
            className='w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500'
          >
            <option disabled value=''>
              Select currency
            </option>
            {currencies.map((currency) => (
              <option key={currency} value={currency}>
                {currency}
              </option>
            ))}
          </select> */}

          <CustomSelect
            options={currencies.map((currency) => ({
              value: currency.code,
              label: (
                <span className="flex items-center">
                  <Image 
                    src={currency.flag || "/placeholder.svg"}
                    alt={`${currency.code} Flag`}
                    className="inline-block w-4 h-3 mr-2"
                    width={20}
                    height={15}
                  />
                  {currency.code} ({currency.symbol})
                </span>
              ),
            }))}
            value={paymentData.currency}
            onChange={(selectedOption) =>
              handleInputChange({
                target: { name: "currency", value: selectedOption },
              })
            }
            placeholder="Select Currency"
          />
        </div>
        <div className="grid mb-3">
          <div>
            <label
              className="block text-black sm:text-sm text-xs font-medium mb-1.5"
              htmlFor="username"
            >
              Amount Paid
            </label>
            <input
              type="number"
              name="amount"
              value={paymentData.amount}
              onChange={handleInputChange}
              className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
              placeholder="Enter Amount Paid"
            />
          </div>
        </div>
        <div className="flex items-center justify-between w-full sm:my-14 my-7 gap-4 col-span-2 py-4 bg-white/60 sticky bottom-0 backdrop-blur-sm">
          <button
            className="hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 px-4 border-black rounded-lg w-full text-sm"
            onClick={closebookingModal}
          >
            Cancel
          </button>
          <button
            className="bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4 border-black rounded-lg w-full text-sm"
            onClick={handleSubmit}
          >
            Add Payment
          </button>
        </div>
      </div>
    </>
  );
};

export default Addpayment;
