 
"use client";
import { FaRedo } from "react-icons/fa";
import { VscFilter } from "react-icons/vsc";

const FilterBox = () => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-7 w-[80%] border-2 bg-white rounded-xl">
        <button className="flex items-center justify-center md:border-r border-b p-4 rounded-l-xl bg-white ">
          <VscFilter className="text-gray-700" size={17} />
        </button>
        <div className="text-sm font-medium  bg-white flex items-center  md:border-r border-b p-4 justify-center text-gray-700">
          Filter By
        </div>{" "}
        <button className="flex items-center justify-center space-x-4  md:border-r border-b p-4 bg-white ">
          <select className="bg-transparent  focus:outline-none sm:text-sm">
            <option>Country</option>
          </select>
        </button>
        <button className="flex items-center justify-center space-x-4  md:border-r border-b p-4 bg-white ">
          <select className="bg-transparent focus:outline-none sm:text-sm">
            <option>New Hostels</option>
          </select>
        </button>
        <button className="flex items-center justify-center space-x-4  md:border-r border-b p-4 bg-white ">
          <select className="bg-transparent focus:outline-none sm:text-sm">
            <option>Status</option>
          </select>
        </button>
        <button className="flex items-center justify-center space-x-4  md:border-r border-b p-4 bg-white ">
          <select className="bg-transparent focus:outline-none sm:text-sm">
            <option>Rates</option>
          </select>
        </button>
        <button className="text-red-600  bg-white text-sm font-medium  md:border-r p-4 rounded-r-xl flex items-center justify-center">
          <FaRedo className="mr-1" size={16} />
          Reset Filter
        </button>
      </div>
    );
};

export default FilterBox;