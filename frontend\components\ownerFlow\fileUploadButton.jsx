 
 
import { styled } from "@mui/material/styles";
import Button from "@mui/material/Button";
import Image from "next/image";

const VisuallyHiddenInput = styled("input")({
  clip: "rect(0 0 0 0)",
  clipPath: "inset(50%)",
  height: 1,
  overflow: "hidden",
  position: "absolute",
  bottom: 0,
  left: 0,
  whiteSpace: "nowrap",
  width: 1,
});

const FileUploadButton = ({
  label,
  name,
  onChange,
  buttonText,
  multiple = false,
  selectedFiles,
  accept,
}) => {
  const displayText =
    selectedFiles && selectedFiles.length > 0
      ? selectedFiles.map((file) => file.name).join(", ")
      : buttonText;

  return (
    <>
      <h4 className="text-lg font-semibold text-black my-5">{label}</h4>
      <Button
        component="label"
        variant="outlined"
        startIcon={
          <Image
            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/cloudupload.svg`}
            alt="Upload Icon"
            width={24}
            height={24}
            loading="lazy"
          />
        }
        sx={{
          width: "100%",
          paddingX: "16px",
          paddingY: "12px",
          borderColor: "#EEEEEE",
          borderRadius: "32px",
          justifyContent: "flex-start",
          color: "#888888",
          "&:focus": {
            outline: "none",
            ring: "2px solid #40E0D0",
          },
          "&:hover": {
            backgroundColor: "transparent",
            borderColor: "#EEEEEE",
          },
        }}
      >
        {displayText}
        <VisuallyHiddenInput
          type="file"
          name={name}
          onChange={onChange}
          multiple={multiple}
          accept={accept}
        />
      </Button>
      
    </>
  );
};

export default FileUploadButton;
