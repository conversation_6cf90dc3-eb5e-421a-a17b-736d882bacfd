    import mongoose from 'mongoose';

    const socialLikeSchema = new mongoose.Schema({
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
    socialIntrectionComment: {
        type: mongoose.Schema.Types.ObjectId,
        required: true
    },
    isDeleted: {
        type: Boolean,
        default: false,
    },
    isLike:{
        type:Boolean,
        default:false
    }
    }, { timestamps: true });

    const socialIntrectionCommentLikesModel = mongoose.model('socialInteractionsCommentsLikes', socialLikeSchema);
    export default socialIntrectionCommentLikesModel;
