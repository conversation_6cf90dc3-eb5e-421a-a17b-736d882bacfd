import React from "react";
import Image from "next/image";
import Link from "next/link";
import { CalendarDays } from "lucide-react";

import { But<PERSON> } from "@mui/material";
import { getBlogApi } from "@/services/webflowServices";
import dynamic from "next/dynamic";
import { getItemLocalStorage } from "@/utils/browserSetting";
// import { useNavbar } from "@/components/home/<USER>";
import toast from "react-hot-toast";
import Head from "next/head";
import { FaHeart } from "react-icons/fa6";
import { getContentSchema } from "@/lib/schema/contentSchema";

export async function getServerSideProps(context) {
  const currentPage = 1;
  const limit = 5;
  let blogData = [];
  let totalPages = 1;
  try {
    const response = await getBlogApi(currentPage, limit);
    blogData = response?.data?.data?.blogs || [];
    totalPages = response?.data?.data?.pagination?.totalPages || 1;
  } catch (error) {
    console.error("Error fetching blog data:", error);
  }

  // Build the full URL for the main blog post
  let fullUrl = "";
  if (blogData[0]?._id) {
    const protocol = context.req.headers['x-forwarded-proto'] || 'https';
    const host = context.req.headers.host;
    fullUrl = `${protocol}://${host}/blog-details?id=${blogData[0]._id}`;
  }

  return {
    props: {
      blogData,
      totalPages,
      fullUrl,
    },
  };
}

const Blog = ({ blogData, totalPages, fullUrl }) => {
  const [loading, setLoading] = React.useState(false);
  const [currentPage, setCurrentPage] = React.useState(1);
  // eslint-disable-next-line no-unused-vars
  const [isUpdate, setIsUpdateData] = React.useState(false);
  const [imageLoaded, setImageLoaded] = React.useState(false);
  const [imageFailedMap, setImageFailedMap] = React.useState({});
  // const isFirstRender = React.useRef(null);
  // const { token } = useNavbar ? useNavbar() : { token: null };

  const CustomPagination = dynamic(
    () => import("@/components/customPagination/customPagination"),
    {
      ssr: false,
    }
  );

  const Loader = dynamic(() => import("@/components/loader/loader"), {
    ssr: false,
  });

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  const HandleLike = async () => {
    if (getItemLocalStorage("token")) {
      setLoading(true);
      try {
        setIsUpdateData((prevState) => !prevState);
      } catch (error) {
        console.error("Error fetching stay data:", error);
      } finally {
        setLoading(false);
      }
    } else {
      toast.error("Please login first!!");
      // router.push("/");
    }
  };

  const mainBlog = blogData?.[0];
  const contentSchema = mainBlog
    ? getContentSchema({
        title: mainBlog.title,
        description: mainBlog.description,
        authorName: mainBlog.authorName || "Aayush",
        datePublished: mainBlog.createdAt,
        dateModified: mainBlog.updatedAt,
        imageUrl: mainBlog.images?.[0] || "",
        url: fullUrl,
      })
    : null;

  return (
    <>
      <Head>
        <title>Hostel Booking Tips and Travel Guides</title>
        <meta
          name="description"
          content="Read the Latest Travel Guides, Hostel Reviews, and Expert Tips for Budget Travelers Worldwide."
        />
        {contentSchema && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(contentSchema) }}
          />
        )}
      </Head>
      <Loader open={loading} />

      {loading ? (
        <div className="container md:pb-16 font-manrope md:pt-12 py-8">
          {/* Main Post Skeleton */}
          <div className="bg-white md:pb-16 font-manrope md:pt-12 py-8">
            <div className="container md:px-8 lg:px-12 xl:px-16">
              {/* Title Skeleton */}
              <div className="md:mb-8 mb-4">
                <div className="h-10 w-64 bg-gray-200 rounded-md animate-pulse"></div>
              </div>

              {/* Flex Container for Main Post and Other Posts */}
              <div className="flex flex-col lg:flex-row">
                {/* Main Post Skeleton */}
                <div className="w-full lg:w-3/5 lg:pr-6 rounded-b-lg mb-5 lg:mb-0">
                  <div className="border-2 border-[#EEEEEE] rounded-3xl overflow-hidden h-auto flex flex-col">
                    <div className="relative">
                      <div className="w-full h-80 bg-gray-200 rounded-t-3xl animate-pulse"></div>
                    </div>
                    <div className="md:p-6 p-4 flex-grow flex flex-col leading-6">
                      <div className="flex items-center mb-4 xs:text-sm text-xs md:gap-4 gap-2">
                        <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
                      </div>
                      <div className="mb-3">
                        <div className="h-6 w-full bg-gray-200 rounded animate-pulse"></div>
                        <div className="h-6 w-3/4 bg-gray-200 rounded animate-pulse mt-2"></div>
                      </div>
                      <div className="space-y-2 mb-5">
                        <div className="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
                        <div className="h-4 w-5/6 bg-gray-200 rounded animate-pulse"></div>
                        <div className="h-4 w-4/6 bg-gray-200 rounded animate-pulse"></div>
                      </div>
                      <div className="h-10 w-32 bg-gray-200 rounded-3xl animate-pulse ml-auto"></div>
                    </div>
                  </div>
                </div>

                {/* Other Posts Skeleton */}
                <div className="grid grid-cols-1 md:grid-cols-2 xl:gap-6 gap-3 w-full lg:w-2/5">
                  {[...Array(4)].map((_, index) => (
                    <div
                      key={index}
                      className="bg-white border-2 border-[#EEEEEE] rounded-3xl overflow-hidden shadow-sm flex flex-col max-h-[300px]"
                    >
                      <div className="relative">
                        <div className="w-full h-36 bg-gray-200 rounded-t-3xl animate-pulse"></div>
                      </div>
                      <div className="p-3 flex-grow flex flex-col">
                        <div className="mb-1.5">
                          <div className="h-3 w-32 bg-gray-200 rounded animate-pulse"></div>
                        </div>
                        <div className="space-y-2">
                          <div className="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
                          <div className="h-4 w-5/6 bg-gray-200 rounded animate-pulse"></div>
                        </div>
                        <div className="h-8 w-24 bg-gray-200 rounded-3xl animate-pulse ml-auto mt-4"></div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-white md:pb-16 font-manrope md:pt-12 py-8">
          <div className="container md:px-8 lg:px-12 xl:px-16">
            <h2 className="font-bold  text-black font-manrope md:mb-8 mb-4 md:text-[35px]">
              <span className="text-[#40E0D0]">Latest</span> Blog
            </h2>
            {/* Flex Container for Main Post and Other Posts */}
            <div className="flex flex-col lg:flex-row ">
              {/* Main Post */}
              <div className="w-full lg:w-3/5 lg:pr-6 rounded-b-lg mb-5 lg:mb-0 ">
                <div className="border-2 border-[#EEEEEE] rounded-3xl overflow-hidden h-auto flex flex-col">
                  <div className="relative h-80 w-full">
                    {blogData?.[0]?.images?.[0] ? (
                      <>
                        {/* Loading/Fallback State */}
                        {(!imageLoaded || imageFailedMap["mainBlog"]) && (
                          <div className="absolute inset-0 bg-gray-200 rounded-t-3xl flex items-center justify-center animate-pulse">
                            <span className="text-4xl font-bold text-gray-400">
                              MixDorm
                            </span>
                          </div>
                        )}

                        {/* Actual Image */}
                        {!imageFailedMap["mainBlog"] && (
                          <Image
                            src={blogData[0].images[0]}
                            alt="Main Post"
                            fill
                            className={`object-cover rounded-t-3xl ${
                              !imageLoaded ? "opacity-0" : "opacity-100"
                            }`}
                            loading="lazy"
                            onLoad={() => setImageLoaded(true)}
                            onError={() =>
                              setImageFailedMap((prev) => ({
                                ...prev,
                                ["mainBlog"]: true,
                              }))
                            }
                          />
                        )}
                      </>
                    ) : (
                      <div className="absolute inset-0 bg-gray-100 rounded-t-3xl flex items-center justify-center">
                        <span className="text-4xl font-bold text-gray-400">
                          MIXDORM
                        </span>
                      </div>
                    )}

                    {/* Floating Elements (Category & Like Button) */}
                    <div className="absolute top-4 left-0 right-0 flex items-center justify-between px-4 z-10">
                      <Link
                        href=""
                        className="bg-white px-3 py-1 rounded-full text-xs font-semibold shadow-sm"
                        prefetch={false}
                      >
                        {blogData?.[0]?.categoryId?.title || "Uncategorized"}
                      </Link>
                      <Button
                        className={`w-7 h-7 p-0 min-w-0 text-center rounded-full shadow-sm ${
                          blogData?.[0]?.liked
                            ? "text-red-600 bg-white"
                            : "bg-white text-black hover:text-red-600"
                        }`}
                        onClick={() =>
                          HandleLike(blogData?.[0]?.liked, blogData?.[0]?._id)
                        }
                      >
                        <FaHeart className="text-base" />
                      </Button>
                    </div>
                  </div>
                  <div className="md:p-6 p-4 flex-grow flex flex-col leading-6">
                    <div className="flex items-center text-black/50 mb-4 xs:text-sm text-xs md:gap-4 gap-2">
                      <div className="flex items-center">
                        <span>
                          <CalendarDays size={18} />
                        </span>
                        <span className="ml-1">
                          <div className="w-fit">
                            {" "}
                            {new Date(
                              blogData?.[0]?.createdAt
                            ).toLocaleDateString("en-US", {
                              year: "numeric",
                              month: "short",
                              day: "numeric",
                            })}
                          </div>
                        </span>
                      </div>
                      {/* <div className="flex items-center">
                      <span>
                        <Clock3 size={18} />
                      </span>
                      <span className="ml-1">6 mins</span>
                    </div> */}
                      {/* <div className="flex items-center">
                      <span>
                        <MessageCircleMore size={18} />
                      </span>
                      <span className="ml-1">38 comments</span>
                    </div> */}
                    </div>
                    <h2 className="mb-3 leading-6">
                      <Link
                        href={`/blog-details?id=${blogData?.[0]?._id}`}
                        className="sm:text-xl text-lg font-bold hover:text-primary-blue"
                        prefetch={false}
                      >
                        {blogData?.[0]?.title}
                      </Link>
                    </h2>
                    <p className="text-[#888888] text-sm leading-6 mb-5">
                      {blogData?.[0]?.description}
                    </p>
                    {/* <p className="text-[#888888] text-sm leading-6 mb-5">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed
                    do eiusmod tempor incididunt ut labore et dolore magna
                    aliqua. Ut enim ad minim veniam, quis nostrud exercitation
                    ullamco laboris nisi ut aliquip ex ea commodo consequat.
                    Duis aute irure dolor in reprehenderit.
                  </p> */}
                    <div className="xs:flex items-center sm:mt-4">
                      {/* <div className="flex items-center mb-5 xs:mb-0">
                      <span>
                        <Image
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/profile.svg`}
                          alt="Main Post"
                          className="w-[44px] h-[44px] object-cover rounded-full"
                          width={44}
                          height={44}
                          loading="lazy"
                        />
                      </span>
                      <span className="ml-2 text-gray-800 font-semibold">
                        Jimmy Dave
                      </span>
                    </div> */}
                      <Link
                        href={`/blog-details?id=${blogData?.[0]?._id}`}
                        className="ml-auto bg-black text-white px-4 py-2 rounded-3xl font-semibold hover:bg-primary-blue hover:text-black transition-all text-center inline-block"
                        prefetch={false}
                      >
                        Keep Reading
                      </Link>
                    </div>
                  </div>
                </div>
              </div>

              {/* Other Posts */}
              <div className="grid grid-cols-1 md:grid-cols-2 xl:gap-6 gap-3 w-full lg:w-2/5 ">
                {blogData?.slice(1).map((item, index) => (
                  <div
                    key={index}
                    className="bg-white border-2 border-[#EEEEEE] rounded-3xl overflow-hidden shadow-sm flex flex-col max-h-[300px]"
                  >
                    <div className="relative">
                      {/* <Image
                        src={item?.images?.[0]}
                        alt="Main Post"
                        className="w-full h-36 object-cover rounded-t-3xl"
                        width={500}
                        height={192}
                        loading="lazy"
                      /> */}
                      {item?.images?.[0] ? (
                        <div className="relative">
                          {(!imageLoaded || imageFailedMap[item._id]) && (
                            <div className="w-full h-36 bg-gray-200 flex justify-center items-center rounded-t-3xl animate-pulse">
                              <h1 className="text-gray-400 font-bold font-manrope text-4xl">
                                MixDorm
                              </h1>
                            </div>
                          )}
                          {!imageFailedMap[item._id] && (
                            <Image
                              src={item.images[0]}
                              alt={item?.title}
                              className={`w-full h-36 object-cover rounded-t-3xl ${
                                !imageLoaded ? "opacity-0" : "opacity-100"
                              }`}
                              width={500}
                              height={192}
                              onLoadingComplete={() => setImageLoaded(true)}
                              onError={() =>
                                setImageFailedMap((prev) => ({
                                  ...prev,
                                  [item._id]: true,
                                }))
                              }
                            />
                          )}
                        </div>
                      ) : (
                        <div className="w-full xs:h-[212px] h-[150px] bg-slate-200 flex justify-center items-center rounded-t-3xl">
                          <h1 className="text-white font-bold font-manrope">
                            MixDorm
                          </h1>
                        </div>
                      )}

                      <div className="absolute top-4 -left-1 right-0 flex items-center justify-between px-2">
                        <Link
                          href=""
                          className="bg-white px-3 py-1 rounded-full text-xs font-semibold "
                          prefetch={false}
                        >
                          {item?.categoryId?.title}
                        </Link>
                        <Button
                          className={`w-7 h-7 p-0 min-w-0 text-center rounded-full flex items-center justify-center 
                          ${
                            item?.liked
                              ? "text-red-600"
                              : "bg-white text-black hover:text-red-600 "
                          }
                        `}
                          onClick={() => HandleLike(item?.liked, item?._id)}
                        >
                          <FaHeart className="text-base" />
                        </Button>
                      </div>
                    </div>
                    <div className="px-3 pt-3 flex-grow flex flex-col ">
                      <div className="mb-1.5 xl:flex items-center lg:block flex">
                        <p className="flex items-center font-medium text-black/50  text-xs mb-1 xl:mb-0">
                          {" "}
                          <span>
                            <CalendarDays size={13} />
                          </span>
                          <span className="ml-1 mr-4">
                            {new Date(item?.createdAt).toLocaleDateString(
                              "en-US",
                              {
                                year: "numeric",
                                month: "short",
                                day: "numeric",
                              }
                            )}
                          </span>
                        </p>
                        {/* <p className="flex items-center font-medium text-black/50  text-xs ">
                        {" "}
                        <span>
                          <Clock3 size={13} />
                        </span>
                        <span className="ml-1">6 mins</span>
                      </p> */}
                      </div>
                      <h3>
                        <Link
                          href={`/blog-details?id=${item?._id}`}
                          className="text-sm lg:text-xs xl:text-sm font-bold hover:text-primary-blue block"
                          prefetch={false}
                        >
                          {item?.title}
                        </Link>
                      </h3>
                      <div className="xl:flex lg:block flex items-center mt-4 lg:mt-3 2xl:mt-1">
                        {/* <div className="flex items-center mb-2 xl:mb-0">
                        <span>
                          <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/profile.svg`}
                            alt="Main Post"
                            className="w-[22px] h-[22px] object-cover rounded-full"
                            width={22}
                            height={22}
                            loading="lazy"
                          />
                        </span>
                        <span className="ml-2 text-gray-800 text-xs font-semibold">
                          Jimmy Dave
                        </span>
                      </div> */}
                        <Link
                          href={`/blog-details?id=${item?._id}`}
                          className="ml-auto bg-black text-white px-2 py-2 rounded-3xl text-[10px] font-semibold hover:bg-primary-blue transition-all text-center hover:text-black"
                          prefetch={false}
                        >
                          Keep Reading
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {totalPages > 1 && (
        <div className="flex justify-center mt-4">
          <CustomPagination
            currentPage={currentPage}
            total={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </>
  );
};

export default Blog;
