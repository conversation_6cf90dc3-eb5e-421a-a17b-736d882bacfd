 
import React from 'react';
import OTPInput from "otp-input-react";

export default function OtpForm({ otp, setOtp, loading, handleVerifyOtp, resendOTP }) {
  return (
    <form onSubmit={handleVerifyOtp}>
      <div className="mb-10">
        <OTPInput
          value={otp}
          onChange={(otp) => setOtp(otp)}
          autoFocus
          OTPLength={6}
          otpType="number"
          secure
          className="w-full text-xs input-otp font-light mb-5 py-2.5 mt-1 bg-transparent outline-none focus:outline-none placeholder:text-gray-50/40 text-gray-50"
        />
      </div>
      <div className="flex justify-center">
        <a href="#" className="mb-8 text-center text-sm text-[#50C2FF]" onClick={resendOTP}>
          Resend verification code
        </a>
      </div>
      <button
        type="submit"
        className="w-full mb-4 bg-[#40E0D0] font-semibold text-black py-4 rounded-2xl hover:bg-[#40E0D0] transition duration-200"
        disabled={loading}
      >
        {loading ? 'Verifying...' : 'Verify'}
      </button>
    </form>
  );
}
