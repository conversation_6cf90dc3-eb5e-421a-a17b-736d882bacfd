 
"use client";

import { FaInstagram } from "react-icons/fa6";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation } from "swiper/modules";
import { useRef, useState } from "react";
import { FiPlayCircle } from "react-icons/fi";
import { BsPauseCircle } from "react-icons/bs";

const baseUrl = process.env.NEXT_PUBLIC_S3_URL_FE;
const videos = [
  `${baseUrl}/front-images/video1.mp4`,
  `${baseUrl}/front-images/video2.mp4`,
  `${baseUrl}/front-images/video3.mp4`,
  `${baseUrl}/front-images/video4.mp4`,
  `${baseUrl}/front-images/video5.mp4`,
  `${baseUrl}/front-images/video6.mp4`,
];


export default function OurLensGallery() {
  const [videoLoadedFlags, setVideoLoadedFlags] = useState(
    Array(videos.length * 2).fill(false) // *2 because you're duplicating videos in the loop
  );

  const handleVideoLoad = (index) => {
    setVideoLoadedFlags((prev) => {
      const updated = [...prev];
      updated[index] = true;
      return updated;
    });
  };

  return (
    <div className="container py-8 xs:mb-8 mb-0 md:mb-12 ">
      <h2 className="font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl px-1 sm:mb-8 mb-3">
        See the World Through <span className="text-primary-blue">Our</span>{" "}
        <span className="text-primary-blue">Lens</span>
      </h2>

      <p
        className="hidden sm:block mt-2 text-base font-medium text-[#737373] font-manrope mb-3 w-[80%]">
        From sunrise dorm views to street food nights and hostel rooftop parties — this is what travel
        really looks like.
        Join the Mixdorm tribe of solo backpackers, budget travelers, and hostel explorers as we
        capture raw, real, and unforgettable moments from around the globe.
      </p>
      
      <div className="overflow-x-hidden hidden justify-center mt-0  md:mt-6 relative w-full min-h-96 items-center z-10">
        <Swiper
          slidesPerView={"auto"}
          spaceBetween={15}
          autoplay
          loop={true}
          modules={[Autoplay, Navigation]}
          // navigation={true}
          className="mySwiper myCustomSwiper overflow-hidden"
          breakpoints={{
            0: { slidesPerView: 1.5, spaceBetween: 20 },
            640: { slidesPerView: 2 },
            768: { slidesPerView: 3 },
            1024: { slidesPerView: 4 },
          }}
        >
          <div className="flex w-[140%] md:w-[120%] lg:w-[100%] justify-center gap-x-6">
            {videos.map((src, index) => {
              const videoRef = useRef(null);
              const [isPlaying, setIsPlaying] = useState(false);

              const handlePlayPause = () => {
                if (isPlaying) {
                  videoRef.current.pause(); // Pause the video if it's playing
                  setIsPlaying(false);
                } else {
                  videoRef.current.play(); // Play the video if it's paused
                  setIsPlaying(true);
                }
              };

              return (
                <SwiperSlide
                  key={index}
                  className="relative w-[316px] h-[340px] md:h-[360px]"
                >
                  <div className="relative h-[308px] w-full py-2">
                    <video
                      ref={videoRef}
                      src={src}
                      className="h-full w-full object-cover shadow-md"
                      muted
                      loop
                      playsInline
                      controls
                      preload="metadata"
                      crossOrigin="anonymous"
                    />
                    {!isPlaying && (
                      <button
                        onDoubleClick={handlePlayPause} // Double-click to stop
                        aria-label="Video Play"
                        onClick={handlePlayPause} // Single click to play
                        className="absolute inset-0 flex items-center justify-center  transition"
                      >
                        <FiPlayCircle className="text-white text-3xl" />
                      </button>
                    )}
                    {isPlaying && (
                      <button
                        onDoubleClick={handlePlayPause} // Double-click to stop
                        aria-label="Video Pause"
                        onClick={handlePlayPause} // Single click to pause
                        className="absolute inset-0 flex items-center justify-center transition"
                      >
                        <BsPauseCircle className="text-white text-3xl" />
                      </button>
                    )}
                  </div>

                  <div className="w-full text-base font-manrope absolute -bottom-1 md:bottom-3 right-2">
                    <div className="text-black text-right">
                      <span className="text-xl">
                        <FaInstagram className="absolute text-2xl md:text-3xl right-24 mr-3 mt-0" />
                        #Mixdorm
                      </span>
                    </div>
                  </div>
                </SwiperSlide>
              );
            })}
          </div>
        </Swiper>
      </div>

      <div className="relative w-full overflow-hidden xs:py-10 py-5">
        <div className="scroll-wrapper flex md:gap-3 gap-2">
          {[...videos, ...videos].map((src, index) => (
            <div
              key={index}
              className="xs:w-[250px] w-[210px] md:mx-2 mx-1 p-3 shadow-lg bg-white shadow-sky-200 border-2 -rotate-[10deg] rounded-lg relative"
            >
              <div className="relative xs:w-[220px] xs:h-[220px] w-[180px] h-[180px]">
                {!videoLoadedFlags[index] ? (
                  <div className="xs:w-[220px] xs:h-[220px] w-[180px] h-[180px] flex items-center justify-center bg-gray-300 animate-pulse rounded-lg">
                    <h1 className="text-white font-bold font-manrope">MixDorm</h1>  
                  </div>
                ) : null}

                <video
                  src={src}
                  className="xs:w-[220px] xs:h-[220px] w-[180px] h-[180px] object-cover shadow-md rounded-lg bg-gray-300"
                  muted
                  loop
                  playsInline
                  preload="metadata"
                  onLoadedData={() => handleVideoLoad(index)}
                  style={{
                    display: videoLoadedFlags[index] ? "block" : "none",
                  }}
                  onMouseEnter={(e) => e.currentTarget.play()}
                  onMouseLeave={(e) => {
                    e.currentTarget.pause();
                    e.currentTarget.currentTime = 0;
                  }}
                />
              </div>

              <div className="text-black text-base font-manrope w-full pt-3 flex items-center justify-end gap-2 text-right">
                <FaInstagram className="text-xl" />
                <span className="xs:text-xl text-md">#Mixdorm</span>
              </div>
            </div>
          ))}
        </div>
      </div>


    </div>
  );
}
