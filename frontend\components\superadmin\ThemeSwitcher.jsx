 
// "use client";
// import { Moon, Sun } from "lucide-react";
// import { useTheme } from "next-themes";
// import { useEffect, useState } from "react";
// import { BsMoonStars } from "react-icons/bs";
// import { IoSunnyOutline } from "react-icons/io5";

// const ThemeSwitcher = ({ isCollapsed }) => {
//   const { theme, setTheme } = useTheme();
//   const [mounted, setMounted] = useState(false);

//   useEffect(() => {
//     setMounted(true);
//   }, []);

//   if (!mounted) return null;

//   return (
//     <button
//       className={`flex items-center justify-center text-sm ${
//         isCollapsed ? "" : "gap-x-2"
//       } `}
//       onClick={() => setTheme(theme === "light" ? "dark" : "light")}
//     >
//       {theme === "light" ? <IoSunnyOutline  size={18} /> : <BsMoonStars  size={18} />}
//       {isCollapsed ? "" : " Theme"}
//     </button>
//   );
// };

// export default ThemeSwitcher;



"use client"; // Ensures it runs on client-side

import { useState, useEffect } from "react";
import { IoSunnyOutline } from "react-icons/io5";
import { BsMoonStars } from "react-icons/bs";

export default function ThemeSwitcher() {
  const [theme, setTheme] = useState("light");
  // eslint-disable-next-line no-unused-vars
  const [activeItem, setActiveItem] = useState("");

  useEffect(() => {
    // Get theme from localStorage or default to light
    const storedTheme = localStorage.getItem("theme") || "light";
    setTheme(storedTheme);
    document.documentElement.classList.toggle("dark", storedTheme === "dark");
  }, []);

  const toggleTheme = () => {
    const newTheme = theme === "light" ? "dark" : "light";
    setTheme(newTheme);
    document.documentElement.classList.toggle("dark", newTheme === "dark");
    localStorage.setItem("theme", newTheme);
  };

  return (
    <div className={`${
      activeItem === "Theme" ? "border-l-4 border-[#50C2FF] " : ""
    }`}>
      <button
      onClick={toggleTheme}
      className={`flex items-center justify-center text-sm font-poppins gap-x-2  cursor-pointer  ${
                    activeItem === "Theme" ? "bg-[#50C2FF] text-white" : ""
                  }`}
    >
      {theme === "light" ? <IoSunnyOutline size={18}/> : <BsMoonStars size={18}/>}
      Theme
    </button>
    </div>
  );
}



