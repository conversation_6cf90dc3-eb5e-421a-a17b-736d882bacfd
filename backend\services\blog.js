import blogModel from '../models/blog.js';
import mongoose from 'mongoose';

// Function to add a new blog
const addBlog = async (data) => {
    return await blogModel.create(data);
};

// Function to update a blog by ID
const updateBlogById = async (blogId, newData) => {
    try {
        const blog = await blogModel.findById({_id:blogId});
        if (!blog) {
            throw new Error('Blog Not Found');
        }

        const updatedBlog = await blogModel.findByIdAndUpdate({_id:blogId}, { ...newData }, { new: true });
        return updatedBlog;
    } catch (error) {
        console.error("Error updating blog:", error.message);
        throw error;
    }
};

// Function to get a blog by ID
const getBlogById = async (slug) => {
    try {
        const blog = await blogModel.findOne({slug:slug});
        if (!blog) {
            throw new Error('Blog Not Found');
        }
        return blog;
    } catch (error) {
        console.error("Error getting blog:", error.message);
        throw error;
    }
};

// Function to list all blogs
const listAllBlogs = async (filter = {}, page, limit) => {
    const skip = (page - 1) * limit;
    const query = { ...filter, isActive: true };

    const blogs = await blogModel.find(query).populate('categoryId')
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec();

    const totalBlogs = await blogModel.countDocuments(query);

    return { blogs, totalBlogs};
};

// Function to delete a blog by ID
const deleteBlogById = async (blogId) => {
    try {
        const blog = await blogModel.findById(blogId);
        if (!blog) {
            throw new Error('Blog Not Found');
        }
        await blogModel.findByIdAndUpdate(blogId, { isActive: false, isDeleted: true });
        return { message: 'Deleted Successfully' };
    } catch (error) {
        console.error("Error deleting blog:", error.message);
        throw error;
    }
};

export { addBlog, updateBlogById, getBlogById, listAllBlogs, deleteBlogById };