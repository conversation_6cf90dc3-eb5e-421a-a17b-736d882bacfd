import eventBookingModel from '../models/eventBooking.js';
import eventModel from '../models/events.js';
import mongoose from 'mongoose'
//import geocodeAddress from '../utils/geocode.js'; // Assuming you have a geocode utility to get location from address

// Function to add a new event
const addEvent = async (data) => {
    // if (data.address) {
    //     const location = await geocodeAddress(data.address);
    //     data.location = location;
    // }
    return await eventModel.create(data);
};

// Function to update an event by ID
const updateEventById = async (eventId, newData) => {
    try {
        const event = await eventModel.findById({ _id: eventId });
        if (!event) {
            throw new Error('Event Not Found');
        }

        // if (newData.address) {
        //     const location = await geocodeAddress(newData.address);
        //     newData.location = location;
        // }

        const updatedEvent = await eventModel.findByIdAndUpdate(eventId, { $set: newData }, { new: true });
        return updatedEvent;
    } catch (error) {
        console.error("Error updating event:", error.message);
        throw error;
    }
};

// Function to get an event by ID
const getEventById = async (eventId) => {
    try {
        const event = await eventModel.findById(eventId)
            .populate({
                path: 'createdBy',
                select: 'name' // Only select the 'name' field from the 'users' collection
            })
            .select('-propertyId -status -isActive -isDeleted -refundable -nonRefundable -cancellation'); // Exclude specified fields

        if (!event) {
            throw new Error('Event Not Found');
        }
        return event;
    } catch (error) {
        console.error("Error getting event:", error.message);
        throw error;
    }
};

// Function to list all events by property with pagination and filter
const listPropertyEvents = async (propertyId, filter, page, limit) => {
    const skip = (page - 1) * limit;
    const query = { propertyId, ...filter, isDeleted: false };
    const events = await eventModel.find(query).skip(skip).limit(limit).sort({ createdAt: -1 }).exec();
    const totalEvents = await eventModel.countDocuments(query);

    return { events, totalEvents, totalPages: Math.ceil(totalEvents / limit) };
};

const listAllEvents = async (filter, page, limit) => {
    const skip = (page - 1) * limit;
    const query = { ...filter, isDeleted: false };

    const events = await eventModel.aggregate([
        { $match: query },
        { $sort: { createdAt: -1 } },
        { $skip: skip },
        { $limit: limit },
        {
            $lookup: {
                from: 'eventbookings',
                let: { eventId: '$_id' },
                pipeline: [
                    { $match: { $expr: { $eq: ['$event', '$$eventId'] } } },
                    {
                        $lookup: {
                            from: 'users',
                            let: { userId: '$user' },
                            pipeline: [
                                { $match: { $expr: { $eq: ['$_id', '$$userId'] } } },
                                {
                                    $project: {
                                        _id: 1,
                                        name: 1,
                                        profileImage: 1
                                    }
                                }
                            ],
                            as: 'userDetails'
                        }
                    },
                    { $unwind: '$userDetails' },
                    {
                        $group: {
                            _id: '$userDetails._id',
                            user: { $first: '$userDetails' }
                        }
                    },
                    {
                        $project: {
                            _id: 0, // Optional: Exclude the grouped `_id` field
                            user: 1
                        }
                    }
                ],
                as: 'attendees'
            }
        },
        {
            $addFields: {
                attendeesCount: { $size: '$attendees' },
            }
        }
    ]).exec();

    const totalEvents = await eventModel.countDocuments(query);

    return {
        events,
        totalEvents,
        totalPages: Math.ceil(totalEvents / limit)
    };
};


// Function to soft delete an event by ID
const deleteEventById = async (eventId) => {
    try {
        const event = await eventModel.findById({ _id: eventId });
        if (!event) {
            throw new Error('Event Not Found');
        }
        await eventModel.findByIdAndUpdate(eventId, { $set: { isDeleted: true, isActive: false } });
        return { message: 'Deleted Successfully' };
    } catch (error) {
        console.error("Error deleting event:", error.message);
        throw error;
    }
};
export const viewMembers = async (userId, page, limit) => {
    const skip = (page - 1) * limit;

    try {
        const results = await eventBookingModel
            .find({ createdBy: userId })
            .sort({ _id: -1 })
            .skip(skip)
            .limit(limit)
            .populate({
                path: 'event',
                select: 'title startDate endDate hours minutes price currency address description status',
            })
            .populate({
                path: 'user',
                select: 'name',
            });

        return results;
    } catch (error) {
        console.error('Error retrieving event members:', error.message);
        throw error;
    }
};

 export { addEvent, updateEventById, getEventById, listPropertyEvents, listAllEvents, deleteEventById };