import axios from 'axios';
import qs from 'qs'; // For formatting data to x-www-form-urlencoded
import User from '../models/auth.js';
import moment from 'moment';
const now = moment();
import { create } from 'xmlbuilder2';
const token = "EQVWtsMYCBgOBbSgcuc0pvGHg7rQF41OaZhVFwHnoOZ9609kj2bLyNRoQL2wPWiKdfsmJ1igFlfE5tLqPhINgsQT+okMNaNq7PMzzxmfLqCF0fhGLgFNmrAmE0NNtmiW+svEBjMZZBwcpGZYADMc/G5+UfkKJ3XKRytCk+QUOM=";

export const cloudBedsBookingSend = async (bookingDetails) => {
  console.log("bookingDetails", bookingDetails)
  const user = await User.findOne({ _id: bookingDetails?.user })
  console.log("user", user)
  const OrderDate = now.format('YYYY-MM-DD');
  const OrderTime = now.format('HH:mm:ss');
  const bookingData = {
    "Customers": [
      {
        "CustomerFName": user?.name.first,
        "CustomerLName": user?.name?.last,
        "CustomerEmail": "<EMAIL>",
        "CustomerCountry": user?.country
      }
    ],
    "IsCancellation": 0,
    "IsModification": 0,
    "OrderDate": OrderDate,
    "OrderTime": OrderTime,
    "OrderId": bookingDetails?.otaId,
    "Rooms": [
      {
        "ChannelRoomType": "138120",
        "StartDate": "2025-05-25",
        "EndDate": "2025-05-25",
        "Units": 1,
        "Price": bookingDetails?.totalAmount,
        "Currency": bookingDetails?.currency,
        "RateId": "r5",
        "RateDesc": "Free Cancellation",
        "DayRates": [
          { "Date": "2025-05-25", "Currency": "USD", "Description": "Non-refundable", "Rate": 50.00 },
          { "Date": "2025-05-25", "Currency": "USD", "Description": "Non-refundable", "Rate": 50.00 }
        ],
        "Adults": 1
      }
    ],
    "TotalCurrency": bookingDetails?.currency,
    "TotalPrice": bookingDetails?.totalAmount,
    "Balance": bookingDetails?.totalAmount,
    "BalanceCurrency": "USD",
    "Deposit": 0.00,
    "DepositCurrency": bookingDetails?.totalAmount,
    "PaymentCollect": "Property",
    "OrderAdults": 1,
    "OrderChildren": 0,
    "OrderCustomers": 1
  };
  console.log("bookingData", JSON.stringify(bookingData, null, 2))
  const data = {
    mya_property_id: bookingDetails?.property?.cloudbedsId,
    shared_secret: "ee80693e243820af6255e69ce3d67f30e53f4e9fcb606591e66042b61555d630",
    ota_property_id: bookingDetails?.property?.otaId,
    booking_json: JSON.stringify(bookingData)
  };

  try {
    const response = await axios.post(
      'https://api.myallocator.com/callback/ota/mix/v202203/BookingCreate',
      qs.stringify(data), // Converts JSON to x-www-form-urlencoded
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': token
        }
      }
    );

    console.log("response.data", response.data);
    return response.data;
  } catch (error) {
    console.error('Error sending booking to Cloudbeds:', error.response?.data || error.message);
    throw error;
  }
};

export const sendToEzeeXmlBooking = async (booking) => {
  try {
    const {
      _id,
      checkInDate,
      checkOutDate,
      user,
      rooms = [],
      property,
      totalAmount = 0,
      totalPaid = 0,
      currency,
      noOfAdults = 1,
      noOfChildren = 0,
    } = booking;

    const checkIn = new Date(checkInDate);
    const checkOut = new Date(checkOutDate);
    const totalNights = Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24));

    // Create dynamic BookingTran array
    const bookingTrans = rooms.map((room, index) => {
      const roomRate = room.rate || totalAmount / rooms.length; // fallback rate
      const perNightRate = roomRate / totalNights;

      return {
        SubBookingId: `${index + 1}`,
        RateTypeID: room.rateTypeId || '800308',
        RateType: room.rateType || 'Standard',
        RoomTypeCode: room.roomTypeCode || '134266',
        RoomTypeName: room.name || 'Room',
        Start: checkInDate.split('T')[0],
        End: checkOutDate.split('T')[0],
        TotalRate: roomRate.toFixed(2),
        TotalDiscount: '0.00',
        TotalExtraCharge: '0.00',
        TotalTax: '0.00',
        TotalPayment: (totalPaid / rooms.length).toFixed(2),
        Salutation: user?.gender === 'Female' ? 'Ms' : 'Mr',
        FirstName: user?.firstName || 'Guest',
        LastName: user?.lastName || '',
        Gender: user?.gender || 'Other',
        Address: user?.address || '',
        City: property?.address?.city || '',
        State: property?.address?.state || '',
        Country: property?.address?.country || '',
        Zipcode: property?.address?.zipcode || '',
        Phone: user?.phone || '',
        Mobile: user?.mobile || '',
        Fax: '',
        Email: user?.email || '',
        TransportationMode: '',
        Vehicle: '',
        PickupDate: '',
        PickupTime: '',
        Comment: 'Reservation from Mixdorm',

        RentalInfo: Array.from({ length: totalNights }).map((_, i) => {
          const effectiveDate = new Date(checkIn);
          effectiveDate.setDate(effectiveDate.getDate() + i);
          return {
            EffectiveDate: effectiveDate.toISOString().split('T')[0],
            Adult: noOfAdults,
            Child: noOfChildren,
            Rent: perNightRate.toFixed(2),
            ExtraCharge: '0.00',
            Tax: '0.00',
            Discount: '0.00'
          };
        })
      };
    });

    // Final XML Object
    const xmlObj = {
      RES_Response: {
        Reservations: {
          Reservation: {
            HotelCode: property?.ezeeHotelCode || '', // dynamic hotel code
            BookingID: booking?.bookingId || '',
            Status: booking?.status || 'New',
            Source: booking?.source || 'Mixdorm',
            Code: booking?.ccCode || '',
            CCNo: booking?.ccNumber || '',
            CCType: booking?.ccType || '',
            CCExpiryDate: booking?.ccExpiry || '',
            CardHoldersName: booking?.cardHolderName || '',
            BookingTran: subBookings.map((subBooking) => ({
              SubBookingId: subBooking?.subBookingId || '',
              RateTypeID: subBooking?.rateTypeId || '',
              RateType: subBooking?.rateType || '',
              RoomTypeCode: subBooking?.roomTypeCode || '',
              RoomTypeName: subBooking?.roomTypeName || '',
              Start: subBooking?.checkInDate || '',
              End: subBooking?.checkOutDate || '',
              TotalRate: subBooking?.totalAmount || '0.00',
              TotalDiscount: subBooking?.totalDiscount || '0.00',
              TotalExtraCharge: subBooking?.totalExtraCharge || '0.00',
              TotalTax: subBooking?.totalTax || '0.00',
              TotalPayment: subBooking?.totalPaidAmount || '0.00',
              TACommision: subBooking?.taCommission || '0.00',
              Salutation: subBooking?.guestDetails?.salutation || '',
              FirstName: subBooking?.guestDetails?.firstName || '',
              LastName: subBooking?.guestDetails?.lastName || '',
              Gender: subBooking?.guestDetails?.gender || '',
              Address: subBooking?.guestDetails?.address || '',
              City: subBooking?.guestDetails?.city || '',
              State: subBooking?.guestDetails?.state || '',
              Country: subBooking?.guestDetails?.country || '',
              Zipcode: subBooking?.guestDetails?.zipcode || '',
              Phone: subBooking?.guestDetails?.phone || '',
              Mobile: subBooking?.guestDetails?.mobile || '',
              Fax: subBooking?.guestDetails?.fax || '',
              Email: subBooking?.guestDetails?.email || '',
              TransportationMode: subBooking?.transportationMode || '',
              Vehicle: subBooking?.vehicle || '',
              PickupDate: subBooking?.pickupDate || '',
              PickupTime: subBooking?.pickupTime || '',
              Comment: subBooking?.comment || '',
              RentalInfo: (subBooking?.rentalInfo || []).map((rent) => ({
                EffectiveDate: rent?.effectiveDate || '',
                Adult: rent?.adult || 0,
                Child: rent?.child || 0,
                Rent: rent?.rent || '0.00',
                ExtraCharge: rent?.extraCharge || '0.00',
                Tax: rent?.tax || '0.00',
                Discount: rent?.discount || '0.00'
              }))
            }))
          }
        }
      }
    };

    const xml = create({ version: '1.0', encoding: 'UTF-8' })
      .ele(xmlObj)
      .end({ prettyPrint: true });
    console.log("xml", xml)
    // const response = await axios.post(process.env.EZEE_BOOKING_URL, xml, {
    //   headers: { 'Content-Type': 'application/xml' }
    // });

    return response.data;
  } catch (error) {
    console.error('eZee XML Booking Error:', error.message);
    throw new Error('Failed to send booking to eZee');
  }
};
