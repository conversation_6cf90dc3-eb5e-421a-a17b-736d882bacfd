import React, { useEffect, useState } from "react";
import Image from "next/image";
import { IoSearchOutline } from "react-icons/io5";
import toast from "react-hot-toast";
import { channelManagersListApi } from "@/services/ownerflowServices";

const ChannelPartner = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedTerm, setSelectedTerm] = useState("");



  const fetchSuggestions = async () => {
    if (!searchTerm.trim()) {
      setSuggestions([]); 
      return;
    }

    setLoading(true);
    try {
      const response = await channelManagersListApi(searchTerm);

      if (response.status === 200) {
        setSuggestions(response?.data?.data || []); 
      } else {
        toast.error(response?.data?.message || "Failed to fetch suggestions");
      }
    } catch (error) {
      console.error("Error fetching suggestions:", error);
      toast.error("Something went wrong while fetching suggestions.");
    } finally {
      setLoading(false);
    }
  };

  // useEffect(() => {
  //   const debounceTimeout = setTimeout(() => {
  //     fetchSuggestions();
  //   }, 300); 

  //   return () => clearTimeout(debounceTimeout);
  // }, [searchTerm]);


  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      if (searchTerm !== selectedTerm) {
        fetchSuggestions();
      }
    }, 300);

    return () => clearTimeout(delayDebounceFn); // Cleanup timeout
  }, [searchTerm]);

  const handleSuggestionClick = (suggestion) => {
    console.log("Selected Suggestion:", suggestion);
    setSearchTerm(suggestion); 
    setSelectedTerm(suggestion);
    setSuggestions([]);
  };


  console.log("suggestions",suggestions)

  

  return (
    <div className="p-2 mx-auto bg-white md:p-5 mmd:p-6">
      <div className="flex flex-wrap items-center justify-between max-w-6xl mx-auto mb-4 gap-y-3">
        <div className="flex flex-wrap items-center gap-2">
          <h2 className="text-base font-medium">Channel Partner</h2>
          <div className="relative">
            <span className="absolute left-2 top-3">
              <IoSearchOutline />
            </span>
            <input
              type="text"
              placeholder="Search"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-3 pl-8 py-3 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
            />
            {searchTerm && suggestions.length > 0 &&(
              <div className="absolute left-0 right-0 z-10 mt-1 bg-white shadow-lg max-h-60 overflow-auto">
                {loading ? (
                  <div className="p-2 text-gray-500">Loading...</div>
                ) : suggestions.length > 0 ? (
                  suggestions.map((suggestion, index) => (
                    <div
                      key={index}
                      onClick={() => handleSuggestionClick(suggestion?.name)}
                      className="p-2 hover:bg-gray-100 cursor-pointer text-[#858D9D]"
                    >
                      {suggestion?.name}
                    </div>
                  ))
                ) : (
                  <div className="p-2 text-gray-500">No suggestions found</div>
                )}
              </div>
            )}
          </div>
        </div>
        <div className="flex flex-wrap items-center gap-y-2">
          <button className="bg-[#1570EF] text-white px-4 py-2 rounded-lg md:mr-2 text-sm">
            Channel Partner List
          </button>
          <button className="bg-[#1570EF] text-white px-4 py-2 rounded-lg text-sm">
            Add Channel Partners
          </button>
        </div>
      </div>

      <div className="flex items-center justify-center min-h-[80vh]">
        <div className="text-center">
          <h2 className="text-base font-bold mmd:text-xl">
            200+ Channel Partners
          </h2>
          <p className="text-base font-bold mmd:text-xl">
            List of channels that are currently integrated to MixDorm API.
          </p>
          <div className="flex justify-center">
            <button className="bg-[#1570EF] text-white px-4 py-2 rounded-lg mt-6 flex items-center justify-center space-x-2">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/help.svg`}
                width={19}
                height={19}
                className="w-[19px] h-[19px]"
                alt="Help Icon"
                loading="lazy"
              />
              <span>Help</span>
            </button>
          </div>
        </div>
      </div>


    </div>
  );
};

export default ChannelPartner;
