import React, { useState } from "react";
import dynamic from 'next/dynamic';
const OTPInput = dynamic(() => import("otp-input-react"), { ssr: false });
import toast from "react-hot-toast";
import { resendOtpApi, verifyOtp } from "@/services/webflowServices";
import { useRouter } from "next/router";
import { setItemLocalStorage,setToken } from "@/utils/browserSetting";
import { requestForFCMToken } from "@/utils/firebaseConfig";
import { saveFirebaseToken } from "@/services/ownerflowServices";
import { useNavbar } from "@/components/home/<USER>";
import Link from "next/link";

const VerifyForm = () => {
  const [email, setEmail] = useState("");
  const [otp, setOtp] = useState("");
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { updateUserStatus,updateUserRole } = useNavbar();


  const handleVerifyOtp = async (e) => {
    e.preventDefault();

    if (!email) {
      toast.error("Please enter your email.");
      return;
    }

    if (otp.length !== 6) {
      toast.error("Please enter a 6-digit OTP.");
      return;
    }

    setLoading(true);

    try {
      const response = await verifyOtp({ email, otp });

      if (response?.data?.status) {
        setItemLocalStorage("name", response?.data?.data?.user?.name.first);
        setItemLocalStorage("id", response?.data?.data?.user?._id);
        setItemLocalStorage("uid", response?.data?.data?.user?._id);
        setItemLocalStorage("role", response?.data?.data?.user?.role);
        updateUserStatus(response?.data?.data?.token);
        updateUserRole(response?.data?.data?.user?.role);
        setToken(response?.data?.data?.token);
        toast.success(response?.data?.message || "OTP verified successfully!");
        const fcmToken = await requestForFCMToken();
        if (fcmToken) {
          setItemLocalStorage("FCT", fcmToken);
          await saveFirebaseToken({
            token: fcmToken,
            userId: response?.data?.data?.user?._id, 
          });
        }
        router.push("/owner/list");
      } else {
        toast.error(response.data.message || "OTP verification failed.");
        setOtp("");
      }
    } catch (error) {
      toast.error(
        error.response?.data?.message ||
          "An error occurred during verification."
      );
      console.error("Verification error:", error);
      setOtp("");
    } finally {
      setLoading(false);
    }
  };

  const resendOTP = async () => {
    if (!email) {
      toast.error("Please enter your email.");
      return;
    }

    try {
      const response = await resendOtpApi({ email });

      if (response?.data?.status) {
        toast.success(`We’ve Resent a verification code to ${email}. Please enter this code to continue.`);
        setOtp("");
      } else {
        toast.error(
          response.data.message || "Failed to resend verification code."
        );
        setOtp("");
      }
    } catch (error) {
      toast.error(
        error.response?.data?.message ||
          "An error occurred while resending OTP."
      );
      console.error("Resend OTP error:", error);
      setOtp("");
    }
  };

  return (
    <div className='min-h-screen flex items-center justify-center bg-[#F7F7F7] w-full pb-[10rem] pt-[3rem]'>
      <div className='w-full max-w-3xl pb-6 bg-white shadow-md px-14 pt-14 rounded-3xl md:max-w-2xl'>
        <form onSubmit={handleVerifyOtp}>
        <h2 className="mb-10 sm:text-[27px] text-lg font-bold text-gray-800">
          👋 Welcome To <span className="text-primary-blue">Mix</span>Dorm
        </h2>
        <h4 className="sm:text-base text-sm text-black my-5">
          {" "}
          Already have an account?{" "}
          <Link
            href="/owner/login"
            className="text-primary-blue font-medium ml-1 cursor-pointer uppercase underline underline-offset-2"
            prefetch={false}
          >
            Sign In
          </Link>{" "}
        </h4>
            <h4 className='mb-6 text-[15px] font-medium text-[#010101] font-poppins'>Enter Authentication Code</h4>
            <h4 className='mb-6 font-normal text-sm text-[#2B303466]'>Two-Factor Authentication (2FA)</h4>
          <div className='mb-6'>
            <label className='block text-black sm:text-base text-sm font-semibold mb-1.5'>
              Email
            </label>
            <input
              type='email'
              id='email'
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className='w-full px-4 py-4 mt-2 border rounded-4xl focus:outline-none focus:ring-1 focus:ring-teal-500'
              placeholder='Enter your email'
              required
            />
          </div>
         
          <div>
           <label
              htmlFor='email'
              className='block text-xs mb-6'
            >
              Enter Code
            </label>
          </div>
          <div className='mb-10'>
            <OTPInput
              value={otp}
              onChange={(otp) => setOtp(otp)}
              autoFocus
              OTPLength={6}
              otpType='number'
              // secure
              className='w-full text-xs input-otp mb-5 py-2.5 mt-1 bg-transparent outline-none focus:outline-none text-[#010101] font-semibold'
               inputClassName="w-10 h-10 mr-2 border border-[#e5e7eb] focus:outline-none focus:ring-1 focus:ring-teal-500 text-[#010101] font-semibold text-sm placeholder:text-gray-500 bg-white"
            />
          </div>
          <div className='flex justify-center'>
            <a
              className='mb-8 text-center text-xs text-[#50C2FF] cursor-pointer underline underline-offset-2'
              onClick={resendOTP}
            >
              Resend verification code
            </a>
          </div>
          <button
            type='submit'
            className='w-full mb-4 bg-[#40E0D0] font-bold text-black py-4 rounded-full hover:bg-[#40E0D0] transition duration-200'
            disabled={loading}
          >
            {loading ? "Verifying..." : "Verify, Lets Go !"}
          </button>
        </form>
      </div>
    </div>
  );
};

export default VerifyForm;
