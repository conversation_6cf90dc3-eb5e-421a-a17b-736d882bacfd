import React, { useEffect, useRef, useState } from "react";
import Link from "next/link";
// import { ChevronDown, ChevronUp } from "lucide-react";
// import toast from "react-hot-toast";
import // getItemLocalStorage,
// removeItemLocalStorage,
"@/utils/browserSetting";
import ContactPopup from "./contactPopup";
// import { getPropertyCountApi } from "@/services/webflowServices";
import { useRouter } from "next/router";
// import { removeFirebaseToken } from "@/services/ownerflowServices";
// import { useNavbar } from "../home/<USER>";
import {
  
  FaRegUser,
} from "react-icons/fa";
import { MdCardMembership, MdLogout, MdOutlineTravelExplore } from "react-icons/md";
import { GrEdit } from "react-icons/gr";
import { IoWalletOutline } from "react-icons/io5";
import { HiOutlineQuestionMarkCircle } from "react-icons/hi";
import { getItemLocalStorage, removeItemLocalStorage } from "@/utils/browserSetting";
import { useNavbar } from "../home/<USER>";
import { removeFirebaseToken } from "@/services/ownerflowServices";
import toast from "react-hot-toast";
import { GoHeart } from "react-icons/go";

const MenuPopup = ({
  isOpen,
  toggleMenu,
  // updateTokenState,
  // toggleLoginPopup,
  // updateRoleState,
}) => {
  // State to manage the open/close state of each section
  // const { updateUserStatus,updateUserRole } = useNavbar();

  // const [openSections, setOpenSections] = useState({
  //   services: false,
  //   company: false,
  //   help: false,
  //   account: false,
  // });
  // const [hasToken, setHasToken] = useState(false);
  const [isContactPopupOpen, setIsContactPopupOpen] = useState(false);
  // const [role, setRole] = useState(false);
  const modalRef = useRef(null);
  const router = useRouter();

  const { updateUserStatus, updateUserRole } = useNavbar();
  
   const handleLogout = async () => {
      removeItemLocalStorage("token");
      updateUserStatus("");
      updateUserRole("");
      removeItemLocalStorage("name");
      removeItemLocalStorage("role");
      removeItemLocalStorage("email");
      removeItemLocalStorage("contact");
      toggleMenu();

      toast.success("Logged out successfully");
      const payload = {
        token: getItemLocalStorage("FCT"),
        userId: getItemLocalStorage("id"),
      };
      try {
        await removeFirebaseToken(payload);
        console.log("FCM token removed successfully.");
        removeItemLocalStorage("FCT");
      } catch (error) {
        console.error("Error removing FCM token:", error);
      }
      removeItemLocalStorage("id");
      router.push("/");
    };

  const menuItems = [
    { icon: <FaRegUser className="text-xl" />, label: "My Profile", href: "/my-profile?section=profile" },
    { icon: <GoHeart className="text-xl"/>, label: "Wishlist", href: "/my-profile?section=wishlist" },
    { icon: <GrEdit className="text-xl"/>, label: "Edit Details", href: "/my-profile?section=edit" },
    { icon: <MdCardMembership className="text-xl"/>, label: "Membership", href: "/my-profile?section=membership" },
    { icon: <MdOutlineTravelExplore className="text-xl"/>, label: "My Trips", href: "/my-profile?section=stay" },
    { icon: <IoWalletOutline className="text-xl"/>, label: "My Wallet", href: "/my-profile?section=wallet" },
    { icon: <HiOutlineQuestionMarkCircle className="text-xl"/>, label: "Help", href: "/my-profile?section=help" },
  ];
  // Toggle function for each section
  // const toggleSection = (section) => {
  //   setOpenSections((prevState) => ({
  //     ...prevState,
  //     [section]: !prevState[section],
  //   }));
  // };

  // useEffect(() => {
  //   const token = getItemLocalStorage("token");
  //   setHasToken(!!token);
  // }, []);

  // useEffect(() => {
  //   const role = getItemLocalStorage("role");
  //   setRole(role);
  // }, []);

  // const handleLogout = async () => {
  //   removeItemLocalStorage("token");
  //   removeItemLocalStorage("name");
  //   removeItemLocalStorage("id");
  //   removeItemLocalStorage("role");
  //   toggleMenu();
  //   updateTokenState();
  //   updateRoleState();
  //   updateUserStatus("")
  //     updateUserRole("")
  //     toast.success("Logged out successfully");
  //     const payload = {
  //       token: getItemLocalStorage("FCT"),
  //       userId: getItemLocalStorage("id"),
  //     };
  //     try {
  //       await removeFirebaseToken(payload);
  //       console.log("FCM token removed successfully.");
  //       removeItemLocalStorage("FCT");
  //     } catch (error) {
  //       console.error("Error removing FCM token:", error);
  //     }
  //     removeItemLocalStorage("id")
  //     router.push("/");
  // };

  // const openContactPopup = async () => {
  //   setIsContactPopupOpen(true);
  // };

  const closeContactPopup = () => {
    setIsContactPopupOpen(false);
  };

  // const handleLoginClick = () => {
  //   toggleLoginPopup();
  //   toggleMenu();
  // };

  // Close modal when clicking outside
  useEffect(() => {
    const handleOutsideClick = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        toggleMenu();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleOutsideClick);
    }

    return () => {
      document.removeEventListener("mousedown", handleOutsideClick);
    };
  }, [isOpen, toggleMenu]);

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed top-0 bottom-0 left-0 right-0 z-50 flex items-start justify-end bg-black bg-opacity-[70%]">
        <div
          ref={modalRef}
          className="bg-white rounded-2xl sm:w-[70%] w-[250px] max-w-xs sm:p-6 p-4 mx-10 mt-24 font-manrope"
        >
          <div className="flex items-center justify-between sm:mb-6 mb-4">
            <span className="text-[#40E0D0] flex text-2xl font-extrabold">
              Mix<p className="text-black text-2xl font-extrabold ">Dorm</p>
            </span>
            <button onClick={toggleMenu} className="text-black">
              ✕
            </button>
          </div>
          {menuItems.map((item, idx) => (
            <Link
              key={idx}
              href={item.href}
              className="flex items-center sm:gap-3 gap-2 bg-[#D9F9F6] hover:bg-primary-blue transition-colors sm:p-3 p-2.5 rounded-full text-sm font-medium text-gray-800 sm:mt-2 mt-1"
            >
              <span className="sm:text-lg text-base">{item.icon}</span>
              {item.label}
            </Link>
          ))}
          <button
            onClick={handleLogout}
            className="flex items-center sm:gap-3 gap-2 bg-[#D9F9F6] hover:bg-primary-blue transition-colors p-3 rounded-full text-sm font-medium text-gray-800 sm:mt-2 mt-1 w-full"
          >
            <span className="sm:text-lg text-base"><MdLogout className="text-xl"/></span>
            Logout
          </button>

          {/* <ul className='overflow-y-auto max-h-96 fancy_y_scroll pr-0'>
            
            <li className=''>
              <div
                className={`flex justify-between items-center mb-5 cursor-pointer border border-solid border-[#EEEEEE]  py-3 px-4 rounded-full ${
                  openSections.services &&
                  "bg-[#D9F9F6] text-black !border-[#D9F9F6]"
                }`}
                onClick={() => toggleSection("services")}
              >
                <div className='text-base font-[600] text-black'>Services</div>
                {openSections.services ? <ChevronUp /> : <ChevronDown />}
              </div>
              {openSections.services && (
                <ul className='text-base font-medium mt-[-20px] mb-3'>
                  <li>
                    <button
                      onClick={async () => {
                        if (getItemLocalStorage("token") && role === "user") {
                          const propertyCountResponse = await getPropertyCountApi();
                          if (propertyCountResponse?.data?.data?.totalBooking > 0) {
                            router.push('/noticeboard-detail');
                          } else if (propertyCountResponse?.data?.data?.totalBooking === 0) {
                            router.push('/noticeboard-detail');
                          }
                        } else {
                          toast.error("Please Login !", {
                            subText: "You need to be Logged In",
                          });
                        }
                      }}
                      className='text-[16px] font-[600] block w-full text-left text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
                    >
                      Noticeboard
                    </button>
                  </li>
                  <li>
                    <Link
                      href='/meetbuddies'
                      className='text-[16px] font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
                      prefetch={false}
                    >
                      Mix Ride
                    </Link>
                  </li>
                  <li>
                    <Link
                      href='/meetbuddies'
                      className='text-[16px] font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
                      prefetch={false}
                    >
                      Mix Creators
                    </Link>
                  </li>
                  <li>
                    <Link
                      href='/meetbuddies'
                      className='text-[16px] font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
                      prefetch={false}
                    >
                      Mix Mate
                    </Link>
                  </li>
                  <li>
                    <Link
                      href='/discover-event'
                      className='text-[16px] font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
                      prefetch={false}
                    >
                      Events
                    </Link>
                  </li>
                </ul>
              )}
            </li>
            
            <li>
              <div
                className={`flex justify-between items-center mb-5 cursor-pointer border border-solid border-[#EEEEEE]  py-3 px-4 rounded-full ${
                  openSections.company &&
                  "bg-[#D9F9F6] !mb-0 text-black !border-[#D9F9F6]"
                }`}
                onClick={() => toggleSection("company")}
              >
                <div className='text-base font-[600] text-black'>Company</div>
                {openSections.company ? <ChevronUp /> : <ChevronDown />}
              </div>
              {openSections.company && (
                <ul className='text-base font-medium mt-[-20px] mb-3'>
                  <li>
                    <Link
                      className='text-[16px] font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
                      href='/aboutus'
                      prefetch={false}
                    >
                      About Us
                    </Link>
                  </li>
                  <li>
                    <Link
                      className='text-[16px] font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
                      href='/company/rewards'
                      prefetch={false}
                    >
                      Rewards
                    </Link>
                  </li>
                  <li>
                    <Link
                      className='text-[16px] font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
                      href='blog'
                      prefetch={false}
                    >
                      Blogs
                    </Link>
                  </li>
                  <li>
                    <button
                      onClick={openContactPopup}
                      className='text-[16px] font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
                    >
                      Contact Us
                    </button>
                  </li>
                </ul>
              )}
            </li>
      
            <li>
              <div
                className={`flex justify-between items-center mb-6 cursor-pointer border border-solid border-[#EEEEEE]  py-3 px-4 rounded-full ${
                  openSections.help &&
                  "bg-[#D9F9F6] text-black !border-[#D9F9F6]"
                }`}
                onClick={() => toggleSection("help")}
              >
                <div className='text-base font-[600] text-black'>Help</div>
                {openSections.help ? <ChevronUp /> : <ChevronDown />}
              </div>
              {openSections.help && (
                <ul className='text-base font-medium mt-[-20px] mb-3'>
                  <li>
                    <Link
                      className='text-[16px] font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
                      href='faqs'
                      prefetch={false}
                    >
                      FAQs
                    </Link>
                  </li>
                  <li>
                    <Link
                      className='text-[16px] font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
                      href='privacypolicy'
                      prefetch={false}
                    >
                      Privacy Policy
                    </Link>
                  </li>
                  <li>
                    <Link
                      className='text-[16px] font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
                      href='terms-condition'
                      prefetch={false}
                    >
                      Terms and Conditions
                    </Link>
                  </li>
                </ul>
              )}
            </li>
         
            <li>
              <div
                className={`flex justify-between items-center mb-5 cursor-pointer border border-solid border-[#EEEEEE]  py-3 px-4 rounded-full ${
                  openSections.account &&
                  "bg-[#D9F9F6] text-black !border-[#D9F9F6]"
                }`}
                onClick={() => toggleSection("account")}
              >
                <div className='text-base font-[600] text-black'>
                  My Account
                </div>
                {openSections.account ? <ChevronUp /> : <ChevronDown />}
              </div>
              {openSections.account &&
                (!hasToken || role !== "user" ? (
                  <ul className='text-base font-medium mt-[-20px] mb-3'>
                    <li>
                      <button
                        className='text-[16px] font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
                        onClick={handleLoginClick}
                      >
                        Login
                      </button>
                    </li>
                    <li>
                      <button
                        onClick={handleLoginClick}
                        className='text-[16px] font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
                      >
                        Signup
                      </button>
                    </li>
                  </ul>
                ) : (
                  <ul className='text-base font-medium mt-[-20px] mb-3'>
                    <li>
                      <Link
                        className='text-[16px] font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
                        href={
                          role === "user"
                            ? "/my-profile"
                            : "/owner/dashboard/profile"
                        }
                        prefetch={false}
                      >
                        Profile
                      </Link>
                    </li>
                    <li>
                      <button
                        onClick={handleLogout}
                        className='text-[16px] font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
                      >
                        Logout
                      </button>
                    </li>
                  </ul>
                ))}
            </li>
          </ul> */}
        </div>
      </div>
      {isContactPopupOpen && (
        <ContactPopup isOpen={isContactPopupOpen} onClose={closeContactPopup} />
      )}
    </>
  );
};

export default MenuPopup;
