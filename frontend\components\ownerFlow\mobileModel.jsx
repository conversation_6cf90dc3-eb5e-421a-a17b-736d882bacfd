import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/router";
import Link from "next/link";
import { Box } from "@mui/material";
import Image from "next/image";
import Menu from "./menu";
import {
  getItemLocalStorage,
  removeItemLocalStorage,
} from "@/utils/browserSetting";
import {  removeFirebaseToken } from "@/services/ownerflowServices";
import { useHeaderOwner } from "./headerContex";
import { useNavbar } from "../home/<USER>";
import countries from "world-countries";
import Loader from "../loader/loader";

const MobileModal = ({ collapsed }) => {
  // eslint-disable-next-line no-unused-vars
  const [isMobileMenuOpen, setMobileMenuOpen] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [flags, setFlags] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [uploading, setUploading] = useState(false);
  // const pathname = usePathname();
  const router = useRouter();
  const { updateUserStatus, updateUserRole, updateHopId,updateCountryOwner } = useNavbar();
  // eslint-disable-next-line no-unused-vars
  const { profileData, propertyData } = useHeaderOwner();
  // const fileInputRef = useRef(null);
  

  // const [activeIndex, setActiveIndex] = useState(Menu.findIndex((item) => pathname === item.link));

  // Close the menu when the route changes
  useEffect(() => {
    const handleRouteChange = () => {
      setMobileMenuOpen(false);
    };

    router.events.on("routeChangeStart", handleRouteChange);

    // Cleanup the event listener on component unmount
    return () => {
      router.events.off("routeChangeStart", handleRouteChange);
    };
  }, [router.events]);

  // const handleFileChange = async (e) => {
  //   const selectedFile = e.target.files[0];
  //   if (selectedFile) {
  //     setUploading(true);
  //     // setErrors({ ...errors, file: null });

  //     try {
  //       const formData = new FormData();
  //       formData.append("file", selectedFile);

  //       const presignedUrlResponse = await fetch(
  //         `${BASE_URL}/fileUpload/generate-presigned-url`,
  //         {
  //           method: "POST",
  //           body: formData,
  //         }
  //       );

  //       if (!presignedUrlResponse.ok) {
  //         throw new Error("Failed to get presigned URL");
  //       }

  //       const presignedUrlData = await presignedUrlResponse.json();
  //       const { objectURL } = presignedUrlData.data;

  //       if (presignedUrlData?.status) {
  //         const response = await editProfileApi({
  //           profileImage: {
  //             objectURL: objectURL,
  //           },
  //         });
  //         if (response?.data?.status) {
  //           toast.success(
  //             response?.data?.message || "Profile updated successfully!"
  //           );

  //           try {
  //             const response = await getProfileApi();

  //             if (response?.status === 200) {
  //               updateuserData(response?.data?.data);
  //             }
  //           } catch (error) {
  //             console.error("Error fetching profile:", error.message);
  //           }
  //         }
  //       }

  //       toast.success("Profile picture uploaded successfully!");
  //     } catch (error) {
  //       console.error("Error uploading profile picture", error);
  //       toast.error("Error uploading profile picture");
       
  //     } finally {
  //       setUploading(false);
  //     }
  //   } else {
  //     toast.error("Error uploading profile picture");
  //   }
  // };

  useEffect(() => {
    const fetchFlags = () => {
      try {
        const filteredFlags = countries
          .filter((country) =>
            propertyData?.address?.country?.includes(country.name.common)
          )
          .map((country) => ({
            id: country.cca3,
            img:
              // eslint-disable-next-line no-constant-binary-expression
              `https://flagcdn.com/w320/${country.cca2.toLowerCase()}.png` ||
              "https://via.placeholder.com/30x25",
            name: country.name.common,
          }));
        setFlags(filteredFlags);
      } catch (error) {
        console.error("Error processing flags:", error);
      }
    };

    if (propertyData?.address?.country) fetchFlags();
  }, [propertyData?.address?.country]);

  const handleLogout = async () => {
    removeItemLocalStorage("token");
    removeItemLocalStorage("name");
    removeItemLocalStorage("id");
    removeItemLocalStorage("role");
    removeItemLocalStorage("hopid");
    removeItemLocalStorage("selectedOwnerCountry");
    removeItemLocalStorage("selectedCurrencyCodeOwner");
    removeItemLocalStorage("email");
    removeItemLocalStorage("contact");
    updateUserStatus("");
    updateUserRole("");
    updateHopId("");
    updateCountryOwner(null);

    const payload = {
      token: getItemLocalStorage("FCT"),
      userId: getItemLocalStorage("uid"),
    };

    try {
      await removeFirebaseToken(payload);
      removeItemLocalStorage("FCT");
    } catch (error) {
      console.error("Error removing FCM token:", error);
    }
    removeItemLocalStorage("uid");
    router.push("/owner/login");
  };

  const { pathname } = useRouter();
  // eslint-disable-next-line no-unused-vars
  const [currentIndex, setCurrentIndex] = useState(0);
  const [activeIndex, setActiveIndex] = useState(0);
  const [menuPositions, setMenuPositions] = useState([]);
  const contentRef = useRef(null);
  // eslint-disable-next-line no-unused-vars
  const [thumbTop, setThumbTop] = useState(0);

  const handleScroll = () => {
    if (!contentRef.current) return;

    const content = contentRef.current;
    const scrollRatio =
      content.scrollTop / (content.scrollHeight - content.clientHeight);
    const thumbPosition = scrollRatio * (content.clientHeight - 40); // 40 is the thumb height
    setThumbTop(thumbPosition);
  };

  useEffect(() => {
    // Attach scroll event listener
    const content = contentRef.current;
    if (content) content.addEventListener("scroll", handleScroll);

    // Cleanup
    return () => {
      if (content) content.removeEventListener("scroll", handleScroll);
    };
  }, []);

  useEffect(() => {
    if (contentRef.current) {
      // Calculate positions of all menu items
      const items = contentRef.current.querySelectorAll("li");
      const positions = Array.from(items).map((item) => item.offsetTop);
      setMenuPositions(positions);

      // Update the current active index
      const activeIdx = Menu.findIndex((item) => pathname === item.link);
      if (activeIdx !== -1) {
        setActiveIndex(activeIdx);
        setCurrentIndex(activeIdx); // Directly set the index
      }
    }
  }, [pathname, Menu]);

  // Animation logic for sliding indicator
  useEffect(() => {
    if (menuPositions.length > 0) {
      const position = menuPositions[activeIndex] || 0;
      setCurrentIndex(position);
    }
  }, [activeIndex, menuPositions]);

  return (
    <>
      <Loader open={uploading} />
      {/* Mobile Modal */}
      <Box
        ref={contentRef}
        className={`sticky-sidebar md:fixed top-0 left-0 h-full bg-white z-40 md:block hidden mobilemenubox transition-all duration-300  ${
          collapsed ? "w-[80px]" : "md:w-[250px] w-[180px]"
        }`}
        style={{
          overflowY: "scroll",
          position: "relative",
        }}
      >
        <div
          className={` px-5 pt-4 pb-6 text-center bg-sky relative ${
            pathname === Menu[0]?.link ? "border-right-bottom" : ""
          }`}
        >
          {/* Profile and Logo */}
          {/* {profileData?.profileImage?.objectURL ? ( */}
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mixowner.png`}
              alt='Profile Pic'
              className='w-[40px] h-[40px] mx-auto rounded-lg'
              width={40}
              height={40}
              loading='lazy'
            />
          {/* ) : (
            <CircleUser className='w-10 h-10 mx-auto text-black' size={40} />
          )} */}
          {/* <div
            className='absolute flex items-center justify-center w-5 h-5 rounded-full sm:w-8 sm:h-8 -bottom-0 -right-0 bg-primary-blue cursor-pointer z-10'
            onClick={() => fileInputRef.current.click()}
          >
            <Camera size={15} className='w-2 text-white sm:w-5' />
          </div>
          <input
            type='file'
            ref={fileInputRef}
            style={{ display: "none" }}
            onChange={handleFileChange}
            accept='image/*'
          /> */}

          <Image
            className={`mt-3 mx-auto sm:w-[129px] w-[110px] ${
              collapsed ? "hidden" : "block"
            } `}
            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/logoWhite.svg`}
            width={129}
            height={39}
            alt='logo'
            loading='lazy'
          />
        </div>

        <div className='relative'>
          {/* Sliding Background - placed once outside of the loop */}

          {/* <motion.div
              className="absolute left-3 bg-white rounded-r-md rounded-l-full w-full"
              animate={{
                top: menuPositions[activeIndex] || 0,
                opacity: (latestY) =>
                  Math.abs(latestY - (menuPositions[activeIndex] || 0)) < 5 ? 0 : 1,
              }}
              transition={{
                type: "spring",
                stiffness: 120,
                damping: 20,
              }}
              style={{
                height: "52px",
                zIndex: 20, // Always below menu items
              }}
            /> */}
          <ul
            className='flex flex-col relative'
            ref={contentRef}
            style={{
              zIndex: 19, // Always below menu items
            }}
          >
            {Menu.map((item, id) => {
              const isActive = activeIndex === id;
              const isBelowActive = id > 0 && pathname === Menu[id - 1].link;
              const isAboveActive =
                id < Menu.length - 1 && pathname === Menu[id + 1].link;

              return (
                <li
                  key={id}
                  className={`${isActive ? "active bg-sky" : ""}`}
                  // style={{ zIndex: 21 }}
                  // onClick={() => setActiveIndex(id)}
                >
                  <Link
                    href={item.link}
                    className={`relative flex items-center w-full sm:text-sm text-xs sm:py-4 py-2.5 transition-all text-black ${
                      isActive
                        ? "font-bold bg-white ml-3 sm:pl-5 pl-3 rounded-s-full"
                        : isAboveActive
                        ? "font-normal bg-sky sm:pl-8 pl-6 border-right-bottom"
                        : isBelowActive
                        ? "font-normal bg-sky sm:pl-8 pl-6 border-right-top"
                        : "font-normal bg-sky sm:pl-8 pl-6"
                    }`}
                    style={{ zIndex: 22 }}
                    prefetch={false}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <div
                      className='relative flex items-center gap-x-2'
                      style={{ zIndex: 23 }}
                    >
                      {item.icon && (
                        <Image
                          src={item.icon}
                          alt={`${item.name} Icon`}
                          width={20}
                          height={20}
                          className='max-h-6 max-w-6'
                          loading='lazy'
                        />
                      )}
                      {!collapsed && (
                        <span className='whitespace-nowrap'>{item.name}</span>
                      )}
                      {/* <span className="whitespace-nowrap">{item.name}</span> */}
                    </div>
                  </Link>
                </li>
              );
            })}
          </ul>
        </div>

        <ul>
          <li className='bg-black' onClick={handleLogout}>
            <Link
              href=''
              className='flex items-center justify-start gap-x-3 w-full text-white sm:text-sm text-xs sm:py-4 py-2.5 font-normal bg-black sm:pl-8 pl-6'
            >
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/logout.svg`}
                alt='Logout Icon'
                width={20}
                height={20}
                className='max-h-6 max-w-6'
                loading='lazy'
              />
              {!collapsed && <span className='whitespace-nowrap'>Logout</span>}
            </Link>
          </li>
        </ul>
      </Box>
    </>
  );
};

export default MobileModal;
