import { initializeApp, cert } from 'firebase-admin/app';
import { getMessaging } from 'firebase-admin/messaging';
import Notification from "../models/notifications.js"
import mongoose from "mongoose"
import userModel from '../models/auth.js'
// import Notification from '../models/n'
initializeApp({
  credential: cert(********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  ),
});
// const sendPushNotification = async (userIds, propertyId) => {
//   try {
//     const token = "fvCs6cyi3k_HhMgxGlKApY:APA91bFvKcsUhZeTFnlY6gMS9GxIab41a8K322gVWHzmXjuq3ClihKSzPiTEFpQuEFPPUFV6A6oE4C1d_c1WzFzNKodDV-BGKA0IWfD3fSueID4cwnrOZspnrlmWwvnsFXbb5au1aZ_p"
//     const message = {
//       notification: {
//         title: "Test",
//         body: "Property Added",
//       },
//       data: {
//         dynamicData: "datavalue",
//       },
//     };
//     const response = await getMessaging().send({ ...message, token });
//     console.log('Successfully sent message to token:', token, response);
//     // await Notification.create({
//     //   userId: user._id,
//     //   title: template.notificationTitle,
//     //   body: notificationMessage,
//     //   data: dynamicData,
//     //   type: dynamicData.type,
//     //   fcmTokens: successfulTokens, // Store only the successful tokens
//     //   status: 'sent',
//     // });

//   } catch (error) {
//     console.error('Error sending notification:', error);
//   }
// };

const sendPushNotification = async (global,userIds, title,action, dynamicId,type,message,actionBy) => {
  try {
    console.log("dynamicId",dynamicId)
    const message = {
      notification: {
        title,
      },
      data: dynamicId
    };
    const users = await userModel.find({ _id: { $in: userIds } }, 'fcmTokens').lean();
    const fcmTokens = users.map(user => user.fcmTokens).flat().filter(token => !!token);
    if(users && users.length){
      for (const user of users) {
        if (user.fcmTokens && user.fcmTokens.length > 0) {
          const successfulTokens = [];
          const failedTokens = [];
          let sendErrorMessage = '';
  
          // Attempt to send notification to each FCM token
          for (const token of user.fcmTokens) {
            try {
              console.log('Sending notification to token:', token);
              const response = await getMessaging().send({ ...message, token });
              console.log('Successfully sent message to token:', token, response);
              successfulTokens.push(token);
            } catch (sendError) {
              console.error('Error sending notification to token:', token, sendError);
              failedTokens.push(token);
              sendErrorMessage = sendError.message;
            }
          }
          const notificationData = {
            isGlobal:global,
            userId: user._id,
            title: template.notificationTitle,
            body: notificationMessage,
            fcmTokens: successfulTokens, // Store only the successful tokens
            status: 'sent',
            dynamicId,
            action
          }
          console.log("notificationData",notificationData)
          // Store notification for successful tokens
          if (successfulTokens.length > 0) {
            await Notification.create(notificationData);
          }
  
          // Store notification for failed tokens
          if (failedTokens.length > 0) {
            await Notification.create({
              userId: user._id,
              title: template.notificationTitle,
              body: notificationMessage,
              data: dynamicData,
              type: dynamicData.type,
              fcmTokens: failedTokens, // Store only the failed tokens
              status: 'failed',
              error: sendErrorMessage,
            });
          }
        }
      }
    } else{
      console.log({
        isGlobal:global,
        title: title,
        // message: message,
        status: 'sent',
        dynamicId,
        action,
        type,
        actionBy,
        
      })
      await Notification.create({
        isGlobal:global,
        title: title,
        // message: message,
        status: 'sent',
        dynamicId,
        action,
        type,
        actionBy,
        
      }); 
    }

  } catch (error) {
    console.error('Error sending notification:', error);
  }
};

export { sendPushNotification }
