 


"use client";
import { useState } from "react";
import { Plus } from "lucide-react";
import Link from "next/link";
import { FaRegTrashCan } from "react-icons/fa6";
import { FiEye } from "react-icons/fi";
import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight,
} from "react-icons/md";
import { TfiPencilAlt } from "react-icons/tfi";

// Function to split text into lines with a specific number of words per line
const splitTextByWords = (text, wordsPerLine) => {
  if (typeof text !== "string") return [text]; // Wrap non-string content in an array
  const words = text.split(" ");

  // If no words are found, return an empty array to avoid `.map` errors
  if (words.length === 0) return [];

  return words
    .reduce((acc, word, index) => {
      if (index % wordsPerLine === 0) acc.push([]);
      acc[acc.length - 1].push(word);
      return acc;
    }, [])
    .map((line) => line.join(" "));
};



const Awards = () => {
  // eslint-disable-next-line no-unused-vars
  const [wordsPerLine, setWordsPerLine] = useState(13);

  const awardData = [
    {
      id: 1,
      name: (
        <div>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Celebrating</span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Excellence</span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Hostel</span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Hospitality</span>
        </div>
      ),
      feedback:
        "At MixDorm, we believe in recognizing and celebrating the outstanding efforts of hostels that go above and beyond to provide exceptional experiences for travelers. Our Global Hostel Awards honor the best hostels across 149+ countries, showcasing their dedication to hospitality, community building, and innovation.",
    },
    {
      id: 2,
      name: (
        <div className="">
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">About the</span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">MixDorm</span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            Global Hostel
          </span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">Awards</span>
        </div>
      ),
      feedback: (
        <div>
          <h1 className="text-base text-black font-bold dark:text-[#B6B6B6]">1. Our Vision</h1>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            The MixDorm Global Hostel Awards aim to elevate the standards of
            hostel hospitality by recognizing the finest accommodations
            worldwide. These awards celebrate the hostels that embody the spirit
            of adventure, foster community among travelers, and provide
            unparalleled service and comfort.
          </p>
          <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
            2. Award Categories
          </h1>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            We award hostels across various categories to acknowledge the
            diverse aspects of hostel management:
          </p>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            {" "}
            <strong className="text-sm text-black font-bold dark:text-gray-200 ">
              Best Hostel in Country:
            </strong>
            Recognizing the top-rated hostel in each region.
          </p>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            {" "}
            <strong className="text-sm text-black font-bold dark:text-gray-200">
              Best Community Experience:
            </strong>
            Awarded to the hostel that excels in creating a vibrant and
            inclusive community for travelers.
          </p>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            {" "}
            <strong className="text-sm text-black font-bold dark:text-gray-200">
              Most Innovative Hostel:
            </strong>
            Celebrating hostels that implement creative solutions and
            technologies to enhance the guest experience.
          </p>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            {" "}
            <strong className="text-sm text-black font-bold dark:text-gray-200">
              Best Sustainable Hostel:
            </strong>
            Honoring hostels that prioritize eco-friendly practices and
            contribute positively to the environment.
          </p>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            {" "}
            <strong className="text-sm text-black font-bold dark:text-gray-200">
              Top-Rated Hostel by Travelers:
            </strong>
            Based on traveler reviews and feedback, this award goes to the
            hostel with the highest guest satisfaction.
          </p>

          <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
            3. Selection Process
          </h1>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            The selection process for the MixDorm Global Hostel Awards is
            thorough and transparent:
          </p>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            {" "}
            <strong className="text-sm text-black font-bold dark:text-gray-200">
              Nomination:
            </strong>
            Hostels are nominated by MixDorm users, local communities, and our
            expert panel.
          </p>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            {" "}
            <strong className="text-sm text-black font-bold dark:text-gray-200">
              Evaluation:
            </strong>
            Each nominated hostel undergoes a rigorous evaluation process,
            including guest reviews, on-site inspections, and community
            feedback.
          </p>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            {" "}
            <strong className="text-sm text-black font-bold dark:text-gray-200">
              Announcement:
            </strong>
            Winners are announced annually on our website, social media
            platforms, and during the MixDorm Global Hostel Awards Ceremony.
          </p>

          <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
            4. Impact of the Awards
          </h1>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            Winning a MixDorm Global Hostel Award is a prestigious honor that
            brings international recognition, attracts more guests, and enhances
            the reputation of the hostel within the global travel community.
          </p>
        </div>
      ),
    },
  ];

  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <div className="flex items-center justify-between w-full">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Award
        </h2>
        <div className="w-[50%] gap-x-5 flex justify-end items-center">
          <Link
            href={"/superadmin/dashboard/awards-add"}
            className="px-4 py-2 text-sm font-medium font-poppins text-white rounded flex items-center bg-sky-blue-650"
          >
            <Plus size={18} className="mr-1" /> Award
          </Link>
        </div>
      </div>

      <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border dark:border-none">
        <table className="w-full divide-y bg-white rounded-xl divide-gray-200 dark:bg-black">
          <thead>
            <tr className="w-full items-center justify-between">
              <th className="pr-8 py-6 bg-white text-center text-sm font-semibold font-poppins text-black uppercase dark:bg-black dark:text-[#B6B6B6]">
                TITLE TEXT
              </th>
              <th className="py-6 pr-10 md:pr-8 lg:pr-10 bg-white text-sm font-semibold text-center font-poppins text-black uppercase dark:bg-black dark:text-[#B6B6B6]">
                SUB TEXT
              </th>
              <th className="py-6  bg-white text-center text-sm font-poppins font-semibold text-black uppercase dark:bg-black dark:text-[#B6B6B6]">
                ACTION
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 border-x border-y dark:border-x-0 text-black/70 dark:text-[#757575]">
            {awardData.map((awardItem) => (
              <tr key={awardItem.id} className="w-full">
                <td className="whitespace-nowrap text-gray-500 text-sm font-medium font-poppins px-6 py-8 dark:text-[#757575]">
                  {awardItem.name}
                </td>
                <td className="pl-6 lg:pl-32 px-5 text-sm text-gray-500 font-medium font-poppins py-4 dark:text-[#757575]">
                  {splitTextByWords(awardItem.feedback, wordsPerLine)?.map(
                    (line, index) => (
                      <div key={index} className="block">
                        {line}
                      </div>
                    )
                  )} 
                </td>
                <td className="py-6 md:py-6 lg:py-10 flex justify-end px-6">
                  <Link
                    href={"/superadmin/dashboard/awards-details"}
                    className="border p-2 rounded-l-lg text-black/75 hover:text-blue-700 dark:text-[#757575] dark:hover:text-blue-700"
                  >
                    <FiEye />
                  </Link>
                  <Link
                    href={"/superadmin/dashboard/awards-edit"}
                    className="border p-2 text-black/75 hover:text-yellow-400 dark:text-[#757575] dark:hover:text-yellow-400"
                  >
                    <TfiPencilAlt />
                  </Link>
                  <button className="p-2 border rounded-r-lg text-red-600">
                    <FaRegTrashCan />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

       <div className="flex justify-between items-center mt-5">
                    <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
                    <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
                      <a
                        href="#"
                        className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                      >
                        <span className="sr-only">Next Page</span>
                        <MdOutlineKeyboardArrowLeft />
                      </a>
            
                      <a
                        href="#"
                        className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                      >
                        <span className="sr-only">Next Page</span>
                        <MdOutlineKeyboardArrowRight />
                      </a>
                    </div>
                  </div>
    </div>
  );
};

export default Awards;
