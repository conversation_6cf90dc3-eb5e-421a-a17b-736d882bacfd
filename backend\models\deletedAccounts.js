import mongoose from 'mongoose';

const messageSchema = mongoose.Schema(
    {
        user: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'users',
            required: true,
        },
        reason: {
            type: String
        },
        deactivatePeriod: {
            type: Number
        }
    },
    {
        timestamps: true,
    }
);

const Message = mongoose.model('chats', messageSchema);
export default Message;
