import express from "express";
import mongoose from "mongoose";
import authRoutes from './routes/auth.js';
import socialAuthRoutes from './routes/socialAuth.js';
import hostelRoutes from './routes/hostel.js';
import roomRoutes from './routes/room.js';
import eventRoutes from './routes/event.js';
import commonRoutes from './routes/front/properties.js';
import bookingRoutes from './routes/booking.js';
import reviewsRoutes from './routes/reviews.js';
import blogRoutes from './routes/blog.js';
import userRoutes from './routes/user.js';
import pageRoutes from './routes/page.js';
import permissionRoutes from './routes/permission.js';
import contactUsRoutes from "./routes/contactUs.js";
import settingsRoute from "./routes/setting.js";
import groupRoute from "./routes/group.js";
import ridesRoute from "./routes/ride.js";
import basicAuth from 'express-basic-auth';
import swaggerUi from 'swagger-ui-express';
import swaggerSpec from './swagger.js';
import dotenv from 'dotenv';
import cors from 'cors';
import passport from 'passport';
import path from 'path';
import { fileURLToPath } from 'url';
import { initializeRoles, initializeDefaultAdmin } from "./init/initRolePermission.js";
import { initializeEmailTemplates } from "./init/initEmailTemplates.js";
import { initializeSettings } from "./init/initSettings.js";
import xmlparser from 'express-xml-bodyparser';
import cron from "node-cron";


dotenv.config();
const app = express();
app.use(cors())
// app.use(cors({
//   origin:'*',
//   credentials: true,
//   methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
//   allowedHeaders: ['Authorization', 'Content-Type']
// }));

// Serve static files from the 'public' directory
// app.use(responseEncryptMiddleware)

app.use(express.static('public'));
app.use(xmlparser());
app.use(express.json());

// Convert `import.meta.url` to `__dirname`
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Serve static files
app.use('/public', express.static(path.join(__dirname, '../public')));
app.use(passport.initialize());

mongoose
  .connect(process.env.MONGODB_URI)
  .then(() => console.log("Connected to MongoDB"))
  .catch((err) => console.error("MongoDB connection error:", err));


// Ensure preflight requests are handled
app.options('*', cors());
const users = { [process.env.SWAGGER_USERNAME]: process.env.SWAGGER_PASSWORD };
import setupRoutes from './routes.js'; // Import the routes setup
import { responseEncryptMiddleware } from "./utills/encryptions.js";
import { updateCurrencyRates } from "./cron/liveRateFetch.js";

const authMiddleware = basicAuth({
  users,
  challenge: true, // Show the login dialog
  unauthorizedResponse: "Unauthorized access", // Error message for unauthorized users
});
// Function to filter public paths and associated tags
const getFilteredSwaggerSpec = (host) => {
  const filteredSwaggerSpec = { ...swaggerSpec, paths: { ...swaggerSpec.paths } };
  const tagsSet = new Set(); // To store unique tags for public paths

  // Check if the host is one of the allowed ones
  if (host === 'chanel-api.mixdorm.com') {
    const publicPaths = Object.keys(swaggerSpec.paths).reduce((acc, path) => {
      const methods = swaggerSpec.paths[path];

      const hasPublicMethod = Object.keys(methods).some((method) => {
        const isPublic = methods[method]?.['x-isPublic'] === true;

        if (isPublic) {
          // Add tags to the set if the method is public
          methods[method].tags.forEach(tag => tagsSet.add(tag));
        }
        return isPublic;
      });

      // Add path to acc if any method is public
      if (hasPublicMethod) {
        acc[path] = methods;
      }

      return acc;
    }, {});

    filteredSwaggerSpec.paths = publicPaths;

    // Convert the tags set to an array and assign it to the filteredSwaggerSpec
    filteredSwaggerSpec.tags = Array.from(tagsSet).map(tag => ({ name: tag }));

  }

  return filteredSwaggerSpec;
};
app.use('/pages', pageRoutes);

// app.get('/auth', (req, res) => {

//   const authUrl = `https://api.instagram.com/oauth/authorize?
//   client_id=8897652390319755
//   &redirect_uri=http://localhost:4000/auth/callback
//   &response_type=code
//   &scope=user_profile,user_media`;
//   res.redirect(authUrl);

// });
// app.get('/auth/callback', async (req, res) => {
//   const { code } = req.query;

//   if (!code) {
//     return res.status(400).send('No code received');
//   }

//   // Use the authorization code to get the access token
//   const tokenUrl = `https://api.instagram.com/oauth/access_token`;

//   const form = {
//     client_id: '8897652390319755',
//     client_secret: 'your-instagram-client-secret',
//     grant_type: 'authorization_code',
//     redirect_uri: 'http://localhost:4000/auth/callback',
//     code: code,
//   };

//   try {
//     const response = await axios.post(tokenUrl, form);
//     const { access_token, user_id } = response.data;
//     console.log('Access token:', access_token);
//     res.json({ access_token, user_id });
//   } catch (error) {
//     console.error('Error fetching access token:', error);
//     res.status(500).send('Error retrieving access token');
//   }
// });

// Middleware to filter routes based on domain
app.use((req, res, next) => {
  const host = req.get('host');
  next();
});

// Serve Swagger UI with filtered paths
app.use('/api-docs', swaggerUi.serve, (req, res) => {
  const filteredSwaggerSpec = getFilteredSwaggerSpec(req.get('host'));
  swaggerUi.setup(filteredSwaggerSpec)(req, res);
});
setupRoutes(app);
initializeRoles()
initializeDefaultAdmin()
initializeEmailTemplates()
initializeSettings()
// Schedule the cron job to run every 12 hours
cron.schedule("0 */12 * * *", async () => {
  console.log("Fetching live rates (Cron Triggered)...");
  try {
    await updateCurrencyRates();
    console.log("Live rates updated successfully.");
  } catch (error) {
    console.error("Error updating live rates:", error);
  }
});
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));