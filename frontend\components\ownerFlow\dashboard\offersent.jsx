import React, { useState } from "react";

import { <PERSON>, <PERSON>ci<PERSON>, MoreVertical, Trash } from "lucide-react";
import { <PERSON>u, <PERSON>uButton, MenuItem, MenuItems } from "@headlessui/react";



const Offersent = () => {
  // eslint-disable-next-line no-unused-vars
  const [totalPages, setTotalPages] = useState(1);
  // eslint-disable-next-line no-unused-vars
  const [currentPage, setCurrentPage] = useState(1);

  return (
    <>
      <div className="overflow-x-auto  mt-5 rounded-md">
        <table className="w-full border rounded-t-lg border-gray-340">
          <thead>
            <tr>
              <th className="px-2 py-2.5 bg-[#EEEEEE] font-medium text-xs text-black text-left whitespace-nowrap">
                Room IN
              </th>
              <th className="bg-[#EEEEEE] font-medium text-xs text-black text-left px-2 py-2.5 whitespace-nowrap">
                Name
              </th>
              <th className="bg-[#EEEEEE] font-medium text-xs text-black text-left px-2 py-2.5 whitespace-nowrap">
                Location
              </th>
              <th className="bg-[#EEEEEE] font-medium text-xs text-black text-left px-2 py-2.5 whitespace-nowrap">
                Date of arrival
              </th>
              <th className="bg-[#EEEEEE] font-medium text-xs text-black text-left px-2 py-2.5 whitespace-nowrap">
                Services offer
              </th>
              <th className="bg-[#EEEEEE] font-medium text-xs text-black text-left px-2 py-2.5 whitespace-nowrap">
                Instagram Id
              </th>
              <th className="bg-[#EEEEEE] font-medium text-xs text-black text-left px-2 py-2.5 whitespace-nowrap">
                Email
              </th>
              <th className="bg-[#EEEEEE] font-medium text-xs text-black text-left px-2 py-2.5 whitespace-nowrap">
                Hostel Name
              </th>
         
              <th className="bg-[#EEEEEE] font-medium text-xs text-black text-left px-2 py-2.5 whitespace-nowrap">
                Send Offer
              </th>
              <th className="bg-[#EEEEEE] font-medium text-xs text-black text-center px-2 py-2.5"></th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="text-xs font-semibold p-2 border-b">#01</td>
              <td className="text-xs font-normal p-2 border-b whitespace-nowrap">Alexander</td>
              <td className="text-xs font-normal p-2 border-b whitespace-nowrap">
                Mumbai, India
              </td>
              <td className="text-xs font-normal p-2 border-b whitespace-nowrap">Sep 25,2019</td>
              <td className="text-xs font-semibold p-2 border-b whitespace-nowrap">Paid</td>
              <td className="text-xs font-normal p-2 border-b whitespace-nowrap">
                https:://www.instagram.com
              </td>
              <td className="text-xs font-normal p-2 border-b whitespace-nowrap">Alexlee@</td>
              <td className="text-xs font-normal p-2 border-b whitespace-nowrap">made monkey</td>

              <td className="text-xs font-normal p-2 border-b text-left">
                <div className="inline-flex">
                  <button className="text-xs font-inter text-[#1570ef] bg-[#C8DAF4] rounded-2xl p-2 px-3">
                    Accept
                  </button>
                  <button className="text-xs font-inter text-[#f36960] bg-[#F5D9D7] rounded-2xl p-2 px-3 ml-2">
                    Reject
                  </button>
                </div>
              </td>
              {/* <td className="text-xs font-normal p-2 border-b text-center">
                    <Dropdown>
                          <MenuButton>
                            <MoreVertIcon color="action" />
                          </MenuButton>
                          <Menu className="bg-white shadow rounded-lg">
                          <MenuItem>
                              <button
                                className="flex items-center gap-3"
                              >
                                <Eye size={18} />
                                View{" "}
                              </button>
                            </MenuItem>
                            <MenuItem>
                              <button
                                // onClick={() => {
                                //   setEditOpen(true);
                                //   setEditId(item?._id);
                                // }}
                                className="flex items-center gap-3"
                              >
                                <CiEdit />
                                Edit{" "}
                              </button>
                            </MenuItem>
                            <MenuItem>
                              <button
                                // onClick={() => {
                                //   handleDelete(item?._id);
                                // }}
                                className="flex items-center gap-3"
                              >
                                <FaRegTrashCan />
                                Delete
                              </button>
                            </MenuItem>
                          </Menu>
                        </Dropdown>
                    </td> */}
              <td className="p-4 relative">
                <Menu as="div" className="relative inline-block text-left">
                  <div>
                    <MenuButton>
                      <MoreVertical aria-hidden="true" size={16}></MoreVertical>
                    </MenuButton>
                  </div>

                  <MenuItems
                    // transition
                    // ref={(el) => {
                    //   if (el) {
                    //     const rect = el.getBoundingClientRect();
                    //     const windowHeight = window.innerHeight;
                    //     const isLastItem = index === EventList.length - 1;
                    //     const isOnlyItem = EventList.length === 1;

                    //     // Clear any previously applied classes
                    //     el.classList.remove(
                    //       "bottom-full",
                    //       "mb-2",
                    //       "mt-2",
                    //       "top-1/2",
                    //       "-translate-y-1/2"
                    //     );

                    //     if (isOnlyItem) {
                    //       // Center the dropdown vertically in the viewport
                    //       el.classList.add("top-1/2", "-translate-y-1/2");
                    //     } else if (isLastItem || rect.bottom > windowHeight) {
                    //       el.classList.add("bottom-full", "mb-2");
                    //     } else {
                    //       el.classList.add("mt-2");
                    //     }
                    //   }
                    // }}
                    className="absolute right-0 z-10 mt-2 w-max origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
                  >
                    <div>
                      <MenuItem>
                        <button
                          href="#"
                          className="px-4 py-2 text-sm w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-t-md"
                        >
                          <Eye size={16}></Eye>
                          View
                        </button>
                      </MenuItem>
                      <MenuItem>
                        <button
                          href="#"
                          className="px-4 py-2 text-sm w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-t-md"
                        >
                          <Pencil size={16}></Pencil>
                          Edit
                        </button>
                      </MenuItem>
                      <MenuItem>
                        <button className="px-4 py-2 text-sm text-red-600 w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-b-md">
                          <Trash size={16} className="text-red-600" />{" "}
                          <span>Delete</span>
                        </button>
                      </MenuItem>
                    </div>
                  </MenuItems>
                </Menu>
              </td>
            </tr>
          </tbody>
        </table>
        
      </div>
    </>
  );
};

export default Offersent;
