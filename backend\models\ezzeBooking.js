import mongoose from "mongoose";
// Define the schema for EzeeBooking
const ezeeBookingSchema = new mongoose.Schema({
    hotelCode: { type: String,  },
    bookingID: { type: String },
    salutation: { type: String },
    firstName: { type: String },
    lastName: { type: String },
    gender: { type: String },
    city: { type: String },
    zipcode: { type: String },
    phone: { type: String },
    mobile: { type: String },
    email: { type: String },
    comment: { type: String },
    rooms: [
        {
            rateTypeID: { type: String },
            rateType: { type: String },
            roomTypeCode: { type: String },
            roomTypeName: { type: String },
            rentalInfoList: [
                {
                    date: { type: String },
                    adult: { type: Number },
                    child: { type: Number },
                    rent: { type: Number },
                    extraCharge: { type: Number },
                    tax: { type: Number },
                    discount: { type: Number }
                }
            ]
        }
    ]
}, { timestamps: true });

// Create a model using the schema
const EzeeBooking = mongoose.model('EzeeBooking', ezeeBookingSchema);
export default EzeeBooking;

