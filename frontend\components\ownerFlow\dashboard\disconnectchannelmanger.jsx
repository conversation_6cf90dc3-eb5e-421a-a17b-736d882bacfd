import React from "react";

const Disconnectchannelmanager = () => {
  return (
    <>
      <section className="w-full">
        <div className="flex items-center justify-center min-h-[400px] bg-transparent">
          <div className="text-center w-[600px]">
            <h1 className="text-lg font-semibold text-black mb-4">
              Disconnect Channel Manager Before Deleting Profile
            </h1>
            <p className="text-gray-600 text-sm mb-6">
              To proceed with account deletion, you must first disconnect your
              channel manager integration. This ensures that no active
              connections remain.
            </p>
            <button className="bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-8 rounded-full shadow-md">
              Disconnect Channel Manager
            </button>
          </div>
        </div>
      </section>
    </>
  );
};
export default Disconnectchannelmanager;
