import express from 'express';
import {
    addRoom<PERSON>ontroller,
    updateRoom<PERSON>ontroller,
    getRoomsByPropertyIdController,
    getRoomBySlugController,
    deleteRoomController,
    getRoomListByPropertyIdController,
    getRoomsRatesControllers,
    updateRoomRateController,
    getRoomTypesControllers

} from '../controller/room.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();

router.post('/', checkAuth('add_room'), addRoomController);
router.put('/room-rates', checkAuth('get_room_rate'),updateRoomRateController);
router.put('/:id', checkAuth('update_room'), updateRoomController);
router.get('/all/:propertyId', getRoomsByPropertyIdController);
router.get('/:propertyId', getRoomListByPropertyIdController);
router.get('/:slug', getRoomBySlugController);
router.delete('/:slug', checkAuth('delete_room'), deleteRoomController);
router.post('/room-rates', checkAuth('get_room_rate'),getRoomsRatesControllers);
router.post('/room-types', checkAuth('get_room_rate'),getRoomTypesControllers);

export default router;

/**
 * @swagger
 * tags:
 *   name: Rooms
 *   description: Room management endpoints
 */

/**
 * @swagger
 * /room:
 *   post:
 *     summary: Add a new room
 *     tags: [Rooms]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Room name
 *                 example: 'Deluxe Room'
 *               type:
 *                 type: string
 *                 description: Room type
 *                 example: 'Single'
 *               property:
 *                 type: string
 *                 description: Property ID
 *                 example: '60d0fe4f5311236168a109ca'
 *               beds:
 *                 type: number
 *                 description: Number of beds
 *                 example: 2
 *               roomNumber:
 *                 type: number
 *                 description: Room Number
 *                 example: 201
 *               capacity:
 *                 type: number
 *                 description: Room capacity
 *                 example: 4
 *               ensuite:
 *                 type: string
 *                 description: Ensuite information
 *                 example: 'Yes'
 *               rate:
 *                 type: object
 *                 properties:
 *                   weekdayRate:
 *                     type: object
 *                     properties:
 *                       value:
 *                         type: number
 *                         example: 100
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                         example: '2023-01-01T00:00:00Z'
 *                   weekendRate:
 *                     type: object
 *                     properties:
 *                       value:
 *                         type: number
 *                         example: 120
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                         example: '2023-01-01T00:00:00Z'
 *               currency:
 *                 type: string
 *                 description: Currency for the rate
 *                 example: 'USD'
 *               images:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     title:
 *                       type: string
 *                       description: Image title
 *                       example: 'Room view'
 *                     url:
 *                       type: string
 *                       description: Image URL
 *                       example: 'http://example.com/image.jpg'
 *               description:
 *                 type: string
 *                 description: Room description
 *                 example: 'A deluxe room with all amenities.'
 *               grade:
 *                 type: string
 *                 description: Room grade
 *                 example: 'A'
 *               discount:
 *                 type: object
 *                 properties:
 *                   discountPercentage:
 *                     type: number
 *                     example: 10
 *                   startDate:
 *                     type: string
 *                     format: date
 *                     example: '2023-01-01'
 *                   endDate:
 *                     type: string
 *                     format: date
 *                     example: '2023-01-10'
 *                   standardRate:
 *                     type: number
 *                     example: 150
 *                   nonRefundableRate:
 *                     type: number
 *                     example: 130
 *               bedAnBreakfast:
 *                 type: boolean
 *                 description: Indicates if bed and breakfast is included
 *                 example: false
 *               nonRefundable:
 *                 type: boolean
 *                 description: Indicates if the room is non-refundable
 *                 example: false
 *               freeCancellation:
 *                 type: boolean
 *                 description: Indicates if the room has free cancellation
 *                 example: false
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                   example: 'wifi'
 *               isActive:
 *                 type: boolean
 *                 description: Room active status
 *                 example: true
 *               isDeleted:
 *                 type: boolean
 *                 description: Room deleted status
 *                 example: false
 *               addedBy:
 *                 type: string
 *                 description: User who added the room
 *                 example: '60d0fe4f5311236168a109ca'
 *             required:
 *               - name
 *               - type
 *               - property
 *               - currency
 *     responses:
 *       '201':
 *         description: Room added successfully
 *         content:
 *           application/json:
 *             example:
 *               message: 'Room Added Successfully'
 *               room:
 *                 name: 'Deluxe Room'
 *                 slug: 'deluxe-room'
 *                 type: 'Single'
 *                 property: '60d0fe4f5311236168a109ca'
 *                 beds: 2
 *                 roomNumber: 201
 *                 capacity: 4
 *                 ensuite: 'Yes'
 *                 rate:
 *                   weekdayRate:
 *                     value: 100
 *                     updatedAt: '2023-01-01T00:00:00Z'
 *                   weekendRate:
 *                     value: 120
 *                     updatedAt: '2023-01-01T00:00:00Z'
 *                 currency: 'USD'
 *                 images:
 *                   - title: 'Room view'
 *                     url: 'http://example.com/image.jpg'
 *                 description: 'A deluxe room with all amenities.'
 *                 grade: 'A'
 *                 discount:
 *                   discountPercentage: 10
 *                   startDate: '2023-01-01'
 *                   endDate: '2023-01-10'
 *                   standardRate: 150
 *                   nonRefundableRate: 130
 *                 bedAnBreakfast: false
 *                 nonRefundable: false
 *                 freeCancellation: false
 *                 tags: ['wifi']
 *                 isActive: true
 *                 isDeleted: false
 *                 addedBy: '60d0fe4f5311236168a109ca'
 *                 createdAt: '2023-01-01T00:00:00Z'
 *                 updatedAt: '2023-01-02T00:00:00Z'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */

/**
 * @swagger
 * /room/{slug}:
 *   put:
 *     summary: Update room details by slug
 *     tags: [Rooms]
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: The room slug
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Room name
 *                 example: 'Deluxe Room'
 *               type:
 *                 type: string
 *                 description: Room type
 *                 example: 'Single'
 *               property:
 *                 type: string
 *                 description: Property ID
 *                 example: '60d0fe4f5311236168a109ca'
 *               beds:
 *                 type: number
 *                 description: Number of beds
 *                 example: 2
 *               roomNumber:
 *                 type: number
 *                 description: Room Number
 *                 example: 2
 *               capacity:
 *                 type: number
 *                 description: Room capacity
 *                 example: 4
 *               ensuite:
 *                 type: string
 *                 description: Ensuite information
 *                 example: 'Yes'
 *               rate:
 *                 type: object
 *                 properties:
 *                   weekdayRate:
 *                     type: object
 *                     properties:
 *                       value:
 *                         type: number
 *                         example: 100
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                         example: '2023-01-01T00:00:00Z'
 *                   weekendRate:
 *                     type: object
 *                     properties:
 *                       value:
 *                         type: number
 *                         example: 120
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                         example: '2023-01-01T00:00:00Z'
 *               currency:
 *                 type: string
 *                 description: Currency for the rate
 *                 example: 'USD'
 *               images:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     title:
 *                       type: string
 *                       description: Image title
 *                       example: 'Room view'
 *                     url:
 *                       type: string
 *                       description: Image URL
 *                       example: 'http://example.com/image.jpg'
 *               description:
 *                 type: string
 *                 description: Room description
 *                 example: 'A deluxe room with all amenities.'
 *               grade:
 *                 type: string
 *                 description: Room grade
 *                 example: 'A'
 *               discount:
 *                 type: object
 *                 properties:
 *                   discountPercentage:
 *                     type: number
 *                     example: 10
 *                   startDate:
 *                     type: string
 *                     format: date
 *                     example: '2023-01-01'
 *                   endDate:
 *                     type: string
 *                     format: date
 *                     example: '2023-01-10'
 *                   standardRate:
 *                     type: number
 *                     example: 150
 *                   nonRefundableRate:
 *                     type: number
 *                     example: 130
 *               bedAnBreakfast:
 *                 type: boolean
 *                 description: Indicates if bed and breakfast is included
 *                 example: false
 *               nonRefundable:
 *                 type: boolean
 *                 description: Indicates if the room is non-refundable
 *                 example: false
 *               freeCancellation:
 *                 type: boolean
 *                 description: Indicates if the room has free cancellation
 *                 example: false
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: List of tags
 *                 example: ['WiFi', 'Air Conditioning']
 *               isActive:
 *                 type: boolean
 *                 description: Room active status
 *                 example: true
 *               isDeleted:
 *                 type: boolean
 *                 description: Room deleted status
 *                 example: false
 *               addedBy:
 *                 type: string
 *                 description: User who added the room
 *                 example: '60d0fe4f5311236168a109ca'
 *     responses:
 *       '200':
 *         description: Room updated successfully
 *         content:
 *           application/json:
 *             example:
 *               message: 'Room Updated Successfully'
 *       '404':
 *         description: Room not found
 *         content:
 *           application/json:
 *             example:
 *               message: 'Room Not Found'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */

/**
 * @swagger
 * /room/all/{propertyId}:
 *   get:
 *     summary: Get rooms by property ID with optional filters
 *     tags: [Rooms]
 *     x-isPublic: true
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *         description: The property ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         required: false
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 10
 *         required: false
 *         description: Number of items per page
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by room type (e.g., "Private", "Female Dorm","Mixed Dorm","Double")
 *       - in: query
 *         name: beds
 *         schema:
 *           type: integer
 *           minimum: 1
 *         required: false
 *         description: Filter by number of beds
 *     responses:
 *       '200':
 *         description: Successfully retrieved rooms
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 rooms:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Room'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     totalRooms:
 *                       type: integer
 *             example:
 *               rooms:
 *                 - name: 'Deluxe Room'
 *                   slug: 'deluxe-room'
 *                   type: 'Single'
 *                   beds: 2
 *                   ...
 *               pagination:
 *                 page: 1
 *                 limit: 10
 *                 totalPages: 5
 *                 totalRooms: 50
 *       '404':
 *         description: Rooms not found
 *         content:
 *           application/json:
 *             example:
 *               message: 'Rooms Not Found'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */

/**
 * @swagger
 * /room/{slug}:
 *   get:
 *     summary: Get room by slug
 *     tags: [Rooms]
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: The room slug
 *     responses:
 *       '200':
 *         description: Successfully retrieved room
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 _id:
 *                   type: string
 *                   description: Room ID
 *                 name:
 *                   type: string
 *                   description: Room name
 *                 slug:
 *                   type: string
 *                   description: Room slug
 *                 type:
 *                   type: string
 *                   description: Room type
 *                 property:
 *                   type: string
 *                   description: Property ID
 *                 beds:
 *                   type: number
 *                   description: Number of beds
 *                 roomNumber:
 *                   type: number
 *                   description: Room Number
 *                 capacity:
 *                   type: number
 *                   description: Capacity of the room
 *                 ensuite:
 *                   type: string
 *                   description: Ensuite information
 *                 rate:
 *                   type: object
 *                   properties:
 *                     weekdayRate:
 *                       type: object
 *                       properties:
 *                         value:
 *                           type: number
 *                         updatedAt:
 *                           type: string
 *                           format: date-time
 *                     weekendRate:
 *                       type: object
 *                       properties:
 *                         value:
 *                           type: number
 *                         updatedAt:
 *                           type: string
 *                           format: date-time
 *                   description: Room rates for weekday and weekend
 *                 currency:
 *                   type: string
 *                   description: Currency for the rate
 *                 images:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       title:
 *                         type: string
 *                         description: Image title
 *                       url:
 *                         type: string
 *                         description: Image URL
 *                 description:
 *                   type: string
 *                   description: Room description
 *                 grade:
 *                   type: string
 *                   description: Grade of the room
 *                 discount:
 *                   type: object
 *                   properties:
 *                     discountPercentage:
 *                       type: number
 *                     startDate:
 *                       type: string
 *                       format: date-time
 *                     endDate:
 *                       type: string
 *                       format: date-time
 *                     standardRate:
 *                       type: number
 *                     nonRefundableRate:
 *                       type: number
 *                   description: Discount details
 *                 bedAndBreakfast:
 *                   type: boolean
 *                   description: Indicates if bed and breakfast is included
 *                 nonRefundable:
 *                   type: boolean
 *                   description: Indicates if the room is non-refundable
 *                 freeCancellation:
 *                   type: boolean
 *                   description: Indicates if the room has free cancellation
 *                 tags:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: List of tags
 *                 isActive:
 *                   type: boolean
 *                   description: Room active status
 *                 isDeleted:
 *                   type: boolean
 *                   description: Room deleted status
 *                 isArchived:
 *                   type: boolean
 *                   description: Room archived status
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *                   description: Timestamp when the room was created
 *                 updatedAt:
 *                   type: string
 *                   format: date-time
 *                   description: Timestamp when the room was last updated
 *             example:
 *               _id: '60d0fe4f5311236168a109cb'
 *               name: 'Deluxe Room'
 *               slug: 'deluxe-room'
 *               type: 'Single'
 *               property: '60d0fe4f5311236168a109ca'
 *               beds: 2
 *               roomNumber: 201
 *               capacity: 4
 *               ensuite: 'Yes'
 *               rate:
 *                 weekdayRate:
 *                   value: 120
 *                   updatedAt: '2023-01-01T10:00:00Z'
 *                 weekendRate:
 *                   value: 140
 *                   updatedAt: '2023-01-01T10:00:00Z'
 *               currency: 'USD'
 *               images: [{ title: 'Room Image', url: 'http://example.com/image.jpg' }]
 *               description: 'A deluxe room with all amenities.'
 *               grade: 'A'
 *               discount:
 *                 discountPercentage: 10
 *                 startDate: '2023-01-01T00:00:00Z'
 *                 endDate: '2023-12-31T23:59:59Z'
 *                 standardRate: 150
 *                 nonRefundableRate: 120
 *               bedAndBreakfast: false
 *               nonRefundable: false
 *               freeCancellation: true
 *               tags: ['WiFi', 'Air Conditioning']
 *               isActive: true
 *               isDeleted: false
 *               createdAt: '2023-01-01T08:00:00Z'
 *               updatedAt: '2023-01-02T15:00:00Z'
 *       '404':
 *         description: Room not found
 *         content:
 *           application/json:
 *             example:
 *               message: 'Room Not Found'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */

/**
 * @swagger
 * /room/{propertyId}:
 *   get:
 *     summary: Get rooms by propertyId
 *     tags:
 *       - Rooms
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the property to filter rooms
 *     responses:
 *       200:
 *         description: A list of rooms filtered by propertyId
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   _id:
 *                     type: string
 *                     description: The room ID
 *                   name:
 *                     type: string
 *                     description: The room name
 *       400:
 *         description: Invalid propertyId or missing required fields
 *       500:
 *         description: Server error
*/

/**
 * @swagger
 * /room/{slug}:
 *   delete:
 *     summary: Delete room by slug
 *     tags: [Rooms]
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: The room slug
 *     responses:
 *       '200':
 *         description: Room deleted successfully
 *         content:
 *           application/json:
 *             example:
 *               message: 'Room Deleted Successfully'
 *       '404':
 *         description: Room not found
 *         content:
 *           application/json:
 *             example:
 *               message: 'Room Not Found'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */
/**
 * @swagger
 * /room/room-rates:
 *   post:
 *     summary: Get room rates based on property ID and date
 *     tags: [Rooms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               propertyId:
 *                 type: string
 *                 description: The ID of the property to fetch room rates for
 *             required:
 *               - propertyId
 *           example:
 *             propertyId: "66bd2f7592a0a6fb036f3458"
 *     responses:
 *       '200':
 *         description: Room rates fetched successfully
 *         content:
 *           application/json:
 *             example:
 *               message: 'Rooms fetched successfully'
 *               data:
 *                 Mixed Dorm:
 *                   - name: "Basic 7 Bed Mixed Dorm"
 *                     capacity: 7
 *                     rate: 21.7
 *                     currency: "EUR"
 *                     slug: "basic-7-bed-mixed-dorm"
 *                     images: []
 *       '400':
 *         description: Bad request
 *         content:
 *           application/json:
 *             example:
 *               message: 'Property ID is required'
 *       '404':
 *         description: No rooms found
 *         content:
 *           application/json:
 *             example:
 *               message: 'No rooms found for the given property'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 */
/**
 * @swagger
 * /room/room-rates:
 *   put:
 *     summary: Update room rates by roomTypeId and date range
 *     tags: [Rooms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - roomTypeId
 *               - FromDate
 *               - ToDate
 *               - roomRate
 *             properties:
 *               roomTypeId:
 *                 type: string
 *                 format: uuid
 *                 description: ID of the room type
 *                 example: "652d749c9ce2fbe01e4ecf0a"
 *               FromDate:
 *                 type: string
 *                 format: date
 *                 description: Start date for the rate update
 *                 example: "2025-04-20"
 *               ToDate:
 *                 type: string
 *                 format: date
 *                 description: End date for the rate update
 *                 example: "2025-04-22"
 *               roomRate:
 *                 type: object
 *                 required:
 *                   - BaseRate
 *                 properties:
 *                   BaseRate:
 *                     type: string
 *                     description: Default base rate
 *                     example: "1000"
 *                   weekday:
 *                     type: string
 *                     description: Weekday rate
 *                     example: "900"
 *                   weekend:
 *                     type: string
 *                     description: Weekend rate
 *                     example: "1200"
 *     responses:
 *       200:
 *         description: Room rates updated successfully
 *         content:
 *           application/json:
 *             example:
 *               message: "Room rates updated successfully."
 *       400:
 *         description: Bad request, missing or invalid fields
 *         content:
 *           application/json:
 *             example:
 *               message: "Missing required fields."
 *       404:
 *         description: Room type not found
 *         content:
 *           application/json:
 *             example:
 *               message: "Room type not found."
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: "Internal server error"
 */
/**
 * @swagger
 * /room/room-types:
 *   post:
 *     summary: Get available room types
 *     tags: [Rooms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       description: Authenticated request to retrieve room types
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               propertyId:
 *                 type: string
 *                 description: Optional filter by property ID
 *                 example: '60d0fe4f5311236168a109ca'
 *     responses:
 *       200:
 *         description: Room types retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 roomTypes:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       RoomTypeId:
 *                         type: string
 *                         example: 'rm1'
 *                       RoomType:
 *                         type: string
 *                         example: 'Private Standard Room'
 *                       type:
 *                         type: string
 *                         example: 'private'
 *                       isActive:
 *                         type: boolean
 *                         example: true
 *                       isDeleted:
 *                         type: boolean
 *                         example: false
 *       401:
 *         description: Unauthorized - missing or invalid token
 *         content:
 *           application/json:
 *             example:
 *               success: false
 *               message: 'Unauthorized'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               success: false
 *               message: 'Internal Server Error'
 */
