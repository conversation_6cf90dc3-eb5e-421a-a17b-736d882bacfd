import Link from "next/link";
import React from "react";

const HelpDetails = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 dark:bg-[#171616] overflow-y-auto scroll-smooth ">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        Help Details
      </h2>
      <div className="bg-white border rounded-xl mt-5 h-auto dark:bg-black dark:border-none">
        <div className="p-8 flex flex-col gap-y-6">
          <div>
            <div className="flex ">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  How we can Assist you?
                </h1>
                <p className=" text-black/55 text-sm font-poppins font-medium dark:text-[#757575]">
                  At MixDorm, we’re here to make your experience as smooth and
                  enjoyable as possible. Whether you need help with a booking,
                  have questions about our services, or need support during your
                  stay, our Help page provides resources and contact information
                  to get you the assistance you need.
                </p>
              </div>
              <div className="">
                <Link href={"/superadmin/dashboard/help-edit"} className="text-white text-sm font-poppins py-1 md:py-2 lg:py-2 px-6 lg:px-8   rounded bg-sky-blue-650">
                  Edit
                </Link>
              </div>
            </div>
            <div className="flex flex-col">
              <div className="flex flex-col w-[90%] gap-y-3">
                <h1 className="text-lg text-black font-bold dark:text-[#B6B6B6] mt-2">
                  Common issue and solutions
                </h1>
                <div>
                  <h1 className="text-lg text-black font-bold dark:text-[#B6B6B6]">
                    1. Booking Assistance
                  </h1>
                  <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
                    <strong className="text-base text-black font-semibold dark:text-gray-200">
                      Problem:
                    </strong>
                    I need to modify or cancel my Booking.
                  </p>
                  <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
                    <strong className="text-base text-black font-semibold dark:text-gray-200">
                      Solution:
                    </strong>
                    You can modify or cancel your booking by logging into your
                    account on our website or app. For further assistance,
                    please contact our customer support team.
                  </p>
                  <h1 className="text-lg text-black font-bold mt-1 dark:text-[#B6B6B6]">
                    2. Check-In And Check-Out
                  </h1>
                  <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
                    <strong className="text-base text-black font-semibold dark:text-gray-200">
                      Problem:
                    </strong>
                    I have a question about check-in or check-out times.
                  </p>
                  <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
                    <strong className="text-base text-black font-semibold dark:text-gray-200">
                      Solution:
                    </strong>
                    Standard check-in is at 12 noon, and check-out is at 10 AM.
                    Early check-in or late check-out may be available upon
                    request. Contact the property directly for availability.
                  </p>
                  <h1 className="text-lg text-black font-bold mt-1 dark:text-[#B6B6B6]">
                    3. Lost And Found
                  </h1>
                  <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
                    <strong className="text-base text-black font-semibold dark:text-gray-200">
                      Problem:
                    </strong>
                    I lost something during my stay.
                  </p>
                  <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
                    <strong className="text-base text-black font-semibold dark:text-gray-200">
                      Solution:
                    </strong>
                    Report lost items to our customer support team as soon as
                    possible. We will make every effort to locate and return
                    lost belongings.
                  </p>
                  <h1 className="text-lg text-black font-bold mt-1 dark:text-[#B6B6B6]">
                    4. Technical Issues
                  </h1>
                  <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
                    <strong className="text-base text-black font-semibold dark:text-gray-200">
                      Problem:
                    </strong>
                    I’m having trouble using the website or app.
                  </p>
                  <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
                    <strong className="text-base text-black font-semibold dark:text-gray-200">
                      Solution:
                    </strong>
                    Try clearing your browser cache or updating the app. If the
                    issue persists, contact our technical support team for help.
                  </p>
                </div>
              </div>
            </div>
            <div className="flex ">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  Contact Us
                </h1>
                <p className=" text-black/55 text-sm font-poppins font-medium dark:text-[#757575]">
                  <div>
                    <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
                      {" "}
                      If you need help that isn’t covered here, our customer
                      support team is ready to assist you:
                    </p>
                    <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
                      Email: <EMAIL>
                    </p>
                  </div>
                </p>
              </div>
            </div>
            <div className="flex ">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  Support Hours our customer support team is available
                </h1>
                <div>
                  <p className="block text-gray-500 font-semibold text-sm dark:text-[#757575]">
                    Monday to Friday: 9 AM - 6 PM
                    <p className="block text-gray-500 font-semibold text-sm dark:text-[#757575]">
                      Saturday and Sunday: 10 AM - 4 PM
                    </p>
                    <p className="block text-gray-500 font-semibold mt-2 text-sm dark:text-[#757575]">
                      For urgent matters outside of these hours, please leave a
                      message or send an email,  and we will respond as
                      soon as possible.
                    </p>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-start justify-start p-8">
          <Link
            href={"/superadmin/dashboard/help"}
            className="text-white py-2 w-32 max-w-md rounded bg-sky-blue-650 flex items-center justify-center"
          >
            Cancel
          </Link>
        </div>
      </div>
    </div>
  );
};

export default HelpDetails;
