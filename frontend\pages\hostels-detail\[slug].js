/* eslint-disable react/no-unescaped-entities */

/* eslint-disable no-extra-boolean-cast */
/* eslint-disable no-unused-vars */
import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
// import SearchNavbar from "@/components/layout/SearchNavbar";
import { LiaBedSolid } from "react-icons/lia";
// import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { LuCalendarDays } from "react-icons/lu";
import { TbUsersGroup } from "react-icons/tb";
import { getHostelDeatil } from "@/services/webflowServices";
import { usePathname } from "next/navigation";
import { BsCupHot } from "react-icons/bs";
import { FcCancel } from "react-icons/fc";
import {
  format,
  differenceInDays,
  addMonths,
  isSameMonth,
  isSameDay,
} from "date-fns";
import { <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON><PERSON>, FaAngleUp } from "react-icons/fa6";
import { But<PERSON> } from "@mui/material";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, Autoplay } from "swiper/modules";
// import Loader from "@/components/Loader";
import toast from "react-hot-toast";
import {
  getItemLocalStorage,
  setItemLocalStorage,
} from "@/utils/browserSetting";
import { useRouter } from "next/router";
import dynamic from "next/dynamic";
import { useNavbar } from "@/components/home/<USER>";
import Loader from "@/components/loader/loader";
import Head from "next/head";
import { FaFireAlt, FaShoppingCart } from "react-icons/fa";
import { PiMapPinAreaFill } from "react-icons/pi";
import { BsCashCoin, BsEmojiFrown } from "react-icons/bs";
import { motion, AnimatePresence } from "framer-motion";
import { CiCalendar } from "react-icons/ci";
import { RxCrossCircled } from "react-icons/rx";

const DatePicker = dynamic(() => import("react-datepicker"), {
  ssr: false,
  loading: () => (
    <div className="h-12 w-full bg-gray-200 animate-pulse rounded-lg" />
  ),
});

const AccordianSearch = dynamic(
  () => import("@/components/accordian/accordianSearch"),
  {
    loading: () => (
      <div className="mt-8 space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-10 w-3/4 bg-gray-200 rounded-lg mb-2"></div>
          </div>
        ))}
      </div>
    ),
    ssr: false,
  }
);

const Carousel = dynamic(() => import("@/components/carousel/swipper"), {
  ssr: false,
});

const Breadcrumb = dynamic(() => import("@/components/breadcrumb/breadcrumb"), {
  ssr: false,
});

const HostelsDetails = ({ image, handleChange }) => {
  const router = useRouter();
  // const [startDate, setStartDate] = useState(new Date());
  const [hostelData, setHostelData] = useState();
  // const [showCalender, setShowCalender] = useState(false);
  const [calendarOpen, setCalendarOpen] = useState(false);
  const pathname = usePathname();
  const id = pathname?.split("/").pop();
  const [dates, setDates] = useState([null, null]);
  const [roomQuantities, setRoomQuantities] = useState({});
  const [selectedRooms, setSelectedRooms] = useState([]);
  const [currentHostelId, setCurrentHostelId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isLoadedMap, setIsLoadedMap] = useState({});
  const [imageFailedMap, setImageFailedMap] = useState({});
  const [bedSelections, setBedSelections] = useState({});
  const [guest, setGuest] = useState(1);
  const [currencySymbol, setCurrencySymbol] = useState("€");
  const [currency, setCurrency] = useState("");
  const { flagUrl2 } = useNavbar();
  const [loading, setLoading] = useState(false);
  const [imagesLoaded, setImagesLoaded] = useState({});
  const [imagesError, setImagesError] = useState({});

  // const [showMore, setShowMore] = useState(false);
  const [showBookingBanner, setShowBookingBanner] = useState(true);
  const targetSectionRef = useRef(null);
  const [nights, setNights] = useState(0);
  const [calendarMonths, setCalendarMonths] = useState([
    new Date(), // Left calendar (e.g., June)
    addMonths(new Date(), 1), // Right calendar (e.g., July)
  ]);
  const aboutText = hostelData?.propertyDetails?.property?.aboutUs;
  // const freeCancel = hostelData?.propertyDetails?.property?.freeCancellation;
  // const amenities = ["Wifi", "AC", "Ensuite"];

  const [expanded, setExpanded] = useState(false);
  const [showToggle, setShowToggle] = useState(false);
  const [needsGradient, setNeedsGradient] = useState(false);
  const textRef = useRef(null);

  const accordionRef = useRef(null);

  const propertyName = hostelData?.propertyDetails?.property?.name || "";
  const nameWords = propertyName.split(" ");

  const container = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const child = {
    hidden: {
      opacity: 0,
      y: 20,
      textShadow: "0 0 0px #40E0D0",
    },
    visible: {
      opacity: 1,
      y: 0,

      transition: {
        duration: 0.9,
      },
    },
  };

  // const textShadow = {
  //   hidden: {
  //     textShadow: "0 0 0px #40E0D0"
  //   },
  //   visible: {
  //     textShadow: [
  //       "0 0 0px #40E0D0",
  //       "0 0 8px #40E0D0",
  //       "0 0 0px #40E0D0"
  //     ],
  //     transition: {
  //       duration: 1.9,
  //       repeat: Infinity,
  //       repeatType: "reverse"
  //     }
  //   }
  // }

  // const handleScrollToReviews = () => {
  //   if (
  //     accordionRef.current &&
  //     typeof accordionRef.current.scrollToReview === "function"
  //   ) {
  //     accordionRef.current.scrollToReview();
  //   } else {
  //     console.warn("scrollToReview not available");
  //   }
  // };

  // Handlers
  const handleImageLoad = (hostelId, index) => {
    setImagesLoaded((prev) => ({
      ...prev,
      [`${hostelId}-${index}`]: true,
    }));
  };

  const handleImageError = (hostelId, index) => {
    setImagesError((prev) => ({
      ...prev,
      [`${hostelId}-${index}`]: true,
    }));
  };

  const [showPopup, setShowPopup] = useState(false);
  const popupRef = useRef(null);

  // Close popup when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        setShowPopup(false);
      }
    };

    if (showPopup) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showPopup]);

  useEffect(() => {
    if (textRef.current) {
      // Check if the text needs more than 6 lines
      const lineHeight = parseInt(getComputedStyle(textRef.current).lineHeight);
      const maxHeight = lineHeight * 6;
      const actualHeight = textRef.current.scrollHeight;

      const textOverflows = actualHeight > maxHeight;
      setShowToggle(textOverflows);
      setNeedsGradient(textOverflows && !expanded);
    }
  }, [aboutText, expanded]);

  useEffect(() => {
    // Check if the text overflows (more than 3 lines)
    const el = textRef.current;
    if (el) {
      setShowToggle(el.scrollHeight > el.clientHeight);
    }
  }, [aboutText]);

  const handleScrollToSection = () => {
    targetSectionRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    // Load selected rooms and bed selections from localStorage on initial render
    const storedData = JSON.parse(localStorage.getItem("selectedRoomsData"));
    const initialBedSelections = {};

    if (!!storedData?.length) {
      setSelectedRooms(storedData);

      const quantities = storedData.reduce((acc, room) => {
        // acc[room.roomId] = room.count;
        // return acc;
        acc[`${room.roomId}_${room.ratePlanId}`] = room.count;
        return acc;
      }, {});

      setRoomQuantities(quantities);

      // Initialize bed selections with "Lower Bed" if not already set
      storedData.forEach((room) => {
        // initialBedSelections[room.roomId] = room.selectedBed || "Lower Bed";
        initialBedSelections[room.roomId] = room.selectedBed || "";
      });
      setBedSelections(initialBedSelections);
    } else {
      // If no data is stored, initialize bed selections for all dorm rooms
      hostelData?.propertyDetails?.rooms.forEach((room) => {
        if (
          ["Male Dorm", "Female Dorm", "Mixed Dorm", "dorm"].includes(
            room.type
          ) ||
          room.dormitory
        ) {
          // initialBedSelections[room._id] = "Lower Bed";
          initialBedSelections[room._id] = "";
        }
      });
      setBedSelections(initialBedSelections);
    }
  }, [hostelData?.propertyDetails?.rooms]);

  const handleBedSelection = (roomId, bedType) => {
    setBedSelections((prevSelections) => ({
      ...prevSelections,
      [roomId]: bedType,
    }));

    // Update the selected bed in selectedRooms and localStorage
    const updatedRooms = selectedRooms.map((room) =>
      room.roomId === roomId ? { ...room, selectedBed: bedType } : room
    );
    setSelectedRooms(updatedRooms);
    localStorage.setItem("selectedRoomsData", JSON.stringify(updatedRooms));
  };

  useEffect(() => {
    const hostelIdLocal = getItemLocalStorage("hostelId");

    const currentPath = pathname;
    const hostelId = id;
    console.log("hostelId", hostelId, hostelIdLocal);
    if (hostelId !== hostelIdLocal) {
      localStorage.removeItem("selectedRoomsData");
      setRoomQuantities({});
      setSelectedRooms([]);
      setCurrentHostelId(hostelIdLocal);
      setItemLocalStorage("hostelId", id);
    }

    const storedData = JSON.parse(localStorage.getItem("selectedRoomsData"));
    if (storedData) {
      setSelectedRooms(storedData);
      const quantities = storedData.reduce((acc, room) => {
        // acc[room.roomId] = room.count;
        // return acc;
        acc[`${room.roomId}_${room.ratePlanId}`] = room.count;
        return acc;
      }, {});
      setRoomQuantities(quantities);

      const beds = storedData.reduce((acc, room) => {
        if (
          ["Male Dorm", "Female Dorm", "Mixed Dorm", "dorm"].includes(
            room.type
          ) ||
          room.dormitory
        ) {
          // acc[room.roomId] = room.selectedBed || "Lower Bed"; // Default to Lower Bed if not set
          acc[room.roomId] = room.selectedBed || ""; // Default to Lower Bed if not set
        }
        return acc;
      }, {});
      setBedSelections(beds);
    }
  }, [id]);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedData = localStorage.getItem("bookingdata");
      const selectedCurrencySymbol = localStorage.getItem(
        "selectedCurrencySymbol"
      );
      const selectedCurrencyCode = localStorage.getItem("selectedCurrencyCode");

      if (storedData) {
        const parsedData = JSON.parse(storedData);
        setDates([
          parsedData.checkIn ? new Date(parsedData.checkIn) : null,
          parsedData.checkOut ? new Date(parsedData.checkOut) : null,
        ]);
        setGuest(parsedData?.guest || 1);
      }
      if (selectedCurrencySymbol) {
        setCurrencySymbol(selectedCurrencySymbol);
      }
      if (selectedCurrencyCode) {
        setCurrency(selectedCurrencyCode);
      }
      setRoomQuantities({});
    }
  }, [flagUrl2]);

  useEffect(() => {
    const rightBox = document.getElementById("right-box");
    const leftCol = document.getElementById("left-column");

    const onScroll = () => {
      const leftBottom = leftCol.getBoundingClientRect().bottom;
      const rightHeight = rightBox.offsetHeight;
      const padding = 20;

      if (leftBottom - padding <= rightHeight) {
        rightBox.style.position = "absolute";
        rightBox.style.top = leftCol.offsetHeight - rightHeight + "px";
      } else {
        rightBox.style.position = "sticky";
        rightBox.style.top = "120px";
      }
    };

    window.addEventListener("scroll", onScroll);
    return () => window.removeEventListener("scroll", onScroll);
  }, []);

  const isFirstRender = useRef(true);

  useEffect(() => {
    if (!isFirstRender.current) {
      if (id && dates[0] && dates[1]) {
        setIsLoading(true);
        getDetail(id);
        setIsLoading(false);
        setCalendarOpen(false);
      }
    } else {
      isFirstRender.current = false;
    }
  }, [id, dates]);
  const getDetail = async () => {
    try {
      setLoading(true); // Start loading
      const formattedCheckIn = dates[0] ? format(dates[0], "yyyy-MM-dd") : null;
      const formattedCheckOut = dates[1]
        ? format(dates[1], "yyyy-MM-dd")
        : null;
      const response = await getHostelDeatil(
        id,
        formattedCheckIn,
        formattedCheckOut,
        currency
      );
      console.log("response", response);
      if (response?.status == 200) {
        setHostelData(response?.data?.data);

        console.log(response, "========");
        setLoading(false); // Start loading
      } else if (response?.status == 500) {
        setLoading(true);
        toast.error("No Rooms for this currency");
        // router.push("/");
      }
      console.log("response.....", response?.data?.data?.combinedData);
    } catch (error) {
      console.log("error", error);
    }
  };
  console.log("ostelData?.combinedData", hostelData?.propertyDetails);
  const hostel = {
    name: "Namstey Mumbai Back Packers",
    path: "Home > Hostel > 9.80km from city center",
    distance: "5.8 Km away from city center",
    rating: 4.96,
    reviews: 672,
    amenities: ["Wi-Fi", "Shower", "Parking", "Cafe", "Lockers"],
    amenitiesSmall: ["Wi-Fi", "Shower", "Parking", "Cafe"],
    image: "/path/to/hostel-image.jpg",
    rooms: [
      {
        type: "4 Bed Mixed Dorm Ensuite",
        price: "$20 per night",
        image: "/path/to/room-image.jpg",
        isAvailable: true,
      },
      {
        type: "8 Bed Mixed Dorm Ensuite",
        price: "$25 per night",
        image: "/path/to/room-image.jpg",
        isAvailable: true,
      },
    ],
  };

  const amenityIcons = {
    "Wi-Fi": `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/free_wi_fi.svg`,
    Parking: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/parking.svg`,
    Shower: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/shower.svg`,
    Cafe: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/cafe.svg`,
    Lockers: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/lockers.svg`,
  };
  const amenityIconsSmall = {
    "Wi-Fi": `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/free_wi_fi.svg`,
    Parking: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/parking.svg`,
    Shower: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/shower.svg`,
    Cafe: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/cafe.svg`,
  };
  if (hostelData) {
    console.log("hostel data ", hostelData?.combinedData);
  }

  // const getCalender = async () => {
  //   setShowCalender(!showCalender);

  //   // Log dates to ensure they are correctly set
  //   console.log("dates object:", dates);
  //   var start = dates?.checkIn;
  //   var end = dates?.checkOut;
  //   const payload = {
  //     startDate: start,
  //     endDate: end,
  //   };

  //   // Log payload to ensure endDate is present
  //   console.log("payload:", payload);

  //   try {
  //     const res = await getCalenderApi(payload);
  //     console.log("calender response", res);
  //   } catch (error) {
  //     console.error("Error fetching calendar:", error);
  //   }
  // };

  const breadcrumbItems = [
    { label: "Home", href: "/" },
    { label: "Hostel", href: "/search" },
    ...(hostelData?.propertyDetails?.property?.distanceFromCityCenter
      ? [
          {
            label: `${hostelData.propertyDetails.property.distanceFromCityCenter} KM away from city`,
            href: "",
          },
        ]
      : []),
  ];

  // const handleDateChange = (dates) => {
  //   setDates(dates);

  //   const formattedCheckIn = dates[0] ? format(dates[0], "yyyy-MM-dd") : "";
  //   const formattedCheckOut = dates[1] ? format(dates[1], "yyyy-MM-dd") : "";

  //   const existingData = JSON.parse(localStorage.getItem("bookingdata")) || {};

  //   if (formattedCheckIn && formattedCheckOut) {
  //     existingData.checkIn = formattedCheckIn;
  //     existingData.checkOut = formattedCheckOut;
  //   }

  //   localStorage.setItem("bookingdata", JSON.stringify(existingData));
  // };

  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(typeof window !== "undefined" && window.innerWidth <= 768);
    };

    // Initial check
    checkMobile();

    // Add resize listener
    if (typeof window !== "undefined") {
      window.addEventListener("resize", checkMobile);
      return () => window.removeEventListener("resize", checkMobile);
    }
  }, []);
  // const DateInputWithPlaceholder = ({ placeholder }) => {
  //   const formattedStartDate = dates[0]
  //     ? format(dates[0], "dd-MMM-yyyy")
  //     : "Check In";
  //   const formattedEndDate = dates[1]
  //     ? format(dates[1], "dd-MMM-yyyy")
  //     : "Check Out";

  //   return (
  //     <div className="relative">
  //       <div className="flex items-center justify-center gap-1 cursor-pointer">
  //         <DatePicker
  //           calendarClassName="custom-calendar"
  //           selected={dates[0]}
  //           onChange={handleDateChange}
  //           dateFormat="dd-MMM-yyyy"
  //           selectsRange
  //           startDate={dates[0]}
  //           endDate={dates[1]}
  //           placeholderText={placeholder}
  //           open={calendarOpen}
  //           onClickOutside={() => setCalendarOpen(false)}
  //           monthsShown={2}
  //           customInput={
  //             <div className="flex w-full p-1 items-center justify-between cursor-pointer">
  //               <span
  //                 className={`text-xs ${
  //                   formattedStartDate || formattedEndDate
  //                     ? "text-black"
  //                     : "text-gray-400"
  //                 } pointer-events-none font-normal flex`}
  //               >
  //                 <span className="flex text-xs md:text-sm font-manrope font-bold items-center mx-4 border-r border-gray-400 px-2">
  //                   {" "}
  //                   <RiCalendarScheduleFill
  //                     className="text-sm md:text-base font-extrabold mr-0.5"
  //                     onClick={() => setCalendarOpen(true)}
  //                   />
  //                   {formattedStartDate}
  //                 </span>{" "}
  //                 <span className="flex text-xs md:text-sm font-manrope font-bold items-center">
  //                   {" "}
  //                   <RiCalendarScheduleFill
  //                     className="text-sm md:text-base font-extrabold mr-0.5"
  //                     onClick={() => setCalendarOpen(true)}
  //                   />{" "}
  //                   {formattedEndDate}
  //                 </span>
  //               </span>
  //             </div>
  //           }
  //         />
  //       </div>
  //     </div>
  //   );
  // };

  const handleClear = () => {
    setDates([null, null]);
    setNights(0);
    handleChange({
      target: {
        value: {
          checkIn: "",
          checkOut: "",
        },
        name: "dateRange",
      },
    });
  };
  const formatDate = (date) => {
    if (!date) return "";
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-based
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  const handleDateChange = (selectedDates) => {
    if (!Array.isArray(selectedDates)) return;

    setDates(selectedDates);

    const [checkIn, checkOut] = selectedDates || [];
    if (!checkIn) return;

    const formattedCheckIn = formatDate(checkIn);
    const formattedCheckOut = checkOut ? formatDate(checkOut) : "";

    handleChange?.({
      target: {
        name: "dateRange",
        value: {
          checkIn: formattedCheckIn,
          checkOut: formattedCheckOut,
        },
      },
    });
    const existingData = JSON.parse(localStorage.getItem("bookingdata")) || {};

    if (formattedCheckIn && formattedCheckOut) {
      existingData.checkIn = formattedCheckIn;
      existingData.checkOut = formattedCheckOut;
    }

    localStorage.setItem("bookingdata", JSON.stringify(existingData));
    localStorage.removeItem("selectedRoomsData");
    setSelectedRooms([]);
    setRoomQuantities({});
  };

  const formatWeekDay = (day) => {
    return day.substring(0, 3).toLowerCase();
  };

  const getDayClassNames = (date, startDate, endDate) => {
    let classNames = "text-sm flex items-center justify-center";

    const isStart = startDate && isSameDay(date, startDate);
    const isEnd =
      endDate && isSameDay(date, endDate) && isSameMonth(date, endDate);
    const isInRange =
      startDate && endDate && date > startDate && date < endDate;

    const isFirstOfMonth = date.getDate() === 1;
    const isLastOfMonth =
      date.getDate() ===
      new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();

    if (isStart) {
      classNames +=
        " bg-primary-blue !text-black font-semibold rounded-s rounded-e-none";
    } else if (isEnd) {
      classNames +=
        " bg-primary-blue text-black font-semibold rounded-e rounded-s-none";
    } else if (isInRange) {
      classNames += " bg-primary-blue/30 text-black font-semibold rounded-none";

      if (isFirstOfMonth) {
        classNames += " first-date text-black font-semibold rounded-none";
      }

      if (isLastOfMonth) {
        classNames += " last-date text-black font-semibold rounded-none";
      }
    }

    return classNames;
  };

  const DateInputWithPlaceholder = ({
    placeholder,
    shiftLeftOnMobile = false,
  }) => {
    if (!dates) return null;

    const formattedStartDate = dates[0]
      ? format(dates[0], "dd-MMM")
      : "Check In";
    const formattedEndDate = dates[1]
      ? format(dates[1], "dd-MMM")
      : "Check Out";

    return (
      <>
        <div className="relative ">
          {calendarOpen && isMobile && (
            <div
              className="fixed inset-0 bg-black opacity-50 z-40 sm:hidden"
              onClick={() => setCalendarOpen(false)}
            />
          )}
          <div className="flex items-center justify-start sm:justify-center gap-1 cursor-pointer z-30">
            <CiCalendar
              className={`text-xl text-black 
                  `}
              onClick={() => setCalendarOpen(true)}
            />

            <DatePicker
              calendarClassName={`custom-calendar ${
                shiftLeftOnMobile ? "mobile-left-offset" : ""
              }`}
              className="w-full cursor-pointer max-w-[720px]"
              selected={dates[0]}
              onChange={handleDateChange}
              dateFormat="dd-MMM-yyyy"
              selectsRange
              startDate={dates[0]}
              endDate={dates[1]}
              placeholderText={placeholder}
              monthsShown={isMobile ? 1 : 2}
              open={calendarOpen}
              openToDate={calendarMonths[0]}
              minDate={new Date()}
              dayClassName={(date) =>
                getDayClassNames(date, dates[0], dates[1])
              }
              filterDate={(date) => {
                return calendarMonths.some((month) => isSameMonth(date, month));
              }}
              // maxDate={endOfMonth(calendarMonths[0])}
              renderCustomHeader={({
                decreaseMonth,
                increaseMonth,
                prevMonthButtonDisabled,
                nextMonthButtonDisabled,
                customHeaderCount,
              }) => (
                <div>
                  {customHeaderCount === 0 && (
                    <div className="flex items-center xjustify-between py-2 min-h-[2.5rem] custom-date-picker-header max-w-full sm:max-w-[720px] absolute top-[-41px] left-3  md:left-[20px] w-[95%] mx-auto md:w-[700px] lg:w-[700px] z-50 ">
                      <span className="text-sm flex items-center justify-center text-center gap-2 text-[#636c7d] font-normal bg-transparent w-full">
                        {dates?.[0] && dates?.[1] ? (
                          <>
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width={16}
                              height={16}
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth={2}
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="icon icon-tabler icons-tabler-outline icon-tabler-moon-stars"
                            >
                              <path
                                stroke="none"
                                d="M0 0h24v24H0z"
                                fill="none"
                              />
                              <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
                              <path d="M17 4a2 2 0 0 0 2 2a2 2 0 0 0 -2 2a2 2 0 0 0 -2 -2a2 2 0 0 0 2 -2" />
                              <path d="M19 11h2m-1 -1v2" />
                            </svg>
                            {nights} nights selected
                          </>
                        ) : (
                          <span className="flex items-center gap-2 font-normal">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width={16}
                              height={16}
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth={2}
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="icon icon-tabler icons-tabler-outline icon-tabler-calendar"
                            >
                              <path
                                stroke="none"
                                d="M0 0h24v24H0z"
                                fill="none"
                              />
                              <path d="M4 7a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12z" />
                              <path d="M16 3v4" />
                              <path d="M8 3v4" />
                              <path d="M4 11h16" />
                              <path d="M11 15h1" />
                              <path d="M12 15v3" />
                            </svg>
                            Select a check in date
                          </span>
                        )}
                      </span>
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          setCalendarOpen(false);
                        }}
                        className="text-xs md:text-sm sm:pe-0 pe-2 font-semibold text-gray-600 hover:text-gray-800"
                      >
                        OK
                      </button>
                    </div>
                  )}
                  <div className="flex items-center justify-center px-4 py-0 relative">
                    {customHeaderCount === 0 && (
                      <button
                        onClick={() => {
                          decreaseMonth();
                          setCalendarMonths((prev) => [
                            addMonths(prev[0], -1),
                            prev[0],
                          ]);
                        }}
                        disabled={prevMonthButtonDisabled}
                        className="p-2 hover:bg-gray-100 rounded-full disabled:opacity-50 absolute left-[6px]"
                        type="button"
                      >
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        >
                          <path d="M15 18l-6-6 6-6" />
                        </svg>
                      </button>
                    )}
                    <span className="text-lg font-semibold">
                      {format(
                        customHeaderCount === 0
                          ? calendarMonths[0]
                          : calendarMonths[1],
                        "MMMM yyyy"
                      )}
                    </span>
                    {(customHeaderCount === 0 && isMobile) ||
                    customHeaderCount === 1 ? (
                      <button
                        onClick={() => {
                          increaseMonth();
                          setCalendarMonths((prev) =>
                            isMobile
                              ? [addMonths(prev[0], 1), addMonths(prev[0], 2)]
                              : [prev[1], addMonths(prev[1], 1)]
                          );
                        }}
                        disabled={nextMonthButtonDisabled}
                        className="p-2 hover:bg-gray-100 rounded-full disabled:opacity-50 absolute right-[6px]"
                        type="button"
                      >
                        <svg
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        >
                          <path d="M9 18l6-6-6-6" />
                        </svg>
                      </button>
                    ) : null}
                  </div>
                </div>
              )}
              formatWeekDay={formatWeekDay}
              onClickOutside={() => setCalendarOpen(false)}
              customInput={
                <div className="flex w-full p-1 items-center justify-between cursor-pointer">
                  <span
                    className={`text-base ${
                      formattedStartDate || formattedEndDate
                        ? "text-black md:text-black"
                        : "text-gray-400"
                    } pointer-events-none font-semibold`}
                  >
                    {formattedStartDate} - {formattedEndDate}
                  </span>
                </div>
              }
            >
              <div className="flex justify-end">
                <button
                  onClick={handleClear}
                  className="text-sm font-semibold text-gray-600 hover:text-gray-800 mt-[-28px] mr-[20px]"
                >
                  Clear
                </button>
              </div>
            </DatePicker>
          </div>
        </div>
      </>
    );
  };
  const getDateRangeText = () => {
    if (!dates[0] || !dates[1]) return "Select Dates";

    const nights = differenceInDays(dates[1], dates[0]);
    const startDate = format(dates[0], "EEE d MMM, yyyy");
    const endDate = format(dates[1], "EEE d MMM, yyyy");

    return `${nights} night${nights > 1 ? "s" : ""} starting from ${startDate}`;
  };

  // console.log("dates",dates[0],format(dates[0], "yyyy-MM-dd") )

  // useEffect(() => {
  //   // Load room quantities from localStorage on initial render
  //   const storedQuantities = JSON.parse(localStorage.getItem("selectedRoomsData"));
  //   if (storedQuantities) {
  //     setRoomQuantities(storedQuantities.rooms || {});
  //     setSelectedRooms(storedQuantities.rooms || {});
  //   }
  // }, []);

  const updateRoomQuantity = (room, delta, ratePlan) => {
    const roomId = room._id;
    const ratePlanId = ratePlan?.ratePlanId;
    const ratePlanName = ratePlan?.ratePlanName;
    const averagePrice = ratePlan?.averagePrice;

    // Use both roomId and ratePlanId as unique key
    const key = `${roomId}_${ratePlanId}`;

    // Find if this room+ratePlan is already selected
    const existingRoomIndex = selectedRooms.findIndex(
      (r) => r.roomId === roomId && r.ratePlanId === ratePlanId
    );

    const updatedRooms = [...selectedRooms];
    let currentRoomQuantity = 0;
    if (existingRoomIndex !== -1) {
      currentRoomQuantity = updatedRooms[existingRoomIndex].count;
    }

    const roomMaxUnits = room?.maxUnits;
    const maxAllowedUnits = roomMaxUnits > 8 ? 8 : roomMaxUnits;

    const totalRoomCount = updatedRooms
      .filter((r) => r.roomId === roomId)
      .reduce((sum, r) => sum + r.count, 0);

    if (delta > 0) {
      if (maxAllowedUnits === 0) {
        toast.error(`No room available.`);
        return;
      }
      if (totalRoomCount >= maxAllowedUnits) {
        toast.error(
          `You can only add up to ${maxAllowedUnits} units for this room.`
        );
        return;
      }
    }

    if (existingRoomIndex !== -1) {
      const newCount = Math.max(
        updatedRooms[existingRoomIndex].count + delta,
        0
      );
      if (newCount === 0) {
        updatedRooms.splice(existingRoomIndex, 1);
      } else {
        updatedRooms[existingRoomIndex].count = newCount;
      }
    } else {
      if (delta > 0) {
        const updatedRoom = {
          ...room,
          roomId,
          count: 1,
          ratePlanId,
          ratePlanName,
          averagePrice,
        };
        updatedRooms.push(updatedRoom);
      }
    }

    setSelectedRooms(updatedRooms);
    setRoomQuantities(
      updatedRooms.reduce((acc, r) => {
        // Use roomId+ratePlanId as key for quantities
        acc[`${r.roomId}_${r.ratePlanId}`] = r.count;
        return acc;
      }, {})
    );
    localStorage.setItem("selectedRoomsData", JSON.stringify(updatedRooms));
  };

  // const lowerBedCharges = selectedRooms.reduce((total, room) => {
  //   if (bedSelections[room.roomId] === "Lower Bed") {
  //     return (
  //       total +
  //       room?.rate?.weekdayRate?.value *
  //         0.15 *
  //         room.count *
  //         differenceInDays(dates[1], dates[0])
  //     );
  //   }
  //   return total;
  // }, 0);

  const totalCost = selectedRooms.reduce((total, room) => {
    let roomPrice = room?.averagePrice || 0;
    return (
      total +
      (bedSelections[room.roomId] === "Lower Bed"
        ? roomPrice * 1.5
        : roomPrice) *
        room.count *
        differenceInDays(dates[1], dates[0])
    );
  }, 0);

  // Calculate 85% of the total cost
  const fullPayment = totalCost * 0.85;

  // Calculate 15% of the total cost
  const payableNow = totalCost * 0.15 * 1.18 * 1.03;

  const dormRooms =
    hostelData?.propertyDetails?.rooms?.filter((room) => {
      const typeMatch = [
        "Male Dorm",
        "Female Dorm",
        "Mixed Dorm",
        "dorm",
      ].includes(room.type);
      const dormitoryFlag = room.dormitory === true;
      return dormitoryFlag;
    }) || [];

  const privateRooms =
    hostelData?.propertyDetails?.rooms?.filter((room) => {
      const typeMatch = ![
        "Male Dorm",
        "Female Dorm",
        "Mixed Dorm",
        "dorm",
      ].includes(room.type);
      const dormitoryFlag = room.dormitory === false;
      return dormitoryFlag;
    }) || [];

  const handleCheckout = () => {
    router.push("/checkout");
  };

  useEffect(() => {
    setSelectedRooms([]);
  }, [flagUrl2]);

  // New logic to find the lowest price and its room from all rooms and all rates
  let lowestPrice = null;
  let lowestPriceCurrency = null;
  let lowestPriceRoom = null;
  let lowestPriceRatePlanName = null; // <-- add this

  if (hostelData?.propertyDetails?.rooms?.length) {
    hostelData.propertyDetails.rooms.forEach((room) => {
      if (room.rates && room.rates.length > 0) {
        room.rates.forEach((rate) => {
          if (
            typeof rate.averagePrice === "number" &&
            (lowestPrice === null || rate.averagePrice < lowestPrice)
          ) {
            lowestPrice = rate.averagePrice;
            lowestPriceCurrency =
              room.currency || rate.currency || currencySymbol;
            lowestPriceRoom = room;
            lowestPriceRatePlanName = rate.ratePlanName || ""; // <-- add this
          }
        });
      }
    });
  }

  const isWithinSevenDays = (checkInDate) => {
    if (!checkInDate) return false;
    const today = new Date();
    const checkIn = new Date(checkInDate);
    const diffTime = checkIn.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= 7;
  };

  const popuptext = [
    "A hot pick on our platform today.",
    "Popular choice this week!",
    // "One of the most viewed properties.",
    "Rare find, high demand!",
    "Selling like concert tickets!",
    "A top trending property today.",
    "Booked by trendsetters like you.",
    "A rising star among stays.",
    "This place is having a moment.",
    // "Bookings moving like a viral drop!",
    "This stay's got a waitlist vibe!",
    "Selling faster than hotcakes!",
    // "Bookings moving at lightning speed",
    "No cap – hottest listing RN!",
    "Snooze = lose this one hard",
  ];

  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentMessageIndex((prevIndex) =>
        prevIndex === popuptext.length - 1 ? 0 : prevIndex + 1
      );
    }, 6000);

    return () => clearInterval(interval);
  }, [popuptext.length]);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const modalRef = useRef(null);

  const fullText =
    "Affordable and social, our dorm rooms offer shared accommodations with comfy beds, lockers, and a great way to meet fellow travelers.";
  const truncatedText = "Affordable and social, our dorm rooms offer...";

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        setIsModalOpen(false);
      }
    };

    if (isModalOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isModalOpen]);

  const [isModalOpenPrivate, setIsModalOpenPrivate] = useState(false);
  const modalPrivateRef = useRef(null);

  const fullTextPrivate =
    "Enjoy comfort and privacy in our well-appointed private rooms—perfect for couples, solo travelers, or anyone seeking a peaceful retreat.";
  const truncatedTextPrivate = "Enjoy comfort and privacy in our well...";

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        modalPrivateRef.current &&
        !modalPrivateRef.current.contains(event.target)
      ) {
        setIsModalOpenPrivate(false);
      }
    };

    if (isModalOpenPrivate) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isModalOpenPrivate]);
  // Helper to split text into paragraphs every ~3 lines
  // const formatExpandedText = (text) => {
  //   if (!text) return '';
  //     const words = text.split(' ');
  //   let lines = [];
  //   let currentLine = '';
  //   let lineCount = 0;
  //   let paragraph = '';
  //   let result = '';

  //   for (const word of words) {
  //     // Check if adding this word would exceed a reasonable line length
  //     if ((currentLine + ' ' + word).length > 60) { // Adjust based on your font/container
  //       lines.push(currentLine);
  //       currentLine = word;
  //       lineCount++;

  //       // Every 3 lines, create a new paragraph
  //       if (lineCount % 3 === 0) {
  //         // Ensure we don't break mid-sentence
  //         if (currentLine.endsWith('.')) {
  //           result += paragraph + lines.join(' ') + '\n\n';
  //           lines = [];
  //           paragraph = '';
  //         } else {
  //           paragraph += lines.join(' ') + ' ';
  //           lines = [];
  //         }
  //       }
  //     } else {
  //       currentLine = currentLine ? currentLine + ' ' + word : word;
  //     }
  //   }

  //   // Add remaining content
  //   if (currentLine) lines.push(currentLine);
  //   if (lines.length > 0) result += paragraph + lines.join(' ');

  //   return result.trim();
  // };

  const formatExpandedText = (text) => {
    if (!text) return "";
    const words = text.split(" ");
    let lines = [];
    let currentLine = "";
    let lineCount = 0;
    let paragraph = "";
    let result = "";
    let inNumberedList = false;

    for (const word of words) {
      // Check if we're in a numbered list (starts with "1.", "2.", etc.)
      const isListNumber = /^\d+\.$/.test(word);

      // Check if adding this word would exceed line length
      if ((currentLine + " " + word).length > 60) {
        lines.push(currentLine);
        currentLine = word;
        lineCount++;

        // Only consider paragraph breaks if not in a numbered list
        if (!inNumberedList && lineCount % 3 === 0) {
          // Check for proper sentence end (ends with '.' but not a list number)
          const lastToken = lines[lines.length - 1].split(" ").pop();
          const isProperEnd =
            lines[lines.length - 1].endsWith(".") && !/^\d+\.$/.test(lastToken);

          if (isProperEnd) {
            result += paragraph + lines.join(" ") + "\n\n";
            lines = [];
            paragraph = "";
          } else {
            paragraph += lines.join(" ") + " ";
            lines = [];
          }
        }
      } else {
        currentLine = currentLine ? currentLine + " " + word : word;
      }

      // Update numbered list state
      inNumberedList = isListNumber || (inNumberedList && !word.endsWith("."));
    }

    // Add remaining content
    if (currentLine) lines.push(currentLine);
    if (lines.length > 0) result += paragraph + lines.join(" ");

    return result.trim();
  };

  useEffect(() => {
    if (textRef.current) {
      // Always show toggle if text is long enough when collapsed
      const isOverflowing =
        textRef.current.scrollHeight > textRef.current.clientHeight;
      setShowToggle(isOverflowing || expanded);

      // Only show gradient when collapsed and overflowing
      setNeedsGradient(isOverflowing && !expanded);
    }
  }, [aboutText, expanded]);

  // Helper to shuffle images array
  const shuffleArray = (array) => {
    if (!Array.isArray(array)) return array;
    const arr = [...array];
    const videoExt = /\.(mp4|webm|ogg|mov|m4v)$/i;
    const filteredArr = arr.filter((item) => !videoExt.test(item?.objectUrl));
    for (let i = filteredArr.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [filteredArr[i], filteredArr[j]] = [filteredArr[j], filteredArr[i]];
    }
    return filteredArr;
  };

  return loading ? (
    <>
      <Head>
        <title>Loading... | Mixdorm</title>
      </Head>

      <div className="bg-white min-h-screen">
        {/* Header Skeleton */}
        {/* <div className="w-full relative">
      <div className="py-8 relative">
        <div className="h-64 md:h-96 bg-gray-200 animate-pulse"></div>
        <div className="h-8 w-1/2 bg-gray-300 rounded-full absolute top-12 left-1/2 transform -translate-x-1/2 animate-pulse"></div>
      </div>
    </div> */}
        <div className="w-full relative">
          <div className="py-8 relative">
            <Swiper
              modules={[Autoplay]}
              slidesPerView={1.7}
              centeredSlides={true}
              navigation={false}
              pagination={false}
              className="mySwiper"
              spaceBetween={10}
              autoplay={{
                delay: 5000,
                disableOnInteraction: false,
              }}
              initialSlide={1}
            >
              {[1, 2, 3].map((_, index) => (
                <SwiperSlide key={index}>
                  <div className="relative w-full md:h-[554px] h-[40vh] overflow-hidden">
                    <div
                      className={`transition-all duration-300 ease-in-out will-change-transform h-full w-full ${
                        index === 1
                          ? "scale-100 h-[554px]"
                          : "opacity-80 h-[198px] md:h-[439px] mt-9"
                      }`}
                    >
                      <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-3xl" />
                    </div>
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
            <div className="h-8 w-1/2 bg-gray-300 rounded-full absolute top-12 left-1/2 transform -translate-x-1/2 animate-pulse"></div>
          </div>
        </div>

        <div className="container px-4 lg:px-14">
          <div className="mx-auto flex flex-col lg:flex-row gap-11">
            {/* Left Column Skeleton */}
            <main className="max-w-7xl mx-auto w-full lg:w-[891px] flex-1">
              {/* Breadcrumb Skeleton */}
              <div className="flex space-x-2 mb-4">
                <div className="h-4 w-16 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-16 bg-gray-200 rounded animate-pulse"></div>
              </div>

              {/* Title Skeleton */}
              <div className="h-8 w-3/4 bg-gray-200 rounded-full mb-4 animate-pulse"></div>

              {/* Location/Rating Skeleton */}
              <div className="flex space-x-4 mb-6">
                <div className="h-4 w-32 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
              </div>

              {/* Description Skeleton */}
              <div className="space-y-2 mb-6">
                <div className="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-5/6 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-4/6 bg-gray-200 rounded animate-pulse"></div>
              </div>

              {/* Date Picker Skeleton */}
              <div className="h-12 w-full bg-gray-200 rounded-lg mb-8 animate-pulse"></div>

              {/* Rooms Section Skeleton */}
              <div className="space-y-8">
                {/* Dorm Rooms Section */}
                <div>
                  <div className="h-6 w-32 bg-gray-200 rounded-full mb-4 animate-pulse"></div>
                  <div className="space-y-6">
                    {[...Array(2)].map((_, i) => (
                      <div
                        key={i}
                        className="bg-white rounded-3xl shadow-md flex flex-col md:flex-row h-[236px] border animate-pulse"
                      >
                        <div className="w-full md:w-[30%] h-full bg-gray-200 rounded-t-3xl md:rounded-l-3xl md:rounded-tr-none"></div>
                        <div className="p-4 flex-1 flex flex-col justify-between">
                          <div>
                            <div className="h-6 w-3/4 bg-gray-200 rounded-full mb-2"></div>
                            <div className="h-4 w-1/2 bg-gray-200 rounded mb-4"></div>
                            <div className="h-4 w-24 bg-gray-200 rounded mb-4"></div>
                          </div>
                          <div className="flex justify-between items-center">
                            <div className="h-4 w-20 bg-gray-200 rounded"></div>
                            <div className="h-10 w-24 bg-gray-200 rounded-full"></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Private Rooms Section */}
                <div>
                  <div className="h-6 w-40 bg-gray-200 rounded-full mb-4 animate-pulse"></div>
                  <div className="space-y-6">
                    {[...Array(2)].map((_, i) => (
                      <div
                        key={i}
                        className="bg-white rounded-3xl shadow-md flex flex-col md:flex-row h-[236px] border animate-pulse"
                      >
                        <div className="w-full md:w-[30%] h-full bg-gray-200 rounded-t-3xl md:rounded-l-3xl md:rounded-tr-none"></div>
                        <div className="p-4 flex-1 flex flex-col justify-between">
                          <div>
                            <div className="h-6 w-3/4 bg-gray-200 rounded-full mb-2"></div>
                            <div className="h-4 w-1/2 bg-gray-200 rounded mb-4"></div>
                            <div className="h-4 w-24 bg-gray-200 rounded mb-4"></div>
                          </div>
                          <div className="flex justify-between items-center">
                            <div className="h-4 w-20 bg-gray-200 rounded"></div>
                            <div className="h-10 w-24 bg-gray-200 rounded-full"></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Accordion Skeleton */}
              <div className="mt-8 space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-10 w-3/4 bg-gray-200 rounded-lg mb-2"></div>
                  </div>
                ))}
              </div>
            </main>

            {/* Right Column Skeleton */}
            <div className="lg:w-[370px] mb-14">
              <div className="sticky top-[120px]">
                <div className="bg-[#F7F7F7] rounded-3xl p-6 shadow-lg animate-pulse">
                  <div className="h-6 w-3/4 bg-gray-200 rounded-full mb-6"></div>

                  <div className="flex items-center pb-4 border-b gap-4">
                    <div className="w-24 h-24 bg-gray-200 rounded-xl"></div>
                    <div className="flex-1 space-y-2">
                      <div className="h-4 w-full bg-gray-200 rounded"></div>
                      <div className="h-3 w-3/4 bg-gray-200 rounded"></div>
                      <div className="h-3 w-3/4 bg-gray-200 rounded"></div>
                    </div>
                  </div>

                  <div className="py-4 border-b space-y-3">
                    <div className="h-4 w-full bg-gray-200 rounded"></div>
                    <div className="h-4 w-5/6 bg-gray-200 rounded"></div>
                  </div>

                  <div className="py-4 space-y-3">
                    <div className="h-10 w-full bg-gray-200 rounded-full"></div>
                    <div className="h-10 w-full bg-gray-200 rounded-full"></div>
                  </div>

                  <div className="pt-4 flex items-center justify-between pb-5">
                    <div className="h-5 w-16 bg-gray-200 rounded-full"></div>
                    <div className="h-5 w-20 bg-gray-200 rounded-full"></div>
                  </div>

                  <div className="relative mb-4">
                    <div className="h-12 w-full bg-gray-200 rounded-3xl"></div>
                  </div>

                  <div className="flex items-center gap-2 justify-between mt-4">
                    {[...Array(4)].map((_, i) => (
                      <div
                        key={i}
                        className="h-8 w-16 bg-gray-200 rounded"
                      ></div>
                    ))}
                  </div>
                </div>

                <div className="bg-[#e0f7f9] p-5 rounded-xl my-5">
                  <div className="h-4 w-full bg-gray-300 rounded mb-2"></div>
                  <div className="h-4 w-5/6 bg-gray-300 rounded"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  ) : (
    <>
      <Head>
        <title>
          Stay at {hostelData?.propertyDetails?.property?.name} | Book on
          Mixdorm
        </title>
        <link rel="preload" href="/path/to/hero-image.webp" as="image" />
        <link rel="preload" href="/_next/static/css/styles.css" as="style" />
        <link rel="preload" href="/_next/static/chunks/main.js" as="script" />
      </Head>
      <Loader open={loading} />
      {/* {isLoading && <Loader />} */}
      {/* <SearchNavbar /> */}
      <div className="bg-white min-h-screen">
        <div className="w-full relative">
          <div className="py-8 relative">
            <Carousel
              image={hostelData?.propertyDetails?.property?.images || []}
              hostelName={hostelData?.propertyDetails?.property?.name}
            />
            <h1 className="absolute top-12 left-1/2 transform -translate-x-1/2 text-sm md:text-[27px] font-manrope font-bold text-white backdrop-blur-sm text-center px-2  z-20 rounded-3xl">
              {hostelData?.propertyDetails?.property?.name}
            </h1>
            {/* <div className="absolute inset-0 flex items-center justify-center">
              <h1 className="text-[14px] md:text-[27px] font-manrope font-bold text-white backdrop-blur-sm text-center px-4 z-20">
                {hostelData?.propertyDetails?.property?.name}
              </h1>
            </div> */}
          </div>
        </div>
        <div className="container px-4 lg:px-14">
          <div className="mx-auto flex flex-col lg:flex-row gap-11">
            <main
              id="left-column"
              className="max-w-7xl mx-auto w-full lg:w-[830px] flex-1"
            >
              <div className="flex flex-col lg:flex-row lg:space-x-6">
                <div className="flex-1">
                  <Breadcrumb items={breadcrumbItems} />
                  {showBookingBanner && (
                    <div className="flex md:hidden items-center justify-between w-[90%] bg-red-100 text-black  p-2 md:p-3 rounded-xl mb-4 booking-banner relative ">
                      <div className="flex items-center gap-2">
                        <FaFireAlt className="text-red-500 text-xl" />

                        <AnimatePresence mode="wait">
                          <motion.p
                            key={currentMessageIndex}
                            initial={{ y: 10, opacity: 0 }}
                            animate={{ y: 0, opacity: 1 }}
                            exit={{ y: -10, opacity: 0 }}
                            transition={{ duration: 0.3 }}
                            className="text-xs md:text-sm font-medium"
                          >
                            {popuptext[currentMessageIndex]}
                          </motion.p>
                        </AnimatePresence>
                      </div>
                      <button
                        onClick={() => setShowBookingBanner(false)}
                        className="ml-2 text-gray-500 text-lg font-bold hover:text-black"
                        aria-label="cancel"
                      >
                        <RxCrossCircled />
                      </button>
                    </div>
                  )}
                  {/* <h1 className='text-3xl font-bold font-manrope text-black animate-slideFadeUp'>
                    {hostelData?.propertyDetails?.property?.name}
                  </h1> */}

                  <motion.h1
                    className="text-3xl font-bold font-manrope text-black"
                    variants={container}
                    initial="hidden"
                    animate="visible"
                  >
                    {nameWords.map((word, index) => (
                      <motion.span
                        key={index}
                        variants={child}
                        style={{ display: "inline-block", marginRight: "5px" }}
                      >
                        {word + " "}
                      </motion.span>
                    ))}
                  </motion.h1>

                  <div className="flex items-center gap-x-4">
                    <p
                      className="text-sm font-semibold font-manrope py-3 text-black cursor-pointer flex"
                      onClick={() => {
                        // Trigger accordion open
                        window.dispatchEvent(
                          new CustomEvent("openAccordion", {
                            detail: "contactUs",
                          })
                        );

                        // Scroll after short delay
                        setTimeout(() => {
                          const el = document.getElementById("contactUs");
                          el?.scrollIntoView({
                            behavior: "smooth",
                            block: "start",
                          });
                        }, 200);
                      }}
                    >
                      <span>
                        {" "}
                        <PiMapPinAreaFill className="text-lg mr-0.5" />
                      </span>
                      <span>
                        {
                          hostelData?.propertyDetails?.property
                            ?.distanceFromCityCenter
                        }{" "}
                      </span>
                      <span className="text-gray-500 font-normal ml-1">
                        {" "}
                        KM away from city
                      </span>
                    </p>
                    {/* <div
                      className="flex items-center space-x-1 text-yellow-500 mt-1 text-sm cursor-pointer"
                      onClick={() => {
                        // Trigger accordion open
                        window.dispatchEvent(
                          new CustomEvent("openAccordion", {
                            detail: "reviews",
                          })
                        );

                        // Scroll after short delay
                        setTimeout(() => {
                          const el = document.getElementById("review_acc");
                          el?.scrollIntoView({
                            behavior: "smooth",
                            block: "start",
                          });
                        }, 200);
                      }}
                    >
                      <ReactStars
                        count={1}
                        value={
                          hostelData?.propertyDetails?.property?.starRating || 5
                        }
                        size={24}
                        edit={false}
                        isHalf={true}
                      />
                      <span className="text-black font-bold text-sm min-w-2">
                        {hostelData?.propertyDetails?.property?.starRating ||
                          "5"}{" "}
                        <span className="text-gray-400 font-normal">
                          (
                          {hostelData?.propertyDetails?.property?.googleReviews
                            ?.length?.overall || "0"}{" "}
                          reviews)
                        </span>
                       </span>
                      </div> */}
                  </div>

                  {/* <p className='text-xl font-bold '>
                    {" "}
                    {hostelData?.combinedData?.property?.name}
                  </p> */}
                  {/* <div className='flex items-center space-x-1 text-yellow-500 py-2'>
      

                    <ReactStars
                      count={1}
                      value={
                        hostelData?.propertyDetails?.property?.starRating || "5"
                      }
                      size={24}
                      edit={false}
                      isHalf={true}
                    />
                   
                    <span className='text-gray-600 ml-2'>
                      {hostelData?.propertyDetails?.property?.starRating || "5"}{" "}
                      (
                      {hostelData?.propertyDetails?.property?.googleReviews?.length
                        ?.overall || "0"}{" "}
                      reviews)
                    </span>
                  </div> */}
                  {/* <p className='text-[#888888] text-sm py-2 w-full'>
                    {hostelData?.propertyDetails?.property?.type}
                  </p> */}
                  {/* <p className='text-[#888888] text-sm py-2 w-full'>
                    {hostelData?.propertyDetails?.property?.aboutUs}
                  </p> */}
                  {/* <p className="text-[#888888] text-sm py-2 w-full overflow-hidden text-ellipsis cursor-pointer">
                    {aboutText}
                  </p> */}
                  {/* <div>
                    <p
                      ref={textRef}
                      className={`text-[#888888] text-sm py-2 w-full overflow-hidden text-ellipsis cursor-pointer transition-all duration-300 ${
                        !expanded ? "line-clamp-6" : ""
                      }`}
                    >
                      {aboutText}
                    </p>
                    {showToggle && (
                      <button
                        className="text-black mt-1 float-end text-sm"
                        onClick={() => setExpanded(!expanded)}
                      >
                        {expanded ? "View Less" : "Read More"}
                      </button>
                    )}
                  </div> */}
                  {/* <div className='relative'>
                    <p
                      ref={textRef}
                      className={`text-[#888888] text-sm py-2 w-full transition-all duration-300 ${
                        !expanded ? "line-clamp-3" : ""
                      }`}
                      style={{ height: expanded ? "auto" : "auto" }}
                    >
                      {aboutText}
                    </p>

                    {needsGradient && (
                      <div className='absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent pointer-events-none' />
                    )}

                    {showToggle && (
                      <button
                        className='text-black mt-1 float-end text-sm font-semibold'
                        onClick={() => setExpanded(!expanded)}
                      >
                        {expanded ? "View Less" : "Read More"}
                      </button>
                    )}
                  </div> */}
                  <div className="relative">
                    <p
                      ref={textRef}
                      className={`text-[#888888] text-sm py-2 w-full transition-all duration-300 ${
                        !expanded ? "line-clamp-3" : ""
                      }`}
                      style={{
                        whiteSpace: "pre-line", // This preserves the line breaks we'll add
                      }}
                    >
                      {expanded ? formatExpandedText(aboutText) : aboutText}
                    </p>

                    {needsGradient && !expanded && (
                      <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent pointer-events-none" />
                    )}

                    {showToggle && (
                      <button
                        className="text-black mt-1 float-end text-sm font-semibold"
                        onClick={() => setExpanded(!expanded)}
                      >
                        {expanded ? "View Less" : "Read More"}
                      </button>
                    )}
                  </div>

                  <button className="mt-4 bg-[#F7F7F7] text-black text-xs md:text-lg font-manrope font-bold px-4 py-2 rounded-lg w-full flex  justify-between items-center whitespace-nowrap min-w-20 min-h-2">
                    <span className="text-base">Reserve Your Escape</span>
                    <div
                      className="flex items-center space-x-2 text-black text-sm font-bold font-manrope"
                      onClick={() => setCalendarOpen(true)}
                    >
                      <DateInputWithPlaceholder
                        placeholder="Select Dates"
                        shiftLeftOnMobile
                      />
                    </div>
                  </button>
                </div>
              </div>
              <section ref={targetSectionRef} className="hidden md:block ">
                {!!dormRooms?.length && (
                  <div className="mt-8">
                    <h2 className="text-2xl font-bold mb-4">Dorm Rooms</h2>
                    <div className="space-y-6">
                      {dormRooms?.map((room, roomIndex) => {
                        const imageCount =
                          hostelData?.propertyDetails?.property?.images
                            ?.length || 0;
                        const initialSlide = roomIndex % imageCount;
                        return (
                          <div
                            key={roomIndex}
                            className="bg-white rounded-3xl shadow-md flex flex-col md:flex-row relative border h-[240px] "
                          >
                            {/* {hostelData?.propertyDetails?.property
                            ?.freeCancellationAvailable && (
                            <Image
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/freecancel.svg`}
                              alt="Free Cancellation"
                              width={150}
                              height={35}
                              objectFit="contain"
                              className="absolute left-0 top-[1.5rem] z-10 transform translate-x-[-10%] animate-pulse"
                              loading="lazy"
                            />
                          )} */}
                            {/* {hostelData?.propertyDetails?.property?.images?.[0]
                              ?.objectUrl && (
                              <Swiper
                                autoplay={{
                                  delay: 3000,
                                  disableOnInteraction: false,
                                }}
                                loop
                                speed={1000}
                                spaceBetween={0}
                                slidesPerView={1}
                                modules={[Pagination, Navigation, Autoplay]}
                                className="mySwiper myCustomSwiper m-0 w-[30%] h-full"
                              >
                                {hostelData?.propertyDetails?.property?.images?.map(
                                  (img, index) => (
                                    <SwiperSlide
                                      key={index}
                                      className="w-full bg-transparent h-full"
                                    >
                                      {isLoading && (
                                        <div className="w-full h-full bg-gray-200 animate-pulse rounded-t-2xl">
                                          <div className="h-full w-full bg-gray-300"></div>
                                        </div>
                                      )}
                                      <div className="relative w-full h-full">
                                        <Image
                                          // src={`https://${img.url}`}
                                          src={img?.objectUrl}
                                          alt={`Image ${index + 1}`}
                                          fill
                                          className="md:rounded-l-3xl md:rounded-tr-none rounded-t-3xl object-cover w-full  md:w-full h-full"
                                          loading="lazy"
                                        />
                                      </div>
                                    </SwiperSlide>
                                  )
                                )}
                              </Swiper>
                            )} */}
                            {room?.images?.length > 0 ? (
                              // Show room images
                              <Swiper
                                autoplay={{
                                  delay: 3000,
                                  disableOnInteraction: false,
                                }}
                                loop
                                speed={1000}
                                spaceBetween={0}
                                slidesPerView={1}
                                modules={[Autoplay]}
                                className="min-w-[250px] w-[250px] h-full"
                              >
                                {shuffleArray(room.images).map((img, index) => {
                                  const imageKey = `${hostelData._id}-room-${index}`;
                                  const isLoaded = imagesLoaded[imageKey];
                                  const hasError = imagesError[imageKey];

                                  return (
                                    <SwiperSlide
                                      key={index}
                                      className="mySwiper myCustomSwiper m-0 w-full h-full md:rounded-l-3xl md:rounded-tr-none rounded-t-3xl"
                                    >
                                      <div className="relative w-full h-full">
                                        {(!isLoaded || hasError) && (
                                          <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-t-2xl flex items-center justify-center z-0">
                                            <h1 className="text-white font-bold font-manrope text-4xl">
                                              Mixdorm
                                            </h1>
                                          </div>
                                        )}
                                        {!hasError && (
                                          <Image
                                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/${img?.objectUrl}`}
                                            alt={`Image ${index + 1}`}
                                            fill
                                            className={`md:rounded-l-3xl md:rounded-tr-none rounded-t-3xl object-cover transition-opacity duration-300 ${
                                              !isLoaded
                                                ? "opacity-0"
                                                : "opacity-100"
                                            }`}
                                            loading={
                                              index === 0 ? "eager" : "lazy"
                                            }
                                            quality={85}
                                            priority={index === 0}
                                            onLoadingComplete={() =>
                                              handleImageLoad(
                                                hostelData._id,
                                                index
                                              )
                                            }
                                            onError={() =>
                                              handleImageError(
                                                hostelData._id,
                                                index
                                              )
                                            }
                                          />
                                        )}
                                      </div>
                                    </SwiperSlide>
                                  );
                                })}
                              </Swiper>
                            ) : hostelData?.propertyDetails?.property?.images
                                ?.length > 0 ? (
                              // Show hostelData property images
                              <Swiper
                                autoplay={{
                                  delay: 3000,
                                  disableOnInteraction: false,
                                }}
                                loop
                                speed={1000}
                                spaceBetween={0}
                                slidesPerView={1}
                                modules={[Autoplay]}
                                className="min-w-[250px] w-[250px] h-full"
                              >
                                {shuffleArray(
                                  hostelData.propertyDetails.property.images
                                ).map((img, index) => (
                                  <SwiperSlide
                                    key={index}
                                    className="mySwiper myCustomSwiper m-0 w-full h-full md:rounded-l-3xl md:rounded-tr-none rounded-t-3xl"
                                  >
                                    <div className="relative w-full h-full">
                                      <Image
                                        src={
                                          img?.objectUrl.startsWith("http")
                                            ? img.objectUrl
                                            : `https://${img?.objectUrl}`
                                        }
                                        alt={`Image ${index + 1}`}
                                        fill
                                        className="md:rounded-l-3xl md:rounded-tr-none rounded-t-3xl object-cover w-full h-full"
                                        loading={index === 0 ? "eager" : "lazy"}
                                        quality={85}
                                        priority={index === 0}
                                      />
                                    </div>
                                  </SwiperSlide>
                                ))}
                              </Swiper>
                            ) : (
                              // Fallback skeleton
                              <div className="min-w-[250px] w-[250px] h-full bg-gray-200 animate-pulse rounded-t-2xl md:rounded-l-3xl md:rounded-tr-none">
                                <div className="h-full w-full bg-gray-300 animate-pulse rounded-t-2xl flex items-center justify-center">
                                  <h1 className="text-white font-bold font-manrope text-4xl">
                                    Mixdorm
                                  </h1>
                                </div>
                              </div>
                            )}

                            <div className="ml-0 pt-4 px-4 md:mt-0 flex flex-col justify-between flex-1 overflow-hidden">
                              <h2 className="text-xl font-manrope font-bold mb-1">
                                {room?.name}
                              </h2>
                              <p className="text-xs text-gray-700">
                                {room.description}
                                Affordable and social, our dorm rooms offer
                                shared accommodations with comfy beds, lockers,
                                and a great way to meet fellow travelers.{" "}
                              </p>
                              {/* <Image
                                    src={assets?.Label}
                                    alt={hostel?.name}
                                    width={44}
                                    height={44}
                                    className="absolute bottom-0 left-[190%] mr-[10px]"
                                  /> */}

                              {/* <div className="font-manrope font-normal text-base leading-5 text-[#888888] mb-2">
                              Taxes Not Included
                            </div> */}

                              {/* <p className="text-gray-600 text-base py-1">
                                {hostel?.distance}
                              </p> */}
                              {/* <div className="flex items-center space-x-1 text-yellow-500">
                                {[...Array(Math.floor(hostel?.rating))].map(
                                  (_, i) => (
                                    <svg
                                      key={i}
                                      className="w-4 h-4 fill-current"
                                      viewBox="0 0 20 20"
                                    >
                                      <path d="M10 15l-5.878 3.09 1.123-6.54L.487 7.906l6.568-.955L10 .905l2.945 6.046 6.568.955-4.758 3.644 1.123 6.54z" />
                                    </svg>
                                  )
                                )}
                                {hostel?.rating % 1 !== 0 && (
                                  <svg
                                    className="w-4 h-4 fill-current"
                                    viewBox="0 0 20 20"
                                  >
                                    <path d="M10 15l-5.878 3.09 1.123-6.54L.487 7.906l6.568-.955L10 .905v14.095z" />
                                  </svg>
                                )}
                                <span className="text-gray-600 ml-2 text-xs ">
                                  {hostel?.rating} ({hostel?.reviews} reviews)
                                </span>
                              </div> */}
                              {/* Discount badge */}
                              {/* <div className="absolute top-0 right-5">
                              {" "}
                              <div className="w-[44px] h-[44px] relative">
                                <svg
                                  width="44"
                                  height="44"
                                  viewBox="0 0 44 44"
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="absolute top-0 left-0"
                                >
                                  <path
                                    d="M0 0H44V34L22 44L0 34V0Z"
                                    fill="#FFC800"
                                  />
                                </svg>
                                <span className="absolute inset-0 flex items-center justify-center text-black font-bold text-sm ">
                                  -25%
                                </span>
                              </div>
                            </div> */}
                              {/* <div className='absolute top-0 right-5'>
                                <motion.div
                                  className='w-[44px] h-[44px] relative'
                                  animate={{ scale: [1, 1.1, 1] }}
                                  transition={{
                                    duration: 1.5,
                                    repeat: Infinity,
                                    ease: "easeInOut",
                                  }}
                                >
                                  <svg
                                    width='44'
                                    height='44'
                                    viewBox='0 0 44 44'
                                    xmlns='http://www.w3.org/2000/svg'
                                    className='absolute top-0 left-0'
                                  >
                                    <path
                                      d='M0 0H44V34L22 44L0 34V0Z'
                                      fill='#FFC800'
                                    />
                                  </svg>
                                  <span className='absolute inset-0 flex items-center justify-center text-black font-bold text-sm'>
                                    -25%
                                  </span>
                                </motion.div>
                              </div> */}
                              <div className="flex space-x-4 font-medium text-sm text-black my-1 ">
                                {hostel?.amenities.map((amenity, index) => (
                                  <span
                                    key={index}
                                    className="flex items-center space-x-1"
                                  >
                                    <Image
                                      src={amenityIcons[amenity]}
                                      className="w-4 h-4 fill-current text-primary-blue"
                                      alt={amenity}
                                      width={16}
                                      height={16}
                                      loading="lazy"
                                    />
                                    <span>{amenity}</span>
                                  </span>
                                ))}
                              </div>
                              {/* <div className="flex flex-col w-full md:flex-row md:justify-end"> */}
                              {/* <div className="flex items-center">
                                <button
                                  className={`border border-[#EEEEEE] text-black px-5 py-2 rounded-3xl flex items-center hover:bg-[#40E0D033] text-sm ${
                                    bedSelections[room._id] === "Lower Bed"
                                      ? "bg-[#40E0D033]"
                                      : ""
                                  }`}
                                  onClick={() =>
                                    handleBedSelection(room._id, "Lower Bed")
                                  }
                                >
                                  Lower Bed
                                </button>
                                <button
                                  className={`border border-[#EEEEEE] text-black px-5 py-2 rounded-3xl flex items-center hover:bg-[#40E0D033] text-sm ml-2 ${
                                    bedSelections[room._id] === "Upper Bed"
                                      ? "bg-[#40E0D033]"
                                      : ""
                                  }`}
                                  onClick={() =>
                                    handleBedSelection(room._id, "Upper Bed")
                                  }
                                >
                                  Upper Bed
                                </button>
                              </div> */}
                              {/* <span className="text-black font-bold md:text-right py-5 px-2">
                                {currencySymbol}
                                {room?.rate?.weekdayRate?.value} / Night
                              </span> */}
                              {/* </div> */}
                              <div className="flex items-center gap-3  overflow-x-auto scrollbar-thin scrollbar-thumb-blue-500 mt-1 scrollbar-track-transparent overflow-y-hidden">
                                {/* <p className="text-sm flex items-center">
                                Available calendar{" "}
                                <IoIosArrowDown className="mt-1 ml-0.5 text-sm font-normal" />
                              </p> */}

                                {room?.rates
                                  ?.filter((rate) => rate.averagePrice > 0)
                                  ?.map((rate, idx) => (
                                    <div
                                      key={idx}
                                      className="flex-shrink-0 rounded-lg border border-black/50 pt-2 text-black my-1 w-[173px]"
                                    >
                                      <p className="text-base px-2 font-semibold">
                                        {currencySymbol}
                                        {rate.averagePrice}
                                      </p>
                                      <p className="text-xxs px-2">
                                        {{
                                          EP: "Room Only",
                                          CP: "With Breakfast",
                                          MAP: "With Breakfast + L/D",
                                          AP: "All Meals",
                                        }[rate.ratePlanName] ||
                                          rate.ratePlanName}
                                      </p>
                                      {!roomQuantities[
                                        `${room._id}_${rate.ratePlanId}`
                                      ] ? (
                                        room?.maxUnits === 0 ? (
                                          <div className="relative group">
                                            <button
                                              className="bg-red-500 rounded-b-[7px] text-white text-sm px-3 py-1 w-full font-medium cursor-not-allowed font-manrope text-center flex items-center justify-center gap-2 mx-auto mt-2"
                                              disabled
                                            >
                                              <span className="animate-pulse">
                                                Sold Out
                                              </span>
                                            </button>
                                            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-1 py-1 bg-black text-white text-xxs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                                              This room is currently unavailable
                                            </div>
                                          </div>
                                        ) : (
                                          <button
                                            className="bg-black rounded-b-[7px] text-white text-sm px-3 py-1 w-full font-medium hover:bg-primary-blue hover:text-black font-manrope mx-auto mt-2"
                                            onClick={() =>
                                              updateRoomQuantity(room, 1, rate)
                                            }
                                          >
                                            Book
                                          </button>
                                        )
                                      ) : (
                                        <div className="bg-black rounded-b-[7px] w-full py-1 mt-2">
                                          <div className="flex items-center justify-center w-full rounded-[4px] mx-auto">
                                            <Button
                                              onClick={() =>
                                                updateRoomQuantity(
                                                  room,
                                                  -1,
                                                  rate
                                                )
                                              }
                                              className="w-5 h-5 min-w-0 p-0 rounded-full flex items-center justify-center bg-white text-black "
                                            >
                                              <FaMinus />
                                            </Button>
                                            <input
                                              type="number"
                                              value={
                                                roomQuantities[
                                                  `${room._id}_${rate.ratePlanId}`
                                                ] || 0
                                              }
                                              className="appearance-none w-10 h-5 text-center text-white placeholder:text-white mx-2"
                                              readOnly
                                            />
                                            <Button
                                              onClick={() =>
                                                updateRoomQuantity(
                                                  room,
                                                  1,
                                                  rate
                                                )
                                              }
                                              className="w-5 h-5 min-w-0 p-0 rounded-full flex items-center justify-center bg-white text-black "
                                            >
                                              <FaPlus />
                                            </Button>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  ))}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                      {/* <div className="flex justify-center mt-4">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="px-3 py-1 mx-1 bg-gray-300 rounded disabled:opacity-50"
                  >
                    Previous
                  </button>
                  {[...Array(totalPages).keys()].map((page) => (
                    <button
                      key={page + 1}
                      onClick={() => handlePageChange(page + 1)}
                      className={`px-3 py-1 mx-1 ${
                        currentPage === page + 1
                          ? "bg-blue-500 text-white"
                          : "bg-gray-300"
                      } rounded`}
                    >
                      {page + 1}
                    </button>
                  ))}
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="px-3 py-1 mx-1 bg-gray-300 rounded disabled:opacity-50"
                  >
                    Next
                  </button>
                </div> */}
                    </div>
                  </div>
                )}
                {!!privateRooms?.length && (
                  <div className="mt-8">
                    <h2 className="text-2xl font-bold mb-4">Private Rooms</h2>
                    <div className="space-y-6">
                      {privateRooms?.map((room, index) => {
                        let photoUrl =
                          hostelData?.propertyDetails?.property?.images?.[
                            index %
                              hostelData?.propertyDetails?.property?.images
                                ?.length
                          ]?.objectUrl;

                        if (photoUrl && !photoUrl.startsWith("http")) {
                          photoUrl = `https://${photoUrl}`;
                        }

                        const fallbackImage =
                          "https://via.placeholder.com/200x100.png?text=No+Image";

                        const weekdayRate = room?.rate?.weekdayRate?.value;
                        const weekendRate = room?.rate?.weekendRate?.value;

                        // Calculate initial slide index based on room index
                        const imageCount =
                          hostelData?.propertyDetails?.property?.images
                            ?.length || 0;
                        const initialSlide = Math.floor(
                          Math.random() * imageCount
                        );
                        {
                          room?.images?.length > 0 ? (
                            <Swiper
                              autoplay={{
                                delay: 3000,
                                disableOnInteraction: false,
                              }}
                              loop
                              speed={1000}
                              spaceBetween={0}
                              slidesPerView={1}
                              modules={[Autoplay]}
                              initialSlide={initialSlide}
                              preloadImages={false}
                              lazy={{
                                loadPrevNext: true,
                                loadPrevNextAmount: 1,
                              }}
                              watchSlidesProgress
                              className="min-w-[250px] w-[250px] h-full"
                            >
                              {room?.images?.map((img, index) => {
                                const imageKey = `${hostelData._id}-${index}`;
                                const isLoaded = imagesLoaded[imageKey];
                                const hasError = imagesError[imageKey];

                                return (
                                  <SwiperSlide
                                    key={index}
                                    className="mySwiper myCustomSwiper m-0 w-full h-full md:rounded-l-3xl md:rounded-tr-none rounded-t-3xl"
                                  >
                                    <div className="relative w-full h-full">
                                      {/* Fallback state */}
                                      {(!isLoaded || hasError) && (
                                        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-t-2xl flex items-center justify-center z-0">
                                          <h1 className="text-white font-bold font-manrope text-4xl">
                                            MixDorm
                                          </h1>
                                        </div>
                                      )}

                                      <Image
                                        src={
                                          !hasError && img?.objectUrl
                                            ? `${process.env.NEXT_PUBLIC_S3_URL_FE}/${img?.objectUrl}`
                                            : "/fallback/mixdorm-default.jpg" // fallback image
                                        }
                                        alt={`Image ${index + 1}`}
                                        fill
                                        className={`md:rounded-l-3xl md:rounded-tr-none rounded-t-3xl object-cover transition-opacity duration-300 ${
                                          !isLoaded
                                            ? "opacity-0"
                                            : "opacity-100"
                                        }`}
                                        loading={index === 0 ? "eager" : "lazy"}
                                        quality={85}
                                        priority={index === 0}
                                        onLoadingComplete={() =>
                                          handleImageLoad(hostelData._id, index)
                                        }
                                        onError={() =>
                                          handleImageError(
                                            hostelData._id,
                                            index
                                          )
                                        }
                                      />
                                    </div>
                                  </SwiperSlide>
                                );
                              })}
                            </Swiper>
                          ) : (
                            <div className="min-w-[250px] w-[250px] h-full bg-gray-200 animate-pulse rounded-t-2xl md:rounded-l-3xl md:rounded-tr-none">
                              <div className="h-full w-full bg-gray-300 animate-pulse rounded-t-2xl flex items-center justify-center">
                                <h1 className="text-white font-bold font-manrope text-4xl">
                                  Mixdorm
                                </h1>
                              </div>
                            </div>
                          );
                        }

                        return (
                          <div
                            key={index}
                            className="bg-white rounded-3xl shadow-md flex flex-col md:flex-row relative border h-[240px]"
                          >
                            {/* {hostelData?.propertyDetails?.property
                               ?.freeCancellationAvailable && (
                               <Image
                                 src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/freecancel.svg`}
                                 alt="Free Cancellation"
                                 width={150}
                                 height={35}
                                 objectFit="contain"
                                 className="absolute left-0 top-[1.5rem] z-10 transform translate-x-[-10%] animate-pulse"
                                 loading="lazy"
                               />
                             )} */}
                            {/* {hostelData?.propertyDetails?.property?.images?.[0]
                              ?.objectUrl && (
                              <Swiper
                                autoplay={{
                                  delay: 3000,
                                  disableOnInteraction: false,
                                }}
                                loop
                                speed={1000}
                                spaceBetween={0}
                                slidesPerView={1}
                                modules={[Pagination, Navigation, Autoplay]}
                                className="mySwiper myCustomSwiper m-0 w-[30%] h-full"
                              >
                                {hostelData?.propertyDetails?.property?.images?.map(
                                  (img, index) => (
                                    <SwiperSlide
                                      key={index}
                                      className="w-full bg-transparent h-full"
                                    >
                                      {isLoading && (
                                        <div className="w-full h-full bg-gray-200 animate-pulse rounded-t-2xl">
                                          <div className="h-full w-full bg-gray-300"></div>
                                        </div>
                                      )}
                                      <div className="relative w-full h-full">
                                        <Image
                                          // src={`https://${img.url}`}
                                          src={img?.objectUrl}
                                          alt={`Image ${index + 1}`}
                                          fill
                                          className="md:rounded-l-3xl md:rounded-tr-none rounded-t-3xl object-cover w-full  md:w-full h-full"
                                          loading="lazy"
                                        />
                                      </div>
                                    </SwiperSlide>
                                  )
                                )}
                              </Swiper>
                            )} */}
                            {room?.images?.length > 0 ? (
                              // ROOM IMAGES
                              <Swiper
                                autoplay={{
                                  delay: 3000,
                                  disableOnInteraction: false,
                                }}
                                loop
                                speed={1000}
                                spaceBetween={0}
                                slidesPerView={1}
                                modules={[Autoplay]}
                                initialSlide={initialSlide}
                                preloadImages={false}
                                lazy={{
                                  loadPrevNext: true,
                                  loadPrevNextAmount: 1,
                                }}
                                watchSlidesProgress
                                className="min-w-[250px] w-[250px] h-full"
                              >
                                {shuffleArray(room.images).map((img, index) => {
                                  const imageKey = `${hostelData._id}-room-${index}`;
                                  const isLoaded = imagesLoaded[imageKey];
                                  const hasError = imagesError[imageKey];

                                  return (
                                    <SwiperSlide
                                      key={index}
                                      className="mySwiper myCustomSwiper m-0 w-full h-full md:rounded-l-3xl md:rounded-tr-none rounded-t-3xl"
                                    >
                                      <div className="relative w-full h-full">
                                        {(!isLoaded || hasError) && (
                                          <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-t-2xl flex items-center justify-center z-0">
                                            <h1 className="text-white font-bold font-manrope text-4xl">
                                              Mixdorm
                                            </h1>
                                          </div>
                                        )}

                                        {!hasError && (
                                          <Image
                                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/${img?.objectUrl}`}
                                            alt={`Image ${index + 1}`}
                                            fill
                                            className={`md:rounded-l-3xl md:rounded-tr-none rounded-t-3xl object-cover transition-opacity duration-300 ${
                                              !isLoaded
                                                ? "opacity-0"
                                                : "opacity-100"
                                            }`}
                                            loading={
                                              index === 0 ? "eager" : "lazy"
                                            }
                                            quality={85}
                                            priority={index === 0}
                                            onLoadingComplete={() =>
                                              handleImageLoad(
                                                hostelData._id,
                                                index
                                              )
                                            }
                                            onError={() =>
                                              handleImageError(
                                                hostelData._id,
                                                index
                                              )
                                            }
                                          />
                                        )}
                                      </div>
                                    </SwiperSlide>
                                  );
                                })}
                              </Swiper>
                            ) : hostelData?.propertyDetails?.property?.images
                                ?.length > 0 ? (
                              // HOSTEL PROPERTY IMAGES
                              <Swiper
                                autoplay={{
                                  delay: 3000,
                                  disableOnInteraction: false,
                                }}
                                loop
                                speed={1000}
                                spaceBetween={0}
                                slidesPerView={1}
                                modules={[Pagination, Navigation, Autoplay]}
                                className="mySwiper myCustomSwiper m-0 min-w-[250px] w-[250px] h-full"
                              >
                                {shuffleArray(
                                  hostelData.propertyDetails.property.images
                                ).map((img, index) => (
                                  <SwiperSlide
                                    key={index}
                                    className="w-full bg-transparent h-full relative"
                                  >
                                    {!isLoaded && (
                                      <div className="w-full h-full bg-gray-200 animate-pulse rounded-t-2xl absolute flex items-center justify-center">
                                        <h1 className="text-white font-bold font-manrope text-4xl">
                                          Mixdorm
                                        </h1>
                                      </div>
                                    )}
                                    <div className="relative w-full h-full">
                                      <Image
                                        src={img?.objectUrl}
                                        alt={`Image ${index + 1}`}
                                        fill
                                        className="md:rounded-l-3xl md:rounded-tr-none rounded-t-3xl object-cover w-full md:w-full h-full"
                                        loading={index === 0 ? "eager" : "lazy"}
                                        quality={85}
                                        priority={index === 0}
                                        onLoadingComplete={() =>
                                          setIsLoaded(true)
                                        }
                                      />
                                    </div>
                                  </SwiperSlide>
                                ))}
                              </Swiper>
                            ) : (
                              // FALLBACK
                              <div className="min-w-[250px] w-[250px] h-full bg-gray-200 animate-pulse rounded-t-2xl md:rounded-l-3xl md:rounded-tr-none">
                                <div className="h-full w-full bg-gray-300 animate-pulse rounded-t-2xl flex items-center justify-center">
                                  <h1 className="text-white font-bold font-manrope text-4xl">
                                    Mixdorm
                                  </h1>
                                </div>
                              </div>
                            )}

                            <div className="ml-0 pt-4 pb-2 px-4 md:mt-0 flex flex-col justify-between flex-1 overflow-hidden">
                              <h2 className="text-xl font-manrope font-bold mb-1">
                                {room?.name}
                              </h2>
                              <p className="text-xs text-gray-700">
                                {" "}
                                Enjoy comfort and privacy in our well-appointed
                                private rooms—perfect for couples, solo
                                travelers, or anyone seeking a peaceful retreat.{" "}
                              </p>
                              {/* <div className="font-manrope font-normal text-base leading-5 text-[#888888]  mt-2">
                                     Taxes Not Included
                                   </div> */}
                              {/* Discount badge */}
                              {/* <div className='absolute top-0 right-5'>
                                 <motion.div
                                   className='w-[44px] h-[44px] relative'
                                   animate={{ scale: [1, 1.1, 1] }}
                                   transition={{
                                     duration: 1.5,
                                     repeat: Infinity,
                                     ease: "easeInOut",
                                   }}
                                 >
                                   <svg
                                     width='44'
                                     height='44'
                                     viewBox='0 0 44 44'
                                     xmlns='http:www.w3.org/2000/svg'
                                     className='absolute top-0 left-0'
                                   >
                                     <path
                                       d='M0 0H44V34L22 44L0 34V0Z'
                                       fill='#FFC800'
                                     />
                                   </svg>
                                   <span className='absolute inset-0 flex items-center justify-center text-black font-bold text-sm'>
                                     -25%
                                   </span>
                                 </motion.div>
                               </div> */}
                              <div className="flex space-x-4 font-medium text-sm text-black my-1 ">
                                {hostel?.amenities?.map((amenity, index) => (
                                  <span
                                    key={index}
                                    className="flex items-center space-x-1"
                                  >
                                    <Image
                                      src={amenityIcons[amenity]}
                                      className="w-4 h-4 fill-current text-primary-blue"
                                      alt={amenity}
                                      loading="lazy"
                                      width={16}
                                      height={16}
                                    />
                                    <span>{amenity}</span>
                                  </span>
                                ))}
                              </div>
                              {/* <div className="flex flex-col w-full md:flex-row md:justify-end"> */}
                              {/* <div className="flex items-center">
                                     <button
                                       className={`border border-[#EEEEEE] text-black px-5 py-2 rounded-3xl flex items-center hover:bg-[#40E0D033] text-sm ${
                                         bedSelections[room._id] === "Lower Bed"
                                           ? "bg-[#40E0D033]"
                                           : ""
                                       }`}
                                       onClick={() =>
                                     handleBedSelection(room._id, "Lower Bed")
                                       }
                                     >
                                       Lower Bed
                                     </button>
                                     <button
                                       className={`border border-[#EEEEEE] text-black px-5 py-2 rounded-3xl flex items-center hover:bg-[#40E0D033] text-sm ml-2 ${
                                         bedSelections[room._id] === "Upper Bed"
                                           ? "bg-[#40E0D033]"
                                           : ""
                                       }`}
                                       onClick={() =>
                                     handleBedSelection(room._id, "Upper Bed")
                                       }
                                     >
                                       Upper Bed
                                     </button>
                                   </div> */}
                              {/* <span className="text-black font-bold md:text-right py-5 px-2">
                                 {currencySymbol}
                                 {room?.rate?.weekdayRate?.value} / Night
                               </span> */}
                              {/* </div> */}
                              <div className="flex items-center gap-3  overflow-x-auto  overflow-y-hidden scrollbar-thin scrollbar-thumb-blue-500 mt-5 scrollbar-track-transparent pb-1">
                                {/* <div className="flex items-center space-x-1">
                                 <span
                                   className="text-sm font-medium"
                                   onClick={getCalender}
                                 >
                                   Available calendar
                                 </span>
                                 <ChevronDown
                                   className="text-black border-r"
                                   size={12}
                                 />
                                 {showCalender && (
                                   <DatePicker
                                     selected={startDate}
                                     onChange={(date) => setStartDate(date)}
                                     inline
                                   />
                                 )}
                               </div> */}
                                {/* <button
                                 className="bg-black text-white text-sm px-6 py-3 rounded-3xl flex items-center hover:bg-primary-blue"
                                 onClick={() => {
                                   router?.push(`/checkout/${room?._id}`);
                                   console.log("checkout", room?._id);
                                 }}
                               >
                                 <FaPlus className="mr-1" />
                                 Add
                               </button> */}

                                {room?.rates
                                  ?.filter((rate) => rate.averagePrice > 0)
                                  ?.map((rate, idx) => (
                                    <div
                                      key={idx}
                                      className="flex-shrink-0 rounded-lg border border-black/50 pt-2 text-black  w-[173px] mt-2"
                                    >
                                      <p className="text-base px-2 font-semibold">
                                        {currencySymbol}
                                        {rate.averagePrice}
                                      </p>
                                      <p className="text-xxs px-2">
                                        {{
                                          EP: "Room Only",
                                          CP: "With Breakfast",
                                          MAP: "With Breakfast + L/D",
                                          AP: "All Meals",
                                        }[rate.ratePlanName] ||
                                          rate.ratePlanName}
                                      </p>
                                      {!roomQuantities[
                                        `${room._id}_${rate.ratePlanId}`
                                      ] ? (
                                        room?.maxUnits === 0 ? (
                                          <div className="relative group">
                                            <button
                                              className="bg-red-500 rounded-b-[7px] text-white text-sm px-3 py-1 w-full font-medium cursor-not-allowed font-manrope text-center flex items-center justify-center gap-2 mx-auto mt-2"
                                              disabled
                                            >
                                              <span className="animate-pulse">
                                                Sold Out
                                              </span>
                                            </button>
                                            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black text-white text-xxs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                                              This room is currently unavailable
                                            </div>
                                          </div>
                                        ) : (
                                          <button
                                            className="bg-black rounded-b-[7px] text-white text-sm px-3 py-1 w-full font-medium hover:bg-primary-blue hover:text-black font-manrope mx-auto mt-2"
                                            onClick={() =>
                                              updateRoomQuantity(room, 1, rate)
                                            }
                                          >
                                            Book
                                          </button>
                                        )
                                      ) : (
                                        <div className="bg-black rounded-b-[7px] w-full py-1 mt-2">
                                          <div className="flex items-center justify-center w-full rounded-[4px] mx-auto">
                                            <Button
                                              onClick={() =>
                                                updateRoomQuantity(
                                                  room,
                                                  -1,
                                                  rate
                                                )
                                              }
                                              className="w-5 h-5 min-w-0 p-0 rounded-full flex items-center justify-center bg-white text-black "
                                            >
                                              <FaMinus />
                                            </Button>
                                            <input
                                              type="number"
                                              value={
                                                roomQuantities[
                                                  `${room._id}_${rate.ratePlanId}`
                                                ] || 0
                                              }
                                              className="appearance-none w-10 h-5 text-center text-white placeholder:text-white mx-2"
                                              readOnly
                                            />
                                            <Button
                                              onClick={() =>
                                                updateRoomQuantity(
                                                  room,
                                                  1,
                                                  rate
                                                )
                                              }
                                              className="w-5 h-5 min-w-0 p-0 rounded-full flex items-center justify-center bg-white text-black "
                                            >
                                              <FaPlus />
                                            </Button>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  ))}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {!dormRooms?.length && !privateRooms?.length && !loading && (
                  <div className="mt-8 text-center py-8">
                    <h2 className="text-2xl font-bold text-red-600 flex items-center justify-center gap-x-2">
                      {" "}
                      <BsEmojiFrown /> All Booked Up!
                    </h2>
                    <p className="text-gray-600 mt-2 font-semibold">
                      No rooms available at the moment
                    </p>
                    <p className="text-gray-600 mt-1 font-semibold">
                      {" "}
                      Please check back shortly!!
                    </p>
                  </div>
                )}
                <div className="md:mt-8 ">
                  {hostelData ? (
                    <AccordianSearch
                      ref={accordionRef}
                      combinedData={hostelData?.propertyDetails}
                      hostelIdd={id}
                    />
                  ) : (
                    ""
                  )}
                </div>
              </section>
              <section ref={targetSectionRef} className="block md:hidden">
                {!!dormRooms?.length && (
                  <div className="mt-8">
                    <h2 className="text-2xl font-bold mb-4">Dorm Rooms</h2>
                    <div className="space-y-6">
                      {dormRooms?.map((room, index) => {
                        const imageCount =
                          hostelData?.propertyDetails?.property?.images
                            ?.length || 0;
                        const initialSlide = index % imageCount;
                        return (
                          <div
                            key={index}
                            className="bg-white rounded-3xl shadow-md flex flex-col md:flex-row relative border h-auto md:h-[236px]"
                          >
                            {/* {hostelData?.propertyDetails?.property
                            ?.freeCancellationAvailable && (
                            <Image
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/freecancel.svg`}
                              alt="Free Cancellation"
                              width={150}
                              height={35}
                              objectFit="contain"
                              className="absolute left-0 top-[1.5rem] z-10 transform translate-x-[-10%] animate-pulse"
                              loading={index === 0 ? "eager" : "lazy"}
                             quality={85}
                             priority={index === 0}
                            />
                          )} */}
                            {room?.images?.length > 0 ? (
                              // ROOM IMAGES
                              <Swiper
                                autoplay={{
                                  delay: 3000,
                                  disableOnInteraction: false,
                                }}
                                loop
                                speed={1000}
                                spaceBetween={0}
                                slidesPerView={1}
                                initialSlide={initialSlide}
                                modules={[Pagination, Navigation, Autoplay]}
                                preloadImages={false}
                                lazy={{
                                  loadPrevNext: true,
                                  loadPrevNextAmount: 1,
                                }}
                                watchSlidesProgress
                                className="mySwiper myCustomSwiper m-0 w-full h-full"
                              >
                                {shuffleArray(room.images).map((img, index) => (
                                  <SwiperSlide
                                    key={index}
                                    className="w-full bg-transparent h-full relative"
                                  >
                                    {(!isLoadedMap[index] ||
                                      imageFailedMap[index]) && (
                                      <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-t-3xl flex items-center justify-center z-0">
                                        <h1 className="text-white font-bold font-manrope text-4xl">
                                          Mixdorm
                                        </h1>
                                      </div>
                                    )}

                                    {!imageFailedMap[index] && (
                                      <div className="relative w-full h-full">
                                        <Image
                                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/${img?.objectUrl}`}
                                          alt={`Room Image ${index + 1}`}
                                          width={200}
                                          height={100}
                                          className={`rounded-t-3xl md:rounded-l-3xl md:rounded-tr-none object-cover h-48 w-full z-10 ${
                                            !isLoadedMap[index]
                                              ? "opacity-0"
                                              : "opacity-100"
                                          }`}
                                          loading={
                                            index === 0 ? "eager" : "lazy"
                                          }
                                          quality={85}
                                          priority={index === 0}
                                          onLoadingComplete={() => {
                                            setIsLoadedMap((prev) => ({
                                              ...prev,
                                              [index]: true,
                                            }));
                                          }}
                                          onError={() => {
                                            setImageFailedMap((prev) => ({
                                              ...prev,
                                              [index]: true,
                                            }));
                                          }}
                                        />
                                      </div>
                                    )}
                                  </SwiperSlide>
                                ))}
                              </Swiper>
                            ) : hostelData?.propertyDetails?.property?.images
                                ?.length > 0 ? (
                              // HOSTEL IMAGES
                              <Swiper
                                autoplay={{
                                  delay: 3000,
                                  disableOnInteraction: false,
                                }}
                                loop
                                speed={1000}
                                spaceBetween={0}
                                slidesPerView={1}
                                modules={[Pagination, Navigation, Autoplay]}
                                className="mySwiper myCustomSwiper m-0 w-full h-full"
                              >
                                {shuffleArray(
                                  hostelData.propertyDetails.property.images
                                ).map((img, index) => (
                                  <SwiperSlide
                                    key={index}
                                    className="w-full bg-transparent h-full relative"
                                  >
                                    {!isLoaded && (
                                      <div className="w-full h-full bg-gray-200 animate-pulse rounded-t-2xl absolute flex items-center justify-center">
                                        <h1 className="text-white font-bold font-manrope text-4xl">
                                          Mixdorm
                                        </h1>
                                      </div>
                                    )}
                                    <div className="relative w-full h-full">
                                      <Image
                                        src={img?.objectUrl}
                                        alt={`Hostel Image ${index + 1}`}
                                        width={200}
                                        height={100}
                                        className="rounded-t-3xl md:rounded-l-3xl md:rounded-tr-none object-cover w-full h-48"
                                        loading={index === 0 ? "eager" : "lazy"}
                                        quality={85}
                                        priority={index === 0}
                                        onLoadingComplete={() =>
                                          setIsLoaded(true)
                                        }
                                      />
                                    </div>
                                  </SwiperSlide>
                                ))}
                              </Swiper>
                            ) : (
                              // FALLBACK
                              <div className="w-full h-full bg-gray-200 animate-pulse rounded-t-2xl">
                                <div className="h-full w-full bg-gray-300 animate-pulse rounded-t-3xl flex items-center justify-center">
                                  <h1 className="text-white font-bold font-manrope text-4xl">
                                    MixDorm
                                  </h1>
                                </div>
                              </div>
                            )}

                            {/* <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/hotel.svg`}
                            alt={room?.name}
                            width={200}
                            height={100}
                            className="rounded-t-3xl md:rounded-l-3xl md:rounded-tr-none object-cover w-full md:w-[30%] h-48"
                            loading="lazy"
                          /> */}
                            <div className="ml-0 md:ml-4 py-4 px-4 md:mt-0 flex flex-col justify-between flex-1">
                              <h2 className="text-xl font-manrope font-bold mb-1">
                                {room?.name}
                              </h2>
                              {/* <div className="font-manrope font-normal text-base leading-5 text-[#888888] mb-2">
                              Taxes Not Included
                            </div> */}
                              <div className="relative">
                                <p
                                  className="text-sm my-3 cursor-pointer"
                                  onClick={() => setIsModalOpen(true)}
                                >
                                  {truncatedText}
                                </p>

                                {isModalOpen && (
                                  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                                    <div
                                      ref={modalRef}
                                      className="bg-white rounded-lg p-6 max-w-md w-full relative"
                                    >
                                      <button
                                        onClick={() => setIsModalOpen(false)}
                                        className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
                                      >
                                        <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          className="h-4 w-4"
                                          fill="none"
                                          viewBox="0 0 24 24"
                                          stroke="currentColor"
                                        >
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M6 18L18 6M6 6l12 12"
                                          />
                                        </svg>
                                      </button>
                                      <p className="text-sm my-2">{fullText}</p>
                                    </div>
                                  </div>
                                )}
                              </div>
                              {/* <div className='absolute top-0 right-5 z-20'>
                                <motion.div
                                  className='w-[44px] h-[44px] relative'
                                  animate={{ scale: [1, 1.1, 1] }}
                                  transition={{
                                    duration: 1.5,
                                    repeat: Infinity,
                                    ease: "easeInOut",
                                  }}
                                >
                                  <svg
                                    width='44'
                                    height='44'
                                    viewBox='0 0 44 44'
                                    xmlns='http://www.w3.org/2000/svg'
                                    className='absolute top-0 left-0'
                                  >
                                    <path
                                      d='M0 0H44V34L22 44L0 34V0Z'
                                      fill='#FFC800'
                                    />
                                  </svg>
                                  <span className='absolute inset-0 flex items-center justify-center text-black font-bold text-sm'>
                                    -25%
                                  </span>
                                </motion.div>
                              </div> */}
                              <div className="flex flex-wrap space-x-4 font-medium text-sm text-black mb-4">
                                {hostel?.amenitiesSmall.map(
                                  (amenity, index) => (
                                    <span
                                      key={index}
                                      className="flex items-center space-x-1"
                                    >
                                      <Image
                                        src={amenityIconsSmall[amenity]}
                                        className="w-4 h-4 fill-current text-primary-blue"
                                        alt={amenity}
                                        width={16}
                                        height={16}
                                        loading="lazy"
                                      />
                                      <span>{amenity}</span>
                                    </span>
                                  )
                                )}
                              </div>
                              {/* <div className="flex flex-col w-full md:flex-row md:justify-end"> */}
                              {/* Action buttons can go here */}
                              {/* </div> */}
                              <div className="grid xs:grid-cols-3 grid-cols-1 justify-between items-center gap-1.5">
                                {room?.rates
                                  ?.filter((rate) => rate.averagePrice > 0)
                                  ?.map((rate, idx) => (
                                    <div
                                      key={idx}
                                      className="rounded-lg border xs:block flex justify-between items-center border-black/50 xs:pt-2 text-black xs:p-0 p-2"
                                    >
                                      <div>
                                        <p className="text-sm xs:px-2 font-semibold">
                                          {currencySymbol}
                                          {rate.averagePrice}
                                        </p>
                                        <p className="text-xs xs:px-2">
                                          {{
                                            EP: "Room Only",
                                            CP: "With Breakfast",
                                            MAP: "With Breakfast + L/D",
                                            AP: "All Meals",
                                          }[rate.ratePlanName] ||
                                            rate.ratePlanName}
                                        </p>
                                      </div>
                                      {!roomQuantities[
                                        `${room._id}_${rate.ratePlanId}`
                                      ] ? (
                                        room?.maxUnits === 0 ? (
                                          <div className="relative group">
                                            <button
                                              className="bg-red-500 text-white text-sm xs:px-3 xs:py-1 px-4 py-2 rounded-b-lg xs:rounded-t-none rounded-t-lg font-medium cursor-not-allowed font-manrope flex items-center gap-2 xs:mx-auto xs:mt-2 xs:w-full"
                                              disabled
                                            >
                                              <span className="animate-pulse">
                                                Sold Out
                                              </span>
                                            </button>
                                            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                                              This room is currently unavailable
                                            </div>
                                          </div>
                                        ) : (
                                          <button
                                            className="bg-black rounded-b-lg xs:rounded-t-none rounded-t-lg text-white text-sm xs:px-3 xs:py-1 px-4 py-2 font-medium hover:bg-primary-blue hover:text-black font-manrope xs:mx-auto xs:mt-2 xs:w-full"
                                            onClick={() =>
                                              updateRoomQuantity(room, 1, rate)
                                            }
                                          >
                                            Book
                                          </button>
                                        )
                                      ) : (
                                        <div className="bg-black rounded-b-lg xs:rounded-t-none rounded-t-lg xs:w-full xs:px-3 xs:py-1 px-3 py-2 xs:mt-2">
                                          <div className="flex items-center justify-center w-full rounded-[4px] mx-auto">
                                            <Button
                                              onClick={() =>
                                                updateRoomQuantity(
                                                  room,
                                                  -1,
                                                  rate
                                                )
                                              }
                                              className="w-5 h-5 min-w-0 p-0 rounded-full flex items-center justify-center bg-white text-black "
                                            >
                                              <FaMinus />
                                            </Button>
                                            <input
                                              type="number"
                                              value={
                                                roomQuantities[
                                                  `${room._id}_${rate.ratePlanId}`
                                                ] || 0
                                              }
                                              className="appearance-none xs:w-10 w-8 h-5 text-center text-white placeholder:text-white mx-2"
                                              readOnly
                                            />
                                            <Button
                                              onClick={() =>
                                                updateRoomQuantity(
                                                  room,
                                                  1,
                                                  rate
                                                )
                                              }
                                              className="w-5 h-5 min-w-0 p-0 rounded-full flex items-center justify-center bg-white text-black "
                                            >
                                              <FaPlus />
                                            </Button>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  ))}

                                {/* <div className="rounded-lg border xs:block flex justify-between items-center border-black/50 xs:pt-2 text-black xs:p-0 p-2">
                                <div>
                                  <p className="text-sm xs:px-2 font-semibold">{currencySymbol}
                                {room?.rate?.weekdayRate?.value}</p>
                                  <p className="text-xs xs:px-2 flex items-center gap-1"><BsCupHot /> <span>Breakfast Included</span></p>
                                </div>
                                <button
                                  className="hover:bg-black rounded-b-lg hover:text-white text-sm px-3 py-1 w-full font-medium bg-primary-blue text-black font-manrope mx-auto mt-2"
                                  onClick={() => updateRoomQuantity(room, 1)}
                                >
                                  Book
                                </button>
                              </div> */}

                                {/* <div className="rounded-lg border xs:block flex justify-between items-center border-black/50 xs:pt-2 text-black xs:p-0 p-2">
                                <div>
                                  <p className="text-sm xs:px-2 font-semibold">{currencySymbol}
                                {room?.rate?.weekdayRate?.value}</p>
                                  <p className="text-xs xs:px-2 flex items-center gap-1"><TbCancel /> Non-refundable</p>
                                </div>
                                <button
                                  className="bg-black rounded-b-lg text-white text-sm px-3 py-1 w-full font-medium hover:bg-primary-blue hover:text-black font-manrope mx-auto mt-2"
                                  onClick={() => updateRoomQuantity(room, 1)}
                                >
                                  Book
                                </button>
                              </div> */}

                                {/* -------------------- Original code -------------------------------- */}
                                {/* <span className="text-black font-bold md:text-right py-5 px-2">
                                {currencySymbol}
                                {room?.rate?.weekdayRate?.value} / Night
                              </span>
                              {!roomQuantities[room._id] ? (
                                room?.maxUnits === 0 ? (
                                  <div className="relative group">
                                    <button
                                      className="bg-red-500 text-white text-sm px-12 py-2 rounded-full font-medium cursor-not-allowed font-manrope flex items-center gap-2"
                                      disabled
                                    >
                                      <span className="animate-pulse">Sold Out</span>
                                    </button>
                                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                                      This room is currently unavailable
                                    </div>
                                  </div>
                                ) : (
                                  <button
                                    className="bg-black text-white text-sm px-12 py-2 rounded-full font-medium hover:bg-primary-blue hover:text-black font-manrope"
                                    onClick={() => updateRoomQuantity(room, 1)}
                                  >
                                    Book
                                  </button>
                                )
                              ) : (
                                <div className="flex">
                                  <Button
                                    onClick={() => updateRoomQuantity(room, -1)}
                                    className="w-8 h-8 min-w-0 p-0 rounded-full flex items-center justify-center bg-primary-blue text-white"
                                  >
                                    <FaMinus />
                                  </Button>
                                  <input
                                    type="number"
                                    value={roomQuantities[room._id] || 1}
                                    className="appearance-none w-10 text-center text-black placeholder:text-black mx-2"
                                    readOnly
                                  />
                                  <Button
                                    onClick={() => updateRoomQuantity(room, 1)}
                                    className="w-8 h-8 min-w-0 p-0 rounded-full flex items-center justify-center bg-primary-blue text-white"
                                  >
                                    <FaPlus />
                                  </Button>
                                </div>
                              )} */}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                      {/* <div className="flex justify-center mt-4">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="px-3 py-1 mx-1 bg-gray-300 rounded disabled:opacity-50"
                  >
                    Previous
                  </button>
                  {[...Array(totalPages).keys()].map((page) => (
                    <button
                      key={page + 1}
                      onClick={() => handlePageChange(page + 1)}
                      className={`px-3 py-1 mx-1 ${
                        currentPage === page + 1
                          ? "bg-blue-500 text-white"
                          : "bg-gray-300"
                      } rounded`}
                    >
                      {page + 1}
                    </button>
                  ))}
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="px-3 py-1 mx-1 bg-gray-300 rounded disabled:opacity-50"
                  >
                    Next
                  </button>
                </div> */}
                    </div>
                  </div>
                )}

                {Object.values(roomQuantities).some((qty) => qty > 0) && (
                  <>
                    <div className="fixed bottom-10 left-1/2 transform -translate-x-1/2 z-50">
                      <button
                        className="bg-primary-blue text-black border border-teal-500 px-4 py-2 rounded-full shadow-lg flex items-center gap-2 font-manrope font-medium  transition-colors"
                        onClick={() => {
                          const element = document.getElementById("right-box");
                          if (element) {
                            element.scrollIntoView({ behavior: "smooth" });
                          }
                        }}
                      >
                        <FaShoppingCart className="text-lg" />
                        <span>
                          View Cart (
                          {Object.values(roomQuantities).reduce(
                            (a, b) => a + b,
                            0
                          )}
                          )
                        </span>
                      </button>
                    </div>
                  </>
                )}

                {!!privateRooms?.length && (
                  <div className="mt-8">
                    <h2 className="text-2xl font-bold mb-4">Private Rooms</h2>
                    <div className="space-y-6">
                      {privateRooms?.map((room, index) => {
                        let photoUrl =
                          hostelData?.propertyDetails?.property?.images?.[
                            index %
                              hostelData?.propertyDetails?.property?.images
                                ?.length
                          ]?.objectUrl;

                        if (photoUrl && !photoUrl.startsWith("http")) {
                          photoUrl = `https://${photoUrl}`;
                        }

                        const fallbackImage =
                          "https://via.placeholder.com/200x100.png?text=No+Image";

                        const weekdayRate = room?.rate?.weekdayRate?.value;
                        const weekendRate = room?.rate?.weekendRate?.value;

                        const imageCount =
                          hostelData?.propertyDetails?.property?.images
                            ?.length || 0;
                        const initialSlide = Math.floor(
                          Math.random() * imageCount
                        );
                        return (
                          <div
                            key={index}
                            className="bg-white rounded-3xl shadow-md flex flex-col md:flex-row relative border h-auto md:h-[236px]"
                          >
                            {/* {hostelData?.propertyDetails?.property
                              ?.freeCancellationAvailable && (
                              <Image
                                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/freecancel.svg`}
                                alt="Free Cancellation"
                                width={150}
                                height={35}
                                objectFit="contain"
                                className="absolute left-0 top-[1.5rem] z-10 transform translate-x-[-10%] animate-pulse"
                                loading="lazy"
                                
                              />
                            )} */}
                            {/* <Image
                              src={photoUrl || fallbackImage}
                              alt={room?.name}
                              width={200}
                              height={100}
                              className="rounded-t-3xl md:rounded-l-3xl md:rounded-tr-none object-cover w-full md:w-[30%] h-48 md:h-full shadow-md"
                              loading="lazy"
                            /> */}

                            {room?.images?.[0]?.objectUrl ? (
                              <Swiper
                                autoplay={{
                                  delay: 3000,
                                  disableOnInteraction: false,
                                }}
                                loop
                                speed={1000}
                                spaceBetween={0}
                                slidesPerView={1}
                                modules={[Pagination, Navigation, Autoplay]}
                                initialSlide={initialSlide}
                                preloadImages={false}
                                lazy={{
                                  loadPrevNext: true,
                                  loadPrevNextAmount: 1,
                                }}
                                watchSlidesProgress
                                className="mySwiper myCustomSwiper m-0 w-full h-full"
                              >
                                {room?.images?.map((img, index) => (
                                  <SwiperSlide
                                    key={index}
                                    className="w-full bg-transparent h-full relative"
                                  >
                                    {/* Loading/Fallback State */}
                                    {(!isLoadedMap[index] ||
                                      imageFailedMap[index]) && (
                                      <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-t-3xl flex items-center justify-center z-0">
                                        <h1 className="text-white font-bold font-manrope text-4xl">
                                          Mixdorm
                                        </h1>
                                      </div>
                                    )}

                                    {/* Actual Image */}
                                    {!imageFailedMap[index] && (
                                      <div className="relative w-full h-full">
                                        <Image
                                          // src={img?.objectUrl}
                                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/${img?.objectUrl}`}
                                          alt={`Image ${index + 1}`}
                                          width={200}
                                          height={100}
                                          className={`rounded-t-3xl md:rounded-l-3xl md:rounded-tr-none object-cover w-full md:w-[30%] h-48 ${
                                            !isLoadedMap[index]
                                              ? "opacity-0"
                                              : "opacity-100"
                                          }`}
                                          loading={
                                            index === 0 ? "eager" : "lazy"
                                          }
                                          quality={85}
                                          priority={index === 0}
                                          onLoadingComplete={() => {
                                            setIsLoadedMap((prev) => ({
                                              ...prev,
                                              [index]: true,
                                            }));
                                          }}
                                          onError={() => {
                                            setImageFailedMap((prev) => ({
                                              ...prev,
                                              [index]: true,
                                            }));
                                          }}
                                        />
                                      </div>
                                    )}
                                  </SwiperSlide>
                                ))}
                              </Swiper>
                            ) : (
                              <div className="w-full h-full bg-gray-200 animate-pulse rounded-t-2xl">
                                <div className="h-full w-full bg-gray-300 animate-pulse rounded-t-3xl flex items-center justify-center">
                                  <h1 className="text-white font-bold font-manrope text-4xl">
                                    Mixdorm
                                  </h1>
                                </div>
                              </div>
                            )}
                            <div className="p-4 md:ml-4 md:mt-0 flex flex-col justify-between flex-1">
                              <div>
                                <div className="flex justify-between items-start">
                                  <div className="flex items-start relative">
                                    <h2 className="text-xl font-bold font-manrope">
                                      {room?.name}
                                    </h2>
                                  </div>
                                </div>
                                <div className="relative">
                                  <p
                                    className="text-sm my-3 cursor-pointer"
                                    onClick={() => setIsModalOpenPrivate(true)}
                                  >
                                    {truncatedTextPrivate}
                                  </p>

                                  {isModalOpenPrivate && (
                                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                                      <div
                                        ref={modalPrivateRef}
                                        className="bg-white rounded-lg p-6 max-w-md w-full relative"
                                      >
                                        <button
                                          onClick={() =>
                                            setIsModalOpenPrivate(false)
                                          }
                                          className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
                                        >
                                          <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            className="h-4 w-4"
                                            fill="none"
                                            viewBox="0 0 24 24"
                                            stroke="currentColor"
                                          >
                                            <path
                                              strokeLinecap="round"
                                              strokeLinejoin="round"
                                              strokeWidth={2}
                                              d="M6 18L18 6M6 6l12 12"
                                            />
                                          </svg>
                                        </button>
                                        <p className="text-sm my-2">
                                          {fullTextPrivate}
                                        </p>
                                      </div>
                                    </div>
                                  )}
                                </div>
                                {/* <div className="font-manrope font-normal text-base leading-5 text-[#888888] mt-2">
                                  Taxes Not Included
                                </div> */}

                                {/* <div className='absolute top-0 right-5 z-20'>
                                  <motion.div
                                    className='w-[44px] h-[44px] relative'
                                    animate={{ scale: [1, 1.1, 1] }}
                                    transition={{
                                      duration: 1.5,
                                      repeat: Infinity,
                                      ease: "easeInOut",
                                    }}
                                  >
                                    <svg
                                      width='44'
                                      height='44'
                                      viewBox='0 0 44 44'
                                      xmlns='http://www.w3.org/2000/svg'
                                      className='absolute top-0 left-0'
                                    >
                                      <path
                                        d='M0 0H44V34L22 44L0 34V0Z'
                                        fill='#FFC800'
                                      />
                                    </svg>
                                    <span className='absolute inset-0 flex items-center justify-center text-black font-bold text-sm'>
                                      -25%
                                    </span>
                                  </motion.div>
                                </div> */}
                              </div>
                              <div className="flex flex-wrap space-x-4 font-medium text-sm text-black my-2">
                                {hostel?.amenitiesSmall?.map(
                                  (amenity, index) => (
                                    <span
                                      key={index}
                                      className="flex items-center space-x-1"
                                    >
                                      <Image
                                        src={amenityIconsSmall[amenity]}
                                        className="w-4 h-4 fill-current text-primary-blue"
                                        alt={amenity}
                                        loading="lazy"
                                        width={16}
                                        height={16}
                                      />
                                      <span>{amenity}</span>
                                    </span>
                                  )
                                )}
                              </div>
                              <div className="flex flex-col md:flex-row md:justify-between">
                                <div className="flex flex-col md:flex-row md:justify-end w-full mt-2"></div>
                              </div>
                              <div className="grid xs:grid-cols-3 grid-cols-1 justify-between items-center gap-1.5 ">
                                {room?.rates
                                  ?.filter((rate) => rate.averagePrice > 0)
                                  ?.map((rate, idx) => (
                                    <div
                                      key={idx}
                                      className="rounded-lg border xs:block flex justify-between items-center border-black/50 xs:pt-2 text-black xs:p-0 p-2"
                                    >
                                      <div>
                                        <p className="text-sm xs:px-2 font-semibold">
                                          {currencySymbol}
                                          {rate.averagePrice}
                                        </p>
                                        <p className="text-xs xs:px-2">
                                          {{
                                            EP: "Room Only",
                                            CP: "With Breakfast",
                                            MAP: "With Breakfast + L/D",
                                            AP: "All Meals",
                                          }[rate.ratePlanName] ||
                                            rate.ratePlanName}
                                        </p>
                                      </div>
                                      {!roomQuantities[
                                        `${room._id}_${rate.ratePlanId}`
                                      ] ? (
                                        room?.maxUnits === 0 ? (
                                          <div className="relative group">
                                            <button
                                              className="bg-red-500 text-white text-sm xs:px-3 xs:py-1 px-4 py-2 rounded-b-lg xs:rounded-t-none rounded-t-lg font-medium cursor-not-allowed font-manrope flex items-center gap-2 xs:mx-auto xs:mt-2 xs:w-full"
                                              disabled
                                            >
                                              <span className="animate-pulse">
                                                Sold Out
                                              </span>
                                            </button>
                                            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                                              This room is currently unavailable
                                            </div>
                                          </div>
                                        ) : (
                                          <button
                                            className="bg-black rounded-b-lg xs:rounded-t-none rounded-t-lg text-white text-sm xs:px-3 xs:py-1 px-4 py-2 font-medium hover:bg-primary-blue hover:text-black font-manrope xs:mx-auto xs:mt-2 xs:w-full"
                                            onClick={() =>
                                              updateRoomQuantity(room, 1, rate)
                                            }
                                          >
                                            Book
                                          </button>
                                        )
                                      ) : (
                                        <div className="bg-black rounded-b-lg xs:rounded-t-none rounded-t-lg xs:w-full xs:px-3 xs:py-1 px-3 py-2 xs:mt-2">
                                          <div className="flex items-center justify-center w-full rounded-[4px] mx-auto">
                                            <Button
                                              onClick={() =>
                                                updateRoomQuantity(
                                                  room,
                                                  -1,
                                                  rate
                                                )
                                              }
                                              className="w-5 h-5 min-w-0 p-0 rounded-full flex items-center justify-center bg-white text-black "
                                            >
                                              <FaMinus />
                                            </Button>
                                            <input
                                              type="number"
                                              value={
                                                roomQuantities[
                                                  `${room._id}_${rate.ratePlanId}`
                                                ] || 0
                                              }
                                              className="appearance-none xs:w-10 w-8 h-5 text-center text-white placeholder:text-white mx-2"
                                              readOnly
                                            />
                                            <Button
                                              onClick={() =>
                                                updateRoomQuantity(
                                                  room,
                                                  1,
                                                  rate
                                                )
                                              }
                                              className="w-5 h-5 min-w-0 p-0 rounded-full flex items-center justify-center bg-white text-black "
                                            >
                                              <FaPlus />
                                            </Button>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  ))}

                                {/* <div className="rounded-lg border xs:block flex justify-between items-center border-black/50 xs:pt-2 text-black xs:p-0 p-2">
                                  <div>
                                    <p className="text-sm xs:px-2 font-semibold">{currencySymbol} {weekdayRate}</p>
                                    <p className="text-xs xs:px-2 flex items-center gap-1"><BsCupHot /> <span>Breakfast Included</span></p>
                                  </div>
                                  <button
                                    className="hover:bg-black rounded-b-lg hover:text-white text-sm px-3 py-1 w-full font-medium bg-primary-blue text-black font-manrope mx-auto mt-2"
                                    onClick={() => updateRoomQuantity(room, 1)}
                                  >
                                    Book
                                  </button>
                                </div> */}

                                {/* <div className="rounded-lg border xs:block flex justify-between items-center border-black/50 xs:pt-2 text-black xs:p-0 p-2">
                                  <div>
                                    <p className="text-sm xs:px-2 font-semibold">{currencySymbol} {weekdayRate}</p>
                                    <p className="text-xs xs:px-2 flex items-center gap-1"><TbCancel /> Non-refundable</p>
                                  </div>
                                  <button
                                    className="bg-black rounded-b-lg xs:rounded-t-none rounded-t-lg text-white text-sm xs:px-3 xs:py-1 px-4 py-2 font-medium hover:bg-primary-blue hover:text-black font-manrope xs:mx-auto xs:mt-2 xs:w-full"
                                    onClick={() => updateRoomQuantity(room, 1)}
                                  >
                                    Book
                                  </button>
                                </div> */}

                                {/* Original Code */}
                                {/* <span className="text-black font-bold md:text-right py-4 px-2">
                                  {currencySymbol} {weekdayRate} / Night
                                </span>
                                {!roomQuantities[room._id] ? (
                                  room?.maxUnits === 0 ? (
                                    <div className="relative group">
                                      <button
                                        className="bg-red-500 text-white text-sm px-12 py-2 rounded-full font-medium cursor-not-allowed font-manrope flex items-center gap-2"
                                        disabled
                                      >
                                        <span className="animate-pulse">Sold Out</span>
                                      </button>
                                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                                        This room is currently unavailable
                                      </div>
                                    </div>
                                  ) : (
                                    <button
                                      className="bg-black text-white text-sm px-12 py-2 rounded-full font-medium hover:bg-primary-blue hover:text-black font-manrope"
                                      onClick={() => updateRoomQuantity(room, 1)}
                                    >
                                      Book
                                    </button>
                                  )
                                ) : (
                                  <div className="flex">
                                    <Button
                                      onClick={() => updateRoomQuantity(room, -1)}
                                      className="w-8 h-8 min-w-0 p-0 rounded-full flex items-center justify-center bg-primary-blue text-white"
                                    >
                                      <FaMinus />
                                    </Button>
                                    <input
                                      type="number"
                                      value={roomQuantities[room._id] || 1}
                                      className="appearance-none w-10 text-center text-black placeholder:text-black mx-2"
                                      readOnly
                                    />
                                    <Button
                                      onClick={() => updateRoomQuantity(room, 1)}
                                      className="w-8 h-8 min-w-0 p-0 rounded-full flex items-center justify-center bg-primary-blue text-white"
                                    >
                                      <FaPlus />
                                    </Button>
                                  </div>
                                )} */}
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* {Object.values(roomQuantities).some((qty) => qty > 0) && (
                  <>
                    <div className='fixed bottom-10 left-1/2 transform -translate-x-1/2 z-50'>
                      <button
                        className='bg-primary-blue text-black border border-teal-500 px-4 py-2 rounded-full shadow-lg flex items-center gap-2 font-manrope font-medium  transition-colors'
                        onClick={() => {
                          const element = document.getElementById("right-box");
                          if (element) {
                            element.scrollIntoView({ behavior: "smooth" });
                          }
                        }}
                      >
                        <FaShoppingCart className='text-lg' />
                        <span>
                          View Cart (
                          {Object.values(roomQuantities).reduce(
                            (a, b) => a + b,
                            0
                          )}
                          )
                        </span>
                      </button>
                    </div>
                  </>
                )} */}

                {!dormRooms?.length && !privateRooms?.length && !loading && (
                  <div className="mt-8 text-center py-8">
                    <h2 className="text-2xl font-bold text-red-600 flex items-center justify-center gap-x-2">
                      {" "}
                      <BsEmojiFrown /> All Booked Up!
                    </h2>
                    <p className="text-gray-600 mt-2 font-semibold text-base">
                      No rooms available at the moment.
                    </p>
                    <p className="text-gray-600 mt-1 font-semibold text-base">
                      {" "}
                      Please check back shortly!!
                    </p>
                  </div>
                )}
                <div className="md:mt-8">
                  {hostelData ? (
                    <AccordianSearch
                      combinedData={hostelData?.propertyDetails}
                      hostelIdd={id}
                    />
                  ) : (
                    ""
                  )}
                </div>
              </section>
            </main>
            <div className="lg:w-[370px] mb-14 min-h-[400px] ">
              <div id="right-box" className=" sticky top-[120px]">
                {!!selectedRooms?.length ? (
                  <div className=" bg-[#F7F7F7] rounded-3xl pb-7 pt-2 px-4 mb-4 shadow-lg ">
                    <>
                      <div
                        className="bg-[#e0f7f9] text-black p-5 rounded-xl my-2 font-sans shadow-md cursor-pointer min-h-[80px]"
                        onClick={() => setShowPopup(true)}
                      >
                        <p className="font-bold text-sm">
                          "{hostelData?.propertyDetails?.property?.name}"
                          requires 100% advance or pre-payment of the total
                          amount....
                        </p>
                      </div>
                    </>
                    <div className="flex justify-between">
                      <h4 className="mb-5 text-lg font-bold">
                        Your Booking Summary
                      </h4>
                      <div className="block md:hidden mt-2">
                        <button
                          className="bg-gray-300 text-gray-700 border border-gray-700 px-1 py-1 rounded-full shadow-lg flex items-center gap-2 font-manrope font-medium transition-colors"
                          onClick={() => {
                            targetSectionRef.current?.scrollIntoView({
                              behavior: "smooth",
                            });
                          }}
                        >
                          <FaAngleUp className="text-sm" />
                        </button>
                      </div>{" "}
                    </div>
                    <div className="flex items-center pb-2 border-b gap-4">
                      <Image
                        // src={assets.CheckOutBookingHostel}

                        // src={`https://${hostelData?.propertyDetails?.property?.images?.[0]?.objectUrl}`}
                        src={`${hostelData?.propertyDetails?.property?.images?.[0]?.objectUrl}`}
                        width={96}
                        height={96}
                        alt="Room"
                        className="w-24 h-24 rounded-xl object-cover"
                        loading="lazy"
                      />
                      <div className="flex-1">
                        <p className="text-sm font-bold text-black mb-2">
                          {hostelData?.propertyDetails?.property?.name}
                        </p>
                        <p className="text-[12px] font-medium flex items-center gap-1 text-black">
                          <span className="text-primary-blue text-base">
                            <LuCalendarDays />
                          </span>{" "}
                          {format(dates[0], "yyyy-MM-dd")} ,{" "}
                          {differenceInDays(dates[1], dates[0])}{" "}
                          {differenceInDays(dates[1], dates[0]) === 1
                            ? "Night"
                            : "Nights"}
                        </p>
                        <p className="text-[12px] font-medium flex items-center gap-1 text-black">
                          <span className="text-primary-blue text-base">
                            <TbUsersGroup />
                          </span>{" "}
                          {guest} {guest === 1 ? "guest" : "guests"}
                        </p>
                      </div>
                    </div>
                    <div className="py-2 border-b ">
                      {selectedRooms.map((room) => (
                        <div key={room.roomId} className="">
                          <p className="text-sm font-bold text-black">
                            {room.name}(
                            {{
                              EP: "Room Only",
                              CP: "With Breakfast",
                              MAP: "With Breakfast + L/D",
                              AP: "All Meals",
                            }[room.ratePlanName] || room.ratePlanName}
                            )
                          </p>
                          <div className="py-2 flex items-center justify-between">
                            <p className="py-1 text-sm font-medium text-black">
                              {currencySymbol} {room?.averagePrice} x{" "}
                              {room.count}{" "}
                              {room.count === 1 ? "Room/Bed" : "Rooms/Beds"} x{" "}
                              {differenceInDays(dates[1], dates[0])}{" "}
                              {differenceInDays(dates[1], dates[0]) === 1
                                ? "Night"
                                : "Nights"}
                            </p>
                            <p className="text-sm font-medium text-black">
                              {currencySymbol}
                              {(
                                room?.averagePrice *
                                room.count *
                                differenceInDays(dates[1], dates[0])
                              ).toFixed(2)}
                            </p>
                          </div>
                          {bedSelections[room.roomId] === "Lower Bed" && (
                            <div className="py-2 flex items-center justify-between">
                              <p className="py-1 text-sm font-medium text-black">
                                Lower Bed Service
                              </p>
                              <p className="text-sm font-medium text-black">
                                {currencySymbol}{" "}
                                {(
                                  room?.rate?.weekdayRate?.value *
                                  0.5 *
                                  room.count *
                                  differenceInDays(dates[1], dates[0])
                                ).toFixed(2)}
                              </p>
                            </div>
                          )}
                        </div>
                      ))}
                      <div className="flex items-center justify-between py-1 ">
                        <p className="text-sm font-medium">Taxes & Fees</p>
                        <p className="text-sm font-medium">
                          {currencySymbol}{" "}
                          {(totalCost * 0.85 + payableNow - totalCost).toFixed(
                            2
                          )}
                        </p>
                      </div>
                    </div>

                    {/* <div className="py-4 border-b">
                    <p className="text-sm font-bold text-black">
                      Full Payment Confirmed Booking / Partial Payment Confirm
                      Booking
                    </p>
                  </div> */}

                    <div className="py-2 border-b">
                      <div className="flex items-center justify-between">
                        <p className="text-sm text-black font-bold">
                          Payable on Arrival
                        </p>
                        <p className="text-sm font-medium">
                          {!hostelData?.propertyDetails?.property
                            ?.requiredFullPayment ? (
                            <>
                              {currencySymbol} {fullPayment.toFixed(2)}
                            </>
                          ) : (
                            <>{currencySymbol} 0</>
                          )}
                        </p>
                      </div>
                      <div className="mt-5">
                        {dates[0] && (
                          <>
                            {/* {!isWithinSevenDays(dates[0]) && ( */}
                            <button
                              className="w-full text-sm font-semibold py-4 bg-primary-blue hover:bg-sky-blue-750 hover:text-white rounded-full mb-3"
                              onClick={handleCheckout}
                            >
                              Pay Partial Payment -{" "}
                              {!hostelData?.propertyDetails?.property
                                ?.requiredFullPayment
                                ? `${currencySymbol} ${payableNow.toFixed(2)}`
                                : `${currencySymbol} ${totalCost.toFixed(2)}`}
                            </button>
                            {/* )} */}
                            {/* <button
                              className='w-full text-sm font-semibold py-4 bg-primary-blue hover:bg-sky-blue-750 hover:text-white rounded-full'
                              onClick={handleCheckout}
                            >
                              Pay Full Payment -{" "}
                              {`${currencySymbol} ${totalCost.toFixed(2)}`}
                            </button> */}
                          </>
                        )}
                      </div>
                    </div>

                    <div className="pt-4 flex items-center justify-between pb-5">
                      <p className="text-base font-bold">Total</p>
                      <p className="text-sm font-medium">
                        {(totalCost * 0.85 + payableNow).toFixed(2)}{" "}
                        {currencySymbol}
                      </p>
                    </div>

                    <div>
                      {/* <label
                        htmlFor="couponCode"
                        className="text-sm font-medium block mb-2"
                      >
                        Have a coupon Apply?
                      </label> */}
                      <div className="relative">
                        <input
                          type="text"
                          id="couponCode"
                          placeholder="Discount Coupon"
                          className="w-full p-3 pr-24  text-sm border rounded-3xl text-black placeholder:text-gray-400 font-light"
                        />
                        <button className="absolute text-sm right-1 top-1 py-2 px-6 bg-black text-white rounded-3xl hover:bg-primary-blue hover:text-black">
                          Apply
                        </button>
                      </div>
                    </div>
                    <div className="flex items-center gpa-2 justify-between mt-4">
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/upi.svg`}
                        alt="UPI"
                        width={50}
                        height={17}
                        loading="lazy"
                        className="object-contain"
                      />
                      {/* <Image
                        alt=""
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/visa.svg`}
                        width={50}
                        height={19}
                        loading="lazy"
                      /> */}

                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/visa.svg`}
                        alt="Visa"
                        width={50}
                        height={19}
                        loading="lazy"
                        className="object-contain"
                      />
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/master_card.svg`}
                        alt="MasterCard"
                        width={35}
                        height={31}
                        loading="lazy"
                        className="object-contain"
                      />
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/rupay.svg`}
                        alt="Rupay"
                        width={60}
                        height={19}
                        loading="lazy"
                        className="object-contain"
                      />
                    </div>
                  </div>
                ) : (
                  <div className="bg-white shadow-md p-6 rounded-3xl w-full flex flex-col">
                    {showBookingBanner && (
                      <div className="hidden md:flex items-center justify-between bg-red-100 text-black  p-2 md:p-3 rounded-xl mb-4 booking-banner relative ">
                        <div className="flex items-center gap-2">
                          <FaFireAlt className="text-red-500 text-xl" />
                          {/* <p className="text-xs md:text-sm font-medium">
                            This property has been{" "}
                            <span className="font-bold"> 10 times</span>{" "}
                            today.
                          </p> */}
                          <AnimatePresence mode="wait">
                            <motion.p
                              key={currentMessageIndex}
                              initial={{ y: 10, opacity: 0 }}
                              animate={{ y: 0, opacity: 1 }}
                              exit={{ y: -10, opacity: 0 }}
                              transition={{ duration: 0.3 }}
                              className="text-xs md:text-sm font-medium"
                            >
                              {popuptext[currentMessageIndex]}
                            </motion.p>
                          </AnimatePresence>
                        </div>
                        <button
                          onClick={() => setShowBookingBanner(false)}
                          className="ml-2 text-gray-500 text-lg font-bold hover:text-black"
                          aria-label="cancel"
                        >
                          <RxCrossCircled />
                        </button>
                      </div>
                    )}
                    <h2 className="text-xl font-bold mb-2">Booking Summary</h2>
                    <div className="border-b w-full pb-2 mb-4">
                      <span className="text-[#888888] text-sm">
                        {getDateRangeText()}
                      </span>
                    </div>
                    {lowestPriceRoom && (
                      <div className="flex items-center pb-4 border-b gap-4">
                        <Image
                          src={`${hostelData?.propertyDetails?.property?.images?.[0]?.objectUrl}`}
                          width={96}
                          height={96}
                          alt="Room"
                          className="w-24 h-24 rounded-xl object-cover"
                          loading="lazy"
                        />
                        <div className="flex-1">
                          <p className="text-sm font-bold text-black mb-2">
                            {lowestPriceRoom?.name}
                            {lowestPriceRatePlanName
                              ? ` (${
                                  {
                                    EP: "Room Only",
                                    CP: "With Breakfast",
                                    MAP: "With Breakfast + L/D",
                                    AP: "All Meals",
                                  }[lowestPriceRatePlanName] ||
                                  lowestPriceRatePlanName
                                })`
                              : ""}
                          </p>
                          <p className="text-[12px] font-medium flex items-center gap-1 text-black mb-1">
                            <span className="text-primary-blue text-base">
                              <LuCalendarDays />
                            </span>{" "}
                            {differenceInDays(dates[1], dates[0])}{" "}
                            {differenceInDays(dates[1], dates[0]) === 1
                              ? "Night"
                              : "Nights"}
                          </p>
                          <p className="text-[12px] font-medium flex items-center gap-1 text-black">
                            <span className="text-primary-blue text-base">
                              <TbUsersGroup />
                            </span>{" "}
                            {guest} {guest === 1 ? "guest" : "guests"}
                          </p>
                          <p className="text-[12px] font-medium flex items-center gap-1 text-black">
                            <span className="text-primary-blue text-base">
                              <BsCashCoin />
                            </span>{" "}
                            {currencySymbol}
                            {lowestPrice !== null &&
                            differenceInDays(dates[1], dates[0]) > 0
                              ? lowestPrice *
                                guest *
                                differenceInDays(dates[1], dates[0])
                              : ""}
                          </p>
                        </div>
                      </div>
                    )}
                    <div className="px-6 py-4 text-center">
                      {/* <Image
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/booksummary.svg`}
                      alt='Summary'
                      width={251}
                      height={190}
                      className='rounded-lg object-cover mb-4'
                      loading='lazy'
                    /> */}
                      <p className="text-black font-medium mb-5 text-base text-center">
                        Please select your room
                      </p>
                      <button
                        className="mt-auto bg-[#40E0D0] hover:bg-teal-500 hover:text-white text-black font-medium px-4 py-3 flex items-center justify-center gap-2 rounded-full w-full"
                        onClick={handleScrollToSection}
                      >
                        <LiaBedSolid />
                        Choose Bed
                      </button>
                    </div>
                    {/* <div className="flex items-center gpa-2 justify-between mt-4">
                      <Image
                        alt=""
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/upi.svg`}
                        width={50}
                        height={17}
                        loading="lazy"
                      />
                      <Image
                        alt=""
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/visa.svg`}
                        width={50}
                        height={19}
                        loading="lazy"
                      />
                      <Image
                        alt=""
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/master_card.svg`}
                        width={35}
                        height={31}
                        loading="lazy"
                      />
                      <Image
                        alt=""
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/rupay.svg`}
                        width={60}
                        height={19}
                        loading="lazy"
                      />
                    </div> */}
                    <div className="flex items-center gpa-2 justify-between mt-4">
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/upi.svg`}
                        alt="UPI"
                        width={50}
                        height={17}
                        loading="lazy"
                        className="object-contain"
                      />
                      {/* <Image
                        alt=""
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/visa.svg`}
                        width={50}
                        height={19}
                        loading="lazy"
                      /> */}

                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/visa.svg`}
                        alt="Visa"
                        width={50}
                        height={19}
                        loading="lazy"
                        className="object-contain"
                      />
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/master_card.svg`}
                        alt="MasterCard"
                        width={35}
                        height={31}
                        loading="lazy"
                        className="object-contain"
                      />
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/rupay.svg`}
                        alt="Rupay"
                        width={60}
                        height={19}
                        loading="lazy"
                        className="object-contain"
                      />
                    </div>
                  </div>
                )}

                {/* <div className="bg-[#e0f7f9] text-black p-5 rounded-xl my-5 font-sans shadow-md ">
                  <p className="font-bold text-sm">
                    "{hostelData?.propertyDetails?.property?.name}" requires
                    100% advance or pre-payment of the total amount for
                    reservations to be confirmed before the arrival date.
                    {!showMore && (
                      <button
                        onClick={() => setShowMore(true)}
                        className="text-black ml-1"
                      >
                        ...
                      </button>
                    )}
                  </p>
                  {showMore && (
                    <>
                      <p className="text-sm mt-2">
                        "{hostelData?.propertyDetails?.property?.name}" will
                        process payment cards issued outside of India once the
                        free cancellation period is over, ensuring they have
                        sufficient balance. For cards issued in India, bookings
                        will not be confirmed unless we receive the full payment
                        in advance.
                      </p>
                      <button
                        onClick={() => setShowMore(false)}
                        className="text-black text-xs font-bold mt-1"
                      >
                        View less
                      </button>
                    </>
                  )}
                </div> */}
                {/* <div 
                 className="bg-[#e0f7f9] text-black p-5 rounded-xl my-5 font-sans shadow-md cursor-pointer min-h-[80px]"
                onClick={() => setShowMore(!showMore)}
                 >
                 <p className="font-bold text-sm">
                 "{hostelData?.propertyDetails?.property?.name}" requires
                  100% advance or pre-payment of the total amount for
                  reservations to be confirmed before the arrival date.
                  {!showMore && (
                 <span className="text-black">...</span>
                 )}
                   </p>
                
               {showMore && (
                <>
               <p className="text-sm mt-2">
              "{hostelData?.propertyDetails?.property?.name}" will
                process payment cards issued outside of India once the
                free cancellation period is over, ensuring they have
                sufficient balance. For cards issued in India, bookings
                will not be confirmed unless we receive the full payment
               in advance.
               </p>
                <div className="text-black text-xs font-bold mt-1">
                View less
                </div>
               </>
               )}
              </div> */}
              </div>
              {/* <div className="block md:hidden absolute right-3 mt-2 mr-2">
             <button
               className="bg-primary-blue text-black border border-teal-500 px-1 py-1 rounded-full shadow-lg flex items-center gap-2 font-manrope font-medium transition-colors"
               onClick={() => {
                 targetSectionRef.current?.scrollIntoView({ behavior: 'smooth' });
               }}
             >
               <FaAngleUp className="text-sm" />
               
             </button>
            </div> */}
            </div>
          </div>
          {/* <div className="md:mt-8">
            {hostelData ? (
              <AccordianSearch
                combinedData={hostelData?.propertyDetails}
                hostelIdd={id}
              />
            ) : (
              ""
            )}
          </div> */}
        </div>
      </div>

      {/* Popup overlay and content */}
      {showPopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div
            ref={popupRef}
            className="bg-[#e0f7f9] text-black p-4 rounded-xl font-sans shadow-lg max-w-md w-full relative"
          >
            <span
              className="float-end absolute right-1 top-1"
              onClick={() => setShowPopup(false)}
            >
              <RxCrossCircled className="text-lg" />
            </span>
            <p className="font-bold text-sm">
              "{hostelData?.propertyDetails?.property?.name}" requires 100%
              advance or pre-payment of the total amount for reservations to be
              confirmed before the arrival date.
            </p>
            <p className="text-sm mt-2">
              "{hostelData?.propertyDetails?.property?.name}" will process
              payment cards issued outside of India once the free cancellation
              period is over, ensuring they have sufficient balance. For cards
              issued in India, bookings will not be confirmed unless we receive
              the full payment in advance.
            </p>
          </div>
        </div>
      )}
    </>
  );
};

export default HostelsDetails;
