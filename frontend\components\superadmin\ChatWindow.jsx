 
import React from "react";
import ChatMessage from "./ChatMessage";
import MessageInput from "./MessageInput";
import { BsThreeDotsVertical } from "react-icons/bs";
import Image from "next/image";
import { IoChevronBackOutline } from "react-icons/io5";

function ChatWindow({ onClose }) {
  return (
    <div className="flex flex-col border rounded-2xl bg-white w-full p-6 h-screen dark:bg-black dark:border-none">


      <div className="flex items-center">
        <button
          className="flex lg:hidden items-center gap-2 text-gray-600 hover:text-gray-800 mr-1 dark:text-[#B6B6B6]"
          onClick={onClose}
        >
          <IoChevronBackOutline size={20} />
        </button>
        <Image
          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
          width={37}
          height={37}
          alt="<PERSON><PERSON>"
          className="w-10 h-10 mr-4 rounded-full"
        />
        <div>
          <h4 className="text-lg font-poppins font-medium dark:text-[#B6B6B6]"><PERSON><PERSON></h4>
          <p className="text-xxs font-poppins font-medium text-gray-500 dark:text-[#757575]">Active Now</p>
        </div>
        <div className="ml-auto cursor-pointer">
          <div className="border rounded-md flex h-7 w-7 justify-center items-center dark:border-none dark:bg-[#171616]">
            <BsThreeDotsVertical className="text-black/60 dark:text-[#B6B6B6] " />
          </div>
        </div>
      </div>
      <div className="flex items-center my-4">
        <div className="flex-grow border-t border-gray-300"></div>
        <span className="mx-4 text-gray-500 text-xs font-poppins font-normal dark:text-[#757575]">March 23</span>
        <div className="flex-grow border-t border-gray-300"></div>
      </div>
      <div className="flex-1 overflow-y-scroll text-sm font-poppins">
        {/* Incoming Messages */}
        <ChatMessage
          message="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Dolor mollis leo proin turpis eu hac. Tortor dolor eu at bibendum suspendisse. Feugiat mi eu, rhoncus diam consectetur libero morbi pharetra. Id tristique mi eget eget tristique orci."
          time="10:15 pm"
          isSender={false}
          
        />
        <ChatMessage
        
          message="Lorem ipsum dolor sit amet, consectetur adipiscing elit. Dolor mollis leo proin turpis eu hac. Tortor dolor eu at bibendum suspendisse. "
          time="12:15 pm"
          isSender={true} // This makes the message outgoing
        />
        <div className="flex items-center my-4">
          <div className="flex-grow border-t border-gray-300"></div>
          <span className="mx-4 text-gray-500 text-xs font-poppins font-normal">March 23</span>
          <div className="flex-grow border-t border-gray-300"></div>
        </div>
        <ChatMessage
        
          isFile={true}
          fileName="Wireframe.pdf"
          fileSize="5.2MB"
          time="06:00 pm"
          isSender={false}
        />
      </div>
      <div className="border-t border-gray-300 mt-4 pt-4">
        <MessageInput />
      </div>
    </div>
  );
}

export default ChatWindow;
