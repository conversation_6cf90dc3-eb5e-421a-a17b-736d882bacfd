import mongoose from 'mongoose';

const profileViewSchema = new mongoose.Schema({
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
    viewBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'users',
        required: true
    }
},{
    timestamps:true
});

const profileViewModel = mongoose.model('profileViews', profileViewSchema);

export default profileViewModel;
