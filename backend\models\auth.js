import mongoose from 'mongoose';
import bcrypt from 'bcrypt';
const userSchema = mongoose.Schema(
  {
    name: {
      first: {
        type: String,
        required: true,
      },
      last: {
        type: String,
      }
    },
    email: {
      type: String,
      trim: true,
      lowercase: true,
    },
    password: {
      type: String,
    },
    address: {
      type: String,
    },
    location: {
      type: { type: String },
      coordinates: { type: Array, default: [] },
    },
    dob: {
      type: String,
    },
    contact: {
      type: String,
      minlength: 10,
    },
    role: {
      type: String,
      enum: ["admin", "sub_admin", "user", "hostel_owner"],
      default: 'user',
    },
    isRootUser: {
      // used for identifying root user for the project, this user cannot be deleted
      type: Boolean,
      default: false,
    },
    isEmailVerified: {
      type: Boolean,
      default: false,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    profileImage: { type: Array },
  privacy: {
  email: {
    type: Boolean,
    default: false,
  },
  phone: {
    type: Boolean,
    default: false,
  },
},
  subRole: {
  type: mongoose.SchemaTypes.ObjectId,
},
  isBanned: {
  status: {
    type: Boolean,
    default: false,
  },
  bannedAt: {
    type: Date,
  },
},
  friends: [{
    type: mongoose.SchemaTypes.ObjectId,
    ref: 'User'
  }],
  groups: [{
    type: mongoose.SchemaTypes.ObjectId,
    ref: 'Group'
  }],
  activityLogs: [{
    type:
      mongoose.SchemaTypes.ObjectId,
    ref: 'ActivityLog'
  }],
  razorpay_customer_id: {
  type: String,
  unique: true
},
  razorpay_plan_id: {
  type: String,
  unique: true
}, //
  deactivate_period: {
  type: Number, // Represents the number of days a user is deactivated
  default: 0, // 0 means no deactivation
},
  deactivatedAt: {
  type: Date,
  default: null // The date when the user was deactivated
},
  aboutMe: {
  type: String,
},
  profileHeading: {
  type: String,
},
  propertyLicenceKey: {
  type: String,
},
  resetPasswordToken: {
  type: String
},
  resetPasswordExpires: {
  type: Date
},
  otp: {
  type: Number
},
  otpExpires: {
  type: Date
},
  isArchived: {
  type: Boolean,
  default: false
},
  isDeleted: {
  type: Boolean,
  default: false
},
  isActive: {
  type: Boolean,
  default: true
},
  lastAccess: {
  type: Date,
  default: Date.now
},
  countryCode: {
  type: Number
},
  country: {
  type: String
},
  currency: {
  type: String
},
  countryShortName: {
  type: String
},
  fcmTokens: [{ type: String }],
  isPremiumUser: {
  type: Boolean,
  default: false
},
  lastSeen: {
  type: String
}
  },
{
  timestamps: true,
  },
);

// create 2dsphere index
userSchema.index({ location: '2dsphere' });

/**
 * Check if email is taken
 * @param {string} email - The user's email
 * @param {ObjectId} [excludeUserId] - The id of the user to be excluded
 * @return {Promise<boolean>}
 */
userSchema.statics.isEmailTaken = async function (email, excludeUserId) {
  const user = await this.findOne({ email, _id: { $ne: excludeUserId } }).lean();
  return !!user;
};

/**
  * Check if password matches the user's password
  * @param {string} password
  * @returns {Promise<boolean>}
  */
// userSchema.methods.isPasswordMatch = async function (password) {
//   const user = this;
//   console.log(password)
//   return await bcrypt.compare(password, user.password);
// };

// userSchema.pre('save', async function (next) {
//   // eslint-disable-next-line no-invalid-this
//   const user = this;
//   if (user.isModified('password')) {
//     user.password = await bcrypt.hash(user.password, 8);
//   }
//   next();
// });

/**
 * @typedef User
 */
const User = mongoose.model("users", userSchema);

export default User;
