import { addRide, getAllRides, getRideById,
     getUserRides, cancelRide, deleteRide } from '../services/ride.js';
import { sendPushNotification } from "../utills/firebase.js"
import rideModel from "../models/rides.js"
import rideReviewModel from "../models/rideReviews.js"; 
import Response from "../utills/response.js";
import {noticeboardAdd} from "../controller/noticeboard.js"
import RiderecentSearches from '../models/rideRecentSearches.js';
const addRideController = async (req, res) => {
    try {
        // const userId = req.user._id; // Assuming user ID is available in `req.user`
        const data = {
            ...req.body,
            user:req.user._id
        }
        const ride = await addRide(data);
        const rideMessage = `${req.body.name} Ride has been Created for the date ${req.body.date}`
        const dynamicId = {ride:ride._id}
        const dynamicData = {
            fromAddress:ride.fromAddress,
            destination:ride.destination,
            name:ride.name,
            date:ride.date,
            time:ride.time,
            transportMode:ride.transportMode,
            price:ride.price,
            currency:ride.currency
        }
        await noticeboardAdd(req.user._id,rideMessage,'rides','Ride Created',true,dynamicId,dynamicData)
        await sendPushNotification(true, [], "Rides Created", 'created', { ride: ride._id }, 'ride', 'Mix Ride Created by', req.user._id)
        res.status(201).json({ success: true, data: ride });
    } catch (error) {
        res.status(400).json({ success: false, message: error.message });
    }
};

const getAllRidesController = async (req, res) => {
    try {
        const { page = 1, limit = 10 } = req.query;
        const rides = await getAllRides({isDeleted:false}, parseInt(page), parseInt(limit));
        res.status(200).json({ success: true, data: rides });
    } catch (error) {
        res.status(400).json({ success: false, message: error.message });
    }
};

const getRideByIdController = async (req, res) => {
    try {
        const rideId = req.params.id;
        const ride = await getRideById(rideId);
        await RiderecentSearches.create({user:req.user._id,ride:rideId})
        res.status(200).json({ success: true, data: ride });
    } catch (error) {
        res.status(404).json({ success: false, message: error.message });
    }
};

const cancelRideController = async (req, res) => {
    try {
        const rideId = req.params.id;
        const ride = await cancelRide(rideId);
        res.status(200).json({ success: true, data: ride });
    } catch (error) {
        res.status(400).json({ success: false, message: error.message });
    }
};
const getUserRidesController = async (req, res) => {
    try {
        const userId = req.user._id; // Assuming user ID is available in `req.user`

        // Get page and limit from query parameters, with default values
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;

        // Calculate the number of documents to skip
        const skip = (page - 1) * limit;

        // Fetch the total count of user rides for pagination
        const totalRides = await rideModel.countDocuments({user: userId }); // Implement this function to count total rides
        // Fetch rides with pagination
        const rides = await getUserRides(userId, skip, limit); // Modify getUserRides to support skip and limit

        // Calculate total pages
        const totalPages = Math.ceil(totalRides / limit);

        const pagination = {
            page,
            limit,
            totalPages,
            totalRides,
        };

        res.status(200).json({ success: true, data: rides, pagination });
    } catch (error) {
        res.status(400).json({ success: false, message: error.message });
    }
};

const addRideReviews = async (req, res) => {
    try {
        // Ensure req.user is populated by middleware
        const userId = req.user?._id;
        if (!userId) {
            return res.status(401).json({ status: false, message: 'User not authenticated' });
        }
        console.log('userId:', userId);

        const { rideId } = req.params;
        const { rating, comment, images, likes, dislikes } = req.body;

        // Create review data
        const rideReviewData = {
            user: userId,
            ride: rideId,
            rating,
            comment,
            images,
            likes,
            dislikes,
        };

        // Save review
        const newReview = await rideReviewModel.create(rideReviewData);
        return Response.Created(res, newReview, 'Review Added Successfully');
    } catch (error) {
        console.error('Error adding review:', error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};

const getRideReviews = async (req, res) => {
    try {
        const { rideId } = req.params;
        const { page = 1, limit = 10, rating } = req.query; // Include rating as a query parameter

        // Ensure page and limit are numbers
        const pageNumber = parseInt(page, 10);
        const limitNumber = parseInt(limit, 10);

        // Build the filter object
        const filter = { ride: rideId };
        if (rating) {
            filter.rating = parseInt(rating, 10); // Filter reviews by specific rating
        }

        // Fetch reviews with pagination and rating filter
        const reviews = await rideReviewModel
            .find(filter)
            .populate('user', 'name avatar contact profileImage') // Populate user details
            .sort({ createdAt: -1 }) // Sort by newest first
            .skip((pageNumber - 1) * limitNumber) // Skip records for pagination
            .limit(limitNumber); // Limit records to `limit`

        // Get total count of reviews for the ride with the applied filter
        const totalReviews = await rideReviewModel.countDocuments(filter);

        const pagination = {
            currentPage: pageNumber,
            totalPages: Math.ceil(totalReviews / limitNumber),
            totalReviews,
            limit: limitNumber,
        };
        
        return Response.OK(res, { reviews, pagination }, 'Ride reviews fetched successfully');     
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const editRideController = async (req, res) => {
    try {
        const { rideId } = req.params; // Extract ride ID from route params
        const userId = req.user._id; // Assuming the user ID is available in `req.user`
        const updatedData = req.body; // Get updated ride details from the request body

        // Check if the ride exists and belongs to the user
        const existingRide = await rideModel.findOne({ _id: rideId });
        if (!existingRide) {
            return res.status(404).json({ success: false, message: "Ride not found or unauthorized" });
        }

        // Update the ride
        Object.assign(existingRide, updatedData); // Merge new data into the existing ride
        await existingRide.save();

        // Generate a dynamic message for the noticeboard
        const rideMessage = `${updatedData.name || existingRide.name} Ride has been updated for the date ${
            updatedData.date || existingRide.date
        }`;

        const dynamicId = { ride: existingRide._id };
        const dynamicData = {
            fromAddress: updatedData.fromAddress || existingRide.fromAddress,
            destination: updatedData.destination || existingRide.destination,
            name: updatedData.name || existingRide.name,
            date: updatedData.date || existingRide.date,
            time: updatedData.time || existingRide.time,
            transportMode: updatedData.transportMode || existingRide.transportMode,
            price: updatedData.price || existingRide.price,
            currency: updatedData.currency || existingRide.currency,
        };

        // Add a noticeboard entry
        await noticeboardAdd(userId, rideMessage, "rides", "Ride Updated", true, dynamicId, dynamicData);

        // // Send a push notification
        // await sendPushNotification(
        //     true,
        //     [],
        //     "Ride Updated",
        //     "updated",
        //     { ride: existingRide._id },
        //     "ride",
        //     `${updatedData.name || existingRide.name} Ride has been updated`,
        //     userId
        // );

        res.status(200).json({ success: true, data: existingRide });
    } catch (error) {
        res.status(400).json({ success: false, message: error.message });
    }
};
export const deleteRideController = async (req, res) => {
    try {
        const rideId = req.params.rideId;
        const ride = await deleteRide(rideId);
        res.status(200).json({ success: true, message:"Ride Deleted Success" });
    } catch (error) {
        res.status(400).json({ success: false, message: error.message });
    }
};
const getRideRecentSearches = async (req, res) => {
    try {
        const rides = await RiderecentSearches.find({user:req?.user?._id}).
        populate('user', 'name profileImage _id')
        .populate('ride')
        res.status(200).json({ success: true, data: rides });
    } catch (error) {
        res.status(400).json({ success: false, message: error.message });
    }
};
export { addRideController, getAllRidesController,
     getRideByIdController, cancelRideController, 
     getUserRidesController,addRideReviews,getRideReviews,editRideController,getRideRecentSearches };
