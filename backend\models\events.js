import mongoose from 'mongoose';
import slugify from 'slugify';

const eventSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
    },
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
    },
    duration: {
      type: String,
      required: true,
    },
    hours: {
      type: Number,
      required: true,
    },
    minutes: {
      type: Number,
      required: true,
    },
    price: {
      type: Number,
      required: true,
    },
    currency:{
      typee:String
    },
    address: {
      type: String,
      required: true,
    },
    location: {
        type: { type: String, default: 'Point' },
        coordinates: { type: [Number], default: [0, 0] },
      },
    attachment:[{
      url: {
        type: String,
        required: false,
      },
    }],
    description: {
      type: String,
      required: false,
    },
    tags: [String],
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'users',
      required: true,
    },
    propertyId: {
      type: mongoose.Types.ObjectId,
      ref: 'properties'   
    },
    status: {
      type: String,
      enum: ['Upcoming', 'Ongoing', 'Completed'],
      default: 'Upcoming',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    refundable: {
        type: String,
        default: true
    },
    nonRefundable: {
        type: String,
        default: false
    },
    cancellation: {
        type: String,
        default: false
    }
  },
  {
    timestamps: true,
  }
);

const eventModel = mongoose.model('events', eventSchema);

export default eventModel;