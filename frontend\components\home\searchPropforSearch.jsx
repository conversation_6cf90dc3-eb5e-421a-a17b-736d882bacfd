import React, { useEffect, useRef, useState } from "react";
import StateAutocomplete from "./StateAutocomplete";
import { Button } from "@mui/material";
import { Search } from "lucide-react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { CiCalendar } from "react-icons/ci";
import { FiUserPlus } from "react-icons/fi";
import { FaMapMarkerAlt } from "react-icons/fa";
import {
  format,
  addDays,
  isSameDay,
  differenceInDays,
  addMonths,
  isSameMonth,
  
} from "date-fns";
import StateAutocompleteForSearch from "./stateAutoCompleteForSearch";

const SearchPropforSearch = ({
  state,
  dataa,
  setState,
  guest,
  setGuest,
  handleChange,
  handleSubmit,
  setHeaderSearch,
  size = "small",
}) => {
  const [dates, setDates] = useState([null, null]);
  const [isMobile, setIsMobile] = useState(false);
  const [nights, setNights] = useState(0);
  const [calendarMonths, setCalendarMonths] = useState([
    new Date(),
    addMonths(new Date(), 1),
  ]);

  // useEffect(() => {
  //   const checkMobile = () => {
  //     setIsMobile(typeof window !== "undefined" && window.innerWidth <= 768);
  //   };

  //   // Initial check
  //   checkMobile();

  //   // Add resize listener
  //   if (typeof window !== "undefined") {
  //     window.addEventListener("resize", checkMobile);
  //     return () => window.removeEventListener("resize", checkMobile);
  //   }
  // }, []);

  // useEffect(() => {
  //   // Set initial dates only on the client side after the component mounts
  //   const initialDates = [
  //     dataa?.checkIn ? new Date(dataa.checkIn) : new Date(),
  //     dataa?.checkOut ? new Date(dataa.checkOut) : addDays(new Date(), 1),
  //   ];
  //   setDates(initialDates);
  // }, []);

  // useEffect(() => {
  //   if (dates && dates[0] && dates[1]) {
  //     const bookingData = {
  //       checkIn: formatDate(dates[0]),
  //       checkOut: formatDate(dates[1]),
  //     };
  //     localStorage.setItem("bookingdata", JSON.stringify(bookingData));
  //     // Calculate nights when dates change
  //     const nightsCount = differenceInDays(dates[1], dates[0]);
  //     setNights(nightsCount);
  //   }
  // }, [dates]);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(typeof window !== "undefined" && window.innerWidth <= 768);
    };

    // Initial check
    checkMobile();

    // Add resize listener
    if (typeof window !== "undefined") {
      window.addEventListener("resize", checkMobile);
      return () => window.removeEventListener("resize", checkMobile);
    }
  }, []);

  useEffect(() => {
    // Set initial dates only on the client side after the component mounts
    const initialDates = [
      dataa?.checkIn ? new Date(dataa.checkIn) : new Date(),
      dataa?.checkOut ? new Date(dataa.checkOut) : addDays(new Date(), 1),
    ];
    setDates(initialDates);
  }, []);

  useEffect(() => {
    if (dates && dates[0] && dates[1]) {
      const bookingData = {
        checkIn: formatDate(dates[0]),
        checkOut: formatDate(dates[1]),
      };
      localStorage.setItem("bookingdata", JSON.stringify(bookingData));
      // Calculate nights when dates change
      const nightsCount = differenceInDays(dates[1], dates[0]);
      setNights(nightsCount);
    }
  }, [dates]);

  const [guestNum, setGuestNum] = useState(false);
  const [calendarOpen, setCalendarOpen] = useState(false);
  const guestDropdownRef = useRef(null);

  // eslint-disable-next-line no-unused-vars
  const [destination, setDestination] = useState("");
  // eslint-disable-next-line no-unused-vars
  const [datess, setDatess] = useState("28 Mar - 29 Mar");
  // eslint-disable-next-line no-unused-vars
  const [guests, setGuests] = useState(1);

  const [showInput, setShowInput] = useState(false);
  const wrapperRef = useRef(null);
  const calendarRef = useRef(null);
  

  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedData = localStorage.getItem("bookingdata");
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        setDates([
          parsedData.checkIn ? new Date(parsedData.checkIn) : null,
          parsedData.checkOut ? new Date(parsedData.checkOut) : null,
        ]);
      }
    }
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
        setShowInput(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    function handleClickOutside(event) {
      if (!calendarOpen) return;

      const isInsideWrapper = calendarRef.current && calendarRef.current.contains(event.target);
      const isInsideCalendar = event.target.closest(".react-datepicker");

      if (!isInsideWrapper && !isInsideCalendar) {
        setCalendarOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [calendarOpen]);

  useEffect(() => {
    function handleClickOutside(event) {
      if (
        guestDropdownRef.current &&
        !guestDropdownRef.current.contains(event.target)
      ) {
        setGuestNum(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [guestDropdownRef]);

  const incrementGuest = () => {
    if (guest < 10) {
      setGuest(guest + 1);
    }
  };

  const decrementGuest = () => {
    if (guest > 0) {
      setGuest(guest - 1);
    }
  };
  const handleClear = () => {
    setDates([null, null]);
    setNights(0);
    handleChange({
      target: {
        value: {
          checkIn: "",
          checkOut: "",
        },
        name: "dateRange",
      },
    });
  };

  const formatDate = (date) => {
    if (!date) return "";
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-based
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  const handleDateChange = (selectedDates) => {
    // Set the selected start and end dates
    setDates(selectedDates);

    const [checkIn, checkOut] = selectedDates;
    const formattedCheckIn = formatDate(checkIn);
    const formattedCheckOut = checkOut ? formatDate(checkOut) : "";

    handleChange({
      target: {
        value: {
          checkIn: formattedCheckIn,
          checkOut: formattedCheckOut,
        },
        name: "dateRange",
      },
    });
  };

  // Function to format weekdays to 3 letters
  const formatWeekDay = (day) => {
    return day.substring(0, 3).toLowerCase();
  };

  // Custom day class names to handle the range styling
  // const getDayClassNames = (date, start, end) => {
  //   let classes = "relative ";

  //   // Add range class for all days in between
  //   if (start && end && date >= start && date <= end) {
  //     classes += "custom-range-day ";
  //   }

  //   // Add boundary class for start and end dates
  //   if (start && end && (isSameDay(date, start) || isSameDay(date, end))) {
  //     classes += "range-boundary-day ";
  //   }

  //   return classes;
  // };
    const getDayClassNames = (date, startDate, endDate) => {
      let classNames = "text-sm flex items-center justify-center";
  
      const isStart = startDate && isSameDay(date, startDate);
      const isEnd = endDate && isSameDay(date, endDate) && isSameMonth(date, endDate);
      const isInRange = startDate && endDate && date > startDate && date < endDate;
  
      const isFirstOfMonth = date.getDate() === 1;
      const isLastOfMonth = date.getDate() === new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  
      if (isStart) {
        classNames += " bg-primary-blue !text-black font-semibold rounded-s rounded-e-none";
      } else if (isEnd) {
        classNames += " bg-primary-blue text-black font-semibold rounded-e rounded-s-none";
      } else if (isInRange) {
        classNames += " bg-primary-blue/30 text-black font-semibold rounded-none";
  
        if (isFirstOfMonth) {
          classNames += " first-date text-black font-semibold rounded-none";
        }
  
        if (isLastOfMonth) {
          classNames += " last-date text-black font-semibold rounded-none";
        }
      }
  
  
      return classNames;
    };

  // const DateInputWithPlaceholder = ({ placeholder }) => {

    
  //   if (!dates) return null;

  //   const formattedStartDate = dates[0]
  //     ? format(dates[0], "dd-MMM")
  //     : "Check In";
  //   const formattedEndDate = dates[1]
  //     ? format(dates[1], "dd-MMM")
  //     : "Check Out";

  //   return (
  //     <>
  //       <div className="relative" ref={calendarRef} >
  //         {calendarOpen && isMobile && (
  //           <div
  //             className="fixed inset-0 bg-black opacity-50 z-40 sm:hidden "
  //             onClick={() => setCalendarOpen(false)}
  //           />
  //         )}
  //         <div className="flex items-center justify-start sm:justify-center gap-1 cursor-pointer ">
  //           <CiCalendar
  //             className="text-xl 'text-black md:text-black "
  //             onClick={() => setCalendarOpen(true)}
  //           />

  //           <DatePicker
  //             calendarClassName="custom-calendar"
  //             className="w-full cursor-pointer max-w-[720px] "
  //             selected={dates[0]}
  //             onChange={handleDateChange}
  //             dateFormat="dd-MMM-yyyy"
  //             selectsRange
  //             startDate={dates[0]}
  //             endDate={dates[1]}
  //             placeholderText={placeholder}
  //             monthsShown={isMobile ? 1 : 2}
  //             open={calendarOpen}
  //             shouldCloseOnSelect={false}
              
  //             renderCustomHeader={({
  //               decreaseMonth,
  //               increaseMonth,
  //               prevMonthButtonDisabled,
  //               nextMonthButtonDisabled,
  //               customHeaderCount,
  //             }) => (
  //               <div>
  //                 {customHeaderCount === 0 && (
  //                   <div className="flex items-center justify-center py-2 min-h-[2.5rem] custom-date-picker-header max-w-fit sm:max-w-[720px] absolute top-[-41px] left-[116px] sm:left-[20px] md:left-[8px]  w-full sm:w-[700px] md:w-[360px] lg:w-[700px] z-50 ">
  //                     <span className="text-sm flex items-center gap-2 text-[#636c7d] font-normal">
  //                       {dates?.[0] && dates?.[1] ? (
  //                         <>
  //                           <svg
  //                             xmlns="http://www.w3.org/2000/svg"
  //                             width={16}
  //                             height={16}
  //                             viewBox="0 0 24 24"
  //                             fill="none"
  //                             stroke="currentColor"
  //                             strokeWidth={2}
  //                             strokeLinecap="round"
  //                             strokeLinejoin="round"
  //                             className="icon icon-tabler icons-tabler-outline icon-tabler-moon-stars"
  //                           >
  //                             <path
  //                               stroke="none"
  //                               d="M0 0h24v24H0z"
  //                               fill="none"
  //                             />
  //                             <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
  //                             <path d="M17 4a2 2 0 0 0 2 2a2 2 0 0 0 -2 2a2 2 0 0 0 -2 -2a2 2 0 0 0 2 -2" />
  //                             <path d="M19 11h2m-1 -1v2" />
  //                           </svg>
  //                           {nights} nights selected
  //                         </>
  //                       ) : (
  //                         <span className="flex items-center gap-2 font-normal">
  //                           <svg
  //                             xmlns="http://www.w3.org/2000/svg"
  //                             width={16}
  //                             height={16}
  //                             viewBox="0 0 24 24"
  //                             fill="none"
  //                             stroke="currentColor"
  //                             strokeWidth={2}
  //                             strokeLinecap="round"
  //                             strokeLinejoin="round"
  //                             className="icon icon-tabler icons-tabler-outline icon-tabler-calendar"
  //                           >
  //                             <path
  //                               stroke="none"
  //                               d="M0 0h24v24H0z"
  //                               fill="none"
  //                             />
  //                             <path d="M4 7a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12z" />
  //                             <path d="M16 3v4" />
  //                             <path d="M8 3v4" />
  //                             <path d="M4 11h16" />
  //                             <path d="M11 15h1" />
  //                             <path d="M12 15v3" />
  //                           </svg>
  //                           Select a check in date
  //                         </span>
  //                       )}
  //                     </span>
  //                     <button
  //                       onClick={(e) => {
  //                         e.preventDefault();
  //                         e.stopPropagation();
  //                         setCalendarOpen(false);
  //                       }}
  //                       className="absolute -right-14 md:right-2 text-xs md:text-sm font-semibold text-gray-600 hover:text-gray-800"
  //                     >
  //                       OK
  //                     </button>
  //                   </div>
  //                 )}
  //                 <div className="flex items-center justify-center px-4 py-0 relative">
  //                   {customHeaderCount === 0 && (
  //                     <button
  //                       onClick={() => {
  //                         decreaseMonth();
  //                         setCalendarMonths((prev) => [
  //                           addMonths(prev[0], -1),
  //                           prev[0],
  //                         ]);
  //                       }}
  //                       disabled={prevMonthButtonDisabled}
  //                       className="p-2 hover:bg-gray-100 rounded-full disabled:opacity-50 absolute left-[6px]"
  //                       type="button"
  //                     >
  //                       <svg
  //                         width="16"
  //                         height="16"
  //                         viewBox="0 0 24 24"
  //                         fill="none"
  //                         stroke="currentColor"
  //                         strokeWidth="2"
  //                       >
  //                         <path d="M15 18l-6-6 6-6" />
  //                       </svg>
  //                     </button>
  //                   )}
  //                   <span className="text-lg font-semibold">
  //                     {format(
  //                       customHeaderCount === 0
  //                         ? calendarMonths[0]
  //                         : calendarMonths[1],
  //                       "MMMM yyyy"
  //                     )}
  //                   </span>
  //                   {customHeaderCount === 1 && (
  //                     <button
  //                       onClick={() => {
  //                         increaseMonth();
  //                         setCalendarMonths((prev) => [
  //                           prev[1],
  //                           addMonths(prev[1], 1),
  //                         ]);
  //                       }}
  //                       disabled={nextMonthButtonDisabled}
  //                       className="p-2 hover:bg-gray-100 rounded-full disabled:opacity-50 absolute right-[6px]"
  //                       type="button"
  //                     >
  //                       <svg
  //                         width="16"
  //                         height="16"
  //                         viewBox="0 0 24 24"
  //                         fill="none"
  //                         stroke="currentColor"
  //                         strokeWidth="2"
  //                       >
  //                         <path d="M9 18l6-6-6-6" />
  //                       </svg>
  //                     </button>
  //                   )}
  //                 </div>
  //               </div>
  //             )}
  //             formatWeekDay={formatWeekDay}
  //             dayClassName={(date) =>
  //               getDayClassNames(date, dates[0], dates[1])
  //             }
  //             // onClickOutside={() => setCalendarOpen(false)}
  //             customInput={
  //               <div className="flex w-full p-1 items-center justify-between cursor-pointer">
  //                 <span
                    // className={`text-sm ${
                    //   formattedStartDate || formattedEndDate
                    //     ? "text-black md:text-black"
                    //     : "text-gray-400"
                    // } pointer-events-none font-normal`}
  //                 >
  //                   {formattedStartDate} - {formattedEndDate}
  //                 </span>
  //               </div>
  //             }
  //           >
  //             <div className="flex justify-end">
  //               <button
  //                 onClick={handleClear}
  //                 className="text-sm font-semibold text-gray-600 hover:text-gray-800 mt-[-28px] mr-[20px]"
  //               >
  //                 Clear
  //               </button>
  //             </div>
  //           </DatePicker>
  //         </div>
  //       </div>
  //     </>
  //   );
  // };

 const DateInputWithPlaceholder = ({ placeholder }) => {
     if (!dates) return null;
 
     const formattedStartDate = dates[0]
       ? format(dates[0], "dd-MMM")
       : "Check In";
     const formattedEndDate = dates[1]
       ? format(dates[1], "dd-MMM")
       : "Check Out";
 
     return (
       <>
         <div className="relative" ref={calendarRef}>
           {calendarOpen && isMobile && (
             <div
               className="fixed inset-0 bg-black opacity-50 z-40 sm:hidden"
               onClick={() => setCalendarOpen(false)}
             />
           )}
           <div className="flex items-center justify-start sm:justify-center gap-1 cursor-pointer z-30">
             <CiCalendar
               className={`text-xl 
                  text-black md:text-black`}
               onClick={() => setCalendarOpen(true)}
             />
 
                 
             <DatePicker
               calendarClassName="custom-calendar"
               className="w-full cursor-pointer max-w-[720px]"
               selected={dates[0]}
               onChange={handleDateChange}
               dateFormat="dd-MMM-yyyy"
               selectsRange
               startDate={dates[0]}
               endDate={dates[1]}
               placeholderText={placeholder}
               monthsShown={isMobile ? 1 : 2}
               open={calendarOpen}
               shouldCloseOnSelect={false}
               openToDate={calendarMonths[0]}
               minDate={new Date()}
               dayClassName={(date) => getDayClassNames(date, dates[0], dates[1])}
               filterDate={(date) => {
                 return calendarMonths.some((month) =>
                   isSameMonth(date, month)
                 );
               }}
               // maxDate={endOfMonth(calendarMonths[0])}
               renderCustomHeader={({
                 decreaseMonth,
                 increaseMonth,
                 prevMonthButtonDisabled,
                 nextMonthButtonDisabled,
                 customHeaderCount,
               }) => (
                 <div>
                   {customHeaderCount === 0 && (
                     <div className="flex items-center xjustify-between py-2 min-h-[2.5rem] custom-date-picker-header max-w-full sm:max-w-[720px] absolute top-[-41px] left-3 md:left-[20px] w-[95%] mx-auto md:w-[700px] lg:w-[700px] z-50 ">
                       <span className="text-sm flex items-center justify-center text-center gap-2 text-[#636c7d] font-normal bg-transparent w-full">
                         {dates?.[0] && dates?.[1] ? (
                           <>
                             <svg
                               xmlns="http://www.w3.org/2000/svg"
                               width={16}
                               height={16}
                               viewBox="0 0 24 24"
                               fill="none"
                               stroke="currentColor"
                               strokeWidth={2}
                               strokeLinecap="round"
                               strokeLinejoin="round"
                               className="icon icon-tabler icons-tabler-outline icon-tabler-moon-stars"
                             >
                               <path
                                 stroke="none"
                                 d="M0 0h24v24H0z"
                                 fill="none"
                               />
                               <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
                               <path d="M17 4a2 2 0 0 0 2 2a2 2 0 0 0 -2 2a2 2 0 0 0 -2 -2a2 2 0 0 0 2 -2" />
                               <path d="M19 11h2m-1 -1v2" />
                             </svg>
                             {nights} nights selected
                           </>
                         ) : (
                           <span className="flex items-center gap-2 font-normal">
                             <svg
                               xmlns="http://www.w3.org/2000/svg"
                               width={16}
                               height={16}
                               viewBox="0 0 24 24"
                               fill="none"
                               stroke="currentColor"
                               strokeWidth={2}
                               strokeLinecap="round"
                               strokeLinejoin="round"
                               className="icon icon-tabler icons-tabler-outline icon-tabler-calendar"
                             >
                               <path
                                 stroke="none"
                                 d="M0 0h24v24H0z"
                                 fill="none"
                               />
                               <path d="M4 7a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12z" />
                               <path d="M16 3v4" />
                               <path d="M8 3v4" />
                               <path d="M4 11h16" />
                               <path d="M11 15h1" />
                               <path d="M12 15v3" />
                             </svg>
                             Select a check in date
                           </span>
                         )}
                       </span>
                       <button
                         onClick={(e) => {
                           e.preventDefault();
                           e.stopPropagation();
                           setCalendarOpen(false);
                         }}
                         className="text-xs md:text-sm sm:pe-0 pe-2 font-semibold text-gray-600 hover:text-gray-800"
                       >
                         OK
                       </button>
                     </div>
                   )}
                   <div className="flex items-center justify-center px-4 py-0 relative">
                     {customHeaderCount === 0 && (
                       <button
                         onClick={() => {
                           decreaseMonth();
                           setCalendarMonths((prev) => [
                             addMonths(prev[0], -1),
                             prev[0],
                           ]);
                         }}
                         disabled={prevMonthButtonDisabled}
                         className="p-2 hover:bg-gray-100 rounded-full disabled:opacity-50 absolute left-[6px]"
                         type="button"
                       >
                         <svg
                           width="16"
                           height="16"
                           viewBox="0 0 24 24"
                           fill="none"
                           stroke="currentColor"
                           strokeWidth="2"
                         >
                           <path d="M15 18l-6-6 6-6" />
                         </svg>
                       </button>
                     )}
                     <span className="text-lg font-semibold">
                       {format(
                         customHeaderCount === 0
                           ? calendarMonths[0]
                           : calendarMonths[1],
                         "MMMM yyyy"
                       )}
                     </span>
                     {(customHeaderCount === 0 && isMobile) || customHeaderCount === 1 ? (
                       <button
                         onClick={() => {
                           increaseMonth();
                           setCalendarMonths((prev) =>
                             isMobile
                               ? [addMonths(prev[0], 1), addMonths(prev[0], 2)]
                               : [prev[1], addMonths(prev[1], 1)]
                           );
                         }}
                         disabled={nextMonthButtonDisabled}
                         className="p-2 hover:bg-gray-100 rounded-full disabled:opacity-50 absolute right-[6px]"
                         type="button"
                       >
                         <svg
                           width="16"
                           height="16"
                           viewBox="0 0 24 24"
                           fill="none"
                           stroke="currentColor"
                           strokeWidth="2"
                         >
                           <path d="M9 18l6-6-6-6" />
                         </svg>
                       </button>
                     ) : null}
                   </div>
                 </div>
               )}
               formatWeekDay={formatWeekDay}
               
               customInput={
                 <div className="flex w-full p-1 items-center justify-between cursor-pointer">
                   <span
                     className={`text-xs  text-black
                        pointer-events-none font-semibold`}
                   >
                     {formattedStartDate} - {formattedEndDate}
                   </span>
                 </div>
               }
             >
               <div className="flex justify-end">
                 <button
                   onClick={handleClear}
                   className="text-sm font-semibold text-gray-600 hover:text-gray-800 mt-[-28px] mr-[20px]"
                 >
                   Clear
                 </button>
               </div>
             </DatePicker>
           </div>
         </div>
       </>
     );
   };

  const NumberInputWithPlaceholder = ({ placeholder, value }) => {
    return (
      <div className="relative">
        <div className="flex items-center justify-center gap-1">
          <FiUserPlus className="text-2xl" />
          <input
            type="number"
            min={1}
            max={10}
            value={value}
            readOnly
            className={`w-full p-1 placeholder-black font-semibold ${
              size === "large" ? "text-base" : "text-sm"
            }`}
            placeholder={placeholder}
            style={{ outline: "none" }}
          />
        </div>
      </div>
    );
  };

  const textSizeClasses = size === "large" ? "text-sm" : "text-xs";

  return (
    <>
      <div className="w-full max-w-[900px] mx-auto relative pb-8 sm:pb-0 px-5 sm:px-3 hidden md:block">
        <div className="font-manrope bg-white border border-slate-105 rounded-xl sm:rounded-full shadow-xl px-4 py-2 sm:flex sm:items-center sm:justify-between gap-4 ">
          {/* Location Input */}
          <div className="w-full sm:w-96  p-3  border-r-0 md:border-r border-slate-200 border-b md:border-b-0">
            <div
              className={`inner_search flex justify-start w-full font-bold  text-black  ${textSizeClasses}`}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setCalendarOpen(false);
              }}
            >
              <StateAutocomplete state={state} setState={setState} />
            </div>
          </div>

          {/* Check-in Input */}
          <div className="w-80 p-3 z-40 border-r-0 md:border-r border-slate-200  border-b md:border-b-0 whitespace-nowrap px-1 text-left">
            <div
              className={`flex w-full font-bold text-black  ${textSizeClasses}`}
              onClick={() => setCalendarOpen(true)}
            >
              <DateInputWithPlaceholder placeholder="Select Dates" />
            </div>
          </div>

          {/* Guests Input */}
          <div
            className="w-full sm:w-[70px] rounded-lg sm:rounded-none p-3 border-b md:border-b-0"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setCalendarOpen(false);
            }}
          >
            <div 
              className={`relative flex w-full font-bold text-black ${textSizeClasses}`}
            >
              <div onClick={() => setGuestNum(!guestNum)}>
                <NumberInputWithPlaceholder placeholder="1" value={guest} />
              </div>

              {/* Guests Dropdown */}
              {guestNum && (
                <div
                  ref={guestDropdownRef}
                  className="w-full min-w-28 rounded-xl p-2 z-50 bg-white shadow-lg absolute left-0 lg:left-[-10px] top-[45px]"
                >
                  <div className="flex items-center base:text-sm text-base justify-center">
                    <Button
                      className="base:w-8 base:h-8 w-10 h-10 min-w-0 text-xl p-0 rounded-full flex items-center justify-center bg-primary-blue text-white"
                      onClick={decrementGuest}
                    >
                      -
                    </Button>
                    <input
                      type="number"
                      value={guest}
                      className="appearance-none w-8 text-center text-black placeholder:text-black focus:outline-none"
                      readOnly
                    />
                    <Button
                      className="base:w-8 base:h-8 w-10 h-10 min-w-0 text-lg p-0 rounded-full flex items-center justify-center bg-primary-blue text-white"
                      onClick={incrementGuest}
                    >
                      +
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Search Button */}
          <Button
            className={`min-w-0 base:w-24 base:h-9 col-span-3 w-32 text-black base:text-xs flex items-center justify-center gap-2 bg-[#40E0D0] md:text-sm outline-none rounded-full text-center ml-12 md:ml-0 mt-2 md:mt-0 ${
              size === "large" ? "py-2 md:py-5" : "base:py-3 py-2"
            }`}
            // onClick={() => {
            //   if (setHeaderSearch) {
            //     setHeaderSearch(true);
            //   }
            //   handleSubmit();
            // }}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();

              setCalendarOpen(false); // First handler logic

              if (setHeaderSearch) {
                setHeaderSearch(true); // Second handler logic
              }

              handleSubmit();
            }}
          >
            <Search className="text-black base:hidden" size={18} />
            <span className="base:flex-1 capitalize">Search</span>
          </Button>
        </div>
      </div>

      <div className="relative flex items-center justify-between !bg-white p-2 rounded-full w-full max-w-md mx-auto md:hidden">
        {/* Location Icon */}
        <div className="relative" ref={wrapperRef}>
          <button
            className="p-2 text-gray-600 flex items-center gap-1"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setCalendarOpen(false); // First functionality
              setShowInput(true); // Second functionality
            }}
            aria-label="Location"
          >
            <FaMapMarkerAlt size={18} />
            <span>{destination}</span>
          </button>

          {showInput && (
            <div className="absolute top-10 -left-4 mt-2 bg-white p-2 shadow-md  flex items-center gap-2 z-10 rounded-3xl">
              {/* <input
            type="text"
            placeholder="Where to?"
            className="rounded px-2 py-1 text-sm"
          /> */}
              <StateAutocompleteForSearch state={state} setState={setState} />
            </div>
          )}
        </div>

        {/* Date Picker */}
        <div className="flex items-center text-gray-600 text-sm space-x-2 whitespace-nowrap ml-2">
          <div
            className={`flex w-full font-semibold text-black ${textSizeClasses} `}
            onClick={() => setCalendarOpen(true)}
          >
            <DateInputWithPlaceholder
              size={20}
              placeholder="Select Dates"
              className="  mt-0.5 "
            />
          </div>
        </div>

        {/* Guests */}
        <div className="flex items-center text-gray-600 text-xs ml-4">
          <div
            className={`relative flex w-full font-bold text-black ${textSizeClasses}`}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setCalendarOpen(false);
            }}
          >
            <div onClick={() => setGuestNum(!guestNum)}>
              <NumberInputWithPlaceholder
                placeholder="1"
                value={guest}
                size={24}
              />
            </div>

            {/* Guests Dropdown */}
            {guestNum && (
              <div
                ref={guestDropdownRef}
                className="w-full min-w-28 rounded-xl p-2 z-50 bg-white shadow-lg absolute -right-2 lg:left-[-10px] top-[45px]"
              >
                <div className="flex items-center base:text-sm text-base justify-center">
                  <Button
                    className="w-8 h-8 min-w-0 text-xl p-0 rounded-full flex items-center justify-center bg-primary-blue text-white"
                    onClick={decrementGuest}
                  >
                    -
                  </Button>
                  <input
                    type="number"
                    value={guest}
                    className="appearance-none w-8 text-center text-black placeholder:text-black focus:outline-none"
                    readOnly
                  />
                  <Button
                    className="w-8 h-8 min-w-0 text-lg p-0 rounded-full flex items-center justify-center bg-primary-blue text-white"
                    onClick={incrementGuest}
                  >
                    +
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Search Button */}
        {/* <Button
            className="bg-teal-400 text-white rounded-full w-6 h-6"
            onClick={() => {
              if (setHeaderSearch) {
                setHeaderSearch(true);
              }
              handleSubmit();
            }}
          >
            <Search className="text-black base:hidden" size={18} />
            
          </Button> */}

        <button
          className="bg-teal-400 text-black p-2 rounded-full"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();

            setCalendarOpen(false); // First handler logic

            if (setHeaderSearch) {
              setHeaderSearch(true); // Second handler logic
            }

            handleSubmit();
          }}
          aria-label="Search"
        >
          <Search size={18} />
        </button>
      </div>
    </>
  );
};

export default SearchPropforSearch;
