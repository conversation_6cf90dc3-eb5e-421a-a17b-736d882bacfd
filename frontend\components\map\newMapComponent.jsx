import React, { useState, useEffect, useRef } from "react";
import { GoogleMap, InfoWindow } from "@react-google-maps/api";
import { FaWifi } from "react-icons/fa6";
import { PiBathtub } from "react-icons/pi";
import countries from "world-countries";
import Link from "next/link";
import Loader from "../loader/loader";
import { useNavbar } from "../home/<USER>";
import { MarkerClusterer } from "@googlemaps/markerclusterer";
import Image from "next/image";


const newMapComponent = ({ property = [], setShowMapList, setShowMap }) => {
  const mapContainerStyle = {
    height: "620px",
    width: "100%",
    borderRadius: "25px",
    overflow: "hidden",
  };

  const [selectedProperty, setSelectedProperty] = useState(null);
  const [isApiLoaded, setIsApiLoaded] = useState(false);
  const [currencyData, setCurrencyData] = useState({});
  const { updateMapState } = useNavbar();
  const mapRef = useRef(null);
  const markersRef = useRef([]);
  const clustererRef = useRef(null);
  const [showPrice, setShowPrice] = useState(false); // New state for toggle

  // Set initial zoom level
  const [zoom, setZoom] = useState(10);
  const [center, setCenter] = useState(
    property.length > 0
      ? {
          lat: property[0]?.location?.coordinates[1],
          lng: property[0]?.location?.coordinates[0],
        }
      : { lat: 0, lng: 0 }
  );

  // Format price with currency symbol
  const formatPrice = (hostel) => {
    const price = hostel?.lowestAveragePricePerNight?.value || "35";
    const currency = hostel?.lowestAveragePricePerNight?.currency || "USD";
    return `${getCurrencySymbol(currency)}${price}`;
  };

  // Initialize map with clustering
  const onLoad = (map) => {
    mapRef.current = map;
    initClusterer();
  };

  // const createHostelMarker = (hostel) => {
  //   const google = window.google;
  //   const content = showPrice ? formatPrice(hostel) : hostel.name || "Hostel";

    // // Calculate dynamic width based on content length
    // const baseWidth = showPrice ? 80 : 100;
    // const padding = 10;
    // const charWidth = showPrice ? 8 : 7;
    // const calculatedWidth = Math.min(
    //   Math.max(content.length * charWidth + padding, baseWidth),
    //   300
    // );

  //   // Create marker with dynamic content
  //   const marker = new google.maps.Marker({
  //     position: {
  //       lat: hostel?.location?.coordinates[1],
  //       lng: hostel?.location?.coordinates[0],
  //     },
  //     map: mapRef.current,
  //     icon: {
  //       url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
  //         <svg width="${calculatedWidth}" height="26" viewBox="0 0 ${calculatedWidth} 26" xmlns="http://www.w3.org/2000/svg">
  //           <rect width="100%" height="100%" rx="4" ry="4" fill="${
  //             showPrice ? "#40E0D0" : "black"
  //           }"/>
  //           <text 
  //             x="50%" 
  //             y="50%" 
  //             font-family="Arial" 
  //             font-size="12" 
  //             fill="${showPrice ? "black" : "white"}" 
  //             text-anchor="middle"
  //             dominant-baseline="middle"
  //             font-weight="${showPrice ? "bold" : "normal"}"
  //           >
  //             ${content}
  //           </text>
  //         </svg>
  //       `)}`,
  //       scaledSize: new google.maps.Size(calculatedWidth, 26),
  //       anchor: new google.maps.Point(calculatedWidth / 2, 13),
  //     },
  //   });

  //   marker.addListener("click", () => {
  //     setSelectedProperty(hostel);
  //     setZoom(14);
  //     setCenter({
  //       lat: hostel?.location?.coordinates[1],
  //       lng: hostel?.location?.coordinates[0],
  //     });
  //   });

  //   return marker;
  // };

  
  // const initClusterer = () => {
  //   if (!mapRef.current || !window.google || !property.length) return;

  //   // Clear existing markers and clusterer
  //   if (clustererRef.current) {
  //     clustererRef.current.clearMarkers();
  //   }
  //   markersRef.current = [];

  //   // Create markers
  //   markersRef.current = property.map((hostel) => createHostelMarker(hostel));

  //   // Create clusterer with custom styling
  //   clustererRef.current = new MarkerClusterer({
  //     map: mapRef.current,
  //     markers: markersRef.current,
  //     renderer: {
  //       render: ({ count, position }) => {
  //         const google = window.google;
  //         return new google.maps.Marker({
  //           position,
  //           icon: {
  //             url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
  //             <svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
  //               <!-- Bigger animated ripple (more visible) -->
  //               <circle cx="30" cy="30" r="10" fill="${
  //                 showPrice ? "black" : "#40E0D0"
  //               }" opacity="0">
  //                 <animate 
  //                   attributeName="r" 
  //                   values="10;30" 
  //                   dur="1.5s" 
  //                   repeatCount="indefinite"
  //                   keyTimes="0;1"
  //                   calcMode="spline"
  //                   keySplines="0.4,0,0.2,1"
  //                 />
  //                 <animate 
  //                   attributeName="opacity" 
  //                   values="0.6;0" 
  //                   dur="1.5s" 
  //                   repeatCount="indefinite"
  //                 />
  //               </circle>
                
  //               <!-- Second ripple for smoother effect -->
  //               <circle cx="30" cy="30" r="10" fill="${
  //                 showPrice ? "black" : "#40E0D0"
  //               }" opacity="0">
  //                 <animate 
  //                   attributeName="r" 
  //                   values="10;25" 
  //                   dur="1.5s" 
  //                   begin="0.5s"
  //                   repeatCount="indefinite"
  //                   keyTimes="0;1"
  //                   calcMode="spline"
  //                   keySplines="0.4,0,0.2,1"
  //                 />
  //                 <animate 
  //                   attributeName="opacity" 
  //                   values="0.4;0" 
  //                   dur="1.5s" 
  //                   begin="0.5s"
  //                   repeatCount="indefinite"
  //                 />
  //               </circle>
                
  //               <!-- Solid base circle -->
  //               <circle cx="30" cy="30" r="18" fill="${
  //                 showPrice ? "black" : "#40E0D0"
  //               }"/>
                
  //               <!-- Inner glow -->
  //               <circle cx="30" cy="30" r="14" fill="${
  //                 showPrice ? "black" : "#40E0D0"
  //               }" opacity="0.8"/>
                
  //               <!-- Count text (bold and centered) -->
  //               <text x="30" y="35" font-family="Arial" font-size="16" font-weight="bold" 
  //                     fill="${
  //                       showPrice ? "white" : "black"
  //                     }" text-anchor="middle">
  //                 ${count}
  //               </text>
  //             </svg>
  //           `)}`,
  //             scaledSize: new google.maps.Size(60, 60),
  //           },
  //         });
  //       },
  //     },
  //     styles: [
  //       {
  //         textColor: "white",
  //         url: "",
  //         height: 60,
  //         width: 60,
  //         anchor: [30, 30],
  //         fontSize: 16,
  //         fontWeight: "bold",
  //       },
  //     ],
  //   });
  // };

const createHostelMarker = (hostel) => {
  const google = window.google;
  const content = showPrice ? formatPrice(hostel) : hostel.name || "Hostel";

  // Calculate dynamic width based on content length
    // Calculate dynamic width based on content length
    const baseWidth = showPrice ? 80 : 120;
    const padding = showPrice ? 10 : 14;
    const charWidth = showPrice ? 8 : 7;
    const calculatedWidth = Math.min(
      Math.max(content.length * charWidth + padding, baseWidth),
      300
    );

  // Create marker with fixed style
  const marker = new google.maps.Marker({
    position: {
      lat: hostel?.location?.coordinates[1],
      lng: hostel?.location?.coordinates[0],
    },
    map: mapRef.current,
    icon: {
      url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
        <svg width="${calculatedWidth}" height="26" viewBox="0 0 ${calculatedWidth} 26" xmlns="http://www.w3.org/2000/svg">
          <rect width="100%" height="100%" rx="4" ry="4" fill="#40E0D0"/>
          <text 
            x="50%" 
            y="50%" 
            font-family="Arial" 
            font-size="12" 
            fill="black" 
            text-anchor="middle"
            dominant-baseline="middle"
            font-weight="bold"
          >
            ${content}
          </text>
        </svg>
      `)}`,
      scaledSize: new google.maps.Size(calculatedWidth, 26),
      anchor: new google.maps.Point(calculatedWidth / 2, 13),
    },
  });

  marker.addListener("click", () => {
    setSelectedProperty(hostel);
    setZoom(14);
    setCenter({
      lat: hostel?.location?.coordinates[1],
      lng: hostel?.location?.coordinates[0],
    });
  });

  return marker;
};




  const initClusterer = () => {
  if (!mapRef.current || !window.google || !property.length) return;

  // Clear existing markers and clusterer
  if (clustererRef.current) {
    clustererRef.current.clearMarkers();
  }
  markersRef.current = [];

  // Create markers
  markersRef.current = property.map((hostel) => createHostelMarker(hostel));

  // Create clusterer with custom styling
  clustererRef.current = new MarkerClusterer({
    map: mapRef.current,
    markers: markersRef.current,
    renderer: {
      render: ({ count, position }) => {
        const google = window.google;
        return new google.maps.Marker({
          position,
          icon: {
            url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
              <svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
                <!-- Bigger animated ripple (more visible) -->
                <circle cx="30" cy="30" r="10" fill="black" opacity="0">
                  <animate 
                    attributeName="r" 
                    values="10;30" 
                    dur="1.5s" 
                    repeatCount="indefinite"
                    keyTimes="0;1"
                    calcMode="spline"
                    keySplines="0.4,0,0.2,1"
                  />
                  <animate 
                    attributeName="opacity" 
                    values="0.6;0" 
                    dur="1.5s" 
                    repeatCount="indefinite"
                  />
                </circle>
                
                <!-- Second ripple for smoother effect -->
                <circle cx="30" cy="30" r="10" fill="black" opacity="0">
                  <animate 
                    attributeName="r" 
                    values="10;25" 
                    dur="1.5s" 
                    begin="0.5s"
                    repeatCount="indefinite"
                    keyTimes="0;1"
                    calcMode="spline"
                    keySplines="0.4,0,0.2,1"
                  />
                  <animate 
                    attributeName="opacity" 
                    values="0.4;0" 
                    dur="1.5s" 
                    begin="0.5s"
                    repeatCount="indefinite"
                  />
                </circle>
                
                <!-- Solid base circle -->
                <circle cx="30" cy="30" r="18" fill="black"/>
                
                <!-- Inner glow -->
                <circle cx="30" cy="30" r="14" fill="black" opacity="0.8"/>
                
                <!-- Count text (bold and centered) -->
                <text x="30" y="35" font-family="Arial" font-size="16" font-weight="bold" 
                      fill="white" text-anchor="middle">
                  ${count}
                </text>
              </svg>
            `)}`,
            scaledSize: new google.maps.Size(60, 60),
          },
        });
      },
    },
    styles: [
      {
        textColor: "white",
        url: "",
        height: 60,
        width: 60,
        anchor: [30, 30],
        fontSize: 16,
        fontWeight: "bold",
      },
    ],
  });
};
  useEffect(() => {
    if (mapRef.current && isApiLoaded) {
      initClusterer();
    }
  }, [property, isApiLoaded]);

  console.log("selectedProperty", selectedProperty);

  // Set the first property as selected when component mounts
  useEffect(() => {
    // Check if there's a selected property in localStorage
    const storedSelectedProperty = localStorage.getItem("selectedMapProperty");

    if (storedSelectedProperty) {
      try {
        const parsedProperty = JSON.parse(storedSelectedProperty);
        setSelectedProperty(parsedProperty);
        setZoom(14); // Zoom in closer to the property
        setCenter({
          lat: parsedProperty?.location?.coordinates[1],
          lng: parsedProperty?.location?.coordinates[0],
        });
        // Clear the stored property after using it
        localStorage.removeItem("selectedMapProperty");
      } catch (error) {
        console.error("Error parsing stored property:", error);
      }
    } else if (property.length > 0 && window.innerWidth > 640 ) {
      // If no stored property, use the first property
      setSelectedProperty(property[0]);
      setZoom(14); // Zoom in closer to the property
      setCenter({
        lat: property[0]?.location?.coordinates[1],
        lng: property[0]?.location?.coordinates[0],
      });
    }
  }, [property]);

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);
      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);

  const getCurrencySymbol = (currencyCode) => {
    return currencyData[currencyCode] || currencyCode;
  };

  const loadGoogleMapsApi = () => {
    const script = document.createElement("script");
    script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyBv_hPcDOPcrTfHnLrFNduHgJWDwv1pjfU&libraries=places`;
    script.async = true;
    script.onload = () => setIsApiLoaded(true);
    document.head.appendChild(script);
  };

  useEffect(() => {
    if (window.google && window.google.maps) {
      setIsApiLoaded(true);
    } else {
      loadGoogleMapsApi();
    }
  }, []);
  useEffect(() => {
    if (mapRef.current && isApiLoaded) {
      initClusterer();
    }
  }, [showPrice]);
  const onSwitchToListView = () => {
    setShowMapList(true);
    setShowMap(false);
    updateMapState(false);
  };

  // // Custom Price Marker Component
  // const PriceMarker = ({ property, position }) => {
  //   // Format price to currency format
  //   const formatPrice = (price, currency) => {
  //     if (!price) return "₹0";
  //     return `${getCurrencySymbol(currency)} ${price.toLocaleString()}`;
  //   };

  //   const price =
  //     property?.lowestAveragePricePerNight?.value ||
  //     Math.floor(Math.random() * 100000) + 4000;
  //   const formattedPrice = formatPrice(
  //     price,
  //     property?.lowestAveragePricePerNight?.currency
  //   );

  //   return (
  //     <OverlayView
  //       position={position}
  //       mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}
  //       getPixelPositionOffset={(width, height) => ({
  //         x: -(width / 2),
  //         y: -(height / 2),
  //       })}
  //     >
  //       <div
  //         className="bg-primary-blue text-black max-w-[100px] w-[94px] rounded-full px-2 py-1 text-sm font-semibold shadow-md cursor-pointer flex items-center justify-center z-50"
  //         onClick={() => setSelectedProperty(property)}
  //       >
  //         {formattedPrice}
  //       </div>
  //     </OverlayView>
  //   );
  // };

  const getImageUrl = (url) => {
    return url && url.startsWith("http") ? url : `https://${url}`;
  };

  if (!isApiLoaded) {
    return <Loader open={true} />;
  }

  return (
    <>
      <div className="relative">
        <div className="absolute top-4 left-4 z-10 bg-white p-2 rounded-lg shadow-md flex items-center gap-2">
          <button
            onClick={() => setShowPrice(false)}
            className={`px-3 py-1 rounded-l-md text-sm ${
              !showPrice ? "bg-[#40E0D0] text-black font-medium" : "bg-gray-200"
            }`}
          >
            Names
          </button>
          <button
            onClick={() => setShowPrice(true)}
            className={`px-3 py-1 rounded-r-md text-sm ${
              showPrice ? "bg-[#40E0D0] text-black font-medium" : "bg-gray-200"
            }`}
          >
            Prices
          </button>
        </div>

        <button
          onClick={onSwitchToListView}
          className="fixed bottom-8 bg-black text-white z-50 px-4 py-2 rounded-[50px] left-[50%] transform -translate-x-1/2 flex items-center gap-2 shadow-lg"
        >
          <span className="flex items-center gap-2 text-base">
            List view
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width={24}
              height={24}
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth={2}
              strokeLinecap="round"
              strokeLinejoin="round"
              className="icon icon-tabler icons-tabler-outline icon-tabler-list"
            >
              <path stroke="none" d="M0 0h24v24H0z" fill="none" />
              <path d="M9 6l11 0" />
              <path d="M9 12l11 0" />
              <path d="M9 18l11 0" />
              <path d="M5 6l0 .01" />
              <path d="M5 12l0 .01" />
              <path d="M5 18l0 .01" />
            </svg>
          </span>
        </button>
      </div>
      {isApiLoaded ? (
        <GoogleMap
          mapContainerStyle={mapContainerStyle}
          zoom={zoom}
          center={center}
          options={{
            disableDefaultUI: true,
            clickableIcons: false,
          }}
          onLoad={onLoad}
        >
          {selectedProperty && (
            <InfoWindow
              position={{
                lat: selectedProperty.location.coordinates[1],
                lng: selectedProperty.location.coordinates[0],
              }}
              onCloseClick={() => setSelectedProperty(null)}
            >
              {/* Your existing InfoWindow content */}
              <div className="flex p-0 rounded-lg overflow-hidden shadow-md w-full max-h-[170px]">
                {/* Image Container */}
                <div className="w-[30%] relative h-auto">
                  <Image
                  width={150}
                  height={150}
                    src={getImageUrl(selectedProperty?.images?.[0]?.objectUrl)}
                    alt={selectedProperty.name}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Content Container */}
                <div className="w-[70%] bg-white h-full">
                  <div className="px-3 py-3 h-full">
                    {/* Property Name and Rating */}

                    <h2 className="text-[14px] leading-5 text-black font-bold truncate w-40">
                      {selectedProperty.name}
                    </h2>

                    <div
                      className="mb-1 mt-[6px] flex  items-center bg-[#40E0D026] rounded-full border border-[#E4E6E8] border-solid w-[90px] py-[2px] justify-center"
                      style={{ boxShadow: "0px 0.93px 3.26px 0px #00000012" }}
                    >
                      <span className="text-yellow-400 mr-1 flex">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width={24}
                          height={24}
                          viewBox="0 0 24 24"
                          fill="currentColor"
                          className="icon icon-tabler icons-tabler-filled icon-tabler-star text-yellow-400 w-[0.8rem] h-[0.8rem]"
                        >
                          <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                          <path d="M8.243 7.34l-6.38 .925l-.113 .023a1 1 0 0 0 -.44 1.684l4.622 4.499l-1.09 6.355l-.013 .11a1 1 0 0 0 1.464 .944l5.706 -3l5.693 3l.1 .046a1 1 0 0 0 1.352 -1.1l-1.091 -6.355l4.624 -4.5l.078 -.085a1 1 0 0 0 -.633 -1.62l-6.38 -.926l-2.852 -5.78a1 1 0 0 0 -1.794 0l-2.853 5.78z" />
                        </svg>
                      </span>
                      <span className="font-semibold text-[12px] leading-4">
                        {selectedProperty.starRating || "5"}
                      </span>
                      <span className="text-[#737373] text-[12px] leading-4 ml-1">
                        ({selectedProperty.reviewCount || "672"})
                      </span>
                    </div>
                    {/* Distance from city center */}
                    <p className="text-[#888888] text-[12px] mb-3">
                      {selectedProperty?.distanceFromCityCenter || "1.5"} Km
                      from city Center
                    </p>

                    {/* Amenities */}
                    <div className="flex items-center gap-4 mb-3">
                      <div className="flex items-center">
                        <span className=" mr-1">
                          <FaWifi className="text-[#40E0D0] text-xl h-[16px] w-[16px]" />
                        </span>
                        <span className="text-[12px] text-black font-medium leading-3">
                          wifi
                        </span>
                      </div>
                      <div className="flex items-center">
                        <span className="text-[#40E0D0] text-xl h-[16px] w-[16px] mr-1">
                          {/* AC icon - using a placeholder */}
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                            width="16"
                            height="16"
                          >
                            <path d="M22 11h-4.17l3.24-3.24-1.41-1.42L15 11h-2V9l4.66-4.66-1.42-1.41L13 6.17V2h-2v4.17L7.76 2.93 6.34 4.34 11 9v2H9L4.34 6.34 2.93 7.76 6.17 11H2v2h4.17l-3.24 3.24 1.41 1.42L9 13h2v2l-4.66 4.66 1.42 1.41L11 17.83V22h2v-4.17l3.24 3.24 1.42-1.41L13 15v-2h2l4.66 4.66 1.41-1.42L17.83 13H22z" />
                          </svg>
                        </span>
                        <span className="text-[12px] text-black font-medium leading-3">
                          AC
                        </span>
                      </div>
                      <div className="flex items-center">
                        <span className=" mr-1">
                          <PiBathtub className="text-[#40E0D0] text-xl h-[16px] w-[16px]" />
                        </span>
                        <span className="text-[12px] text-black font-medium leading-3">
                          Ensuite
                        </span>
                      </div>
                    </div>

                    {/* Price and View Button */}
                    <div className="flex justify-between items-center">
                      <div>
                        <span className="text-base font-bold">
                          {getCurrencySymbol(
                            selectedProperty?.lowestAveragePricePerNight
                              ?.currency || "USD"
                          )}
                          {selectedProperty?.lowestAveragePricePerNight
                            ?.value || "35"}
                        </span>
                        <span className="text-[#737373] text-base">
                          / night
                        </span>
                      </div>
                      <Link
                        href={`/hostels-detail/${selectedProperty?._id}`}
                        prefetch={false}
                        className=" text-black text-sm font-[700]"
                      >
                        View
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </InfoWindow>
          )}
        </GoogleMap>
      ) : (
        <p>Loading Google Maps API...</p>
      )}
    </>
  );
};

export default newMapComponent;
