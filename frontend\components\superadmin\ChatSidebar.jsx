 
import React from "react";
import UserCard from "./usercard";
import { Search } from "lucide-react";

const users = [
  {
    id: 1,
    name: "<PERSON>",
    status: "10min ago",
    message:
      "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint. ",
    unseenmessage: 2,
    image: "https://i.pravatar.cc/150?img=1",
  },
  {
    id: 2,
    name: "<PERSON>",
    status: "Just now",
    message:
      "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint. ",
    unseenmessage: 3,
    image: "https://i.pravatar.cc/150?img=2",
  },
  {
    id: 3,
    name: "<PERSON><PERSON>",
    status: "5min ago",
    message:
      "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint. ",
    unseenmessage: 4,
    image: "https://i.pravatar.cc/150?img=3",
  },
  {
    id: 4,
    name: "<PERSON>",
    status: "10min ago",
    message:
      "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint. ",
    unseenmessage: 2,
    image: "https://i.pravatar.cc/150?img=1",
  },
  {
    id: 5,
    name: "<PERSON>",
    status: "Just now",
    message:
      "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint. ",
    unseenmessage: 3,
    image: "https://i.pravatar.cc/150?img=2",
  },
  {
    id: 6,
    name: "Arlene McCoy",
    status: "5min ago",
    message:
      "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint. ",
    unseenmessage: 4,
    image: "https://i.pravatar.cc/150?img=3",
  },
  {
    id: 7,
    name: "Arlene McCoy",
    status: "5min ago",
    message:
      "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint. ",
    unseenmessage: 4,
    image: "https://i.pravatar.cc/150?img=3",
  },
];

function Sidebar({ onSelectUser }) {
  return (
    <div className="w-full p-2 bg-white border rounded-lg overflow-y-auto dark:bg-black dark:border-none py-5">
      <div className="flex gap-3 mb-4">
        <button className="px-10 py-2 text-white text-sm font-semibold font-nunito bg-sky-blue-650 rounded">
          All
        </button>
        <button className="px-4 py-2 text-gray-700 border rounded text-sm font-semibold font-nunito dark:bg-transparent dark:text-[#B6B6B6]">
          Complain
        </button>
        <button className="px-4 py-2 text-gray-700 border rounded text-sm font-semibold font-nunito dark:bg-transparent dark:text-[#B6B6B6]">
          Query Solved
        </button>
      </div>
      <div className="relative w-full h-[38px] ">
        {" "}
        <input
          type="search"
          className="relative flex items-center justify-center w-full py-2 pl-10 pr-4 text-sm font-nunito border border-gray-200 rounded-full outline-none text-gray-130 h-9 bg-white dark:bg-transparent dark:text-[#B6B6B6]"
          placeholder="Search User"
        />
        <Search
          className="absolute -translate-y-1/2 top-1/2 left-3 text-gray-600 dark:text-[#B6B6B6]"
          size={18}
        />
      </div>
      <div className="overflow-y-scroll h-full" onClick={onSelectUser}>
        {users.map((user) => (
          <UserCard
            key={user.id}
            name={user.name}
            message={user.message}
            unseenmessage={user.unseenmessage}
            status={user.status} 
            image={user.image}
          />
        ))}
      </div>
    </div>
  );
}

export default Sidebar;
