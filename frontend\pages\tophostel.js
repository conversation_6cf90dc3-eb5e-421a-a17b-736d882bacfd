import React, { useEffect, useRef, useState } from "react";
import SwapVertOutlinedIcon from "@mui/icons-material/SwapVertOutlined";
import Image from "next/image";
import dynamic from "next/dynamic";
import Banner from "@/components/home/<USER>";
import BestCitiesSlider from "@/components/home/<USER>";
// import CountryFeaturedHostel from "@/components/home/<USER>";
import { useRouter } from "next/router";
import {
  getTopHostelByCountryApi,
  likeUnlikePropertyApi,
} from "@/services/webflowServices";
import { useNavbar } from "@/components/home/<USER>";
import countries from "world-countries";
import Head from "next/head";
import { HostelCardSkeleton } from "@/components/home/<USER>";
import { toast } from "react-hot-toast";
import { getItemLocalStorage } from "@/utils/browserSetting";
import HostelCard from "@/components/home/<USER>";
import { getContentSchemaOtherPages } from "@/lib/schema/contentSchemaOtherPages";
import { getFaqSchema } from "@/lib/schema/faqSchema";
import { faqData } from "@/utils/faqData";
// const HostelCard = dynamic(() => import("@/components/home/<USER>"), {
//   ssr: false,
// });
const NewMapComponent = dynamic(
  () => import("@/components/map/newMapComponent"),
  {
    ssr: false,
  }
);
const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

// eslint-disable-next-line no-unused-vars
const Page = ({ country2 }) => {
  const [hostelData, setHostelData] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [locationImages, setLocationImages] = useState([]);
  const [locationAbout, setLocationAbout] = useState("");
  const [loading, setLoading] = useState(true);
  const [currencyData, setCurrencyData] = useState({});
  // const [isUpdate, setIsUpdateData] = useState(false);
  const [selectedCity, setSelectedCity] = useState(null);
  const isFirstRender = useRef(null);
  const router = useRouter();
  const { country } = router.query;
  const { currencyCode2, token, updateMapState } = useNavbar();
  const [showMap, setShowMap] = useState(false);
  const [showMapList, setShowMapList] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const [showToggle, setShowToggle] = useState(false);

  const textRef = useRef(null);
  const mapBottomRef = useRef(null);

  const handleMap = () => {
    setShowMap(true);
    setShowMapList(false);
    updateMapState(true);

    // Scroll to the bottom of the map after a short delay (wait for rendering)
    setTimeout(() => {
      mapBottomRef.current?.scrollIntoView({ behavior: "smooth" });
    }, 100);
  };
  useEffect(() => {
    const el = textRef.current;
    if (el) {
      setShowToggle(el.scrollHeight > el.clientHeight);
    }
  }, [locationAbout]);
  useEffect(() => {
    const fetchHostelData = async () => {
      setLoading(true);
      try {
        if (country) {
          const response = await getTopHostelByCountryApi(
            country,
            currencyCode2 || "USD",
            selectedCity
          );
          setHostelData(response?.data?.data?.properties || []);
          setLocationImages(response?.data?.data?.locations || []);
          setLocationAbout(response?.data?.data?.about || "");
        }
      } catch (error) {
        console.error("Error fetching stay data:", error);
      } finally {
        setLoading(false);
      }
    };
    if (!isFirstRender.current) {
      fetchHostelData();
    } else {
      isFirstRender.current = false;
    }
  }, [country, currencyCode2, token, selectedCity]);

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);
      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);

  const getCurrencySymbol = (currencyCode) => {
    return currencyData[currencyCode] || currencyCode;
  };

  const handleShowMap = () => {
    setShowMap(!showMap);
    updateMapState(!showMap);
    setShowMapList(false);
  };

  const handleCitySelect = (city) => {
    setSelectedCity(city);
  };

  const handleLikeUnlike = async (hostelId, liked) => {
    if (!getItemLocalStorage("token")) {
      toast.error("Please login first!!");
      return;
    }
    setLoading(true);
    try {
      const payload = { isLike: !liked };
      const response = await likeUnlikePropertyApi(hostelId, payload);
      const updatedHostel = response?.data?.data || response?.data;
      if (updatedHostel && updatedHostel._id) {
        setHostelData((prev) =>
          prev.map((item) =>
            item._id === updatedHostel._id
              ? { ...item, ...updatedHostel }
              : item
          )
        );
      }
    } catch (error) {
      console.error("Error updating like status:", error);
      toast.error("Failed to update like status");
    } finally {
      setLoading(false);
    }
  };

  // const canonical = country2
  //   ? `https://mixdorm.com/tophostel?country=${country2}`
  //   : "https://mixdorm.com/tophostel";

  const faqs = faqData[country.toLowerCase()] || null;
  const faqSchema = faqs ? getFaqSchema(faqs) : null;

  const contentSchema =
    country === "Australia"
      ? getContentSchemaOtherPages({
          title: "Top Hostels in Australia | Mixdorm",
          description:
            "Book Top hostels in Australia with MixDorm – Enjoy affordable stays, vibrant vibes, and easy booking for solo travelers and backpackers",
          authorName: "Mixdorm",
          datePublished: "2023-07-17",
        })
      : country === "Brazil"
      ? getContentSchemaOtherPages({
          title: "Top Hostels in Brazil | Mixdorm",
          description:
            "Book Your Stay at Top Hostels in Brazil With Mixdorm. Experience Comfort, Community, and Adventure While Exploring the Beauty of This Vibrant Country.",
          authorName: "Mixdorm",
          datePublished: "2023-07-17",
        })
      : country === "Colombia"
      ? getContentSchemaOtherPages({
          title: "Top Hostels in Colombia | Mixdorm",
          description:
            "Discover the Top Hostels in Colombia With Mixdorm! Find Your Perfect Stay, Meet Fellow Travelers, and Explore Vibrant Cities. Adventure Awaits!",
          authorName: "Mixdorm",
          datePublished: "2023-07-17",
        })
      : country === "Spain"
      ? getContentSchemaOtherPages({
          title: "Top Hostels in Spain | Mixdorm",
          description:
            "Book Top-Rated Hostels in Spain with Mixdorm. Find Affordable, Safe, and Social Stays for Backpackers and Travelers Across Top Spanish Cities.",
          authorName: "Mixdorm",
          datePublished: "2023-07-17",
        })
      : country === "France"
      ? getContentSchemaOtherPages({
          title: "Top Hostels in France | Mixdorm",
          description:
            "Mixdorm Brings You the Best Hostels in France—Cheap, Clean, and Perfectly Located. Book Your Stay Today and Travel Smart!",
          authorName: "Mixdorm",
          datePublished: "2023-07-17",
        })
      : country === "Indonesia"
      ? getContentSchemaOtherPages({
          title: "Top Hostels in Indonesia | Mixdorm",
          description:
            "Plan Your Indonesian Trip with Mixdorm! Find Top Hostels in Indonesia, Across Islands—Affordable, Comfortable, and Great for Meeting Travelers.",
          authorName: "Mixdorm",
          datePublished: "2023-07-17",
        })
      : country === "India"
      ? getContentSchemaOtherPages({
          title: "Top Hostels in India | Mixdorm",
          description:
            "Explore Best Hostels in India with Mixdorm. Find Budget-Friendly Accommodations, Unique Experiences, and Connect with Like-Minded Travelers",
          authorName: "Mixdorm",
          datePublished: "2023-07-17",
        })
      : country === "Italy"
      ? getContentSchemaOtherPages({
          title: "Top Hostels in Italy | Mixdorm",
          description:
            "Find Top Hostels in Italy with Mixdorm. Budget Stays in Rome, Venice & Beyond. Book Cozy, Clean Dorms Perfect for Backpackers and Solo Travelers",
          authorName: "Mixdorm",
          datePublished: "2023-07-17",
        })
      : country === "Mexico"
      ? getContentSchemaOtherPages({
          title: "Top Hostels in Mexico | Mixdorm",
          description:
            "Find Top-Rated Hostels in Mexico with Mixdorm. Perfect for Solo Travelers, Backpackers & Groups. Book Your Budget Stay Now!",
          authorName: "Mixdorm",
          datePublished: "2023-07-17",
        })
      : country === "Peru"
      ? getContentSchemaOtherPages({
          title: "Top Hostels in Peru | Mixdorm",
          description:
            "Discover and Book Top-Rated Hostels in Peru with Mixdorm. Affordable, Secure, and Perfect for Backpackers. Start Your Peruvian Adventure Today!",
          authorName: "Mixdorm",
          datePublished: "2023-07-17",
        })
      : country === "Portugal"
      ? getContentSchemaOtherPages({
          title: "Top Hostels in Portugal | Mixdorm",
          description:
            "Explore Portugal on a Budget! Book Top-Rated Hostels with Mixdorm for a Comfy, Affordable Stay In Lisbon, Porto, and More.",
          authorName: "Mixdorm",
          datePublished: "2023-07-17",
        })
      : country === "Thailand"
      ? getContentSchemaOtherPages({
          title: "Top Hostels in Thailand | Mixdorm",
          description:
            "Explore Thailand on a Budget! Book Top Hostels in Bangkok, Chiang Mai & More with Mixdorm. Safe, Cheap, and Backpacker-Friendly Stays.",
          authorName: "Mixdorm",
          datePublished: "2023-07-17",
        })
      : country === "USA"
      ? getContentSchemaOtherPages({
          title: "Top Hostels in USA | Mixdorm",
          description:
            "Book Top Hostels in USA with Mixdorm. Affordable, Safe, and Social Stays for Every Traveler. Explore and Book Your Next Adventure Today!",
          authorName: "Mixdorm",
          datePublished: "2023-07-17",
        })
      : country === "Vietnam"
      ? getContentSchemaOtherPages({
          title: "Top Hostels in Vietnam | Mixdorm",
          description:
            "Booking Hostels in Vietnam Made Easy. Top Picks in Hanoi, Da Nang, Hoi an & More—Great Rates on Mixdorm for Every Traveler.",
          authorName: "Mixdorm",
          datePublished: "2023-07-17",
        })
      : null;

  return (
    <>
      <Head>
        <title>Top Hostels in {country} | Mixdorm</title>

        {/* <link rel="canonical" href={canonical} /> */}

        {country === "Australia" && (
          <>
            <meta
              name='description'
              content='Book Top hostels in Australia with MixDorm – Enjoy affordable stays, vibrant vibes, and easy booking for solo travelers and backpackers'
            />
          </>
        )}
        {country === "Brazil" && (
          <>
            <meta
              name='description'
              content='Book Your Stay at Top Hostels in Brazil With Mixdorm. Experience Comfort, Community, and Adventure While Exploring the Beauty of This Vibrant Country.'
            />
          </>
        )}
        {country === "Colombia" && (
          <>
            <meta
              name='description'
              content='Discover the Top Hostels in Colombia With Mixdorm! Find Your Perfect Stay, Meet Fellow Travelers, and Explore Vibrant Cities. Adventure Awaits!'
            />
          </>
        )}
        {country === "Spain" && (
          <>
            <meta
              name='description'
              content='Book Top-Rated Hostels in Spain with Mixdorm. Find Affordable, Safe, and Social Stays for Backpackers and Travelers Across Top Spanish Cities.'
            />
          </>
        )}
        {country === "France" && (
          <>
            <meta
              name='description'
              content='Mixdorm Brings You the Best Hostels in France—Cheap, Clean, and Perfectly Located. Book Your Stay Today and Travel Smart!'
            />
          </>
        )}
        {country === "Indonesia" && (
          <>
            <meta
              name='description'
              content='Plan Your Indonesian Trip with Mixdorm! Find Top Hostels in Indonesia, Across Islands—Affordable, Comfortable, and Great for Meeting Travelers.'
            />
          </>
        )}
        {country === "Italy" && (
          <>
            <meta
              name='description'
              content='Find Top Hostels in Italy with Mixdorm. Budget Stays in Rome, Venice & Beyond. Book Cozy, Clean Dorms Perfect for Backpackers and Solo Travelers'
            />
          </>
        )}
        {country === "Japan" && (
          <>
            <meta
              name='description'
              content='Stay In the Best Hostels in Japan with Mixdorm. Budget-Friendly Dorms, Prime Locations, and Hassle-Free Booking. Reserve Your Bed Now!'
            />
          </>
        )}
        {country === "Mexico" && (
          <>
            <meta
              name='description'
              content='Find Top-Rated Hostels in Mexico with Mixdorm. Perfect for Solo Travelers, Backpackers & Groups. Book Your Budget Stay Now!'
            />
          </>
        )}
        {country === "Peru" && (
          <>
            <meta
              name='description'
              content='Discover and Book Top-Rated Hostels in Peru with Mixdorm. Affordable, Secure, and Perfect for Backpackers. Start Your Peruvian Adventure Today!'
            />
          </>
        )}
        {country === "Portugal" && (
          <>
            <meta
              name='description'
              content='Explore Portugal on a Budget! Book Top-Rated Hostels with Mixdorm for a Comfy, Affordable Stay In Lisbon, Porto, and More.'
            />
          </>
        )}
        {country === "Thailand" && (
          <>
            <meta
              name='description'
              content='Explore Thailand on a Budget! Book Top Hostels in Bangkok, Chiang Mai & More with Mixdorm. Safe, Cheap, and Backpacker-Friendly Stays.'
            />
          </>
        )}
        {country === "USA" && (
          <>
            <meta
              name='description'
              content='Book Top Hostels in USA with Mixdorm. Affordable, Safe, and Social Stays for Every Traveler. Explore and Book Your Next Adventure Today!'
            />
          </>
        )}
        {country === "Vietnam" && (
          <>
            <meta
              name='description'
              content='Booking Hostels in Vietnam Made Easy. Top Picks in Hanoi, Da Nang, Hoi an & More—Great Rates on Mixdorm for Every Traveler.'
            />
          </>
        )}
        {country === "India" && (
          <>
            <meta
              name='description'
              content='Explore Best Hostels in India with Mixdorm. Find Budget-Friendly Accommodations, Unique Experiences, and Connect with Like-Minded Travelers'
            />
          </>
        )}
        {contentSchema && (
          <script
            type='application/ld+json'
            dangerouslySetInnerHTML={{ __html: JSON.stringify(contentSchema) }}
          />
        )}
        {faqSchema && (
          <script
            type='application/ld+json'
            dangerouslySetInnerHTML={{ __html: JSON.stringify(faqSchema) }}
          />
        )}
      </Head>
      <Loader open={loading} />
      {/* <div
        style={{
          backgroundImage: `url(${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/top-black-bg.webp)`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
        className="pb-10 md:pb-14"
      >
        <Banner country={country} />
        <BestCitiesSlider country={country} onCitySelect={handleCitySelect} />
      </div> */}
      <div className='relative pb-10 md:pb-14'>
        <Image
          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/top-black-bg.webp`}
          alt='Top Hostels in Australia'
          layout='fill'
          priority
          className='object-cover'
        />
        <div className='relative z-10'>
          <Banner country={country} />
          <BestCitiesSlider country={country} onCitySelect={handleCitySelect} />
        </div>
      </div>

      <section
        className={` border-t border-gray-200 ${
          showMap ? "pt-6 pb-6" : "pt-8 lg:pb-28"
        } `}
      >
        <div className='w-full xxxl:container px-4 lg:px-10'>
          {!showMap && (
            <div className='sm:flex items-center justify-between pb-4'>
              <h2 className='font-bold text-black font-manrope md:text-3xl sm:text-2xl text-xl mb-3'>
                Top Hostels in{" "}
                <span className='text-primary-blue xs:text-4xl text-3xl  font-semibold font-mashiny'>
                  {country}
                </span>
              </h2>
              <div className='relative flex gap-2'>
                <button className='border border-[#EEEEEE] text-black px-4 py-2 rounded-3xl flex items-center text-sm font-semibold'>
                  <SwapVertOutlinedIcon className='mr-2' />
                  Sort by
                </button>
                <button className='border border-[#EEEEEE] text-black px-4 py-2 rounded-3xl flex items-center text-sm font-semibold'>
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/filter.png`}
                    width={14}
                    height={14}
                    alt='Filter'
                    className='w-full max-w-3 mr-2'
                    loading='lazy'
                  />
                  Filter
                </button>
              </div>
            </div>
          )}
          {!showMap && (
            <div className='mid:flex flex-col pb-10 gap-x-7 listing'>
              <div className='w-full  '>
                <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
                  {hostelData && hostelData.length > 0 ? (
                    hostelData?.map((item) => (
                      <HostelCard
                        key={item?._id}
                        tag='Top Rated'
                        title={`${item?.name},${item?.address?.country}`}
                        image={item?.images?.[0]?.objectUrl}
                        images={item?.images || []}
                        guest='4-6 guest'
                        time='2 days 3 nights'
                        feature={item?.freeFacilities}
                        price={`${getCurrencySymbol(
                          item?.lowestAveragePricePerNight?.currency
                        )} ${item?.lowestAveragePricePerNight?.value}`}
                        rating='4.9'
                        review='672'
                        hostelId={item?._id}
                        liked={item?.liked}
                        onLikeUnlike={handleLikeUnlike}
                      />
                    ))
                  ) : (
                    <>
                      {[...Array(10)].map((_, i) => (
                        <HostelCardSkeleton key={`skeleton-${i}`} />
                      ))}
                    </>
                  )}
                </div>
              </div>
              {/* <div className="mid:w-[40%] w-full">
              <MapComponent property={hostelData} />
            </div> */}

              <button
                onClick={() => {
                  handleMap();
                  handleShowMap();
                }}
                className='fixed bottom-8 bg-black text-white z-20 px-4 py-2 rounded-[50px] left-[50%] transform -translate-x-1/2 flex items-center gap-2'
              >
                <span className='flex items-center gap-2 text-base'>
                  View Map
                  <svg
                    xmlns='http://www.w3.org/2000/svg'
                    width={24}
                    height={24}
                    viewBox='0 0 24 24'
                    fill='none'
                    stroke='currentColor'
                    strokeWidth={2}
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    className='icon icon-tabler icons-tabler-outline icon-tabler-map'
                  >
                    <path stroke='none' d='M0 0h24v24H0z' fill='none' />
                    <path d='M3 7l6 -3l6 3l6 -3v13l-6 3l-6 -3l-6 3v-13' />
                    <path d='M9 4v13' />
                    <path d='M15 7v13' />
                  </svg>
                </span>
              </button>
            </div>
          )}

          {!showMapList && showMap && (
            <div className='mid:w-[100%] w-full mt-6 self-start'>
              <NewMapComponent
                property={hostelData}
                setShowMapList={setShowMapList}
                showMapList={showMapList}
                showMap={showMap}
                setShowMap={setShowMap}
              />
              <div ref={mapBottomRef} />
            </div>
          )}

          {!showMap && (
            <>
              {/* <h2 className="font-semibold text-black font-manrope md:text-3xl sm:text-2xl text-xl mb-3">
                {country} Best <span className="text-primary-blue"> Location</span>
              </h2>
              <div className="grid md:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-6 mb-10">
                <div className="col-span-2 relative">
                  <Image
                    src={locationImages?.[0]?.image?.url}
                    width={806}
                    height={390}
                    alt="BgImg"
                    className="w-full rounded-lg h-full max-h-[329px] object-cover"
                    loading="lazy"
                  />
                  <div className="absolute bottom-[5%] left-6 bg-white w-[90%] rounded-lg p-4 max-w-[354px]">
                    <div className="flex items-start justify-between">
                      <h3 className="text-lg font-bold text-black mb-1">
                        {locationImages?.[0]?.name}
                      </h3>
                      <div className="flex items-center justify-between">
                        <Link
                          href='#'
                          className='bg-[#F2F4F6] rounded-full flex items-center justify-center w-7 h-7 hover:bg-primary-blue hover:text-white'
                          prefetch={false}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width={24}
                            height={24}
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth={2}
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="icon icon-tabler icons-tabler-outline icon-tabler-arrow-right"
                          >
                            <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                            <path d="M5 12l14 0" />
                            <path d="M13 18l6 -6" />
                            <path d="M13 6l6 6" />
                          </svg>

                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
                {locationImages.length > 1 && (
                  <div className="relative">
                    <Image
                      src={locationImages?.[1]?.image?.url}
                      width={519}
                      height={390}
                      alt="BgImg"
                      className="w-full rounded-lg h-full max-h-[329px]  object-cover"
                      loading="lazy"
                    />
                    <div className="absolute bottom-[5%] left-[5%] bg-white w-[90%] rounded-lg p-4 max-w-[354px]">
                      <div className="flex items-start justify-between">
                        <h3 className="text-lg font-bold text-black mb-1">
                          {locationImages?.[1]?.name}
                        </h3>
                        <div className="flex items-center justify-between">
                          <Link
                            href='#'
                            className='bg-[#F2F4F6] rounded-full flex items-center justify-center w-7 h-7 hover:bg-primary-blue hover:text-white'
                            prefetch={false}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width={24}
                              height={24}
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth={2}
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="icon icon-tabler icons-tabler-outline icon-tabler-arrow-right"
                            >
                              <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                              <path d="M5 12l14 0" />
                              <path d="M13 18l6 -6" />
                              <path d="M13 6l6 6" />
                            </svg>

                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                {locationImages.length > 2 &&
                  locationImages?.slice(2).map((data) => (
                    <>
                    <div className="relative">
                      <Image
                        src={data?.image?.url}
                        width={519}
                        height={390}
                        alt="BgImg"
                        className="w-full rounded-lg h-full max-h-[329px] object-cover"
                        loading="lazy"
                      />
                      <div className="absolute bottom-[5%] left-[5%] bg-white w-[90%] rounded-lg p-4 max-w-[354px]">
                        <div className="flex items-start justify-between">
                          <h3 className="text-lg font-bold text-black mb-1">
                            {data?.name}
                          </h3>
                          <div className="flex items-center justify-between">
                            <Link
                              href='#'
                              className='bg-[#F2F4F6] rounded-full flex items-center justify-center w-7 h-7 hover:bg-primary-blue hover:text-white'
                              prefetch={false}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width={24}
                                height={24}
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth={2}
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="icon icon-tabler icons-tabler-outline icon-tabler-arrow-right"
                              >
                                <path stroke="none" d="M0 0h24v24H0z" fill="none" />
                                <path d="M5 12l14 0" />
                                <path d="M13 18l6 -6" />
                                <path d="M13 6l6 6" />
                              </svg>

                            </Link>
                          </div>
                        </div>
                      </div>
                    </div>
                    </>
                  ))}
              </div> */}

              <div className='mb-14 md:mb-4 lg:mb-0 p-4 rounded-3xl shadow-md border border-gray-200'>
                <h2 className='font-semibold text-black font-manrope md:text-3xl sm:text-2xl text-xl mb-3'>
                  Explore{" "}
                  <span className='text-primary-blue xs:text-4xl text-3xl  font-semibold font-mashiny'>
                    {country}
                  </span>{" "}
                  With <span className='text-primary-blue'>Mix</span>dorm
                </h2>
                <div className='relative'>
                  <p
                    ref={textRef}
                    className={`block mt-3  font-medium text-[#737373] font-Poppins mb-1 text-sm md:text-[15px] ${
                      !expanded ? "line-clamp-6" : ""
                    }`}
                    dangerouslySetInnerHTML={{ __html: locationAbout }}
                  ></p>{" "}
                  {!expanded && (
                    <div className='absolute bottom-0 left-0 right-0 h-8 pointer-events-none' />
                  )}
                  {showToggle && (
                    <button
                      className='text-black mt-1 float-end text-sm'
                      onClick={() => setExpanded(!expanded)}
                    >
                      {expanded ? "View Less" : "Read More"}
                    </button>
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      </section>
      {/* {!showMap && <CountryFeaturedHostel />} */}
    </>
  );
};

export default Page;

export async function getServerSideProps(context) {
  const country2 = context.query.country || null;

  // If no country parameter is provided, redirect to the same URL with Australia as default
  if (!country2) {
    return {
      redirect: {
        destination: "/tophostel?country=Australia",
        permanent: false,
      },
    };
  }

  return {
    props: {
      country2,
    },
  };
}
