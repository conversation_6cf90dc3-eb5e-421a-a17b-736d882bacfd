import mongoose from 'mongoose';
const Schema = mongoose.Schema;

const groupSchema = new Schema({
    name: { 
        type: String, 
        required: true 
    },
    createdBy: { 
        type: Schema.Types.ObjectId, ref: 'User', 
        required: true 
    },
    members: [{ 
        type: Schema.Types.ObjectId, ref: 'User' 
    }],
    balances: [
        {
          userId: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
          amount: { type: Number, default: 0 },
        },
    ],
    expenses: [{ 
        type: Schema.Types.ObjectId, ref: 'Expense' 
    }],
    isDeleted: { 
        type: Boolean, 
        default: false 
    },
    deletedAt: { 
        type: Date, 
        default: null 
    },
});

const groupModel = mongoose.model('Group', groupSchema);

export default groupModel;