 
import { Bs<PERSON><PERSON>clip } from "react-icons/bs";
import { FiSend } from "react-icons/fi";
import { LuSmile } from "react-icons/lu";

function MessageInput() {
  return (
    <div className="relative flex items-center ">
      <input
        type="text"
        placeholder="Write Message..."
        className="w-full p-3 text-sm font-poppins font-medium border border-gray-300 rounded-lg focus:outline-none dark:bg-transparent dark:text[#B6B6B6]"
      />
      <div className="absolute right-11 flex items-center">
        <button className="p-1 text-gray-500 hover:text-gray-700 dark:text[#B6B6B6] dark:hover:text-gray-200">
          <BsPaperclip size={20} />
        </button>
        <button className="p-1 text-gray-500 hover:text-gray-700 dark:text[#B6B6B6] dark:hover:text-gray-200">
          <LuSmile size={20} />
        </button>
      </div>
      <button className="absolute right-1 p-2 text-white bg-sky-blue-650 rounded-lg hover:bg-blue-600">
        <FiSend size={20} />
      </button>
    </div>
  );
}

export default MessageInput;
