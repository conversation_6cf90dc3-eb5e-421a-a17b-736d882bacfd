import React, { useState, Fragment } from "react";
import AddRoom from "@/components/ownerFlow/dashboard/addRooms";
import dynamic from "next/dynamic";
import Image from "next/image";
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
  Transition,
} from "@headlessui/react";
import Head from "next/head";

const Roomlist = dynamic(
  () => import("@/components/ownerFlow/dashboard/roomList"),
  {
    ssr: false,
  }
);

const AddEditRoom = dynamic(
  () => import("@/components/ownerFlow/dashboard/addEditRoom"),
  {
    ssr: false,
  }
);

const ViewRoom = dynamic(
  () => import("@/components/ownerFlow/dashboard/viewRoom"),
  {
    ssr: false,
  }
);

const RateAvalability = dynamic(
  () => import("@/components/ownerFlow/dashboard/rateAvaibility"),
  {
    ssr: false,
  }
);

const Calendar = dynamic(
  () => import("@/components/ownerFlow/dashboard/calendar"),
  {
    ssr: false,
  }
);

const Filter = dynamic(() => import("../../../components/model/filter"), {
  ssr: false,
});

const Roommanagement = () => {
  const [activeTab, setActiveTab] = useState(1);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const closefilterModal = () => setIsModalOpen(false);
  const [editRoom, setEditRoom] = useState(null);
  const [editId, setEditId] = useState(null);
  const [isOpen, setIsOpen] = useState(false);
  const [iseditOpen, setIseditOpen] = useState(false);
  const [isviewOpen, setIsviewOpen] = useState(false);
  const [isUpdate, setIsUpdate] = useState(false);
  // const openModal = () => setIsOpen(true);
  const closeModal = () => setIsOpen(false);
  const closeeditModal = () => setIseditOpen(false);
  const closeviewModal = () => setIsviewOpen(false);

  // Filter Modal
  // eslint-disable-next-line no-unused-vars
  const [openFilter, setOpenFilter] = useState(false);

  const renderTabContent = () => {
    switch (activeTab) {
      case 1:
        return (
          <Roomlist
            setActiveTab={setActiveTab}
            setEditRoom={setEditRoom}
            setEditId={setEditId}
            setIseditOpen={setIseditOpen}
            setIsviewOpen={setIsviewOpen}
            isUpdate={isUpdate}
          />
        );
      case 2:
        return <Calendar />;
      // case 3:
      //   return (
      //     <Roomlist setActiveTab={setActiveTab} setEditRoom={setEditRoom} />
      //   );
      // case 3:
      //   return <AddEditRoom editRoom={editRoom} />;
      case 3:
        return <RateAvalability />;
      case 4:
        return (
          <AddRoom setActiveTab={setActiveTab} setIsUpdate={setIsUpdate} />
        );
      case 5:
        return <AddEditRoom editRoom={editRoom} setActiveTab={setActiveTab} />;
      default:
        return (
          <Roomlist
            setActiveTab={setActiveTab}
            setEditRoom={setEditRoom}
            setEditId={setEditId}
            setIseditOpen={setIseditOpen}
            setIsviewOpen={setIsviewOpen}
            isUpdate={isUpdate}
          />
        );
    }
  };

  return (
    <>
      <Head>
        <title>Manage Rooms and Pricing | Mixdorm</title>
      </Head>
      <section className='w-full'>
        <div className='flex justify-between items-center'>
          <h1 className='page-title'>Room Management</h1>
          <div className='flex sm:gap-2 gap-1'>
            <button
              className='sm:flex block items-center text-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-gray-300 font-medium cursor-not-allowed'
              // onClick={() => setIsModalOpen(true)}
            >
              <Image
                className='sm:mr-2 mx-auto'
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/filter.svg`}
                width={20}
                height={20}
              />
              Filter
            </button>
            <button
              className='flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-gray-300 font-medium cursor-not-allowed'
              // onClick={openModal}
            >
              Add Room
            </button>
            {/* <button
              className='flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-primary-blue font-medium'
              onClick={openeditModal}
            >
              Edit Room
            </button> */}
          </div>
        </div>
        <div className='w-full mt-4 bg-white shadow-4xl rounded-2xl'>
          <div className='w-full'>{renderTabContent()}</div>
        </div>

        {isModalOpen && (
          <Dialog
            open={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            className='relative z-50'
          >
            <DialogBackdrop
              transition
              className='fixed inset-0 bg-[#000000B2] transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in'
            />

            <div className='fixed inset-0 z-10 w-screen overflow-y-auto'>
              <div className='flex min-h-full justify-center p-4 text-center items-center sm:p-0'>
                <DialogPanel
                  transition
                  className='relative transform overflow-hidden rounded-2xl bg-white text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-full sm:max-w-[490px] max-w-full w-full data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95'
                >
                  <div className='bg-white sm:px-7 sm:pb-7 pt-3 p-3 pb-3'>
                    <DialogTitle>
                      <h3 className='text-center text-black font-bold sm:text-lg text-sm'>
                        Filter
                      </h3>
                    </DialogTitle>
                    <Filter closefilterModal={closefilterModal} />
                  </div>
                </DialogPanel>
              </div>
            </div>
          </Dialog>
        )}

        <Transition show={isOpen} as={Fragment}>
          <Dialog as='div' className='relative z-50' onClose={closeModal}>
            {/* Overlay */}
            <Transition.Child
              as={Fragment}
              enter='ease-out duration-300'
              enterFrom='opacity-0'
              enterTo='opacity-100'
              leave='ease-in duration-200'
              leaveFrom='opacity-100'
              leaveTo='opacity-0'
            >
              <div className='fixed inset-0 bg-black/50' />
            </Transition.Child>

            {/* Slide-In Modal */}
            <div className='fixed inset-0 overflow-hidden'>
              <div className='absolute inset-0 flex justify-end sm:top-20 top-16'>
                <Transition.Child
                  as={Fragment}
                  enter='transform transition ease-out duration-300'
                  enterFrom='translate-x-full'
                  enterTo='translate-x-0'
                  leave='transform transition ease-in duration-200'
                  leaveFrom='translate-x-0'
                  leaveTo='translate-x-full'
                >
                  <Dialog.Panel className='md:w-[42.5%] sm:w-[60%] w-[95%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll'>
                    {/* Modal Header */}
                    <div className='sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm'>
                      <h2 className='page-title'>Add Room</h2>
                      <button
                        onClick={closeModal}
                        className='text-gray-500 hover:text-gray-800'
                      >
                        &#10005; {/* Close icon */}
                      </button>
                    </div>

                    {/* Modal Content */}
                    <div className='sm:px-6 px-4'>
                      <AddRoom
                        setIsUpdate={setIsUpdate}
                        closeModal={closeModal}
                      ></AddRoom>
                    </div>
                  </Dialog.Panel>
                </Transition.Child>
              </div>
            </div>
          </Dialog>
        </Transition>

        <Transition show={iseditOpen} as={Fragment}>
          <Dialog as='div' className='relative z-50' onClose={closeeditModal}>
            {/* Overlay */}
            <Transition.Child
              as={Fragment}
              enter='ease-out duration-300'
              enterFrom='opacity-0'
              enterTo='opacity-100'
              leave='ease-in duration-200'
              leaveFrom='opacity-100'
              leaveTo='opacity-0'
            >
              <div className='fixed inset-0 bg-black/50' />
            </Transition.Child>

            {/* Slide-In Modal */}
            <div className='fixed inset-0 overflow-hidden'>
              <div className='absolute inset-0 flex justify-end sm:top-20 top-16'>
                <Transition.Child
                  as={Fragment}
                  enter='transform transition ease-out duration-300'
                  enterFrom='translate-x-full'
                  enterTo='translate-x-0'
                  leave='transform transition ease-in duration-200'
                  leaveFrom='translate-x-0'
                  leaveTo='translate-x-full'
                >
                  <Dialog.Panel className='md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll'>
                    {/* Modal Header */}
                    <div className='sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50'>
                      <h2 className='page-title'>Edit Room</h2>
                      <button
                        onClick={closeeditModal}
                        className='text-gray-500 hover:text-gray-800'
                      >
                        &#10005; {/* Close icon */}
                      </button>
                    </div>

                    {/* Modal Content */}
                    <div className='sm:px-6 px-4'>
                      <AddEditRoom
                        closeeditModal={closeeditModal}
                        roomId={editId}
                        setIsUpdate={setIsUpdate}
                      ></AddEditRoom>
                    </div>
                  </Dialog.Panel>
                </Transition.Child>
              </div>
            </div>
          </Dialog>
        </Transition>

        <Transition show={isviewOpen} as={Fragment}>
          <Dialog as='div' className='relative z-50' onClose={closeviewModal}>
            {/* Overlay */}
            <Transition.Child
              as={Fragment}
              enter='ease-out duration-300'
              enterFrom='opacity-0'
              enterTo='opacity-100'
              leave='ease-in duration-200'
              leaveFrom='opacity-100'
              leaveTo='opacity-0'
            >
              <div className='fixed inset-0 bg-black/50' />
            </Transition.Child>

            {/* Slide-In Modal */}
            <div className='fixed inset-0 overflow-hidden'>
              <div className='absolute inset-0 flex justify-end sm:top-20 top-16'>
                <Transition.Child
                  as={Fragment}
                  enter='transform transition ease-out duration-300'
                  enterFrom='translate-x-full'
                  enterTo='translate-x-0'
                  leave='transform transition ease-in duration-200'
                  leaveFrom='translate-x-0'
                  leaveTo='translate-x-full'
                >
                  <Dialog.Panel className='md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll'>
                    {/* Modal Header */}
                    <div className='sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50'>
                      <h2 className='page-title'>View Room</h2>
                      <button
                        onClick={closeviewModal}
                        className='text-gray-500 hover:text-gray-800'
                      >
                        &#10005; {/* Close icon */}
                      </button>
                    </div>

                    {/* Modal Content */}
                    <div className='sm:px-6 px-4'>
                      <ViewRoom
                        closeviewModal={closeviewModal}
                        roomId={editId}
                        setIsUpdate={setIsUpdate}
                      ></ViewRoom>
                    </div>
                  </Dialog.Panel>
                </Transition.Child>
              </div>
            </div>
          </Dialog>
        </Transition>
      </section>
    </>
  );
};

export default Roommanagement;
