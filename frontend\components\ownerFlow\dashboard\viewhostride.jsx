/* eslint-disable react/jsx-key */
/* eslint-disable no-constant-binary-expression */
import React, { useEffect, useState } from "react";
import { CarFront, BusFront, Bike, CarTaxiFront } from "lucide-react";
import Loader from "@/components/loader/loader";
import Image from "next/image";
import { getHostRideDatabyIdApi } from "@/services/ownerflowServices";

const Viewhostride = ({ editId }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    fromAddress: {
      address: "", // The address as a string
      location: {
        type: "Point", // The type can be "Point" (GeoJSON format)
        coordinates: [0, 0], // Array with [longitude, latitude]
      },
    },
    destination: {
      address: "", // The address as a string
      location: {
        type: "Point", // The type can be "Point" (GeoJSON format)
        coordinates: [0, 0], // Array with [longitude, latitude]
      },
    },
    date: null,
    time: {
      start: 0, // Start time initialized to 0
      end: 0, // End time initialized to 0
    },
    passengers: "",
    modeOfTransfer: "",
    price: "",
    carDetails: {
      companyName: "",
      model: "",
      registrationNumber: "",
      passengerCapacity: "",
    },
    photos: [],
  });

  const transportModes = [
    { label: "Bus", icon: BusFront },
    { label: "Private Car", icon: CarFront },
    { label: "Bike", icon: Bike },
    { label: "Share Cab / Taxi", icon: CarTaxiFront },
  ];

  const mapTransportMode = (responseMode) => {
    const mode = responseMode?.toLowerCase();
    const matchedMode = transportModes.find(
      (item) => item.label.toLowerCase() === mode
    );
    return matchedMode ? matchedMode.label : null;
  };

  useEffect(() => {
    fetchData(editId);
  }, [editId]);

  const fetchData = async (editId) => {
    setIsLoading(true)
    try {
      const res = await getHostRideDatabyIdApi(editId);
      if (res?.status == 200) {
        const data = res?.data?.data;
        setFormData({
          fromAddress: {
            address: data?.fromAddress?.address,
            location: {
              type: "Point",
              coordinates: [
                data?.fromAddress?.location?.coordinates?.[0],
                data?.fromAddress?.location?.coordinates?.[1],
              ],
            },
          },
          destination: {
            address: data?.destination?.address,
            location: {
              type: "Point",
              coordinates: [
                data?.destination?.location?.coordinates?.[0],
                data?.destination?.location?.coordinates?.[1],
              ],
            },
          },
          date: data?.date.split("T")[0],
          time: {
            start: data?.time?.start,
            end: data?.time?.end,
          },
          passengers: "1",
          modeOfTransfer: mapTransportMode(data?.transportMode),
          price: data?.price,
          carDetails: {
            companyName: data?.name,
            model: data?.model,
            registrationNumber: data?.number,
            passengerCapacity: data?.capacity,
          },
          photos: data?.photos,
        });
      }
    } catch (error) {
      console.log("error", error);
    } finally{
      setIsLoading(false)
    }
  };
  return (
    <>
      <Loader open={isLoading} />
      <section className='w-full'>
        <div className='w-full'>
          <div className='max-w-[780px] mx-auto pb-5'>
            <div className='grid sm:grid-cols-[1.4fr_3fr] grid-cols-2 sm:gap-4 gap-3 font-inter'>
              <div>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  From Where :
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Destination :
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Date :
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Time :
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Passenger:
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Mode of Transport :
                </p>
              </div>
              <div>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData?.fromAddress?.address || " - "}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData?.destination?.address || " - "}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData?.date ? new Date(formData?.date)?.toLocaleDateString(
                    "en-GB",
                    {
                      day: "2-digit",
                      month: "2-digit",
                      year: "numeric",
                    }
                  ) : '-' || "-"}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData?.time?.start || " - "}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData?.passengers || " - "}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData?.modeOfTransfer || " - "}
                </p>
              </div>
              <p className='font-bold text-black sm:text-base text-xs mb-5 col-span-2'>
                {formData?.modeOfTransfer || " - "} Detail
              </p>
              <div>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Company :
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Model :
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Registration Number :
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Passenger Capacity :
                </p>
                <p className='font-bold text-black sm:text-base text-xs mb-5'>
                  Add Photo :
                </p>
              </div>
              <div>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData.carDetails.companyName || " - "}{" "}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData.carDetails.model || " - "}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData.carDetails.registrationNumber || " - "}
                </p>
                <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
                  {formData.carDetails.passengerCapacity || " - "}
                </p>
                <div className='flex gap-1.5'>
                  {formData?.photos?.map((image, index) => (
                    <Image
                      className='w-[54px] h-[36px] object-cover rounded-sm'
                      src={image?.url}
                      alt={`Existing Image ${index + 1}`}
                      width={54}
                      height={36}
                    ></Image>
                  )) || " - "}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};
export default Viewhostride;
