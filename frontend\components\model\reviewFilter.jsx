import React from "react";
// import { useState, useRef } from "react";
// import Flatpickr from "react-flatpickr";
// import "flatpickr/dist/flatpickr.min.css";

const ReviewFilter = ({ closefilterModal }) => {
//   const [selectedDate, setSelectedDate] = useState(null);
//   const inputRef = useRef(null);
  return (
    <>
      <form className="space-y-6 max-w-[830px] mx-auto">
        {/* <div className="relative">
          <label className="block text-sm font-medium text-black">Date</label>

          <Flatpickr
            ref={inputRef}
            value={selectedDate}
            options={{
              dateFormat: "d-M",
              allowInput: true,
            }}
            onChange={([date]) => setSelectedDate(date)}
            className="mt-1 block w-full border border-gray-300 rounded-lg p-2 pl-10 shadow-sm appearance-none cursor-pointer"
            placeholder="10 Sep"
          />

          <div
            className="absolute top-[36px] left-3 cursor-pointer"
            onClick={() => inputRef.current.flatpickr.open()}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M8 7V3m8 4V3m-9 8h10m-12 8h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
              />
            </svg>
          </div>
        </div> */}
   <div className="relative">
            <label className="block text-sm font-medium text-black">
              Date
            </label>
            <input
              type="date"
              name="title"
              className="mt-1 block w-full border border-gray-300 rounded-lg p-2 pl-10 shadow-sm appearance-none cursor-pointer"
              placeholder="10 Sep"
              style={{ outline: "none" }}
              onClick={(e) => e.target.showPicker()} // Opens the date picker on input click
            />
            
            <div
              className="absolute top-5 left-3 cursor-pointer mt-4 "
              onClick={(e) => {
                e.target.previousSibling.showPicker(); // Opens the date picker on icon click
              }}
            >
              
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M8 7V3m8 4V3m-9 8h10m-12 8h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
            </div>
            <div className="absolute top-5 right-3 cursor-pointer mt-4 w-5 h-5 bg-white"></div>
          </div>
        <div>
          <label className="block text-sm font-medium text-black mb-1">
            Country
          </label>
          <select
            name="type"
            className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
          >
             <option disabled value="">
              Select Country
            </option>
            <option value="India">India</option>
            <option value="Australia">Australia</option>
            <option value="Japan">Japan</option>
            <option value="Russia">Russia</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-black mb-1">
            Rating
          </label>
          <select
            name="type"
            className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
          >
            <option disabled value="">
              Select Rating
            </option>
            <option value="1">1</option>
            <option value="2">2</option>
            <option value="3">3</option>
            <option value="4">4</option>
          </select>
        </div>
        <div className="flex justify-center gap-3 mx-9">
          <button
            type="button"
            onClick={closefilterModal}
            className="hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm"
          >
            Apply
          </button>
        </div>
      </form>
    </>
  );
};

export default ReviewFilter;
