import Link from "next/link";
import React from "react";

const FaqsDetails = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 dark:bg-[#171616] overflow-y-auto scroll-smooth ">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        Faqs Details
      </h2>
      <div className="bg-white border rounded-xl mt-5 h-auto dark:bg-black dark:border-none">
        <div className="px-8 py-4 flex flex-col gap-y-4">
          <div>
            <div className="flex">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  What is mixdorm?
                </h1>
                <p className=" text-black/55 text-sm font-poppins font-medium dark:text-[#757575]">
                  MixDorm is a vibrant community-focused hostel network designed
                  for young travelers and backpackers. We offer modern
                  accommodations, unique features like Mix Ride and Mix
                  Creators, and a range of activities to enhance your travel
                  experience.
                </p>
              </div>
              <div className="">
                <Link href={"/superadmin/dashboard/faqs-edit"} className="text-white text-sm font-poppins py-1 md:py-2 lg:py-2 px-6 lg:px-8   rounded bg-sky-blue-650">
                  Edit
                </Link>
              </div>
            </div>
            <div className="flex w-[90%]">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  How can i Book a stay at Mixdorm?
                </h1>
                <p className=" text-black/55 text-sm font-poppins font-medium dark:text-[#757575]">
                  You must be at least 18 years of age to use our services and
                  make a booking. By using our services, you represent and
                  warrant that you meet the age requirement and have the legal
                  capacity to enter into these Terms
                </p>
              </div>
            </div>
            <div className="flex w-[90%]">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  What is included in the room rate?
                </h1>
                <p className=" text-black/55 text-sm font-poppins font-medium dark:text-[#757575]">
                  The room rate includes accommodation in our well-maintained
                  dorms or private rooms. Additional amenities such as
                  breakfast, tours, and activities may be available for an extra
                  charge
                </p>
              </div>
            </div>
            <div className="flex w-[90%]">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  What payment methods do you accept?
                </h1>
                <p className=" text-black/55 text-sm font-poppins font-medium dark:text-[#757575]">
                  We accept major credit cards, debit cards, and other online
                  payment methods. Payment is processed securely through our
                  payment gateway.
                </p>
              </div>
            </div>
            <div className="flex w-[90%]">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  Is my booking guarantee?
                </h1>
                <p className=" text-black/55 text-sm font-poppins font-medium dark:text-[#757575]">
                  Yes, once you receive a confirmation email, your booking is
                  guaranteed. We use secure payment methods to ensure your
                  reservation is safe
                </p>
              </div>
            </div>
            <div className="flex w-[90%]">
              <div>
                <h1 className="text-lg font-poppins font-bold my-4 dark:text-[#B6B6B6]">
                  Can i modify or cancel my booking?
                </h1>
                <p className=" text-black/55 text-sm font-poppins font-medium dark:text-[#757575]">
                  Yes, you can modify or cancel your booking based on our
                  Cancellation Policy. Please visit our website or contact our
                  customer support team for assistance with changes.
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-start justify-start p-8">
          <Link
            href={"/superadmin/dashboard/faqs"}
            className="text-white py-2 w-32 max-w-md rounded bg-sky-blue-650 flex items-center justify-center"
          >
            Cancel
          </Link>
        </div>
      </div>
    </div>
  );
};

export default FaqsDetails;
