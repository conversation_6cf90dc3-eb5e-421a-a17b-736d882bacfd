import React, { useState } from "react";
import Flatpickr from "react-flatpickr";
import "flatpickr/dist/flatpickr.min.css";

// import { format } from "date-fns";

const BookingFilter = ({ closefilterModal }) => {
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  return (
    <>
      <form className="space-y-6 max-w-[830px] mx-auto">
        <div className="grid grid-cols-2 gap-4">
          {/* Start Date */}
          <div className="relative">
            <label className="block text-sm font-medium text-black mb-1">
              Dates
            </label>
            <Flatpickr
              value={startDate}
              options={{
                dateFormat: "d-M",
                allowInput: true,
                maxDate: endDate || null,
              }}
              onChange={([date]) => setStartDate(date)}
              className="w-full border border-gray-300 rounded-lg pl-10 pr-4 py-2 text-sm placeholder:text-gray-400 shadow-sm appearance-none focus:outline-none focus:ring-1 focus:ring-primary-blue cursor-pointer"
              placeholder="10-Sep"
            />
            <div className="absolute top-9 left-3 text-gray-400 pointer-events-none">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M8 7V3m8 4V3m-9 8h10m-12 8h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
            </div>
          </div>

          {/* End Date */}
          <div className="relative">
            <label className="block text-sm font-medium text-black mb-1">
              &nbsp;
            </label>
            <Flatpickr
              value={endDate}
              options={{
                dateFormat: "d-M",
                allowInput: true,
                minDate: startDate || null,
                position: "auto right", 
              }}
              onChange={([date]) => setEndDate(date)}
              className="w-full border border-gray-300 rounded-lg pl-10 pr-4 py-2 text-sm text-gray-900 shadow-sm appearance-none cursor-pointer focus:outline-none focus:ring-1 focus:ring-primary-blue"
              placeholder="15-Sep"
            />
            <div className="absolute top-9 left-3 text-gray-400 pointer-events-none">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M8 7V3m8 4V3m-9 8h10m-12 8h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
            </div>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-black mb-1">
            Room Type
          </label>
          <select
            name="type"
            className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-blue text-black sm:text-sm text-xs placeholder:text-gray-500"
          >
            <option disabled value="">
              4 Bed Mixdorm
            </option>
            <option value="Visa">Super Delux</option>
            <option value="Visa">Super Delux</option>
            <option value="Visa">Suite</option>
          </select>
        </div>

        <div className="flex justify-center gap-3 mx-9">
          <button
            type="button"
            onClick={closefilterModal}
            className="hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm"
          >
            Apply
          </button>
        </div>
      </form>
    </>
  );
};

export default BookingFilter;
