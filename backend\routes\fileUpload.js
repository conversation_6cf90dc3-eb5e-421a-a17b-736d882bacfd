import express from "express";
import {generateURLAndUpload, generateURLAndUploadMultiple} from "../controller/fileupload.js"
const router = express.Router();

// fileupload route file
router.post("/generate-presigned-url", generateURLAndUploadMultiple);
export default router;

/**
 * @swagger
 * tags:
 *   name: S3
 *   description: Endpoints for AWS S3 operations
 */
/**
 * @swagger
 * /fileUpload/generate-presigned-url:
 *   post:
 *     summary: Upload multiple files directly to S3 and generate object URLs
 *     tags: [S3]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               files:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Array of files to be uploaded to S3.
 *             required:
 *               - files
 *     responses:
 *       '200':
 *         description: Successfully uploaded files and generated access URLs.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Files uploaded and access URLs generated successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       filename:
 *                         type: string
 *                         example: "example.jpg"
 *                       path:
 *                         type: string
 *                         example: "uploads/1633629665-example.jpg"
 *                       objectURL:
 *                         type: string
 *                         example: "https://your-s3-bucket.s3.region.amazonaws.com/uploads/1633629665-example.jpg"
 *       '400':
 *         description: Bad Request, missing files.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Files are required"
 *       '500':
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */
