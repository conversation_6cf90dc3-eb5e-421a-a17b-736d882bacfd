import mongoose from 'mongoose';

const reviewSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'users',
    required: true
  },
  event: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'events',
    required: true
  },
  rating: {
    type: Number,
    min: 0,
    max: 5,
    required: false
  },
  comment: {
    type: String,
    required: false
  },
  images: [
    {
      url: {
        type: String,
        required: false
      },
      title: {
        type: String
      }
    }
  ],
  likes: {
    type: Number, // string or number
    default: 0
  },
  dislikes: {
    type: Number, // string or number
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isDeleted: {
    type: Boolean,
    default: false,
  }
}, {timestamps : true});

const reviewModel = mongoose.model('eventReviews', reviewSchema,"eventReviews");

export default reviewModel;