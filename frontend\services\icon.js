import Image from "next/image";
import { Io<PERSON>icycleSharp } from "react-icons/io5";
import {
  MdNo<PERSON>ealsOuline,
  MdOutlineSignalCellularConnectedNoInternet4Bar,
} from "react-icons/md";
// import { FaCarAlt, FaMapMarkerAlt } from "react-icons/fa";
// import { FaWifi } from "react-icons/fa6";
// import { GiRolledCloth } from "react-icons/gi";
// import {
//   // MdLunchDining,
//   MdOutlineNightShelter,
//   MdOutlineSignalCellularConnectedNoInternet4Bar,
// } from "react-icons/md";

// const iconStyle = {
//   color: "#40E0D0", // Sets the color of the icon
//   fontSize: "24px", // Adjust size as needed
// };

// const Faciicons = {
  // "Free Breakfast": (
  //   <Image
  //     src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/breakfast.svg`}
  //     width={12}
  //     height={12}
  //     alt="Free Breakfast"
  //     className="w-5 h-5 text-primary-blue"
  //   />
  // ),
//   "Free WiFi": (
//     <Image
//       src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/free_wi_fi.svg`}
//       width={12}
//       height={12}
//       alt="Free Wifi"
//       className="w-5 h-5 text-primary-blue"
//     />
//   ),
//     "Free Wi-Fi": (
//     <Image
//       src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/free_wi_fi.svg`}
//       width={12}
//       height={12}
//       alt="Free Wifi"
//       className="w-5 h-5 text-primary-blue"
//     />
//   ),
//   "Linen Included": (
//     <Image
//       src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/linen_included.svg`}
//       width={12}
//       height={12}
//       alt="Free Linen"
//       className="w-4 h-4 text-primary-blue"
//     />
//   ),
//   "Towels Included": (
//     <Image
//       src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/towels_on_rent.svg`}
//       width={12}
//       height={12}
//       alt="Free Towels"
//       className="w-5 h-5 text-primary-blue"
//     />
//   ),
//   "Free Parking": (
//     <Image
//       src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/parking.svg`}
//       width={12}
//       height={12}
//       alt="Free Parking"
//       className="w-5 h-5 text-primary-blue"
//     />
//   ),
//   "Free City Maps": (
//     <Image
//       src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/breakfast.svg`}
//       width={12}
//       height={12}
//       alt="Free Maps"
//       className="w-6 h-6 text-primary-blue"
//     />
//   ),
//   "Lockers": (
//     <Image
//       src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/lockers.svg`}
//       width={12}
//       height={12}
//       alt="Lockers"
//       className="w-6 h-6 text-primary-blue"
//     />
//   ),
//   "Hot water": (
//     <Image
//       src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/hot_water.svg`}
//       width={12}
//       height={12}
//       alt="Hot Water"
//       className="w-6 h-6 text-primary-blue"
//     />
//   ),
//   "Laundry Services (Extra)": (
//     <Image
//       src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/laundry_services.svg`}
//       width={12}
//       height={12}
//       alt="Laundry"
//       className="w-6 h-6 text-primary-blue"
//     />
//   ),
//   "Card Payment Accepted": (
//     <Image
//       src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/card_payment_accepted.svg`}
//       width={12}
//       height={12}
//       alt="Card Payment Accepted"
//       className="w-6 h-6 text-primary-blue"
//     />
//   ),
//   "Parking": (
//     <Image
//       src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/parking.svg`}
//       width={12}
//       height={12}
//       alt="Card Payment Accepted"
//       className="w-6 h-6 text-primary-blue"
//     />
//   ),
//   "Bicycle": <IoBicycleSharp className="text-lg" />,
//   "Bicycle Parking": <IoBicycleSharp className="text-lg" />,
//   "Breakfast Not Included": <MdNoMealsOuline className="text-lg" />,
//   "Free Internet Access": (
//     <MdOutlineSignalCellularConnectedNoInternet4Bar className="text-lg" />
//   ),
// };

// export default Faciicons;


const FreeBreakfastIcon = (props) => (
  <Image
    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/breakfast.svg`}
    alt="Free Breakfast"
    width={20}
    height={20}
    {...props}
  />
);

const FreeWifiIcon = (props) => (
  <Image
    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/free_wi_fi.svg`}
    alt="Free Wifi"
    width={20}
    height={20}
    {...props}
  />
);

const LinenIncludedIcon = (props) => (
  <Image
    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/linen_included.svg`}
    alt="Linen Included"
    width={20}
    height={20}
    {...props}
  />
);

const TowelsIncludedIcon = (props) => (
  <Image
    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/towels_on_rent.svg`}
    alt="Towels Included"
    width={20}
    height={20}
    {...props}
  />
);

const FreeParkingIcon = (props) => (
  <Image
    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/parking.svg`}
    alt="Parking"
    width={20}
    height={20}
    {...props}
  />
);

const FreeCityMapsIcon = (props) => (
  <Image
    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/breakfast.svg`}
    alt="Free City Maps"
    width={20}
    height={20}
    {...props}
  />
);

const LockersIcon = (props) => (
  <Image
    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/lockers.svg`}
    alt="Lockers"
    width={20}
    height={20}
    {...props}
  />
);

const HotWaterIcon = (props) => (
  <Image
    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/hot_water.svg`}
    alt="Hot Water"
    width={20}
    height={20}
    {...props}
  />
);

const LaundryServicesIcon = (props) => (
  <Image
    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/laundry_services.svg`}
    alt="Laundry Services"
    width={20}
    height={20}
    {...props}
  />
);

const CardPaymentIcon = (props) => (
  <Image
    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/card_payment_accepted.svg`}
    alt="Card Payment Accepted"
    width={20}
    height={20}
    {...props}
  />
);

//  Final icon map with normalized keys
const Faciicons = {
  freebreakfast: FreeBreakfastIcon,
  freewifi: FreeWifiIcon,
  linenincluded: LinenIncludedIcon,
  towelsincluded: TowelsIncludedIcon,
  freeparking: FreeParkingIcon,
  freecitymaps: FreeCityMapsIcon,
  lockers: LockersIcon,
  hotwater: HotWaterIcon,
  laundryservicesextra: LaundryServicesIcon,
  cardpaymentaccepted: CardPaymentIcon,
  parking: FreeParkingIcon,
  bicycle: IoBicycleSharp,
  breakfastnotincluded: MdNoMealsOuline,
  freeinternetaccess: MdOutlineSignalCellularConnectedNoInternet4Bar,
};

export default Faciicons;