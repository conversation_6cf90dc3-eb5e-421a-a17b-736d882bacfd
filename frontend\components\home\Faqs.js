"use client";
import { motion } from "framer-motion";
import Link from "next/link";

const faqs = [
  {
    question: "What is Mixdorm and how does it work?",
    answer: (
      <>
        Mixdorm is a{" "}
        <Link
          href={"/"}
          className="text-primary-blue"
          onClick={(e) => e.stopPropagation()}
        >
          {" "}
          global hostel booking{" "}
        </Link>{" "}
        platform designed for backpackers, solo travelers, and budget-conscious
        adventurers. You can search, compare, and book hostels, dormitories, and
        budget stays across 15+ countries—all in one place.{" "}
      </>
    ),
  },
  {
    question: "What types of hostels can I find on Mixdorm?",
    answer:
      "Mixdorm offers a wide range of hostels including social hubs, peaceful retreats, adventure-focused stays, and budget-friendly options. From beachside hostels to city center dorms, you can filter by country, ratings, and features to find the perfect match for your travel style.",
  },
  {
    question: "Is it free to use Mixdorm for booking hostels?",
    answer:
      "Yes! Mixdorm is completely free for travelers to use. You only pay for your hostel stay—no hidden charges or booking fees.",
  },
  {
    question: "What amenities can I expect at Mixdorm hostels?",
    answer:
      "Common amenities include free WiFi, linen, city maps, and internet access. Many top-rated hostels listed also offer exclusive events, guided tours, and on-site social activities to enhance your stay.",
  },
  {
    question: "Can I list my hostel on Mixdorm?",
    answer: (
      <>
        Absolutely! Hostel owners can click on{" "}
        <Link
          href={"/owner/about-mixdorm"}
          className="text-primary-blue"
          onClick={(e) => e.stopPropagation()}
        >
          {" "}
          “List Your Hostel”{" "}
        </Link>{" "}
        in the top menu to register their property and start receiving global
        bookings.{" "}
      </>
    ),
  },
  {
    question: "Does Mixdorm offer travel tips and guides?",
    answer: (
      <>
        Yes! Mixdorm features blog-style{" "}
        <Link
          href={"/blog"}
          className="text-primary-blue"
          onClick={(e) => e.stopPropagation()}
        >
          {" "}
          travel guides{" "}
        </Link>{" "}
        like Hostel Life Experiences, Budget Travel Tips, and Travel Activities
        to help you plan better, save more, and explore smarter.{" "}
      </>
    ),
  },
  {
    question: "What sets Mixdorm apart from other hostel booking platforms?",
    answer: (
      <>
        {" "}
        <Link
          href={"/aboutus"}
          className="text-primary-blue"
          onClick={(e) => e.stopPropagation()}
        >
          Mixdorm{" "}
        </Link>{" "}
        combines hostel booking with event discovery, AI-powered
        recommendations, and interactive planning tools, offering a more
        personalized and engaging travel experience compared to standard booking
        sites.
      </>
    ),
  },
];

export default function FAQs() {
  return (
    <div className=" px-6 pt-10 pb-10 md:pb-28 relative">
      <div className="absolute -top-6 -right-4 w-40 h-40 bg-cyan-400 rounded-full blur-2xl opacity-30"></div>
          {/* <div className="absolute top-1 left-[50%] w-40 h-40 bg-pink-400 rounded-full blur-2xl opacity-30"></div>
          <div className="absolute top-1 left-[40%] w-40 h-40 bg-yellow-300 rounded-full blur-2xl opacity-20"></div> */}
          <div className="absolute bottom-1/4 -left-2 w-36 h-36 bg-cyan-400 rounded-full blur-2xl opacity-30"></div>
          <div className="absolute bottom-[48%] left-[15%] w-36 h-36 bg-yellow-300 rounded-full blur-xl opacity-30"></div>
          <div className="absolute bottom-[7%] right-[29%] w-40 h-40 bg-cyan-400 rounded-full blur-2xl opacity-30"></div>
          <div className="absolute bottom-[23%] right-[4%] w-40 h-40 bg-pink-400 rounded-full blur-xl opacity-30"></div>
      {/* Heading */}
      <div className="text-center mb-12 ">
        <h1 className="text-4xl md:text-5xl  text-primary-blue font-mashiny font-extrabold">
          FAQs
        </h1>
      </div>

      {/* Grid Layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-6xl mx-auto">
        {/* Left column - first 4 FAQs */}
        <div className="space-y-6 ">
          {faqs.slice(0, 4).map((faq, index) => (
            <motion.div
              key={index}
              whileHover={{ scale: 1.05, rotateX: 5, rotateY: -5 }}
              className="relative group px-6 py-4 rounded-2xl shadow-lg bg-[#D9F9F6] backdrop-blur-lg border border-[#40E0D0] transition-all duration-300 cursor-pointer hover:shadow-[0_0_20px_rgba(64,224,208,0.8)]"
            >
              <div>
                <h3 className="text-base md:text-lg font-semibold text-black mb-3 group-hover:text-[#40E0D0] transition flex">
                  {faq.question}
                </h3>
                <div className="max-h-0 overflow-hidden group-hover:max-h-96 transition-all duration-500">
                  <div
                    className="text-black text-sm leading-relaxed pt-2"
                    onClick={(e) => e.stopPropagation()}
                  >
                    {faq.answer}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Right column - remaining 3 FAQs */}
        <div className="space-y-6">
          {faqs.slice(4).map((faq, index) => (
            <motion.div
              key={index + 4} // Adding offset to avoid duplicate keys
              whileHover={{ scale: 1.05, rotateX: 5, rotateY: -5 }}
              className="relative group px-6 py-4 rounded-2xl shadow-lg bg-[#D9F9F6] backdrop-blur-lg border border-[#40E0D0] transition-all duration-300 cursor-pointer hover:shadow-[0_0_20px_rgba(64,224,208,0.8)]"
            >
              <div>
                <h3 className="text-base md:text-lg font-semibold text-black mb-3 group-hover:text-[#40E0D0] transition">
                  {faq.question}
                </h3>
                <div className="max-h-0 overflow-hidden group-hover:max-h-96 transition-all duration-500">
                  <div
                    className="text-black text-sm leading-relaxed pt-2"
                    onClick={(e) => e.stopPropagation()}
                  >
                    {faq.answer}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
        {/* {faqs.map((faq, index) => (
      <motion.div
        key={index}
        whileHover={{ scale: 1.02 }}
        onClick={() => setExpandedIndex(expandedIndex === index ? null : index)}
        className="relative p-6 rounded-2xl shadow-lg bg-white/70 backdrop-blur-lg border border-[#40E0D0]/30 transition-all duration-300 cursor-pointer"
      >
        <h3 className="text-xl font-semibold text-gray-800 mb-3">
          {faq.question}
        </h3>
        {expandedIndex === index && (
          <div className="text-gray-500 text-sm leading-relaxed" onClick={(e) => e.stopPropagation()}>
            {faq.answer}
          </div>
        )}
        
      </motion.div>
    ))} */}
      </div>
    </div>
  );
}
