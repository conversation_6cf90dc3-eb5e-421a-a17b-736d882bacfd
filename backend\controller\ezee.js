import ezeeModel from "../models/ezee.js";
import EzeeBooking from "../models/ezzeBooking.js";
import RatePlan from "../models/roomRatePlans.js";
import roomRatesModel from "../models/rates.js";
import roomTypesModel from "../models/roomTypes.js";
import roomModel from "../models/room.js"
import property from "../models/properties.js"
export const getRoomInfo = async (req, res) => {
    try {
        // Save the incoming request

        // Extract authentication data and hotel code
        const authData = req.body?.res_request?.authentication?.[0];
        const HotelCode = authData?.hotelcode?.[0];

        if (!HotelCode) {
            res.set('Content-Type', 'application/xml');
            return res.status(400).send(`<?xml version="1.0" encoding="UTF-8"?>
                <RES_Response>
                    <Errors>
                        <ErrorCode>400</ErrorCode>
                        <ErrorMessage>Missing HotelCode</ErrorMessage>
                    </Errors>
                </RES_Response>`);
        }
        const propertyData = await property.findOne({ otaId: HotelCode })
        // Get active room types for this hotel
        const roomTypes = await roomModel.find({
            property: propertyData?._id
        });
        console.log("roomTypes", roomTypes)
        // Extract room IDs
        const roomIds = roomTypes.map(room => room._id);
        console.log("roomIds", roomIds)
        // Get rate plans where room ID matches
        const ratePlans = await RatePlan.find({
            room: { $in: roomIds }
        });
        // Construct XML
        let ratePlansXml = roomTypes.map(room => {
            const matchingRates = ratePlans.filter(rate => rate.room.toString() === room._id.toString());
            return matchingRates.map(rate => `
                <RatePlan>
                    <RoomTypeID>${room.roomId}</RoomTypeID>
                    <RoomType>${room.name}</RoomType>
                    <RateTypeID>${rate.otaRateId}</RateTypeID>
                    <RateType>${rate.name}</RateType>
                    <Rate>${rate.rate}</Rate>
                    <Currency>${rate.currency}</Currency>
                </RatePlan>
            `).join('');
        }).join('');

        res.set('Content-Type', 'application/xml');
        await ezeeModel.create({
            resp: req.body, url: req.url, sendResp: `<?xml version="1.0" encoding="UTF-8"?>
            <RES_Response>
                <RoomInfo>
                    <RatePlans>
                        ${ratePlansXml}
                    </RatePlans>
                </RoomInfo>
                <Errors>
                    <ErrorCode>0</ErrorCode>
                    <ErrorMessage>Success</ErrorMessage>
                </Errors>
            </RES_Response>` });

        return res.status(200).send(`<?xml version="1.0" encoding="UTF-8"?>
            <RES_Response>
                <RoomInfo>
                    <RatePlans>
                        ${ratePlansXml}
                    </RatePlans>
                </RoomInfo>
                <Errors>
                    <ErrorCode>0</ErrorCode>
                    <ErrorMessage>Success</ErrorMessage>
                </Errors>
            </RES_Response>`);

    } catch (error) {
        console.error("Server Error:", error);
        res.set('Content-Type', 'application/xml');
        return res.status(500).send(`<?xml version="1.0" encoding="UTF-8"?>
            <RES_Response>
                <Errors>
                    <ErrorCode>500</ErrorCode>
                    <ErrorMessage>${error.message}</ErrorMessage>
                </Errors>
            </RES_Response>`);
    }
};

import RoomInventory from "../models/ARI.js";
import avability from "../models/avability.js";

export const pushInventory = async (req, res) => {
    try {
        res.set("Content-Type", "application/xml");
        console.log("Received Request:", JSON.stringify(req.body, null, 2));

        await ezeeModel.create({
            resp: req.body,
            url: req.url
        });

        const roomtype = req.body?.res_request?.roomtype?.[0];

        if (!roomtype) {
            return res.status(400).send(`<?xml version="1.0" standalone="yes"?>
        <RES_Response>
            <Errors>
                <ErrorCode>405</ErrorCode>
                <ErrorMessage>Missing Required Data</ErrorMessage>
            </Errors>
        </RES_Response>`);
        }

        const roomtypeid = roomtype?.roomtypeid?.[0];
        const fromdate = roomtype?.fromdate?.[0];
        const todate = roomtype?.todate?.[0];
        const availability = roomtype?.availability?.[0];
        const HotelCode = req.body?.res_request?.authentication?.[0]?.hotelcode?.[0];
        if (!roomtypeid || !fromdate || !todate || !availability) {
            return res.status(400).send(`<?xml version="1.0" standalone="yes"?>
        <RES_Response>
            <Errors>
                <ErrorCode>400</ErrorCode>
                <ErrorMessage>Invalid or missing required fields</ErrorMessage>
            </Errors>
        </RES_Response>`);
        }

        const roomDoc = await roomModel.findOne({ roomId: roomtypeid });
        console.log("roomtypeid", roomtypeid)
        if (!roomDoc) {
            return res.status(404).send(`<?xml version="1.0" standalone="yes"?>
        <RES_Response>
            <Errors>
                <ErrorCode>404</ErrorCode>
                <ErrorMessage>Room type not found</ErrorMessage>
            </Errors>
        </RES_Response>`);
        }

        const start = new Date(fromdate);
        const end = new Date(todate);
        const availableCount = parseInt(availability);

        for (let dt = new Date(start); dt <= end; dt.setDate(dt.getDate() + 1)) {
            const currentDate = new Date(dt);
            await RoomInventory.updateOne(
                {
                    roomId: roomtypeid,
                    date: currentDate
                },
                {
                    $set: {
                        propertyId: roomDoc.property, // assumes your room has a property ref
                        availableUnits: availableCount,
                        updatedAt: new Date()
                    },
                    $setOnInsert: {
                        rate: 0,  // default if first time
                        currency: "EUR", // or your default
                        close: false,
                        closeArrival: false,
                        closeDeparture: false,
                        minLos: 1,
                        maxLos: 0,
                        minAdvanceOffset: 0,
                        maxAdvanceOffset: 0,
                        numAdultsIncluded: 1,
                        numChildrenIncluded: 0
                    }
                },
                { upsert: true }
            );
            await avability.updateOne(
                {
                    roomId: roomtypeid,
                    date: currentDate
                },
                {
                    $set: {
                        propertyId: roomDoc.property, // assumes your room has a property ref
                        availableUnits: availableCount,
                    }
                },
                { upsert: true }
            );
        }

        // await roomModel.updateOne(
        //     { roomId: roomtypeid },
        //     { $set: { availableRooms: availableCount } }
        // );

        await property.updateOne(
            { otaId: HotelCode },
            { $set: { isPropertyLive: true } }
        );

        return res.status(200).send(`<?xml version="1.0" standalone="yes"?>
      <RES_Response>
        <Success>
            <SuccessMsg>Room Inventory Successfully Updated</SuccessMsg>
        </Success>
        <Errors>
            <ErrorCode>0</ErrorCode>
            <ErrorMessage>Success</ErrorMessage>
        </Errors>
      </RES_Response>`);
    } catch (error) {
        console.error("Server Error:", error);
        res.set("Content-Type", "application/xml");
        return res.status(500).send(`<?xml version="1.0" standalone="yes"?>
      <RES_Response>
        <Errors>
            <ErrorCode>500</ErrorCode>
            <ErrorMessage>${error.message}</ErrorMessage>
        </Errors>
      </RES_Response>`);
    }
};
export const pushLinearRate = async (req, res) => {
    try {
        res.set("Content-Type", "application/xml");
        await ezeeModel.create({ resp: req.body, url: req.url });

        // Parse nested XML-to-JSON fields
        const requestData = req.body.res_request || {};
        const authentication = requestData.authentication?.[0] || {};
        const rateType = requestData.ratetype?.[0] || {};
        const roomRate = rateType.roomrate?.[0] || {};

        const HotelCode = authentication.hotelcode?.[0] || null;
        const AuthCode = authentication.authcode?.[0] || null;

        const RoomTypeID = rateType.roomtypeid?.[0] || null;
        const RateTypeID = rateType.ratetypeid?.[0] || null;
        const FromDate = rateType.fromdate?.[0] || null;
        const ToDate = rateType.todate?.[0] || null;

        const BaseRate = roomRate.base?.[0] || null;
        const weekdayRate = roomRate.weekday ? parseFloat(roomRate.weekday[0]) : parseFloat(BaseRate);
        const weekendRate = roomRate.weekend ? parseFloat(roomRate.weekend[0]) : parseFloat(BaseRate);
        const averagePrice = (weekdayRate + weekendRate) / 2;

        // validation
        if (!HotelCode || !AuthCode || !RoomTypeID || !RateTypeID || !FromDate || !ToDate || !BaseRate) {
            return res.status(400).send(`<?xml version="1.0" standalone="yes"?>
        <RES_Response>
            <Errors>
                <ErrorCode>400</ErrorCode>
                <ErrorMessage>Missing required fields</ErrorMessage>
            </Errors>
        </RES_Response>`);
        }

        // find room in your room model by OTA room id
        const roomDoc = await roomModel.findOne({ roomId: RoomTypeID });

        if (!roomDoc) {
            return res.status(404).send(`<?xml version="1.0" standalone="yes"?>
        <RES_Response>
            <Errors>
                <ErrorCode>404</ErrorCode>
                <ErrorMessage>Room type not found</ErrorMessage>
            </Errors>
        </RES_Response>`);
        }

        const start = new Date(FromDate);
        const end = new Date(ToDate);

        for (let dt = new Date(start); dt <= end; dt.setDate(dt.getDate() + 1)) {
            // Get the RatePlan document using RateTypeID (otaRateId)
            const ratePlan = await RatePlan.findOne({ otaRateId: RateTypeID });
            const ratePlanName = ratePlan?.name || 'Default Rate Plan';

            const currentDate = new Date(dt);

            await RoomInventory.updateOne(
                {
                    roomId: RoomTypeID,
                    date: currentDate,
                    ratePlan: RateTypeID
                },
                {
                    $set: {
                        propertyId: roomDoc.property,
                        rate: averagePrice,
                        weekdayRate,
                        weekendRate,
                        baseRate: parseFloat(BaseRate),
                        updatedAt: new Date(),
                        ratePlanName
                    },
                    $setOnInsert: {
                        availableUnits: roomDoc.availableRooms || 0,
                        currency: "INR", // or dynamic if you want
                        close: false,
                        closeArrival: false,
                        closeDeparture: false,
                        minLos: 1,
                        maxLos: 0,
                        minAdvanceOffset: 0,
                        maxAdvanceOffset: 0,
                        numAdultsIncluded: 1,
                        numChildrenIncluded: 0
                    }
                },
                { upsert: true }
            );
        }

        return res.status(200).send(`<?xml version="1.0" standalone="yes"?>
      <RES_Response>
        <Success>
            <SuccessMsg>Room Rates Successfully Updated</SuccessMsg>
        </Success>
        <Errors>
            <ErrorCode>0</ErrorCode>
            <ErrorMessage>Success</ErrorMessage>
        </Errors>
      </RES_Response>`);
    } catch (error) {
        console.error("Server Error:", error);
        res.set("Content-Type", "application/xml");
        return res.status(500).send(`<?xml version="1.0" standalone="yes"?>
      <RES_Response>
        <Errors>
            <ErrorCode>500</ErrorCode>
            <ErrorMessage>${error.message}</ErrorMessage>
        </Errors>
      </RES_Response>`);
    }
};

// export const pushLinearRate = async (req, res) => {
//     try {
//         res.set('Content-Type', 'application/xml');
//         await ezeeModel.create({ resp: req.body, url: req.url })

//         // Extracting data correctly from nested JSON
//         const requestData = req.body.res_request || {};
//         const authentication = requestData.authentication ? requestData.authentication[0] : {};
//         const rateType = requestData.ratetype ? requestData.ratetype[0] : {};
//         const roomRate = rateType.roomrate ? rateType.roomrate[0] : {}; // Extract first element of roomrate array

//         // Extract HotelCode & AuthCode
//         const HotelCode = authentication.hotelcode ? authentication.hotelcode[0] : null;
//         const AuthCode = authentication.authcode ? authentication.authcode[0] : null;

//         // Extract RateType details
//         const RoomTypeID = rateType.roomtypeid ? rateType.roomtypeid[0] : null;
//         const RateTypeID = rateType.ratetypeid ? rateType.ratetypeid[0] : null;
//         const FromDate = rateType.fromdate ? rateType.fromdate[0] : null;
//         const ToDate = rateType.todate ? rateType.todate[0] : null;

//         // Extract Room Rates
//         const BaseRate = roomRate.base ? roomRate.base[0] : null;
//         const ExtraAdult = roomRate.extraadult ? roomRate.extraadult[0] : null;
//         const ExtraChild = roomRate.extrachild ? roomRate.extrachild[0] : null;

//         console.log("HotelCode:", HotelCode);
//         console.log("AuthCode:", AuthCode);
//         console.log("RoomTypeID:", RoomTypeID);
//         console.log("RateTypeID:", RateTypeID);
//         console.log("FromDate:", FromDate);
//         console.log("ToDate:", ToDate);
//         console.log("BaseRate:", BaseRate);
//         console.log("ExtraAdult:", ExtraAdult);
//         console.log("ExtraChild:", ExtraChild);

//         // Validation Checks
//         if (!HotelCode || !AuthCode || !RoomTypeID || !RateTypeID || !FromDate || !ToDate || !BaseRate) {
//             return res.status(400).send(`<?xml version="1.0" standalone="yes"?>
//             <RES_Response>
//                 <Errors>
//                     <ErrorCode>400</ErrorCode>
//                     <ErrorMessage>Missing required fields</ErrorMessage>
//                 </Errors>
//             </RES_Response>`);
//         }

//         // Mock Success Response

//         // Find existing rate entry

//         const roomType = await roomTypesModel.findOne({ RoomTypeId: RoomTypeID })

//         // Convert string dates to Date objects
//         const start = new Date(FromDate);
//         const end = new Date(ToDate);
//         // Loop from start date to end date (inclusive)
//         for (let dt = new Date(start); dt <= end; dt.setDate(dt.getDate() + 1)) {
//             const currentDate = new Date(dt); // Create a fresh date object to avoid reference issues
//             const existingRate = await roomRatesModel.findOne({
//                 room: roomType._id,
//                 fromDate: currentDate,
//                 toDate: currentDate,
//                 date: currentDate
//             });
//             console.log("existingRate", existingRate)
//             const weekdayRate = roomRate.weekday ? parseFloat(roomRate.weekday[0]) : parseFloat(BaseRate);
//             const weekendRate = roomRate.weekend ? parseFloat(roomRate.weekend[0]) : parseFloat(BaseRate);
//             const averagePrice = (weekdayRate + weekendRate) / 2;

//             if (existingRate) {
//                 existingRate.rate.basePrice = BaseRate;
//                 existingRate.rate.weekdayRate = {
//                     value: weekdayRate,
//                     updatedAt: new Date(),
//                 };
//                 existingRate.rate.weekendRate = {
//                     value: weekendRate,
//                     updatedAt: new Date(),
//                 };
//                 existingRate.rate.averagePrice = {
//                     value: averagePrice,
//                     updatedAt: new Date(),
//                 };

//                 await existingRate.save();
//             } else {
//                 await roomRatesModel.create({
//                     room: roomType._id,
//                     fromDate: currentDate,
//                     toDate: currentDate,
//                     date: currentDate,
//                     rate: {
//                         basePrice: BaseRate,
//                         weekdayRate: {
//                             value: weekdayRate,
//                             updatedAt: new Date(),
//                         },
//                         weekendRate: {
//                             value: weekendRate,
//                             updatedAt: new Date(),
//                         },
//                         averagePrice: {
//                             value: averagePrice,
//                             updatedAt: new Date(),
//                         },
//                     },
//                 });
//             }
//         }

//         return res.status(200).send(`<?xml version="1.0" standalone="yes"?>
//             <RES_Response>
//                 <Success>
//                     <SuccessMsg>Room Rates Successfully Updated</SuccessMsg>
//                 </Success>
//                 <Errors>
//                     <ErrorCode>0</ErrorCode>
//                     <ErrorMessage>Success</ErrorMessage>
//                 </Errors>
//             </RES_Response>`);
//     } catch (error) {
//         console.error("Server Error:", error);
//         res.set('Content-Type', 'application/xml');
//         return res.status(500).send(`<?xml version="1.0" standalone="yes"?>
//         <RES_Response>
//             <Errors>
//                 <ErrorCode>500</ErrorCode>
//                 <ErrorMessage>${error.message}</ErrorMessage>
//             </Errors>
//         </RES_Response>`);
//     }
// };
export const getBookingToYCS = async (req, res) => {
    try {
        await ezeeModel.create({ resp: req.body, url: req.url })

        const bookingData = {
            hotelCode: "xxxx",
            bookingID: "7002016070604",
            status: "New",
            source: "Test",
            subBookingId: "1",
            rateTypeID: "r1",
            rateType: "Refundable",
            roomTypeCode: "rm`",
            roomTypeName: "Private Standard Room",
            startDate: "2025-04-01",
            endDate: "2025-05-01",
            totalRate: "1000.00",
            totalDiscount: "0.00",
            totalExtraCharge: "0.00",
            totalTax: "0.00",
            totalPayment: "0.00",
            salutation: "Ms",
            firstName: "test",
            lastName: "name",
            gender: "Male",
            city: "Goa",
            zipcode: "403604",
            phone: "123456",
            mobile: "+91 1234567890",
            email: "<EMAIL>",
            comment: "Reservation : test",
            rentalInfo: {
                effectiveDate: "2021-03-05",
                adult: "2",
                child: "0",
                rent: "1000.00",
                extraCharge: "0.00",
                tax: "1000.00",
                discount: "0.00"
            }
        };

        res.set('Content-Type', 'application/xml');
        return res.status(200).send(`<?xml version="1.0" encoding="UTF-8"?>
<RES_Response>
    <Reservations>
        <Reservation>
            <HotelCode>${bookingData.hotelCode}</HotelCode>
            <BookingID>${bookingData.bookingID}</BookingID>
            <Status>${bookingData.status}</Status>
            <Source>${bookingData.source}</Source>
            <BookingTran>
                <SubBookingId>${bookingData.subBookingId}</SubBookingId>
                <RateTypeID>${bookingData.rateTypeID}</RateTypeID>
                <RateType>${bookingData.rateType}</RateType>
                <RoomTypeCode>${bookingData.roomTypeCode}</RoomTypeCode>
                <RoomTypeName>${bookingData.roomTypeName}</RoomTypeName>
                <Start>${bookingData.startDate}</Start>
                <End>${bookingData.endDate}</End>
                <TotalRate>${bookingData.totalRate}</TotalRate>
                <TotalDiscount>${bookingData.totalDiscount}</TotalDiscount>
                <TotalExtraCharge>${bookingData.totalExtraCharge}</TotalExtraCharge>
                <TotalTax>${bookingData.totalTax}</TotalTax>
                <TotalPayment>${bookingData.totalPayment}</TotalPayment>
                <Salutation>${bookingData.salutation}</Salutation>
                <FirstName>${bookingData.firstName}</FirstName>
                <LastName>${bookingData.lastName}</LastName>
                <Gender>${bookingData.gender}</Gender>
                <City>${bookingData.city}</City>
                <Zipcode>${bookingData.zipcode}</Zipcode>
                <Phone>${bookingData.phone}</Phone>
                <Mobile>${bookingData.mobile}</Mobile>
                <Email>${bookingData.email}</Email>
                <Comment>${bookingData.comment}</Comment>
                <RentalInfo>
                    <EffectiveDate>${bookingData.rentalInfo.effectiveDate}</EffectiveDate>
                    <Adult>${bookingData.rentalInfo.adult}</Adult>
                    <Child>${bookingData.rentalInfo.child}</Child>
                    <Rent>${bookingData.rentalInfo.rent}</Rent>
                    <ExtraCharge>${bookingData.rentalInfo.extraCharge}</ExtraCharge>
                    <Tax>${bookingData.rentalInfo.tax}</Tax>
                    <Discount>${bookingData.rentalInfo.discount}</Discount>
                </RentalInfo>
            </BookingTran>
        </Reservation>
    </Reservations>
</RES_Response>`);
    } catch (error) {
        console.error("Server Error in getBookingToYCS:", error);
        res.set('Content-Type', 'application/xml');
        return res.status(500).send(`<?xml version="1.0" encoding="UTF-8"?>
<RES_Response>
    <Errors>
        <ErrorCode>500</ErrorCode>
        <ErrorMessage>${error.message}</ErrorMessage>
    </Errors>
</RES_Response>`);
    }
};
export const getNewBookingOneDay = async (req, res) => {
    try {
        res.set('Content-Type', 'application/xml');

        const requestData = req.body?.res_request || {};
        console.log(JSON.stringify(requestData, null, 2))
        const booking = req.body?.res_request?.[0]?.booking?.[0] || {};
        const guest = {
            salutation: booking?.salutation?.[0] || "Mr",
            firstName: booking?.firstname?.[0] || "John",
            lastName: booking?.lastname?.[0] || "Doe",
            gender: booking?.gender?.[0] || "Male",
            city: booking?.city?.[0] || "City",
            zipcode: booking?.zipcode?.[0] || "000000",
            phone: booking?.phone?.[0] || "0000000000",
            mobile: booking?.mobile?.[0] || "0000000000",
            email: booking?.email?.[0] || "<EMAIL>",
            comment: booking?.comment?.[0] || ""
        };

        const rooms = booking?.rooms?.[0]?.room || [];

        const bookingResponse = rooms.map((room, index) => {
            const rentalInfos = room?.rentalinfolist?.[0]?.rentalinfo || [];

            const rentalInfoXml = rentalInfos.map(r => `
                <RentalInfo>
                    <EffectiveDate>${r?.date?.[0] || ""}</EffectiveDate>
                    <Adult>${r?.adult?.[0] || "1"}</Adult>
                    <Child>${r?.child?.[0] || "0"}</Child>
                    <Rent>${r?.rent?.[0] || "0.00"}</Rent>
                    <ExtraCharge>${r?.extracharge?.[0] || "0.00"}</ExtraCharge>
                    <Tax>${r?.tax?.[0] || "0.00"}</Tax>
                    <Discount>${r?.discount?.[0] || "0.00"}</Discount>
                </RentalInfo>
            `).join("");

            return `
                <BookingTran>
                    <SubBookingId>${index + 1}</SubBookingId>
                    <RateTypeID>${room?.ratetypeid?.[0] || ""}</RateTypeID>
                    <RateType>${room?.ratetype?.[0] || ""}</RateType>
                    <RoomTypeCode>${room?.roomtypecode?.[0] || ""}</RoomTypeCode>
                    <RoomTypeName>${room?.roomtypename?.[0] || ""}</RoomTypeName>
                    <Start>${rentalInfos[0]?.date?.[0] || ""}</Start>
                    <End>${rentalInfos[rentalInfos.length - 1]?.date?.[0] || ""}</End>
                    <TotalRate>0.00</TotalRate>
                    <TotalDiscount>0.00</TotalDiscount>
                    <TotalExtraCharge>0.00</TotalExtraCharge>
                    <TotalTax>0.00</TotalTax>
                    <TotalPayment>0.00</TotalPayment>
                    <Salutation>${guest.salutation}</Salutation>
                    <FirstName>${guest.firstName}</FirstName>
                    <LastName>${guest.lastName}</LastName>
                    <Gender>${guest.gender}</Gender>
                    <City>${guest.city}</City>
                    <Zipcode>${guest.zipcode}</Zipcode>
                    <Phone>${guest.phone}</Phone>
                    <Mobile>${guest.mobile}</Mobile>
                    <Email>${guest.email}</Email>
                    <Comment>${guest.comment}</Comment>
                    ${rentalInfoXml}
                </BookingTran>
            `;
        }).join("");
        console.log("booking", JSON.stringify(booking, null, 2))
        // Optional: convert rooms data to internal format
        console.log("rooms", JSON.stringify(rooms, null, 2))
        const bookingData = {
            hotelCode: booking?.hotelcode?.[0] || '',
            bookingID: booking?.bookingid?.[0] || '',
            ...guest,
            rooms: rooms.map(room => ({
                rateTypeID: room?.ratetypeid?.[0] || '',
                rateType: room?.ratetype?.[0] || '',
                roomTypeCode: room?.roomtypecode?.[0] || '',
                roomTypeName: room?.roomtypename?.[0] || '',
                rentalInfoList: (room?.rentalinfolist?.[0]?.rentalinfo || []).map(info => ({
                    date: info?.date?.[0] || '',
                    adult: parseInt(info?.adult?.[0] || "0", 10),
                    child: parseInt(info?.child?.[0] || "0", 10),
                    rent: parseFloat(info?.rent?.[0] || "0.00"),
                    extraCharge: parseFloat(info?.extracharge?.[0] || "0.00"),
                    tax: parseFloat(info?.tax?.[0] || "0.00"),
                    discount: parseFloat(info?.discount?.[0] || "0.00"),
                }))
            }))
        };

        console.log("bookingData", JSON.stringify(bookingData, null, 2));

        await EzeeBooking.create(bookingData);
        await ezeeModel.create({ resp: req.body, url: req.url });

        const xmlResponse = `<?xml version="1.0" encoding="UTF-8"?>
<RES_Response>
    <Reservations>
        <Reservation>
          
            <Status>New</Status>
            <Source>MixDorm</Source>
            ${bookingResponse}
        </Reservation>
    </Reservations>
</RES_Response>`;

        res.set("Content-Type", "application/xml");
        return res.status(200).send(xmlResponse);

    } catch (error) {
        console.error("Error in getNewBookingOneDay:", error);
        res.set("Content-Type", "application/xml");
        return res.status(500).send(`<?xml version="1.0" encoding="UTF-8"?>
<RES_Response>
    <Errors>
        <ErrorCode>500</ErrorCode>
        <ErrorMessage>${error.message}</ErrorMessage>
    </Errors>
</RES_Response>`);
    }
};

export const modifyBookingById = async (req, res) => {
    try {
        res.set('Content-Type', 'application/xml');
        const requestData = req.body?.res_request || {};
        const booking = req.body?.res_request?.[0]?.booking?.[0] || {};

        const bookingID = booking?.bookingid?.[0];
        if (!bookingID) {
            return res.status(400).send(`<RES_Response>
                <Errors>
                    <ErrorCode>400</ErrorCode>
                    <ErrorMessage>Missing booking ID</ErrorMessage>
                </Errors>
            </RES_Response>`);
        }

        const existingBooking = await EzeeBooking.findOne({ bookingID });
        if (!existingBooking) {
            return res.status(404).send(`<RES_Response>
                <Errors>
                    <ErrorCode>404</ErrorCode>
                    <ErrorMessage>Booking not found</ErrorMessage>
                </Errors>
            </RES_Response>`);
        }

        const guest = {
            salutation: booking?.salutation?.[0] || "Mr",
            firstName: booking?.firstname?.[0] || "John",
            lastName: booking?.lastname?.[0] || "Doe",
            gender: booking?.gender?.[0] || "Male",
            city: booking?.city?.[0] || "City",
            zipcode: booking?.zipcode?.[0] || "000000",
            phone: booking?.phone?.[0] || "0000000000",
            mobile: booking?.mobile?.[0] || "0000000000",
            email: booking?.email?.[0] || "<EMAIL>",
            comment: booking?.comment?.[0] || ""
        };

        const rooms = booking?.rooms?.[0]?.room || [];

        const updatedData = {
            hotelCode: booking?.hotelcode?.[0] || '',
            ...guest,
            rooms: rooms.map(room => ({
                rateTypeID: room?.ratetypeid?.[0] || '',
                rateType: room?.ratetype?.[0] || '',
                roomTypeCode: room?.roomtypecode?.[0] || '',
                roomTypeName: room?.roomtypename?.[0] || '',
                rentalInfoList: (room?.rentalinfolist?.[0]?.rentalinfo || []).map(info => ({
                    date: info?.date?.[0] || '',
                    adult: parseInt(info?.adult?.[0] || "0", 10),
                    child: parseInt(info?.child?.[0] || "0", 10),
                    rent: parseFloat(info?.rent?.[0] || "0.00"),
                    extraCharge: parseFloat(info?.extracharge?.[0] || "0.00"),
                    tax: parseFloat(info?.tax?.[0] || "0.00"),
                    discount: parseFloat(info?.discount?.[0] || "0.00"),
                }))
            }))
        };

        await EzeeBooking.updateOne({ bookingID }, updatedData);
        await ezeeModel.create({ resp: req.body, url: req.url });

        const bookingResponse = rooms.map((room, index) => {
            const rentalInfos = room?.rentalinfolist?.[0]?.rentalinfo || [];
            const rentalInfoXml = rentalInfos.map(r => `
                <RentalInfo>
                    <EffectiveDate>${r?.date?.[0] || ""}</EffectiveDate>
                    <Adult>${r?.adult?.[0] || "1"}</Adult>
                    <Child>${r?.child?.[0] || "0"}</Child>
                    <Rent>${r?.rent?.[0] || "0.00"}</Rent>
                    <ExtraCharge>${r?.extracharge?.[0] || "0.00"}</ExtraCharge>
                    <Tax>${r?.tax?.[0] || "0.00"}</Tax>
                    <Discount>${r?.discount?.[0] || "0.00"}</Discount>
                </RentalInfo>`).join("");

            return `
                <BookingTran>
                    <SubBookingId>${index + 1}</SubBookingId>
                    <RateTypeID>${room?.ratetypeid?.[0] || ""}</RateTypeID>
                    <RateType>${room?.ratetype?.[0] || ""}</RateType>
                    <RoomTypeCode>${room?.roomtypecode?.[0] || ""}</RoomTypeCode>
                    <RoomTypeName>${room?.roomtypename?.[0] || ""}</RoomTypeName>
                    <Start>${rentalInfos[0]?.date?.[0] || ""}</Start>
                    <End>${rentalInfos[rentalInfos.length - 1]?.date?.[0] || ""}</End>
                    <TotalRate>0.00</TotalRate>
                    <TotalDiscount>0.00</TotalDiscount>
                    <TotalExtraCharge>0.00</TotalExtraCharge>
                    <TotalTax>0.00</TotalTax>
                    <TotalPayment>0.00</TotalPayment>
                    <Salutation>${guest.salutation}</Salutation>
                    <FirstName>${guest.firstName}</FirstName>
                    <LastName>${guest.lastName}</LastName>
                    <Gender>${guest.gender}</Gender>
                    <City>${guest.city}</City>
                    <Zipcode>${guest.zipcode}</Zipcode>
                    <Phone>${guest.phone}</Phone>
                    <Mobile>${guest.mobile}</Mobile>
                    <Email>${guest.email}</Email>
                    <Comment>${guest.comment}</Comment>
                    ${rentalInfoXml}
                </BookingTran>`;
        }).join("");

        const xmlResponse = `<?xml version="1.0" encoding="UTF-8"?>
<RES_Response>
    <Reservations>
        <Reservation>
            <Status>Modified</Status>
            <Source>MixDorm</Source>
            ${bookingResponse}
        </Reservation>
    </Reservations>
</RES_Response>`;

        return res.status(200).send(xmlResponse);

    } catch (error) {
        console.error("Error in modifyBookingById:", error);
        res.set("Content-Type", "application/xml");
        return res.status(500).send(`<?xml version="1.0" encoding="UTF-8"?>
<RES_Response>
    <Errors>
        <ErrorCode>500</ErrorCode>
        <ErrorMessage>${error.message}</ErrorMessage>
    </Errors>
</RES_Response>`);
    }
};

const generateXmlResponse = (bookingData) => {
    return `<?xml version="1.0" encoding="UTF-8"?>
<RES_Response>
    <Reservations>
        <Reservation>
            <HotelCode>${bookingData.hotelCode}</HotelCode>
            <BookingID>${bookingData.bookingID}</BookingID>
            <Status>${bookingData.status}</Status>
            <Source>${bookingData.source}</Source>
            <BookingTran>
                <SubBookingId>${bookingData.subBookingId}</SubBookingId>
                <RateTypeID>${bookingData.rateTypeID}</RateTypeID>
                <RateType>${bookingData.rateType}</RateType>
                <RoomTypeCode>${bookingData.roomTypeCode}</RoomTypeCode>
                <RoomTypeName>${bookingData.roomTypeName}</RoomTypeName>
                <Start>${bookingData.startDate}</Start>
                <End>${bookingData.endDate}</End>
                <TotalRate>${bookingData.totalRate}</TotalRate>
                <TotalDiscount>${bookingData.totalDiscount}</TotalDiscount>
                <TotalExtraCharge>${bookingData.totalExtraCharge}</TotalExtraCharge>
                <TotalTax>${bookingData.totalTax}</TotalTax>
                <TotalPayment>${bookingData.totalPayment}</TotalPayment>
                <Salutation>${bookingData.salutation}</Salutation>
                <FirstName>${bookingData.firstName}</FirstName>
                <LastName>${bookingData.lastName}</LastName>
                <Gender>${bookingData.gender}</Gender>
                <City>${bookingData.city}</City>
                <Zipcode>${bookingData.zipcode}</Zipcode>
                <Phone>${bookingData.phone}</Phone>
                <Mobile>${bookingData.mobile}</Mobile>
                <Email>${bookingData.email}</Email>
                <Comment>${bookingData.comment}</Comment>
                <RentalInfo>
                    <EffectiveDate>${bookingData.rentalInfo.effectiveDate}</EffectiveDate>
                    <Adult>${bookingData.rentalInfo.adult}</Adult>
                    <Child>${bookingData.rentalInfo.child}</Child>
                    <Rent>${bookingData.rentalInfo.rent}</Rent>
                    <ExtraCharge>${bookingData.rentalInfo.extraCharge}</ExtraCharge>
                    <Tax>${bookingData.rentalInfo.tax}</Tax>
                    <Discount>${bookingData.rentalInfo.discount}</Discount>
                </RentalInfo>
            </BookingTran>
        </Reservation>
    </Reservations>
</RES_Response>`;
};
export const cancelBookingById = async (req, res) => {
    try {
        res.set('Content-Type', 'application/xml');

        // Log the incoming request body
        console.log("Cancel Booking Request:", JSON.stringify(req.body, null, 2));
        console.log("req.body", JSON.stringify(req.body, null, 2))
        const bookingData = req.body?.res_request?.booking?.[0] || {};
        const bookingId = bookingData?.bookingid?.[0];
        const hotelCode = bookingData?.hotelcode?.[0];
        const cancelled = bookingData?.cancelled?.[0];
        const comment = bookingData?.comment?.[0] || "";

        // Validate the required fields
        if (!bookingId || !hotelCode || cancelled !== "1") {
            return res.status(400).send(`<?xml version="1.0" encoding="UTF-8"?>
            <RES_Response>
                <Errors>
                    <ErrorCode>400</ErrorCode>
                    <ErrorMessage>Invalid request: Missing or incorrect data</ErrorMessage>
                </Errors>
            </RES_Response>`);
        }

        // Log the cancellation details for debugging
        console.log(`Canceling booking: ${bookingId} for hotel: ${hotelCode}`);

        // Fetch the booking from the database (Example: use a model method to find the booking by ID)
        const booking = await EzeeBooking.findOne({ bookingId, hotelCode });

        // if (!booking) {
        //     return res.status(404).send(`<?xml version="1.0" encoding="UTF-8"?>
        //     <RES_Response>
        //         <Errors>
        //             <ErrorCode>404</ErrorCode>
        //             <ErrorMessage>Booking not found</ErrorMessage>
        //         </Errors>
        //     </RES_Response>`);
        // }

        // Mark the booking as canceled
        // booking.status = "canceled";
        // booking.cancelledAt = new Date();
        // booking.comment = comment;

        // Save the updated booking in the database
        // await booking.save();

        // Send success response in XML format
        const xmlResponse = `<?xml version="1.0" encoding="UTF-8"?>
        <RES_Response>
            <Reservations>
                <Reservation>
                    <Status>Cancelled</Status>
                    <Source>MixDorm</Source>
                    <BookingID>${bookingId}</BookingID>
                    <HotelCode>${hotelCode}</HotelCode>
                    <Comment>${comment}</Comment>
                </Reservation>
            </Reservations>
        </RES_Response>`;

        res.set("Content-Type", "application/xml");
        return res.status(200).send(xmlResponse);

    } catch (error) {
        console.error("Error in cancelBookingById:", error);
        res.set("Content-Type", "application/xml");
        return res.status(500).send(`<?xml version="1.0" encoding="UTF-8"?>
        <RES_Response>
            <Errors>
                <ErrorCode>500</ErrorCode>
                <ErrorMessage>${error.message}</ErrorMessage>
            </Errors>
        </RES_Response>`);
    }
};




