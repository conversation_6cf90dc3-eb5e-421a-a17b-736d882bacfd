import React from 'react'
import { toast, Toaster } from 'react-hot-toast'
import { CheckCircle, AlertCircle, AlertTriangle, Info, X } from 'lucide-react'

const Toast = ({ visible, message, subMessage, type = 'info', onDismiss }) => {
  if (!visible) return null

  const icons = {
    success: <CheckCircle className="h-5 w-5 text-green-500" />,
    error: <AlertCircle className="h-5 w-5 text-red-500" />,
    warning: <AlertTriangle className="h-5 w-5 text-yellow-500" />,
    info: <Info className="h-5 w-5 text-blue-500" />
  }

  const subMessageColors = {
    success: 'text-green-500',
    error: 'text-red-500',
    warning: 'text-yellow-500',
    info: 'text-blue-500'
  }

  return (
    <div className="flex items-center gap-3 rounded-full bg-white px-4 py-3 shadow-lg max-w-md animate-in fade-in-0 zoom-in-95">
      <div className="flex-shrink-0">
        {icons[type]}
      </div>
      <div className="flex-1">
        <p className="text-sm font-medium text-gray-900">{message}</p>
        {subMessage && (
          <p className={`text-sm font-medium ${subMessageColors[type]}`}>
            {subMessage}
          </p>
        )}
      </div>
      <button
        onClick={onDismiss}
        className="flex-shrink-0 rounded-full p-1 text-gray-400 hover:text-gray-500 hover:bg-gray-100"
      >
        <X className="h-4 w-4" />
      </button>
    </div>
  )
}

// Create the Toast object for direct usage
const Toast2 = {
  success: (message, subMessage) => {
    toast.custom((t) => (
      <Toast
        visible={t.visible}
        message={message}
        subMessage={subMessage}
        type="success"
        onDismiss={() => toast.dismiss(t.id)}
      />
    ))
  },
  error: (message, subMessage) => {
    toast.custom((t) => (
      <Toast
        visible={t.visible}
        message={message}
        subMessage={subMessage}
        type="error"
        onDismiss={() => toast.dismiss(t.id)}
      />
    ))
  },
  warning: (message, subMessage) => {
    toast.custom((t) => (
      <Toast
        visible={t.visible}
        message={message}
        subMessage={subMessage}
        type="warning"
        onDismiss={() => toast.dismiss(t.id)}
      />
    ))
  },
  info: (message, subMessage) => {
    toast.custom((t) => (
      <Toast
        visible={t.visible}
        message={message}
        subMessage={subMessage}
        type="info"
        onDismiss={() => toast.dismiss(t.id)}
      />
    ))
  }
}

export { Toast2, Toaster }

// Example usage component
export function ToastExamples() {
  return (
    <div className="flex flex-col gap-4">
      <button
        onClick={() => Toast.success("Your AI profile picture is ready", "View results")}
        className="rounded-md bg-green-500 px-4 py-2 text-white"
      >
        Show Success Toast
      </button>
      <button
        onClick={() => Toast.error("Credit card expired", "Update Payment Details")}
        className="rounded-md bg-red-500 px-4 py-2 text-white"
      >
        Show Error Toast
      </button>
      <button
        onClick={() => Toast.info("107 New Reviews found", "Refresh Feed")}
        className="rounded-md bg-blue-500 px-4 py-2 text-white"
      >
        Show Info Toast
      </button>
      <button
        onClick={() => Toast.warning("Warning!", "Go to settings")}
        className="rounded-md bg-yellow-500 px-4 py-2 text-white"
      >
        Show Warning Toast
      </button>
    </div>
  )
}

