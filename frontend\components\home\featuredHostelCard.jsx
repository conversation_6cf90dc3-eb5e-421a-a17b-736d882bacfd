import React, {useEffect, useState} from "react";
import Image from "next/image";
import Link from "next/link";
import { getItemLocalStorage } from "@/utils/browserSetting";
import { likeUnlikePropertyApi } from "@/services/webflowServices";
import toast from "react-hot-toast";
import { FaHeart } from "react-icons/fa";
import { IoStar } from "react-icons/io5";
import { RxMix } from "react-icons/rx";
import { FaWifi, FaMapMarkedAlt, FaBed } from "react-icons/fa";
import { FaGlobe } from "react-icons/fa6";
import { FaCarAlt,  } from "react-icons/fa";
import { Building2,  } from "lucide-react";
import { PiForkKnifeBold, PiMapPinLineFill, PiTowel } from "react-icons/pi";

const HostelCard = ({
  tag,
  title,
  image,
  feature,
  rating,
  review,
  price,
  hostelId,
  setIsUpdateData,
  setLoading,
  liked,
}) => {
  const getImageUrl = (url) => {
    return url && url.startsWith("http") ? url : `https://${url}`;
  };

  const HandleLike = async () => {
    if (getItemLocalStorage("token")) {
      setLoading(true);
      try {
        const payload = {
          isLike: !liked,
        };
        await likeUnlikePropertyApi(hostelId, payload);
        setIsUpdateData((prevState) => !prevState);
      } catch (error) {
        console.error("Error fetching stay data:", error);
      } finally {
        setLoading(false);
      }
    } else {
      toast.error("Please login first!!");
      // router.push("/");
    }
  };

  const facilityIcons = {
    FREEWIFI: <FaWifi className="text-gray-800 text-xl h-[16px] w-[16px]" />,
    FREECITYMAPS: (
      <FaMapMarkedAlt className="text-gray-800 text-xl h-[16px] w-[16px]" />
    ),
    LINENINCLUDED: <FaBed className="text-gray-800 text-xl h-[16px] w-[16px]" />,
    FREEINTERNETACCESS: (
      <FaGlobe className="text-gray-800 text-xl h-[16px] w-[16px]" />
    ),
    BREAKFASTINCLUDED: (
      <PiForkKnifeBold className="text-gray-800 text-xl h-[16px] w-[16px]" />
    ), 
    FREEPARKING: <FaCarAlt className="text-gray-800 text-xl h-[16px] w-[16px]" />, 
    TOWELSINCLUDED: (
      <PiTowel className="text-gray-800 text-xl h-[16px] w-[16px]" />
    ), 
    FREECITYTOUR: (
      <Building2 className="text-gray-800 text-xl h-[16px] w-[16px]" />
    ), 
  };

  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageFailed, setImageFailed] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!image) return;

    const preload = new window.Image();
    preload.src = getImageUrl(image);

    preload.onload = () => {
      setImageLoaded(true);
    };

    // Optional: fallback after 3 seconds if image fails silently
    const timeout = setTimeout(() => {
      setImageLoaded(true);
    }, 3000);

    return () => clearTimeout(timeout);
  }, [image]);

  useEffect(() => {
    const isDataReady = feature && price && rating && review && title;
    if (isDataReady) {
      setIsLoading(false);
    }
  }, [feature, price, rating, review, title]);

  return (
    <>
      {/* <div className="rounded-3xl w-full border border-slate-105 font-manrope overflow-hidden">
        <div className="relative w-full rounded-t-3xl tracking-normal">
          <Link href={`/hostels-detail/${hostelId}`} prefetch={false}>
            <Image
              src={getImageUrl(image)}
              alt={title}
              title={title}
              className="w-full h-[200px] "
              width={1920}
              height={700}
              loading="lazy"
            />
          </Link>
          <div className="absolute flex items-center justify-center px-3 py-1 text-xs font-semibold text-black bg-white rounded-4xl top-5 left-5 font-manrope ">
            {tag}
          </div>
          <button
            type="button"
            onClick={() => HandleLike()}
            className={`absolute flex items-center justify-center p-1 rounded-full w-5 h-5 top-5 right-5 font-manrope ${
              liked ? "text-red-600 bg-white" : "text-black bg-white"
            } hover:bg-white-600 hover:text-red-600`}
          >
            <FaHeart size={16} />
          </button>
       
        </div>
       <div className="xl:p-4 px-4 pb-4 pt-4 rounded-b-3xl tracking-normal h-[170px]">
                 <div className="h-[100px]">
                   <h2 className=" leading-4">
                     <Link
                       href={`/hostels-detail/${hostelId}`}
                       className=" sm:text-[18px] text-[16px]  font-manrope leading-6 font-bold cursor-pointer text-black duration-300 ease-in-out hover:text-[#40E0D0] line-clamp-2"
                       prefetch={false}
                     >
                       {title}
                     </Link>
                   </h2>
          </div>

          <div className="flex flex-wrap gap-3 font-manrope text-[#737373] ">
            
            <p className="text-sm text-[#737373]">
              <span className="font-bold text-black">Key Features: </span>
              {feature?.map((feature) => feature?.name)?.join(", ")}
            </p>
          </div>
         
          <div className="sm:block items-center justify-between xl:mt-3 mt-3">
            <div className="flex items-center font-bold text-black text-lg gap-1 font-manrope">
              <Link
                href={`/hostels-detail/${hostelId}`}
                prefetch={false}
                className="hover:text-[#40E0D0] group"
              >
                {price}
                <span className="text-xs text-[#737373] font-normal ml-1 group-hover:text-[#40E0D0]">
                  / Per Night
                </span>
              </Link>

              <div className="ml-auto">
                <Link
                  href={`/hostels-detail/${hostelId}`}
                  prefetch={false}
                  className="bg-black flex items-center hover:bg-[#40E0D0] text-white font-semibold rounded-3xl px-4  font-manrope  h-[30px] text-[10px] leading-[16.5px] "
                >
                  Book Now
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div> */}
      <div className="rounded-3xl w-full border border-slate-105 font-manrope overflow-hidden">
        <div className="relative w-full rounded-t-3xl tracking-normal">
          <Link href={`/hostels-detail/${hostelId}`} prefetch={false}>
            {!imageLoaded || imageFailed ? (
              <div className="w-full  h-[180px] sm:h-[215px] bg-slate-200 animate-pulse flex justify-center items-center">
                <h1 className="text-white font-bold font-manrope">MixDorm</h1>  
              </div>
            ) : (
              <Image
                src={getImageUrl(image)}
                alt={title}
                title={title}
                className="w-full  h-[180px] sm:h-[215px] object-cover"
                width={1920}
                height={250}
                loading="lazy"
                onError={() => setImageFailed(true)}
              />
            )}
          </Link>
          {imageLoaded   && (
          <div className="absolute flex items-center justify-center px-3 py-1 text-xs font-semibold text-black bg-white rounded-4xl top-5 left-5 font-manrope ">
            {tag}
          </div>
          )}

          {imageLoaded  && (
          <button
            type="button"
            onClick={() => HandleLike()}
            className={`absolute flex items-center justify-center rounded-full w-7 h-7 top-5 right-5 font-manrope ${
              liked ? "text-red-600 bg-white" : "text-black bg-white"
            } hover:bg-white-600 hover:text-red-600`}
          >
            <FaHeart size={16} />
          </button>
          )}
          <div className="flex items-center text-sm gap-1 font-manrope absolute left-7 bottom-[-10px] bg-white rounded-4xl shadow-lg py-1 px-2">
            <IoStar className="text-yellow-550" />
            <span className="text-black font-semibold">{rating}</span>
            <span className="text-xs text-[#737373]">({review} reviews)</span>
          </div>
        </div>
        {isLoading ? (
          // ✅ Show Skeleton
          <div className="p-4 space-y-2">
            <div className="h-4 w-3/4 bg-slate-200 animate-pulse rounded" />
            <div className="h-3 w-1/2 bg-slate-200 animate-pulse rounded" />
            <div className="h-8 w-full bg-slate-200 animate-pulse rounded mt-4" />
            <div className="flex justify-between items-center">
              <div className="h-3 w-1/2 bg-slate-200 animate-pulse rounded" />
              <div className="h-3 w-1/3 bg-slate-200 animate-pulse rounded" />
            </div>
          </div>
        ) : (
          <div className="xl:p-4 px-4 pb-4 pt-4 rounded-b-3xl tracking-normal h-[170px]">
            <div className="h-[100px] flex flex-col justify-around">
            <div className="min-h-[80px]">  <h2 className=" leading-4 mt-1  ">
                <Link
                  href={`/hostels-detail/${hostelId}`}
                  className=" sm:text-[18px] text-[16px]  font-manrope leading-6 font-bold cursor-pointer text-black duration-300 ease-in-out hover:text-[#40E0D0] line-clamp-2"
                  prefetch={false}
                >
                  {title}
                </Link>
              </h2>
              <p
                className="text-[#888888] text-xs sm:text-sm flex items-center font-manrope cursor-pointer hover:text-primary-blue mt-2"
                
              >
                <PiMapPinLineFill className="text-primary-blue mr-1.5" />
                2 KM away from city center
              </p></div>
              <div className="flex flex-wrap gap-3 font-manrope text-[#737373] ">
                <p className="flex items-center text-sm gap-x-2 ">
                  <span className="text-[14px]  font-manrope font-medium cursor-pointer text-gray duration-300 ease-in-out">
                    Key Features:{" "}
                  </span>
                  <div className="flex items-center justify-start gap-x-1">
                    {feature.map((facility, index) => {
                      console.log(facility, "hdfhd");
                      const isLastItem = index === feature.length - 1; // Check if it's the last item
                      return (
                        <div
                          key={facility.id}
                          className="relative group w-6 h-6 flex items-center justify-center"
                          // title={facility.name} // For simple tooltips
                        >
                          {facilityIcons[facility.id] || (
                            <RxMix className="text-gray-800 text-xl h-[16px] w-[16px]" />
                          )}{" "}
                          {/* Default Icon */}
                          <span
                            className={`absolute bottom-6 ${
                              isLastItem ? "left-[-64px]" : "left-[-100%]"
                            } transform text-xs text-white bg-black px-2 py-1 rounded opacity-0 min-w-[max-content] max-w-[max-content] group-hover:opacity-100 duration-200`}
                          >
                            {facility.name}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </p>
              </div>
            </div>

            <div className="sm:block items-center justify-between xl:mt-3 mt-3">
              <div className="flex items-center font-bold text-black text-lg gap-1 font-manrope">
                <Link
                  href={`/hostels-detail/${hostelId}`}
                  prefetch={false}
                  className="hover:text-[#40E0D0] group"
                >
                  {price}
                  <span className="text-xs text-[#737373] font-normal ml-1 group-hover:text-[#40E0D0]">
                    / Per Night
                  </span>
                </Link>

                <div className="ml-auto">
                  <Link
                    href={`/hostels-detail/${hostelId}`}
                    prefetch={false}
                    className="bg-black flex items-center hover:bg-[#40E0D0] text-white font-semibold rounded-3xl px-4  font-manrope  h-[30px] text-[10px] leading-[16.5px] "
                  >
                    Book Now
                  </Link>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default HostelCard;
