import React, { useEffect, useRef, useState } from "react";
import { FaAngleLeft, FaAngleRight, FaAngleDown } from "react-icons/fa6";
import { LuCalendarDays, LuPlus } from "react-icons/lu";
import ShowDates from "./showDate";
import toast from "react-hot-toast";
import { getCalendarApi } from "@/services/ownerflowServices";
import Loader from "@/components/loader/loader";
import { isWeekend } from "date-fns";
import countries from "world-countries";
import Head from "next/head";
import { FaListUl } from "react-icons/fa";
import { IoSearchSharp } from "react-icons/io5";
import OverlayPortal from "./overlayPortal";
import { IoClose } from "react-icons/io5";
import { FiChevronRight } from "react-icons/fi";
import { getItemLocalStorage } from "@/utils/browserSetting";

const Index = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState([]);
  const isFirstRender = useRef(null);
  const [expandedCategories, setExpandedCategories] = useState({});
  const [selectedRoomTypes, setSelectedRoomTypes] = useState({});

  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [currencyData, setCurrencyData] = useState({});

  const [showMenu, setShowMenu] = useState(false);
  const [submenuVisible, setSubmenuVisible] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const [submenuPosition, setSubmenuPosition] = useState({ top: 0, left: 0 });
  const [popupPosition, setPopupPosition] = useState(null);
  const buttonRef = useRef(null);
  const menuRef = useRef(null);
  const submenuRef = useRef(null);
  const popupRef = useRef(null);
  const [openConfirmIndex, setOpenConfirmIndex] = useState(null);
  // const [openConfirm, setOpenConfirm] = useState(false);
  // const ConfirmRef = useRef(null);

  const toggleMenu = () => {
    setShowMenu((prev) => !prev);
    setSubmenuVisible(false); // Close submenu when toggling main menu
  };

  const getDaysInMonth = (year, month) => {
    return new Date(year, month + 1, 0).getDate();
  };
  const today = new Date();
  const isCurrentMonth =
    today.getFullYear() === currentYear && today.getMonth() === currentMonth;
  const startDay = isCurrentMonth ? today.getDate() : 1;
  const totalDays = getDaysInMonth(currentYear, currentMonth);

  const dates = Array.from(
    { length: totalDays - startDay + 1 },
    (_, i) => i + startDay
  );
  // const dates = Array.from(
  //   { length: getDaysInMonth(currentYear, currentMonth) },
  //   (_, i) => i + 1
  // );
  // const dates = Array.from(
  //   { length: getDaysInMonth(currentYear, currentMonth) - (startDay - 1) },
  //   (_, i) => i + startDay
  // );

  const handleMenuItemClick = (item, e) => {
    if (item === "New Reservation") {
      const rect = e.currentTarget.getBoundingClientRect();
      setSubmenuPosition({
        top: rect.top + window.scrollY + 20,
        left: rect.right + window.scrollX + 10,
      });
      setSubmenuVisible(true);
    }
  };

  const handleSlotClick = (index, event) => {
    const rect = event.currentTarget.getBoundingClientRect();
    setPopupPosition({
      top: rect.bottom + window.scrollY + 8, // 8px spacing
      left: rect.left + rect.width / 2 + window.scrollX,
    });
    setOpenConfirmIndex(index);
  };
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        setOpenConfirmIndex(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [openConfirmIndex]);

  useEffect(() => {
    if (showMenu && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setPosition({
        top: rect.bottom + window.scrollY + 8,
        left: rect.left + window.scrollX,
      });
    }
  }, [showMenu]);
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target) &&
        submenuRef.current &&
        !submenuRef.current.contains(event.target)
      ) {
        setShowMenu(false);
        setSubmenuVisible(false);
      }
    }

    if (showMenu) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showMenu]);

  useEffect(() => {
    if (showMenu && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setPosition({
        top: rect.bottom + window.scrollY + 8, // add spacing if needed
        left: rect.left + window.scrollX,
      });
    }
  }, [showMenu]);

  useEffect(() => {
    if (!isFirstRender.current) {
      const startDate = new Date(currentYear, currentMonth, 1)
        .toISOString()
        .split("T")[0];

      const endDate = new Date(currentYear, currentMonth + 1, 0)
        .toISOString()
        .split("T")[0];

      fetchList(startDate, endDate);
    } else {
      isFirstRender.current = false;
    }
  }, [currentMonth, currentYear]);

  useEffect(() => {
    if (data.length > 0) {
      const initialExpanded = data.reduce((acc, category) => {
        acc[category._id] = true;
        return acc;
      }, {});
      setExpandedCategories(initialExpanded);

      // Set the first room type as selected for each category
      const initialSelectedRoomTypes = data.reduce((acc, category) => {
        if (category.roomTypes && category.roomTypes.length > 0) {
          acc[category._id] = category.roomTypes[0];
        }
        return acc;
      }, {});
      setSelectedRoomTypes(initialSelectedRoomTypes);
    }
  }, [data]);

  const fetchList = async () => {
    setIsLoading(true);
    try {
      const formattedDate = `${currentYear}-${String(currentMonth + 1).padStart(
        2,
        "0"
      )}-01`;
      const endDate = new Date(currentYear, currentMonth + 1, 0);
      const formattedEndDate = `${endDate.getFullYear()}-${String(
        endDate.getMonth() + 1
      ).padStart(2, "0")}-${String(endDate.getDate()).padStart(2, "0")}`;
      const storedId = getItemLocalStorage("hopid");

      const response = await getCalendarApi(
        formattedDate,
        formattedEndDate,
        storedId
      );

      if (response.status === 200) {
        setData(response.data.data);
      } else {
        toast.error("Failed to fetch calendar");
      }
    } catch (error) {
      console.log("Error fetching calendar:", error);
      toast.error("Error fetching calendar");
    } finally {
      setIsLoading(false);
    }
  };

  const changeMonth = (increment) => {
    setCurrentMonth((prev) => (prev + increment + 12) % 12);
    if (increment === 1 && currentMonth === 11) {
      setCurrentYear((prev) => prev + 1);
    } else if (increment === -1 && currentMonth === 0) {
      setCurrentYear((prev) => prev - 1);
    }
  };

  // const [tooltip, setTooltip] = useState({
  //   visible: false,
  //   x: 0,
  //   y: 0,
  //   element: null,
  // });
  // eslint-disable-next-line no-unused-vars
  const [activeSlot, setActiveSlot] = useState(null);

  // const toggleTooltip = (e, index) => {
  //   const rect = e.target.getBoundingClientRect();

  //   if (tooltip.element === e.target) {
  //     setTooltip({ visible: false, x: 0, y: 0, element: null });
  //     setActiveSlot(null);
  //     return;
  //   }

  //   setTooltip({
  //     visible: true,
  //     x: rect.left + window.scrollX,
  //     y: rect.top + window.scrollY + rect.height,
  //     element: e.target,
  //   });
  //   setActiveSlot(index);
  // };

  const toggleCategory = (categoryId) => {
    setExpandedCategories((prev) => ({
      ...prev,
      [categoryId]: !prev[categoryId],
    }));
  };

  const handleRoomTypeClick = (roomType, categoryId) => {
    setSelectedRoomTypes((prev) => ({
      ...prev,
      [categoryId]: roomType,
    }));
  };

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);
      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);

  const getCurrencySymbol = (currencyCode) => {
    return currencyData[currencyCode] || currencyCode;
  };

  // Function to find room rate for a specific date and room type
  const findRoomRate = (roomType, day) => {
    if (!roomType || !roomType.roomRates) return null;
    return roomType.roomRates.find(
      (rate) =>
        new Date(rate.date).getDate() === day &&
        new Date(rate.date).getMonth() === currentMonth &&
        new Date(rate.date).getFullYear() === currentYear
    );
  };

  return (
    <>
      <Head>
        <title>Calendar Management Mixdorm</title>
      </Head>
      <Loader open={isLoading} />
      <div className="pt-8 ">
        <div className="calendar-container border border-[#E8F1FD] rounded-lg relative">
          <div className="overflow-x-auto">
            <div className="inline-block min-w-full">
              <div className="bg-[#EEEEEE] main-content-wrap rounded-t-lg relative ">
                <div className="calendar-header flex ">
                  <div className="flex flex-col gap-y-6 sm:min-w-[196px] min-w-[120px] border-r border-[#EEF0F2] py-3 sm:px-4 px-3 sticky left-0 z-10 bg-[#EEEEEE] ">
                    <div className="flex justify-around gap-x-1 md:gap-x-0">
                      <span className="bg-primary-blue h-8 w-8 flex items-center justify-center rounded-lg">
                        <FaListUl className="text-base" />
                      </span>

                      <span className="bg-primary-blue h-8 w-8 flex items-center justify-center rounded-lg">
                        <LuCalendarDays className="text-lg" />
                      </span>
                      <span className="bg-primary-blue h-8 w-8 flex items-center justify-center rounded-lg">
                        <IoSearchSharp className="text-lg" />
                      </span>
                      {/* <div className="relative">
                   
                        <span
                          ref={buttonRef}
                          onClick={toggleMenu}
                          className="bg-primary-blue h-8 w-8 flex items-center justify-center rounded-lg cursor-pointer"
                        >
                          <LuPlus className="text-lg text-black" />
                        </span>

                       
                        {showMenu && (
                          <div
                            ref={menuRef}
                            className="absolute top-10 -left-20 bg-white shadow-lg rounded-xl w-40 z-50"
                          >
                            {[
                              "New Reservation",
                              "Block Dates",
                              "Courtesy Hold",
                              "Out of services",
                            ].map((item, index) => (
                              <div
                                key={index}
                                className="px-4 py-2 hover:bg-gray-200 text-black text-sm font-medium border-b last:border-b-0 cursor-pointer rounded-t-xl"
                              >
                                {item}
                              </div>
                            ))}
                          </div>
                        )}
                      </div> */}
                      <>
                        <div className="relative z-[10000]">
                          <span
                            ref={buttonRef}
                            onClick={toggleMenu}
                            className="bg-primary-blue h-8 w-8 flex items-center justify-center rounded-lg cursor-pointer"
                          >
                            <LuPlus className="text-lg text-black" />
                          </span>
                        </div>

                        {showMenu && (
                          <OverlayPortal>
                            <div
                              className="fixed top-0 left-0 w-screen h-screen bg-black bg-opacity-60 z-[9999]"
                              onClick={toggleMenu}
                            />

                            {/* Main Menu */}
                            <div
                              ref={menuRef}
                              className="absolute bg-white shadow-lg rounded-xl w-40 py-1.5 z-[10000]"
                              style={{
                                top: `${position.top}px`,
                                left: `${position.left}px`,
                              }}
                            >
                              {[
                                "New Reservation",
                                "Block Dates",
                                "Courtesy Hold",
                                "Out of services",
                              ].map((item, index) => (
                                <div
                                  key={index}
                                  className="px-4 py-1.5 hover:bg-gray-200 text-black text-sm font-medium border-b last:border-b-0 cursor-pointer mx-0.5"
                                  onClick={(e) => handleMenuItemClick(item, e)}
                                >
                                  {item}
                                </div>
                              ))}
                            </div>

                            {/* Submenu */}
                            {submenuVisible && (
                              <div
                                className="absolute w-[430px] bg-white rounded-xl shadow-lg z-[10001]"
                                ref={submenuRef}
                                style={{
                                  top: `${submenuPosition.top}px`,
                                  left: `${submenuPosition.left}px`,
                                }}
                              >
                                {/* Header with close button */}
                                <div className="bg-teal-400 text-black font-semibold px-4 py-2 flex justify-between items-center rounded-t-xl">
                                  <span className="whitespace-nowrap">
                                    Tue, Aug.29 – Wed, Aug.30 Accommodation{" "}
                                    <br />
                                    total:
                                  </span>
                                  <div
                                    className="cursor-pointer "
                                    onClick={() => setSubmenuVisible(false)}
                                  >
                                    <IoClose className="text-xl ml-3" />
                                    <span className="text-sm">$100</span>
                                  </div>
                                </div>

                                {/* Options */}
                                <div className="bg-white divide-y">
                                  {[
                                    "Create New Reservation",
                                    "Blocked Dates",
                                    "Out of services",
                                  ].map((label, idx) => (
                                    <div
                                      key={idx}
                                      className="flex items-center  px-3 py-3 hover:bg-gray-100 cursor-pointer"
                                    >
                                      <span className="text-xl">
                                        <FiChevronRight className="text-gray-700" />
                                      </span>
                                      <span className="text-sm font-medium underline">
                                        {label}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </OverlayPortal>
                        )}
                      </>
                    </div>
                    <div className="date-nav flex  justify-between">
                      <div className="btns flex sm:gap-x-6 gap-x-1 items-center">
                        <button
                          onClick={() => changeMonth(-1)}
                          className="ml-auto text-gray-700 hover:bg-primary-blue hover:text-white  rounded-sm sm:text-base text-sm"
                        >
                          <FaAngleLeft />
                        </button>
                        <h6 className="flex items-center gap-2 text-[#000000] text-xs w-full">
                          {new Date(
                            currentYear,
                            currentMonth
                          ).toLocaleDateString("en-GB", {
                            month: "short",
                            year: "numeric",
                          })}
                        </h6>
                        <button
                          onClick={() => changeMonth(1)}
                          className="text-gray-700 hover:bg-primary-blue hover:text-white justify-center rounded-sm sm:text-base text-sm"
                        >
                          <FaAngleRight />
                        </button>
                      </div>
                    </div>
                    <div className="calendar-room-header w-full text-sm text-black font-medium flex gap-2">
                      <div className="w-5 h-5 rounded-sm bg-gray-300 flex items-center justify-center">
                        <FaAngleDown
                          className={`transition-transform duration-200 `}
                        />
                      </div>
                      All Room Types
                    </div>
                  </div>
                  <div className="relative z-5 mt-4">
                    {/* <ShowDates
                      currentMonth={currentMonth}
                      currentYear={currentYear}
                    /> */}
                    <ShowDates
                      currentMonth={currentMonth}
                      currentYear={currentYear}
                      dates={dates}
                    />
                  </div>
                </div>
              </div>

              {data.length === 0 ? (
                <div className="flex justify-center items-center py-10 text-gray-500 text-lg">
                  No rates found
                </div>
              ) : (
                data
                  .filter(
                    (category) =>
                      category.roomTypes && category.roomTypes.length > 0
                  )
                  .map((category) => (
                    <React.Fragment key={category._id}>
                      <div className="calendar-row flex  items-center border-b border-t border[#E8F1FD] bg-[#EEEEEE] ">
                        <div className="calendar-room-name flex gap-2 items-center sm:w-[196px] w-[165px] px-4 text-sm text-[#667085]  sticky left-0 z-30 bg-[#EEEEEE] border  ">
                          <div
                            className="w-[11.5rem] flex gap-2 text-black font-medium cursor-pointer  z-30 h-20 items-center bg-[#EEEEEE] "
                            onClick={() => toggleCategory(category._id)}
                          >
                            <div className="w-5 h-5 rounded-sm bg-gray-300 flex items-center justify-center">
                              <FaAngleDown
                                className={`transition-transform duration-200  ${
                                  expandedCategories[category._id]
                                    ? "rotate-180"
                                    : ""
                                }`}
                              />
                            </div>
                            {category._id === "private"
                              ? "Private Rooms"
                              : category._id === "dormitory"
                              ? "Dormitories"
                              : category._id}
                          </div>
                        </div>
                        <div className="calendar-slots flex  items-center justify-center  cursor-pointer z-0 px-2 bg-[#EEEEEE]">
                          {dates.map((date, index) => {
                            const slotDate = new Date(currentYear, currentMonth, date);
                            const isSlotWeekend = isWeekend(slotDate);

                            // Use selected room type for this category if available, otherwise use the first room type
                            const selectedRoomType = selectedRoomTypes[category._id];
                            const roomTypeToUse = selectedRoomType || category.roomTypes[0];
                            const roomRate = findRoomRate(roomTypeToUse, date);

                            const price = roomRate
                              ? isSlotWeekend
                                ? roomRate.basePrice
                                : roomRate.basePrice
                              : "N/A";

                            const currency = roomRate?.currency || "INR";

                            return (
                              <div
                                key={index}
                                className={`relative calendar-slot text-sm text-center font-medium min-w-[81px] w-max p-2 bg-white  my-2 mx-1 rounded-lg border    ${
                                  selectedRoomType ? "" : ""
                                } `}
                                onClick={(e) => handleSlotClick(index, e)}
                              >
                                <div className="px-2 rounded-lg text-center flex items-center justify-center flex-col cursor-pointer">
                                  <div
                                    className={`registers  text-black py-0.5 px-2.5 text-sm font-medium rounded-xl  font-inter ${
                                      !roomRate || roomRate.availableRooms === 0
                                        ? "bg-red-500"
                                        : roomRate.availableRooms <= 2
                                        ? "bg-orange-500"
                                        : "bg-green-500"
                                    }`}
                                  >
                                    {roomRate ? roomRate.availableRooms : 0}
                                  </div>
                                </div>
                                <div className="underline underline-offset-4">
                                  {getCurrencySymbol(currency)} {price}
                                </div>

                                {/* ✅ Popup below the slot */}
                                {openConfirmIndex === index &&
                                  popupPosition && (
                                    <OverlayPortal>
                                      <>
                                        {/* Full-screen dark overlay */}
                                        <div
                                          className="fixed inset-0 bg-black bg-opacity-50 z-40"
                                          onClick={() =>
                                            setOpenConfirmIndex(null)
                                          }
                                        />

                                        {/* Popup using fixed positioning */}
                                        <div
                                          ref={popupRef}
                                          className="fixed z-50"
                                          style={{
                                            top: `${popupPosition.top}px`,
                                            left: `${popupPosition.left}px`,
                                            transform: "translateX(-50%)",
                                          }}
                                        >
                                          {/* Triangle */}
                                          <div className="absolute left-1/2 -translate-x-1/2 -top-1 w-3 h-3 bg-gray-100 rotate-45 shadow-md z-50"></div>

                                          {/* Popup Box */}
                                          <div className="relative bg-gray-100 rounded-xl px-2 py-2 flex gap-1 items-center z-50 border border-gray-100">
                                            <button
                                              className="bg-white text-black px-4 rounded-lg border h-10"
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                setOpenConfirmIndex(null);
                                              }}
                                            >
                                              Cancel
                                            </button>
                                            <button className="bg-black text-white px-4 rounded-lg h-10">
                                              Confirm
                                            </button>
                                          </div>
                                        </div>
                                      </>
                                    </OverlayPortal>
                                  )}
                              </div>
                            );
                          })}
                        </div>
                      </div>

                      {expandedCategories[category._id] &&
                        category.roomTypes.map((room) => {
                          

                          return (
                            <div
                              key={room.roomTypeId}
                              className={`calendar-row flex justify-between items-center border-b border[#E8F1FD] ${
                                selectedRoomTypes[category._id]?.roomTypeName === room.roomTypeName
                                  ? "bg-white "
                                  : ""
                              }`}
                            >
                              <div
                                className={`calendar-room-name sm:w-[197px] w-[120px] sm:px-4 px-3 py-6 text-sm  border-r border-[#EEF0F2] sticky left-0 z-5 bg-white cursor-pointer hover:text-primary-blue ${
                                  selectedRoomTypes[category._id]?.roomTypeName === room.roomTypeName
                                    ? "text-primary-blue font-medium "
                                    : ""
                                }`}
                                onClick={() => handleRoomTypeClick(room, category._id)}
                              >
                                {room.roomTypeName}
                              </div>
                            </div>
                          );
                        })}
                    </React.Fragment>
                  ))
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Index;

// import React from 'react';
// import { FaChevronDown, FaPlus, FaCalendarAlt, FaBars } from 'react-icons/fa';

// const rooms = [
//   {
//     type: 'Standard Room',
//     price: '$100.00',
//     entries: [
//       { name: 'STA(1)', data: ['+ Maria...'] },
//       { name: 'SDQ(2)', data: ['+ Maria...'] },
//       { name: 'STA(3)', data: ['+ Big Chuck'] },
//       { name: 'STA(5)', data: ['+ Big Chuck'] },
//     ],
//   },
//   {
//     type: 'Double Room',
//     price: '$100.00',
//     entries: [
//       { name: 'DR5(1)', data: ['+ George Common'] },
//       { name: 'TWN 5/1', data: ['Katie Holmes'] },
//       { name: 'TWN 5/1', data: ['Katie Holmes'] },
//     ],
//   },
//   {
//     type: 'Room',
//     price: '$100.00',
//     entries: [
//       { name: 'TWI 1/1', data: ['+ George Common'] },
//       { name: 'TWI 1/2', data: ['Broken..'] },
//       { name: 'TWI 2/1', data: ['Possible////'] },
//     ],
//   },
// ];

// const BookingTable = () => {
//   return (
//     <div className="p-4 w-full overflow-auto">
//       {/* Header */}
//       <div className="flex items-center justify-between mb-4">
//         <div className="flex gap-2">
//           <button className="p-2 rounded bg-teal-100"><FaBars /></button>
//           <button className="p-2 rounded bg-teal-100"><FaCalendarAlt /></button>
//           <button className="p-2 rounded bg-teal-100"><FaPlus /></button>
//         </div>
//         <div className="text-gray-700 font-semibold">03 August, 2023</div>
//         <div className="text-gray-600 cursor-pointer">All Room Types</div>
//       </div>

//       {/* Dates */}
//       <div className="grid grid-cols-10 gap-2 text-center text-sm font-medium mb-4">
//         {[
//           'TUE 01', 'WED 02', 'THU 03', 'FRI 04', 'SAT 05',
//           'SUN 06', 'TUE 01', 'WED 02', 'THU 03', 'FRI 04',
//         ].map((day, index) => (
//           <div key={index} className="bg-gray-100 p-2 rounded">
//             <div className="text-lg font-bold">{index % 5 === 2 ? 4 : index % 5 === 4 ? 2 : 0}</div>
//             <div>{day}</div>
//             <div className="text-xs text-gray-500">4%</div>
//           </div>
//         ))}
//       </div>

//       {/* Table */}
//       <div>
//         {rooms.map((section, sIdx) => (
//           <div key={sIdx} className="mb-6">
//             <div className="flex items-center gap-2 font-semibold bg-gray-100 px-2 py-1 rounded">
//               <FaChevronDown className="text-xs" />
//               <span>{section.type}</span>
//               <span className="ml-auto text-sm">2</span>
//               <span className="text-sm">{section.price}</span>
//             </div>
//             {section.entries.map((entry, eIdx) => (
//               <div key={eIdx} className="flex items-center gap-2 border-b py-1">
//                 <div className="w-32 text-sm text-gray-700">{entry.name}</div>
//                 {entry.data.map((val, vIdx) => (
//                   <div
//                     key={vIdx}
//                     className={`px-3 py-1 rounded text-white text-xs whitespace-nowrap ${
//                       val.includes('Chuck') ? 'bg-green-600' :
//                       val.includes('Pulson') ? 'bg-sky-400' :
//                       val.includes('Katie') ? 'bg-cyan-800' :
//                       val.includes('Common') ? 'bg-sky-400' :
//                       val.includes('Broken') ? 'bg-red-500' :
//                       val.includes('Possible') ? 'bg-gray-400' :
//                       'bg-gray-400'}
//                   `}
//                   >
//                     {val}
//                   </div>
//                 ))}
//               </div>
//             ))}
//           </div>
//         ))}
//       </div>
//     </div>
//   );
// };

// export default BookingTable;

// import React from "react";
// import { FaAngleDown } from "react-icons/fa6";

// const StaticCalendarUI = () => {
//   const days = [
//     "TUE 01",
//     "WED 02",
//     "THU 03",
//     "FRI 04",
//     "SAT 05",
//     "SUN 06",
//     "TUE 01",
//     "WED 02",
//     "THU 03",
//     "FRI 04",
//     "FRI 04"
//   ];

//   return (
//     <div className="p-4">
//       {/* Header */}
//       <div className="flex bg-gray-100 rounded-t-lg">
//         <div className="min-w-[200px] p-3 border-r border-gray-300 font-medium text-sm">
//           All Room Types
//         </div>
//         <div className="flex overflow-x-auto">
//           {days.map((day, i) => (
//             <div
//               key={i}
//               className="min-w-[70px] text-center border-r border-gray-300 p-2"
//             >
//               <div className="text-xs font-semibold">{i % 2 === 0 ? 0 : 4}</div>
//               <div className="text-xs">{day}</div>
//               <div className="text-xs">4%</div>
//             </div>
//           ))}
//         </div>
//       </div>

//       {/* Room Categories */}
//       <div>
//         {[
//           { title: "Standard Room", rooms: ["STA(1)", "SDQ(2)", "STA(3)", "STA(5)"] },
//           { title: "Double Room", rooms: ["DR5(1)", "TWN 5/1", "TWN 5/1"] },
//           { title: "Room", rooms: ["TWI 1/1", "TWI 1/2", "TWI 2/1"] }
//         ].map((category, idx) => (
//           <div key={idx}>
//             {/* Category Header */}
//             <div className="flex bg-white border-b border-gray-200">
//               <div className="min-w-[200px] px-4 py-3 text-sm font-semibold text-gray-700 border-r border-gray-200 bg-white sticky left-0 z-10">
//                 <div className="flex items-center gap-1">
//                   <FaAngleDown className="text-sm" /> {category.title}
//                 </div>
//               </div>
//               <div className="flex">
//                 {days.map((_, i) => (
//                   <div
//                     key={i}
//                     className="min-w-[70px] text-center text-sm py-3 border-r border-gray-100"
//                   >
//                     2<br />
//                     $100.00
//                   </div>
//                 ))}
//               </div>
//             </div>

//             {/* Room Rows */}
//             {category.rooms.map((room, rIdx) => (
//               <div key={rIdx} className="flex border-b border-gray-200 bg-white">
//                 <div className="min-w-[200px] px-4 py-3 text-sm text-gray-700 border-r border-gray-200 sticky left-0 bg-white z-0">
//                   {room}
//                 </div>
//                 <div className="relative flex items-center">
//                   {rIdx === 0 && idx === 0 && (
//                     <div className="absolute left-[270px] bg-gray-400 text-white text-sm px-3 py-1 rounded-md">+ Maria...</div>
//                   )}
//                   {rIdx === 1 && idx === 0 && (
//                     <div className="absolute left-[270px] bg-gray-400 text-white text-sm px-3 py-1 rounded-md">+ Maria...</div>
//                   )}
//                   {rIdx === 2 && idx === 0 && (
//                     <div className="absolute left-[210px] bg-green-500 text-white text-sm px-3 py-1 rounded-md">+ Big Chuck</div>
//                   )}
//                   {rIdx === 3 && idx === 0 && (
//                     <div className="absolute left-[210px] bg-green-500 text-white text-sm px-3 py-1 rounded-md">+ Big Chuck</div>
//                   )}
//                   {rIdx === 0 && idx === 1 && (
//                     <div className="absolute left-[210px] bg-sky-400 text-white text-sm px-3 py-1 rounded-md">+ George Common</div>
//                   )}
//                   {rIdx === 1 && idx === 1 && (
//                     <div className="absolute left-[300px] bg-blue-900 text-white text-sm px-3 py-1 rounded-md">Katie Holmes</div>
//                   )}
//                   {rIdx === 2 && idx === 1 && (
//                     <div className="absolute left-[300px] bg-blue-900 text-white text-sm px-3 py-1 rounded-md">Katie Holmes</div>
//                   )}
//                   {rIdx === 0 && idx === 2 && (
//                     <div className="absolute left-[210px] bg-sky-400 text-white text-sm px-3 py-1 rounded-md">+ George Common</div>
//                   )}
//                   {rIdx === 1 && idx === 2 && (
//                     <div className="absolute left-[270px] bg-red-500 text-white text-sm px-3 py-1 rounded-md">Broken..</div>
//                   )}
//                   {rIdx === 2 && idx === 2 && (
//                     <div className="absolute left-[390px] bg-gray-400 text-white text-sm px-3 py-1 rounded-md">Possible////</div>
//                   )}
//                 </div>
//               </div>
//             ))}
//           </div>
//         ))}
//       </div>
//     </div>
//   );
// };

// export default StaticCalendarUI;
