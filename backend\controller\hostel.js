import Response from "../utills/response.js";
import mongoose from 'mongoose';
import {
    createHostel, updateHostel, listHostels, softDeleteHostel,
    createProperty, updateProperty, listPropertiesByOwner, listProperties,
    checkProperty, getPropertyDetailsById,
    uploadPropertyFiles, listFilteredProperties,
    getVisitorsCounts
} from "../services/hostel.js";
import Property from "../models/properties.js";
import sendEmail from "../commons/mail.js";
import bookingModel from "../models/bookings.js";
import { generatePropertyNumber } from "../utills/helper.js";
import propertyModel from "../models/properties.js"
import hostelLoginModel from "../models/hostelsLogin.js";
const addNewHostel = async (req, res) => {
    try {
        const hostelData = req.body;
        if (!req.body.name || !req.body.address || !req.body.room || !req.body.bed) {
            return Response.NotFound(res, null, 'Missing fields');
        }
        const hostel = await createHostel(hostelData);
        return Response.Created(res, hostel, 'Hostel created');
    } catch (error) {
        console.error("Error creating hostel:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};

const editHostel = async (req, res) => {
    try {
        const { id } = req.params;
        const hostelData = req.body;
        const updatedHostel = await updateHostel(id, hostelData);
        if (!updatedHostel) {
            return Response.NotFound(res, null, 'Hostel not found');
        }

        return Response.OK(res, null, 'Hostel updated');
    } catch (error) {
        console.error("Error updating hostel:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};

const listAllHostels = async (req, res) => {
    try {
        let { page, limit, ...filter } = req.query;

        // Convert page and limit to integers
        page = parseInt(page);
        limit = parseInt(limit);

        // If page or limit are not valid numbers, default them
        if (isNaN(page) || page <= 0) {
            page = 1;
        }
        if (isNaN(limit) || limit <= 0) {
            limit = 10;
        }

        const hostels = await listHostels(filter, page, limit);

        const totalPages = Math.ceil(eventsData.totalEvents / parseInt(limit));
        const pagination = {
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages,
            totalEvents: eventsData.totalEvents
        };

        return Response.OK(res, { hostels: hostels.data, pagination }, 'Hostels listed');
    } catch (error) {
        console.error("Error listing hostels:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};

// const getSingleHostel = async (req, res) => {
//     try {
//         const { id } = req.params;
//         const hostel = await getHostel(id);

//         if (!hostel) {
//             return Response.NotFound(res, null, 'Hostel not found');
//         }

//         return Response.OK(res, hostel, 'Hostel found');
//     } catch (error) {
//         console.error("Error getting hostel:", error.message);
//         return Response.InternalServerError(res, null, error.message);
//     }
// };

const softDelete = async (req, res) => {
    try {
        const { id } = req.params;
        const deletedHostel = await softDeleteHostel(id);

        if (!deletedHostel) {
            return Response.NotFound(res, null, 'Hostel not found');
        }

        return Response.OK(res, { message: 'Hostel deleted' });
    } catch (error) {
        console.error("Error soft deleting hostel:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};

const addNewProperty = async (req, res) => {
    try {
        if (req.user.role !== 'hostel_owner') {
            return Response.Forbidden(res, null, 'Not authorized');
        }

        const propertyData = req.body;
        propertyData.propertyOwner = req.user._id;

        const property = await createProperty(propertyData);
        return Response.Created(res, property, 'Property created');
    } catch (error) {
        console.error("Error creating property:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};

const editProperty = async (req, res) => {
    try {
        const { id } = req.params;
        const propertyData = req.body;

        const updatedProperty = await updateProperty(id, propertyData);
        if (!updatedProperty) {
            return Response.NotFound(res, null, 'Property not found');
        }

        return Response.OK(res, null, 'Property updated');
    } catch (error) {
        console.error("Error updating property:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};

const myPropertyCounts = async (req, res) => {
    try {
        const userId = req.user._id;
        console.log("userId",userId)
        // 1. Find the hostel login document for the user
        const hostelOwner = await hostelLoginModel.findById(userId);
        if (!hostelOwner) {
            return Response.BadRequest(res, null, "Hostel owner or otaId not found.");
        }

        // 2. Use otaId to query properties
        const otaId = hostelOwner.otaId;
        const email = hostelOwner.email
        const propertyQuery = { otaId, isDeleted: false, isActive: true };
        console.log("propertyQuery",propertyQuery)
        const totalProperties = await Property.countDocuments(propertyQuery);
        const properties = await Property.find(propertyQuery, { _id: 1, name: 1 });

        // 3. Count bookings for this otaId
        const totalBooking = await bookingModel.countDocuments({ otaId });

        return Response.OK(res, {
            counts: totalProperties,
            properties,
            totalBooking
        }, 'Properties Counts');

    } catch (error) {
        console.error("Error listing properties:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};

const listOwnerProperties = async (req, res) => {
    try {
        const ownerId = req.user._id;
        let { page, limit, ...filter } = req.query;

        // Convert page and limit to integers
        page = parseInt(page);
        limit = parseInt(limit);

        // If page or limit are not valid numbers, default them
        if (isNaN(page) || page <= 0) {
            page = 1;
        }
        if (isNaN(limit) || limit <= 0) {
            limit = 10;
        }

        const { properties, totalProperties } = await listPropertiesByOwner(ownerId, filter, parseInt(page), parseInt(limit));

        const totalPages = Math.ceil(totalProperties / limit);
        const pagination = {
            page,
            limit,
            totalPages,
            totalProperties
        };

        return Response.OK(res, { properties, pagination }, 'Properties listed');
    } catch (error) {
        console.error("Error listing properties:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};

const listPropertiesController = async (req, res) => {
    try {
        let { page, limit, ...filter } = req.query;

        // Convert page and limit to integers
        page = parseInt(page);
        limit = parseInt(limit);

        // If page or limit are not valid numbers, default them
        if (isNaN(page) || page <= 0) {
            page = 1;
        }
        if (isNaN(limit) || limit <= 0) {
            limit = 10;
        }

        const { properties, totalProperties } = await listProperties(filter, parseInt(page), parseInt(limit));

        const totalPages = Math.ceil(totalProperties / limit);
        const pagination = {
            page,
            limit,
            totalPages,
            totalProperties
        };

        return Response.OK(res, { properties, pagination }, 'Properties listed');
    } catch (error) {
        console.error("Error listing properties:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};

const checkPropertyExistsController = async (req, res) => {
    try {
        const { id } = req.body;
        const property = await checkProperty(id, req.user._id);
       // await sendEmail("propertyListed", { email: '<EMAIL>', propertyName: "Aarhus Hostel" })
        if (!property) {
            return Response.NotFound(res, null, 'Property not found');
        }
        if(property.propertyOwner){
            return Response.Conflict(res, null, 'Property Already Occupied');
        }
          // Generate the property number
          const propertyNumber = await generatePropertyNumber();

          // Update the property with verification status, owner, generated number, and other details
          const propertyUpdate = await propertyModel.findByIdAndUpdate(
              { _id: property._id },
              {
                  isPropertyVerified: true,
                  verifiedDate: Date.now(),
                  propertyOwner: req.user._id,
                  number: propertyNumber // Add the generated property number
              },
              { new: true, lean: true }
          );
          await hostelLoginModel.findByIdAndUpdate(  { _id: req.user._id },
              {
                 otaId:property?.otaId
              })
        return Response.OK(res, property, 'Property & Room added!');
    } catch (error) {
        console.error("Error finding property data in ota schema:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
}

const getSingleProperty = async (req, res) => {
    try {
        const { id } = req.params;
        const property = await getProperty(id);

        if (!property) {
            return Response.NotFound(res, null, 'Property not found');
        }

        return Response.OK(res, property, 'Property found');
    } catch (error) {
        console.error("Error getting property:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};

const softDeletePropertyController = async (req, res) => {
    try {
        const { id } = req.params;
        const deletedProperty = await softDeleteProperty(id);

        if (!deletedProperty) {
            return Response.NotFound(res, null, 'Property not found');
        }

        return Response.OK(res, { message: 'Property deleted' });
    } catch (error) {
        console.error("Error soft deleting property:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};

const getPropertyDetails = async (req, res) => {
    try {
        const { id } = req.params;
        const hostel = await getPropertyDetailsById(id);

        if (!hostel) {
            return Response.NotFound(res, null, 'Property not found');
        }

        return Response.OK(res, hostel, 'Property Details');
    } catch (error) {
        console.error("Error getting hostel:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};

const uploadPropertyFilesController = async (req, res) => {
    try {
        const { id } = req.params;
        const files = req.files;
        const property = await uploadPropertyFiles(id, files);
        return Response.OK(res, null, 'Property Details');
    } catch (error) {
        console.error("Error getting hostel:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
}

const listFilteredPropertiesController = async (req, res) => {
    try {
        let { page, limit, rate, status, country, sort } = req.query;

        page = parseInt(page) || 1;
        limit = parseInt(limit) || 10;

        const filters = { rate, status, country, sort }; // Added sort to filters

        const propertiesData = await listFilteredProperties(filters, page, limit);

        const totalPages = Math.ceil(propertiesData.totalProperties / limit);
        const pagination = {
            page,
            limit,
            totalPages,
            totalProperties: propertiesData.totalProperties
        };

        return Response.OK(res, { properties: propertiesData.properties, pagination }, 'Properties Retrieved Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const getTravellersVisited = async (req, res) => {
    try {
      const { propertyId } = req.params;
  
      // Validate propertyId
      if (!mongoose.Types.ObjectId.isValid(propertyId)) {
        return Response.BadRequest(res, null, 'Invalid property ID.');
      }
  
      // Define the filter for bookings
      const filter = {
        property: new mongoose.Types.ObjectId(propertyId),
        checkOutDate: { $lt: new Date() } // Only consider past bookings
      };
      const visitors = await getVisitorsCounts(filter)
      const visitorCount = visitors;
  
      return Response.OK(res, { visitorCount }, 'Count of travelers who visited the property retrieved successfully.');
    } catch (error) {
      console.error('Error fetching travelers visited:', error);
      return Response.InternalServerError(res, null, error.message);
    }
  };


export {
    addNewHostel, editHostel, listAllHostels, softDelete, addNewProperty,
    editProperty, listOwnerProperties, listPropertiesController, checkPropertyExistsController,
    getSingleProperty, softDeletePropertyController,
    getPropertyDetails, uploadPropertyFilesController,
    listFilteredPropertiesController,myPropertyCounts,getTravellersVisited
};
