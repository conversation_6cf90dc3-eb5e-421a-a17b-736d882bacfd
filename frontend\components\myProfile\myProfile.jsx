/* eslint-disable react/jsx-key */
import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
import { MdEdit } from "react-icons/md";
import Flags from "country-flag-icons/react/3x2";
import toast from "react-hot-toast";
import { editProfile<PERSON>pi, getProfile<PERSON>pi } from "@/services/webflowServices";
import { BASE_URL } from "@/utils/api";
import { Trash2, ChevronRight } from "lucide-react";
import Link from "next/link";
import countries from "world-countries";

const MyProfile = ({
  data,
  profileTravelingData,
  profileViewsData,
  setLoading,
}) => {
  const [formData, setFormData] = useState({
    firstName: data?.data?.name?.first || "",
    lastName: data?.data?.name?.last || "",
    email: data?.data?.email || "",
    contact: data?.data?.contact || "",
    address: data?.data?.address || "",
    profilePic: data?.data?.profileImage || "",
  });

  const [errors, setErrors] = useState({});
  const [countryNameToCode, setCountryNameToCode] = useState({});

  useEffect(() => {
    setFormData({
      firstName: data?.data?.name?.first || "",
      lastName: data?.data?.name?.last || "",
      email: data?.data?.email || "",
      contact: data?.data?.contact || "",
      address: data?.data?.address || "",
      profilePic: data?.data?.profileImage || "",
    });
  }, [data]);

  const fileInputRef = useRef(null);

  const CountryFlag = Flags[data?.data?.countryShortName] || Flags["US"];

  const handleEditClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = async (e) => {
    const selectedFile = e.target.files[0];
    const { files } = e.target;

    if (files.length === 0) {
      toast.error("At least one file is required");
      return;
    }
    const allowedTypes = ["image/jpeg", "image/png", "image/jpg"];

    // Check if all selected files are of allowed types
    const invalidFiles = Array.from(files).filter(
      (file) => !allowedTypes.includes(file.type)
    );

    if (invalidFiles.length > 0) {
      toast.error("Only JPG, JPEG, and PNG files are allowed.");
      return;
    }
    if (selectedFile) {
      setLoading(true);
      setErrors({ ...errors, file: null });

      try {
        const formData = new FormData();
        formData.append("files", selectedFile);

        const presignedUrlResponse = await fetch(
          `${BASE_URL}/fileUpload/generate-presigned-url`,
          {
            method: "POST",
            body: formData,
          }
        );

        if (!presignedUrlResponse.ok) {
          throw new Error("Failed to get presigned URL");
        }

        const presignedUrlData = await presignedUrlResponse.json();
        const objectURL = Array.isArray(presignedUrlData.data) && presignedUrlData.data[0]?.path;

        if (presignedUrlData?.status) {
          const response = await editProfileApi({
            profileImage: objectURL,
          });
          toast.success(
            response?.data?.message || "Profile updated successfully!"
          );

          const updatedProfileResponse = await getProfileApi();
          console.log("updatedProfileResponse", updatedProfileResponse);
          setFormData({
            firstName: updatedProfileResponse?.data?.data?.name?.first || "",
            lastName: updatedProfileResponse?.data?.data?.name?.last || "",
            email: updatedProfileResponse?.data?.data?.email || "",
            contact: updatedProfileResponse?.data?.data?.contact || "",
            address: updatedProfileResponse?.data?.data?.address || "",
            profilePic: updatedProfileResponse?.data?.data?.profileImage || "",
          });
        }

        toast.success("Profile picture uploaded successfully!");
      } catch (error) {
        console.error("Error uploading profile picture", error);
        toast.error("Error uploading profile picture");
        setErrors({
          ...errors,
          file: "Error uploading file. Please try again.",
        });
      } finally {
        setLoading(false);
      }
    } else {
      setErrors({ ...errors, file: "File is required" });
    }
  };

  useEffect(() => {
    const buildCountryNameToCodeMapping = () => {
      const mapping = {};
      countries.forEach((country) => {
        const name = country.name.common; // Country name
        const code = country.cca2; // ISO 3166-1 alpha-2 code
        if (name && code) {
          mapping[name] = code.toLowerCase(); // Convert to lowercase for flagcdn compatibility
        }
      });
      setCountryNameToCode(mapping); // Set mapping to state
    };

    buildCountryNameToCodeMapping();
  }, []);

  const [isLoading, setIsLoading] = useState(true);
  const [imgLoaded, setImgLoaded] = useState(false);
  const [imgFailed, setImgFailed] = useState(false);

  useEffect(() => {
    if (formData?.profilePic) {
      const img = new window.Image();
      img.src = `${process.env.NEXT_PUBLIC_S3_URL_FE}/${formData.profilePic}`;

      img.onload = () => {
        setImgLoaded(true);
        setImgFailed(false);
      };

      img.onerror = () => {
        setImgLoaded(false);
        setImgFailed(true);
      };
    } else {
      setImgLoaded(false);
      setImgFailed(true); // If there's no image, fallback should display
    }
  }, [formData?.profilePic]);

  useEffect(() => {
    if (data && profileTravelingData && profileViewsData) {
      setIsLoading(false);
    }
  }, [data, profileTravelingData, profileViewsData]);

  const ProfileSkeleton = () => (
    <>
      <div className="h-8 w-48 bg-gray-300 rounded mb-4 animate-pulse" />
      <div className="bg-gray-200 rounded-3xl p-6 animate-pulse mb-8 h-[200px]" />
      <div className="flex justify-between items-center">
        <div className="h-6 w-36 bg-gray-300 rounded mb-3 animate-pulse" />
        <div className="h-6 w-36 bg-gray-300 rounded mb-3 animate-pulse" />
      </div>
      <div className="flex justify-between items-center">
        <div className="h-6 w-36 bg-gray-300 rounded my-6 animate-pulse" />
        <div className="h-6 w-36 bg-gray-300 rounded my-6 animate-pulse" />
      </div>
      <div className="flex justify-between items-center">
        <div className="flex gap-2">
            <div className="w-[30px] h-[30px] bg-gray-300 rounded-md animate-pulse"></div>
            <div className="h-6 w-36 bg-gray-300 rounded animate-pulse" />
        </div>
        <div className="w-[30px] h-[30px] rounded-md bg-gray-300 animate-pulse"></div>
      </div>
      
    </>
  );

  return (
    <>
      {isLoading ? (
        <ProfileSkeleton />
      ) : (
      <>
        <h2 className='text-[#40E0D0] sm:text-3xl text-2xl font-bold mb-6'>
          My <span className='text-black'>Profile</span>
        </h2>
        <div
          className=' bg-cover rounded-3xl sm:mb-8 mb-5 lg:p-16 xs:p-8 p-6'
          style={{
            backgroundImage: `url(${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/profile_bg.png)`,
          }}
        >
          <div className='relative flex items-center lg:gap-12 gap-8'>
            <div className='relative xs:w-32 xs:h-32 w-20 h-20 rounded-full border-teal-400'>
              {formData?.profilePic && !imgFailed ? (
                <>
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/${formData.profilePic}`}
                    alt="Profile"
                    className={`object-cover w-full h-full rounded-full transition-opacity duration-300 ${
                      imgLoaded ? 'opacity-100' : 'opacity-0'
                    }`}
                    width={128}
                    height={128}
                  />
                  {!imgLoaded && (
                    <div className="absolute top-0 left-0 w-full h-full rounded-full bg-gray-300 animate-pulse flex items-center justify-center text-white">
                      MixDorm
                    </div>
                  )}
                </>
              ) : (
                <div className="w-full h-full bg-gray-300 rounded-full flex items-center justify-center text-white">
                  MixDorm
                </div>
              )}
              <button
                type='button'
                className='absolute top-0 xs:-right-1 -right-2 z-10 lg:w-8 lg:h-8 xs:w-6 xs:h-6 w-5 h-5 rounded-full text-[#012e42] bg-white text-center flex items-center justify-center hover:bg-primary-blue hover:text-white'
                onClick={handleEditClick} // Trigger file input on click
              >
                <MdEdit />
              </button>
              <input
                type='file'
                ref={fileInputRef}
                className='hidden'
                accept='.jpg, .jpeg, .png'
                onChange={handleFileChange}
              />
              <div className='absolute top-0 right-0 bottom-0 left-0'>
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/profile_pic_circle.png`}
                  alt='Elipse'
                  width={124}
                  height={153}
                  className='absolute xs:left-4 left-3 top-1/2 transform -translate-y-1/2'
                  loading='lazy'
                />
              </div>
              <div className='absolute bottom-0 xs:-right-1 -right-2 z-10 lg:w-8 lg:h-8 xs:w-6 xs:h-6 w-5 h-5 rounded-full border-2 border-white overflow-hidden'>
                {/* <Image
                  src={assets.MyProfileFlag}
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                  width={32}
                  height={32}
                  alt="Flag"
                  className="w-8 h-8 rounded-full border-2 border-white object-cover"
                /> */}
                <CountryFlag
                  title='Country Flag'
                  className='object-cover lg:w-10 lg:h-10 xs:w-8 xs:h-8 w-7 h-7 -mt-[5px] -ml-1'
                />
              </div>
            </div>
            <div>
              <div className='flex items-start gap-10'>
                <h2 className='text-white xs:text-2xl text-lg font-bold'>
                  {data?.data?.name?.first} {data?.data?.name?.last}
                  <span className='block text-white font-light xs:text-base text-sm xs:mt-1'>
                    {data?.data?.address}
                  </span>
                </h2>
                {/* <div className="flex space-x-2">
                  <button className="bg-primary-blue text-black text-sm px-4 py-2 hover:bg-sky-blue-200  rounded-full">
                    Follow
                  </button>
                  <button className="bg-primary-blue text-black text-sm px-4 py-2 hover:bg-sky-blue-200  rounded-full">
                    More
                  </button>
                </div> */}
              </div>
              <div className='flex items-center lg:mt-10 mt-5 ml-0 gap-10'>
                <div className='text-white xs:text-base text-sm mr-4'>
                  <span>Country Visited</span>
                  <br />
                  <p className='md:pt-4 xs:pt-2 pt-1 text-center xs:text-base text-sm'>
                    {profileTravelingData?.data?.length}
                  </p>
                </div>
                <div className='text-white xs:text-base text-sm'>
                  <span>Profile Visit</span>
                  <br />
                  <p className='md:pt-4 xs:pt-2 pt-1 text-center xs:text-base text-sm'>
                    {profileTravelingData?.data?.reduce(
                      (count, country) => count + country?.properties?.length,
                      0
                    ) || 0}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className='sm:mb-6 mb-3 mt-6'>
          <div className='flex justify-between'>
            <h2 className='text-[#40E0D0] sm:text-2xl text-lg font-bold'>
              Country{" "}
              <span className='text-black'>
                Visited({profileTravelingData?.data?.length})
              </span>
            </h2>
            <Link
              href='#'
              className='sm:text-lg text-md font-extrabold text-black bg-transparent rounded-4xl hover:text-sky-blue-750'
              prefetch={false}
            >
              See All
            </Link>
          </div>

          <div className='grid grid-cols-2 md:grid-cols-3 sm:grid-cols-1 justify-center lg:gap-4 md:gap-2 gap-1'>
            {profileTravelingData?.data?.map((data) => {
              const propertiesCount = data?.properties?.length || 0; // Calculate properties count for each country

              return (
                <>
                  <div className='relative h-[130px] bg-[#CCCEDB] border border-[#495DCE] rounded-3xl mt-[40px] cursor-pointer'>
                    <div className='absolute top-[-25px] left-5'>
                      <Image 
                        src={`https://flagcdn.com/w320/${
                          countryNameToCode[data?.country]
                        }.png`}
                        className='object-cover sm:w-[55px] w-10 h-10 sm:h-[55px] rounded-full border'
                        height='55'
                        width='55'
                        alt='flag'
                      />
                    </div>
                    <h3 className='font-bold text-xl text-[#495DCE] ml-5 mt-8'>
                      {data?.country}
                    </h3>
                    <div className='absolute bottom-5 left-5'>
                    <Link href="/my-profile?section=stay">
                      <p className='text-sm font-normal text-[#495DCE] underline'>
                        <span>{data?.properties?.length}</span> Properties
                        <br />
                        Visited
                      </p>
                    </Link>
                    </div>
                    <div className='absolute right-[55px] top-2 sm:block hidden'>
                      {propertiesCount >= 1 && (
                        <div className='absolute left-[-45px] top-[40px]'>
                          <Image 
                            // src={`https://${data?.properties?.[0]?.photos?.[0]?.url}`}
                            src={data?.properties?.[0]?.images?.[0]?.objectUrl}
                            alt='visitor'
                            width='67'
                            height='80'
                            className='rotate-[-20deg]'
                          />
                        </div>
                      )}
                      {propertiesCount >= 2 && (
                        <div>
                          <Image 
                            // src={`https://${data?.properties?.[1]?.photos?.[1]?.url}`}
                            src={data?.properties?.[1]?.images?.[1]?.objectUrl}
                            alt='visitor'
                            width='55'
                            height='65'
                          />
                        </div>
                      )}
                      {propertiesCount === 3 ||
                        (propertiesCount >= 3 && (
                          <div className='absolute right-[-45px] top-[40px]'>
                            <Image 
                              // src={`https://${data?.properties?.[2]?.photos?.[2]?.url}`}
                              src={data?.properties?.[2]?.images?.[2]?.objectUrl}
                              alt='visitor'
                              width='50'
                              height='60'
                              className='rotate-[20deg]'
                            />
                          </div>
                        ))}
                    </div>
                  </div>
                </>
              );
            })}
          </div>
        </div>
        <div className='mb-6 sm:mt-6 mt-0'>
          <div className='flex justify-between'>
            <h2 className='text-[#40E0D0] sm:text-2xl text-lg font-bold'>
              Profile{" "}
              <span className='text-black'>
                Visited({profileViewsData?.data?.length})
              </span>
            </h2>
            <Link
              href='#'
              className='sm:text-lg text-md font-extrabold text-black bg-transparent rounded-4xl hover:text-sky-blue-750'
              prefetch={false}
            >
              See All
            </Link>
          </div>
          <div className='grid grid-cols-2 md:grid-cols-3 sm:grid-cols-2 justify-center lg:gap-4 md:gap-2 gap-1 mt-5'>
            {profileViewsData?.data?.map((data) => (
              <div className='block h-65 border border-grey-300 rounded-2xl p-3'>
                <div className='flex justify-between'>
                  <div className='flex gap-2'>
                    <Image 
                      className='w-[50px] h-[50px] rounded-full'
                      src={data?.profileImage}
                      alt='profile'
                      width={30}
                      height={30}
                    />
                    <div>
                      <p className='text-sm font-semibold text-black'>
                        {data?.name?.first}
                      </p>
                      <p className='text-sm font-normal text-[#888888]'>
                        0 Follwers
                      </p>
                    </div>
                  </div>
                  <div className='mt-3'>
                    <Image 
                      className='w-7 h-7'
                      src='https://mixdorm.s3.ap-south-1.amazonaws.com/front-images/userwitheye-icon.png'
                      alt='profile'
                      width={20}
                      height={20}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
          <Link
            className='flex justify-between cursor-pointer mt-5 items-center'
            href='/accountdelete'
            target='_blank'
            prefetch={false}
          >
            <div className='flex gap-2'>
              <Trash2
                size='30'
                className='bg-[#FDE1DF] text-red-600 p-1 rounded-lg'
              />
              <span className='text-lg font-semibold text-red-600'>
                Delete Account
              </span>
            </div>
            <div>
              <ChevronRight size='26' />
            </div>
          </Link>
        </div>
      </>
      )}
    </>
  );
};

export default MyProfile;
