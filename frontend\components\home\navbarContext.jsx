 
import { createContext, useState, useContext, useEffect } from "react";
import { getItemLocalStorage } from "@/utils/browserSetting";

const NavbarContext = createContext();

export const NavbarProvider = ({ children }) => {
  const [flagUrl2, setFlagUrl] = useState("");
  const [currencyCode2, setCurrencyCode] = useState("");
  const [currencyCodeOwner, setCurrencyOwner] = useState("");
  const [token, setTokenCode] = useState("");
  const [role, setRoleCode] = useState("");
  const [hopid, setHopid] = useState("");
  const [isMapOpen, setIsMapOpen] = useState(false);
  const [selectedOwner, setSelectedOwner] = useState("");


  const updateCountry2 = (flag2, code2) => {
    setFlagUrl(flag2);
    setCurrencyCode(code2);
    // Optionally save to local storage if you want it to persist
    localStorage.setItem("selectedCountryFlag", flag2);
    localStorage.setItem("selectedCurrencyCode", code2);
  };

  const updateCountryOwner = (selectedOption) => {
    setCurrencyOwner(selectedOption?.value?.currencyCode);
    setSelectedOwner(selectedOption);
    localStorage.setItem("selectedOwnerCountry", selectedOption?.value?.name);
    localStorage.setItem("selectedCurrencyCodeOwner", selectedOption?.value?.currencyCode);
  };

  const updateUserStatus = (token) => {
    setTokenCode(token);
    localStorage.setItem("token", token);
  };

  const updateUserRole = (role) => {
    setRoleCode(role);
    localStorage.setItem("role", role);
  };

  const updateHopId = (id) => {
    setHopid(id);
    localStorage.setItem("hopid", id);
  };

  const updateMapState= (value) => {
    setIsMapOpen(value);
   
  };

  useEffect(() => {
    // Initialize the state from local storage when the app loads
    const flag2 = getItemLocalStorage("selectedCountryFlag");
    const code2 = getItemLocalStorage("selectedCurrencyCode");
    const codeOwner = getItemLocalStorage("selectedCurrencyCodeOwner");
    const selectedOwnerCountry = getItemLocalStorage("selectedOwnerCountry");
    const token = getItemLocalStorage("token");
    const role = getItemLocalStorage("role");
    const hopid = getItemLocalStorage("hopid");


    if (flag2) setFlagUrl(flag2);
    if (code2) setCurrencyCode(code2);
    if (token) setTokenCode(token);
    if (role) setRoleCode(role);
    if (hopid) setRoleCode(hopid);
    if (codeOwner) setCurrencyOwner(codeOwner);
    if (selectedOwnerCountry) setSelectedOwner(selectedOwnerCountry);

  }, []);

  return (
    <NavbarContext.Provider value={{ flagUrl2, currencyCode2, updateCountry2, token, updateUserStatus,role,updateUserRole, updateHopId ,hopid,updateMapState,isMapOpen,setIsMapOpen , updateCountryOwner, currencyCodeOwner ,selectedOwner}}>
      {children}
    </NavbarContext.Provider>
  );
};

export const useNavbar = () => useContext(NavbarContext);
