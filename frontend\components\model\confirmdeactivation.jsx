/* eslint-disable react/no-unknown-property */
import React, { useState } from "react";
import Modal from "@mui/material/Modal";
import dynamic from "next/dynamic";
import { deleteAccountApi } from "@/services/webflowServices";
import {
  getItemLocalStorage,
  removeItemLocalStorage,
} from "@/utils/browserSetting";
import { useNavbar } from "../home/<USER>";
import { removeFirebaseToken } from "@/services/ownerflowServices";
import toast from "react-hot-toast";

const Finaldeactivation = dynamic(
  () => import("../../components/model/finaldeactivation"),
  {
    ssr: false,
  }
);

const Confirmdeactivation = ({
  openConfirmdeactivationpopup,
  handleCloseConfirmdeactivationpopup,
  selectedReason,
  selectedPeriod,
  comment,
}) => {
  const [openFinaldeactivationpopup, setopenFinaldeactivationpopup] =
    useState(false);
  const handleCloseFinaldeactivationpopup = () =>
    setopenFinaldeactivationpopup(false);
  const style = {
    position: "fixed",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: "100%",
    bgcolor: "background.paper",
    border: "2px solid #000",
    boxShadow: 24,
  };
  const { updateUserStatus,updateUserRole } = useNavbar();

  const handleOpenFinaldeactivationpopup = async () => {
    const payload = {
      reason: selectedReason || "",
      deactivatePeriod: selectedPeriod || "",
      comment: comment || "",
      type: "deactivate",
    };

    try {
      const response = await deleteAccountApi(payload);
      console.log("response", response);
      if (response?.data?.status) {
        handleCloseConfirmdeactivationpopup();
        setopenFinaldeactivationpopup(true);
        removeItemLocalStorage("token");
        updateUserStatus("");
        updateUserRole("");
        removeItemLocalStorage("name");
        removeItemLocalStorage("role");
        removeItemLocalStorage("email");
        removeItemLocalStorage("contact");
        const payload = {
          token: getItemLocalStorage("FCT"),
          userId: getItemLocalStorage("id"),
        };
        try {
          await removeFirebaseToken(payload);
          console.log("FCM token removed successfully.");
          removeItemLocalStorage("FCT");
        } catch (error) {
          console.error("Error removing FCM token:", error);
        }
        removeItemLocalStorage("id");
      } else {
        handleCloseConfirmdeactivationpopup();
        toast.error("There is some issue in Delete your account..");
      }
    } catch (error) {
      console.error("Failed to delete account:", error);
      // Optionally, display an error message to the user here
    }
  };

  return (
    <>
      <Modal
        open={openConfirmdeactivationpopup}
        onClose={handleCloseConfirmdeactivationpopup}
        aria-labelledby='modal-modal-title'
        aria-describedby='modal-modal-description'
      >
        <div sx={style}>
          <div className='bg-white rounded-2xl max-w-[700px] mx-auto left-1/2 absolute w-[95%] top-1/2 -translate-y-1/2 -translate-x-1/2 text-center'>
            <div className='md:p-6 p-4'>
              <h3 className='text-black text-2xl font-semibold mb-1 mt-5'>
                Confirm <span className='text-[#40E0D0]'>Deactivation</span>
              </h3>
              <p className='text-base text-black font-semiboldbold mt-7'>
                Your account will be deactivated for the selected period. You
                can log
                <div>back in to reactivate it anytime</div>
              </p>
              <div className='flex justify-center gap-2 mt-7'>
                <div>
                  <button
                    className='bg-[#eeeeee] text-black font-semibold py-2 px-5 rounded-full text-sm lg:text-md'
                    onClick={handleCloseConfirmdeactivationpopup}
                  >
                    Go back
                  </button>
                </div>
                <div>
                  <button
                    className='bg-primary-blue text-black font-semibold py-2 px-5 rounded-full text-sm lg:text-md'
                    onClick={handleOpenFinaldeactivationpopup}
                  >
                    Confirm Deactivation
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Modal>
      <Finaldeactivation
        openFinaldeactivationpopup={openFinaldeactivationpopup}
        handleCloseFinaldeactivationpopup={handleCloseFinaldeactivationpopup}
      />
    </>
  );
};

export default Confirmdeactivation;
