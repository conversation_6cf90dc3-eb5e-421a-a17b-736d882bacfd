import express from 'express';
import { addBlog<PERSON>ontroller, updateBlogController, getBlogByIdController, listAllBlogsController, deleteBlogController, listBlogCategory, addBlogCategory } from '../controller/blog.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();
router.post('/category', checkAuth('add_blog'), addBlogCategory);
router.get('/category', checkAuth('add_blog'), listBlogCategory);

router.post('/', checkAuth('add_blog'), addBlogController);
router.put('/:id', checkAuth('update_blog'), updateBlogController);
router.get('/', listAllBlogsController);
router.get('/:slug', getBlogByIdController);
router.delete('/:id', checkAuth('delete_blog'), deleteBlogController);



export default router;

/**
 * @swagger
 * tags:
 *   name: Blog
 *   description: Blog Management
*/

/**
 * @swagger
 * /blog:
 *   post:
 *     tags: [Blog]
 *     summary: Add a new blog
 *     description: Creates a new blog
 *     security:
 *       - bearerAuth: []
 *     consumes:
 *       - application/json
 *     produces:
 *       - application/json
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/definitions/Blog'
 *     responses:
 *       201:
 *         description: Blog Added Successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/BlogResponse'
 *       500:
 *         description: Internal Server Error
 */

/**
 * @swagger
 * /blog/{id}:
 *   put:
 *     tags: [Blog]
 *     summary: Update blog by ID
 *     description: Updates a blog
 *     security:
 *       - bearerAuth: []
 *     consumes:
 *       - application/json
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/definitions/Blog'
 *     responses:
 *       200:
 *         description: Blog Updated Successfully
 *         content:
 *           application/json:
 *             example:
 *               message: 'Blog updated successfully'
 *       500:
 *         description: Internal Server Error
 */

/**
 * @swagger
 * /blog/{id}:
 *   get:
 *     tags: [Blog]
 *     summary: Get blog by ID
 *     description: Returns a single blog
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         type: string
 *     responses:
 *       200:
 *         description: Blog Retrieved Successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/BlogResponse'
 *       500:
 *         description: Internal Server Error
 */

/**
 * @swagger
 * /blog:
 *   get:
 *     tags: [Blog]
 *     summary: List all blogs
 *     description: Returns a list of blogs
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: page
 *         in: query
 *         required: false
 *         type: integer
 *         default: 1
 *       - name: limit
 *         in: query
 *         required: false
 *         type: integer
 *         default: 10
 *       - name: filter
 *         in: query
 *         required: false
 *         schema:
 *           type: object
 *           style: deepObject
 *           explode: true
 *     responses:
 *       200:
 *         description: Blogs Retrieved Successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 blogs:
 *                   type: array
 *                   items:
 *                     $ref: '#/definitions/BlogResponse'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     totalBlogs:
 *                       type: integer
 *       500:
 *         description: Internal Server Error
 */

/**
 * @swagger
 * /blog/{id}:
 *   delete:
 *     tags: [Blog]
 *     summary: Delete blog by ID
 *     description: Deletes a blog
 *     security:
 *       - bearerAuth: []
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Blog Deleted Successfully
 *         content:
 *           application/json:
 *             example:
 *               message: 'Blog deleted successfully'
 *       500:
 *         description: Internal Server Error
 */

/**
 * @swagger
 * definitions:
 *   Blog:
 *     type: object
 *     properties:
 *       title:
 *         type: string
 *       categoryId:
 *         type: string
 *       createdBy:
 *         type: string
 *       description:
 *         type: string
 *       images:
 *         type: array
 *         items:
 *           type: string
 *       videoLink:
 *         type: string
 *       isActive:
 *         type: boolean
 *       isDeleted:
 *         type: boolean
 *       keywords:
 *         type: array
 *         items:
 *           type: string
 *     required:
 *       - title
 *       - createdBy
 */

/**
 * @swagger
 * definitions: 
 *   BlogResponse:
 *     type: object
 *     properties:
 *       id:
 *         type: string
 *       title:
 *         type: string
 *       categoryId:
 *         type: object
 *         properties:
 *           id:
 *             type: string
 *           name:
 *             type: string
 *       createdBy:
 *         type: object
 *         properties:
 *           id:
 *             type: string
 *           username:
 *             type: string
 *       description:
 *         type: string
 *       images:
 *         type: array
 *         items:
 *           type: string
 *       videoLink:
 *         type: string
 *       isActive:
 *         type: boolean
 *       isDeleted:
 *         type: boolean
 *       keywords:
 *         type: array
 *         items:
 *           type: string
 *       createdAt:
 *         type: string
 *         format: date-time
 *       updatedAt:
 *         type: string
 *         format: date-time
*/
/**
 * @swagger
 * /blog/category:
 *   post:
 *     tags: [Blog]
 *     summary: Add a new blog category
 *     description: Creates a new blog category
 *     security:
 *       - bearerAuth: []
 *     consumes:
 *       - application/json
 *     produces:
 *       - application/json
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *             properties:
 *               title:
 *                 type: string
 *                 example: "Travel Tips"
 *     responses:
 *       201:
 *         description: Blog Added Successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/definitions/BlogResponse'
 *       500:
 *         description: Internal Server Error
 */
/**
 * @swagger
 * /blog/category:
 *   get:
 *     tags: [Blog]
 *     summary: Get all blog categories
 *     description: Retrieves a list of all blog categories
 *     security:
 *       - bearerAuth: []
 *     produces:
 *       - application/json
 *     responses:
 *       200:
 *         description: List of blog categories
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   _id:
 *                     type: string
 *                     example: "66bdb5efc1f6ab232a54d9e1"
 *                   title:
 *                     type: string
 *                     example: "Travel Tips"
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *                     example: "2025-08-11T12:34:56.789Z"
 *                   updatedAt:
 *                     type: string
 *                     format: date-time
 *                     example: "2025-08-11T12:34:56.789Z"
 *       500:
 *         description: Internal Server Error
 */
