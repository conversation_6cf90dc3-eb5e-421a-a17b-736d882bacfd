import Settings from "../models/settings.js";

const defaultSettings = [
    {
        key : "contact-us",
        value : {
            email : "<EMAIL>"
        }
    },
]

async function initializeSettings() {
    for (const settingData of defaultSettings) {
        try {            
            await Settings.findOneAndUpdate(
                { key : settingData.key },
                settingData,
                {upsert : true, new : true}
            );
            console.log(`Setting '${settingData.key}' initialized or updated`);
        } catch (err) {
            console.error(`Error initializing settings '${settingData.key}':`, err);
        }
    }
}

export { initializeSettings };