import mongoose from 'mongoose';

const propertyPaymentsSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Types.ObjectId,
      ref: 'users',
    },
    name:{
      type:String
    },
    room: {
      type: mongoose.Types.ObjectId,
      ref: 'properties',
    },
    property: {
      type: mongoose.Types.ObjectId,
      ref: 'rooms',
    },
    paymentId: {
      type: String,
      unique: true, // Ensure unique payment IDs
    },
    amount: {
      type: Number,
      required: true,
    },
    currency: {
      type: String,
      required: true,
    },
    checkIn:{
      type:Date
    },
    checkOut:{
      type:Date
    },
    type:{
      type:String,
      enum: ['cash', 'card'], 
    },
    status: {
      type: String,
      enum: ['fail', 'pending', 'paid'],
      default: 'paid',
    }
  },
  {
    timestamps: true,
  }
);

const PropertyPayments = mongoose.model('propertyPayments', propertyPaymentsSchema);
export default PropertyPayments;
