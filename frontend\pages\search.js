import React, { useEffect, useState, useCallback, useRef } from "react";
import Image from "next/image";
import Link from "next/link";
import { ChevronDown, Globe } from "lucide-react";

import SwapVertOutlinedIcon from "@mui/icons-material/SwapVertOutlined";
import { Box, Divider, Drawer } from "@mui/material";
import {
  // getFeaturedHostelApi,
  getlistApiPagination,
  likeUnlikePropertyApi,
} from "@/services/webflowServices";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, Autoplay } from "swiper/modules";
import dynamic from "next/dynamic";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { motion, AnimatePresence } from "framer-motion";

// const FaHeart = dynamic(
//   () => import("react-icons/fa6").then((mod) => mod.FaHeart),
//   { ssr: false }
// );
const FaHeart = dynamic(
  () => import("react-icons/fa6").then((mod) => mod.FaHeart),
  {
    ssr: false,
    loading: () => (
      <div className='h-5 w-5 bg-gray-200 animate-pulse rounded-full' />
    ),
  }
);
// const NewMapComponent = dynamic(
//   () => import("@/components/map/newMapComponent"),
//   {
//     ssr: false,
//   }
// );
const NewMapComponent = dynamic(
  () => import("@/components/map/newMapComponent").then((mod) => mod.default),
  {
    ssr: false,
    loading: () => (
      <div className='h-[400px] w-full bg-gray-100 animate-pulse'></div>
    ),
  }
);
// Lazy load SortPopup
// const SortPopup = dynamic(() => import("../components/popup/sortPopup"), {
//   ssr: false,
// });
// const SortPopup = dynamic(
//   () => import("../components/popup/sortPopup").then((mod) => mod.default),
//   {
//     ssr: false,
//     loading: () => (
//       <div className="h-8 w-8 animate-pulse bg-gray-200 rounded" />
//     ),
//   }
// );
const SortPopup = dynamic(
  () => import("../components/popup/sortPopup").then((mod) => mod.default),
  {
    ssr: false,
    loading: () => (
      <div className='h-8 w-8 bg-gray-200 rounded animate-pulse' />
    ),
  }
);

const SearchPropforSearch = dynamic(
  () => import("@/components/home/<USER>"),
  {
    ssr: false,
  }
);
import Faciicons from "@/services/icon";
import {
  getItemLocalStorage,
  setItemLocalStorage,
} from "@/utils/browserSetting";
import { useRouter } from "next/router";
import Loader from "@/components/loader/loader";
// import { FaHeart } from "react-icons/fa6";
import countries from "world-countries";
import { PiMapPinLineFill } from "react-icons/pi";

import { TbMap2 } from "react-icons/tb";
import { useNavbar } from "@/components/home/<USER>";
import toast from "react-hot-toast";
import SearchPaginaton from "@/components/common/commonPaginationForSearch";
import Head from "next/head";
import { RxMix } from "react-icons/rx";

const profileImages = [
  `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/usericon1.jpeg`,
  `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/usericon2.jpeg`,
  `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/usericon3.jpeg`,
  `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/usericon4.jpeg`,
];
const flagImages = [
  `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/flag14.png`,
  `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/flag15.png`,
  `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/flag13.png`,
  `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/flag11.png`,
];
const SearchListing = () => {
  // Active Tabs

  const [layout, setLayout] = useState("grid"); // Default is grid view

  const fullText = "Our listings are powered by advanced AI technology";
  const words = fullText.split(" ");

  // Variants for animation
  const container = {
    hidden: { opacity: 0 },
    visible: (i = 1) => ({
      opacity: 1,
      transition: { staggerChildren: 0.12, delayChildren: 0.04 * i },
    }),
  };

  const child = {
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        damping: 12,
        stiffness: 100,
      },
    },
    hidden: {
      opacity: 0,
      y: 20,
      transition: {
        type: "spring",
        damping: 12,
        stiffness: 100,
      },
    },
  };

  // Mobile Drawer
  const [openDrawer, setOpenDrawer] = useState(false);
  const [currencyData, setCurrencyData] = useState({});
  const [loading, setLoading] = useState(true);

  // For Map
  const [showMap, setShowMap] = useState(false);
  const [showMapList, setShowMapList] = useState(false);

  const [imagesLoaded, setImagesLoaded] = useState({});

  // Function to handle image load
  const handleImageLoad = (itemId, imgIndex) => {
    setImagesLoaded((prev) => ({
      ...prev,
      [`${itemId}-${imgIndex}`]: true,
    }));
  };

  const toggleDrawer = (newOpen) => () => {
    setOpenDrawer(newOpen);
  };

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);
      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);

  const getCurrencySymbol = (currencyCode) => {
    return currencyData[currencyCode] || currencyCode;
  };

  // Mobile Drawer for Search

  // const [openSearchMobile, setOpenSearchMobile] = useState(false);

  const [initialData, setInitialData] = useState({});
  const router = useRouter();

  // States for search criteria
  const [state, setState] = useState("");
  const [dataa, setDataa] = useState({
    checkIn: "",
    checkOut: "",
  });
  const [guest, setGuest] = useState(1);
  const [selectedstate, setselectedstate] = useState("");

  // States for search results
  const [property, setProperty] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [headerSearch, setHeaderSearch] = useState(false);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(12);
  const [totalData, setTotalData] = useState();
  // Sorting state

  const [sort, setSort] = useState(null);
  const [showSortPopup, setShowSortPopup] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [currency, setCurrency] = useState("");

  // const [featuredHostelData, setFeaturedHostelData] = useState([]);
  const { currencyCode2, token, updateMapState } = useNavbar();
  // const isFirstRender = useRef(null);
  const mapBottomRef = useRef(null);
  const [selectedCategory, setSelectedCategory] = useState("");

  const tags = [
    "budgetFriendly",
    "luxuryHostels",
    "natureConnect",
    "partyHostels",
    "digitalNomadFriendly",
    "unique",
    "ecoFriendlyHostel",
    "newTown",
    "adventureSeekers",
    "cultureAndLocalSeekers",
  ];

  const tagDisplayNames = {
    budgetFriendly: "Budget Friendly",
    luxuryHostels: "Luxury Hostels",
    natureConnect: "Nature Connect",
    partyHostels: "Party Hostels",
    digitalNomadFriendly: "Digital Nomad Friendly",
    unique: "Unique",
    ecoFriendlyHostel: "Eco Friendly Hostel",
    newTown: "New Town",
    adventureSeekers: "Adventure Seekers",
    cultureAndLocalSeekers: "Cultural & Local Experience",
  };
  // const normalizeFaciName = (name) =>
  //   name
  //     ?.toLowerCase()
  //     .replace(/[\s\\-]/g, "_")
  //     .replace(/[^a-z0-9_]/g, "");
  const normalizeFaciName = (name) =>
    name?.toLowerCase().replace(/[^a-z0-9]/g, "");

  const handleCategoryClick = (category) => {
    setSelectedCategory(category);
    setCurrentPage(1);
    getData(1, category);
  };

  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedData = localStorage.getItem("bookingdata");
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        setInitialData(parsedData);
        setState(parsedData.state || "");
        setDataa({
          checkIn: parsedData.checkIn || "",
          checkOut: parsedData.checkOut || "",
        });
        setGuest(parsedData.guest || 1);
      }
      const currencyData = getItemLocalStorage("selectedCurrencyCode");
      setCurrency(currencyData);
    }
  }, []);
  const getData = useCallback(
    async (page = 1, category = selectedCategory) => {
      try {
        setLoading(true);
        const searchState = state.trim();
        if (!searchState) {
          setProperty([]);
          setTotalPages(1);
          setLoading(false);
          return;
        }
        const finalState = searchState.split(",")[0].trim();

        const res = await getlistApiPagination(
          finalState,
          page,
          itemsPerPage,
          sort,
          dataa?.checkIn,
          dataa?.checkOut,
          currencyCode2 || "",
          guest || 1,
          category
        );
        setselectedstate(searchState);

        if (res?.data?.data?.properties) {
          setProperty(res.data.data.properties);
          setTotalPages(res.data.data.pagination.totalPages);
          setTotalData(res.data.data.pagination.totalProperties);
          const checkIn = dataa?.checkIn;
          const checkOut = dataa?.checkOut;
          const bookingData = { state, checkIn, checkOut, guest: guest };
          localStorage.setItem("bookingdata", JSON.stringify(bookingData));
        } else {
          setProperty([]);
          setTotalPages(1);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        setProperty([]);
        setTotalPages(1);
      } finally {
        setLoading(false);
      }
    },
    [state, sort, dataa, currencyCode2, token, itemsPerPage]
  );
  useEffect(() => {
    if (initialData) {
      getData();
    }
  }, [initialData, currencyCode2, token, itemsPerPage]);

  const handleSubmit = () => {
    setHeaderSearch(true);
    setCurrentPage(1);
    getData(1);
  };
  const handleChange = (e) => {
    const { value } = e.target;
    setDataa((prevData) => ({
      ...prevData,
      checkIn: value?.checkIn,
      checkOut: value?.checkOut,
    }));
  };

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
      getData(newPage);
    }
  };

  const freeFacilities = (item) => {
    return Array.isArray(item?.freeFacilities)
      ? item.freeFacilities.slice(0, 5)
      : [];
  };

  // eslint-disable-next-line no-unused-vars
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 100) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const [isLoading, setIsLoading] = useState(true);

  // const handleImageLoad = () => {
  //   setIsLoading(false);
  // };
  useEffect(() => {
    if (property?.length > 0) {
      setIsLoading(false);
    }
  }, [property]);

  const handleMap = () => {
    setShowMap(true);
    setShowMapList(false);
    updateMapState(true);

    // Scroll to the bottom of the map after a short delay (wait for rendering)
    setTimeout(() => {
      mapBottomRef.current?.scrollIntoView({ behavior: "smooth" });
    }, 100);
  };
  const handleShowMap = (selectedItem = null) => {
    setShowMap(!showMap);
    updateMapState(!showMap);
    setShowMapList(false);

    if (selectedItem?._id) {
      localStorage.setItem("selectedMapProperty", JSON.stringify(selectedItem));
    }
  };

  const HandleLike = async (id, like) => {
    if (getItemLocalStorage("token")) {
      setLoading(true);
      try {
        const payload = {
          isLike: !like,
        };
        const response = await likeUnlikePropertyApi(id, payload);
        // Update the UI directly with the response data
        if (response?.data?.data || response?.data) {
          const updatedProperty = response?.data?.data || response?.data;
          setProperty((prev) =>
            prev.map((item) =>
              item._id === id
                ? { ...item, ...updatedProperty, liked: updatedProperty.liked }
                : item
            )
          );
        }
      } catch (error) {
        console.error("Error updating like status:", error);
        toast.error("Failed to update like status");
      } finally {
        setLoading(false);
      }
    } else {
      toast.error("Please login first!!");
      // router.push("/");
    }
  };

  // const fetchFeaturedHostelData = async () => {
  //   setLoading(true);
  //   try {
  //     const response = await getFeaturedHostelApi(currencyCode2 || "USD");

  //     setFeaturedHostelData(response?.data?.data?.properties || []);
  //   } catch (error) {
  //     console.error("Error fetching stay data:", error);
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  // useEffect(() => {
  //   if (!isFirstRender.current) {
  //     fetchFeaturedHostelData();
  //   } else {
  //     isFirstRender.current = false;
  //   }
  // }, [currencyCode2, token]);

  // const getImageUrl = (url) => {
  //   return url && url.startsWith("http") ? url : `https://${url}`;
  // };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const formatTag = (tag) => {
    if (!tag) return "";

    const formatted = tag.replace(/([A-Z])/g, " $1").trim();

    return formatted
      .split(" ")
      .map((word) => word.toUpperCase())
      .join(" ");
  };

  const [hasFetched] = useState(false);

  const WordTag = () => {
    // const wordData = [
    //   {
    //     word: "SleptOn",
    //     color: "Dusty Grey",
    //     tooltip: "Under-the-radar, surprisingly good",
    //     hexColor: "#4a4a4a",
    //     bgColor: "#e8e8e8"
    //   },
    //   {
    //     word: "Vibed",
    //     color: "Jade Wave",
    //     tooltip: "Balanced vibes, calm but social",
    //     hexColor: "#00A86B",
    //     bgColor: "#E0F5EB"
    //   },
    //   {
    //     word: "Raw",
    //     color: "Earth Brown",
    //     tooltip: "Real, unpolished charm",
    //     hexColor: "#7a4a15",
    //     bgColor: "#f0e0d0"
    //   },
    //   {
    //     word: "Plugged",
    //     color: "Electric Purple",
    //     tooltip: "Tapped into local scene, social",
    //     hexColor: "#9c00d6",
    //     bgColor: "#f0d5ff"
    //   },
    //   {
    //     word: "Booked",
    //     color: "Forest Green",
    //     tooltip: "Loved by frequent backpackers",
    //     hexColor: "#1a751a",
    //     bgColor: "#d5f0d5"
    //   },
    //    {
    //   word: "Tapped",
    //   color: "Matcha Pop",
    //   tooltip: "Gen Z fav, creator-endorsed",
    //   hexColor: "#7EBA00",
    //   bgColor: "#EEFFCC"
    // },
    //   {
    //     word: "Curated",
    //     color: "Deep Indigo",
    //     tooltip: "Designed, aesthetic, intentional",
    //     hexColor: "#3a1ca8",
    //     bgColor: "#ddd0ff"
    //   },
    //   {
    //     word: "Dreamed",
    //     color: "Ocean Blue",
    //     tooltip: "Feels surreal — scenic, peaceful",
    //     hexColor: "#02557a",
    //     bgColor: "#cce4f0"
    //   },
    //   {
    //     word: "Wavy",
    //     color: "Sunset Coral",
    //     tooltip: "Lighthearted, surfer or chill crowd",
    //     hexColor: "#e66b3a",
    //     bgColor: "#ffd8c5"
    //   },
    //   {
    //     word: "Buzzed",
    //     color: "Mint Green",
    //     tooltip: "Something exciting is always happening",
    //     hexColor: "#40c040",
    //     bgColor: "#d5ffd5"
    //   },
    //   {
    //     word: "Mooded",
    //     color: "Blush Pink",
    //     tooltip: "Relaxed energy, emotionally warm",
    //     hexColor: "#e05a67",
    //     bgColor: "#ffd5d9"
    //   },
    //  {
    //   word: "Glowup",
    //   color: "Molten Copper",
    //   tooltip: "Recently upgraded or improved",
    //   hexColor: "#B87333",
    //   bgColor: "#F5E6D3"
    // },
    //   {
    //     word: "Unfiltered",
    //     color: "Clay Beige",
    //     tooltip: "Authentic, raw travel experience",
    //     hexColor: "#b58a4a",
    //     bgColor: "#f0e2cc"
    //   },
    //   {
    //     word: "Slayed",
    //     color: "Lavender",
    //     tooltip: "Stylish, sassy and confident space",
    //     hexColor: "#8a5dc8",
    //     bgColor: "#e5d5ff"
    //   },
    //   {
    //     word: "Wildin",
    //     color: "Hot Neon Red",
    //     tooltip: "Nonstop parties, loud energy",
    //     hexColor: "#ff0033",
    //     bgColor: "#ffccd5"
    //   },
    //   {
    //     word: "Popped",
    //     color: "Tangerine Orange",
    //     tooltip: "Got trendy fast, full of hype",
    //     hexColor: "#ff7b2a",
    //     bgColor: "#ffd8b3"
    //   },
    // {
    //   word: "Glowup",
    //   color: "Burnt Amber",
    //   tooltip: "Recently upgraded or improved",
    //   hexColor: "#D18B47",
    //   bgColor: "#FAEBD7"
    // },
    //   {
    //     word: "Iconic",
    //     color: "Royal Violet",
    //     tooltip: "Bucket-list worthy, famous",
    //     hexColor: "#5c3d8c",
    //     bgColor: "#ddd0f0"
    //   },
    //   {
    //     word: "Legendary",
    //     color: "Deep Crimson",
    //     tooltip: "Timeless, respected by all",
    //     hexColor: "#990000",
    //     bgColor: "#ffcccc"
    //   },
    //   {
    //     word: "Trendish",
    //     color: "Electric Green",
    //     tooltip: "Fashion-forward, culture rich",
    //     hexColor: "#00cc00",
    //     bgColor: "#ccffcc"
    //   },
    //   {
    //     word: "Scene",
    //     color: "Matte Navy",
    //     tooltip: "Perfectly placed for everything",
    //     hexColor: "#2a3c50",
    //     bgColor: "#d5dbe3"
    //   },
    //   {
    //     word: "Real",
    //     color: "Steel Blue",
    //     tooltip: "No BS, all experience",
    //     hexColor: "#3a6b8c",
    //     bgColor: "#cce0f0"
    //   },
    //   {
    //     word: "Unlocked",
    //     color: "Neon Cyan",
    //     tooltip: "Gives access to local secrets",
    //     hexColor: "#00cccc",
    //     bgColor: "#ccffff"
    //   },
    //   {
    //     word: "Linked",
    //     color: "Teal Glow",
    //     tooltip: "Creator collabs, community network",
    //     hexColor: "#006666",
    //     bgColor: "#ccf0f0"
    //   }
    // ];
    const wordData = [
      {
        word: "SleptOn",
        color: "Dusty Grey",
        tooltip: "Under-the-radar, surprisingly good",
        hexColor: "#4a4a4a",
        bgColor: "rgba(232, 232, 232, 0.8)",
      },
      {
        word: "Vibed",
        color: "Jade Wave",
        tooltip: "Balanced vibes, calm but social",
        hexColor: "#00A86B",
        bgColor: "rgba(224, 245, 235, 0.8)",
      },
      {
        word: "Raw",
        color: "Earth Brown",
        tooltip: "Real, unpolished charm",
        hexColor: "#7a4a15",
        bgColor: "rgba(240, 224, 208, 0.8)",
      },
      {
        word: "Plugged",
        color: "Electric Purple",
        tooltip: "Tapped into local scene, social",
        hexColor: "#9c00d6",
        bgColor: "rgba(240, 213, 255, 0.8)",
      },
      {
        word: "Tapped",
        color: "Matcha Pop",
        tooltip: "Gen Z fav, creator-endorsed",
        hexColor: "#7EBA00",
        bgColor: "rgba(238, 255, 204, 0.8)",
      },
      {
        word: "Curated",
        color: "Deep Indigo",
        tooltip: "Designed, aesthetic, intentional",
        hexColor: "#3a1ca8",
        bgColor: "rgba(221, 208, 255, 0.8)",
      },
      {
        word: "Dreamed",
        color: "Ocean Blue",
        tooltip: "Feels surreal — scenic, peaceful",
        hexColor: "#02557a",
        bgColor: "rgba(204, 228, 240, 0.8)",
      },
      {
        word: "Wavy",
        color: "Sunset Coral",
        tooltip: "Lighthearted, surfer or chill crowd",
        hexColor: "#e66b3a",
        bgColor: "rgba(255, 216, 197, 0.8)",
      },
      {
        word: "Buzzed",
        color: "Mint Green",
        tooltip: "Something exciting is always happening",
        hexColor: "#40c040",
        bgColor: "rgba(213, 255, 213, 0.8)",
      },
      {
        word: "Mooded",
        color: "Blush Pink",
        tooltip: "Relaxed energy, emotionally warm",
        hexColor: "#e05a67",
        bgColor: "rgba(255, 213, 217, 0.8)",
      },
      {
        word: "Glowup",
        color: "Molten Copper",
        tooltip: "Recently upgraded or improved",
        hexColor: "#B87333",
        bgColor: "rgba(245, 230, 211, 0.8)",
      },
      {
        word: "Unfiltered",
        color: "Clay Beige",
        tooltip: "Authentic, raw travel experience",
        hexColor: "#b58a4a",
        bgColor: "rgba(240, 226, 204, 0.8)",
      },
      {
        word: "Slayed",
        color: "Lavender",
        tooltip: "Stylish, sassy and confident space",
        hexColor: "#8a5dc8",
        bgColor: "rgba(229, 213, 255, 0.8)",
      },
      {
        word: "Wildin",
        color: "Hot Neon Red",
        tooltip: "Nonstop parties, loud energy",
        hexColor: "#ff0033",
        bgColor: "rgba(255, 204, 213, 0.8)",
      },
      {
        word: "Popped",
        color: "Tangerine Orange",
        tooltip: "Got trendy fast, full of hype",
        hexColor: "#ff7b2a",
        bgColor: "rgba(255, 216, 179, 0.8)",
      },
      {
        word: "Glowup",
        color: "Burnt Amber",
        tooltip: "Recently upgraded or improved",
        hexColor: "#D18B47",
        bgColor: "rgba(250, 235, 215, 0.8)",
      },
      {
        word: "Iconic",
        color: "Royal Violet",
        tooltip: "Bucket-list worthy, famous",
        hexColor: "#5c3d8c",
        bgColor: "rgba(221, 208, 240, 0.8)",
      },
      {
        word: "Legendary",
        color: "Deep Crimson",
        tooltip: "Timeless, respected by all",
        hexColor: "#990000",
        bgColor: "rgba(255, 204, 204, 0.8)",
      },
      {
        word: "Trendish",
        color: "Electric Green",
        tooltip: "Fashion-forward, culture rich",
        hexColor: "#00cc00",
        bgColor: "rgba(204, 255, 204, 0.8)",
      },
      {
        word: "Scene",
        color: "Matte Navy",
        tooltip: "Perfectly placed for everything",
        hexColor: "#2a3c50",
        bgColor: "rgba(213, 219, 227, 0.8)",
      },
      {
        word: "Real",
        color: "Steel Blue",
        tooltip: "No BS, all experience",
        hexColor: "#3a6b8c",
        bgColor: "rgba(204, 224, 240, 0.8)",
      },
      {
        word: "Unlocked",
        color: "Neon Cyan",
        tooltip: "Gives access to local secrets",
        hexColor: "#00cccc",
        bgColor: "rgba(204, 255, 255, 0.8)",
      },
      {
        word: "Linked",
        color: "Teal Glow",
        tooltip: "Creator collabs, community network",
        hexColor: "#006666",
        bgColor: "rgba(204, 240, 240, 0.8)",
      },
    ];
    const [currentIndex, setCurrentIndex] = useState(
      Math.floor(Math.random() * wordData.length)
    );

    useEffect(() => {
      const interval = setInterval(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % wordData.length);
      }, 9000);

      return () => clearInterval(interval);
    }, []);

    const currentWord = wordData[currentIndex];

    return (
      <div>
        <AnimatePresence mode='wait'>
          <motion.span
            key={currentWord.word}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.8 }}
            className='text-xs font-bold flex items-center px-3 py-1.5 rounded-3xl shadow-lg border-2'
            title={currentWord.tooltip}
            style={{
              color: currentWord.hexColor,
              backgroundColor: currentWord.bgColor,
              borderColor: currentWord.hexColor,
            }}
          >
            {currentWord.word}
          </motion.span>
        </AnimatePresence>
      </div>
    );
  };

  return (
    <>
      <Head>
        <title>
          {/* {selectedstate
            ? `Hostels in ${selectedstate} | Book Affordable Dorms | Mixdorm`
            : "Search Hostels & Dorms | Find Your Stay | Mixdorm"} */}
          Find the Best Hostels Near You | Mixdorm
        </title>
        <meta
          name='description'
          content='Search and Compare Hostels by Type—Budget, Luxury, Nature, Party & More. Fast and Easy Booking with Free Mix Premium Offer!'
        />
        <link
          rel='preload'
          as='image'
          href={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/top-black-bg.webp`}
          fetchPriority='high'
        />
      </Head>
      <div className='font-manrope w-full xxxl:container xxxl:mx-auto'>
        <Loader open={loading} />

        {/* Main Content */}
        <main className=''>
          <div className=''>
            {/* Search Results Header */}
            <div className='relative min-h=[200px] md:min-h-[300px]'>
              {/* Background Image */}
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/top-black-bg.webp`}
                alt='background'
                fill
                quality={75}
                priority
                className='z-0 object-cover'
                sizes='100vw'
              />
              <div className='flex items-center justify-center border-gray-200 flex-col border-y pt-10 z-10'>
                <h1 className='mid:text-3xl md:text-2xl text-xl font-bold text-center text-[#40E0D0] mb-4 z-10 mt-10 '>
                  {selectedstate}{" "}
                  {/* <span className="text-black">Maharashtra, India</span> */}
                </h1>
                <div className='w-[80%] md:w-[90%] lg:w-[60%] my-2'>
                  {/* <SearchProperties
                  setState={setState}
                  state={state}
                  dataa={dataa}
                  guest={guest}
                  setGuest={setGuest}
                  handleChange={handleChange}
                  handleSubmit={handleSubmit}
                  setHeaderSearch={setHeaderSearch}
                  size="small"
                /> */}
                  <SearchPropforSearch
                    setState={setState}
                    state={state}
                    dataa={dataa}
                    guest={guest}
                    setGuest={setGuest}
                    handleChange={handleChange}
                    handleSubmit={handleSubmit}
                    setHeaderSearch={setHeaderSearch}
                    size='small'
                  />
                </div>
                {/* <div className="relative flex sm:mt-0 mt-4">
                <button
                  className="border border-[#EEEEEE] text-black px-4 py-2 rounded-3xl flex items-center"
                  onClick={() => setShowSortPopup(true)}
                >
                  <SwapVertOutlinedIcon className="mr-2" />
                  Sort by
                </button>
                <SortPopup
                  show={showSortPopup}
                  onClose={() => setShowSortPopup(false)}
                  setSort={setSort}
                  getData={() => getData(currentPage)}
                  close={setShowSortPopup}
                />
              </div> */}
                <div className='h-[100px] w-full mt-0 md:mt-6 overflow-x-auto z-10 hidden md:block'>
                  <ul className='flex items-center justify-evenly px-4 lg:px-0 w-max md:w-full  whitespace-nowrap'>
                    <li
                      className={`flex flex-col text-sm font-manrope text-white text-center font-normal items-center justify-center md:justify-around h-[100px] hover:border-b-4 hover:border-b-primary-blue hover:text-gray-200 cursor-pointer w-16 md:w-auto 
                      ${
                        selectedCategory === tags[0]
                          ? "border-b-4 border-b-primary-blue text-white "
                          : "text-white"
                      }`}
                      onClick={() => handleCategoryClick(tags[0])}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Budget-w.svg`}
                        alt='icon'
                        width={150}
                        height={75}
                        className='h-9 w-9 '
                        loading='lazy'
                      />
                      <div className='hidden md:block'>
                        Budget <br /> Friendly
                      </div>
                    </li>
                    <li
                      className={`flex flex-col text-sm font-manrope text-white text-center items-center font-normal justify-around h-[100px] hover:border-b-4 hover:border-b-primary-blue hover:text-gray-200 cursor-pointer w-16 md:w-auto 
                      ${
                        selectedCategory === tags[1]
                          ? "border-b-4 border-b-primary-blue text-white"
                          : "text-white"
                      }`}
                      onClick={() => handleCategoryClick(tags[1])}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Luxury-w.svg`}
                        alt='icon'
                        width={150}
                        height={75}
                        className='h-9 w-9'
                        loading='lazy'
                      />
                      <div className='hidden md:block'>
                        {" "}
                        Luxury <br /> Hostels
                      </div>
                    </li>
                    <li
                      className={`flex flex-col text-sm font-manrope text-white text-center items-center font-normal justify-around h-[100px] hover:border-b-4 hover:border-b-primary-blue hover:text-gray-200 cursor-pointer w-16 md:w-auto 
                      ${
                        selectedCategory === tags[2]
                          ? "border-b-4 border-b-primary-blue text-white"
                          : "text-white"
                      }`}
                      onClick={() => handleCategoryClick(tags[2])}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Nature-w.svg`}
                        alt='icon'
                        width={150}
                        height={75}
                        className='h-9 w-9'
                        loading='lazy'
                      />
                      <div className='hidden md:block'>
                        {" "}
                        Nature <br /> Connect
                      </div>
                    </li>
                    <li
                      className={`flex flex-col text-sm font-manrope text-white text-center items-center font-normal justify-around h-[100px] hover:border-b-4 hover:border-b-primary-blue hover:text-gray-200 cursor-pointer w-16 md:w-auto 
                      ${
                        selectedCategory === tags[3]
                          ? "border-b-4 border-b-primary-blue text-white"
                          : "text-white"
                      }`}
                      onClick={() => handleCategoryClick(tags[3])}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Party-w.svg`}
                        alt='icon'
                        width={150}
                        height={75}
                        className='h-9 w-9'
                        loading='lazy'
                      />
                      <div className='hidden md:block'>
                        {" "}
                        Party <br /> Hostels
                      </div>
                    </li>
                    <li
                      className={`flex flex-col text-sm font-manrope text-white text-center items-center font-normal justify-around h-[100px] hover:border-b-4 hover:border-b-primary-blue hover:text-gray-200 cursor-pointer w-16 md:w-auto 
                      ${
                        selectedCategory === tags[4]
                          ? "border-b-4 border-b-primary-blue text-white"
                          : "text-white"
                      }`}
                      onClick={() => handleCategoryClick(tags[4])}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Nomad-w.svg`}
                        alt='icon'
                        width={150}
                        height={75}
                        className='h-9 w-9'
                        loading='lazy'
                      />
                      <div className='hidden md:block'>
                        {" "}
                        Digital
                        <br /> Nomad-Friendly
                      </div>
                    </li>
                    <li
                      className={`flex flex-col text-sm font-manrope text-white text-center items-center font-normal justify-around h-[100px] hover:border-b-4 hover:border-b-primary-blue hover:text-gray-200 cursor-pointer w-16 md:w-auto 
                      ${
                        selectedCategory === tags[5]
                          ? "border-b-4 border-b-primary-blue text-white"
                          : "text-white"
                      }`}
                      onClick={() => handleCategoryClick(tags[5])}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Unique-w.svg`}
                        alt='icon'
                        width={150}
                        height={75}
                        className='h-9 w-9'
                        loading='lazy'
                      />
                      <div className='hidden md:block'> Unique</div>
                    </li>
                    <li
                      className={`flex flex-col text-sm font-manrope text-white text-center items-center font-normal justify-around h-[100px] hover:border-b-4 hover:border-b-primary-blue hover:text-gray-200 cursor-pointer w-16 md:w-auto 
                      ${
                        selectedCategory === tags[6]
                          ? "border-b-4 border-b-primary-blue text-white"
                          : "text-white"
                      }`}
                      onClick={() => handleCategoryClick(tags[6])}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Eco-w.svg`}
                        alt='icon'
                        width={150}
                        height={75}
                        className='h-9 w-9'
                        loading='lazy'
                      />
                      <div className='hidden md:block'>
                        {" "}
                        Eco-Friendly <br /> Hostels
                      </div>
                    </li>
                    <li
                      className={`flex flex-col text-sm font-manrope text-white text-center items-center font-normal justify-around h-[100px] hover:border-b-4 hover:border-b-primary-blue hover:text-gray-200 cursor-pointer w-16 md:w-auto 
                      ${
                        selectedCategory === tags[7]
                          ? "border-b-4 border-b-primary-blue text-white"
                          : "text-white"
                      }`}
                      onClick={() => handleCategoryClick(tags[7])}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Town-w.svg`}
                        alt='icon'
                        width={150}
                        height={75}
                        className='h-9 w-9'
                        loading='lazy'
                      />
                      <div className='hidden md:block'> New Town</div>
                    </li>
                    <li
                      className={`flex flex-col text-sm font-manrope text-white text-center items-center font-normal justify-around h-[100px] hover:border-b-4 hover:border-b-primary-blue hover:text-gray-200 cursor-pointer w-16 md:w-auto 
                      ${
                        selectedCategory === tags[8]
                          ? "border-b-4 border-b-primary-blue text-white"
                          : "text-white"
                      }`}
                      onClick={() => handleCategoryClick(tags[8])}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Adventure-w.svg`}
                        alt='icon'
                        width={150}
                        height={75}
                        className='h-9 w-9'
                        loading='lazy'
                      />
                      <div className='hidden md:block'>
                        {" "}
                        Adventure <br /> Seekers
                      </div>
                    </li>
                    <li
                      className={`flex flex-col text-sm font-manrope text-white text-center items-center font-normal justify-around h-[100px] hover:border-b-4 hover:border-b-primary-blue hover:text-gray-200 cursor-pointer w-16 md:w-auto 
                      ${
                        selectedCategory === tags[9]
                          ? "border-b-4 border-b-primary-blue text-white"
                          : "text-white"
                      }`}
                      onClick={() => handleCategoryClick(tags[9])}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Cultural-w.svg`}
                        alt='icon'
                        width={150}
                        height={75}
                        className='h-9 w-9'
                        loading='lazy'
                      />
                      <div className='hidden md:block'>
                        {" "}
                        Cultural & Local <br /> Experience
                      </div>
                    </li>
                  </ul>
                </div>
                <div className='h-[80px] w-full mt-6 overflow-x-auto z-10 block md:hidden'>
                  <ul className='flex items-center justify-evenly px-4 lg:px-0 w-max md:w-full  whitespace-nowrap space-x-2'>
                    <li
                      className={`flex flex-col text-sm font-manrope text-white text-center font-normal items-center justify-center  h-[60px]  hover:text-gray-200 cursor-pointer w-16 md:w-auto 
                      ${
                        selectedCategory === tags[0]
                          ? " border-primary-blue border-2 shadow-md shadow-primary-blue/40 rounded-full "
                          : "text-white"
                      }`}
                      onClick={() => handleCategoryClick(tags[0])}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Budget-w.svg`}
                        alt='icon'
                        width={150}
                        height={75}
                        className='h-9 w-9 '
                        loading='lazy'
                      />
                      <div className='hidden md:block'>
                        Budget <br /> Friendly
                      </div>
                    </li>
                    <li
                      className={`flex flex-col text-sm font-manrope text-white text-center items-center font-normal justify-around h-[60px] hover:text-gray-200 cursor-pointer w-16 md:w-auto 
                       ${
                         selectedCategory === tags[1]
                           ? " border-primary-blue border-2 shadow-md shadow-primary-blue/40 rounded-full "
                           : "text-white"
                       }`}
                      onClick={() => handleCategoryClick(tags[1])}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Luxury-w.svg`}
                        alt='icon'
                        width={150}
                        height={75}
                        className='h-9 w-9'
                        loading='lazy'
                      />
                      <div className='hidden md:block'>
                        {" "}
                        Luxury <br /> Hostels
                      </div>
                    </li>
                    <li
                      className={`flex flex-col text-sm font-manrope text-white text-center items-center font-normal justify-around h-[60px]  hover:text-gray-200 cursor-pointer w-16 md:w-auto 
                       ${
                         selectedCategory === tags[2]
                           ? " border-primary-blue border-2 shadow-md shadow-primary-blue/40 rounded-full "
                           : "text-white"
                       }`}
                      onClick={() => handleCategoryClick(tags[2])}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Nature-w.svg`}
                        alt='icon'
                        width={150}
                        height={75}
                        className='h-9 w-9'
                        loading='lazy'
                      />
                      <div className='hidden md:block'>
                        {" "}
                        Nature <br /> Connect
                      </div>
                    </li>
                    <li
                      className={`flex flex-col text-sm font-manrope text-white text-center items-center font-normal justify-around h-[60px] hover:text-gray-200 cursor-pointer w-16 md:w-auto 
                      ${
                        selectedCategory === tags[3]
                          ? " border-primary-blue border-2 shadow-md shadow-primary-blue/40 rounded-full "
                          : "text-white"
                      }`}
                      onClick={() => handleCategoryClick(tags[3])}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Party-w.svg`}
                        alt='icon'
                        width={150}
                        height={75}
                        className='h-9 w-9'
                        loading='lazy'
                      />
                      <div className='hidden md:block'>
                        {" "}
                        Party <br /> Hostels
                      </div>
                    </li>
                    <li
                      className={`flex flex-col text-sm font-manrope text-white text-center items-center font-normal justify-around h-[60px]  hover:text-gray-200 cursor-pointer w-16 md:w-auto 
                       ${
                         selectedCategory === tags[4]
                           ? " border-primary-blue border-2 shadow-md shadow-primary-blue/40 rounded-full "
                           : "text-white"
                       }`}
                      onClick={() => handleCategoryClick(tags[4])}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Nomad-w.svg`}
                        alt='icon'
                        width={150}
                        height={75}
                        className='h-9 w-9'
                        loading='lazy'
                      />
                      <div className='hidden md:block'>
                        {" "}
                        Digital
                        <br /> Nomad-Friendly
                      </div>
                    </li>
                    <li
                      className={`flex flex-col text-sm font-manrope text-white text-center items-center font-normal justify-around h-[60px]  hover:text-gray-200 cursor-pointer w-16 md:w-auto 
                       ${
                         selectedCategory === tags[5]
                           ? " border-primary-blue border-2 shadow-md shadow-primary-blue/40 rounded-full "
                           : "text-white"
                       }`}
                      onClick={() => handleCategoryClick(tags[5])}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Unique-w.svg`}
                        alt='icon'
                        width={150}
                        height={75}
                        className='h-9 w-9'
                        loading='lazy'
                      />
                      <div className='hidden md:block'> Unique</div>
                    </li>
                    <li
                      className={`flex flex-col text-sm font-manrope text-white text-center items-center font-normal justify-around h-[60px] hover:text-gray-200 cursor-pointer w-16 md:w-auto 
                       ${
                         selectedCategory === tags[6]
                           ? " border-primary-blue border-2 shadow-md shadow-primary-blue/40 rounded-full "
                           : "text-white"
                       }`}
                      onClick={() => handleCategoryClick(tags[6])}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Eco-w.svg`}
                        alt='icon'
                        width={150}
                        height={75}
                        className='h-9 w-9'
                        loading='lazy'
                      />
                      <div className='hidden md:block'>
                        {" "}
                        Eco-Friendly <br /> Hostels
                      </div>
                    </li>
                    <li
                      className={`flex flex-col text-sm font-manrope text-white text-center items-center font-normal justify-around h-[60px]  hover:text-gray-200 cursor-pointer w-16 md:w-auto 
                       ${
                         selectedCategory === tags[7]
                           ? " border-primary-blue border-2 shadow-md shadow-primary-blue/40 rounded-full "
                           : "text-white"
                       }`}
                      onClick={() => handleCategoryClick(tags[7])}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Town-w.svg`}
                        alt='icon'
                        width={150}
                        height={75}
                        className='h-9 w-9'
                        loading='lazy'
                      />
                      <div className='hidden md:block'> New Town</div>
                    </li>
                    <li
                      className={`flex flex-col text-sm font-manrope text-white text-center items-center font-normal justify-around h-[60px]  hover:text-gray-200 cursor-pointer w-16 md:w-auto 
                      ${
                        selectedCategory === tags[8]
                          ? " border-primary-blue border-2 shadow-md shadow-primary-blue/40 rounded-full "
                          : "text-white"
                      }`}
                      onClick={() => handleCategoryClick(tags[8])}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Adventure-w.svg`}
                        alt='icon'
                        width={150}
                        height={75}
                        className='h-9 w-9'
                        loading='lazy'
                      />
                      <div className='hidden md:block'>
                        {" "}
                        Adventure <br /> Seekers
                      </div>
                    </li>
                    <li
                      className={`flex flex-col text-sm font-manrope text-white text-center items-center font-normal justify-around h-[60px] hover:text-gray-200 cursor-pointer w-16 md:w-auto 
                    ${
                      selectedCategory === tags[9]
                        ? " border-primary-blue border-2 shadow-md shadow-primary-blue/40 rounded-full "
                        : "text-white"
                    }`}
                      onClick={() => handleCategoryClick(tags[9])}
                    >
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Cultural-w.svg`}
                        alt='icon'
                        width={150}
                        height={75}
                        className='h-9 w-9'
                        loading='lazy'
                      />
                      <div className='hidden md:block'>
                        {" "}
                        Cultural & Local <br /> Experience
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            {/* Search Results and Map */}
            {!showMap && (
              <div className='flex flex-col pt-5 pb-10 listing px-4 lg:px-10'>
                <div className='flex justify-between my-2 items-center '>
                  {/* <h1 className="text-xs md:text-[22px] text-manrope lg:px-0 font-normal text-black ">
                    Our listings are powered by advanced{" "}
                    <span className="text-primary-blue font-bold">
                      AI technology
                    </span>
                  </h1> */}{" "}
                  <motion.h1
                    className='text-xs md:text-[22px] text-manrope lg:px-0 font-normal text-black'
                    variants={container}
                    initial='hidden'
                    animate='visible'
                  >
                    {words.map((word, index) => (
                      <motion.span
                        key={index}
                        variants={child}
                        style={{ display: "inline-block", marginRight: "5px" }}
                      >
                        {word === "AI" ? (
                          <span className='text-primary-blue font-bold'>
                            {word}
                          </span>
                        ) : word === "technology" ? (
                          <>
                            <span className='text-primary-blue font-bold'>
                              {word}
                            </span>
                          </>
                        ) : (
                          word + " "
                        )}
                      </motion.span>
                    ))}
                  </motion.h1>
                  <div className='flex gap-x-2 '>
                    <button
                      className='border border-[#EEEEEE] text-black px-1 py-1.5 rounded-3xl flex items-center text-sm'
                      onClick={() => setShowSortPopup(true)}
                      aria-label='Sort By'
                    >
                      <SwapVertOutlinedIcon className='mr-0 md:mr-1 text-lg md:text-xl' />
                      <div className='hidden md:block'>Sort by</div>
                    </button>
                    <SortPopup
                      show={showSortPopup}
                      onClose={() => setShowSortPopup(false)}
                      setSort={setSort}
                      getData={() => getData(currentPage)}
                      close={setShowSortPopup}
                    />
                    {/* <button className="border border-[#EEEEEE] text-black px-4 py-1.5 rounded-3xl flex items-center text-sm">
                      <MdFilterList className="mr-1 text-lg" />
                      Filter
                    </button> */}
                    {/* <div className="relative hidden lg:flex border border-[#EEEEEE] rounded-full p-1.5 w-36">
                      <button className="w-1/2 text-center px-3 py-1 rounded-full transition-all duration-300 text-sm">
                        List
                      </button>
                      <button className="w-1/2 text-center px-3 py-1.5 bg-gray-200 rounded-full transition-all duration-300 text-sm">
                        Grid
                      </button>
                    </div> */}
                    {/* Toggle Buttons */}
                    <div className='relative hidden lg:flex border border-[#EEEEEE] rounded-full p-1.5 w-36'>
                      <button
                        className={`w-1/2 text-center px-3 py-1 rounded-full transition-all duration-300 text-sm ${
                          layout === "list" ? "bg-gray-200" : ""
                        }`}
                        onClick={() => setLayout("list")}
                      >
                        List
                      </button>
                      <button
                        className={`w-1/2 text-center px-3 py-1 rounded-full transition-all duration-300 text-sm ${
                          layout === "grid" ? "bg-gray-200" : ""
                        }`}
                        onClick={() => setLayout("grid")}
                      >
                        Grid
                      </button>
                    </div>
                  </div>
                </div>
                {/* Search Results */}
                {layout === "grid" ? (
                  <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'>
                    {/* Property Cards */}
                    {isLoading
                      ? // Show skeleton loading for all cards
                        Array.from({ length: 8 }).map((_, index) => (
                          <div key={index} className='my-4 w-full'>
                            <div className='border border-[#EEEEEE] rounded-2xl flex flex-col bg-white shadow-sm'>
                              {/* Skeleton for image */}
                              <div className='w-full h-[215px] bg-gray-200 animate-pulse rounded-t-2xl'></div>

                              {/* Skeleton for content */}
                              <div className='p-4 space-y-2'>
                                <div className='h-4 w-3/5 bg-slate-200 animate-pulse rounded' />
                                <div className='h-3 w-3/4 bg-slate-200 animate-pulse rounded mb-3' />
                                <div className='h-[50px] flex items-center'>
                                  <div className='h-6 w-1/2 bg-slate-200 animate-pulse rounded' />
                                </div>
                                <div className='flex justify-between items-center mt-4'>
                                  <div className='h-8 w-1/3 bg-slate-200 animate-pulse rounded mt-4' />
                                  <div className='h-8 w-1/2 bg-slate-200 animate-pulse rounded mt-4' />
                                </div>
                              </div>
                            </div>
                          </div>
                        ))
                      : property?.length > 0
                      ? property.map((item, index) => (
                          <div key={index} className='my-4 w-full'>
                            <div className='border border-[#EEEEEE] rounded-2xl flex flex-col bg-white shadow-sm transition-all duration-300 hover:shadow-'>
                              {/* Property Image */}
                              <div className='flex relative w-full'>
                                {item?.images?.[0]?.objectUrl ? (
                                  <Swiper
                                    pagination={{
                                      dynamicBullets: true,
                                      clickable: true,
                                    }}
                                    navigation={true}
                                    modules={[Pagination, Navigation, Autoplay]}
                                    slidesPerView={1}
                                    loop
                                    speed={1000}
                                    spaceBetween={0}
                                    className='mySwiper myCustomSwiper m-0 w-full'
                                  >
                                    {item?.images?.map((itemImg, imgIndex) => {
                                      const isLoaded =
                                        imagesLoaded[`${item._id}-${imgIndex}`];

                                      return (
                                        <SwiperSlide
                                          key={imgIndex}
                                          className='w-full bg-transparent'
                                        >
                                          <div className='relative'>
                                            {!isLoaded && (
                                              <div className='w-full h-[180px] sm:h-[215px] bg-gray-200 animate-pulse rounded-t-2xl absolute flex items-center justify-center'>
                                                <h1 className='text-white font-bold font-manrope text-4xl'>
                                                  Mixdorm
                                                </h1>
                                              </div>
                                            )}

                                            <Image
                                              src={itemImg?.objectUrl}
                                              className={`rounded-t-2xl object-cover w-full h-[180px] sm:h-[215px] ${
                                                !isLoaded
                                                  ? "opacity-0"
                                                  : "opacity-100"
                                              }`}
                                              alt='Property'
                                              width={320}
                                              height={300}
                                              loading={
                                                imgIndex === 0
                                                  ? "eager"
                                                  : "lazy"
                                              }
                                              priority={imgIndex === 0}
                                              onLoadingComplete={() =>
                                                handleImageLoad(
                                                  item._id,
                                                  imgIndex
                                                )
                                              }
                                            />

                                            <div className='absolute flex items-center justify-center px-3 py-1 text-xs font-semibold text-white bg-black rounded-4xl top-5 left-4 font-manrope'>
                                              {tagDisplayNames[
                                                item?.tag
                                              ]?.toUpperCase() ||
                                                formatTag(item?.tag) ||
                                                item?.type}
                                            </div>
                                            <button
                                              type='button'
                                              onClick={() =>
                                                HandleLike(
                                                  item?._id,
                                                  item?.liked
                                                )
                                              }
                                              className={`absolute flex items-center justify-center p-1 rounded-full w-5 h-5 top-5 right-5 font-manrope ${
                                                item?.liked
                                                  ? "text-red-600 bg-white"
                                                  : "text-black bg-white"
                                              } hover:bg-white-600 hover:text-red-600`}
                                            >
                                              <FaHeart size={16} />
                                            </button>
                                          </div>
                                        </SwiperSlide>
                                      );
                                    })}
                                  </Swiper>
                                ) : (
                                  <div className='w-full h-[180px] sm:h-[215px] bg-gray-200 animate-pulse rounded-t-2xl'>
                                    <div className='h-full w-full bg-gray-300 animate-pulse rounded-t-2xl flex items-center justify-center'>
                                      <h1 className='text-white font-bold font-manrope text-4xl'>
                                        Mixdorm
                                      </h1>
                                    </div>
                                  </div>
                                )}
                                {/* {item?.freeCancellationAvailable && (
                                  <Image
                                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/freecancel.svg`}
                                    alt="Free Cancellation"
                                    width={150}
                                    height={75}
                                    className="absolute left-[-1rem] top-4 z-10 object-contain"
                                    loading="lazy"
                                  />
                                )} */}
                              </div>

                              {/* Property Details */}

                              <div className='w-full p-4 flex flex-col relative'>
                                <div className='min-h-[80px]'>
                                  <h3
                                    onClick={() => {
                                      if (
                                        item?._id !==
                                        getItemLocalStorage("hostelId")
                                      ) {
                                        localStorage.removeItem(
                                          "selectedRoomsData"
                                        );
                                      }
                                      router.push(
                                        `/hostels-detail/${item?._id}`
                                      );
                                      setItemLocalStorage(
                                        "hostelId",
                                        item?._id
                                      );
                                    }}
                                    className='cursor-pointer text-base md:text-lg font-bold font-manrope mb-1 hover:text-primary-blue'
                                  >
                                    {item.name}
                                  </h3>
                                  <p
                                    className='text-[#888888] text-xs sm:text-sm flex items-center font-manrope cursor-pointer hover:text-primary-blue'
                                    onClick={() => handleShowMap(item)}
                                  >
                                    <PiMapPinLineFill className='text-primary-blue mr-1.5' />
                                    {`${item?.distanceFromCityCenter}`} KM away
                                    from city center
                                  </p>
                                </div>

                                {/* <div className="absolute -top-4 z-10">
                                  <span className="text-xs font-bold flex items-center bg-gray-100 px-3 py-1.5 rounded-3xl shadow-lg border">
                                    <FaStar className="text-yellow-400 mr-1" />
                                    {item?.starRating || "5"}{" "}
                                    <span className="text-[#888888] font-normal ml-1">
                                      (
                                      {item?.overallRating?.numberOfRatings ||
                                        0}{" "}
                                      Reviews)
                                    </span>
                                  </span>
                                </div> */}

                                <div className='absolute -top-4 z-10'>
                                  <WordTag />
                                </div>

                                <div className='flex flex-wrap gap-2 font-medium text-xs mt-2 items-center'>
                                  <span className='text-sm font-manrope font-medium text-gray'>
                                    Key Features:
                                  </span>
                                  {/* {freeFacilities(item).map((faci, id) => (
                                    <div
                                      key={id}
                                      className="relative group w-5 h-5 flex items-center justify-center"
                                    >
                                      {Faciicons[faci?.name] || (
                                        <RxMix className="text-lg" />
                                      )}
                                      <span className="absolute bottom-6 left-[-100%] transform text-xs text-white bg-black px-2 py-1 rounded opacity-0 group-hover:opacity-100 duration-200 whitespace-nowrap">
                                        {faci.name}
                                      </span>
                                    </div>
                                  ))} */}

                                  {freeFacilities(item).map((faci, id) => {
                                    const key = normalizeFaciName(faci?.name); // normalize to match keys
                                    const Icon = Faciicons[key] || RxMix;

                                    return (
                                      <div
                                        key={id}
                                        className='relative group w-5 h-5 flex items-center justify-center'
                                      >
                                        <Icon className='text-lg text-black' />
                                        <span className='absolute bottom-6 left-[-100%] transform text-xs text-white bg-black px-2 py-1 rounded opacity-0 group-hover:opacity-100 duration-200 whitespace-nowrap'>
                                          {faci.name}
                                        </span>
                                      </div>
                                    );
                                  })}
                                </div>

                                <div className='flex  justify-between items-center mt-4 gap-3'>
                                  {(item?.lowestAveragePricePerNight?.value ||
                                    item?.lowestPricePerNight?.value) && (
                                    <div className='text-black font-bold text-sm sm:text-base'>
                                      <span
                                        onClick={() => {
                                          if (
                                            item?._id !==
                                            getItemLocalStorage("hostelId")
                                          ) {
                                            localStorage.removeItem(
                                              "selectedRoomsData"
                                            );
                                          }
                                          router.push(
                                            `/hostels-detail/${item?._id}`
                                          );
                                          setItemLocalStorage(
                                            "hostelId",
                                            item?._id
                                          );
                                        }}
                                        className='hover:text-primary-blue cursor-pointer'
                                      >
                                        {" "}
                                        {getCurrencySymbol(
                                          item?.lowestAveragePricePerNight
                                            ?.currency ||
                                            item?.lowestPricePerNight?.currency
                                        )}{" "}
                                        {item?.lowestAveragePricePerNight
                                          ?.value ||
                                          item?.lowestPricePerNight?.value}{" "}
                                      </span>
                                      <span className='text-xs text-[#737373] font-normal'>
                                        / Night
                                      </span>
                                    </div>
                                  )}
                                  <Link href={"#"}>
                                    <div className='flex -space-x-1.5'>
                                      {profileImages.map((profileSrc, i) => (
                                        <div key={i} className='relative'>
                                          <Image
                                            src={profileSrc}
                                            width={34}
                                            height={34}
                                            alt='User'
                                            className='w-[34px] h-[34px] 2xl:w-[38px] 2xl:h-[38px] rounded-full border border-gray-600 backdrop-blur-sm blur-[1.5px]'
                                          />
                                          <span className='absolute -bottom-1 left-[18px] 2xl:left-[20px]'>
                                            <Image
                                              src={flagImages[i]}
                                              width={14}
                                              height={14}
                                              alt='Flag'
                                              className='w-[12px] 2xl:w-[14px] h-[12px] 2xl:h-[14px] rounded-full'
                                            />
                                          </span>
                                          {i === 3 && (
                                            <span className='absolute cursor-pointer top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-sm  text-white font-extrabold '>
                                              +4
                                            </span>
                                          )}
                                        </div>
                                      ))}
                                    </div>
                                  </Link>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))
                      : !loading &&
                        hasFetched &&
                        property.length === 0 && (
                          <p className='text-center text-white'>
                            No properties found.
                          </p>
                        )}
                  </div>
                ) : (
                  <div className='flex flex-col gap-4'>
                    {property?.length > 0
                      ? property.map((item, index) => (
                          <div key={index} className='my-4'>
                            <div className='flex bg-white rounded-xl border w-full h-[234px]'>
                              {/* Image Section */}
                              <div className='relative w-1/3 h-full'>
                                {item?.images?.[0]?.objectUrl ? (
                                  <Swiper
                                    pagination={{
                                      dynamicBullets: true,
                                      clickable: true,
                                    }}
                                    navigation={true}
                                    modules={[Pagination, Navigation, Autoplay]}
                                    slidesPerView={1}
                                    loop
                                    speed={1000}
                                    spaceBetween={0}
                                    className='mySwiper myCustomSwiper m-0 w-full'
                                  >
                                    {item?.images?.map((itemImg, imgIndex) => {
                                      const isLoaded =
                                        imagesLoaded[`${item._id}-${imgIndex}`];
                                      return (
                                        <SwiperSlide
                                          key={imgIndex}
                                          className='w-full bg-transparent'
                                        >
                                          {isLoaded && (
                                            <div className='w-full h-full bg-gray-200 animate-pulse rounded-t-2xl md:rounded-l-2xl md:rounded-tr-none'>
                                              <div className='h-full w-full bg-gray-300'></div>
                                            </div>
                                          )}

                                          <div className='relative tracking-normal'>
                                            <Image
                                              // src={`https://${itemImg.url}`}
                                              src={itemImg?.objectUrl}
                                              className={`rounded-tl-2xl rounded-bl-2xl  rounded-r-none  object-cover h-[234px] w-full  ${
                                                !isLoaded
                                                  ? "opacity-0"
                                                  : "opacity-100"
                                              }`}
                                              alt='Property'
                                              width={320}
                                              height={300}
                                              loading={
                                                index === 0 ? "eager" : "lazy"
                                              }
                                              priority={index === 0}
                                            />
                                            <div className='absolute flex items-center justify-center px-3 py-1 text-xs font-semibold text-white bg-black rounded-4xl top-5 left-5 font-manrope '>
                                              {tagDisplayNames[
                                                item?.tag
                                              ]?.toUpperCase() ||
                                                formatTag(item?.tag) ||
                                                item?.type}
                                            </div>
                                            <button
                                              type='button'
                                              onClick={() =>
                                                HandleLike(
                                                  item?._id,
                                                  item?.liked
                                                )
                                              }
                                              className={`absolute flex items-center justify-center p-1 rounded-full w-6 h-6 top-5 right-5 font-manrope ${
                                                item?.liked
                                                  ? "text-red-600 bg-red-600"
                                                  : "text-black bg-white"
                                              } bg-white
                                         hover:bg-white-600 hover:text-red-600`}
                                            >
                                              <FaHeart size={18} />
                                            </button>
                                          </div>
                                        </SwiperSlide>
                                      );
                                    })}
                                  </Swiper>
                                ) : (
                                  <div className='w-full h-[234px]  bg-gray-200 animate-pulse rounded-tl-2xl rounded-bl-2xl'>
                                    <div className='h-full w-full bg-gray-300 animate-pulse  flex items-center justify-center'>
                                      <h1 className='text-white font-bold font-manrope text-4xl'>
                                        Mixdorm
                                      </h1>
                                    </div>
                                  </div>
                                )}
                                {/* {item?.images?.[0]?.objectUrl ? (
                                  <Swiper
                                    pagination={{
                                      dynamicBullets: true,
                                      clickable: true,
                                    }}
                                    navigation={true}
                                    modules={[Pagination, Navigation, Autoplay]}
                                    slidesPerView={1}
                                    loop
                                    speed={1000}
                                    spaceBetween={0}
                                    className="mySwiper myCustomSwiper m-0 w-full"
                                  >
                                    {item?.images?.map((itemImg, imgIndex) => {
                                      const isLoaded =
                                        imagesLoaded[`${item._id}-${imgIndex}`];

                                      return (
                                        <SwiperSlide
                                          key={imgIndex}
                                          className="w-full bg-transparent"
                                        >
                                          <div className="relative">
                                            {!isLoaded && (
                                              <div className="w-full h-[234px]  bg-gray-200 animate-pulse rounded-tl-2xl absolute flex items-center justify-center rounded-bl-2xl">
                                                <h1 className="text-white font-bold font-manrope text-4xl">
                                                  Mixdorm
                                                </h1>
                                              </div>
                                            )}

                                            <Image
                                              src={itemImg?.objectUrl}
                                              className={`rounded-tl-2xl rounded-bl-2xl object-cover w-full h-[234px]  ${
                                                !isLoaded
                                                  ? "opacity-0"
                                                  : "opacity-100"
                                              }`}
                                              alt="Property"
                                              width={320}
                                              height={300}
                                              loading={
                                                imgIndex === 0
                                                  ? "eager"
                                                  : "lazy"
                                              }
                                              priority={imgIndex === 0}
                                              onLoadingComplete={() =>
                                                handleImageLoad(
                                                  item._id,
                                                  imgIndex
                                                )
                                              }
                                            />

                                            <div className="absolute flex items-center justify-center px-3 py-1 text-xs font-semibold text-white bg-black rounded-4xl top-5 left-4 font-manrope">
                                              {tagDisplayNames[
                                                item?.tag
                                              ]?.toUpperCase() ||
                                                formatTag(item?.tag) ||
                                                item?.type}
                                            </div>
                                            <button
                                              type="button"
                                              onClick={() =>
                                                HandleLike(
                                                  item?._id,
                                                  item?.liked
                                                )
                                              }
                                              className={`absolute flex items-center justify-center p-1 rounded-full w-5 h-5 top-5 right-5 font-manrope ${
                                                item?.liked 
                                                  ? "text-red-600 bg-white"
                                                  : "text-black bg-white"
                                              } hover:bg-white-600 hover:text-red-600`}
                                            >
                                              <FaHeart size={16} />
                                            </button>
                                          </div>
                                        </SwiperSlide>
                                      );
                                    })}
                                  </Swiper>
                                ) : (
                                  <div className="w-full h-[234px]  bg-gray-200 animate-pulse rounded-tl-2xl rounded-bl-2xl">
                                    <div className="h-full w-full bg-gray-300 animate-pulse  flex items-center justify-center">
                                      <h1 className="text-white font-bold font-manrope text-4xl">
                                        Mixdorm
                                      </h1>
                                    </div>
                                  </div>
                                )} */}
                                {/* {item?.freeCancellationAvailable && (
                                  <Image
                                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/freecancel.svg`}
                                    alt="Free Cancellation"
                                    width={150}
                                    height={75}
                                    className="absolute left-[-1rem] top-4 z-10 object-contain"
                                    loading="lazy"
                                  />
                                )} */}
                                <div className='absolute z-10 flex justify-between w-full top-20'></div>
                              </div>

                              {/* Info Section */}
                              <div className='w-2/3 pl-4 flex flex-col justify-around py-2'>
                                <div>
                                  <h3
                                    onClick={() => {
                                      if (
                                        item?._id !==
                                        getItemLocalStorage("hostelId")
                                      ) {
                                        localStorage.removeItem(
                                          "selectedRoomsData"
                                        );
                                      }
                                      router.push(
                                        `/hostels-detail/${item?._id}`
                                      );
                                      setItemLocalStorage(
                                        "hostelId",
                                        item?._id
                                      );
                                    }}
                                    className='cursor-pointer sm:text-xl text-xl font-bold mb-1 font-manrope hover:text-primary-blue'
                                  >
                                    {item.name}
                                  </h3>
                                  <p
                                    className='text-[#888888] mb-3 text-lg flex items-center font-manrope cursor-pointer hover:text-primary-blue'
                                    onClick={() => handleShowMap(item)}
                                  >
                                    <PiMapPinLineFill className='text-primary-blue text-lg mr-0.5' />
                                    {`${item?.distanceFromCityCenter}`} KM away
                                    from city center
                                  </p>{" "}
                                  {/* <div className="flex items-center mt-1">
                                    <span className="text-xs font-bold flex items-center bg-white px-3 py-1.5 rounded-3xl shadow-lg border">
                                      <FaStar className="text-yellow-400 mr-1" />
                                      {item?.starRating || "5"}{" "}
                                      <span className="text-[#888888] font-normal ml-1">
                                        (
                                        {item?.overallRating?.numberOfRatings ||
                                          0}{" "}
                                        Reviews)
                                      </span>
                                    </span>
                                  </div> */}
                                  <div className='flex items-center mt-1'>
                                    <WordTag />
                                  </div>
                                </div>

                                <div className='flex gap-x-2 font-medium text-xs  h-[50px] items-center'>
                                  <span className='text-[14px]  font-manrope font-medium cursor-pointer text-gray duration-300 ease-in-out'>
                                    Key Features:{" "}
                                  </span>
                                  {/* {freeFacilities(item).map((faci, id) => (
                                    // <span
                                    //   className="flex gap-x-2 items-center"
                                    //   key={id}
                                    // >
                                    //   {Faciicons[faci?.name] ? (
                                    //     <span style={{ fontSize: 8 }}>
                                    //       {Faciicons[faci?.name]}
                                    //     </span>
                                    //   ) : (
                                    //     <MdInsertEmoticon
                                    //       style={{ fontSize: 8 }}
                                    //       className="text-xxs"
                                    //     />
                                    //   )}
                                    //   {faci?.name}
                                    // </span>
                                    <div
                                      key={id}
                                      className="relative group w-5 h-5 flex items-center justify-center"
                                      // title={facility.name} // For simple tooltips
                                    >
                                      {Faciicons[faci?.name] || (
                                        <RxMix className="text-lg" />
                                      )}{" "}
                                     
                                      <span className="absolute bottom-6 left-[-100%] transform  text-xs text-white bg-black px-2 py-1 rounded opacity-0 min-w-[max-content] max-w-[max-content]  group-hover:opacity-100 duration-200">
                                        {faci.name}
                                      </span>
                                    </div>
                                  ))} */}
                                  {freeFacilities(item).map((faci, id) => {
                                    const key = normalizeFaciName(faci?.name); // normalize to match keys
                                    const Icon = Faciicons[key] || RxMix;

                                    return (
                                      <div
                                        key={id}
                                        className='relative group w-5 h-5 flex items-center justify-center'
                                      >
                                        <Icon className='text-lg text-black' />
                                        <span className='absolute bottom-6 left-[-100%] transform text-xs text-white bg-black px-2 py-1 rounded opacity-0 group-hover:opacity-100 duration-200 whitespace-nowrap'>
                                          {faci.name}
                                        </span>
                                      </div>
                                    );
                                  })}
                                </div>
                              </div>

                              {/* Price & Avatars */}
                              <div className='flex flex-col justify-around items-end mr-4'>
                                {(item?.lowestAveragePricePerNight?.value ||
                                  item?.lowestPricePerNight?.value) && (
                                  <div className='flex items-center mt-2 min-w-32 font-bold '>
                                    <span
                                      onClick={() => {
                                        if (
                                          item?._id !==
                                          getItemLocalStorage("hostelId")
                                        ) {
                                          localStorage.removeItem(
                                            "selectedRoomsData"
                                          );
                                        }
                                        router.push(
                                          `/hostels-detail/${item?._id}`
                                        );
                                        setItemLocalStorage(
                                          "hostelId",
                                          item?._id
                                        );
                                      }}
                                      className='hover:text-primary-blue cursor-pointer'
                                    >
                                      {getCurrencySymbol(
                                        item?.lowestAveragePricePerNight
                                          ?.currency ||
                                          item?.lowestPricePerNight?.currency
                                      )}{" "}
                                      {item?.lowestAveragePricePerNight
                                        ?.value ||
                                        item?.lowestPricePerNight?.value}{" "}
                                      /{" "}
                                    </span>
                                    <span className='text-sm text-[#737373] font-normal font-manrope'>
                                      Night
                                    </span>
                                  </div>
                                )}
                                <div className='flex mt-2 relative right-20'>
                                  <div className=''>
                                    <div className='relative mt-1'>
                                      <span className=''>
                                        <Image
                                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/usericon1.jpeg`}
                                          width={37}
                                          height={37}
                                          alt='Arlene McCoy'
                                          className='w-[34px] h-[34px] mr-4 rounded-full border border-gray-600 backdrop-blur-sm blur-[2px]'
                                        />
                                      </span>

                                      <span className='absolute -bottom-1 left-7 md:left-5'>
                                        <Image
                                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/flag14.png`}
                                          width={37}
                                          height={37}
                                          alt='Flag'
                                          className='w-[14px] h-[14px] rounded-full'
                                        />
                                      </span>
                                    </div>
                                  </div>

                                  <div className='absolute bottom-[0%] -right-[30%]'>
                                    <div className='relative mt-1'>
                                      <span className=''>
                                        <Image
                                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/usericon2.jpeg`}
                                          width={37}
                                          height={37}
                                          alt='Arlene McCoy'
                                          className='w-[34px] h-[34px] rounded-full border border-gray-600 backdrop-blur-sm blur-[2px]'
                                        />
                                      </span>

                                      <span className='absolute -bottom-1 left-5'>
                                        <Image
                                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/flag15.png`}
                                          width={37}
                                          height={37}
                                          alt='Flag'
                                          className='w-[14px] h-[14px] rounded-full'
                                        />
                                      </span>
                                    </div>
                                  </div>

                                  <div className='absolute bottom-[0%] -right-[92%]'>
                                    <div className='relative mt-1'>
                                      <span className=''>
                                        <Image
                                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/usericon3.jpeg`}
                                          width={37}
                                          height={37}
                                          alt='Arlene McCoy'
                                          className='w-[34px] h-[34px] rounded-full border border-gray-600 backdrop-blur-sm blur-[2px]'
                                        />
                                      </span>

                                      <span className='absolute -bottom-1 left-5 '>
                                        <Image
                                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/flag13.png`}
                                          width={37}
                                          height={37}
                                          alt='Flag'
                                          className='w-[14px] h-[14px] rounded-full'
                                        />
                                      </span>
                                    </div>
                                  </div>

                                  <div className='absolute bottom-[0%] -right-[150%]'>
                                    <div className='relative mt-1'>
                                      <span className=''>
                                        <Image
                                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/usericon4.jpeg`}
                                          width={37}
                                          height={37}
                                          alt='Arlene McCoy'
                                          className='w-[34px] h-[34px] rounded-full border border-gray-600 backdrop-blur-sm blur-[2px]'
                                        />
                                      </span>

                                      <span className='absolute -bottom-1 left-5  '>
                                        <Image
                                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/flag11.png`}
                                          width={37}
                                          height={37}
                                          alt='Flag'
                                          className='w-[14px] h-[14px] rounded-full'
                                        />
                                      </span>
                                      <span className='absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-sm font-extrabold text-white'>
                                        +4
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))
                      : !loading &&
                        hasFetched &&
                        property.length === 0 && (
                          <p className='text-center text-white'>
                            No properties found.
                          </p>
                        )}
                  </div>
                )}
                <button
                  onClick={() => {
                    handleMap();
                    handleShowMap();
                  }}
                  className='fixed bottom-8 bg-black text-white z-20 px-4 py-2 rounded-[50px] left-[50%] transform -translate-x-1/2 flex items-center gap-2'
                >
                  <span className='flex items-center gap-2 text-base'>
                    <TbMap2 className='text-xl' />
                    View Map
                  </span>
                </button>
                {totalPages > 1 && (
                  <SearchPaginaton
                    currentPage={currentPage}
                    totalPages={totalPages}
                    totalItems={totalData || 0}
                    itemsPerPage={itemsPerPage}
                    onPageChange={handlePageChange}
                    onItemsPerPageChange={handleItemsPerPageChange}
                  />
                )}
              </div>
            )}{" "}
          </div>
          <section
            className={` border-t border-gray-200 ${
              showMap ? "pt-6 pb-6" : "pt-8 lg:pb-28"
            } `}
          >
            <div className='w-full xxxl:container px-4 lg:px-10'>
              {!showMapList && showMap && (
                <div className='mid:w-[100%] w-full mt-10 self-start'>
                  <NewMapComponent
                    property={property}
                    setShowMapList={setShowMapList}
                    showMapList={showMapList}
                    showMap={showMap}
                    setShowMap={setShowMap}
                  />

                  <div ref={mapBottomRef} />
                </div>
              )}
            </div>
          </section>
        </main>
      </div>

      <Drawer open={openDrawer} onClose={toggleDrawer(false)}>
        <Box
          sx={{ width: 250 }}
          role='presentation'
          onClick={toggleDrawer(false)}
        >
          <Link href='/' rel='canonical' className='p-4 block'>
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mixdrom.svg`}
              width={110}
              height={28}
              alt='Mixdorm'
              title='Mixdorm'
              className='max-w-[110px] md:max-w-[155px] md:max-h-24 relative z-50 w-fit object-contain bg-blend-color-burn cursor-pointer hover:scale-95 duration-500 ease-in-out'
              loading='lazy'
            />
          </Link>
          <Divider />
          <div className='p-3'>
            <label htmlFor='' className='flex items-center gap-4 mb-4'>
              <Globe size={20} /> Select Currency
            </label>
            <button
              type='button'
              className='flex items-center gap-x-2 text-sm font-manrope font-bold cursor-pointer text-black duration-300 ease-in-out'
              // onClick={handleOpenCountryModal}
            >
              <Image
                src='https://flagcdn.com/w320/in.png'
                alt='Country Flag'
                width={20}
                height={20}
              />
              INR
              <ChevronDown size={18} />
            </button>
          </div>

          <div className='p-3'>
            <Link
              href='#'
              rel='canonical'
              className='block px-5 py-3 text-center font-manrope text-base font-bold bg-primary-blue cursor-pointer rounded-4xl text-black duration-300 ease-in-out'
            >
              List Your Hostel
            </Link>
          </div>
        </Box>
      </Drawer>
    </>
  );
};

export default SearchListing;
