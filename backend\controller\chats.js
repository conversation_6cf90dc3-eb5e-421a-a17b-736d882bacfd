import mongoose from 'mongoose';
import Message from '../models/chats.js';
import User from '../models/auth.js';
import ChatInvitation from '../models/chatInvitations.js';
import ChatRoom from '../models/chatRooms.js';
import Response from '../utills/response.js';
import { io } from '../utills/socketServer.js';
import { sendPushNotification } from '../utills/firebase.js';
import { BlockedUser } from '../models/blockedChat.js';

// Send a message
export const sendMessage = async (req, res) => {

    const { message, type, attachments, receiverId } = req.body;
    const sender = req.user._id;
    // await sendPushNotification()
    try {
        let room = await ChatRoom.findOne({
            $or: [
                { users: [sender, receiverId] },
                { users: [receiverId, sender] }
            ]
        });

        if (!room) {
            room = await ChatRoom.create({ users: [sender, receiverId] });
        }
        const blocked = await BlockedUser.findOne({
            $or: [
                { blocker: sender, blocked: receiverId },
                { blocker: receiverId, blocked: sender }
            ]
        });

        if (blocked) {
            return res.status(403).json({ error: 'Message cannot be sent. One of the users has blocked the other.' });
        }

        const newMessage = await Message.create({
            sender,
            receiver: receiverId,
            message,
            type,
            attachments,
            roomId: room._id
        });

        io.to(`user_${sender}`).emit('receiveMessage', newMessage);
        io.to(`user_${receiverId}`).emit('receiveMessage', newMessage);

             // Fetch updated unread count and last message
        const getChatInfo = async (userId, partnerId) => {
            const unreadCount = await Message.countDocuments({
                sender: partnerId,
                receiver: userId,
                isRead: false,
                isDeleted: false
            });

            const lastMsg = await Message.findOne({
                $or: [
                    { sender: userId, receiver: partnerId },
                    { sender: partnerId, receiver: userId }
                ],
                isDeleted: false
            }).sort({ createdAt: -1 }).lean();

            return {
                userId: partnerId,
                unreadCount,
                lastMessage: lastMsg?.message || '',
                lastMessageTime: lastMsg?.createdAt || null
            };
        };

        const senderInfo = await getChatInfo(sender, receiverId);
        const receiverInfo = await getChatInfo(receiverId, sender);


         // Emit chat updates for both users
        io.to(`user_${sender}`).emit('chatUpdate', senderInfo);
        io.to(`user_${receiverId}`).emit('chatUpdate', receiverInfo);

        res.status(200).json({ message: 'Message sent successfully', data: newMessage });
    } catch (error) {
        console.error('Error sending message:', error);
        res.status(500).json({ error: 'Failed to send message' });
    }
};

// Get chat history
export const getChatHistory = async (req, res) => {
    const sender = req.user._id;
    const receiver = req.params.receiverId;
    const { type, page = 1, limit = 10 } = req.query;

    const query = {
        $or: [
            { sender, receiver },
            { sender: receiver, receiver: sender }
        ],
        isDeleted: false
    };

    if (type) query.type = type;

    try {
        const skip = (page - 1) * limit;
         // Check if chat is blocked (either side)
        const isBlocked = await BlockedUser.countDocuments({
            $or: [
                { blocker: sender, blocked: receiver },
                { blocker: receiver, blocked: sender }
            ]
        });
        const messages = await Message.find(query)
            .populate('sender', 'name email lastSeen')
            .populate('receiver', 'name email lastSeen')
            .sort({ createdAt: 1 })
        // .skip(skip)
        // .limit(Number(limit));

        const totalMessages = await Message.countDocuments(query);
        const totalPages = Math.ceil(totalMessages / limit);

        res.status(200).json({
            isBlocked,
            messages,
            pagination: {
                currentPage: Number(page),
                totalPages,
                totalMessages,
                limit: Number(limit)
            }
        });
    } catch (error) {
        console.error('Error retrieving chat history:', error);
        res.status(500).json({ error: 'Failed to retrieve chat history' });
    }
};

// Mark a message as read
// Mark all messages as read between logged-in user and a receiver
export const markAsRead = async (req, res) => {
    const { receiverId } = req.params;
    const loggedInUserId = req.user._id;

    try {
        const result = await Message.updateMany(
            {
                sender: receiverId,
                receiver: loggedInUserId,
            },
            { $set: { isRead: true } }
        );

        res.status(200).json({
            message: 'Messages marked as read',
            modifiedCount: result.modifiedCount
        });
    } catch (error) {
        console.error('Error marking messages as read:', error);
        res.status(500).json({ error: 'Failed to mark messages as read' });
    }
};

// Soft delete a message
export const deleteAllMessages = async (req, res) => {
    const { messageId } = req.params;

    try {
        const message = await Message.findByIdAndUpdate(messageId, { isDeleted: true });
        if (!message) return res.status(404).json({ error: 'Message not found' });

        res.status(200).json({ message: 'Message deleted successfully' });
    } catch (error) {
        console.error('Error deleting message:', error);
        res.status(500).json({ error: 'Failed to delete message' });
    }
};


export const getChatUsers = async (req, res) => {
    try {
        const userId = new mongoose.Types.ObjectId(req.user.id);

        const chatUsers = await Message.aggregate([
            {
                $match: {
                    isDeleted:false,
                    $or: [
                        { sender: userId },
                        { receiver: userId }
                    ]
                }
            },
            {
                $sort: { createdAt: -1 }  // Sort before grouping!
            },
            {
                $group: {
                    _id: {
                        $cond: [
                            { $eq: ['$sender', userId] },
                            '$receiver',
                            '$sender'
                        ]
                    },
                    lastMessage: { $first: '$message' },
                    lastMessageTime: { $first: '$createdAt' },
                    unreadCount: {
                        $sum: {
                            $cond: [
                                { $and: [{ $eq: ['$receiver', userId] }, { $eq: ['$isRead', false] }] },
                                1,
                                0
                            ]
                        }
                    }
                }
            }
        ]);

        let result = await Promise.all(chatUsers.map(async ({ _id, unreadCount, lastMessage, lastMessageTime }) => {
            const user = await User.findById(_id).lean(); // lean for performance
            return {
                userId: _id,
                name: user?.name,
                unreadCount,
                lastMessage,
                lastMessageTime,
                lastSeen:user?.lastSeen
            };
        }));

        // Sort by lastMessageTime descending — optional now due to $sort in aggregation
        result.sort((a, b) => new Date(b.lastMessageTime) - new Date(a.lastMessageTime));

        res.status(200).json({ users: result });
    } catch (error) {
        console.error('Error fetching chat users:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
};


// Mark all user chats as deleted
export const markAllChatsAsDeleted = async (req, res) => {
    const userId = req.user._id;
    const receiver = req.body.id
    try {
        const result = await Message.updateMany(
             { sender: userId ,receiver},
            { $set: { isDeleted: true } }
        );

        res.status(200).json({
            message: 'All chats cleared',
            modifiedCount: result.modifiedCount,
        });
    } catch (error) {
        console.error('Error marking chats as deleted:', error);
        res.status(500).json({ error: 'Failed to mark chats as deleted' });
    }
};

// Send a chat invitation
export const sendInvitation = async (req, res) => {
    const { receiverId } = req.body;
    const senderId = req.user._id;

    try {
        const existing = await ChatInvitation.findOne({
            sender: senderId,
            receiver: receiverId,
            status: 'pending'
        });

        if (existing) {
            return res.status(400).json({ message: 'Invitation already sent' });
        }

        await ChatInvitation.create({ sender: senderId, receiver: receiverId });
        return Response.Created(res, null, 'Chat invitation sent successfully');
    } catch (error) {
        console.error('Error sending invitation:', error);
        res.status(500).json({ error: 'Failed to send invitation' });
    }
};

// Respond to invitation
export const respondToInvitation = async (req, res) => {
    const { invitationId } = req.params;
    const { status } = req.body;
    const userId = req.user._id;

    try {
        const invitation = await ChatInvitation.findById(invitationId);
        if (!invitation) return res.status(404).json({ message: 'Invitation not found' });

        if (!invitation.receiver.equals(userId)) {
            return res.status(403).json({ message: 'Unauthorized to respond' });
        }

        invitation.status = status;
        await invitation.save();

        if (status === 'accepted') {
            let room = await ChatRoom.findOne({
                participants: { $all: [invitation.sender, invitation.receiver] }
            });

            if (!room) {
                await ChatRoom.create({ participants: [invitation.sender, invitation.receiver] });
            }
        }

        return Response.OK(res, null, 'Chat invitation status updated');
    } catch (error) {
        console.error('Error responding to invitation:', error);
        res.status(500).json({ error: 'Failed to respond to invitation' });
    }
};

// Get received invitations
export const getReceivedInvitations = async (req, res) => {
    try {
        const userId = req.user.id;

        const invitations = await ChatInvitation.find({
            receiver: new mongoose.Types.ObjectId(userId),
            status: 'pending'
        }).populate('sender', 'name email lastSeen');

        return Response.OK(res, invitations, 'Received invitations retrieved successfully');
    } catch (error) {
        console.error('Error fetching received invitations:', error);
        res.status(500).json({
            message: 'An error occurred while fetching received invitations.',
            error: error.message
        });
    }
};
export const blockUser = async (req, res) => {
    const blockerId = req.user._id;
    const { blockedUserId } = req.body;

    if (blockerId.toString() === blockedUserId) {
        return res.status(400).json({ error: 'You cannot block yourself' });
    }

    try {
        const existing = await BlockedUser.findOne({ blocker: blockerId, blocked: blockedUserId });
        if (existing) {
            return res.status(400).json({ message: 'User already blocked' });
        }

        await BlockedUser.create({ blocker: blockerId, blocked: blockedUserId });

        // Optionally: mark chat messages as disabled or do any cleanup

        res.status(200).json({ message: 'User blocked successfully' });
    } catch (error) {
        console.error('Error blocking user:', error);
        res.status(500).json({ error: 'Failed to block user' });
    }
};
export const unblockUser = async (req, res) => {
  const blockerId = req.user._id;
  const { blockedUserId } = req.body;

  try {
    const result = await BlockedUser.findOneAndDelete({ blocker: blockerId, blocked: blockedUserId });
    if (!result) {
      return res.status(404).json({ message: 'User is not blocked' });
    }

    res.status(200).json({ message: 'User unblocked successfully' });
  } catch (error) {
    console.error('Error unblocking user:', error);
    res.status(500).json({ error: 'Failed to unblock user' });
  }
};
export const getBlockedUsers = async (req, res) => {
  const blockerId = req.user._id;

  try {
    // Find all blocked users by the logged-in user
    const blockedEntries = await BlockedUser.find({ blocker: blockerId }).populate('blocked', 'name email profilePicture');

    // Map to return only blocked user info (you can adjust the fields as needed)
    const blockedUsers = blockedEntries.map(entry => entry.blocked);

    res.status(200).json({ success: true, blockedUsers });
  } catch (error) {
    console.error('Error fetching blocked users:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch blocked users' });
  }
};
