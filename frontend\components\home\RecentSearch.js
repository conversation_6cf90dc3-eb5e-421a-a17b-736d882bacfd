/* eslint-disable react/jsx-key */
import React, { useEffect, useRef, useState } from "react";
import { getRecentSearchApi } from "@/services/webflowServices";
import { useNavbar } from "./navbarContext";
import dayjs from "dayjs";
import { useRouter } from "next/router";
import { CiLocationOn } from "react-icons/ci";
// import { CiCalendar, CiLocationOn } from "react-icons/ci";
// import { FiUserPlus } from "react-icons/fi";
// import { motion } from "framer-motion";

const RecentSearch = () => {
  const [recentSeatchData, setRecentSeatchData] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [loading, setLoading] = useState(true);
  const { token , role } = useNavbar();
  const isFirstRender = useRef(null);
  const router = useRouter();

  useEffect(() => {
    const fetchRecentSeatchData = async () => {
      setLoading(true);
      try {
        const response = await getRecentSearchApi();

        setRecentSeatchData(response?.data?.data?.recentSearches || []);
      } catch (error) {
        console.error("Error fetching Recent Search data:", error);
      } finally {
        setLoading(false);
      }
    };
    if (!isFirstRender.current && token && role === "user") {
      fetchRecentSeatchData();
    } else {
      isFirstRender.current = false;
    }
  }, [token]);

  const handleSlideClick = (item) => {
    const today = dayjs().startOf("day");
    const rawCheckIn = dayjs(item?.checkIn);
    const isPastDate = rawCheckIn.isBefore(today);

    const checkIn = isPastDate ? today : rawCheckIn;
    const checkOut = isPastDate
      ? today.add(1, "day")
      : dayjs(item?.checkOut) || checkIn.add(1, "day");

    const state = item?.search || "";
    const guest = item?.guest || 1;

    const bookingData = {
      state,
      checkIn: checkIn.format("YYYY-MM-DD"),
      checkOut: checkOut.format("YYYY-MM-DD"),
      guest,
    };

    localStorage.setItem("bookingdata", JSON.stringify(bookingData));
    router.push("/search");
  };

  return (
    <>
      <section className='w-full relative z-10'>
        <div className='container'>
          {recentSeatchData?.length > 0 && token && role === "user" && (
            <>
              <div className='flex items-center justify-center xs:gap-3 gap-2 sm:mt-[46px] mt-5 lg:mx-6'>
                {/* <motion.h2
                  initial={{ x: -100, opacity: 0 }}
                  whileInView={{ x: 0, opacity: 1 }}
                  transition={{ duration: 0.8, ease: "easeOut" }}
                  viewport={{ once: false }} */}
                <h2 className='font-[600] mb-2 leading-5 text-white font-manrope md:text-[18px] text-sm'>
                  Recent Searched{" "}
                  <span className='text-primary-blue font-mashiny xs:text-3xl text-2xl font-normal'>
                    Destinations
                  </span>
                </h2>
                {/* </motion.h2> */}
                {/* <motion.div
                  initial={{ x: -100, opacity: 0 }}
                  whileInView={{ x: 0, opacity: 1 }}
                  transition={{ duration: 0.8, ease: "easeOut" }}
                  viewport={{ once: false }} */}
                <div className='flex xs:gap-[13px] gap-1'>
                  {recentSeatchData?.map((item) => (
                    <button
                      type='button'
                      onClick={() => handleSlideClick(item)}
                    >
                      <div className='flex items-center border border-[#E4E6E8] sm:rounded-[9.75px] rounded-md sm:p-2 p-1 text-left bg-white bg-opacity-10 backdrop-blur-sm hover:border-primary-blue h-full'>
                        <div className='flex-1 tracking-normal space-y-1'>
                          <h6 className='text-[11px] xs:font-bold font-medium text-gray-200 flex items-center gap-x-1 text-xs'>
                            <CiLocationOn className='font-extrabold text-lg xs:block hidden' />
                            {item?.search
                              ? item.search.charAt(0).toUpperCase() +
                                item.search.slice(1)
                              : ""}
                          </h6>
                          {/* <h6 className="text-xxs md:text-xs font-semibold text-gray-200 sm:mb-1 flex items-center gap-x-1 whitespace-nowrap">
                          <CiCalendar className="font-extrabold text-lg"/>
                            {dayjs(item?.checkIn).format("DD MMMM")} - {dayjs(item?.checkOut).format("DD MMMM")}
                          </h6>
                          <h6 className="text-sm font-normal text-gray-200 sm:mb-1 capitalize flex items-center gap-x-1">
                          <FiUserPlus className="font-extrabold text-base ml-0.5"/> {item?.guest || 1}
                          </h6> */}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
                {/* </motion.div> */}
              </div>
            </>
          )}
        </div>
      </section>
    </>
  );
};

export default RecentSearch;
