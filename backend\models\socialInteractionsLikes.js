import mongoose from 'mongoose';

const socialLikeSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'users',
    required: true
  },
  socialIntrection: {
    type: mongoose.Schema.Types.ObjectId,
    required: true
  },
  isDeleted: {
    type: Boolean,
    default: false,
  },
  isLike:{
    type:Boolean,
    default:false
  }
}, { timestamps: true });

const socialIntrectionLikesModel = mongoose.model('socialInteractionsLikes', socialLikeSchema);
export default socialIntrectionLikesModel;
