import mongoose from 'mongoose';
const propertySchema = mongoose.Schema(
  {
    propertyOwner: {
      type: mongoose.Types.ObjectId,
      ref: "users",
    },
    type: {
      type: String,
    },
    number: {
      type: String,
    },
    name: {
      type: String,
      required: true,
    },
    address: {
      lineOne: {
        type: String,
        required: true,
      },
      lineTwo: {
        type: String
      },
      city: {
        type: String,
        required: false,
      },
      state: {
        type: String,
        required: false,
      },
      country: {
        type: String,
        required: false,
      },
      zipcode: {
        type: Number,
        required: false,
      },
      location: {
        lat: { type: Number },
        lon: { type: Number }
      },
      fax: { type: Number },
      street: { type: String }
    },
    website: { type: String },
    contact: {
      type: Number
    },
    businessContacts: {
      account_manager_name: {
        type: String,
      },
      main_contact_name: {
        type: String,
      },
      company_name: {
        type: String,
      },
      street: {
        type: String,
      },
      city: {
        type: String,
      },
      state: {
        type: String,
      },
      zip: {
        type: String, // use String to preserve leading zeroes
      },
      country: {
        type: String,
      },
      vat_id: {
        type: String,
      }
    },
    location: {
      type: { type: String },
      coordinates: { type: Array, default: [] },
    },
    photos: [{
      filename: {
        type: String
      },
      path: {
        type: String
      },
      objectURL: {
        type: String
      },
      url: {
        type: String
      },
      description:
      {
        type: String
      }
    }],
    aboutUs: {
      type: String
    },
    licenceProof: [{
      title: {
        type: String
      },
      url: {
        type: String
      }
    }],
    addressProof: [{
      title: {
        type: String
      },
      url: {
        type: String
      }
    }],
    isPropertyVerified: {
      type: Boolean,
      default: false,
    },
    verifiedDate: {
      type: Date
    },
    verifiedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'users'
    },
    contactUs: {
      email: { type: String },
      contactAddress: {
        lineOne: {
          type: String,
        },
        lineTwo: {
          type: String
        },
        city: {
          type: String,
        },
        state: {
          type: String,
        },
        country: {
          type: String,
        },
        zipcode: {
          type: Number,
          required: false,
        },
        latitude: { type: String },
        longitude: { type: String }
      },
      phoneNumber: { type: String },
      whatsApp: { type: String },
    },
    rating: { type: Object },
    isRejected: {
      type: Boolean,
      default: false,
    },
    rejectionDate: {
      type: Date
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    isActive: {
      type: Boolean,
      default: true
    },
    rules: {
      checkIn: {
        hour: { type: Number },
        min: { type: Number }
      },
      checkOut: {
        hour: { type: Number },
        min: { type: Number }
      },
      cancellation:{
        type:String
      },
      thingsToNote:{
        type:String
      },
      generalPolicy:{
        type:String
      },
     
    },
    cancellationPolicies: {
      type: Array,
    },
    generalPolicy: [
      {
        label: { type: String },
        description: { type: String }
      }
    ],
    // rules: [
    //   {
    //     key: { type: String },
    //     value: { type: String }
    //   }
    // ],
    amenities: [
      {
        lockers: {
          default: false,
          type: Boolean
        },
        hotWater: {
          default: false,
          type: Boolean
        },
        laundryService: {
          default: true,
          type: Boolean
        },
        freeWiFi: {
          default: true,
          type: Boolean
        },
        acceptCard: {
          default: true,
          type: Boolean
        },
        commonTelevision: {
          default: true,
          type: Boolean
        },
        waterDispenser: {
          default: true,
          type: Boolean
        },
        airConditioning: {
          default: true,
          type: Boolean
        },
        twentyFoutReception: {
          default: true,
          type: Boolean
        },
        hangoutArea: {
          default: true,
          type: Boolean
        },
        cafe: {
          default: true,
          type: Boolean
        },
        inHouseActivities: {
          default: true,
          type: Boolean
        },
        bedSideLamps: {
          default: true,
          type: Boolean
        },
        breakfast: {
          default: true,
          type: Boolean
        },
        storage: {
          default: true,
          type: Boolean
        },
        towelOnRent: {
          default: true,
          type: Boolean
        },
        linenInclude: {
          default: true,
          type: Boolean
        },
        upiPayment: {
          default: true,
          type: Boolean
        },
        shower: {
          default: true,
          type: Boolean
        },
        parking: {
          default: true,
          type: Boolean
        }
      }
    ],
    distance: {
      type: Object
    },
    isOTA: {
      type: Boolean,
      default: false
    },
    overallRating: {
      overall: {
        type: Number
      },
      numberOfRatings: {
        type: String
      },
    },
    freeCancellationAvailable: {
      type: Boolean
    },
    distanceFromCityCenter: {
      type: Number
    },
    starRating: {
      type: Number
    },
    freeCancellationAvailableUntil: {
      type: Date
    },
    freeCancellation: {
      type: Object
    },
    lowestPricePerNight: {
      type: Object
    },
    lowestPrivatePricePerNight: {
      type: Object
    },
    lowestAveragePrivatePricePerNight: {
      type: Object
    },
    lowestDormPricePerNight: {
      type: Object
    },
    lowestAveragePricePerNight: {
      type: Object
    },
    lowestAverageDormPricePerNight: {
      type: Object
    },
    checkin: {
      "from": {
        type: Number
      },
      "until": {
        type: Number
      }
    },
    freeFacilities: {
      type: Object
    },
    id: {
      type: Number
    },
    propertyId: {
      type: String
    },
    requiredFullPayment: {
      type: Boolean,
      default: false
    },
    googleReviews: [],
    hostelId: {
      type: Number
    },
    isAddByCloudbeds: {
      type: Boolean
    },
    cloudbedsId: {
      type: Number
    },
    isTopHostel: {
      type: Boolean,
      default: false
    },
    images: [
      {
        _id: {
          type: mongoose.Schema.Types.ObjectId,
          // default: () => new mongoose.Types.ObjectId()
        },
        s3Uri: { type: String },
        objectUrl: { type: String },
        s3ObjectUrl: { type: String },
      }
    ],
    otaId: { type: Number, unique: true, required: true },
    isPropertyLive: {
      type: Boolean,
      default: false
    }
  },
  {
    timestamps: true,
  },
);

// propertySchema.pre('save', async function (next) {
//   const property = this;

//   if (property.isModified('address')) {
//     try {
//       const fullAddress = `${property.address.lineOne}, ${property.address.city}, ${property.address.state}, ${property.address.country}, ${property.address.zipcode}`;

//       const response = await axios.get('https://maps.googleapis.com/maps/api/geocode/json', {
//         params: {
//           address: fullAddress,  
//           key: 'YOUR_GOOGLE_API_KEY' // Replace with your Google API Key
//         }
//       });

//       const location = response.data.results[0].geometry.location;
//       property.location.coordinates = [location.lng, location.lat];
//     } catch (error) {
//       console.error('Error fetching geolocation:', error);
//       next(error);
//     }
//   }

//   next();
// });

const property = mongoose.model("properties", propertySchema);

export default property;
