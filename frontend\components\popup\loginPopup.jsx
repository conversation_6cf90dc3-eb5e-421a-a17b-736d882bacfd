 
import React, { useEffect, useState } from "react";
import toast, { Toaster } from "react-hot-toast";
import { registerApi } from "../../services/webflowServices";
import { Button } from "@mui/material";
import { FaApple, FaFacebook } from "react-icons/fa";
import { GoogleOAuthProvider } from "@react-oauth/google";
import axios from "axios";
import AppleLogin from "react-apple-login";
import { FacebookProvider, LoginButton } from "react-facebook";
import SignInPopup from "./signinPopup";
import GoogleSocialLogin from "../socialLogin/googleSocialLogin";
import { BASE_URL } from "@/utils/api";
import { setItemLocalStorage, setToken } from "@/utils/browserSetting";
import { useRouter } from "next/router";
import { requestForFCMToken } from "@/utils/firebaseConfig";
import { saveFirebaseToken } from "@/services/ownerflowServices";
import { useNavbar } from "../home/<USER>";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import VisibilityOffOutlinedIcon from "@mui/icons-material/VisibilityOffOutlined";

const LoginPopup = ({ isOpen, onClose }) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);
  const [formData, setFormData] = useState({
    fullname: "",
    phoneNumber: "",
    email: "",
    password: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [isSignInOpen, setIsSignInOpen] = useState(isOpen);
  const [isSignUpOpen, setIsSignUpOpen] = useState(false);

  const router = useRouter();
  const { updateUserStatus, updateUserRole } = useNavbar();
  const [showPassword, setShowPassword] = useState(false);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSignup = async (e) => {
    e.preventDefault();

    if (
      !formData.email ||
      !formData.password ||
      !formData.fullname ||
      !formData.phoneNumber
    ) {
      setError("All fields are required.");
      return;
    }

    setLoading(true);
    setError("");

    try {
      const response = await registerApi({
        name: formData?.fullname,
        email: formData?.email,
        password: formData?.password,
        contact: formData?.phoneNumber,
      });

      if (response?.data?.status) {
        toast.success(response?.data?.message || "Registration successful!");
        setFormData({
          fullname: "",
          phoneNumber: "",
          email: "",
          password: "",
        });
        router.push("/verifyotp");
        onClose(); // Close LoginPopup
      } else {
        toast.error(
          response?.data?.message || "Something went wrong, please try again."
        );
      }
    } catch (error) {
      setError(
        error.response?.data?.message || "An error occurred, please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  const isDisabled =
    loading ||
    !formData.email ||
    !formData.password ||
    !formData.fullname ||
    !formData.phoneNumber;

  const handleFacebookSuccess = async (response) => {
    try {
      const { accessToken } = response.authResponse;

      // Manually fetch user profile from Facebook using the access token
      const profileRes = await axios.get(
        `https://graph.facebook.com/me?fields=name,email&access_token=${accessToken}`
      );

      const { name, email } = profileRes.data;

      const res = await axios.post(`${BASE_URL}/auth/social/login`, {
        token: accessToken,
        role: "user",
        email,
        name,
      });

      if (res.status === 201 && res.data.status) {
        toast.success("Login successful!");

        setItemLocalStorage("name", name);
        setItemLocalStorage("role", res?.data?.data?.user?.role);
        setToken(res?.data?.data?.token);
        setItemLocalStorage("id", res?.data?.data?.user?._id);
        setItemLocalStorage("email", res?.data?.data?.user?.email);
        setItemLocalStorage("contact", res?.data?.data?.user?.contact);
        updateUserStatus(res?.data?.data?.token);
        updateUserRole(res?.data?.data?.user?.role);
        onClose();
        const fcmToken = await requestForFCMToken();
        if (fcmToken) {
          setItemLocalStorage("FCT", fcmToken);
          await saveFirebaseToken({
            token: fcmToken,
            userId: res?.data?.data?.user?._id,
          });
        }
      } else {
        toast.error("Social login failed: " + res.data.message);
      }
    } catch (error) {
      toast.error(error.response?.data.message || error.message);
    }
  };

  const handleFacebookFailure = (error) => {
    toast.error("Facebook login failed: " + error.message);
  };

  const handleAppleSuccess = async (response) => {
    try {
      const { authorization, user } = response;

      const { id_token } = authorization;

      const name = user?.name
        ? `${user.name.firstName} ${user.name.lastName}`
        : null;
      const email = user?.email || null;

      const res = await axios.post(`${BASE_URL}/auth/social/login`, {
        token: id_token,
        role: "user",
        provider: "apple",
        name: name,
        email: email,
      });

      if (res.status === 201 && res.data.status) {
        toast.success("Login successful!");

        setItemLocalStorage(
          "name",
          name ||
            `${res.data.data.user.name.first} ${res.data.data.user.name.last}`
        );
        setItemLocalStorage("role", res?.data?.data?.user?.role);
        setToken(res?.data?.data?.token);
        setItemLocalStorage("id", res?.data?.data?.user?._id);
        setItemLocalStorage("email", res?.data?.data?.user?.email);
        setItemLocalStorage("contact", res?.data?.data?.user?.contact);
        updateUserStatus(res?.data?.data?.token);
        updateUserRole(res?.data?.data?.user?.role);

        const fcmToken = await requestForFCMToken();
        if (fcmToken) {
          setItemLocalStorage("FCT", fcmToken);
          await saveFirebaseToken({
            token: fcmToken,
            userId: res?.data?.data?.user?._id,
          });
        }

        router.push("/");
      } else {
        toast.error("Social login failed: " + res.data.message);
      }
    } catch (error) {
      toast.error(error.response?.data.message || error.message);
    }
  };

  const handleAppleFailure = (response) => {
    toast.error("Apple login failed: " + response.error);
  };

  useEffect(() => {
    setIsSignInOpen(isOpen);
  }, [isOpen]);

  const openSignInForm = () => {
    setIsSignInOpen(true);
    setIsSignUpOpen(false);
  };

  const openSignUpForm = () => {
    setIsSignInOpen(false);
    setIsSignUpOpen(true);
  };

  const closePopup = () => {
    setIsSignInOpen(false);
    setIsSignUpOpen(false);
    onClose(); // Ensure the parent is informed about closing
  };

  if (!isOpen) return null;

  return (
    <>
      <Toaster position='top-center' />
      {isSignInOpen ? (
        <SignInPopup
          isOpen={isSignInOpen}
          onClose={closePopup}
          openSignUpForm={openSignUpForm}
        />
      ) : (
        isSignUpOpen && (
          <div className='fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-50'>
            <div className='bg-white rounded-2xl w-[95%] max-w-2xl p-5 font-manrope'>
              <div className='flex justify-end'>
                <button
                  onClick={closePopup}
                  className='text-black hover:text-gray-600 transition duration-150'
                >
                  ✕
                </button>
              </div>
              <div className='flex flex-col justify-center items-center'>
                <span className='text-[#40E0D0] flex text-2xl font-extrabold mb-2'>
                  Mix<p className='text-black text-2xl font-extrabold '>Dorm</p>
                </span>
              </div>
              <form onSubmit={handleSignup} className='mx-8'>
                <h2 className='text-xl font-bold text-gray-800 mb-4 text-center'>
                  👋 Sign Up your Account
                </h2>

                <div className='mb-5'>
                  {/* <label
                htmlFor="fullname"
                className="text-base block font-semibold text-gray-700"
              >
                Full Name
              </label> */}
                  <input
                    type='text'
                    id='fullname'
                    name='fullname'
                    value={formData.fullname}
                    onChange={handleChange}
                    className='w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500'
                    placeholder='Full Name'
                  />
                </div>
                <div className='mb-5'>
                  {/* <label
                htmlFor="phoneNumber"
                className="text-base block font-semibold text-gray-700"
              >
                Phone Number
              </label> */}
                  <input
                    type='text'
                    id='phoneNumber'
                    name='phoneNumber'
                    value={formData.phoneNumber}
                    onChange={handleChange}
                    className='w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500'
                    placeholder='Phone Number'
                  />
                </div>
                <div className='mb-5'>
                  {/* <label
                htmlFor="email"
                className="text-base block font-semibold text-gray-700"
              >
                Email
              </label> */}
                  <input
                    type='email'
                    id='email'
                    name='email'
                    value={formData.email}
                    onChange={handleChange}
                    className='w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500'
                    placeholder='Email'
                  />
                </div>
                <div className='relative mb-8'>
                  {/* <label
                htmlFor="password"
                className="text-base block font-semibold text-gray-700"
              >
                Password
              </label> */}
                  <input
                    type={showPassword ? "text" : "password"}
                    id='password'
                    name='password'
                    value={formData.password}
                    onChange={handleChange}
                    className='w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500'
                    placeholder='Password'
                  />
                  {isClient && (
                    <button
                      type='button'
                      className='absolute right-4 top-[15px] text-gray-300 transform'
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <VisibilityOutlinedIcon />
                      ) : (
                        <VisibilityOffOutlinedIcon />
                      )}
                    </button>
                  )}
                </div>
                <button
                  type='submit'
                  className={`w-full font-semibold text-white py-4 rounded-full transition duration-200 ${
                    isDisabled
                      ? "bg-gray-400 cursor-not-allowed"
                      : "bg-[#40E0D0] hover:bg-[#38c7b7]"
                  }`}
                  disabled={isDisabled}
                >
                  {loading ? "Signing up..." : "Sign Up"}
                </button>
                {error && (
                  <div className='mb-4 text-center text-red-600'>{error}</div>
                )}
              </form>
              <div className='flex items-center justify-center mx-8 space-x-4 my-8'>
                <span className='flex-1 border-t border-gray-200'></span>
                <span className='text-gray-600'>Or</span>
                <span className='flex-1 border-t border-gray-200'></span>
              </div>
              <div className='flex items-center gap-x-4 justify-center mb-6'>
                <FacebookProvider
                  appId={process.env.NEXT_PUBLIC_FACEBOOK_APP_ID}
                >
                  <LoginButton
                    scope='public_profile,email'
                    onSuccess={handleFacebookSuccess}
                    onError={handleFacebookFailure}
                    className='min-w-0 p-0 text-3xl'
                  >
                    <FaFacebook className="text-[#0866ff]" />
                  </LoginButton>
                </FacebookProvider>
                <GoogleOAuthProvider
                  clientId={process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID}
                >
                  <GoogleSocialLogin
                  
                    role='user'
                    closeLoginPopup={() => closePopup()}
                  />
                </GoogleOAuthProvider>

                <AppleLogin
                  clientId={process.env.NEXT_PUBLIC_APPLE_CLIENT_ID}
                  redirectURI={process.env.NEXT_PUBLIC_APPLE_REDIRECT_URI}
                  responseType='code id_token'
                  scope='name email'
                  render={(renderProps) => (
                    <Button
                      className='min-w-0 p-0 text-3xl text-black mb-1'
                      onClick={renderProps.onClick}
                    >
                      <FaApple />
                    </Button>
                  )}
                  onSuccess={handleAppleSuccess}
                  onError={handleAppleFailure}
                />
              </div>
              <h4 className='text-basec text-center text-sm text-gray-400'>
                Already have an account?
                <a
                  className='text-primary-blue font-medium ml-1 cursor-pointer '
                  onClick={openSignInForm}
                >
                  Sign In
                </a>
              </h4>
            </div>
          </div>
        )
      )}

      {/* <SignInPopup
        isOpen={isSignInOpen}
        onClose={() => setIsSignInOpen(false)}
        openSignUpForm={() => setIsSignInOpen(false)}
        closeLoginPopup={onClose}
      /> */}
    </>
  );
};

export default LoginPopup;
