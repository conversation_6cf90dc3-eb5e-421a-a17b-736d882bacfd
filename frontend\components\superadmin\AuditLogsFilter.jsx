 
 





// "use client";
// import Image from "next/image";
// import { useState } from "react";
// import { FaRedo, FaChevronDown, FaChevronUp } from "react-icons/fa";
// import { LuCalendarDays } from "react-icons/lu";

// const Dropdown = ({ label, options, width = "w-48" }) => {
//   const [isOpen, setIsOpen] = useState(false);
//   const [selectedOption, setSelectedOption] = useState(label);

//   const toggleDropdown = () => setIsOpen(!isOpen);

//   const handleOptionClick = (option) => {
//     setSelectedOption(option);
//     setIsOpen(false);
//   };

//   return (
//     <div className={`relative ${width}`}>
//       <button
//         onClick={toggleDropdown}
//         className="bg-transparent focus:outline-none sm:text-sm w-full flex items-center justify-center"
//       >
//         {selectedOption}
//         {isOpen ? (
//           <FaChevronUp className="ml-2 text-black" size={12} />
//         ) : (
//           <FaChevronDown className="ml-2 text-black" size={12} />
//         )}
//       </button>
//       {isOpen && (
//         <ul className="absolute z-10 bg-white border rounded-lg shadow-md mt-2 w-full">
//           {options.map((option, index) => (
//             <li
//               key={index}
//               className="p-2 hover:bg-gray-100 cursor-pointer"
//               onClick={() => handleOptionClick(option)}
//             >
//               {option}
//             </li>
//           ))}
//         </ul>
//       )}
//     </div>
//   );
// };

// const FeedbackFilter = () => {
//   return (
//     <div className="flex flex-wrap">
//       <button className="flex items-center justify-center border px-3 h-[50px] rounded-l-xl bg-white">
//         <Image
//           src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/filter.png`}
//           width={22}
//           height={25}
//           loading="lazy"
//           alt=""
//         />
//       </button>
//       <div className=" bg-white flex items-center border px-4 text-sm font-poppins h-[50px] justify-center text-black font-semibold">
//         Filter By
//       </div>
//       <div className="flex items-center justify-center text-black font-semibold space-x-6 border px-2 text-sm font-poppins h-[50px]  bg-white">
//         <Dropdown label="Select User" options={["1", "2"]} width="w-48" />
//       </div>
//       <div className="flex text-black font-semibold items-center justify-center space-x-6 border px-2 text-sm font-poppins h-[50px] bg-white">
//         <Dropdown
//           label="Select Enitity"
//           options={["1", "2", "3"]}
//           width="w-36"
//         />
//       </div>
//       <div className="flex text-black font-semibold items-center justify-center gap-x-4 space-x-6 border px-8  text-sm font-poppins h-[50px] bg-white">
//         Start Date <LuCalendarDays className="text-xl mb-0.5"/>
//       </div>
//       <div className="flex text-black font-semibold items-center justify-center gap-x-4 space-x-6 border px-8  text-sm font-poppins h-[50px] bg-white">
//         End Date <LuCalendarDays className="text-xl mb-0.5"/>
//       </div>
//       <button className="text-red-600 bg-white text-sm font-semibold font-poppins h-[50px] border px-10  rounded-r-xl flex items-center justify-center ">
//         <FaRedo className="mr-2 mb-0.5" size={16} />
//         Reset Filter
//       </button>
//     </div>
//   );
// };

// export default FeedbackFilter;










"use client";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { FaRedo, FaChevronDown, FaChevronUp } from "react-icons/fa";
import { LuCalendarDays } from "react-icons/lu";

const Dropdown = ({ label, options, width = "w-48" }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState(label);
  const dropdownRef = useRef(null);

  const toggleDropdown = () => setIsOpen(!isOpen);

  const handleOptionClick = (option) => {
    setSelectedOption(option);
    setIsOpen(false);
  };
    // Function to handle click outside
    useEffect(() => {
      function handleClickOutside(event) {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
          setIsOpen(false);
        }
      }
      document.addEventListener("mousedown", handleClickOutside);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }, []);

  return (
    <div className={`relative ${width}` } ref={dropdownRef}>
      <button
        onClick={toggleDropdown}
        className="bg-transparent focus:outline-none sm:text-sm w-full flex items-center justify-center "
      >
        {selectedOption}
        {isOpen ? (
          <FaChevronUp className="ml-2 text-black dark:text-[#B6B6B6]" size={12} />
        ) : (
          <FaChevronDown className="ml-2 text-black dark:text-[#B6B6B6]" size={12} />
        )}
      </button>
      {isOpen && (
        <ul className="absolute z-10 bg-white border rounded-lg shadow-md mt-2 w-full dark:bg-[#171616] dark:border-none">
          {options.map((option, index) => (
            <li
              key={index}
              className="p-2 hover:bg-gray-100 cursor-pointer text-sm font-semibold dark:hover:bg-[#393939b7]"
              onClick={() => handleOptionClick(option)}
            >
              {option}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

const AuditFilter = () => {
  return (
    <div className="flex flex-wrap">
      <button className="flex h-[50px] items-center justify-center border px-3 rounded-l-xl dark:border-0.5">
        <Image
          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/filter.png`}
          width={22}
          height={25}
          loading="lazy"
          alt=""
        />
      </button>
      <div className="text-sm font-poppins bg-white flex items-center border h-[50px] justify-center text-black font-semibold px-6 dark:bg-black dark:text-[#B6B6B6] dark:border-0.5 dark:border-gray-200">
        Filter By
      </div>
      <div className="flex text-sm font-poppins text-black font-semibold items-center justify-center space-x-4 border px-2 h-[50px] bg-white dark:bg-black dark:text-[#B6B6B6] dark:border-0.5 dark:border-gray-200">
        <Dropdown label="Select User" options={["X", "Y"]} width="w-40" />
      </div>
      <div className="flex text-sm font-poppins items-center justify-center text-black font-semibold space-x-4 border px-2 h-[50px] bg-white dark:bg-black dark:text-[#B6B6B6] dark:border-0.5 dark:border-gray-200">
        <Dropdown
          label="Select Entity"
          options={["X", "Y"]}
          width="w-40"
          
            
        />
      </div>
      <div className="flex text-black font-semibold items-center justify-center gap-x-4 space-x-6 border px-8  text-sm font-poppins h-[50px] bg-white dark:bg-black dark:text-[#B6B6B6] dark:border-0.5 dark:border-gray-200">
         Start Date <LuCalendarDays className="text-xl mb-0.5"/>
       </div>
      <div className="flex text-black font-semibold items-center justify-center gap-x-4 space-x-6 border px-8  text-sm font-poppins h-[50px] bg-white dark:bg-black dark:text-[#B6B6B6] dark:border-0.5 dark:border-gray-200">
         End Date <LuCalendarDays className="text-xl mb-0.5"/>
      </div>
      <button className="text-red-600 bg-white text-sm font-poppins font-semibold border px-6 h-[50px] rounded-r-xl flex items-center justify-center dark:bg-black  dark:border-0.5 dark:border-gray-200">
        <FaRedo className="mr-1" size={16} />
        Reset Filter
      </button>
    </div>
  );
};

export default AuditFilter;
