import React from "react";

const Button = ({ text, type, className, disabled }) => {
  return (
    <button
      className={`w-full px-4 py-2 text-xs font-normal text-white rounded-full relative flex justify-center items-center h-9 bg-sky-blue-650 ${className} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      type={type}
      disabled={disabled}
    >
      {text}
    </button>
  );
};

export default Button;
