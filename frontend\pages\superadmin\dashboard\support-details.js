import Link from "next/link";
import React from "react";

const SupportDetails = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616] h-screen">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        Support Details
      </h2>
      <div className="bg-white border rounded-xl mt-5 h-auto dark:bg-black dark:border-none">
        <div className="p-8 flex flex-col gap-y-6">
          <div>
            <div className="flex items-center justify-between">
              <h1 className="text-lg font-poppins font-bold dark:text-[#B6B6B6]">
                Support Information
              </h1>
              {/* <div className="flex items-center  justify-center">
                <button className="text-white text-sm font-poppins py-1 md:py-2 lg:py-2 px-6 lg:px-8   rounded bg-sky-blue-650">
                  Edit
                </button>
              </div> */}
            </div>
            <div className="flex flex-col gap-x-20 my-6">
              <div className="flex whitespace-nowrap w-[60%] flex-col">
                <strong className="text-black/70  text-sm font-poppins font-semibold dark:text-[#757575]">
                  Question
                </strong>
                <p className="mt-2 text-black/55 text-sm  font-poppins font-medium dark:text-[#B6B6B6]">
                  What is MIXDORM?
                </p>
              </div>
              <div className="flex w-[100%] mt-8  flex-col">
                <strong className="text-black/70 text-sm font-poppins font-semibold dark:text-[#757575]">
                  Answer
                </strong>
                <p className="mt-2 text-black/55 text-sm font-poppins font-medium dark:text-[#B6B6B6]">
                  Amet minim mollit non deserunt ullamco est sit aliqua dolor do
                  amet sint.
                </p>
              </div>
            </div>
          </div>
          <div className="flex items-start justify-start">
            <Link
              href={"/superadmin/dashboard/support"}
              className="text-white py-2 w-32 rounded bg-sky-blue-650 flex items-center justify-center"
            >
              Cancel
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupportDetails;
