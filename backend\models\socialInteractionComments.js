import mongoose from 'mongoose';

const socialCommentsSchema = new mongoose.Schema({
    user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'users',
    required: true
  },
  socialIntrection: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'rides',
    required: true
  },
  rating: {
    type: Number,
    min: 0,
    max: 5,
    required: false
  },
  comment: {
    type: String,
    required: false
  },
  images: [
    {
      url: {
        type: String,
        required: false
      },
      title: {
        type: String
      }
    }
  ],

  // 👇 Support replies by referencing parent comment
  parentComment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'socialInteractionsComments',
    default: null
  },

  isActive: {
    type: Boolean,
    default: true
  },
  isDeleted: {
    type: Boolean,
    default: false
  }

}, {timestamps : true});

const socialIntrectionCommentsModel = mongoose.model('socialInteractionsComments', socialCommentsSchema,"socialInteractionsComments");
export default socialIntrectionCommentsModel;