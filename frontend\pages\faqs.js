import React, { useState } from "react";
import dynamic from "next/dynamic";
import Head from "next/head";
const HeroNavbar = dynamic(() => import("@/components/footer/heroNavBar"), {
  ssr: false,
});

const FAQ = () => {
  const faqContent = {
    "General Questions": [
      {
        title: " What is MixDorm?",
        details: `MixDorm is a vibrant community-focused hostel network designed for
young travelers and backpackers. We offer modern accommodations, unique features like
Mix Ride and Mix Creators, and a range of activities to enhance your travel experience.`,
      },
      {
        title: "How can I book a stay at MixDorm?",
        details: `You can book a stay through our website or mobile
app. Simply select your destination, choose your dates, and complete the reservation
process. You'll receive a confirmation email with your booking details.`,
      },
      {
        title: " What is included in the room rate?",
        details: `The room rate includes accommodation in our
well-maintained dorms or private rooms. Additional amenities such as breakfast, tours, and
activities may be available for an extra charge.`,
      },
    ],
    "Booking and Payment": [
      {
        title: "What payment methods do you accept?",
        details: `We accept major credit cards, debit cards, and
other online payment methods. Payment is processed securely through our payment
gateway.`,
      },
      {
        title: "Is my booking guaranteed? ",
        details: `Yes, once you receive a confirmation email, your booking is
guaranteed. We use secure payment methods to ensure your reservation is safe`,
      },
      {
        title: "Can I modify or cancel my booking?",
        details: `Yes, you can modify or cancel your booking based
on our Cancellation Policy. Please visit our website or contact our customer support team for
assistance with changes.`,
      },
    ],
    "Check-In and Check-Out": [
      {
        title: "What time is check-in and check-out?",
        details: `Check-in: 12 noon <br/>
Check-out: 10 AM Early check-in and late check-out are subject to availability and
property discretion. Feel free to use our common areas if you arrive early or need to
leave later`,
      },
      {
        title: "Do I need to provide identification at check-in ?",
        details: `Yes, valid photo identification is
required at check-in. Acceptable IDs include Passport, Aadhar Card, Driving License, and
Voter ID card.`,
      },
    ],
    "Amenities and Services": [
      {
        title: "Are there any amenities provided?",
        details: `Yes, our hostels offer various amenities including
free Wi-Fi, clean bedding, and access to common areas. Some properties may also provide
breakfast, laundry facilities, and tour services for an additional charge.`,
      },
      {
        title: "Can I bring pets?",
        details: `
No, outside pets are not allowed in our hostels to ensure the comfort
and safety of all guests.`,
      },
      {
        title: " Is there a kitchen available for guest use?",
        details: `
Yes, most of our hostels have a communal
kitchen where guests can prepare their meals. Please check the specific property details for availability.`,
      },
    ],
    "Safety and Security": [
      {
        title: "How is the safety and security of guests ensured? ",
        details: `We prioritize your safety with secure access to rooms, CCTV surveillance in common areas, and regular staff presence.Additionally, we ask guests to use the lockers provided for personal belongings`,
      },
      {
        title: "What should I do if I lose something during my stay?",
        details: `If you lose an item, please
contact our customer support team as soon as possible. We will make every effort to locate
and return lost items. Unclaimed items are typically kept for a limited period before being
donated or disposed of.`,
      },
    ],
    "Local Experience": [
      {
        title: "What is Mix Ride? ",
        details: `Mix Ride is our platform that allows travelers to share rides with
fellow guests, making transportation more affordable and social. Check our website or app
for details on available rides.`,
      },
      {
        title: "How can I participate in Mix Creators?",
        details: `Mix Creators is a platform for social media
influencers to promote MixDorm properties. If you're interested, please contact us with your
social media profile details, and we'll provide further information on collaboration
opportunities.`,
      },
    ],
    "Contact Us": [
      {
        title: "How can I get in touch with MixDorm?",
        details: ` For any questions or support, you can reach us at: <br/>
<strong>Email:</strong> <EMAIL>`,
      },
      {
        title: "Where can I find more information about MixDorm?",
        details: `Visit our website or follow us on
social media for the latest updates, travel tips, and special offers: <br/>
<strong>Instagram:</strong> @mixdorms
<strong>Facebook:</strong> MixDorm
<strong>Twitter:</strong> @mixdorm
`,
      },
    ],
  };
  const styles = [
    {
      bg: "bg-[#FFE9E0]",
      text: "text-[#FF9066]",
      padding: "p-4",
      rounded: "rounded-xl",
      gap: "gap-4",
      border: "border border-[#FF9066]",
    },
    {
      bg: "bg-[#D9F9F6]",
      text: "text-[#40E0D0]",
      padding: "p-4",
      rounded: "rounded-lg",
      gap: "gap-4",
      border: "border border-[#40E0D0]",
    },
    {
      bg: "bg-[#FFE3FF]",
      text: "text-[#FF72AD]",
      padding: "p-4",
      rounded: "rounded-lg",
      gap: "gap-4",
      border: "border border-[#FF72AD]",
    },
  ];

  // State to manage the selected FAQ section
  const [selectedFAQ, setSelectedFAQ] = useState("General Questions");
  const [expandedLine, setExpandedLine] = useState(null);

  const handleLineClick = (index) => {
    setExpandedLine(expandedLine === index ? null : index);
  };

  return (
    <>
      <Head>
        <title>MixDorm FAQs | Booking & Stay Questions Answered</title>
        <meta
          name='description'
          content='Need Help with Your Mixdorm Stay? Browse FAQs on Booking, Check-Ins, Room Rates, and More for a Smooth Hostel Experience.'
        />
      </Head>
      <div className='flex flex-col h-auto'>
        <HeroNavbar />
        <div className='bg-white pb-4 md:pb-[10rem] font-manrope md:pt-12 pt-8'>
          <div className='container'>
            <h2 className='font-bold  text-black font-manrope md:mb-8 mb-5 md:text-[35px] sm:text-xl text-xl'>
              FAQs
            </h2>
            <div className='md:flex xl:gap-10 lg:gap-6 gap-4'>
              <div className='md:w-[30%] flex flex-col xl:w-[25%] sm:mb-5 xs:mb-4 mb-3 md:mb-0'>
                <ul className='md:block flex gap-2 w-full overflow-x-auto pb-2 hover-scrollbar'>
                  {Object.keys(faqContent).map((item) => (
                    <li
                      key={item}
                      onClick={() => setSelectedFAQ(item)}
                      className={`cursor-pointer text-nowrap md:mb-2 last:mb-0 sm:text-base text-sm p-3 border border-primary-blue rounded-md bg-primary-blue/20 text-black ${
                        selectedFAQ === item &&
                        "font-semibold !bg-primary-blue !border-black"
                      }`}
                    >
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
              <div className='md:w-[70%]'>
                <div>
                  <p className='font-bold sm:mb-5 mb-3 sm:text-xl text-lg'>
                    {selectedFAQ}
                  </p>
                  {faqContent[selectedFAQ].map((line, index) => {
                    const style = styles[index % styles.length];
                    return (
                      <div
                        key={index}
                        className={`w-full rounded-xl md:mb-5 sm:mb-3 mb-2 last:mb-0 ${style.bg} ${style.text} sm:${style.padding} py-2.5 px-3 ${style.rounded} ${style.gap} ${style.border} mb-4`}
                      >
                        <ul className='list-disc pl-4'>
                          <li className=''>
                            <h3
                              onClick={() => handleLineClick(index)}
                              className='cursor-pointer sm:text-xl text-base font-semibold flex items-center'
                            >
                              {line.title}
                              {/* <span className="ml-auto">
                          {expandedLine ? <FaAngleUp /> : <FaAngleDown />}
                        </span> */}
                            </h3>
                          </li>
                        </ul>
                        {expandedLine === index && (
                          <p
                            className='text-gray-500 sm:text-base text-sm sm:pt-4 pt-3'
                            dangerouslySetInnerHTML={{ __html: line.details }} // Render HTML content
                          />
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default FAQ;
