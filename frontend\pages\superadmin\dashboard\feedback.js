"use client";
import React from "react";
import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight,
} from "react-icons/md";
import { FiEye } from "react-icons/fi";
import { IoArrowRedoOutline } from "react-icons/io5";
import { IoIosStar } from "react-icons/io";
import FeedbackFilter from "@/components/superadmin/FeedbackFilter";
import Link from "next/link";

const Feedback = () => {
  const feedbackData = [
    {
      id: 1,
      name: "<PERSON>",
      hostelname: `88 Backpackers`,
      feedback:
        "Bed sheet dirty and bed not arranges on time",
      time: "03:50",
      date: "04/02/24",
      rates: 4.5,
    },
    {
      id: 2,
      name: "<PERSON>",
      hostelname: `88 Backpackers`,
      feedback:
        "Bed sheet dirty and bed not arranges on time",
      time: "03:50",
      date: "04/02/24",
      rates: 4.5,
    },
    {
      id: 3,
      name: "<PERSON>",
      hostelname: `88 Backpackers`,
      feedback:
        "Bed sheet dirty and bed not arranges on time",
      time: "03:50",
      date: "04/02/24",
      rates: 4.5,
    },
    {
      id: 4,
      name: "<PERSON>",
      hostelname: `88 Backpackers`,
      feedback:
        "Bed sheet dirty and bed not arranges on time",
      time: "03:50",
      date: "04/02/24",
      rates: 4.5,
    },
    {
      id: 5,
      name: "Stuart Coats",
      hostelname: `88 Backpackers`,
      feedback:
        "Bed sheet dirty and bed not arranges on time",
      time: "03:50",
      date: "04/02/24",
      rates: 4.5,
    },
    {
      id: 6,
      name: "Stuart Coats",
      hostelname: `88 Backpackers`,
      feedback:
        "Bed sheet dirty and bed not arranges on time",
      time: "03:50",
      date: "04/02/24",
      rates: 4.5,
    },
    {
      id: 7,
      name: "Stuart Coats",
      hostelname: `88 Backpackers`,
      feedback:
        "Bed sheet dirty and bed not arranges on time",
      time: "03:50",
      date: "04/02/24",
      rates: 4.5,
    },
    {
      id: 8,
      name: "Stuart Coats",
      hostelname: `88 Backpackers`,
      feedback:
        "Bed sheet dirty and bed not arranges on time",
      time: "03:50",
      date: "04/02/24",
      rates: 4.5,
    },
    {
      id: 9,
      name: "Stuart Coats",
      hostelname: `88 Backpackers`,
      feedback:
        "Bed sheet dirty and bed not arranges on time",
      time: "03:50",
      date: "04/02/24",
      rates: 4.5,
    },
  ];

  const splitMessage = (feedback) => {
    const words = feedback.split(" ");
    const midpoint = Math.ceil(words.length / 2);
    const firstPart = words.slice(0, midpoint).join(" ");
    const secondPart = words.slice(midpoint).join(" ");
    return { firstPart, secondPart };
  };

  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">Feedback</h2>
      <div className="mt-5">
        <FeedbackFilter />
      </div>
      <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border dark:border-none">
        <table className="min-w-full divide-y bg-white rounded-xl divide-gray-200 dark:bg-black">
          <thead>
            <tr>
              <th className="px-5 pl-10 py-6 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                NAME
              </th>
              <th className="pr-4 pl-8 lg:pl-6 py-6 bg-white text-left text-sm font-poppins  font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                HOSTEL NAME
              </th>
              <th className="pr-4 pl-8 py-6 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                Feedback
              </th>
              <th className="pr-4 pl-8 lg:pl-5 py-6 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                TIME
              </th>
              <th className="pr-2 pl-10 lg:pl-7 py-6 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                DATE
              </th>
              <th className="pr-4 pl-8  py-6 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                RATES
              </th>
              <th className="py-6 pl-14 lg:pl-10 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                ACTION
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 border-x border-y dark:border-x-0 text-black/70 dark:text-[#757575]">
            {feedbackData.map((feedbackItem) => {
              const { firstPart, secondPart } = splitMessage(
                feedbackItem.feedback
              );
              return (
                <tr key={feedbackItem.id}>
                  <td className="whitespace-nowrap px-5 text-sm font-poppins font-medium text-gray-500 dark:text-[#757575]">
                    {feedbackItem.name}
                  </td>
                  <td className="whitespace-nowrap px-8 lg:px-5 text-sm font-poppins font-medium text-gray-500 dark:text-[#757575]">
                    {feedbackItem.hostelname}
                  </td>
                  <td className="whitespace-nowrap pt-3 break-words max-w-md text-sm ">
                    <div>
                      
                      <span className="font-poppins font-medium text-gray-500 dark:text-[#757575]">{firstPart}</span>
                      <br />
                      <span className="font-poppins font-medium text-gray-500 dark:text-[#757575]">{secondPart}</span>
                    </div>
                  </td>
                  <td className="whitespace-nowrap px-8 lg:px-5 text-sm font-poppins font-medium text-gray-500 dark:text-[#757575]">{feedbackItem.time}</td>
                  <td className="whitespace-nowrap px-8 lg:px-5 text-sm font-poppins font-medium text-gray-500 dark:text-[#757575]">{feedbackItem.date}</td>
                  <td className="px-5">
                    {typeof feedbackItem.rates === "number"  ? (
                      <div className="flex items-center justify-center text-orange-500 w-20 rounded py-2 bg-orange-100  text-sm dark:bg-[#e28f5f66]">
                        <IoIosStar className="text-orange-500 mb-0.5" />
                        <span className="ml-2 text-sm">{feedbackItem.rates}</span>
                      </div>
                    ) : (
                      <button className="text-orange-500 bg-orange-200  font-medium rounded  text-sm">
                        {feedbackItem.rates}
                      </button>
                    )}
                  </td>
                  <td className="py-8 lg:py-5 px-8 lg:px-5 flex">
                    <button className="border px-4 py-2 rounded-l-lg text-black/75 dark:text-[#757575]">
                      <IoArrowRedoOutline />
                    </button>
                    <Link href={"/superadmin/dashboard/feedback-details"} className="border px-4 py-2 rounded-r-lg text-black/75 hover:text-blue-700 dark:hover:text-blue-700 dark:text-[#757575]">
                      <FiEye />
                    </Link>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
      <div className="flex justify-between items-center mt-5">
                   <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
                   <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
                     <a
                       href="#"
                       className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                     >
                       <span className="sr-only">Next Page</span>
                       <MdOutlineKeyboardArrowLeft />
                     </a>
           
                     <a
                       href="#"
                       className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                     >
                       <span className="sr-only">Next Page</span>
                       <MdOutlineKeyboardArrowRight />
                     </a>
                   </div>
                 </div>
    </div>
  );
};

export default Feedback;
