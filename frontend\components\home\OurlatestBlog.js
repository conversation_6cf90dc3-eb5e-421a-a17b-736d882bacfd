/* eslint-disable react/no-unescaped-entities */
import { getBlog<PERSON>pi, likeUnlike<PERSON>roperty<PERSON><PERSON> } from "@/services/webflowServices";
import { getItemLocalStorage } from "@/utils/browserSetting";
import {  ArrowLeft, ArrowRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React, { useEffect, useRef, useState } from "react";
import { useNavbar } from "./navbarContext";
import toast from "react-hot-toast";
import dynamic from "next/dynamic";
import { FaHeart } from "react-icons/fa6";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation } from "swiper/modules";
import "swiper/css/navigation";
// import { motion } from "framer-motion";

const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

const OurlatestBlog = () => {
  const [blogData, setBlogData] = useState([]);
  const [loading, setLoading] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [currentPage, setCurrentPage] = useState(1);
  // eslint-disable-next-line no-unused-vars
  const [totalPages, setTotalPages] = useState(1);
  const limit = 4;
  const isFirstRender = useRef(null);
  const { token } = useNavbar();
  const [isLoading, setIsLoading] = useState(true);
  const [imageFailedMap, setImageFailedMap] = useState({});

  useEffect(() => {
    const fetchBlogData = async () => {
      try {
        const response = await getBlogApi(currentPage, limit);
        setBlogData(response?.data?.data?.blogs || []);
        setTotalPages(response?.data?.data?.pagination?.totalPages || 1);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching stay data:", error);
      }
    };
    if (!isFirstRender.current) {
      fetchBlogData();
    } else {
      isFirstRender.current = false;
    }
  }, [currentPage, token]);

  const HandleLike = async (liked, id) => {
    if (getItemLocalStorage("token")) {
      setLoading(true);
      try {
        const payload = {
          isLike: !liked,
        };
        const response = await likeUnlikePropertyApi(id, payload);
        console.log("Like/Unlike response:", response);
        
        // Update the UI directly with the response data
        if (response?.data?.data || response?.data) {
          setBlogData(prevData => {
            return prevData.map(blog => {
              if (blog._id === id) {
                return {
                  ...blog,
                  ...(response?.data?.data || response?.data),
                  liked: response?.data?.data?.liked || response?.data?.liked // Use the opposite of current liked state
                };
              }
              return blog;
            });
          });
        }
      } catch (error) {
        console.error("Error updating like status:", error);
        toast.error("Failed to update like status");
      } finally {
        setLoading(false);
      }
    } else {
      toast.error("Please login first!!");
    }
  };

  return (
    <div
    // style={{
    //   backgroundImage: `url(${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/bg-insider.png)`,
    //   backgroundSize: "cover", // Ensures the image covers the entire div
    // }}
    >
      <Loader open={loading} />
      <section className="w-full relative md:pb-16 pb-10 lg:px-6 py-8">
        <div className="absolute -top-20 -left-8 w-40 h-40 bg-cyan-400 rounded-full blur-2xl opacity-30"></div>
        <div className="absolute bottom-20 right-4 w-40 h-40 bg-pink-300 rounded-full blur-2xl opacity-30"></div>
        <div className="container relative">
          <div className="flex items-center justify-between w-full my-0">
            {/* <motion.h2
              initial={{ x: -100, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              viewport={{ once: false }} */}
            <h2
              className=" text-black  font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl sm:mb-8 mb-3">
              The
              <span className="text-primary-blue"> Hostel </span> Insider
            </h2>
            {/* </motion.h2> */}
            {/* <motion.div
              initial={{ x: 100, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              viewport={{ once: false }} */}
            <div
              className="text-center sm:hidden block">
              <Link
                href="/blog"
                className="text-sm font-semibold text-black bg-primary-blue rounded-4xl py-2 px-5 hover:bg-sky-blue-750"
                prefetch={false}
              >
                See All
              </Link>
            </div>
            {/* </motion.div> */}
            <div className={`gap-2 ${blogData?.length > 4 ? 'xl:flex' : 'xl:hidden'
              } ${blogData?.length > 3.5 ? 'lg:flex' : 'lg:hidden'} ${blogData?.length > 2.5 ? 'md:flex' : 'md:hidden'} ${blogData?.length > 2 ? 'sm:flex' : 'sm:hidden'} ${blogData?.length > 1.5 ? 'hidden' : 'hidden'}`}
            >
              <div className="slider-button-prev cursor-pointer custom-nav"><ArrowLeft size={18} /></div>
              <div className="slider-button-next cursor-pointer custom-nav"><ArrowRight size={18} /></div>
            </div>
          </div>
          <p
            className="hidden sm:block mt-2 text-base font-medium text-[#737373] font-manrope mb-3 w-[80%]"
          >
           Welcome to The <Link href={"/blog"} className="text-primary-blue"> Hostel Insider guide </Link> to the world of hostels, budget travel, and unforgettable backpacker adventures. From expert tips on finding the <Link href={"/"} className="text-primary-blue"> best hostel bookings </Link> to discovering hidden gems, local experiences, and solo travel hacks, we've got everything you need to travel smart, stay social, and save big. Whether you're a seasoned solo backpacker, planning your first dormitory stay, or just love affordable travel, this is where your journey begins.

          </p>
          {/* <motion.div
            initial={{ y: 80, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: false }}
          > */}
          <div>
            <Swiper
              modules={[Autoplay, Navigation]}
              // autoplay={{ delay: 1500, pauseOnMouseEnter: true }}
              slidesPerView={18}
              navigation={{
                prevEl: '.slider-button-prev',
                nextEl: '.slider-button-next',
              }}
              loop
              speed={1000}
              spaceBetween={14}
              className="mySwiper myCustomSwiper py-4 discover-event-slider-home overflow-hidden"
              breakpoints={{
                0: {
                  slidesPerView: 1.4,
                },
                480: {
                  slidesPerView: 1.4,
                },
                640: {
                  slidesPerView: 2,
                },
                768: {
                  slidesPerView: 2.2,
                },
                1024: { slidesPerView: 3.5 },
                1280: { slidesPerView: 4 },
              }}
            >

              {isLoading
              ? Array.from({ length: 4 }).map((_, index) => (
                  <SwiperSlide key={`skeleton-${index}`}>
                    <div className="border-4 border-slate-100 overflow-hidden font-manrope w-full bg-white my-8 md:my-2 shadow-md animate-pulse">
                      <div className="relative w-full rounded-t-[32px] bg-gray-200 h-[150px] xs:h-[212px] flex items-center justify-center">
                        <h1 className="text-white font-bold font-manrope">MixDorm</h1>
                      </div>

                      <div className="absolute top-3 left-3 bg-gray-300 rounded-full w-16 h-6"></div>
                      <div className="absolute top-3 right-3 bg-gray-300 rounded-full w-6 h-6"></div>

                      <div className="py-4 px-2">
                        <div className="flex items-center gap-x-3 mb-2">
                          <div className="flex items-center gap-x-1">
                            <div className="bg-gray-300 rounded-full w-4 h-4"></div>
                            <div className="bg-gray-300 rounded-md w-20 h-4"></div>
                          </div>
                          <div className="flex items-center gap-x-1">
                            <div className="bg-gray-300 rounded-full w-4 h-4"></div>
                            <div className="bg-gray-300 rounded-md w-12 h-4"></div>
                          </div>
                        </div>

                        <div className="bg-gray-300 h-4 rounded-md mb-2 w-[90%]"></div>
                        <div className="bg-gray-300 h-4 rounded-md w-[70%] mb-4"></div>

                        <div className="flex items-center justify-between mt-6">
                          <div className="flex items-center gap-x-2">
                            <div className="bg-gray-300 rounded-full w-8 h-8"></div>
                            <div className="bg-gray-300 w-20 h-4 rounded-md"></div>
                          </div>
                          <div className="bg-gray-300 w-24 h-8 rounded-2xl"></div>
                        </div>
                      </div>
                    </div>
                  </SwiperSlide>
                ))
              :
                blogData?.map((item) => (
                  <SwiperSlide key={item.id}>
                    <div
                      className={`border-4 border-slate-105 overflow-hidden font-manrope w-full  bg-white my-8 md:my-2 shadow-md  lg:hover:shadow-xl lg:hover:scale-105 lg:hover:-translate-y-1 lg:transition-all lg:duration-300`}
                      key={item?._id}
                    >
                      <div className="relative w-full rounded-t-[32px]">
                        <Link
                          // data-title={item?.title}
                          href={`/blog-details?id=${item?._id}`}
                          className=" before:!top-[81px] before:!left-[140px] sm:before:!left-[150px] lg:before:!left-[125px]  xl:before:!left-[150px] 2xl:before:!left-[150px]"
                          prefetch={false}
                        >
                          
                          {imageFailedMap[item._id] ? (
                            <div className="w-full xs:h-[212px] h-[150px] bg-slate-200 animate-pulse flex justify-center items-center">
                              <h1 className="text-white font-bold font-manrope">MixDorm</h1>
                            </div>
                          ) : (
                            <Image
                              src={item?.images?.[0]}
                              alt={item?.title}
                              width={418}
                              height={259}
                              quality={90}
                              sizes="(max-width: 480px) 100vw, (max-width: 768px) 100vw, 418px"
                              className="w-full xs:h-[212px] h-[150px] object-cover bg-slate-200"
                              onError={() =>
                                setImageFailedMap(prev => ({
                                  ...prev,
                                  [item._id]: true,
                                }))
                              }
                            />
                          )}
                        </Link>
                        <div className="absolute flex items-center justify-center px-3 py-1 text-xs font-medium text-black bg-white rounded-4xl xs:top-5 xs:left-5 top-3 left-3 font-manrope ">
                          {item?.categoryId?.title}
                        </div>
                        <div
                          className={`absolute flex items-center justify-center p-1 rounded-full xs:w-7 xs:h-7 w-6 h-6 xs:top-5 xs:right-5 top-3 right-3 font-manrope ${item?.liked
                            ? "bg-white text-red-600"
                            : "text-black bg-white"
                            } hover:bg-white hover:text-red-600`}
                        >
                          <FaHeart
                            size={18}
                            onClick={() => HandleLike(item?.liked, item?._id)}
                          />
                        </div>
                      </div>
                      <div className={` py-4 px-2`}>
                        {/* <div className="flex items-center text-white gap-x-3">
                          <div className="flex items-center justify-start gap-x-1">
                            {" "}
                            <div className="text-[#8E8E8E]">
                              <CalendarDays size={16} />
                            </div>
                            <div className="text-black text-xxs md:text-xs lg:text-[9px] 2xl:text-xs font-manrope font-bold pt-0.5">
                              {" "}
                              {new Date(item?.createdAt).toLocaleDateString(
                                "en-US",
                                {
                                  year: "numeric",
                                  month: "short",
                                  day: "numeric",
                                }
                              )}
                            </div>
                          </div>
                          <div className="flex items-center justify-start gap-x-1">
                            {" "}
                            <div className="text-[#8E8E8E]">
                              <Clock size={16} />
                            </div>
                            <div className="text-black text-xxs md:text-xs lg:text-[9px] 2xl:text-xs font-manrope font-bold pt-0.5">
                              6 min
                            </div>
                          </div>
                          <div className="flex items-center justify-start gap-x-1">
                            {" "}
                            <div className="text-[#8E8E8E]">
                              <MessageCircleMore size={16} />
                            </div>
                            <div className="text-black text-xxs md:text-xs lg:text-[9px] 2xl:text-xs font-manrope font-bold pt-0.5">
                              38 comments
                            </div>
                          </div>
                        </div> */}
                        <div
                          className=" before:!top-[80px] before:!left-[140px] sm:before:!left-[160px] md:before:!left-[150px] lg:before:!left-[125px] xl:before:!left-[145px] 2xl:before:!left-[150px]"
                        // data-title={item?.description}
                        >
                          <Link
                            href={`/blog-details?id=${item?._id}`}
                            className="my-[11px] xs:text-base text-sm font-bold text-black line-clamp-2 hover:text-primary-blue font-manrope "
                            prefetch={false}
                          >
                            {item?.description}
                          </Link>
                        </div>
                        <div className="flex items-center justify-center mt-6">
                          {/* <div className="flex items-center justify-start text-sm gap-x-[10px]">
                            {" "}
                            <div className="w-8 h-8 rounded-full ">
                              <Image
                                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/avatar.png`}
                                width={32}
                                height={32}
                                alt="user"
                                title="user"
                                className={`w-8 h-8 rounded-full ${ isLoading ? "bg-slate-200 animate-pulse" : "bg-slate-200" }`}
                              />
                            </div>
                            <div className="w-fit text-xxs 2xl:text-[14px] text-black font-bold whitespace-nowrap">
                              Jimmy Dave
                            </div>
                          </div> */}
                          <Link
                            href={`/blog-details?id=${item?._id}`}
                            prefetch={false}
                            className=""
                          >
                            <button
                              type="button"
                              className="md:inline-block   text-xxs 2xl:text-xs font-bold text-white bg-black font-manrope px-2 xs:px-3 2xl:px-5 xs:py-3 py-2 flex justify-center items-center rounded-9xl hover:bg-primary-blue hover:text-black"
                            >
                              Keep Reading
                            </button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </SwiperSlide>
                ))
              }
            </Swiper>
          </div>
          {/* </motion.div> */}

          {/* <motion.div
            initial={{ y: 80, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: false }} */}
          <div
            className="text-center mt-10 hidden sm:block">
            <Link
              href="/blog"
              className="text-sm font-semibold text-black bg-primary-blue rounded-4xl py-4 px-12 hover:bg-sky-blue-750"
              prefetch={false}
            >
              See All
            </Link>
          </div>
          {/* </motion.div> */}

        </div>
      </section>
    </div>
  );
};

export default OurlatestBlog;
