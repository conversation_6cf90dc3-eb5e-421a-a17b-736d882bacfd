import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  CloudUpload,
  Circle,
  LocateFixed,
  CarFront,
  BusFront,
  Bike,
  CarTaxiFront,
  UserPlus,
  Minus,
  Plus,
} from "lucide-react";
import { Autocomplete } from "@react-google-maps/api";
import Loader from "@/components/loader/loader";
import toast from "react-hot-toast";
import { BASE_URL } from "@/utils/api";
import {
  editRideApi,
  getHostRideDatabyIdApi,
} from "@/services/ownerflowServices";
import CustomSelect from "@/components/common/CustomDropdown2";
import { format , isToday } from "date-fns";
import Image from "next/image";

// const DateInputWithPlaceholder = ({ placeholder, value, onChange }) => {
//   const [focused, setFocused] = useState(false);
//   const handleInputClick = (e) => {
//     if (e.target.type === "date") {
//       e.target.showPicker();
//     }
//   };
//   return (
//     <div className='relative'>
//       <input
//         type='date'
//         className={`block w-full border border-black/50 rounded-lg sm:px-4 px-2 sm:py-2.5 py-2 sm:pl-10 pl-7 shadow-sm appearance-none cursor-pointer focus:outline-none focus:ring-1 focus:ring-teal-500 sm:text-sm text-xs`}
//         value={value}
//         onChange={onChange}
//         onFocus={() => setFocused(true)}
//         onBlur={() => setFocused(false)}
//         style={{ color: value ? "inherit" : "transparent", outline: "none" }}
//         onClick={handleInputClick}
//       />
//       {!value && !focused && (
//         <span className='absolute left-10 sm:top-3 top-2 text-sm text-[#00000080] pointer-events-none'>
//           {placeholder}
//         </span>
//       )}
//     </div>
//   );
// };

const Addhostride = ({ closeeditModal, editId, updateRideList }) => {
  // eslint-disable-next-line no-unused-vars
  const [calendarOpen, setCalendarOpen] = useState(false);
  const [count, setCount] = useState(1);
  const [autocomplete, setAutocomplete] = useState(null);
  const [autocompleteD, setAutocompleteD] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isApiLoaded, setIsApiLoaded] = useState(false);
  const [showCalendar, setShowCalendar] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const calendarRef = useRef(null);
  const inputRef = useRef(null);
  const [formData, setFormData] = useState({
    fromAddress: {
      address: "",
      location: {
        type: "Point",
        coordinates: [0, 0],
      },
    },
    destination: {
      address: "",
      location: {
        type: "Point",
        coordinates: [0, 0],
      },
    },
    date: null,
    time: {
      start: 0,
      end: 0,
    },
    passengers: "1",
    modeOfTransfer: "",
    price: "",
    carDetails: {
      companyName: "",
      model: "",
      registrationNumber: "",
      passengerCapacity: "",
    },
    photos: [],
  });

  const handlePrevMonth = () => {
    setCurrentMonth(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() - 1, 1)
    );
  };

  const handleNextMonth = () => {
    setCurrentMonth(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() + 1, 1)
    );
  };

  // Generate days for current month
  function generateDays(month) {
    const daysInMonth = new Date(
      month.getFullYear(),
      month.getMonth() + 1,
      0
    ).getDate();
    const firstDayIndex = new Date(
      month.getFullYear(),
      month.getMonth(),
      1
    ).getDay();

    const days = [];

    // Add blank spaces for days of previous month
    for (let i = 0; i < firstDayIndex; i++) {
      days.push(null);
    }

    // Add days of the current month
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(new Date(month.getFullYear(), month.getMonth(), i));
    }

    return days;
  }

  // Close calendar if clicked outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target) &&
        !inputRef.current.contains(event.target)
      ) {
        setShowCalendar(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  const mapTransportMode = (responseMode) => {
    const mode = responseMode?.toLowerCase();
    const matchedMode = transportModes.find(
      (item) => item.label.toLowerCase() === mode
    );
    return matchedMode ? matchedMode.label : null;
  };

  const fetchData = async (editId) => {
    try {
      const res = await getHostRideDatabyIdApi(editId);
      if (res?.status == 200) {
        const data = res?.data?.data;
        setFormData({
          fromAddress: {
            address: data?.fromAddress?.address,
            location: {
              type: "Point",
              coordinates: [
                data?.fromAddress?.location?.coordinates?.[0],
                data?.fromAddress?.location?.coordinates?.[1],
              ],
            },
          },
          destination: {
            address: data?.destination?.address,
            location: {
              type: "Point",
              coordinates: [
                data?.destination?.location?.coordinates?.[0],
                data?.destination?.location?.coordinates?.[1],
              ],
            },
          },
          date: data?.date.split("T")[0],
          time: {
            start: data?.time?.start,
            end: data?.time?.end,
          },
          passengers: "1",
          modeOfTransfer: mapTransportMode(data?.transportMode),
          price: data?.price,
          carDetails: {
            companyName: data?.name,
            model: data?.model,
            registrationNumber: data?.number,
            passengerCapacity: data?.capacity,
          },
          photos: data?.photos,
        });
        setSelectedMode(mapTransportMode(data?.transportMode));
      }
    } catch (error) {
      console.log("error", error);
    }
  };

  useEffect(() => {
    fetchData(editId);
  }, [editId]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleCarDetailsChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      carDetails: {
        ...prev.carDetails,
        [name]: value,
      },
    }));
  };

  const transportModes = [
    { label: "Bus", icon: BusFront },
    { label: "Private Car", icon: CarFront },
    { label: "Bike", icon: Bike },
    { label: "Share Cab / Taxi", icon: CarTaxiFront },
  ];

  const [selectedMode, setSelectedMode] = useState(transportModes[0].label);

  const handleSelection = (mode) => {
    setSelectedMode(mode);
  };
  const googleMapsApiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

  const loadGoogleMapsApi = () => {
    const script = document.createElement("script");
    script.src = `https://maps.googleapis.com/maps/api/js?key=${googleMapsApiKey}&libraries=places`;
    script.async = true;
    script.onload = () => setIsApiLoaded(true);
    script.onerror = () => setIsApiLoaded(false);
    document.head.appendChild(script);
  };

  useEffect(() => {
    if (window.google && window.google.maps) {
      setIsApiLoaded(true);
    } else {
      loadGoogleMapsApi();
    }
  }, []);

  const handleFAddressChange = useCallback((place) => {
    if (place && place.geometry) {
      console.log("place".place);
      setFormData((prevData) => ({
        ...prevData,
        fromAddress: {
          address: place.formatted_address || "",
          location: {
            type: "Point",
            coordinates: [
              place.geometry.location.lng(),
              place.geometry.location.lat(),
            ],
          },
        },
      }));
    } else {
      console.error("Invalid place selected:", place);
    }
  }, []);

  const handleDAddressChange = useCallback((place) => {
    if (place && place.geometry) {
      setFormData((prevData) => ({
        ...prevData,
        destination: {
          address: place.formatted_address || "",
          location: {
            type: "Point",
            coordinates: [
              place.geometry.location.lng(),
              place.geometry.location.lat(),
            ],
          },
        },
      }));
    } else {
      console.error("Invalid place selected:", place);
    }
  }, []);

  const handleFromAddressChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      fromAddress: {
        ...prevData.fromAddress,
        [name]: value,
      },
    }));
  };

  const handleDestinationAddressChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      destination: {
        ...prevData.destination,
        [name]: value,
      },
    }));
  };

  const handleTimeChange = (type, value) => {
    setFormData((prevState) => ({
      ...prevState,
      time: {
        ...prevState.time,
        [type]: value,
      },
    }));
  };
  const handlePhotoChange = async (e) => {
    const { files } = e.target;

    if (files.length === 0) {
      toast.error("At least one file is required");
      return;
    }
    const allowedTypes = ["image/jpeg", "image/png", "image/jpg"];

    const invalidFiles = Array.from(files).filter(
      (file) => !allowedTypes.includes(file.type)
    );

    if (invalidFiles.length > 0) {
      toast.error("Only JPG, JPEG, and PNG files are allowed.");
      return;
    }

    setIsLoading(true);

    try {
      const uploadedImages = await Promise.all(
        Array.from(files).map(async (file) => {
          const formData = new FormData();
          formData.append("files", file);

          const presignedUrlResponse = await fetch(
            `${BASE_URL}/fileUpload/generate-presigned-url`,
            {
              method: "POST",
              body: formData,
            }
          );

          if (!presignedUrlResponse.ok) {
            throw new Error("Failed to get presigned URL");
          }

          const presignedUrlData = await presignedUrlResponse.json();
          // const { objectURL } = presignedUrlData.data;
          const objectURL = Array.isArray(presignedUrlData.data) && presignedUrlData.data[0]?.path;

          return { title: "Ride Photos", url: objectURL };
        })
      );

      setFormData((prevState) => ({
        ...prevState,
        photos: [...(prevState?.photos || []), ...uploadedImages],
      }));

      toast.success("Files uploaded successfully.");
    } catch (error) {
      console.error("Error uploading files:", error);
      toast.error("Error uploading files.");
    } finally {
      setIsLoading(false);
    }
  };

  const removeImage = (indexToRemove) => {
    setFormData((prevState) => ({
      ...prevState,
      photos: prevState.photos.filter((_, index) => index !== indexToRemove),
    }));
  };

  const handleEdit = async () => {
    const {
      fromAddress,
      destination,
      passengers,
      price,
      carDetails,
      photos,
      date,
      time,
    } = formData;

    // Validate required fields
    if (
      !fromAddress.address ||
      !destination.address ||
      !passengers ||
      !price ||
      !photos ||
      !date ||
      !time ||
      !selectedMode ||
      !carDetails.companyName ||
      !carDetails.model ||
      !carDetails.registrationNumber ||
      !carDetails.passengerCapacity ||
      !photos?.length
    ) {
      toast.error("Please fill all required fields");
      return;
    }
    setIsLoading(true);

    try {
      const payload = {
        fromAddress,
        destination,
        date,
        time,
        passengers: parseInt(passengers),
        price: parseFloat(price),
        transportMode: selectedMode.toLowerCase(),
        name: carDetails.companyName,
        model: carDetails.model,
        number: carDetails.registrationNumber,
        capacity: parseInt(carDetails.passengerCapacity),
        photos,
      };

      const response = await editRideApi(editId, payload);

      if (response.data.success) {
        toast.success("Ride Edit successfully");
        closeeditModal();
        updateRideList();
      } else {
        toast.error(response.data.message);
      }
    } catch (error) {
      console.error("Error Edit Ride:", error);
      toast.error("Failed to Edit Ride");
    } finally {
      setIsLoading(false);
    }
  };

  const handleIncrease = () => {
    setCount(count + 1);
    setFormData((prevState) => ({
      ...prevState,
      passengers: count + 1,
    }));
  };
  const handleDecrease = () => {
    count > 1 && setCount(count - 1);
    setFormData((prevState) => ({
      ...prevState,
      passengers: count - 1,
    }));
  };

  if (!isApiLoaded) {
    return <Loader open={true} />;
  }

  return (
    <>
      <Loader open={isLoading} />
      <section className="w-full">
        <div className="w-full">
          <div className="max-w-[780px] mx-auto">
            <div className="relative">
              <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                From Where to Detination
              </label>
            </div>
            <div className="grid grid-cols-2 sm:gap-5 gap-2">
              <div className="relative">
                <Circle
                  className="absolute left-[10px] top-[19px] text-[#40E0D0]"
                  size={22}
                />
                <Autocomplete
                  onLoad={(autocompleteInstance) =>
                    setAutocomplete(autocompleteInstance)
                  }
                  onPlaceChanged={() => {
                    if (autocomplete) {
                      const place = autocomplete.getPlace();
                      if (!place || !place.geometry) {
                        toast.error("Invalid place selected.");
                        return;
                      }
                      handleFAddressChange(place); // Pass the place to your handler
                    } else {
                      toast.error("Autocomplete is not initialized yet.");
                    }
                  }}
                  className="w-full"
                >
                  <input
                    id="address"
                    name="fromWhere"
                    type="text"
                    placeholder="From Where"
                    value={
                      formData.fromAddress.fromWhere ||
                      formData.fromAddress.address
                    }
                    onChange={handleFromAddressChange}
                    className="mt-1 block border border-black/50 w-full px-3 py-3.5 pl-10 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                  />
                </Autocomplete>
                <LocateFixed
                  className="text-[#1570EF] absolute right-[10px] bottom-[15px]"
                  size={22}
                />
              </div>
              <div className="relative">
                <Circle
                  className="absolute left-[10px] top-[19px] text-[#FF7F50]"
                  size={22}
                />
                <Autocomplete
                  onLoad={setAutocompleteD}
                  onPlaceChanged={() => {
                    if (autocompleteD) {
                      const place = autocompleteD.getPlace(); // Safely get the place
                      handleDAddressChange(place); // Pass the place to your handler
                    } else {
                      console.error(
                        "Autocomplete instance is not initialized."
                      );
                    }
                  }}
                  className="w-full"
                >
                  <input
                    id="address"
                    name="destination"
                    type="text"
                    placeholder="Destination"
                    value={
                      formData.destination.destination ||
                      formData.destination.address
                    }
                    onChange={handleDestinationAddressChange}
                    className="mt-1 block border border-black/50 w-full px-3 py-3.5 pl-10 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                  />
                </Autocomplete>
                <LocateFixed
                  className="text-[#1570EF] absolute right-[10px] bottom-[15px]"
                  size={22}
                />
              </div>
              {/* <div className='relative'>
                <label className='block text-black sm:text-sm text-xs font-medium mb-1.5'>
                  Date
                </label>
                <div className='datepicker relative'>
                  <Image
                    className='text-xl absolute left-[10px] top-[12px] sm:w-[20px] sm:h-[20px] w-[15px] h-[15px]'
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/calendar2.svg`}
                    width='20'
                    height='20'
                    onClick={() => setCalendarOpen(true)}
                  ></Image>

                  <DateInputWithPlaceholder
                    value={formData.date}
                    onChange={(e) =>
                      handleInputChange({
                        ...e,
                        target: { ...e.target, name: "date" },
                      })
                    }
                    className='pl-8'
                  />
                  <div className='absolute top-3 right-3 cursor-pointer w-5 h-5 bg-white'></div>
                </div>
              </div> */}

              <div className="relative w-full max-w-xs">
                <label
                  className="block text-black sm:text-sm text-xs font-medium mb-1.5"
                  htmlFor="toDate"
                >
                  Date
                </label>

                <input
                  type="text"
                  name="toDate"
                  id="toDate"
                  ref={inputRef}
                  value={formData?.date && format(formData?.date, "MM-dd-yyyy")}
                  placeholder="mm/dd/yyyy"
                  readOnly
                  onClick={() => setShowCalendar(!showCalendar)}
                  className="block w-full p-2 px-4 py-3 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-800 placeholder:text-gray-400 cursor-pointer"
                />

                {showCalendar && (
                  <div
                    ref={calendarRef}
                    className="absolute top-full left-0 bg-white border border-black/50 rounded-lg shadow-lg px-4 py-2 z-50 w-full mt-0.5"
                  >
                    {/* Month navigation */}
                    <div className="flex items-center justify-between mb-4">
                      <button
                        onClick={handlePrevMonth}
                        className="text-lg font-bold px-2"
                      >
                        &#8592;
                      </button>

                      <span className="text-base font-semibold">
                        {format(currentMonth, "MMMM yyyy")}
                      </span>

                      <button
                        onClick={handleNextMonth}
                        className="text-lg font-bold px-2"
                      >
                        &#8594;
                      </button>
                    </div>

                    {/* Weekdays */}
                    <div className="grid grid-cols-7 gap-2 text-center text-sm font-semibold text-gray-600">
                      <div>Su</div>
                      <div>Mo</div>
                      <div>Tu</div>
                      <div>We</div>
                      <div>Th</div>
                      <div>Fr</div>
                      <div>Sa</div>
                    </div>

                    {/* Days */}
                    <div className="grid grid-cols-7 gap-1 mt-2 text-center text-sm">
                      {generateDays(currentMonth).map((day, index) =>
                        day ? (
                          <button
                            key={index}
                            className={`rounded-full p-2 text-sm flex items-center justify-center  ${isToday(day) ? "border-2 border-primary-blue" : ""} ${
                              format(day, "MM-dd-yyyy") === formData.date
                                ? "bg-primary-blue text-white"
                                : "hover:bg-primary-blue hover:text-white"
                            }`}
                            onClick={() => {
                              handleInputChange({
                                target: {
                                  name: "date",
                                  value: format(day, "MM-dd-yyyy"),
                                },
                              });
                              setShowCalendar(false);
                            }}
                          >
                            {day.getDate()}
                          </button>
                        ) : (
                          <div key={index} />
                        )
                      )}
                    </div>
                  </div>
                )}
              </div>
              <div className="relative">
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Time
                </label>
                <div className="flex items-center gap-2">
                  <input
                    type="time"
                    className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                    onChange={(e) => handleTimeChange("start", e.target.value)}
                    value={formData.time.start}
                    onFocus={(e) =>
                      e.target.showPicker && e.target.showPicker()
                    }
                  />
                </div>
              </div>
              <div className="relative col-span-2">
                <div className="flex items-center w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500">
                  <div className="flex items-center gap-2 flex-grow">
                    <UserPlus size={20} className="text-gray-500" />
                    <span className="text-black text-sm">{count}</span>
                  </div>

                  <div className="flex items-center gap-2">
                    <button
                      onClick={handleDecrease}
                      className="text-gray-500 hover:text-black cursor-pointer"
                    >
                      <Minus size={18} />
                    </button>
                    <button
                      onClick={handleIncrease}
                      className="text-gray-500 hover:text-black cursor-pointer"
                    >
                      <Plus size={18} />
                    </button>
                  </div>
                </div>
              </div>

              <div className="relative col-span-2">
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Mode of Transfer
                </label>
              </div>
              {transportModes.map((mode, index) => {
                const Icon = mode.icon;
                return (
                  <div
                    key={index}
                    className={`relative cursor-pointer rounded-lg sm:col-span-1 col-span-2 ${
                      selectedMode === mode.label
                        ? "bg-[#40E0D0] text-black"
                        : ""
                    }`}
                    onClick={() => handleSelection(mode.label)}
                  >
                    <Icon
                      className={`absolute left-[10px] sm:top-[13px] top-[9px] ${
                        selectedMode === mode.label
                          ? "text-black"
                          : "text-[#5D6679]"
                      }`}
                      size={22}
                    />
                    <div
                      className={`w-full pl-10 px-3 py-4 border rounded-xl text-sm ${
                        selectedMode === mode.label
                          ? "text-black"
                          : "text-[#5D6679] border border-black/50"
                      }`}
                    >
                      {mode.label}
                    </div>
                  </div>
                );
              })}
              <div className="relative col-span-2">
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Price
                </label>
                <input
                  type="number"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                  placeholder="Enter Price"
                />
              </div>
              <div className="relative col-span-2">
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  {selectedMode} Detail
                </label>
              </div>
              <div className="relative">
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Company Name
                </label>
                <input
                  type="text"
                  name="companyName"
                  value={formData.carDetails.companyName}
                  onChange={handleCarDetailsChange}
                  className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                  placeholder="Enter Company name"
                />
              </div>
              <div className="relative">
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Model
                </label>
                <input
                  type="text"
                  name="model"
                  value={formData.carDetails.model}
                  onChange={handleCarDetailsChange}
                  className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                  placeholder={`Enter ${selectedMode} model`}
                />
              </div>
              <div className="relative">
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Registration Number
                </label>
                <input
                  type="text"
                  name="registrationNumber"
                  value={formData.carDetails.registrationNumber}
                  onChange={handleCarDetailsChange}
                  className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                  placeholder={`Enter ${selectedMode} Registration Number`}
                />
              </div>
              <div className="relative">
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Passenger Capacity
                </label>
                <CustomSelect
                  name="passengerCapacity"
                  options={[...Array(10)].map((_, index) => ({
                    value: index + 1,
                    label: index + 1,
                  }))} // Generate options dynamically
                  value={
                    formData.carDetails.passengerCapacity
                      ? {
                          value: formData.carDetails.passengerCapacity,
                          label: `${formData.carDetails.passengerCapacity}`,
                        }
                      : null
                  }
                  onChange={(selectedOption) =>
                    handleCarDetailsChange({
                      target: {
                        name: "passengerCapacity",
                        value: selectedOption?.value,
                      },
                    })
                  }
                  placeholder="Select Capacity"
                />
              </div>
              <div className="relative col-span-2">
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Add Photo
                </label>
                <input
                  type="file"
                  name="photos"
                  multiple
                  accept=".jpg, .jpeg, .png"
                  onChange={handlePhotoChange}
                  className="z-10 cursor-pointer block w-full p-2 px-4 text-sm bg-transparent border rounded-lg opacity-0 border-gray-220 focus:outline-none text-slate-320 placeholder:text-gray-320 absolute top-0 left-0 right-0 bottom-0"
                  placeholder="Upload Attachment"
                />
                <div className="w-full px-4 py-2 border border-[#40E0D0] rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500 flex items-center justify-between border-dashed bg-[#40E0D01A] cursor-pointer">
                  Upload Attachment
                  <CloudUpload className="text-[#40E0D0]" size={22} />
                </div>
              </div>
              {formData?.photos?.length > 0 && (
                <div className="col-span-2">
                  <div className="grid grid-cols-3 sm:gap-4 gap-3 mt-4">
                    {formData?.photos?.map((image, index) => (
                      <div
                        key={index}
                        className="relative flex items-center justify-between sm:px-4 px-2 sm:py-2.5 py-2 border border-[#40E0D0] border-dashed bg-[#40E0D01A] rounded-lg"
                      >
                        <Image 
                          // src={image?.url}
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/${image?.url}`}
                          alt={`Existing Image ${index + 1}`}
                          className="w-[54px] h-[36px] object-cover rounded-sm"
                          width={54}
                          height={36}
                        />
                        <span
                          className="text-black hover:text-red-500 font-black cursor-pointer text-xl"
                          onClick={() => removeImage(index)}
                        >
                          &#10005;
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="xs:flex block  items-center justify-between w-full sm:my-14 mt-7 mb-4 gap-4 col-span-2 py-4 bg-white/60 sticky bottom-0 backdrop-blur-sm">
              <button
                className="hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 px-4 border-black rounded-lg w-full text-sm xs:mb-0 mb-2"
                onClick={closeeditModal}
              >
                Cancel
              </button>
              <button
                className="bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm"
                onClick={() => handleEdit(true)}
              >
                Edit
              </button>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};
export default Addhostride;
