import React from "react";

const AddCard = () => {
  return (
    <div className="max-w-xl mb-10">
      <h2 className="text-[#40E0D0] flex gap-1 text-2xl font-bold mb-6">
        Add
        <span className="text-black ml-0.5"> Card</span>
      </h2>

      <div className="mb-5">
        <label
          className="block text-gray-400 font-normal text-sm mb-1"
          htmlFor="name"
        >
          Name on Card
        </label>
        <input
          type="text"
          name="name"
          className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
          placeholder="Enter name"
        />
      </div>

      <div className="mb-5">
        <label
          className="block text-gray-400 font-normal text-sm mb-1"
          htmlFor="CardNumber"
        >
          Card Number
        </label>
        <input
          type="text"
          name="CardNumber"
          className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
          placeholder="Enter Number"
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="mb-5">
          <label
            className="block text-gray-400 font-normal text-sm mb-1"
            htmlFor="Expiration"
          >
            Expiration
          </label>
          <input
            type="number"
            name="Expiration"
            className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
            placeholder="01/25"
          />
        </div>
        <div className="mb-5">
          <label
            className="block text-gray-400 font-normal text-sm mb-1"
            htmlFor="cvv"
          >
            CVV
          </label>
          <input
            type="text"
            name="cvv"
            className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
            placeholder="---"
          />
        </div>
      </div>

      <div className="mb-5">
        <label
          className="block text-gray-400 font-normal text-sm mb-1"
          htmlFor="Code"
        >
          Postal Code
        </label>
        <input
          type="number"
          name="Code"
          className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
          placeholder="Enter Code"
        />
      </div>

      <div className="">
        <button
          type="button"
          className="w-full py-4 transition-all hover:bg-sky-blue-750 hover:text-white bg-primary-blue px-4 text-black font-semibold rounded-full"
        >
          Save Card
        </button>
      </div>
    </div>
  );
};

export default AddCard;
