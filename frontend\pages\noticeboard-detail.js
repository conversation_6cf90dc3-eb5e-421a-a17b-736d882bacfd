/* eslint-disable no-irregular-whitespace */
import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
import { IoFilterSharp } from "react-icons/io5";
import {  FormControl, InputLabel } from "@mui/material";
import { getNoticeApi } from "@/services/webflowServices";
import toast from "react-hot-toast";
import { getItemLocalStorage } from "@/utils/browserSetting";
import { useRouter } from "next/router";
import { IoMdEye } from "react-icons/io";
import { FaGlobe } from "react-icons/fa";
import { FaCalendarDays, FaLocationDot } from "react-icons/fa6";
import { PiMapPinLineFill } from "react-icons/pi";
import { MdOutlineChat } from "react-icons/md";
import { BiSolidHeartCircle } from "react-icons/bi";
import Head from "next/head";
import { IoPaperPlane } from "react-icons/io5";
import { X, Plus } from "lucide-react";
import { SlShare } from "react-icons/sl";

const NoticeBoardDetail = () => {
  // eslint-disable-next-line no-unused-vars
  const [age, setAge] = useState("");
  // eslint-disable-next-line no-unused-vars
  const [notices, setNotices] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [loading, setLoading] = useState(false);
  const isFirstRender = useRef(null);
  const router = useRouter();
  const [isPostModal, setIsPostModal] = useState(false);
  const [isShareModal, setIsShareModal] = useState(false);

  const [activeTab, setActiveTab] = useState('All');

  useEffect(() => {
    const fetchNotices = async () => {
      setLoading(true);
      try {
        const response = await getNoticeApi();
        setNotices(response?.data?.data);
      } catch (error) {
        console.error("Error fetching payment data:", error);
      } finally {
        setLoading(false);
      }
    };
    if (!isFirstRender.current && getItemLocalStorage("token") && open) {
      fetchNotices();
    } else {
      isFirstRender.current = false;
      toast.error("Please Login !!!");
      router.push("/");
    }
  }, []);

  // eslint-disable-next-line no-unused-vars
  const handleChange = (event) => {
    setAge(event.target.value);
  };

  return (
    <>
    <Head>
      <title>Noticeboard | Community Updates & Announcements | Mixdorm</title>
    </Head>
      {/* <div className="bg-white md:pb-16 font-manrope md:pt-12 py-8">
        <div className="container md:px-8 lg:px-12 xl:px-16">
          <div className="relative mb-8">
            <h2 className="sm:text-[27px] text-xl leading-none md:text-center text-left font-bold text-gray-800  pt-2">
              <span className="text-teal-500">Notice</span>board
            </h2>

            <FormControl
              sx={{ minWidth: 140 }}
              size="small"
              className="absolute top-0 right-0"
            >
              <InputLabel
                id="demo-simple-select-label"
                className="flex items-center gap-1 text-sm"
              >
                <IoFilterSharp />
                Hostel / City
              </InputLabel>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                value={age}
                label="Hostel / City"
                onChange={handleChange}
              >
                <MenuItem value={10}>Ten</MenuItem>
                <MenuItem value={20}>Twenty</MenuItem>
                <MenuItem value={30}>Thirty</MenuItem>
              </Select>
            </FormControl>
          </div>
          <div className="">
            <Swiper
              
              modules={[Pagination, Autoplay]}
              autoplay={{ delay: 3000 }}
              slidesPerView={1}
              loop
              speed={1000}
              spaceBetween={0}
              navigation={{
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
              }}
              className=" mySwiper bg-[#D9F9F6] rounded-xl border-2 hover:border-[#40E0D0] pt-5 pb-10"
            >
              {notices.map((notice) => (
                <SwiperSlide
                  key={notice.title}
                  className="w-full bg-transparent"
                >
                  <div className=" flex flex-col justify-center items-center">
                    <Image
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/apple.svg`}
                      alt="NoticeBoardIcon"
                      title="NoticeBoardIcon"
                      className="w-fit max-w-16 max-h-16 rounded-full"
                      width={54}
                      height={54}
                      loading="lazy"
                    />
                    <p className="text-xs pt-4 w-[300px] text-center text-black font-normal">
                      {notice.text}
                    </p>
                  </div>
                </SwiperSlide>
              ))}

              <button className="absolute left-4 top-1/2 -mt-4 text-[#D9F9F6] cursor-pointer z-40 bg-black w-6 h-6 rounded-full">
                <ChevronLeft className="text-[#D9F9F6]" />
              </button>

              <button className="absolute right-4 top-1/2 -mt-4 text-[#D9F9F6] cursor-pointer z-40 bg-black w-6 h-6 rounded-full">
                <ChevronRight className="text-[#D9F9F6]" />
              </button>
            </Swiper>

            <div className="flex gap-4 py-6 border-b -mx-8 px-8 overflow-y-auto">
              <button
                type="button"
                className="px-4 py-5 min-w-[120px] bg-[#FFE3EF] border-1.5 hover:border-[#FF72AD] text-left rounded-[20px]"
              >
                <span className="block mb-3">
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/hostel_icon.svg`}
                    alt=""
                    width={30}
                    height={30}
                    loading="lazy"
                  />
                </span>
                <p className="mb-0 text-[#FF72AD] font-semibold text-base">
                  Hostels <br />
                  600
                </p>
              </button>
              <button
                type="button"
                className="px-4 py-5 min-w-[120px] bg-[#D9F9F6] border-1.5 hover:border-[#40E0D0] text-left rounded-[20px]"
              >
                <span className="block mb-3">
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/notify_icon.svg`}
                    alt=""
                    width={30}
                    height={30}
                    loading="lazy"
                  />
                </span>
                <p className="mb-0 text-[#40E0D0] font-semibold text-base">
                  All <br />
                  2400
                </p>
              </button>

              <button
                type="button"
                className="px-4 py-5 min-w-[120px] bg-[#FFEFE3] border-1.5 hover:border-[#FFAD72] text-left rounded-[20px]"
              >
                <span className="block mb-3">
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/city_icon.svg`}
                    alt=""
                    width={30}
                    height={30}
                    loading="lazy"
                  />
                </span>
                <p className="mb-0 text-[#FFAD72] font-semibold text-base">
                  City <br />
                  700
                </p>
              </button>

              <button
                type="button"
                className="px-4 py-5 min-w-[120px] bg-[#FBF5FE] border-1.5 hover:border-[#D092F5] text-left rounded-[20px]"
              >
                <span className="block mb-3">
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/ride_icon.svg`}
                    alt=""
                    loading="lazy"
                    width={30}
                    height={30}
                  />
                </span>
                <p className="mb-0 text-[#D092F5] font-semibold text-base">
                  Ride <br />
                  2400
                </p>
              </button>

              <button
                type="button"
                className="px-4 py-5 min-w-[120px] bg-[#F1F7FF] border-1.5 hover:border-[#72ADFF] text-left rounded-[20px]"
              >
                <span className="block mb-3">
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/event_icon.svg`}
                    alt=""
                    loading="lazy"
                    width={30}
                    height={30}
                  />
                </span>
                <p className="mb-0 text-[#72ADFF] font-semibold text-base">
                  Events <br />
                  2400
                </p>
              </button>
              <button
                type="button"
                className="px-4 py-5 min-w-[120px] bg-[#FFFBE6] border-1.5 hover:border-[#FFD600] text-left rounded-[20px]"
              >
                <span className="block mb-3">
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/creator_icon.svg`}
                    alt=""
                    width={30}
                    height={30}
                    loading="lazy"
                  />
                </span>
                <p className="mb-0 text-[#FFD600] font-semibold text-base">
                  Creators <br />
                  700
                </p>
              </button>

              <button
                type="button"
                className="px-4 py-5 min-w-[120px] bg-[#F6FBEA] border-1.5 hover:border-[#9FD62A] text-left rounded-[20px]"
              >
                <span className="block mb-3">
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/split_icon.svg`}
                    alt=""
                    width={30}
                    height={30}
                    loading="lazy"
                  />
                </span>
                <p className="mb-0 text-[#9FD62A] font-semibold text-base">
                  Spit bill <br />
                  700
                </p>
              </button>
              <button
                type="button"
                className="px-4 py-5 min-w-[120px] bg-[#FDF2EF] border-1.5 hover:border-[#EA4080] text-left rounded-[20px]"
              >
                <span className="block mb-3">
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mixmate_icon.svg`}
                    alt=""
                    width={30}
                    height={30}
                    loading="lazy"
                  />
                </span>
                <p className="mb-0 text-[#EA4080] font-semibold text-base">
                  Mixmate <br />
                  700
                </p>
              </button>
              <button
                type="button"
                className="px-4 py-5 min-w-[120px] bg-[#FEF5EF] border-1.5 hover:border-[#EE925F] text-left rounded-[20px]"
              >
                <span className="block mb-3">
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/webCheckin.svg`}
                    alt=""
                    width={30}
                    height={30}
                    loading="lazy"
                  />
                </span>
                <p className="mb-0 text-[#EE925F] font-semibold text-base">
                  Web checking <br />
                  700
                </p>
              </button>
            </div>
           

            <div>
              {notices.map((notice) => (
                <div
                  key={notice._id}
                  className='py-6 border-b px-4 flex items-start'
                >
                  <div className='relative'>
                    <Image
                      src={notice?.actionBy?.profileImage?.objectURL}
                      alt={notice?.actionBy?.name?.first}
                      width={56}
                      height={56}
                      className='w-14 h-14 object-cover rounded-full'
                      loading='lazy'
                    />
                  </div>
                  <div className='flex-1 pl-7'>
                    <h6 className='text-sm font-semibold text-black mb-2'>
                      {notice?.actionBy?.name?.first} - {notice?.title}
                      <span className='block text-[#7F7F7F] font-normal'>
                        {(() => {
                          const formattedDate = new Date(notice?.createdAt);
                          const day = String(formattedDate.getDate()).padStart(
                            2,
                            "0"
                          );
                          const month = String(
                            formattedDate.getMonth() + 1
                          ).padStart(2, "0");
                          const year = formattedDate.getFullYear();

                          let hours = formattedDate.getHours();
                          const minutes = String(
                            formattedDate.getMinutes()
                          ).padStart(2, "0");
                          const ampm = hours >= 12 ? "PM" : "AM"; 

                          hours = hours % 12; 
                          hours = hours ? String(hours).padStart(2, "0") : "12"; 

                          return `${day}/${month}/${year} ${hours}:${minutes} ${ampm}`;
                        })()}
                      </span>
                    </h6>
                 
                    <div className='relative'>
                      <input
                        type='text'
                        className='w-full rounded-4xl bg-white border border-[#D6D6D6] py-3 pl-3 pr-10 text-black placeholder:text-[#7F7F7F] text-xs focus:outline-none'
                        placeholder='Write here...'
                      />
                      <button
                        type='button'
                        className='absolute right-2 top-1 p-2'
                      >
                        <HiReply />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div> */}
      <div className="container my-4">
        <div className="grid grid-cols-12 gap-4">
          <div className="col-span-12 sm:col-span-4 lg:col-span-3 order-1 lg:order-1 xs:block hidden">
            <div className="sticky top-[130px]">
              <div className="sm:block flex gap-2">
                <div className="relative object-cover sm:w-full xs:w-[50%] w-[55%] sm:h-[150px] h-full ms:p-6 p-4 bg-[#012E42] rounded-xl text-white flex justify-between" style={{
                      backgroundImage:
                        `url(${`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/line-bg.png`})`,
                    }}>
                  <div className="relative z-10">
                    <h2 className="md:text-2xl text-xl font-bold">Shravani</h2>
                    <p className="text-xs xs:text-sm text-white">Developer</p>
                    <p className="text-white text-xs xs:text-sm">21</p>
                    <p className="text-white text-xs xs:text-sm">Mumbai, India</p>
                  </div>

                  <div className="relative mt-2">
                    <span className="relative">
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                        width={37}
                        height={37}
                        alt="Arlene McCoy"
                        className="sm:w-14 sm:h-14 w-12 h-12 rounded-full"
                      />
                      <div
                        className="absolute -top-1 -right-1 sm:w-16 sm:h-16 w-14 h-14 border-2 border-teal-400 rounded-full -rotate-90"
                        style={{ clipPath: "inset(30% 0 0 0)" }}
                      ></div>
                      <span className="absolute -bottom-1 left-9 ">
                        <Image
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                          width={37}
                          height={37}
                          alt="Flag"
                          className="w-3.5 h-3.5 rounded-full"
                        />
                      </span>
                    </span>
                  </div>
                </div>
                <div className="sm:w-full xs:w-[50%] w-[45%]">
                  <div className="w-full bg-[#EEEEEE] rounded-2xl flex items-center sm:mt-3 sm:p-4 p-2 space-x-2">
                    <div>
                      <IoMdEye className="sm:text-2xl xs:text-xl text-lg" />
                    </div>
                    <div>
                      <button className="xs:text-sm text-xs underline flex items-center"><span className="sm:block hidden">Profile &nbsp; </span> Viewers</button>
                      <p className="text-sm">256</p>
                    </div>
                  </div>
                  <div className="w-full bg-[#EEEEEE] rounded-2xl flex items-center sm:mt-3 mt-2 sm:p-4 p-2 space-x-2">
                    <div className="relative">
                      <span className="">
                        <FaGlobe className="sm:text-2xl xs:text-xl text-lg" />
                      </span>
                      <span className="absolute -top-2 left-3.5 sm:block hidden">
                        <FaLocationDot className="text-base text-gray-400" />
                      </span>
                    </div>
                    <div className="space-y-1">
                      <button className="xs:text-sm text-left text-xs underline flex items-center"><span className="sm:block hidden">7 &nbsp;</span>Countries Visites</button>
                      <div className="relative">
                        <span className="">
                          <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                            width={37}
                            height={37}
                            alt="Flag"
                            className="xs:w-5 xs:h-5 w-4 h-4 rounded-full "
                          />
                        </span>
                        <span className="absolute bottom-0 left-4">
                          <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                            width={37}
                            height={37}
                            alt="Flag"
                            className="xs:w-5 xs:h-5 w-4 h-4 rounded-full "
                          />
                        </span>
                        <span className="absolute bottom-0 left-4">
                          <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                            width={37}
                            height={37}
                            alt="Flag"
                            className="xs:w-5 xs:h-5 w-4 h-4 rounded-full "
                          />
                        </span>
                        <span className="absolute bottom-0 left-8">
                          <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                            width={37}
                            height={37}
                            alt="Flag"
                            className="xs:w-5 xs:h-5 w-4 h-4 rounded-full "
                          />
                        </span>
                        <span className="absolute bottom-0 left-12">
                          <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                            width={37}
                            height={37}
                            alt="Flag"
                            className="xs:w-5 xs:h-5 w-4 h-4 rounded-full "
                          />
                        </span>
                        <span className="absolute bottom-0 left-16">
                          <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                            width={37}
                            height={37}
                            alt="Flag"
                            className="xs:w-5 xs:h-5 w-4 h-4 rounded-full "
                          />
                        </span>
                        <span className="absolute bottom-0 left-20">
                          <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                            width={37}
                            height={37}
                            alt="Flag"
                            className="xs:w-5 xs:h-5 w-4 h-4 rounded-full "
                          />
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="lg:hidden sm:block xs:grid block grid-cols-12 gap-2">
                <div className="w-full col-span-6 mt-3">
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/noticeD1.png`}
                    width={40}
                    height={40}
                    alt=""
                    className="w-full sm:h-full h-[150px] object-cover rounded-2xl"
                  />
                
                </div>
                <div className="w-full col-span-6 mt-3 relative">
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/noticeD2.png`}
                    width={40}
                    height={40}
                    alt=""
                    className="w-full sm:h-full h-[150px] object-cover rounded-2xl"
                  />
                  <div className="absolute bg-[#0000008F] rounded-[10px] left-[3%] bottom-0 p-2 w-[94%] mb-3">
                    <h2 className="md:text-4xl text-2xl text-center font-bold text-[#40E0D0] pt-2">
                      <span className="text-white">Mix</span>Shuffle
                    </h2>
                    <p className="mb-0 text-center md:text-sm text-xs text-white font-medium font-manrope leading-tight mt-2">Stay More than Six days and get <br></br> Premium membership </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-span-12 sm:col-span-8 lg:col-span-6 order-3 lg:order-2 sm:mb-0 mb-10">
            <div className="bg-white rounded-2xl w-full p-3 border">
              <div className="flex justify-between items-center">
                <h2 className="xs:text-[27px] text-[20px] font-bold text-black pt-2">
                  <span className="text-[#40E0D0]">Notice</span>board
                </h2>

                <div className="flex items-center md:gap-3">
                  <FormControl className="sm:mb-0 mb-3"
                    sx={{
                      minWidth: {
                        xs: 70, 
                        sm:140,
                        md: 140 
                      },
                      minHeight: {
                        xs: 40,
                        sm: 40,
                        md: 48
                      }
                    }}
                    size="small"
                  >
                    <InputLabel
                      id="demo-simple-select-label"
                      className="flex items-center justify-center xs:gap-2 xs:text-sm text-xs sm:h-auto sm:w-auto h-[40px] w-[40px] border sm:rounded-2xl rounded-full px-2 py-1.5 text-black"
                    >
                      <IoFilterSharp className="xs:min-w-4 min-w-3" size={14}/>
                      <span className="sm:block hidden">Hostel / City</span>
                    </InputLabel>
                  </FormControl>

                  <button className="text-black bg-[#40E0D0] xs:text-sm text-xs h-9 xs:h-10 md:h-12 w-auto xs:px-4 px-2 rounded-full font-bold mt-1.5 md:mt-0 " onClick={() => setIsPostModal(true)}>
                    <Plus className="sm:hidden block" size={18} />
                    <span className="sm:block hidden">Add Post</span>
                  </button>
                </div>
              </div>
              <div className="w-full">
                <div className="flex xs:gap-2 gap-1 px-1 items-center mt-3 mr-0 w-full overflow-y-auto">
                  {['All', 'Activity Updates', 'Property Updates', 'Social Interactions'].map((tab) => (
                    <button
                      key={tab}
                      onClick={() => setActiveTab(tab)}
                      className={`w-auto text-nowrap sm:h-[51px] h-[44px] rounded-lg text-center px-4 font-semibold text-xs xs:text-sm transition-all
                        ${tab === 'All' ? 'bg-[#D9F9F6] text-[#56edde] md:min-w-[67px]' : ''}
                        ${tab === 'Activity Updates' ? 'bg-[#FFE3EF] text-pink-500' : ''}
                        ${tab === 'Property Updates' ? 'bg-[#FFEFE3] text-orange-400' : ''}
                        ${tab === 'Social Interactions' ? 'bg-[#D9F9F6] text-[#56edde]' : ''}
                    
                        ${
                          activeTab === tab
                            ? tab === 'All'
                              ? 'border-2 border-[#66E6D9]'
                              : tab === 'Activity Updates'
                              ? 'border-2 border-pink-500'
                              : tab === 'Property Updates'
                              ? 'border-2 border-orange-400'
                              : tab === 'Social Interactions'
                              ? 'border-2 border-[#66E6D9]'
                              : ''
                            : 'hover:border-2 ' +
                              (tab === 'All'
                                ? 'hover:border-[#66E6D9]'
                                : tab === 'Activity Updates'
                                ? 'hover:border-pink-500'
                                : tab === 'Property Updates'
                                ? 'hover:border-orange-400'
                                : tab === 'Social Interactions'
                                ? 'hover:border-[#66E6D9]'
                                : '')
                        }
                      `}
                    >
                      {tab}
                    </button>
                  ))}
                </div>

                {/* All Tab Content */} 
                {activeTab === 'All' && (
                  <div className="mt-6 w-full ">
                    <div className="bg-white flex justify-between first:border-t border-b last:border-0 w-full py-4">
                      <div className="flex flex-col justify-around w-full">
                        <div className="flex gap-2 w-full">
                          <div>
                            <Image
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                              width={37}
                              height={37}
                              alt="Arlene McCoy"
                              className="min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full"
                            />
                          </div>
                          <div>
                            <button className="text-black bg-[#d8d4d4] px-2 w-max h-[22px] flex items-center justify-center text-xs rounded-md">
                              Mix Mate
                            </button>
                            <p className="font-semibold text-gray-400 xs:text-sm text-xs mt-1">
                              <span className="text-black xs:text-sm text-xs font-bold">
                                {" "}
                                Priyanka
                              </span>{" "}
                              - You got your first like!
                            </p>
                          </div>
                        </div>
                        <div className="px-12">
                          <div className="flex gap-2 mt-2 p-2 border rounded-lg md:w-[50%] w-full">
                            <div className="relative mt-1">
                              <span className="">
                                <Image
                                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                                  width={37}
                                  height={37}
                                  alt="Arlene McCoy"
                                  className="min-w-12 md:min-w-14 min-h-12 md:min-h-14 rounded-full"
                                />
                              </span>
                              <span className="absolute bottom-5 md:bottom-2 left-8 ">
                                <Image
                                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                                  width={37}
                                  height={37}
                                  alt="Flag"
                                  className="w-3 md:w-4 h-3 md:h-4 rounded-full"
                                />
                              </span>
                            </div>

                            <div className="h-[76px] w-full">
                              <div className="mt-2">
                                <p className="font-semibold text-xs md:text-sm ">Elena Sah</p>
                                <div className="flex gap-x-0.5">
                                  <button className="  flex justify-center items-center">
                                    <BiSolidHeartCircle className="text-[#66E6D9] text-base  md:text-lg" />
                                  </button>
                                  <p className="text-gray-600 text-xs md:text-sm ">
                                    {" "}
                                    44 Likes
                                  </p>
                                </div>
                              </div>

                              <div className="relative ">
                                <button className="whitespace-nowrap absolute right-2 -bottom-9 md:-bottom-7 w-[66px] h-[20px] md:w-[76px] md:h-[26px] text-[#66E6D9] border border-[#66E6D9]  rounded-lg text-xxs md:text-xs">
                                  View Profile
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="text-gray-500 flex flex-col justify-center items-center ">
                        <p className="text-sm text-gray-400">5m</p>
                        <div className="relative mt-1">
                          <span className="w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center">
                            <MdOutlineChat className="text-sm text-white" />
                          </span>
                          <span className="h-3.5 w-3.5 bg-[#FF7F50] rounded-full text-[10px] absolute -top-1 left-4 text-center text-black flex items-center justify-center leading-none">
                            1
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white flex justify-between first:border-t border-b last:border-0 py-4 w-full">
                      <div className="flex flex-col justify-evenly w-full">
                        <div className="flex gap-2 w-full">
                          <div>
                            <Image
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                              width={37}
                              height={37}
                              alt="Arlene McCoy"
                              className="min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full"
                            />
                          </div>
                          <div>
                            <button className="text-black bg-[#d8d4d4] px-2 w-max h-[22px] flex items-center justify-center text-xs rounded-md">
                              Mix Event
                            </button>
                            <p className="font-semibold text-black text-xs xs:text-sm mt-1">
                              <span className="text-black xs:text-sm text-xs font-bold">
                                {" "}
                                Mad Monkey
                              </span>{" "}
                              - Created New Event - Club Dance
                            </p>
                          </div>
                        </div>
                        <div className="flex flex-wrap items-center mt-2 px-6 md:px-12 gap-x-2">
                          <div className="flex gap-2">
                            <button className="h-5 md:h-7 w-5 md:w-7 rounded-lg bg-[#D9F9F6] border-2 border-[#66E6D9] text-[#56edde] flex items-center justify-center">
                              <FaCalendarDays className="text-[#56edde] text-xs md:text-base" />
                            </button>
                            <div>
                              <p className="text-xxs md:text-xs">14 December, 2021</p>
                              <p className="text-xxs md:text-xs text-gray-500">
                                Tuesday, 4:00PM - 9:00PM
                              </p>
                            </div>
                          </div>

                          <div className="flex gap-2">
                            <button className="h-5 md:h-7 w-5 md:w-7 rounded-lg bg-[#D9F9F6] border-2 border-[#66E6D9] text-[#56edde] flex items-center justify-center">
                              <PiMapPinLineFill className="text-[#56edde] text-xs md:text-base" />
                            </button>
                            <div>
                              <p className="text-xxs md:text-xs">Gala Covention Center</p>
                              <p className="text-xxs md:text-xs text-gray-500">
                                36 Guild Street London, UK
                              </p>
                            </div>
                          </div>
                          
                        </div>
                        <div className="pl-14 pe-7 pt-2">
                          <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`}
                            width={37}
                            height={37}
                            alt="Arlene McCoy"
                            className="w-[141px] h-[69px] md:h-[89px] "
                          />
                        </div>
                      </div>
                      <div className="text-gray-500 ">
                        <p className="text-sm text-gray-400 ">5m</p>
                      </div>
                    </div>

                    <div className="bg-white flex justify-between first:border-t border-b last:border-0 py-4 w-full">
                      <div className="">
                        <div className="flex gap-2 w-full">
                          <div>
                            <Image
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                              width={37}
                              height={37}
                              alt="Arlene McCoy"
                              className="min-w-11 min-h-11 rounded-full"
                            />
                          </div>
                          <div className="space-y-1.5 md:space-y-2">
                            <button className="text-black bg-[#d8d4d4] px-2 w-max h-[22px] flex items-center justify-center text-xs rounded-md">
                              Mix Creator
                            </button>
                            <p className="font-semibold text-black text-xs xs:text-sm mt-1">
                              <span className="text-black xs:text-sm text-xs font-bold">
                                {" "}
                                Mad Monkey - Mix
                              </span>{" "}
                              <span className="text-[#56edde]">Creator</span>
                              Sent offer request to Ayush Jain
                            </p>
                            <p className="text-black text-xxs md:text-sm font-bold">
                              Service Offered : Story Instagram
                            </p>
                            <button className="whitespace-nowrap w-[66px] md:w-[76px] h-[20px] md:h-[26px] text-[#66E6D9] border border-[#66E6D9]  rounded-lg text-xxs md:text-xs ">
                              View Offer
                            </button>
                          </div>
                        </div>
                      </div>
                      <div className="text-gray-500 ">
                        <p className="text-sm text-gray-400 ">5m</p>
                      </div>
                    </div>

                    <div className="bg-white flex justify-between first:border-t border-b last:border-0 py-4 w-full">
                      <div className="">
                        <div className="flex gap-2 w-full">
                          <div>
                            <Image
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                              width={37}
                              height={37}
                              alt="Arlene McCoy"
                              className="min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full"
                            />
                          </div>
                          <div className="space-y-1.5 md:space-y-2">
                            <button className="text-black bg-[#d8d4d4] px-2 w-max h-[22px] flex items-center justify-center text-xs rounded-md">
                              Hostel
                            </button>
                            <p className="font-semibold text-gray-400 xs:text-sm text-xs mt-1">
                              <span className="text-black xs:text-sm text-xs font-bold">
                                {" "}
                                Hostel Mumbai
                              </span>{" "}
                              - Happy to serve your Group Checkin
                            </p>
                            <div className="flex mt-2 relative">
                              <div>
                                <div className="relative mt-1">
                                  <span className="">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                                      width={37}
                                      height={37}
                                      alt="Arlene McCoy"
                                      className="min-w-10 md:min-w-14 min-h-10 md:min-h-14 rounded-full border border-white backdrop-blur-sm blur-[1px]"
                                    />
                                  </span>
                                  <span className="absolute bottom-0 left-7 md:left-8">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                                      width={37}
                                      height={37}
                                      alt="Flag"
                                      className="w-3 md:w-4 h-3 md:h-4 rounded-full"
                                    />
                                  </span>
                                </div>
                              </div>
                              <div className="absolute bottom-[0%] left-[12%]">
                                <div className="relative mt-1">
                                  <span className="">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                                      width={37}
                                      height={37}
                                      alt="Arlene McCoy"
                                      className="min-w-10 md:min-w-14 min-h-10 md:min-h-14 rounded-full border border-white backdrop-blur-sm blur-[1px]"
                                    />
                                  </span>
                                  <span className="absolute bottom-0 left-7 md:left-8 ">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                                      width={37}
                                      height={37}
                                      alt="Flag"
                                      className="w-3 md:w-4 h-3 md:h-4  rounded-full"
                                    />
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="text-gray-500 ">
                        <p className="text-sm text-gray-400 ">5m</p>
                      </div>
                    </div>

                    <div className="bg-white flex justify-between first:border-t border-b last:border-0 py-4 w-full">
                      <div className="">
                        <div className="flex gap-2 w-full">
                          <div>
                            <Image
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                              width={37}
                              height={37}
                              alt="Arlene McCoy"
                              className="min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full"
                            />
                          </div>
                          <div className="">
                            <button className="text-black bg-[#d8d4d4] px-2 w-max h-[22px] flex items-center justify-center text-xs rounded-md">
                              Mix Event
                            </button>
                            <p className="font-bold text-bold xs:text-sm text-xs mt-1">
                              Made Monkey - A****** +3 other Joint Club Dance Event
                              on 31st December
                            </p>
                            <div className="flex mt-2 relative">
                              <div>
                                <div className="relative mt-1">
                                  <span className="">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                                      width={37}
                                      height={37}
                                      alt="Arlene McCoy"
                                      className="min-w-10 md:min-w-14 min-h-10 md:min-h-14 rounded-full border border-white backdrop-blur-sm blur-[1px]"
                                    />
                                  </span>
                                  <span className="absolute bottom-0 left-7 md:left-8">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                                      width={37}
                                      height={37}
                                      alt="Flag"
                                      className="w-3 md:w-4 h-3 md:h-4 rounded-full"
                                    />
                                  </span>
                                </div>
                              </div>
                              <div className="absolute bottom-[0%] left-[9%]">
                                <div className="relative mt-1">
                                  <span className="">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                                      width={37}
                                      height={37}
                                      alt="Arlene McCoy"
                                      className="min-w-10 md:min-w-14 min-h-10 md:min-h-14 rounded-full border border-white backdrop-blur-sm blur-[1px]"
                                    />
                                  </span>
                                  <span className="absolute bottom-0 left-7 md:left-8 ">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                                      width={37}
                                      height={37}
                                      alt="Flag"
                                      className="w-3 md:w-4 h-3 md:h-4 rounded-full"
                                    />
                                  </span>
                                </div>
                              </div>
                              <div className="absolute bottom-[0%] left-[18%]">
                                <div className="relative mt-1">
                                  <span className="">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                                      width={37}
                                      height={37}
                                      alt="Arlene McCoy"
                                      className="min-w-10 md:min-w-14 min-h-10 md:min-h-14 rounded-full border border-white backdrop-blur-sm blur-[1px]"
                                    />
                                  </span>
                                  <span className="absolute bottom-0 left-7 md:left-8 ">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                                      width={37}
                                      height={37}
                                      alt="Flag"
                                      className="w-3 md:w-4 h-3 md:h-4 rounded-full"
                                    />
                                  </span>
                                </div>
                              </div>
                              <div className="absolute bottom-[0%] left-[27%]">
                                <div className="relative mt-1">
                                  <span className="">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                                      width={37}
                                      height={37}
                                      alt="Arlene McCoy"
                                      className="min-w-10 md:min-w-14 min-h-10 md:min-h-14 rounded-full border border-white backdrop-blur-sm blur-[1px]"
                                    />
                                  </span>
                                  <span className="absolute bottom-0 left-7 md:left-8 ">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                                      width={37}
                                      height={37}
                                      alt="Flag"
                                      className="w-3 md:w-4 h-3 md:h-4 rounded-full"
                                    />
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="text-gray-500 ">
                        <p className="text-sm text-gray-400 ">5m</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Activity Updates Content */}
                {activeTab === 'Activity Updates' && (
                  <div className="mt-6 w-full ">

                    <div className="bg-white flex justify-between first:border-t border-b last:border-0 py-4 w-full">
                      <div className="flex flex-col justify-evenly w-full">
                        <div className="flex gap-2 w-full">
                          <div>
                            <Image
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                              width={37}
                              height={37}
                              alt="Arlene McCoy"
                              className="min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full"
                            />
                          </div>
                          <div>
                            <button className="text-black bg-[#d8d4d4] px-2 w-max h-[22px] flex items-center justify-center text-xs rounded-md">
                              Mix Event
                            </button>
                            <p className="font-semibold text-black text-xs xs:text-sm mt-1">
                              <span className="text-black xs:text-sm text-xs font-bold">
                                {" "}
                                Mad Monkey
                              </span>{" "}
                              - Created New Event - Club Dance
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center mt-2 px-6 md:px-12 gap-x-2">
                          <div>
                            <button className="h-5 md:h-7 w-5 md:w-7 rounded-lg bg-[#D9F9F6] border-2 border-[#66E6D9] text-[#56edde] flex items-center justify-center">
                              <FaCalendarDays className="text-[#56edde] text-xs md:text-base" />
                            </button>
                          </div>
                          <div>
                            <p className="text-xxs md:text-xs">14 December, 2021</p>
                            <p className="text-xxs md:text-xs text-gray-500">
                              Tuesday, 4:00PM - 9:00PM
                            </p>
                          </div>

                          <div>
                            <button className="h-5 md:h-7 w-5 md:w-7 rounded-lg bg-[#D9F9F6] border-2 border-[#66E6D9] text-[#56edde] flex items-center justify-center">
                              <PiMapPinLineFill className="text-[#56edde] text-xs md:text-base" />
                            </button>
                          </div>
                          <div>
                            <p className="text-xxs md:text-xs">Gala Covention Center</p>
                            <p className="text-xxs md:text-xs text-gray-500">
                              36 Guild Street London, UK
                            </p>
                          </div>
                        </div>
                        <div className="pl-14 pe-7 pt-2">
                          <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`}
                            width={37}
                            height={37}
                            alt="Arlene McCoy"
                            className="w-[141px] h-[69px] md:h-[89px] "
                          />
                        </div>
                      </div>
                      <div className="text-gray-500 ">
                        <p className="text-sm text-gray-400 ">5m</p>
                      </div>
                    </div>

                    <div className="bg-white flex justify-between first:border-t border-b last:border-0 py-4 w-full">
                      <div className="">
                        <div className="flex gap-2 w-full">
                          <div>
                            <Image
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                              width={37}
                              height={37}
                              alt="Arlene McCoy"
                              className="min-w-11 min-h-11 rounded-full"
                            />
                          </div>
                          <div className="space-y-1.5 md:space-y-2">
                            <button className="text-black bg-[#d8d4d4] px-2 w-max h-[22px] flex items-center justify-center text-xs rounded-md">
                              Mix Creator
                            </button>
                            <p className="font-semibold text-black text-xs xs:text-sm mt-1">
                              <span className="text-black xs:text-sm text-xs font-bold">
                                {" "}
                                Mad Monkey - Mix
                              </span>{" "}
                              <span className="text-[#56edde]">Creator</span>
                              Sent offer request to Ayush Jain
                            </p>
                            <p className="text-black text-xxs md:text-sm font-bold">
                              Service Offered : Story Instagram
                            </p>
                            <button className="whitespace-nowrap w-[66px] md:w-[76px] h-[20px] md:h-[26px] text-[#66E6D9] border border-[#66E6D9]  rounded-lg text-xxs md:text-xs ">
                              View Offer
                            </button>
                          </div>
                        </div>
                      </div>
                      <div className="text-gray-500 ">
                        <p className="text-sm text-gray-400 ">5m</p>
                      </div>
                    </div>

                    <div className="bg-white flex justify-between first:border-t border-b last:border-0 py-4 w-full">
                      <div className="">
                        <div className="flex gap-2 w-full">
                          <div>
                            <Image
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                              width={37}
                              height={37}
                              alt="Arlene McCoy"
                              className="min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full"
                            />
                          </div>
                          <div className="space-y-1.5 md:space-y-2">
                            <button className="text-black bg-[#d8d4d4] px-2 w-max h-[22px] flex items-center justify-center text-xs rounded-md">
                              Hostel
                            </button>
                            <p className="font-semibold text-gray-400 xs:text-sm text-xs mt-1">
                              <span className="text-black xs:text-sm text-xs font-bold">
                                {" "}
                                Hostel Mumbai
                              </span>{" "}
                              - Happy to serve your Group Checkin
                            </p>
                            <div className="flex mt-2 relative">
                              <div>
                                <div className="relative mt-1">
                                  <span className="">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                                      width={37}
                                      height={37}
                                      alt="Arlene McCoy"
                                      className="min-w-10 md:min-w-14 min-h-10 md:min-h-14 rounded-full border border-white backdrop-blur-sm blur-[1px]"
                                    />
                                  </span>
                                  <span className="absolute bottom-0 left-7 md:left-8">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                                      width={37}
                                      height={37}
                                      alt="Flag"
                                      className="w-3 md:w-4 h-3 md:h-4 rounded-full"
                                    />
                                  </span>
                                </div>
                              </div>
                              <div className="absolute bottom-[0%] left-[12%]">
                                <div className="relative mt-1">
                                  <span className="">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                                      width={37}
                                      height={37}
                                      alt="Arlene McCoy"
                                      className="min-w-10 md:min-w-14 min-h-10 md:min-h-14 rounded-full border border-white backdrop-blur-sm blur-[1px]"
                                    />
                                  </span>
                                  <span className="absolute bottom-0 left-7 md:left-8 ">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                                      width={37}
                                      height={37}
                                      alt="Flag"
                                      className="w-3 md:w-4 h-3 md:h-4  rounded-full"
                                    />
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="text-gray-500 ">
                        <p className="text-sm text-gray-400 ">5m</p>
                      </div>
                    </div>

                    <div className="bg-white flex justify-between first:border-t border-b last:border-0 py-4 w-full">
                      <div className="">
                        <div className="flex gap-2 w-full">
                          <div>
                            <Image
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                              width={37}
                              height={37}
                              alt="Arlene McCoy"
                              className="min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full"
                            />
                          </div>
                          <div className="">
                            <button className="text-black bg-[#d8d4d4] px-2 w-max h-[22px] flex items-center justify-center text-xs rounded-md">
                              Mix Event
                            </button>
                            <p className="font-bold text-bold xs:text-sm text-xs mt-1">
                              Made Monkey - A****** +3 other Joint Club Dance Event
                              on 31st December
                            </p>
                            <div className="flex mt-2 relative">
                              <div>
                                <div className="relative mt-1">
                                  <span className="">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                                      width={37}
                                      height={37}
                                      alt="Arlene McCoy"
                                      className="min-w-10 md:min-w-14 min-h-10 md:min-h-14 rounded-full border border-white backdrop-blur-sm blur-[1px]"
                                    />
                                  </span>
                                  <span className="absolute bottom-0 left-7 md:left-8">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                                      width={37}
                                      height={37}
                                      alt="Flag"
                                      className="w-3 md:w-4 h-3 md:h-4 rounded-full"
                                    />
                                  </span>
                                </div>
                              </div>
                              <div className="absolute bottom-[0%] left-[9%]">
                                <div className="relative mt-1">
                                  <span className="">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                                      width={37}
                                      height={37}
                                      alt="Arlene McCoy"
                                      className="min-w-10 md:min-w-14 min-h-10 md:min-h-14 rounded-full border border-white backdrop-blur-sm blur-[1px]"
                                    />
                                  </span>
                                  <span className="absolute bottom-0 left-7 md:left-8 ">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                                      width={37}
                                      height={37}
                                      alt="Flag"
                                      className="w-3 md:w-4 h-3 md:h-4 rounded-full"
                                    />
                                  </span>
                                </div>
                              </div>
                              <div className="absolute bottom-[0%] left-[18%]">
                                <div className="relative mt-1">
                                  <span className="">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                                      width={37}
                                      height={37}
                                      alt="Arlene McCoy"
                                      className="min-w-10 md:min-w-14 min-h-10 md:min-h-14 rounded-full border border-white backdrop-blur-sm blur-[1px]"
                                    />
                                  </span>
                                  <span className="absolute bottom-0 left-7 md:left-8 ">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                                      width={37}
                                      height={37}
                                      alt="Flag"
                                      className="w-3 md:w-4 h-3 md:h-4 rounded-full"
                                    />
                                  </span>
                                </div>
                              </div>
                              <div className="absolute bottom-[0%] left-[27%]">
                                <div className="relative mt-1">
                                  <span className="">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                                      width={37}
                                      height={37}
                                      alt="Arlene McCoy"
                                      className="min-w-10 md:min-w-14 min-h-10 md:min-h-14 rounded-full border border-white backdrop-blur-sm blur-[1px]"
                                    />
                                  </span>
                                  <span className="absolute bottom-0 left-7 md:left-8 ">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                                      width={37}
                                      height={37}
                                      alt="Flag"
                                      className="w-3 md:w-4 h-3 md:h-4 rounded-full"
                                    />
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="text-gray-500 ">
                        <p className="text-sm text-gray-400 ">5m</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Property Updates Content */}
                {activeTab === 'Property Updates' && (
                  <div className="mt-6 w-full ">
                    <div className="bg-white flex justify-between first:border-t border-b last:border-0 w-full py-4">
                      <div className="flex flex-col justify-around w-full">
                        <div className="flex gap-2">
                          <div>
                            <Image
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                              width={37}
                              height={37}
                              alt="Arlene McCoy"
                              className="min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full"
                            />
                          </div>
                          <div>
                            <button className="text-black bg-[#d8d4d4] px-2 w-max h-[22px] flex items-center justify-center text-xs rounded-md">
                              Mix Mate
                            </button>
                            <p className="font-semibold text-gray-400 xs:text-sm text-xs mt-1">
                              <span className="text-black xs:text-sm text-xs font-bold">
                                {" "}
                                Priyanka
                              </span>{" "}
                              - You got your first like!
                            </p>
                          </div>
                        </div>
                        <div className="px-12">
                          <div className="flex gap-2 mt-2 p-2 border rounded-lg md:w-[50%] w-full">
                            <div className="relative mt-1">
                              <span className="">
                                <Image
                                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                                  width={37}
                                  height={37}
                                  alt="Arlene McCoy"
                                  className="min-w-12 md:min-w-14 min-h-12 md:min-h-14 rounded-full"
                                />
                              </span>
                              <span className="absolute bottom-5 md:bottom-2 left-8 ">
                                <Image
                                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                                  width={37}
                                  height={37}
                                  alt="Flag"
                                  className="w-3 md:w-4 h-3 md:h-4 rounded-full"
                                />
                              </span>
                            </div>

                            <div className="h-[76px] w-full">
                              <div className="mt-2">
                                <p className="font-semibold text-xs md:text-sm ">Elena Sah</p>
                                <div className="flex gap-x-0.5">
                                  <button className="  flex justify-center items-center">
                                    <BiSolidHeartCircle className="text-[#66E6D9] text-base  md:text-lg" />
                                  </button>
                                  <p className="text-gray-600 text-xs md:text-sm ">
                                    {" "}
                                    44 Likes
                                  </p>
                                </div>
                              </div>

                              <div className="relative ">
                                <button className="whitespace-nowrap absolute right-2 -bottom-9 md:-bottom-7 w-[66px] h-[20px] md:w-[76px] md:h-[26px] text-[#66E6D9] border border-[#66E6D9]  rounded-lg text-xxs md:text-xs">
                                  View Profile
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="text-gray-500 flex flex-col justify-center items-center ">
                        <p className="text-sm text-gray-400">5m</p>
                        <div className="relative mt-1">
                          <span className="w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center">
                            <MdOutlineChat className="text-sm text-white" />
                          </span>
                          <span className="h-3.5 w-3.5 bg-[#FF7F50] rounded-full text-[10px] absolute -top-1 left-4 text-center text-black flex items-center justify-center leading-none">
                            1
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-white flex justify-between first:border-t border-b last:border-0 py-4 w-full">
                      <div className="flex flex-col justify-evenly w-full">
                        <div className="flex gap-2 w-full">
                          <div>
                            <Image
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                              width={37}
                              height={37}
                              alt="Arlene McCoy"
                              className="min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full"
                            />
                          </div>
                          <div>
                            <button className="text-black bg-[#d8d4d4] px-2 w-max h-[22px] flex items-center justify-center text-xs rounded-md">
                              Mix Event
                            </button>
                            <p className="font-semibold text-black text-xs xs:text-sm mt-1">
                              <span className="text-black xs:text-sm text-xs font-bold">
                                {" "}
                                Mad Monkey
                              </span>{" "}
                              - Created New Event - Club Dance
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center mt-2 px-6 md:px-12 gap-x-2">
                          <div>
                            <button className="h-5 md:h-7 w-5 md:w-7 rounded-lg bg-[#D9F9F6] border-2 border-[#66E6D9] text-[#56edde] flex items-center justify-center">
                              <FaCalendarDays className="text-[#56edde] text-xs md:text-base" />
                            </button>
                          </div>
                          <div>
                            <p className="text-xxs md:text-xs">14 December, 2021</p>
                            <p className="text-xxs md:text-xs text-gray-500">
                              Tuesday, 4:00PM - 9:00PM
                            </p>
                          </div>

                          <div>
                            <button className="h-5 md:h-7 w-5 md:w-7 rounded-lg bg-[#D9F9F6] border-2 border-[#66E6D9] text-[#56edde] flex items-center justify-center">
                              <PiMapPinLineFill className="text-[#56edde] text-xs md:text-base" />
                            </button>
                          </div>
                          <div>
                            <p className="text-xxs md:text-xs">Gala Covention Center</p>
                            <p className="text-xxs md:text-xs text-gray-500">
                              36 Guild Street London, UK
                            </p>
                          </div>
                        </div>
                        <div className="pl-14 pe-7 pt-2">
                          <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`}
                            width={37}
                            height={37}
                            alt="Arlene McCoy"
                            className="w-[141px] h-[69px] md:h-[89px] "
                          />
                        </div>
                      </div>
                      <div className="text-gray-500 ">
                        <p className="text-sm text-gray-400 ">5m</p>
                      </div>
                    </div>

                    <div className="bg-white flex justify-between first:border-t border-b last:border-0 py-4 w-full">
                      <div className="">
                        <div className="flex gap-2 w-full">
                          <div>
                            <Image
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                              width={37}
                              height={37}
                              alt="Arlene McCoy"
                              className="min-w-11 min-h-11 rounded-full"
                            />
                          </div>
                          <div className="space-y-1.5 md:space-y-2">
                            <button className="text-black bg-[#d8d4d4] px-2 w-max h-[22px] flex items-center justify-center text-xs rounded-md">
                              Mix Creator
                            </button>
                            <p className="font-semibold text-black text-xs xs:text-sm mt-1">
                              <span className="text-black xs:text-sm text-xs font-bold">
                                {" "}
                                Mad Monkey - Mix
                              </span>{" "}
                              <span className="text-[#56edde]">Creator</span>
                              Sent offer request to Ayush Jain
                            </p>
                            <p className="text-black text-xxs md:text-sm font-bold">
                              Service Offered : Story Instagram
                            </p>
                            <button className="whitespace-nowrap w-[66px] md:w-[76px] h-[20px] md:h-[26px] text-[#66E6D9] border border-[#66E6D9]  rounded-lg text-xxs md:text-xs ">
                              View Offer
                            </button>
                          </div>
                        </div>
                      </div>
                      <div className="text-gray-500 ">
                        <p className="text-sm text-gray-400 ">5m</p>
                      </div>
                    </div>

                    <div className="bg-white flex justify-between first:border-t border-b last:border-0 py-4 w-full">
                      <div className="">
                        <div className="flex gap-2 w-full">
                          <div>
                            <Image
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                              width={37}
                              height={37}
                              alt="Arlene McCoy"
                              className="min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full"
                            />
                          </div>
                          <div className="space-y-1.5 md:space-y-2">
                            <button className="text-black bg-[#d8d4d4] px-2 w-max h-[22px] flex items-center justify-center text-xs rounded-md">
                              Hostel
                            </button>
                            <p className="font-semibold text-gray-400 xs:text-sm text-xs mt-1">
                              <span className="text-black xs:text-sm text-xs font-bold">
                                {" "}
                                Hostel Mumbai
                              </span>{" "}
                              - Happy to serve your Group Checkin
                            </p>
                            <div className="flex mt-2 relative">
                              <div>
                                <div className="relative mt-1">
                                  <span className="">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                                      width={37}
                                      height={37}
                                      alt="Arlene McCoy"
                                      className="min-w-10 md:min-w-14 min-h-10 md:min-h-14 rounded-full border border-white backdrop-blur-sm blur-[1px]"
                                    />
                                  </span>
                                  <span className="absolute bottom-0 left-7 md:left-8">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                                      width={37}
                                      height={37}
                                      alt="Flag"
                                      className="w-3 md:w-4 h-3 md:h-4 rounded-full"
                                    />
                                  </span>
                                </div>
                              </div>
                              <div className="absolute bottom-[0%] left-[12%]">
                                <div className="relative mt-1">
                                  <span className="">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                                      width={37}
                                      height={37}
                                      alt="Arlene McCoy"
                                      className="min-w-10 md:min-w-14 min-h-10 md:min-h-14 rounded-full border border-white backdrop-blur-sm blur-[1px]"
                                    />
                                  </span>
                                  <span className="absolute bottom-0 left-7 md:left-8 ">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                                      width={37}
                                      height={37}
                                      alt="Flag"
                                      className="w-3 md:w-4 h-3 md:h-4  rounded-full"
                                    />
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="text-gray-500 ">
                        <p className="text-sm text-gray-400 ">5m</p>
                      </div>
                    </div>

                    <div className="bg-white flex justify-between first:border-t border-b last:border-0 py-4 w-full">
                      <div className="">
                        <div className="flex gap-2 w-full">
                          <div>
                            <Image
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                              width={37}
                              height={37}
                              alt="Arlene McCoy"
                              className="min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full"
                            />
                          </div>
                          <div className="">
                            <button className="text-black bg-[#d8d4d4] px-2 w-max h-[22px] flex items-center justify-center text-xs rounded-md">
                              Mix Event
                            </button>
                            <p className="font-bold text-bold xs:text-sm text-xs mt-1">
                              Made Monkey - A****** +3 other Joint Club Dance Event
                              on 31st December
                            </p>
                            <div className="flex mt-2 relative">
                              <div>
                                <div className="relative mt-1">
                                  <span className="">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                                      width={37}
                                      height={37}
                                      alt="Arlene McCoy"
                                      className="min-w-10 md:min-w-14 min-h-10 md:min-h-14 rounded-full border border-white backdrop-blur-sm blur-[1px]"
                                    />
                                  </span>
                                  <span className="absolute bottom-0 left-7 md:left-8">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                                      width={37}
                                      height={37}
                                      alt="Flag"
                                      className="w-3 md:w-4 h-3 md:h-4 rounded-full"
                                    />
                                  </span>
                                </div>
                              </div>
                              <div className="absolute bottom-[0%] left-[9%]">
                                <div className="relative mt-1">
                                  <span className="">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                                      width={37}
                                      height={37}
                                      alt="Arlene McCoy"
                                      className="min-w-10 md:min-w-14 min-h-10 md:min-h-14 rounded-full border border-white backdrop-blur-sm blur-[1px]"
                                    />
                                  </span>
                                  <span className="absolute bottom-0 left-7 md:left-8 ">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                                      width={37}
                                      height={37}
                                      alt="Flag"
                                      className="w-3 md:w-4 h-3 md:h-4 rounded-full"
                                    />
                                  </span>
                                </div>
                              </div>
                              <div className="absolute bottom-[0%] left-[18%]">
                                <div className="relative mt-1">
                                  <span className="">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                                      width={37}
                                      height={37}
                                      alt="Arlene McCoy"
                                      className="min-w-10 md:min-w-14 min-h-10 md:min-h-14 rounded-full border border-white backdrop-blur-sm blur-[1px]"
                                    />
                                  </span>
                                  <span className="absolute bottom-0 left-7 md:left-8 ">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                                      width={37}
                                      height={37}
                                      alt="Flag"
                                      className="w-3 md:w-4 h-3 md:h-4 rounded-full"
                                    />
                                  </span>
                                </div>
                              </div>
                              <div className="absolute bottom-[0%] left-[27%]">
                                <div className="relative mt-1">
                                  <span className="">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                                      width={37}
                                      height={37}
                                      alt="Arlene McCoy"
                                      className="min-w-10 md:min-w-14 min-h-10 md:min-h-14 rounded-full border border-white backdrop-blur-sm blur-[1px]"
                                    />
                                  </span>
                                  <span className="absolute bottom-0 left-7 md:left-8 ">
                                    <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`}
                                      width={37}
                                      height={37}
                                      alt="Flag"
                                      className="w-3 md:w-4 h-3 md:h-4 rounded-full"
                                    />
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="text-gray-500 ">
                        <p className="text-sm text-gray-400 ">5m</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Social Interactions Content */}
                {activeTab === 'Social Interactions' && (
                  <div className="mt-6 w-full ">
                    <div className="bg-white flex justify-between first:border-t border-b last:border-0 py-4 w-full">
                      <div className="flex flex-col justify-evenly w-full">
                        <div className="flex gap-2 w-full">
                          <div>
                            <Image
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                              width={37}
                              height={37}
                              alt="Arlene McCoy"
                              className="min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full"
                            />
                          </div>
                          <div>
                            <button className="text-black bg-[#d8d4d4] px-2 w-max h-[22px] flex items-center justify-center text-xs rounded-md">
                              Social Interactions
                            </button>
                            <p className="font-semibold text-gray-400 xs:text-sm text-xs mt-1">
                              <span className="text-black xs:text-sm text-xs font-bold">
                                {" "}
                                Priyanka
                              </span>{" "}
                              - Just Posted a Video !
                            </p>
                          </div>
                        </div>
                        <div className="pl-14 pe-7 pt-2">
                          <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/video-post.png`}
                            width={37}
                            height={37}
                            alt="Arlene McCoy" />
                        </div>
                        <div className="flex gap-3 items-center mt-4">
                          <div className="flex gap-3 items-center cursor-pointer">
                              <span className="min-w-7 min-h-7 border rounded-full flex items-center justify-center">
                                 <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/vibe.svg`}
                            width={20}
                            height={20}
                            alt="Vibe"
                            className=""
                          />                           
                              </span>
                              <p className="text-xs font-roboto">Vibe</p>
                          </div>
                          <div className="flex gap-3 items-center cursor-pointer">
                              <span className="min-w-7 min-h-7 border rounded-full flex items-center justify-center">
                                      <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/shoutout.svg`}
                            width={20}
                            height={20}
                            alt="Vibe"
                            className=""
                          />                           
                                                          
                              </span>
                              <p className="text-xs font-roboto">Shoutout</p>
                          </div>
                          <div className="flex gap-3 items-center cursor-pointer group" onClick={() => setIsShareModal(true)}>
                              <span className="min-w-7 min-h-7 border rounded-full flex items-center justify-center transition-colors duration-200 group-hover:bg-black group-hover:text-white">
                                <SlShare size={16} />
                              </span>
                              <p className="text-xs font-roboto transition-colors duration-200 group-hover:font-semibold">
                                Share
                              </p>
                          </div>
                        </div>
                        <div className="[&>p]:font-roboto [&>p]:xs:text-sm [&>p]:text-xs [&>p]:font-normal [&>p]:mb-1 mt-3">
                          <p>1.5K users vibing in sync !</p>
                          <p className="text-[#AAACAE]"><span className="text-black">Madmonkey hostels :</span> pov your camera roll if you actually booked the trio.</p>
                        </div>
                      </div>
                      <div className="text-gray-500 ">
                        <p className="text-sm text-gray-400 ">5m</p>
                      </div>
                    </div>
                    <div className="bg-white flex justify-between first:border-t border-b last:border-0 py-4 w-full">
                      <div className="flex flex-col justify-evenly w-full">
                        <div className="flex gap-2 w-full">
                          <div>
                            <Image
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                              width={37}
                              height={37}
                              alt="Arlene McCoy"
                              className="min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full"
                            />
                          </div>
                          <div>
                            <button className="text-black bg-[#d8d4d4] px-2 w-max h-[22px] flex items-center justify-center text-xs rounded-md">
                              Social Interactions
                            </button>
                            <p className="font-semibold text-gray-400 xs:text-sm text-xs mt-1">
                              <span className="text-black xs:text-sm text-xs font-bold">
                                {" "}
                                Priyanka
                              </span>{" "}
                              - Just Posted a Photo !
                            </p>
                          </div>
                        </div>
                        <div className="pl-14 pe-7 pt-2">
                          <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/photo-post.png`}
                            width={37}
                            height={37}
                            alt="Arlene McCoy"
                            className="w-full aspect-video object-cover mx-auto rounded-[10px]"
                          />
                        </div>
                        <div className="flex gap-3 items-center mt-4">
                          <div className="flex gap-3 items-center cursor-pointer">
                              <span className="min-w-7 min-h-7 border rounded-full flex items-center justify-center">
                                      <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/vibe.svg`}
                            width={20}
                            height={20}
                            alt="Vibe"
                            className=""
                          />                           
                                                          
                              </span>
                              <p className="text-xs font-roboto">Vibe</p>
                          </div>
                          <div className="flex gap-3 items-center cursor-pointer">
                              <span className="min-w-7 min-h-7 border rounded-full flex items-center justify-center">
                                      <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/shoutout.svg`}
                            width={20}
                            height={20}
                            alt="Vibe"
                            className=""
                          />                           
                                                        
                              </span>
                              <p className="text-xs font-roboto">Shoutout</p>
                          </div>
                          <div className="flex gap-3 items-center cursor-pointer group" onClick={() => setIsShareModal(true)}>
                              <span className="min-w-7 min-h-7 border rounded-full flex items-center justify-center transition-colors duration-200 group-hover:bg-black group-hover:text-white">
                                <SlShare size={16} />
                              </span>
                              <p className="text-xs font-roboto transition-colors duration-200 group-hover:font-semibold">
                                Share
                              </p>
                          </div>
                        </div>
                        <div className="[&>p]:font-roboto [&>p]:xs:text-sm [&>p]:text-xs [&>p]:font-normal [&>p]:mb-1 mt-3">
                          <p>1.5K users vibing in sync !</p>
                          <p className="text-[#AAACAE]"><span className="text-black">Madmonkey hostels :</span> pov your camera roll if you actually booked the trio.</p>
                        </div>
                      </div>
                      <div className="text-gray-500 ">
                        <p className="text-sm text-gray-400 ">5m</p>
                      </div>
                    </div>
                    <div className="bg-white flex justify-between first:border-t border-b last:border-0 py-4 w-full">
                      <div className="flex flex-col justify-evenly w-full">
                        <div className="flex gap-2 w-full">
                          <div>
                            <Image
                              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Profile1.png`}
                              width={37}
                              height={37}
                              alt="Arlene McCoy"
                              className="min-w-9 min-h-9 md:min-w-11 md:min-h-11 rounded-full"
                            />
                          </div>
                          <div>
                            <button className="text-black bg-[#d8d4d4] px-2 w-max h-[22px] flex items-center justify-center text-xs rounded-md">
                              Social Interactions
                            </button>
                            <p className="font-semibold text-gray-400 xs:text-sm text-xs mt-1">
                              <span className="text-black xs:text-sm text-xs font-bold">
                                {" "}
                                Alisha
                              </span>{" "}
                              - Just Posted a Photo !
                            </p>
                          </div>
                        </div>
                        <div className="pl-14 pe-7 pt-2 [&>p]:font-roboto [&>p]:xs:text-sm [&>p]:text-xs [&>p]:font-normal">
                          <p>Just wrapped up an insane stay at Lushy Hostel, Canggu, and the vibes were unreal! 🌴✨ From laid-back mornings by the pool to wild nights that turned strangers into lifelong travel buddies, this place knows how to keep the energy high. 🏄‍♂️🔥</p>
                          <p>The AI-powered Mixdorm Noticeboard connected me with fellow travelers for last-minute bar crawls, epic beach parties, and hidden spots I wouldn’t have found alone! 🍻🌊 Whether it was dancing till sunrise or swapping crazy travel stories under the Bali moon, every moment felt like a movie. 🎶💃</p>
                          <p>If you’re in Canggu, don’t miss the hostel parties & nightlife—trust me, it’s a game-changer! Who else has been here? Drop your wildest hostel night stories below! ⬇️ 🎤 #VibeOnMixdorm #LushyCanggu #TravelMore</p>
                        </div>
                        <div className="flex gap-3 items-center mt-4">
                          <div className="flex gap-3 items-center cursor-pointer">
                              <span className="min-w-7 min-h-7 border rounded-full flex items-center justify-center">
                                      <Image
                                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/vibe.svg`}
                                      width={20}
                                      height={20}
                                      alt="Vibe"
                                      className="" />                           
                                                        
                              </span>
                              <p className="text-xs font-roboto">Vibe</p>
                          </div>
                          <div className="flex gap-3 items-center cursor-pointer">
                              <span className="min-w-7 min-h-7 border rounded-full flex items-center justify-center">
                                 <Image
                                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/shoutout.svg`}
                                    width={20}
                                    height={20}
                                    alt="Vibe"
                                    className=""
                                  />                           
                                                          
                              </span>
                              <p className="text-xs font-roboto">Shoutout</p>
                          </div>
                          <div className="flex gap-3 items-center cursor-pointer group" onClick={() => setIsShareModal(true)}>
                              <span className="min-w-7 min-h-7 border rounded-full flex items-center justify-center transition-colors duration-200 group-hover:bg-black group-hover:text-white">
                                <SlShare size={16} />
                              </span>
                              <p className="text-xs font-roboto transition-colors duration-200 group-hover:font-semibold">
                                Share
                              </p>
                          </div>
                        </div>
                        <div className="[&>p]:font-roboto [&>p]:xs:text-sm [&>p]:text-xs [&>p]:font-normal [&>p]:mb-1 mt-3">
                          <p>1.5K users vibing in sync !</p>
                        </div>
                      </div>
                      <div className="text-gray-500 ">
                        <p className="text-sm text-gray-400 ">5m</p>
                      </div>
                    </div>
                  </div>
                )}
                
              </div>
            </div>
          </div>
          <div className="col-span-12 sm:col-span-3 lg:block hidden lg:col-span-3 order-2 lg:order-3">
            <div className="sticky top-[130px]">
              <div className="w-full">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/noticeD1.png`}
                  width={40}
                  height={40}
                  alt=""
                  className="w-full sm:h-full h-[150px] object-cover rounded-2xl"
                />
              
              </div>
              <div className="w-full mt-3">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/noticeD2.png`}
                  width={40}
                  height={40}
                  alt=""
                  className="w-full sm:h-full h-[150px] object-cover rounded-2xl"
                />
                <div className="absolute bg-[#0000008F] rounded-[10px] left-[3%] bottom-0 p-2 w-[94%] mb-3">
                  <h2 className="md:text-4xl text-2xl text-center font-bold text-[#40E0D0] pt-2">
                    <span className="text-white">Mix</span>Shuffle
                  </h2>
                  <p className="mb-0 text-center md:text-sm text-xs text-white font-medium font-manrope leading-tight mt-2">Stay More than Six days and get <br></br> Premium membership </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Post Modal */}
      {isPostModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start pt-44 justify-center z-50">
          <div className="bg-white xs:p-10 p-7 rounded-[30px] lg:max-w-2xl sm:max-w-xl xs:max-w-lg max-w-[90%] w-full relative">
            <h2 className="md:text-[27px] sm:text-md text-[20px] font-bold mb-4 leading-tight	">Post your travel moments on the live <br className="xs:block hidden"></br> <span className="text-[#40E0D0]">AI</span>-Powered <span className="text-[#40E0D0]">Notice</span>board!</h2>
            <button onClick={() => setIsPostModal(false)} className="absolute top-3 right-4 hover:text-gray-600 text-black">
              <X size={30}/>
            </button>
            <div className="xs:my-8 my-5">
              <form>
                <label className="font-manrope font-semibold text-base">Add a Caption</label>
                <div className="border rounded-full mt-2 flex items-center">
                  <input className="p-2 pl-4 w-full md:h-[52px] h-[44px] font-manrope font-light focus:outline-none" type="text" placeholder="Write here ! "/>
                  <button className="bg-black md:min-w-12 min-w-[44px] md:min-h-12 min-h-[44px] rounded-full flex items-center justify-center">
                    <IoPaperPlane color="#fff" size={16}/>
                  </button>
                </div>
              </form>
            </div>
            <div className="flex justify-between">
              <div className="flex xs:gap-2 gap-1 items-center">
                <span className="md:h-[74px] md:w-[74px] xs:h-[54px] xs:w-[54px] h-[40px] w-[40px] rounded-full bg-[#40E0D0] flex items-center justify-center">
                    <Image width={20} height={20} className="md:w-auto md:h-auto w-[50%]" src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/video-icon.png`} />
                </span>
                <p className="mb-0 md:text-xl xs:text-lg text-sm font-manrope text-black font-semibold">Video</p>
              </div>
              <div className="flex xs:gap-2 gap-1 items-center">
                <span className="md:h-[74px] md:w-[74px] xs:h-[54px] xs:w-[54px] h-[40px] w-[40px] rounded-full bg-[#40E0D0] flex items-center justify-center">
                      <Image width={20} height={20} className="md:w-auto md:h-auto w-[50%]" src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/photos-icon.png`} />
                </span>
                <p className="mb-0 md:text-xl xs:text-lg text-sm font-manrope text-black font-semibold">Photos</p>
              </div>
              <div className="flex xs:gap-2 gap-1 items-center">
                <span className="md:h-[74px] md:w-[74px] xs:h-[54px] xs:w-[54px] h-[40px] w-[40px] rounded-full bg-[#40E0D0] flex items-center justify-center">
                  <Image width={20} height={20} className="md:w-auto md:h-auto w-[50%]" src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/exp-icon.png`} />
                </span>
                <p className="mb-0 md:text-xl xs:text-lg text-sm font-manrope text-black font-semibold">Experience</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Share Modal */}
      {isShareModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-start pt-44 justify-center z-50">
          <div className="bg-white xs:p-10 p-7 rounded-[30px] lg:max-w-2xl sm:max-w-xl xs:max-w-lg max-w-[90%] w-full relative">
            <h2 className="md:text-[27px] sm:text-md text-[20px] font-bold mb-4 leading-tight	text-center">Share</h2>
            <button onClick={() => setIsShareModal(false)} className="absolute top-3 right-4 hover:text-gray-600 text-black">
              <X size={30}/>
            </button>
            <div className="xs:my-8 my-5">
              <form>
                <div className="border rounded-full mt-2 flex items-center">
                  <input className="p-2 pl-4 w-full md:h-[52px] h-[44px] font-manrope font-light focus:outline-none" type="text" placeholder="Search"/>
                </div>
              </form>
            </div>
            <div className="grid grid-cols-12 justify-between gap-y-4">
              <div className="col-span-3 text-center cursor-pointer hover:shadow-lg py-2 rounded-md hover:bg-slate-100">
                <span className="mx-auto md:h-[74px] md:w-[74px] xs:h-[54px] xs:w-[54px] h-[40px] w-[40px] rounded-full bg-[#40E0D0] flex items-center justify-center">
                        <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/copy-link.svg`}
                            width={20}
                            height={20}
                            alt="Vibe"
                            className=""
                          />                           
                             
                </span>
                <p className="mt-2 mb-0 md:text-xl xs:text-lg text-sm font-manrope text-black font-semibold">Copy link</p>
              </div>
              <div className="col-span-3 text-center cursor-pointer hover:shadow-lg py-2 rounded-md hover:bg-slate-100">
                <span className="mx-auto md:h-[74px] md:w-[74px] xs:h-[54px] xs:w-[54px] h-[40px] w-[40px] rounded-full bg-[#40E0D0] flex items-center justify-center">
                       <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/facebook.svg`}
                            width={20}
                            height={20}
                            alt="Vibe"
                            className=""
                          />                           
                             
                </span>
                <p className="mt-2 mb-0 md:text-xl xs:text-lg text-sm font-manrope text-black font-semibold">Facebook</p>
              </div>
              <div className="col-span-3 text-center cursor-pointer hover:shadow-lg py-2 rounded-md hover:bg-slate-100">
                <span className="mx-auto md:h-[74px] md:w-[74px] xs:h-[54px] xs:w-[54px] h-[40px] w-[40px] rounded-full bg-[#40E0D0] flex items-center justify-center">
                        <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/messenger.svg`}
                            width={20}
                            height={20}
                            alt="Vibe"
                            className=""
                          />                           
                             
                </span>
                <p className="mt-2 mb-0 md:text-xl xs:text-lg text-sm font-manrope text-black font-semibold">Messenger</p>
              </div>
              <div className="col-span-3 text-center cursor-pointer hover:shadow-lg py-2 rounded-md hover:bg-slate-100">
                <span className="mx-auto md:h-[74px] md:w-[74px] xs:h-[54px] xs:w-[54px] h-[40px] w-[40px] rounded-full bg-[#40E0D0] flex items-center justify-center">
                        <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/whatsApp.svg`}
                            width={20}
                            height={20}
                            alt="Vibe"
                            className=""
                          />                           
                             
                </span>
                <p className="mt-2 mb-0 md:text-xl xs:text-lg text-sm font-manrope text-black font-semibold">WhatsApp</p>
              </div>
              <div className="col-span-3 text-center cursor-pointer hover:shadow-lg py-2 rounded-md hover:bg-slate-100">
                <span className="mx-auto md:h-[74px] md:w-[74px] xs:h-[54px] xs:w-[54px] h-[40px] w-[40px] rounded-full bg-[#40E0D0] flex items-center justify-center">
                        <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/email.svg`}
                            width={20}
                            height={20}
                            alt="Vibe"
                            className=""
                          />                           
                             
                </span>
                <p className="mt-2 mb-0 md:text-xl xs:text-lg text-sm font-manrope text-black font-semibold">Email</p>
              </div>
              <div className="col-span-3 text-center cursor-pointer hover:shadow-lg py-2 rounded-md hover:bg-slate-100">
                <span className="mx-auto md:h-[74px] md:w-[74px] xs:h-[54px] xs:w-[54px] h-[40px] w-[40px] rounded-full bg-[#40E0D0] flex items-center justify-center">
                        <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Threads.svg`}
                            width={20}
                            height={20}
                            alt="Vibe"
                            className=""
                          />                           
                             
                </span>
                <p className="mt-2 mb-0 md:text-xl xs:text-lg text-sm font-manrope text-black font-semibold">Threads</p>
              </div>
              <div className="col-span-3 text-center cursor-pointer hover:shadow-lg py-2 rounded-md hover:bg-slate-100">
                <span className="mx-auto md:h-[74px] md:w-[74px] xs:h-[54px] xs:w-[54px] h-[40px] w-[40px] rounded-full bg-[#40E0D0] flex items-center justify-center">
                        <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/x.svg`}
                            width={20}
                            height={20}
                            alt="Vibe"
                            className=""
                          />                           
                             
                </span>
                <p className="mt-2 mb-0 md:text-xl xs:text-lg text-sm font-manrope text-black font-semibold">X</p>
              </div>
              <div className="col-span-3 text-center cursor-pointer hover:shadow-lg py-2 rounded-md hover:bg-slate-100">
                <span className="mx-auto md:h-[74px] md:w-[74px] xs:h-[54px] xs:w-[54px] h-[40px] w-[40px] rounded-full bg-[#40E0D0] flex items-center justify-center">
                        <Image
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/share-all.svg`}
                            width={20}
                            height={20}
                            alt="Vibe"
                            className=""
                          />                           
                             
                </span>
                <p className="mt-2 mb-0 md:text-xl xs:text-lg text-sm font-manrope text-black font-semibold">Share all</p>
              </div>
            </div>
          </div>
        </div>
      )}

    </>
  );
};

export default NoticeBoardDetail;
