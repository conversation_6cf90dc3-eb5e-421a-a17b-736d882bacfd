import Image from "next/image";
import { FaRegUser } from "react-icons/fa";
import {
  MdCardMembership,
  MdLogout,
  MdOutlineTravelExplore,
} from "react-icons/md";
import { GrEdit } from "react-icons/gr";
import { IoWalletOutline } from "react-icons/io5";

const menuItems = [
  { label: "My Profile", icon: <FaRegUser className="text-xl" /> },
  { label: "Edit Details", icon: <GrEdit className="text-xl" /> },
  { label: "Membership", icon: <MdCardMembership className="text-xl" /> },
  { label: "My Trips", icon: <MdOutlineTravelExplore className="text-xl" /> },
  { label: "My Wallet", icon: <IoWalletOutline className="text-xl" /> },
  { label: "Logout", icon: <MdLogout className="text-xl" /> },
];
export default function Home() {
  return (
    <div className="container my-10">
      <div className="min-h-screen flex bg-white font-sans flex-col md:flex-row">
        {/* Sidebar */}
        <aside className="w-[100%] md:w-72 p-4 space-y-4 border-r-0  md:border-r border-gray-300">
          {menuItems.map(({ label, icon }) => (
            <button
              key={label}
              className={`w-full flex items-center gap-3 px-3 py-3 text-left rounded-full font-normal text-base ${
                label === "Membership"
                  ? "bg-primary-blue text-black"
                  : "bg-[#D9F9F6] hover:bg-primary-blue"
              }`}
            >
              {icon}
              {label}
            </button>
          ))}
        </aside>

        {/* Main Content */}
        <main className="flex-1 px-8 py-4">
          <h1 className="text-2xl font-bold mb-1">
            Unlockkkkk the Ultimate Travel Experience with{" "}
            <span className="text-primary-blue">Mix Premium!</span>
          </h1>
          <p className="mb-6 text-base">
            Access exclusive features and make your journey smarter and
            smoother.
          </p>

          {/* Promo Banner */}
          <div
            className="bg-transparent bg-cover bg-center min-h-[400px] rounded-2xl px-6 pt-2 flex justify-between items-center mb-8 py-8 lg:py-0"
            style={{
              backgroundImage: `url(${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/membership-bg.jpeg)`,
            }}
          >
            <div className="flex flex-col gap-y-8 lg:gap-y-12 xl:gap-y-20">
              <div className="">
                <h2 className="text-xl xl:text-3xl font-semibold mb-2">
                  Unlock the Real Hostel <br className="hidden md:flex" />{" "}
                  Experience with Mix <br className="hidden md:flex" />{" "}
                  Membership
                </h2>
                <p className="text-gray-500 mb-4 text-sm xl:text-base">
                  Match. Split. Ride. Party. All from your phone.
                </p>
              </div>
              <div className="flex gap-2 flex-wrap">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/google-play.png`}
                  alt="Google Play"
                  className="h-14 w-36 xl:w-44"
                  width={120}
                  height={40}
                />
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/app-store.png`}
                  alt="App Store"
                  className="h-14 w-36 xl:w-44"
                  width={120}
                  height={40}
                />
              </div>
            </div>
            <div className="hidden lg:flex w-[43%] relative">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mockup1.png`}
                alt="App Mockup"
                className="h-[240px] xl:h-[350px] w-[180px] xl:w-[270px] z-20  "
                width={230}
                height={370}
              />
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mockup2.png`}
                alt="App Mockup"
                className="h-[200px] xl:h-[300px] w-[180px] xl:w-[270px] z-10 absolute left-24 xl:left-36 top-10 xl:top-12  "
                width={230}
                height={320}
              />
            </div>
          </div>

          {/* Feature List */}
          <h3 className="text-[22px] font-manrope font-bold my-4">
            Feature List
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[
              {
                title: "MixMate (Travel Tinder)",
                desc: "Match with travelers in your hostel",
                tag: "Only On Mobile App",
                color: "bg-pink-100",
                icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/MixMate.png`,
              },
              {
                title: "MixSplit (Split Bills)",
                desc: "Split rides, meals, and rooms with hostel buddies",
                tag: "Mobile Exclusive",
                color: "bg-yellow-100",
                icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/MixSplit.png`,
              },
              {
                title: "MixEvent (Join Hostel Events)",
                desc: "See events in your hostel or city",
                tag: "Download App to View",
                color: "bg-pink-200",
                icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/MixEvent.png`,
              },
              {
                title: "MixRide (Share Rides)",
                desc: "Get or share rides to nearby spots",
                tag: "Unlock in App",
                color: "bg-purple-100",
                icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/MixRide.png`,
              },
            ].map((feature, idx) => (
              <div
                key={idx}
                className="border rounded-lg p-4 bg-white shadow-sm h-[371px] flex flex-col justify-around"
              >
                <div>
                  {" "}
                  <Image
                    src={feature.icon}
                    alt={feature.title}
                    height={50}
                    width={50}
                    className="w-14 h-14"
                  />
                  <h4 className="font-semibold text-base lg:text-sm xl:text-base font-manrope h-16 py-4">
                    {feature.title}
                  </h4>
                  <p className=" text-gray-600 text-sm lg:text-xs xl:text-sm font-manrope h-20">
                    {feature.desc}
                  </p>
                </div>
                <div>
                  <span
                    className={` px-1 py-1 text-[15px] lg:text-xxs xl:text-[15px] h-[50px] border rounded-full  flex items-center justify-center whitespace-nowrap
          ${
            idx < 2
              ? "bg-white text-black border-black hover:text-white hover:bg-black"
              : "bg-black text-white border-black hover:text-black hover:bg-white"
          }`}
                  >
                    {feature.tag}
                  </span>
                </div>
              </div>
            ))}
          </div>

          {/* Footer section */}
          <p className="text-[22px] font-manrope font-bold my-6">
            Collecting from Hostel Tag
          </p>
          <div>
            {" "}
            <Image
              src="/membershipAvatar.png"
              alt="App Mockup"
              className="w-[420px] h-[288px] "
              width={230}
              height={320}
            />
          </div>
        </main>
      </div>
    </div>
  );
}
