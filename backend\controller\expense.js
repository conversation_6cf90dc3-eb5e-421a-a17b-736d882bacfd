import groupModel from "../models/group.js";
import userModel from "../models/auth.js";
import Response from "../utills/response.js";
import ActivityLog from "../models/activityLog.js";
import expenseModel from "../models/expense.js";


const addExpense = async (req, res) => {
    try {
      const { description, amount, category, groupId, paidBy, participants } = req.body;
  
      // Validate input
      if (!description || !amount || !groupId || !paidBy || !participants || !Array.isArray(participants)) {
        return Response.BadRequest(res, null, "All fields are required, including participants as an array.");
      }
  
      // Fetch group
      const group = await groupModel.findById(groupId);
      if (!group) {
        return Response.NotFound(res, null, "Group not found.");
      }
  
      // Validate participants
      const users = await userModel.find({ _id: { $in: participants.map((p) => p.userId) } }).select('_id');
      const validUserIds = users.map((user) => user._id.toString());
      const invalidParticipants = participants.filter((p) => !validUserIds.includes(p.userId));
      if (invalidParticipants.length > 0) {
        return Response.NotFound(res, null, `Invalid participant IDs: ${invalidParticipants.map((p) => p.userId).join(', ')}`);
      }
  
      // Calculate individual shares
      const totalParticipants = participants.length;
      const perShare = amount / totalParticipants;
  
      const expense = new expenseModel({
        description,
        amount,
        category,
        group: groupId,
        paidBy,
        participants: participants.map((p) => ({
          userId: p.userId,
          share: perShare,
        })),
      });
      await expense.save();
  
      // Update group with the new expense
      group.expenses.push(expense._id);
      await group.save();
  
      // Update participants' balances in the group
      participants.forEach((participant) => {
        const isPayer = participant.userId === paidBy;
        const amountPaid = isPayer ? amount : 0;
  
        const balanceUpdate = group.balances.find((b) => b.userId.toString() === participant.userId);
        if (balanceUpdate) {
          balanceUpdate.amount += amountPaid - perShare;
        } else {
          group.balances.push({
            userId: participant.userId,
            amount: amountPaid - perShare,
          });
        }
      });
  
      await group.save();
  
      // Log activity
      const paidByUser = await userModel.findById(paidBy);
      const log = new ActivityLog({
        action: "Added expense",
        details: `${paidByUser.name} added an expense "${description}" of amount ${amount}`,
        user: paidBy,
        group: groupId,
      });
      await log.save();
  
      return Response.Created(res, expense, "Expense added successfully.");
    } catch (err) {
      console.error("Error adding expense:", err);
      return Response.InternalServerError(res, null, "Internal server error.");
    }
  };

  const editExpense = async (req, res) => {
    try {
      const { expenseId } = req.params;
      const { description, amount, category, participants } = req.body;
  
      // Fetch the existing expense
      const expense = await expenseModel.findById(expenseId);
      if (!expense) {
        return Response.NotFound(res, null, "Expense not found.");
      }
  
      // Fetch the group associated with the expense
      const group = await groupModel.findById(expense.group);
      if (!group) {
        return Response.NotFound(res, null, "Group not found.");
      }
  
      // Reverse the balances for the old expense
      expense.participants.forEach((participant) => {
        const balanceUpdate = group.balances.find((b) => b.userId.toString() === participant.userId);
        if (balanceUpdate) {
          const isPayer = participant.userId === expense.paidBy.toString();
          const amountPaid = isPayer ? expense.amount : 0;
          balanceUpdate.amount -= amountPaid - participant.share;
        }
      });
  
      // Validate new participants if provided
      if (participants) {
        const users = await userModel.find({ _id: { $in: participants.map((p) => p.userId) } }).select('_id');
        const validUserIds = users.map((user) => user._id.toString());
        const invalidParticipants = participants.filter((p) => !validUserIds.includes(p.userId));
        if (invalidParticipants.length > 0) {
          return Response.NotFound(res, null, `Invalid participant IDs: ${invalidParticipants.map((p) => p.userId).join(', ')}`);
        }
      }
  
      // Calculate new shares
      const newParticipants = participants || expense.participants;
      const totalParticipants = newParticipants.length;
      const perShare = amount / totalParticipants;
  
      // Update expense details
      expense.description = description || expense.description;
      expense.amount = amount || expense.amount;
      expense.category = category || expense.category;
      expense.participants = newParticipants.map((p) => ({
        userId: p.userId,
        share: perShare,
      }));
      await expense.save();
  
      // Update group balances with the new expense details
      newParticipants.forEach((participant) => {
        const isPayer = participant.userId === expense.paidBy.toString();
        const amountPaid = isPayer ? amount : 0;
  
        const balanceUpdate = group.balances.find((b) => b.userId.toString() === participant.userId);
        if (balanceUpdate) {
          balanceUpdate.amount += amountPaid - perShare;
        } else {
          group.balances.push({
            userId: participant.userId,
            amount: amountPaid - perShare,
          });
        }
      });
  
      await group.save();
  
      return Response.OK(res, expense, "Expense updated successfully.");
    } catch (err) {
      console.error("Error editing expense:", err);
      return Response.InternalServerError(res, null, "Internal server error.");
    }
  };

  const deleteExpense = async (req, res) => {
    try {
      const { expenseId } = req.params;
  
      // Fetch the expense
      const expense = await expenseModel.findById(expenseId);
      if (!expense) {
        return Response.NotFound(res, null, "Expense not found.");
      }
  
      // Fetch the group associated with the expense
      const group = await groupModel.findById(expense.group);
      if (!group) {
        return Response.NotFound(res, null, "Group not found.");
      }
  
      // Reverse the balances for the expense
      expense.participants.forEach((participant) => {
        const balanceUpdate = group.balances.find((b) => b.userId.toString() === participant.userId);
        if (balanceUpdate) {
          const isPayer = participant.userId === expense.paidBy.toString();
          const amountPaid = isPayer ? expense.amount : 0;
          balanceUpdate.amount -= amountPaid - participant.share;
        }
      });
  
      // Remove the expense from the group's expenses list
      group.expenses = group.expenses.filter((expId) => expId.toString() !== expenseId);
      await group.save();
  
      // Delete the expense
      await expenseModel.findByIdAndDelete(expenseId);
  
      return Response.OK(res, null, "Expense deleted successfully.");
    } catch (err) {
      console.error("Error deleting expense:", err);
      return Response.InternalServerError(res, null, "Internal server error.");
    }
  };
  
  export { addExpense,editExpense,deleteExpense };
  