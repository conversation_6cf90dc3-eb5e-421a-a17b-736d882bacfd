import React from "react";
import Image from "next/image";


const Creatordetails = () => {
    return (
      <>
        <section className='w-full'>
     
          <div className='w-full  bg-white '>
            <div className='w-full'>
              <div className='grid grid-cols-2 gap-4'>
                <div>
                  <label className='font-bold text-base font-inter'>Creator Name:</label>
                </div>
                <div>
                  <label className='font-semibold text-base font-inter text-[#2c2c2c9d]'><PERSON></label>
                </div>
                <div>
                  <label className='font-bold text-base font-inter'>Location :</label>
                </div>
                <div>
                  <label className='font-semibold text-base font-inter text-[#2c2c2c9d]'>Mumbai, India</label>
                </div>
                <div>
                  <label className='font-bold text-base font-inter'>Services Offer :</label>
                </div>
                <div>
                  <label className='font-semibold text-base font-inter text-[#2c2c2c9d]'>Free</label>
                </div>
                <div>
                  <label className='font-bold text-base font-inter'>Date Of Arrival :</label>
                </div>
                <div>
                  <label className='font-semibold text-base font-inter text-[#2c2c2c9d]'>Sep 25,2019</label>
                </div>
                <div>
                  <label className='font-bold text-base font-inter'>Email :</label>
                </div>
                <div>
                  <label className='font-semibold text-base font-inter text-[#2c2c2c9d]'>Test@</label>
                </div>
                <div>
                  <label className='font-bold text-base font-inter'>Instagram Id :</label>
                </div>
                <div>
                  <label className='font-semibold text-base font-inter text-[#2c2c2c9d]'>Insta</label>
                </div>
                <div>
                  <label className='font-bold text-base font-inter'>Hostel Name :</label>
                </div>
                <div>
                  <label className='font-semibold text-base font-inter text-[#2c2c2c9d]'>Made Monkey</label>
                </div>
                <div>
                  <label className='font-bold text-base font-inter'>Instagram homepage :</label>
                </div>
                <div>
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/MyStay.jpg`}
                    alt='profile'
                    title='profile'
                    width={64}
                    height={32}
                    className='object-cover'
                    loading='lazy'
                  />
                </div>

              </div>
            </div>
          </div>
        </section>
      </>
    );
};
export default Creatordetails;