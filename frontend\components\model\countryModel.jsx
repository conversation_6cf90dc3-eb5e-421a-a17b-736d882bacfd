/* eslint-disable react/no-unknown-property */
import React, { useState, useEffect } from "react";
import Modal from "@mui/material/Modal";
import { IoCloseCircleOutline, IoSearchOutline } from "react-icons/io5";
import Image from "next/image";
import { setItemLocalStorage } from "@/utils/browserSetting";
import countries from "world-countries";

const CountryModal = ({
  openCountryModal,
  handleCloseCountryModal,
  updateCountry,
  onSelectCountry,
}) => {
  const [countryCurrencyCodes, setCountryCurrencyCodes] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredCountries, setFilteredCountries] = useState([]);

  const [loading, setLoading] = useState(true);
  // eslint-disable-next-line no-unused-vars
  const [error, setError] = useState("");

  const style = {
    position: "fixed",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: "100%",
    bgcolor: "background.paper",
    border: "2px solid #000",
    boxShadow: 24,
  };

  // Fetch country and currency data on mount
  useEffect(() => {
    const fetchCountryData = () => {
      try {
        const data = countries
          .map((country) => {
            const currencyCode =
              country?.currencies && Object.keys(country.currencies).length > 0
                ? Object.keys(country.currencies)[0]
                : "";
            if (!currencyCode) return null; // skip if no currency code
            const currencySymbol = country?.currencies
              ? country?.currencies[currencyCode]?.symbol
              : "€";
            const flagCode = country.cca2.toLowerCase(); // Get the country code (ISO 3166-1 alpha-2) for the flag

            const flag =
              // eslint-disable-next-line no-constant-binary-expression
              `https://flagcdn.com/w320/${flagCode}.png` ||
              "https://via.placeholder.com/30x25"; // Default placeholder for flag if missing
            return {
              country: country?.name?.common,
              code: currencyCode,
              symbol: currencySymbol,
              flag: flag,
            };
          })
          .filter(Boolean); // remove nulls
        setCountryCurrencyCodes(data);
        setFilteredCountries(data);
      } catch (error) {
        console.error("Error fetching country data:", error);
        setError("Could not load country data.");
      } finally {
        setLoading(false); // Set loading to false after fetching
      }
    };

    fetchCountryData();
  }, []);

  // Get user's coordinates
  // useEffect(() => {
  //   if (!navigator.geolocation) {
  //     setError("Geolocation is not supported by this browser.");
  //     return;
  //   }

  //   navigator.geolocation.getCurrentPosition(
  //     (position) => {
  //       const { latitude, longitude } = position.coords;
  //       setCoordinates({ latitude, longitude });
  //     },
  //     (error) => {
  //       console.error("Error getting geolocation:", error);
  //       setError("Could not retrieve location.");
  //     }
  //   );
  // }, []);

  // // Fetch currency based on coordinates
  // useEffect(() => {
  //   const fetchCurrency = async (latitude, longitude) => {
  //     const apiKey = "AIzaSyBv_hPcDOPcrTfHnLrFNduHgJWDwv1pjfU"; // Replace with your actual Google API key
  //     try {
  //       const response = await fetch(
  //         `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${apiKey}`
  //       );
  //       const data = await response.json();

  //       if (data.status === "OK") {
  //         const addressComponents = data.results[0].address_components;
  //         const countryComponent = addressComponents.find(component =>
  //           component.types.includes("country")
  //         );

  //         if (countryComponent && countryToCurrency) {
  //           const countryCode = countryComponent.short_name;
  //           const currencyObject = countryToCurrency.find(item => item.country  === countryComponent?.long_name);

  //           const userCurrency = currencyObject ? currencyObject.code : "USD";
  //           console.log("countryCode",userCurrency,countryCode,currencyObject,countryComponent)

  //           setCurrency(userCurrency);
  //           setItemLocalStorage("selectedCountry", currencyObject?.country);
  //           setItemLocalStorage("selectedCurrencyCode", currencyObject?.code);
  //           setItemLocalStorage("selectedCountryFlag", currencyObject?.flag);
  //           setItemLocalStorage("selectedCurrencySymbol", currencyObject?.symbol);
  //           setItemLocalStorage("selectedRoomsData", null);
  //           setSearchTerm("");
  //         } else {
  //           console.error("Country component not found or countryToCurrency is not defined.");
  //         }
  //       } else {
  //         throw new Error("Unable to retrieve location data.");
  //       }
  //     } catch (error) {
  //       console.error("Error fetching currency:", error);
  //       setError("Could not determine currency.");
  //     }
  //   };

  //   if (coordinates) {
  //     fetchCurrency(coordinates.latitude, coordinates.longitude);
  //   }
  // }, [coordinates, countryToCurrency]);

  const handleSearchChange = (e) => {
    const searchValue = e.target.value;
    setSearchTerm(searchValue);

    const filtered = countryCurrencyCodes.filter(
      ({ country, code }) =>
        country.toLowerCase().includes(searchValue.toLowerCase()) ||
        code?.toLowerCase().includes(searchValue.toLowerCase())
    );
    setFilteredCountries(filtered);
  };

  const handleCountrySelect = (country, code, flag, symbol) => {
    setItemLocalStorage("selectedCountry", country);
    setItemLocalStorage("selectedCurrencyCode", code);
    setItemLocalStorage("selectedCountryFlag", flag);
    setItemLocalStorage("selectedCurrencySymbol", symbol);
    setItemLocalStorage("selectedRoomsData", null);
    setSearchTerm("");
    setFilteredCountries(countryCurrencyCodes);
    updateCountry();

    if (onSelectCountry) {
      onSelectCountry(country, code);
    }
    handleCloseCountryModal();
  };

  return (
    <Modal
      open={openCountryModal}
      onClose={handleCloseCountryModal}
      aria-labelledby="modal-modal-title"
      aria-describedby="modal-modal-description"
    >
      <div sx={style}>
        <div className="bg-white rounded-2xl max-w-[870px] mx-auto left-1/2 absolute w-[95%] top-1/2 -translate-y-1/2 -translate-x-1/2">
          <div className="flex items-center justify-between bg-gray-100 p-4 rounded-t-2xl">
            <h2 className="text-xl text-black font-bold">
              Choose Country/Currency
            </h2>
            <button
              onClick={handleCloseCountryModal}
              className="text-black text-2xl hover:text-primary-blue"
            >
              <IoCloseCircleOutline />
            </button>
          </div>
          <div className="md:p-8 p-4">
            <div className="relative mb-4">
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2 cursor-pointer text-black text-xl">
                <IoSearchOutline />
              </div>
              <input
                type="text"
                placeholder="Search by country or code..."
                className="w-full pl-12 pr-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>
            <div className="grid md:grid-cols-3 grid-cols-2 max-h-96 overflow-y-auto fancy_y_scroll">
              {/* {filteredCountries.length > 0 ? (
                filteredCountries.map(({ country, code, flag, symbol }) => (
                  <button
                    key={country}
                    type='button'
                    className='py-3 px-4 text-left hover:bg-gray-100 rounded-xl flex items-start gap-2 text-sm'
                    onClick={() =>
                      handleCountrySelect(country, code, flag, symbol)
                    }
                  >
                    <span className='pt-0.5'>
                      <Image src={flag} alt='' width={30} height={25} />
                    </span>
                    <span className='flex-1'>
                      <span className='xs:inline-block hidden'> {country}</span>{" "}
                      ({code})
                    </span>
                  </button>
                ))
              ) : (
                <p className='text-center text-red-600 font-semibold py-10 col-span-3'>
                  No Results Found
                </p>
              )} */}{" "}
              {loading ? (
                // Skeleton loading state
                Array.from({ length: filteredCountries.length }).map(
                  (_, index) => (
                    <div
                      key={index}
                      className="py-3 px-4 rounded-xl flex items-start gap-2 text-sm"
                    >
                      <div className="w-[30px] h-[25px] bg-gray-200 rounded animate-pulse"></div>
                      <div className="flex-1 space-y-1">
                        <div className="h-8 bg-gray-200 rounded w-1/3 animate-pulse"></div>
                      </div>
                    </div>
                  )
                )
              ) : filteredCountries.length > 0 ? (
                filteredCountries.map(({ country, code, flag, symbol }) => (
                  <button
                    key={country}
                    type="button"
                    className="py-3 px-4 text-left hover:bg-gray-100 rounded-xl flex items-start gap-2 text-sm"
                    onClick={() =>
                      handleCountrySelect(country, code, flag, symbol)
                    }
                  >
                    <span className="pt-0.5" style={{ width: "auto", height: "auto" }}>
                      <Image src={flag} alt="" width={30} height={25} />
                    </span>
                    <span className="flex-1">
                      <span className="xs:inline-block hidden"> {country}</span>{" "}
                      ({code})
                    </span>
                  </button>
                ))
              ) : (
                <p className="text-center text-red-600 font-semibold py-10 col-span-3">
                  No Results Found
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default CountryModal;
