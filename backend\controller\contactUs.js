import Response from "../utills/response.js";
import { addQuery, getQueryById, listAllQueries } from "../services/contactUs.js";
import sendEmail from "../commons/mail.js";
import Settings from "../models/settings.js";

const addQueryController = async (req, res) => {
    try {
        const newQuery = await addQuery(req.body);
        // email to user
        await sendEmail("userQuerySubmittedSuccessfully", { email: newQuery.email, name: newQuery.name })
        const adminEmail = await Settings.findOne({ key: "contact-us" });
        // Prepare the data to be sent in the email
        const emailData = {
            email: adminEmail.value['email'],  // Admin's email (required)
            userName: newQuery.name,
            userEmail: newQuery.email,    // User's email (required, but checked just in case)
            subject: newQuery.subject,          // Subject (required)
            categories: newQuery.categories || '',  // Categories (empty if not available)
            description: newQuery.description || '', // Description (empty if not available),
            logoUrl:`${process.env.BACKEND_URL}/mixdromLogo.jpg`
        };

        // Send the email
        await sendEmail("newEnquiryNotification", emailData);
        return Response.Created(res, newQuery, 'Query Added Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const getQueryByIdController = async (req, res) => {
    try {
        const { id } = req.params;
        const query = await getQueryById(id);
        return Response.OK(res, query, 'Query Retrieved Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const listAllQueriesController = async (req, res) => {
    try {
        let { page, limit, ...filter } = req.query;

        page = parseInt(page);
        limit = parseInt(limit);

        if (isNaN(page) || page <= 0) page = 1;
        if (isNaN(limit) || limit <= 0) limit = 10;

        const queriesData = await listAllQueries(filter, page, limit);

        const totalPages = Math.ceil(queriesData.totalQueries / parseInt(limit));
        const pagination = {
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages,
            totalQueries: queriesData.totalQueries
        };

        return Response.OK(res, { quires: queriesData.queries, pagination }, 'Queried Retrieved Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

export { addQueryController, getQueryByIdController, listAllQueriesController };