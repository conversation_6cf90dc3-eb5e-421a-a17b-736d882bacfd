import { updateApprovalStatus, listAllOtaProperties, listOtaPropertyAllRooms, getOtaPropertiesByState, getCountriesAndStates } from '../services/otaProperties.js';
import Response from "../utills/response.js";
import { createHostel, listPropertyNameSearch } from "../services/otaProperties.js";
import fs from 'fs'
import otaProperties from "../models/otaProperties.js";
import mongoose from 'mongoose';
import Property from '../models/properties.js'; 
import Room from '../models/room.js';
import RecentSearch from "../models/recentSearches.js"
import fetchBeds24Property from "../utills/beds24Sync.js"
import jwt from 'jsonwebtoken';
import userModel from '../models/auth.js'
import bookingModel from '../models/bookings.js';
import property from '../models/properties.js';
import { getPropertyDetailsById } from '../services/hostel.js';
export const uploadHostels = async (req, res) => {
    try {
        if (!req.files || req.files.length === 0) {
            return Response.BadRequest(res, null, 'No files uploaded');
        }

        const processFile = (file) => {
            return new Promise((resolve, reject) => {
                const filePath = file.path;
                fs.readFile(filePath, 'utf8', async (err, data) => {
                    if (err) {
                        return reject(err);
                    }

                    try {
                        const parsedData = JSON.parse(data);
                        // Extract state and country from location object
                        const state = parsedData.location?.city?.name;
                        const country = parsedData.location?.city?.country;

                        if (!parsedData.properties || !Array.isArray(parsedData.properties)) {
                            return reject(new Error('Invalid file format'));
                        }

                        const filterUndefined = (obj) => {
                            return Object.keys(obj).reduce((acc, key) => {
                                if (obj[key] !== undefined) {
                                    acc[key] = obj[key];
                                }
                                return acc;
                            }, {});
                        };

                        const propertyObjects = parsedData.properties.map(property => {
                            const obj = {
                                id: property.id,
                                isPromoted: property.isPromoted,
                                hbid: property.hbid,
                                name: property.name,
                                starRating: property.starRating,
                                overallRating: property.overallRating,
                                ratingBreakdown: property.ratingBreakdown,
                                latitude: property.latitude,
                                longitude: property.longitude,
                                isFeatured: property.isFeatured,
                                type: property.type,
                                address1: property.address1,
                                address2: property.address2,
                                freeCancellationAvailable: property.freeCancellationAvailable,
                                freeCancellationAvailableUntil: property.freeCancellationAvailableUntil ? new Date(property.freeCancellationAvailableUntil) : undefined,
                                freeCancellation: property.freeCancellation,
                                lowestPricePerNight: property.lowestPricePerNight,
                                lowestPrivatePricePerNight: property.lowestPrivatePricePerNight,
                                lowestDormPricePerNight: property.lowestDormPricePerNight,
                                lowestAveragePricePerNight: property.lowestAveragePricePerNight,
                                lowestAverageDormPricePerNight: property.lowestAverageDormPricePerNight,
                                lowestAveragePrivatePricePerNight: property.lowestAveragePrivatePricePerNight,
                                isNew: property.isNew,
                                overview: property.overview,
                                isElevate: property.isElevate,
                                hostelworldRecommends: property.hostelworldRecommends,
                                distance: property.distance,
                                position: property.position,
                                hwExtra: property.hwExtra,
                                fabSort: property.fabSort,
                                promotions: property.promotions,
                                stayRuleViolations: property.stayRuleViolations,
                                veryPopular: property.veryPopular,
                                rooms: property.rooms,
                                images: property.images,
                                categories: property.categories,
                                facilities: property.facilities,
                                state: state,
                                country: country
                            };

                            return filterUndefined(obj);
                        });

                        await otaProperties.insertMany(propertyObjects);
                        resolve();
                    } catch (error) {
                        reject(error);
                    }
                });
            });
        };

        const promises = req.files.map(file => processFile(file));
        await Promise.all(promises);

        console.log('All properties successfully saved to database');
        return Response.Created(res, null, 'Hostels uploaded and created');
    } catch (error) {
        console.error("Error uploading hostels:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};


const updateApprovalStatusController = async (req, res) => {
    try {
        const { country, state, propertyId } = req.body;

        if (!country && !state && !propertyId) {
            return Response.BadRequest(res, null, 'At least one of country, state, or propertyId must be provided');
        }

        const updateResult = await updateApprovalStatus({ country, state, propertyId });

        return Response.OK(res, null, 'Approval status updated successfully');
    } catch (error) {
        console.error("Error updating approval status:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};

const listAllOtaPropertiesController = async (req, res) => {
    try {
        let userId
        if(req?.headers?.authorization){
            const token = req.headers.authorization?.split(' ')[1];
            const decodedToken = jwt.verify(token, process.env.JWT_SECRET);
            userId =decodedToken.userId
        }
        let { page, limit, sortCondition, ...filter } = req.query;
        // Convert page and limit to integers
        page = parseInt(page, 10) || 1;
        limit = parseInt(limit, 10) || 10;
        const { properties, pagination } = await listAllOtaProperties(page, limit, sortCondition, filter,userId);
        console.log("propertiesproperties",properties)
        if(properties.length>0 && req?.headers?.authorization){
            const token = req.headers.authorization?.split(' ')[1];
            const decodedToken = jwt.verify(token, process.env.JWT_SECRET);
            console.log("token",decodedToken)
            const user = await userModel.findOne({ _id: new mongoose.Types.ObjectId(decodedToken.userId) })
            console.log("user",user)
            const data = {
                user:user?._id,
                search:filter.search,
                checkIn:filter.checkIn,
                checkOut:filter.checkOut,
                guest:filter.guest
            }
            await saveRecentSearch(data);
        }
        // Save search query in the recent searches collection
        return Response.OK(res, { properties, pagination }, 'Properties listed');
    } catch (error) {
        console.error("Error listing properties:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};

const listOtaPropertyAllRoomsController = async (req, res) => {
    try {
        const propertyDetails = await listOtaPropertyAllRooms(req.params.id, req.query);

        return Response.OK(res, { propertyDetails }, 'Property with room details');
    } catch (error) {
        console.error("Error listing properties:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
}

const listPropertyName = async (req, res) => {
    try {
        const hostels = await listPropertyNameSearch(req.params.name)
        return Response.OK(res, { hostels }, 'Properties listed');

    } catch (error) {
        console.error("Error listing properties:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
}
// const getGroupedOtaPropertiesController = async (req, res) => {
//     try {
//         const search = req.query.search || '';
//         const page = parseInt(req.query.page, 10) || 1;
//         const limit = parseInt(req.query.limit, 10) || 10;

//         const { properties, totalDocuments } = await getGroupedOtaProperties(search, page, limit);

//         const totalPages = Math.ceil(totalDocuments / limit);
//         const pagination = {
//             page,
//             limit,
//             totalPages,
//             totalDocuments
//         };

//         return res.status(200).json({
//             message: 'Properties grouped by country and state retrieved successfully',
//             data: { properties, pagination }
//         });
//     } catch (error) {
//         console.error("Error retrieving grouped properties:", error.message);
//         return res.status(500).json({
//             message: 'Internal Server Error',
//             error: error.message
//         });
//     }
// };

const getOtaPropertiesByStateController = async (req, res) => {
    try {
        const search = req.query.search || '';
        const state = req.query.state;
        const page = parseInt(req.query.page, 10) || 1;
        const limit = parseInt(req.query.limit, 10) || 10;

        if (!state) {
            return res.status(400).json({
                message: 'State parameter is required'
            });
        }

        const { properties, totalProperties } = await getOtaPropertiesByState(search, state, page, limit);

        const totalPages = Math.ceil(totalProperties / limit);
        const pagination = {
            page,
            limit,
            totalPages,
            totalProperties
        };

        return Response.OK(res, { properties, pagination }, 'Properties retrieved successfully');

        // return res.status(200).json({
        //     message: 'Properties retrieved successfully',
        //     data: { properties, pagination }
        // });
    } catch (error) {
        console.error("Error retrieving properties:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};

const getCountriesAndStatesController = async (req, res) => {
    try {
        const countriesAndStates = await getCountriesAndStates();
        return Response.OK(res, countriesAndStates, 'Countries and states retrieved successfully');
        // return res.status(200).json({
        //     message: 'Countries and states retrieved successfully',
        //     data: countriesAndStates
        // });
    } catch (error) {
        console.error("Error retrieving countries and states:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};


export const migrateOtaProperties = async () => {
    try {
        // Fetch all OTA Properties
        const otaPropertiesData = await otaProperties.find();
        for (let otaProperty of otaPropertiesData) {
            const existingProperty = await Property.findOne({ id: otaProperty.id });

            if (!existingProperty) {
                // Transform and insert into the Property collection
                const propertyData = {
                    _id: otaProperty._id,
                    id: otaProperty.id,
                    name: otaProperty.name,
                    type: otaProperty.type,
                    address: {
                        lineOne: otaProperty.address1 || '',
                        lineTwo: otaProperty.address2 || '',
                        state: otaProperty.state || '',
                        country: otaProperty.country || '',
                    },
                    aboutUs: otaProperty.overview || '',
                    location: {
                        type: 'Point',
                        coordinates: [otaProperty.longitude, otaProperty.latitude],
                    },
                    photos: (otaProperty.images || []).map(image => ({
                        url: image.prefix,
                    })),
                    rate: {
                        value: otaProperty.lowestPricePerNight?.value || '0',
                        currency: otaProperty.lowestPricePerNight?.currency || 'USD',
                    },
                    freeCancellationAvailable: otaProperty.freeCancellationAvailable || false,
                    freeCancellationAvailableUntil: otaProperty.freeCancellationAvailableUntil,
                    distanceFromCityCenter: otaProperty.distance?.value || 0,
                    freeFacilities: otaProperty.facilities?.[0]?.facilities || [],
                    starRating: otaProperty.starRating || 0,
                    overallRating: otaProperty.overallRating || null,
                    lowestPricePerNight: otaProperty.lowestPricePerNight,
                    lowestPrivatePricePerNight: otaProperty.lowestPrivatePricePerNight,
                    lowestAveragePrivatePricePerNight: otaProperty.lowestAveragePrivatePricePerNight,
                    isOTA: true,
                    freeCancellation: otaProperty.freeCancellation,
                    lowestDormPricePerNight: otaProperty.lowestDormPricePerNight,
                    lowestAveragePricePerNight: otaProperty.lowestAveragePricePerNight,
                    lowestAveragePrivatePricePerNight: otaProperty.lowestAveragePrivatePricePerNight
                    // Add more fields as needed from otaProperties to match the new schema
                };
                const createdProperty = await Property.create(propertyData);

                // If OTA property has room data, insert into the Room collection
                if (otaProperty.rooms && otaProperty.rooms.length > 0) {
                    const roomGroups = otaProperty.rooms;

                    for (let roomGroup of roomGroups) {
                        const privates = roomGroup.privates || [];
                        const dorms = roomGroup.dorms || [];

                        // Handle private rooms
                        for (let otaRoom of privates) {
                            const roomData = {
                                name: otaRoom.name || 'Unnamed Room',
                                type: otaRoom.basicType || 'Unknown Type',
                                property: createdProperty._id,
                                roomNumber: otaRoom.roomNumber || '',
                                beds: otaRoom.beds || 0,
                                capacity: parseInt(otaRoom.capacity) || 0,
                                ensuite: otaRoom.ensuite || false,
                                rate: {
                                    weekdayRate: {
                                        value: otaRoom.averagePrice?.value || 0,
                                        updatedAt: otaRoom.rateUpdatedAt || new Date(),
                                    },
                                    weekendRate: {
                                        value: otaRoom.averagePrice?.value || 0,
                                        updatedAt: otaRoom.rateUpdatedAt || new Date(),
                                    },
                                    averagePrice: {
                                        value: otaRoom.averagePrice?.value || 0,
                                        updatedAt: otaRoom.rateUpdatedAt || new Date(),
                                    }
                                },
                                currency: otaRoom.averagePrice?.currency || 'USD',
                                images: (otaRoom.images || []).map(image => ({
                                    title: image.title || '',
                                    url: image.url || '',
                                })),
                                description: otaRoom.description || '',
                                grade: otaRoom.grade || '',
                                discount: {
                                    discountPercentage: otaRoom.discountPercentage || 0,
                                    startDate: otaRoom.discountStartDate || null,
                                    endDate: otaRoom.discountEndDate || null,
                                    standardRate: otaRoom.standardRate || 0,
                                    nonRefundableRate: otaRoom.nonRefundableRate || 0,
                                },
                                bedAnBreakfast: otaRoom.bedAnBreakfast || false,
                                nonRefundable: otaRoom.nonRefundable || false,
                                freeCancellation: otaRoom.freeCancellation || false,
                                dormitory: otaRoom.dormitory || false,
                                privateRoom: otaRoom.privateRoom || false,
                                tags: otaRoom.tags || [],
                                isActive: otaRoom.isActive || true,
                                isDeleted: otaRoom.isDeleted || false,
                                addedBy: otaRoom.addedBy || null,
                                channelManager: otaRoom.channelManager || {},
                                cloudbeds: otaRoom.cloudbeds || {},
                            };
                            console.log("Private roomData Added");
                            await Room.create(roomData);
                        }

                        // Handle dorm rooms (if applicable)
                        for (let otaRoom of dorms) {
                            const roomData = {
                                name: otaRoom.name || 'Unnamed Room',
                                type: otaRoom.basicType || 'Unknown Type',
                                property: createdProperty._id,
                                roomNumber: otaRoom.roomNumber || '',
                                beds: otaRoom.beds || 0,
                                capacity: parseInt(otaRoom.capacity) || 0,
                                ensuite: otaRoom.ensuite || false,
                                rate: {
                                    weekdayRate: {
                                        value: otaRoom.averagePrice?.value || 0,
                                        updatedAt: otaRoom.rateUpdatedAt || new Date(),
                                    },
                                    weekendRate: {
                                        value: otaRoom.averagePrice?.value || 0,
                                        updatedAt: otaRoom.rateUpdatedAt || new Date(),
                                    },
                                },
                                currency: otaRoom.averagePrice?.currency || 'USD',
                                images: (otaRoom.images || []).map(image => ({
                                    title: image.title || '',
                                    url: image.url || '',
                                })),
                                description: otaRoom.description || '',
                                grade: otaRoom.grade || '',
                                discount: {
                                    discountPercentage: otaRoom.discountPercentage || 0,
                                    startDate: otaRoom.discountStartDate || null,
                                    endDate: otaRoom.discountEndDate || null,
                                    standardRate: otaRoom.standardRate || 0,
                                    nonRefundableRate: otaRoom.nonRefundableRate || 0,
                                },
                                bedAnBreakfast: otaRoom.bedAnBreakfast || false,
                                nonRefundable: otaRoom.nonRefundable || false,
                                freeCancellation: otaRoom.freeCancellation || false,
                                dormitory: otaRoom.dormitory || false,
                                privateRoom: otaRoom.privateRoom || false,
                                tags: otaRoom.tags || [],
                                isActive: otaRoom.isActive || true,
                                isDeleted: otaRoom.isDeleted || false,
                                addedBy: otaRoom.addedBy || null,
                                channelManager: otaRoom.channelManager || {},
                                cloudbeds: otaRoom.cloudbeds || {},
                            };
                            await Room.create(roomData);
                        }
                    }
                }
            }
        }

        console.log('Migration completed successfully');
    } catch (error) {
        console.error('Error during migration:', error);
    } finally {
        mongoose.connection.close();
    }
};
const saveRecentSearch = async (data) => {
    await RecentSearch.create(data);

    // Optionally, keep only the latest 10 searches
    await RecentSearch.find().sort({ timestamp: -1 }).limit(10).exec();
};
export const getPropertyVisitedUsers = async (req, res) => {
    try {
        const { propertyId  } = req.body;
        const hostel = await property.findOne({_id:propertyId});
        console.log("hoste",hostel)
        // if (hostel) {
        //     return Response.NotFound(res, null, 'Property not found');
        // }

        // Get the date range (last 7 days)
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

        // Query for unique users who booked in the last 7 days
        const visitedUsers = await bookingModel.aggregate([
            {
                $match: {
                    property: new mongoose.Types.ObjectId(propertyId) ,
                    createdAt: { $gte: sevenDaysAgo },
                }
            },
            {
                $group: {
                    _id: "$user", // Group by unique user ID
                    latestBooking: { $max: "$createdAt" }
                }
            },
            {
                $lookup: {
                    from: "users", // Assuming users collection exists
                    localField: "_id",
                    foreignField: "_id",
                    as: "userDetails"
                }
            },
            {
                $unwind: "$userDetails"
            },
            {
                $project: {
                    _id: 0,
                    userId: "$_id",
                    name: "$userDetails.name",
                    email: "$userDetails.email",
                    latestBooking: 1
                }
            }
        ]);

        return Response.OK(res, visitedUsers, 'Unique users who visited the property in the last 7 days');
    } catch (error) {
        console.error("Error fetching visited users:", error.message);
        return Response.InternalServerError(res, null, error.message);
    }
};
export {
    updateApprovalStatusController, listAllOtaPropertiesController,
    listOtaPropertyAllRoomsController, getOtaPropertiesByStateController,
    getCountriesAndStatesController, listPropertyName
};
