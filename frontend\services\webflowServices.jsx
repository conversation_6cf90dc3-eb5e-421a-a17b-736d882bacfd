import httpServices from "./httpServices";

export const getlistApi = (state) => {
  // return httpServices.get(`/otaProperties?search=${state}&checkIn=${checkIn}&checkOut=${checkOut}&guest=3`)
  return httpServices.get(`/otaProperties?search=${state}`)

  };
  
  
  export const getlistApiPagination = (state, currentPage,propertiesPerPage,sort,checkIn,checkOut,currency,guest,category) => {
    // return httpServices.get(`/otaProperties?search=${state}&checkIn=${checkIn}&checkOut=${checkOut}&guest=3`)
    return httpServices.get(`/otaProperties?search=${state}&sortCondition=${sort}&page=${currentPage}&limit=${propertiesPerPage}&checkIn=${checkIn}&checkOut=${checkOut}&currency=${currency}&guest=${guest}&tag=${category}`)
    };
    
export const getHostelDeatil = (id,checkIn,checkOut,currency) => {
  return httpServices.get(`/otaProperties/property/${id}?checkIn=${checkIn}&checkOut=${checkOut}&currency=${currency}`)
}

export const registerApi = (payload) => {
  return httpServices.post(`/auth/register`,payload)
}

export const logInApi = (payload) => {
  return httpServices.post(`/auth/login/`,payload)
}

export const verifyOtp = (payload) => {
  return httpServices.post(`/auth/verify-otp/`,payload)
}

export const resendOtpApi = (payload) => {
  return httpServices.post(`/auth/verify-email/`,payload)
}

export const getCalenderApi = (payload) => {
  return httpServices.post(`booking/check-booking-range`,payload)
}
export const forgotPassApi = (payload) => {
  return httpServices.post(`/auth/forgot-password/`,payload)
}

export const resetPassApi = (payload) => {
  return httpServices.put(`/auth/reset-password/`,payload)
}
export const getRoomData = (payload) => {
  return httpServices.post(`/booking/checkout`,payload)
}

export const getProfileApi = (payload) => {
  return httpServices.get(`/auth/profile/`,payload)
}

export const editProfileApi = (payload) => {
  return httpServices.put(`/auth/profile/`,payload)
}

export const CreateOrder = (payload) => {
  return httpServices.post(`/api/payment/createOrder/`,payload)
}

export const PaymentVarification=(payload)=>{
  return httpServices.post(`/api/payment/paymentVerification`,payload)
}

export const contactUsApi=(payload)=>{
  return httpServices.post(`/contact-us`,payload)
}

export const addReviewApi=(payload)=>{
  return httpServices.post(`/reviews`,payload)
}

export const getReviewApi=(id,currentPage,reviewPerPage)=>{
  return httpServices.get(`/reviews/all/${id}?page=${currentPage}&limit=${reviewPerPage}`)
}

export const getMyStayApi = (currentPage,limit) => {
  return httpServices.get(`/users/my-stays?page=${currentPage}&limit=${limit}`)
}

export const getMyEventApi = () => {
  return httpServices.get(`/eventBookings/my-events`)
}

export const getPropertyCountApi = () => {
  return httpServices.get(`/property/my-properties-counts`)
}

export const getMyRideApi = (currentPage,limit) => {
  return httpServices.get(`/rides/user?page=${currentPage}&limit=${limit}`)
}

export const getProfileTravelingApi = () => {
  return httpServices.get(`/users/profile/traveling-details`)
}

export const getBlogApi = (currentPage,limit) => {
  return httpServices.get(`/blog?page=${currentPage}&limit=${limit}`)
}

export const getBlogDetailsApi = (id) => {
  return httpServices.get(`/blog/${id}`)
}

export const getFeaturedHostelApi = (currency) => {
  return httpServices.get(`/pages/top-featured-hostels?currency=${currency}`)
}

export const getTopHostelByCountryApi = (country,currency,selectedCity) => {
  return httpServices.get(`/pages/top-hostels/${country}?city=${selectedCity}&currency=${currency}`)
}

export const getTopHostelByCountryForExploreWorldApi = (country,currency) => {
  return httpServices.get(`/pages/top-hostels?countries=${country}&currency=${currency}`)
}

export const getTravelActivitesApi = (category) => {
  return httpServices.get(`/pages/travel-activities?category=${category}`)
}

export const getRecentSearchApi = () => {
  return httpServices.get(`/pages/recent-searches`)
}

export const getNoticeApi = () => {
  return httpServices.get(`/noticeboard`)
}

export const newsLetterSubscribeApi=(payload)=>{
  return httpServices.post(`/pages/news-letter/subscribe`,payload)
}

export const likeUnlikePropertyApi=(id,payload)=>{
  return httpServices.post(`/wishlists/like/${id}`,payload)
}

export const searchAutocompleteApi=(search)=>{
  return httpServices.get(`/pages/search/autocomplete?search=${search}`)
}

export const deleteAccountApi=(payload)=>{
  return httpServices.post(`/auth/remove-account`,payload)
}

// export const getBookingDetailsApi=(id)=>{
//   return httpServices.get(`booking/${id}`)
// }

export const getBookingDetailsApi=(id)=>{
  return httpServices.get(`/users/my-stays?id=${id}`)
}

export const cancelBookingApi = (payload) => {
  return httpServices.put(`/booking/cancel`,payload)
}

export const eventApi = (currentPage,limit) => {
  return httpServices.get(`/events?page=${currentPage}&limit=${limit}`)
}

export const getProfileViewsApi = () => {
  return httpServices.get(`/profileViews`)
}

export const getHomePagePropertyCountApi = (payload) => {
  return httpServices.post(`/pages/properties-counts`,payload)
}

export const checkoutLoginApi = (payload) => {
  return httpServices.post(`/auth/loginOrRegister`,payload)
}

export const getWalletDataApi = (id) => {
  return httpServices.get(`/wallets/balance/${id}`)
}

export const addAmountToWalletApi = (payload) => {
  return httpServices.get(`/wallets/add-balance`,payload)
}

export const getCityListApi = (country) => {
  return httpServices.get(`/pages/cities-list?country=${country}`)
}