import Link from "next/link";
import React from "react";

const HostelDetails = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616] h-screen">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        Hostel Details
      </h2>
      <div className="bg-white border rounded-xl mt-5 h-auto dark:bg-black dark:border-none">
        <div className="p-8 flex flex-col gap-y-6">
          <div>
            <div className="flex items-center justify-between">
              <h1 className="text-base md:text-lg lg:text-lg font-bold dark:text-[#B6B6B6]">
                Hostel Information
              </h1>
              <div className="flex items-center  justify-center">
                <Link
                  href={"/superadmin/dashboard/hostel-edit"}
                  className="text-white text-sm font-poppins py-1 md:py-2 lg:py-2 px-6 lg:px-8   rounded bg-sky-blue-650"
                >
                  Edit
                </Link>
              </div>
            </div>
            <div className="flex items-center gap-x-20 my-6">
              <div className="flex w-[15%] flex-col">
                <strong className="text-black/70 text-sm dark:text-[#757575]">
                  NAME
                </strong>
                <p className="mt-2 text-black/55 text-sm dark:text-[#B6B6B6]">
                  88 Backpackers
                </p>
              </div>
              <div className="flex w-[60%] md:w-[60%] lg:w-[20%] flex-col">
                <strong className="text-black/70 dark:text-[#757575]">
                  Address
                </strong>
                <p className="mt-2 text-black/55 text-sm dark:text-[#B6B6B6]">
                  6391 Elgin St. Delaware 10299
                </p>
              </div>
            </div>
            <div className="flex items-center gap-x-20 my-6">
              <div className="flex w-[15%] flex-col">
                <strong className="text-black/70 text-sm dark:text-[#757575]">
                  Rate
                </strong>
                <p className="mt-2 text-black/55 text-sm dark:text-[#B6B6B6]">
                  4.5
                </p>
              </div>
              <div className="flex w-[20%] flex-col">
                <strong className="text-black/70 text-sm dark:text-[#757575]">
                  Room
                </strong>
                <p className="mt-2 text-black/55 text-sm dark:text-[#B6B6B6]">
                  45
                </p>
              </div>
              <div className="flexw-[30%] flex-col">
                <strong className="text-black/70 text-sm dark:text-[#757575]">
                  Bed
                </strong>
                <p className="mt-2 text-black/55 text-sm dark:text-[#B6B6B6]">
                  152
                </p>
              </div>
            </div>
            <div className="flex items-center gap-x-20 my-6">
              <div className="flex w-[15%] flex-col">
                <strong className="text-black/70 text-sm dark:text-[#757575]">
                  Country
                </strong>
                <p className="mt-2 text-black/55 text-sm dark:text-[#B6B6B6]">
                  India
                </p>
              </div>
              <div className="flex w-[15%] flex-col">
                <strong className="text-black/70 text-sm dark:text-[#757575]">
                  Offer
                </strong>
                <p className="mt-2 text-black/55 text-sm dark:text-[#B6B6B6]">
                  Closed
                </p>
              </div>
            </div>
          </div>

          <div>
            <h1 className="text-lg font-bold dark:text-[#B6B6B6]">
              Status Information
            </h1>
            <div className="flex items-center gap-x-20 my-6">
              <div className="flex w-[15%] flex-col">
                <strong className="text-black/75 text-sm dark:text-[#757575]">
                  Status
                </strong>
                <p className="mt-2 text-black/55 text-sm dark:text-[#B6B6B6]">
                  Booked
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-start justify-start p-8">
          <Link
            href={"/superadmin/dashboard/hostel"}
            className="text-white py-2 w-32 max-w-md rounded bg-sky-blue-650 flex items-center justify-center"
          >
            Cancel
          </Link>
        </div>
      </div>
    </div>
  );
};

export default HostelDetails;
