// "use client";
// import Link from "next/link";
// import React, { useEffect, useRef, useState } from "react";
// import { FaChevronDown, FaChevronUp } from "react-icons/fa"; // Import icons

// const userEdit = () => {
//   const [isCountryDropdownOpen, setIsCountryDropdownOpen] = useState(false);
//   const [selectedCountry, setSelectedCountry] = useState("India");

//   const [isStateDropdownOpen, setIsStateDropdownOpen] = useState(false);
//   const [selectedState, setSelectedState] = useState("Gujrat");

//   const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false);
//   const [selectedStatus, setSelectedStatus] = useState("Verify");

//   const [isSubscriptionDropdownOpen, setIsSubscriptionDropdownOpen] =
//     useState(false);
//   const [selectedSubscription, setSelectedSubscription] = useState("Premium");

//   const toggleCountryDropdown = () =>
//     setIsCountryDropdownOpen(!isCountryDropdownOpen);
//   const toggleStateDropdown = () =>
//     setIsStateDropdownOpen(!isStateDropdownOpen);
//   const toggleStatusDropdown = () =>
//     setIsStatusDropdownOpen(!isStatusDropdownOpen);
//   const toggleSubscriptionDropdown = () =>
//     setIsSubscriptionDropdownOpen(!isSubscriptionDropdownOpen);

//   // Option select handlers
//   const handleCountrySelect = (country) => {
//     setSelectedCountry(country);
//     setIsCountryDropdownOpen(false);
//   };

//   const handleStateSelect = (state) => {
//     setSelectedState(state);
//     setIsStateDropdownOpen(false);
//   };

//   const handleStatusSelect = (status) => {
//     setSelectedStatus(status);
//     setIsStatusDropdownOpen(false);
//   };

//   const handleSubscriptionSelect = (subscription) => {
//     setSelectedSubscription(subscription);
//     setIsSubscriptionDropdownOpen(false);
//   };

//   return (

//     <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth">
//   <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins">User Edit</h2>
//   <div className="bg-white border flex items-center mt-5 justify-center p-10 rounded-xl h-auto">
//     <div className="bg-white w-full max-w-3xl">
//       <form>
//         <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
//           <div>
//             <label className="block font-semibold text-sm font-poppins text-black/40">Name</label>
//             <input
//               type="text"
//               placeholder="Christine Brooks"
//               className="mt-1 p-2 w-full border text-sm font-medium font-poppins rounded-md bg-[#EEF9FF]"
//             />
//           </div>
//           <div>
//             <label className="block font-semibold text-sm font-poppins text-black/40">Email</label>
//             <input
//               type="email"
//               placeholder="<EMAIL>"
//               className="mt-1 p-2 w-full text-sm font-medium font-poppins border rounded-md bg-[#EEF9FF]"
//             />
//           </div>
//           <div>
//             <label className="block font-semibold text-sm font-poppins text-black/40">Phone Number</label>
//             <input
//               type="text"
//               placeholder="215487362"
//               className="mt-1 p-2 w-full text-sm font-medium font-poppins border rounded-md bg-[#EEF9FF]"
//             />
//           </div>
//           <div>
//             <label className="block font-semibold text-sm font-poppins text-black/40">DOB</label>
//             <input
//               type="text"
//               placeholder="04 Sep 1998"
//               className="mt-1 p-2 w-full text-sm font-medium font-poppins border rounded-md bg-[#EEF9FF]"
//             />
//           </div>
//           <div>
//             <label className="block font-semibold text-sm font-poppins text-black/40">Age</label>
//             <input
//               type="text"
//               placeholder="35"
//               className="mt-1 p-2 w-full text-sm font-medium font-poppins border rounded-md bg-[#EEF9FF]"
//             />
//           </div>
//           <div>
//             <label className="block font-semibold text-sm font-poppins text-black/40">Gender</label>
//             <input
//               type="text"
//               placeholder="Male"
//               className="mt-1 p-2 w-full text-sm font-medium font-poppins border rounded-md bg-[#EEF9FF]"
//             />
//           </div>
//           <div >
//             <label className="block font-semibold text-black/40 text-sm font-poppins">Country</label>
//             <div className="relative mt-1">
//               <button
//                 className="w-full bg-[#EEF9FF] border border-gray-400 p-2 rounded flex items-center justify-between text-sm font-poppins"
//                 onClick={toggleCountryDropdown}
//                 type="button"
//               >
//                 {selectedCountry}
//                 {isCountryDropdownOpen ? (
//                   <FaChevronUp className="text-gray-500 text-sm" />
//                 ) : (
//                   <FaChevronDown className="text-gray-500 text-sm" />
//                 )}
//               </button>
//               {isCountryDropdownOpen && (
//                 <ul className="absolute bg-white border mt-1 rounded w-full z-10">
//                   <li
//                     className="p-2 hover:bg-blue-100 cursor-pointer text-sm font-medium font-poppins"
//                     onClick={() => handleCountrySelect("India")}
//                   >
//                     India
//                   </li>
//                   <li
//                     className="p-2 hover:bg-blue-100 cursor-pointer text-sm font-medium font-poppins"
//                     onClick={() => handleCountrySelect("USA")}
//                   >
//                     USA
//                   </li>
//                   <li
//                     className="p-2 hover:bg-blue-100 cursor-pointer text-sm font-medium font-poppins"
//                     onClick={() => handleCountrySelect("Canada")}
//                   >
//                     Canada
//                   </li>
//                 </ul>
//               )}
//             </div>
//           </div>
//           <div>
//             <label className="block font-semibold text-sm font-poppins text-black/40">State</label>
//             <div className="relative mt-1 " >
//               <button
//                 className="w-full bg-[#EEF9FF] border border-gray-400 p-2 rounded flex items-center justify-between text-sm font-poppins"
//                 onClick={toggleStateDropdown}
//                 type="button"
//               >
//                 {selectedState}
//                 {isStateDropdownOpen ? (
//                   <FaChevronUp className="text-gray-500" />
//                 ) : (
//                   <FaChevronDown className="text-gray-500" />
//                 )}
//               </button>
//               {isStateDropdownOpen && (
//                 <ul className="absolute bg-white border mt-1 rounded w-full z-10">
//                   <li
//                     className="p-2 hover:bg-blue-100 cursor-pointer text-sm font-medium font-poppins"
//                     onClick={() => handleStateSelect("Gujrat")}
//                   >
//                     Gujrat
//                   </li>
//                   <li
//                     className="p-2 hover:bg-blue-100 cursor-pointer text-sm font-medium font-poppins"
//                     onClick={() => handleStateSelect("Maharashtra")}
//                   >
//                     Maharashtra
//                   </li>
//                   <li
//                     className="p-2 hover:bg-blue-100 cursor-pointer text-sm font-medium font-poppins"
//                     onClick={() => handleStateSelect("Karnataka")}
//                   >
//                     Karnataka
//                   </li>
//                 </ul>
//               )}
//             </div>
//           </div>
//           <div>
//             <label className="block font-semibold text-sm font-poppins text-black/40">Address</label>
//             <input
//               type="text"
//               placeholder="1901 Thornridge Cir. Shiloh, Hawaii 81063"
//               className="mt-1 text-sm font-medium font-poppins p-2 w-full border rounded-md bg-[#EEF9FF]"
//             />
//           </div>
//           <div>
//             <label className="block font-semibold text-sm font-poppins text-black/40">PinCode</label>
//             <input
//               type="text"
//               placeholder="395006"
//               className="mt-1 p-2 text-sm font-medium font-poppins w-full border rounded-md bg-[#EEF9FF]"
//             />
//           </div>
//           <div>
//             <label className="block font-semibold text-sm font-poppins text-black/40">Status</label>
//             <div className="relative mt-1">
//               <button
//                 className="w-full bg-[#EEF9FF] border border-gray-400 p-2 rounded flex items-center justify-between text-sm font-poppins"
//                 onClick={toggleStatusDropdown}
//                 type="button"
//               >
//                 {selectedStatus}
//                 {isStatusDropdownOpen ? (
//                   <FaChevronUp className="text-gray-500" />
//                 ) : (
//                   <FaChevronDown className="text-gray-500" />
//                 )}
//               </button>
//               {isStatusDropdownOpen && (
//                 <ul className="absolute bg-white border mt-1 rounded w-full z-10">
//                   <li
//                     className="p-2 hover:bg-blue-100 cursor-pointer text-sm font-medium font-poppins"
//                     onClick={() => handleStatusSelect("Verify")}
//                   >
//                     Verify
//                   </li>
//                   <li
//                     className="p-2 hover:bg-blue-100 cursor-pointer text-sm font-medium font-poppins"
//                     onClick={() => handleStatusSelect("Not Verified")}
//                   >
//                     Not Verified
//                   </li>
//                 </ul>
//               )}
//             </div>
//           </div>
//           <div>
//             <label className="block font-semibold text-sm font-poppins text-black/40">Subscription</label>
//             <div className="relative mt-1">
//               <button
//                 className="w-full bg-[#EEF9FF] border border-gray-400 p-2 rounded flex items-center justify-between text-sm font-poppins"
//                 onClick={toggleSubscriptionDropdown}
//                 type="button"
//               >
//                 {selectedSubscription}
//                 {isSubscriptionDropdownOpen ? (
//                   <FaChevronUp className="text-gray-500" />
//                 ) : (
//                   <FaChevronDown className="text-gray-500" />
//                 )}
//               </button>
//               {isSubscriptionDropdownOpen && (
//                 <ul className="absolute bg-white border mt-1 rounded w-full z-10">
//                   <li
//                     className="p-2 hover:bg-blue-100 cursor-pointer text-sm font-medium font-poppins"
//                     onClick={() => handleSubscriptionSelect("Premium")}
//                   >
//                     Premium
//                   </li>
//                   <li
//                     className="p-2 hover:bg-blue-100 cursor-pointer text-sm font-medium font-poppins"
//                     onClick={() => handleSubscriptionSelect("Standard")}
//                   >
//                     Standard
//                   </li>
//                   <li
//                     className="p-2 hover:bg-blue-100 cursor-pointer text-sm font-medium font-poppins"
//                     onClick={() => handleSubscriptionSelect("Basic")}
//                   >
//                     Basic
//                   </li>
//                 </ul>
//               )}
//             </div>
//           </div>
//         </div>

//         {/* Buttons */}
//         <div className="flex items-center justify-center flex-wrap gap-4 mt-10">
//                     <Link
//                       href={"/superadmin/dashboard/user"}
//                       className={`relative flex  items-center justify-center h-10 px-32 md:px-16 lg:px-40 py-2 text-sm font-medium font-poppins border  rounded  border-gray-200 text-black-100`}
//                     >
//                       Cancel
//                     </Link>
//                     <button
//                       type="button"
//                       className={`relative flex  items-center justify-center h-10 px-32 md:px-16 lg:px-40  py-2 text-sm font-medium font-poppins  border  rounded  bg-sky-blue-650 text-white `}
//                     >
//                       Save
//                     </button>
//                   </div>
//       </form>
//     </div>
//   </div>
// </div>

//   );
// };

// export default userEdit;

"use client";
import Link from "next/link";
import React, { useEffect, useRef, useState } from "react";
import { FaChevronDown, FaChevronUp } from "react-icons/fa"; // Import icons

const UserEdit = () => {
  const [isCountryDropdownOpen, setIsCountryDropdownOpen] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState("India");

  const [isStateDropdownOpen, setIsStateDropdownOpen] = useState(false);
  const [selectedState, setSelectedState] = useState("Gujrat");

  const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState("Verify");

  const [isSubscriptionDropdownOpen, setIsSubscriptionDropdownOpen] =
    useState(false);
  const [selectedSubscription, setSelectedSubscription] = useState("Premium");

  const countryRef = useRef(null);
  const stateRef = useRef(null);
  const statusRef = useRef(null);
  const subscriptionRef = useRef(null);

  // Function to close all dropdowns
  const closeAllDropdowns = () => {
    setIsCountryDropdownOpen(false);
    setIsStateDropdownOpen(false);
    setIsStatusDropdownOpen(false);
    setIsSubscriptionDropdownOpen(false);
  };
    // Effect to handle outside click
    useEffect(() => {
      const handleClickOutside = (event) => {
        if (
          countryRef.current &&
          !countryRef.current.contains(event.target) &&
          stateRef.current &&
          !stateRef.current.contains(event.target) &&
          statusRef.current &&
          !statusRef.current.contains(event.target) &&
          subscriptionRef.current &&
          !subscriptionRef.current.contains(event.target)
        ) {
          closeAllDropdowns();
        }
      };
  
      document.addEventListener("click", handleClickOutside);
      return () => {
        document.removeEventListener("click", handleClickOutside);
      };
    }, []);
  

  // Toggle dropdown functions
  const toggleCountryDropdown = () => {
    closeAllDropdowns();
    setIsCountryDropdownOpen(!isCountryDropdownOpen);
  };

  const toggleStateDropdown = () => {
    closeAllDropdowns();
    setIsStateDropdownOpen(!isStateDropdownOpen);
  };

  const toggleStatusDropdown = () => {
    closeAllDropdowns();
    setIsStatusDropdownOpen(!isStatusDropdownOpen);
  };

  const toggleSubscriptionDropdown = () => {
    closeAllDropdowns();
    setIsSubscriptionDropdownOpen(!isSubscriptionDropdownOpen);
  };

  // Option select handlers
  const handleCountrySelect = (country) => {
    setSelectedCountry(country);
    closeAllDropdowns();
  };

  const handleStateSelect = (state) => {
    setSelectedState(state);
    closeAllDropdowns();
  };

  const handleStatusSelect = (status) => {
    setSelectedStatus(status);
    closeAllDropdowns();
  };

  const handleSubscriptionSelect = (subscription) => {
    setSelectedSubscription(subscription);
    closeAllDropdowns();
  };



  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616] h-screen">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        User Edit
      </h2>
      <div className="bg-white border flex items-center mt-5 justify-center p-10 rounded-xl h-auto dark:bg-black dark:border-none">
        <div className="bg-white w-full max-w-3xl dark:bg-black">
          <form>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div>
                <label className="block font-semibold text-sm font-poppins text-black/40 dark:text-[#B6B6B6]">
                  Name
                </label>
                <input
                  type="text"
                  placeholder="Christine Brooks"
                  className="mt-1 p-2 w-full border text-sm font-medium font-poppins rounded-md bg-[#EEF9FF] dark:bg-transparent dark:placeholder:text-[#B6B6B6]"
                />
              </div>
              <div>
                <label className="block font-semibold text-sm font-poppins text-black/40 dark:text-[#B6B6B6]">
                  Email
                </label>
                <input
                  type="email"
                  placeholder="<EMAIL>"
                  className="mt-1 p-2 w-full text-sm font-medium font-poppins border rounded-md bg-[#EEF9FF]  dark:bg-transparent dark:placeholder:text-[#B6B6B6]"
                />
              </div>
              <div>
                <label className="block font-semibold text-sm font-poppins text-black/40 dark:text-[#B6B6B6]">
                  Phone Number
                </label>
                <input
                  type="text"
                  placeholder="215487362"
                  className="mt-1 p-2 w-full text-sm font-medium font-poppins border rounded-md bg-[#EEF9FF]  dark:bg-transparent dark:placeholder:text-[#B6B6B6]"
                />
              </div>
              <div>
                <label className="block font-semibold text-sm font-poppins text-black/40 dark:text-[#B6B6B6]">
                  DOB
                </label>
                <input
                  type="text"
                  placeholder="04 Sep 1998"
                  className="mt-1 p-2 w-full text-sm font-medium font-poppins border rounded-md bg-[#EEF9FF]  dark:bg-transparent dark:placeholder:text-[#B6B6B6]"
                />
              </div>
              <div>
                <label className="block font-semibold text-sm font-poppins text-black/40 dark:text-[#B6B6B6]">
                  Age
                </label>
                <input
                  type="text"
                  placeholder="35"
                  className="mt-1 p-2 w-full text-sm font-medium font-poppins border rounded-md bg-[#EEF9FF]  dark:bg-transparent dark:placeholder:text-[#B6B6B6]"
                />
              </div>
              <div>
                <label className="block font-semibold text-sm font-poppins text-black/40 dark:text-[#B6B6B6]">
                  Gender
                </label>
                <input
                  type="text"
                  placeholder="Male"
                  className="mt-1 p-2 w-full text-sm font-medium font-poppins border rounded-md bg-[#EEF9FF]  dark:bg-transparent dark:placeholder:text-[#B6B6B6]"
                />
              </div>
              {/* <div>
                <label className="block font-semibold text-black/40 text-sm font-poppins">
                  Country
                </label>
                <div className="relative mt-1">
                  <button
                    className="w-full bg-[#EEF9FF] border border-gray-400 p-2 rounded flex items-center justify-between text-sm font-poppins"
                    onClick={toggleCountryDropdown}
                    type="button"
                  >
                    {selectedCountry}
                    {isCountryDropdownOpen ? (
                      <FaChevronUp className="text-gray-500 text-sm" />
                    ) : (
                      <FaChevronDown className="text-gray-500 text-sm" />
                    )}
                  </button>
                  {isCountryDropdownOpen && (
                    <ul className="absolute bg-white border mt-1 rounded w-full z-10">
                      <li
                        className="p-2 hover:bg-blue-100 cursor-pointer text-sm font-medium font-poppins"
                        onClick={() => handleCountrySelect("India")}
                      >
                        India
                      </li>
                      <li
                        className="p-2 hover:bg-blue-100 cursor-pointer text-sm font-medium font-poppins"
                        onClick={() => handleCountrySelect("USA")}
                      >
                        USA
                      </li>
                      <li
                        className="p-2 hover:bg-blue-100 cursor-pointer text-sm font-medium font-poppins"
                        onClick={() => handleCountrySelect("Canada")}
                      >
                        Canada
                      </li>
                    </ul>
                  )}
                </div>
              </div> */}

              {/* Country Dropdown */}
              <div ref={countryRef}>
                <label className="block font-semibold text-sm font-poppins text-black/40 dark:text-[#B6B6B6]">
                  Country
                </label>
                <div className="relative mt-1">
                  <button
                    className="w-full bg-[#EEF9FF] border border-gray-400 p-2 rounded flex items-center justify-between text-sm font-poppins  dark:bg-transparent dark:text-[#B6B6B6] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
                    onClick={toggleCountryDropdown}
                    type="button"
                  >
                    {selectedCountry}
                    {isCountryDropdownOpen ? (
                      <FaChevronUp className="dark:text-[#B6B6B6]"/>
                    ) : (
                      <FaChevronDown className="dark:text-[#B6B6B6]"/>
                    )}
                  </button>
                  {isCountryDropdownOpen && (
                    <ul className="absolute bg-white  dark:bg-[#171616] border mt-1 rounded w-full z-10">
                      <li
                        className="p-2 hover:bg-blue-100 cursor-pointer text-sm text-poppins dark:text-[#B6B6B6] dark:hover:bg-[#393939b7]"
                        onClick={() => handleCountrySelect("India")}
                      >
                        India
                      </li>
                      <li
                        className="p-2 hover:bg-blue-100 cursor-pointer text-sm text-poppins dark:text-[#B6B6B6] dark:hover:bg-[#393939b7]"
                        onClick={() => handleCountrySelect("USA")}
                      >
                        USA
                      </li>
                      <li
                        className="p-2 hover:bg-blue-100 cursor-pointer text-sm text-poppins dark:text-[#B6B6B6] dark:hover:bg-[#393939b7]"
                        onClick={() => handleCountrySelect("Canada")}
                      >
                        Canada
                      </li>
                    </ul>
                  )}
                </div>
              </div>

              {/* State Dropdown */}
              <div ref={stateRef}>
                <label className="block font-semibold text-sm font-poppins text-black/40 dark:text-[#B6B6B6]">
                  State
                </label>
                <div className="relative mt-1">
                  <button
                    className="w-full bg-[#EEF9FF] border border-gray-400 p-2 rounded flex items-center justify-between text-sm font-poppins dark:bg-transparent dark:text-[#B6B6B6] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
                    onClick={toggleStateDropdown}
                    type="button"
                  >
                    {selectedState}
                    {isStateDropdownOpen ? <FaChevronUp className="dark:text-[#B6B6B6]"/> : <FaChevronDown className="dark:text-[#B6B6B6]"/>}
                  </button>
                  {isStateDropdownOpen && (
                    <ul className="absolute bg-white border mt-1 rounded w-full z-10 dark:bg-[#171616]">
                      <li
                        className="p-2 hover:bg-blue-100 cursor-pointer text-sm text-poppins dark:text-[#B6B6B6] dark:hover:bg-[#393939b7]"
                        onClick={() => handleStateSelect("Gujrat")}
                      >
                        Gujrat
                      </li>
                      <li
                        className="p-2 hover:bg-blue-100 cursor-pointer text-sm text-poppins dark:text-[#B6B6B6] dark:hover:bg-[#393939b7]"
                        onClick={() => handleStateSelect("Maharashtra")}
                      >
                        Maharashtra
                      </li>
                    </ul>
                  )}
                </div>
              </div>
              <div>
                <label className="block font-semibold text-sm font-poppins text-black/40 dark:text-[#B6B6B6]">
                  Address
                </label>
                <input
                  type="text"
                  placeholder="1901 Thornridge Cir. Shiloh, Hawaii 81063"
                  className="mt-1 text-sm font-medium font-poppins p-2 w-full border rounded-md bg-[#EEF9FF] dark:bg-transparent dark:placeholder:text-[#B6B6B6]"
                />
              </div>
              <div>
                <label className="block font-semibold text-sm font-poppins text-black/40 dark:text-[#B6B6B6]">
                  PinCode
                </label>
                <input
                  type="text"
                  placeholder="395006"
                  className="mt-1 p-2 text-sm font-medium font-poppins w-full border rounded-md bg-[#EEF9FF] dark:bg-transparent dark:placeholder:text-[#B6B6B6]"
                />
              </div>
              {/* Status Dropdown */}
              <div ref={statusRef}>
                <label className="block font-semibold text-sm font-poppins text-black/40 dark:text-[#B6B6B6]">
                  Status
                </label>
                <div className="relative mt-1">
                  <button
                    className="w-full bg-[#EEF9FF] border border-gray-400 p-2 rounded flex items-center justify-between text-sm font-poppins dark:bg-transparent dark:text-[#B6B6B6] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
                    onClick={toggleStatusDropdown}
                    type="button"
                  >
                    {selectedStatus}
                    {isStatusDropdownOpen ? <FaChevronUp className="dark:text-[#B6B6B6]"/> : <FaChevronDown className="dark:text-[#B6B6B6]"/>}
                  </button>
                  {isStatusDropdownOpen && (
                    <ul className="absolute bg-white border mt-1 rounded w-full z-10 dark:bg-[#171616]">
                      <li
                        className="p-2 hover:bg-blue-100 cursor-pointer text-sm text-poppins dark:text-[#B6B6B6] dark:hover:bg-[#393939b7]"
                        onClick={() => handleStatusSelect("Verify")}
                      >
                        Verify
                      </li>
                      <li
                        className="p-2 hover:bg-blue-100 cursor-pointer text-sm text-poppins dark:text-[#B6B6B6] dark:hover:bg-[#393939b7]"
                        onClick={() => handleStatusSelect("Not Verified")}
                      >
                        Not Verified
                      </li>
                    </ul>
                  )}
                </div>
              </div>
              {/* Subscription Dropdown */}
              <div ref={subscriptionRef}>
                <label className="block font-semibold text-sm font-poppins text-black/40 dark:text-[#B6B6B6]">
                  Subscription
                </label>
                <div className="relative mt-1">
                  <button
                    className="w-full bg-[#EEF9FF] border border-gray-400 p-2 rounded flex items-center justify-between text-sm font-poppins dark:bg-transparent dark:text-[#B6B6B6] focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
                    onClick={toggleSubscriptionDropdown}
                    type="button"
                  >
                    {selectedSubscription}
                    {isSubscriptionDropdownOpen ? (
                      <FaChevronUp className="text-gray-500" />
                    ) : (
                      <FaChevronDown className="text-gray-500" />
                    )}
                  </button>
                  {isSubscriptionDropdownOpen && (
                    <ul className="absolute bg-white border mt-1 rounded w-full z-10 dark:bg-[#171616]">
                      <li
                        className="p-2 hover:bg-blue-100 cursor-pointer text-sm font-poppins dark:text-[#B6B6B6] dark:hover:bg-[#393939b7]"
                        onClick={() => handleSubscriptionSelect("Premium")}
                      >
                        Premium
                      </li>
                      <li
                        className="p-2 hover:bg-blue-100 cursor-pointer text-sm font-poppins dark:text-[#B6B6B6] dark:hover:bg-[#393939b7]"
                        onClick={() => handleSubscriptionSelect("Standard")}
                      >
                        Standard
                      </li>
                      <li
                        className="p-2 hover:bg-blue-100 cursor-pointer text-sm font-poppins dark:text-[#B6B6B6] dark:hover:bg-[#393939b7]"
                        onClick={() => handleSubscriptionSelect("Basic")}
                      >
                        Basic
                      </li>
                    </ul>
                  )}
                </div>
              </div>
            </div>

            {/* Buttons */}
            <div className="flex items-center justify-center flex-wrap gap-4 mt-10">
              <Link
                href={"/superadmin/dashboard/user"}
                className={`relative flex  items-center justify-center h-10 px-32 md:px-16 lg:px-40 py-2 text-sm font-medium font-poppins border  rounded  border-gray-200 text-black-100 dark:text-gray-100`}
              >
                Cancel
              </Link>
              <button
                type="button"
                className={`relative flex  items-center justify-center h-10 px-32 md:px-16 lg:px-40  py-2 text-sm font-medium font-poppins  border  rounded  bg-sky-blue-650 text-white `}
              >
                Save
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default UserEdit;
