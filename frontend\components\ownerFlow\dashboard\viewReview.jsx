/* eslint-disable react/jsx-key */
import Image from "next/image";
import { useEffect, useState } from "react";
import Loader from "@/components/loader/loader";
import { getOwnerReviewByIdApi } from "@/services/ownerflowServices";
import ReactStars from "react-rating-stars-component";

const ViewReview = ({ editId }) => {
  const [formData, setFormData] = useState({
    name: "",
    date: "",
    rating: "",
    photos: [],
    description: "",
    isDeleted: false,
  });

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (editId) {
      fetchData(editId);
    }
  }, [editId]);

  const fetchData = async (editId) => {
    setIsLoading(true);
    try {
      const res = await getOwnerReviewByIdApi(editId);
      console.log("res",res?.data?.data?.rating)
      if (res?.status == 200) {
        const data = res?.data?.data;
        setFormData({
          name: data?.userName || "",
          date: data?.createdAt,
          rating: data?.rating,
          photos: data?.images || [],
          description: data?.comment,
          isDeleted: data?.isDeleted || false,
        });
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Loader open={isLoading} />

      <div className='max-w-[780px] mx-auto pb-5'>
        <div className='grid sm:grid-cols-[1.4fr_3fr] grid-cols-2 sm:gap-4 gap-3 font-inter'>
          <div>
            <p className='font-bold text-black sm:text-base text-xs mb-5'>
              Name :
            </p>
            <p className='font-bold text-black sm:text-base text-xs mb-5'>
              Dated :
            </p>
            <p className='font-bold text-black sm:text-base text-xs mb-5'>
              Rating :
            </p>
            <p className='font-bold text-black sm:text-base text-xs mb-6'>
              Photos :
            </p>
            <p className='font-bold text-black sm:text-base text-xs mb-5'>
              Description:
            </p>
          </div>
          <div>
            <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
              {formData.name || " - "}
            </p>
            <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
              {formData?.date
                ? (() => {
                    const date = new Date(formData?.date);
                    const day = String(date.getDate()).padStart(2, "0");
                    const month = String(date.getMonth() + 1).padStart(2, "0");
                    const year = date.getFullYear();
                    return `${day}/${month}/${year}`;
                  })()
                : " - "}
            </p>
            <p className='flex font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
              <ReactStars
                key={formData?.rating}
                count={5}
                size={20}
                value={parseFloat(formData?.rating || 0)}
                edit={false}
                isHalf={true}
                activeColor='#FFD700'
              /> 
              <div className="flex font-semibold text-[#00000080] sm:text-base text-xs px-2">{` (${formData?.rating})`}</div>
            </p>
            <div className='flex gap-1.5 mb-4'>
              {formData?.photos?.map((image, index) => (
                <Image
                  className='w-[54px] h-[36px] object-cover rounded-sm'
                  src={image?.url}
                  alt={`Existing Image ${index + 1}`}
                  width={54}
                  height={36}
                ></Image>
              ))}
            </div>
            <p className='font-semibold text-[#00000080] sm:text-base text-xs mb-5'>
              {formData.description || " - "}
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default ViewReview;
