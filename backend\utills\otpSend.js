const watiApiEndpoint = 'https://live-mt-server.wati.io/370421';
const apiKey = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6akvlXiJoVwleiZrBlGikgBb8a7F-uebMccsXSvEeTo';

export async function sendOTP(phoneNumber, otp) {
    let data = JSON.stringify({
        "template_name": "login_code",
        "broadcast_name": "login_code",
        "parameters": [
          {
            "name": "1",
            "value": otp
          }
        ]
      });
      
      let config = {
        method: 'post',
        maxBodyLength: Infinity,
        url: `https://live-mt-server.wati.io/370421/api/v1/sendTemplateMessage?whatsappNumber=${phoneNumber}`,
        headers: { 
          'Content-Type': 'application/json', 
          'Authorization': apiKey
        },
        data : data
      };
      
      axios.request(config)
      .then((response) => {
        console.log(JSON.stringify(response.data));
        return response.data
      })
      .catch((error) => {
        console.log(error);
      });
      
}
