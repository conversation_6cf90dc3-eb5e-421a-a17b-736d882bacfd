import { ReasonPhrases, StatusCodes } from 'http-status-codes';
import { readFile } from 'fs/promises';

// const parsedMessages = JSON.parse(
//     await readFile(
//         new URL('./messages.json', import.meta.url)
//     )
// );
class Response {
    static send(res, statusCode, data, message) {
        // const message = parsedMessages[messageKey] || ReasonPhrases[statusCode];
        const responseData = {
            status: statusCode < 400, // status:true if statusCode is a success code
            data,
            message
        };
        res.status(statusCode).json(responseData);
    }
    static Continue(res, data, message = ReasonPhrases.CONTINUE) {
        this.send(res, StatusCodes.CONTINUE, data, message);
    }

    static SwitchingProtocols(res, data, message = ReasonPhrases.SWITCHING_PROTOCOLS) {
        this.send(res, StatusCodes.SWITCHING_PROTOCOLS, data, message);
    }

    static Processing(res, data, message = ReasonPhrases.PROCESSING) {
        this.send(res, StatusCodes.PROCESSING, data, message);
    }

    static EarlyHints(res, data, message = ReasonPhrases.EARLY_HINTS) {
        this.send(res, StatusCodes.EARLY_HINTS, data, message);
    }

    static OK(res, data, message = ReasonPhrases.OK) {
        this.send(res, StatusCodes.OK, data, message);
    }

    static Created(res, data, message = ReasonPhrases.CREATED) {
        this.send(res, StatusCodes.CREATED, data, message);
    }

    static Accepted(res, data, message = ReasonPhrases.ACCEPTED) {
        this.send(res, StatusCodes.ACCEPTED, data, message);
    }

    static NonAuthoritativeInformation(res, data, message = ReasonPhrases.NON_AUTHORITATIVE_INFORMATION) {
        this.send(res, StatusCodes.NON_AUTHORITATIVE_INFORMATION, data, message);
    }

    static NoContent(res, data, message = ReasonPhrases.NO_CONTENT) {
        this.send(res, StatusCodes.NO_CONTENT, data, message);
    }

    static ResetContent(res, data, message = ReasonPhrases.RESET_CONTENT) {
        this.send(res, StatusCodes.RESET_CONTENT, data, message);
    }

    static PartialContent(res, data, message = ReasonPhrases.PARTIAL_CONTENT) {
        this.send(res, StatusCodes.PARTIAL_CONTENT, data, message);
    }

    static MultiStatus(res, data, message = ReasonPhrases.MULTI_STATUS) {
        this.send(res, StatusCodes.MULTI_STATUS, data, message);
    }

    static MultipleChoices(res, data, message = ReasonPhrases.MULTIPLE_CHOICES) {
        this.send(res, StatusCodes.MULTIPLE_CHOICES, data, message);
    }

    static MovedPermanently(res, data, message = ReasonPhrases.MOVED_PERMANENTLY) {
        this.send(res, StatusCodes.MOVED_PERMANENTLY, data, message);
    }

    static MovedTemporarily(res, data, message = ReasonPhrases.MOVED_TEMPORARILY) {
        this.send(res, StatusCodes.MOVED_TEMPORARILY, data, message);
    }

    static SeeOther(res, data, message = ReasonPhrases.SEE_OTHER) {
        this.send(res, StatusCodes.SEE_OTHER, data, message);
    }

    static NotModified(res, data, message = ReasonPhrases.NOT_MODIFIED) {
        this.send(res, StatusCodes.NOT_MODIFIED, data, message);
    }

    static UseProxy(res, data, message = ReasonPhrases.USE_PROXY) {
        this.send(res, StatusCodes.USE_PROXY, data, message);
    }

    static TemporaryRedirect(res, data, message = ReasonPhrases.TEMPORARY_REDIRECT) {
        this.send(res, StatusCodes.TEMPORARY_REDIRECT, data, message);
    }

    static PermanentRedirect(res, data, message = ReasonPhrases.PERMANENT_REDIRECT) {
        this.send(res, StatusCodes.PERMANENT_REDIRECT, data, message);
    }

    static BadRequest(res, data, message = ReasonPhrases.BAD_REQUEST) {
        this.send(res, StatusCodes.BAD_REQUEST, data, message);
    }

    static Unauthorized(res, data, message = ReasonPhrases.UNAUTHORIZED) {
        this.send(res, StatusCodes.UNAUTHORIZED, data, message);
    }

    static PaymentRequired(res, data, message = ReasonPhrases.PAYMENT_REQUIRED) {
        this.send(res, StatusCodes.PAYMENT_REQUIRED, data, message);
    }

    static Forbidden(res, data, message = ReasonPhrases.FORBIDDEN) {
        this.send(res, StatusCodes.FORBIDDEN, data, message);
    }

    static NotFound(res, data, message = ReasonPhrases.NOT_FOUND) {
        this.send(res, StatusCodes.NOT_FOUND, data, message);
    }

    static MethodNotAllowed(res, data, message = ReasonPhrases.METHOD_NOT_ALLOWED) {
        this.send(res, StatusCodes.METHOD_NOT_ALLOWED, data, message);
    }

    static NotAcceptable(res, data, message = ReasonPhrases.NOT_ACCEPTABLE) {
        this.send(res, StatusCodes.NOT_ACCEPTABLE, data, message);
    }

    static ProxyAuthenticationRequired(res, data, message = ReasonPhrases.PROXY_AUTHENTICATION_REQUIRED) {
        this.send(res, StatusCodes.PROXY_AUTHENTICATION_REQUIRED, data, message);
    }

    static RequestTimeout(res, data, message = ReasonPhrases.REQUEST_TIMEOUT) {
        this.send(res, StatusCodes.REQUEST_TIMEOUT, data, message);
    }

    static Conflict(res, data, message = ReasonPhrases.CONFLICT) {
        this.send(res, StatusCodes.CONFLICT, data, message);
    }

    static Gone(res, data, message = ReasonPhrases.GONE) {
        this.send(res, StatusCodes.GONE, data, message);
    }

    static LengthRequired(res, data, message = ReasonPhrases.LENGTH_REQUIRED) {
        this.send(res, StatusCodes.LENGTH_REQUIRED, data, message);
    }

    static PreconditionFailed(res, data, message = ReasonPhrases.PRECONDITION_FAILED) {
        this.send(res, StatusCodes.PRECONDITION_FAILED, data, message);
    }

    static RequestEntityTooLarge(res, data, message = ReasonPhrases.REQUEST_TOO_LONG) {
        this.send(res, StatusCodes.REQUEST_TOO_LONG, data, message);
    }

    static RequestURITooLong(res, data, message = ReasonPhrases.REQUEST_URI_TOO_LONG) {
        this.send(res, StatusCodes.REQUEST_URI_TOO_LONG, data, message);
    }

    static UnsupportedMediaType(res, data, message = ReasonPhrases.UNSUPPORTED_MEDIA_TYPE) {
        this.send(res, StatusCodes.UNSUPPORTED_MEDIA_TYPE, data, message);
    }

    static RequestedRangeNotSatisfiable(res, data, message = ReasonPhrases.REQUESTED_RANGE_NOT_SATISFIABLE) {
        this.send(res, StatusCodes.REQUESTED_RANGE_NOT_SATISFIABLE, data, message);
    }

    static ExpectationFailed(res, data, message = ReasonPhrases.EXPECTATION_FAILED) {
        this.send(res, StatusCodes.EXPECTATION_FAILED, data, message);
    }

    static ImATeapot(res, data, message = ReasonPhrases.IM_A_TEAPOT) {
        this.send(res, StatusCodes.IM_A_TEAPOT, data, message);
    }

    static InsufficientSpaceOnResource(res, data, message = ReasonPhrases.INSUFFICIENT_SPACE_ON_RESOURCE) {
        this.send(res, StatusCodes.INSUFFICIENT_SPACE_ON_RESOURCE, data, message);
    }

    static InternalServerError(res, data, message = ReasonPhrases.INTERNAL_SERVER_ERROR) {
        this.send(res, StatusCodes.INTERNAL_SERVER_ERROR, data, message);
    }

    static handleError(res, error, statusCode, message) {
        this.send(res, statusCode || StatusCodes.INTERNAL_SERVER_ERROR, {}, message || error.message);
    }
}

export default Response;
