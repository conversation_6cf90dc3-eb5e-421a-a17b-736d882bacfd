import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export const updateCurrencyRates = async () => {
  try {
    const response = await axios.get('https://data.fixer.io/api/latest', {
      params: {
        access_key: 'ad1ebaadbd794d504ee3c5ae00930639',
      },
    });

    const data = response.data;

    if (!data.success) {
      console.error('❌ Fixer API returned an error:', data);
      return;
    }

    // Define full path to currencyRates.js
    const utilsDir = path.resolve(__dirname, '../utills');
    const filePath = path.join(utilsDir, 'currencyRate.js');

    // ✅ Ensure the utils directory exists
    if (!fs.existsSync(utilsDir)) {
      fs.mkdirSync(utilsDir, { recursive: true });
      console.log('📁 Created missing utils/ directory');
    }

    const fileContent = `const currencyRates = ${JSON.stringify(data, null, 4)};\n\nexport default currencyRates;`;

    fs.writeFileSync(filePath, fileContent, 'utf8');
    console.log('✅ Currency rates updated successfully!');
  } catch (error) {
    console.error('❌ Error updating currency rates:', error.message);
  }
};

// updateCurrencyRates();
