import React, { useEffect, useRef, useState, Fragment } from "react";
import toast from "react-hot-toast";
import { DeleteEvent<PERSON>pi, EventListApi } from "@/services/ownerflowServices";
import { getItemLocalStorage } from "@/utils/browserSetting";
import dynamic from "next/dynamic";
import countries from "world-countries";
import Image from "next/image";
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
  Transition,
} from "@headlessui/react";
import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/react";
import { Trash, MoreVertical, Pencil, Eye } from "lucide-react";
import ViewEvent from "@/components/ownerFlow/dashboard/viewEvent";
import Pagination from "@/components/common/commonPagination";
import Head from "next/head";

const Filter = dynamic(() => import("../../../components/model/filter"), {
  ssr: false,
});

const Event = dynamic(
  () => import("@/components/ownerFlow/dashboard/addEvent"),
  {
    ssr: false,
  }
);

const EditEvent = dynamic(
  () => import("@/components/ownerFlow/dashboard/editEvent"),
  {
    ssr: false,
  }
);

const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

const ViewMember = dynamic(
  () => import("@/components/ownerFlow/dashboard/viewMember"),
  {
    ssr: false,
  }
);

const Page = () => {
  // Filter Modal
  const [isModalOpen, setIsModalOpen] = useState(false);
  const closefilterModal = () => setIsModalOpen(false);
  const [activeTab, setActiveTab] = useState("Event");
  const [editId, setEditId] = useState(null);
  const [currencyData, setCurrencyData] = useState({});
  const isFirstRender = useRef(null);
  const [openAddevent, setOpenAddevent] = useState(false);
  const closeeventModal = () => setOpenAddevent(false);
  const [openEditevent, setopenEditevent] = useState(false);
  const closeeventeditModal = () => setopenEditevent(false);
  const [openViewevent, setopenViewevent] = useState(false);
  const closeeventviewModal = () => setopenViewevent(false);

  const tabs = [
    {
      id: "Event",
      label: "All Events",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/all_event.svg`,
      icon1: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/all_event_b.svg`,
    },
    {
      id: "ViewMember",
      label: "View Member",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/view_member.svg`,
      icon1: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/view_member_b.svg`,
    },
  ];

  const headers = [
    "Event ID",
    "Event name",
    "Price",
    "Duration",
    "Address",
    "Hours & Minutes",
    "Description",
    "Status",
    "Tags",
    "Action",
    // "State",
    // "Country",
    // "Location",
    // "Attachment",
    // "Description",
    // "Tags",
    // "Status",
    // "Action",
  ];

  // Define more data arrays for other tabs if needed

  const id = getItemLocalStorage("selectedId");
  const [currentPage, setCurrentPage] = useState(1);
  const [EventList, setEventList] = useState([]);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(false);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalData, setTotalData] = useState();

  useEffect(() => {
    if (!isFirstRender.current) {
      fetchList(currentPage);
    } else {
      isFirstRender.current = false;
    }
  }, [id, currentPage, itemsPerPage]);

  const fetchList = async (page) => {
    setLoading(true);
    try {
      const response = await EventListApi(page, itemsPerPage);
      setEventList(response?.data?.data?.events);
      setTotalPages(response?.data?.data?.pagination?.totalPages || 1);
      setTotalData(response?.data?.data?.pagination?.totalEvents);
      if (response.status == 200) {
        /* empty */
      }
      if (response.status !== 200) {
        toast.error(response?.data?.message);
      }
      // eslint-disable-next-line no-unused-vars
    } catch (error) {
      /* empty */
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    setLoading(true);
    try {
      const response = await DeleteEventApi(id);
      console?.log("daeeada", response);
      if (response?.data?.status) {
        toast.success(response?.data?.message);
        fetchList();
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };
  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const updateEventList = () => {
    fetchList(1);
  };

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);
      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);

  const getCurrencySymbol = (currencyCode) => {
    return currencyData[currencyCode] || currencyCode;
  };

  return (
    <>
      <Head>
        <title>Event Management | Mixdorm</title>
      </Head>
      <Loader open={loading} />
      {loading ? (
        <section className="w-full">
          <div className="flex justify-between items-center">
            <div className="h-8 w-24 bg-gray-200 rounded animate-pulse"></div>
            <div className="flex sm:gap-2 gap-1 relative">
              <div className="h-10 w-20 bg-gray-200 rounded-lg animate-pulse"></div>
              <div className="h-10 w-24 bg-gray-200 rounded-lg animate-pulse"></div>
            </div>
          </div>

          <div className="w-full mt-5 bg-white shadow-4xl rounded-2xl">
            <div className="mb-4">
              <ul className="grid md:grid-cols-[repeat(3,_1fr)_1fr] grid-cols-2 sm:gap-4 gap-2">
                {[1, 2, 3, 4].map((item) => (
                  <li
                    key={item}
                    className="flex items-center sm:gap-10 lg:gap-4 gap-2 sm:p-4 p-2 relative border rounded-lg border-[#D0D3D9] bg-gray-100 animate-pulse"
                  >
                    <div className="w-full">
                      <div className="h-5 w-3/4 bg-gray-300 rounded mb-3"></div>
                      <div className="h-8 w-1/2 bg-gray-300 rounded"></div>
                    </div>
                    <div className="ml-auto w-10 h-10 bg-gray-300 rounded-full"></div>
                  </li>
                ))}
              </ul>
            </div>

            <div className="overflow-x-auto mt-4 rounded-md">
              <div className="min-w-full border">
                {/* Table Header */}
                <div className="bg-gray-100 border-b flex">
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((_, index) => (
                    <div key={index} className="p-4 w-32 flex-grow">
                      <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
                    </div>
                  ))}
                </div>

                {/* Table Rows */}
                {[1, 2, 3, 4, 5].map((row) => (
                  <div key={row} className="border-b flex">
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((_, index) => (
                      <div key={index} className="p-3 w-32 flex-grow">
                        <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            </div>

            {/* Pagination Skeleton */}
            <div className="flex justify-center mt-4 p-4">
              <div className="flex gap-2">
                <div className="h-8 w-8 bg-gray-200 rounded-full animate-pulse"></div>
                <div className="h-8 w-8 bg-gray-200 rounded-full animate-pulse"></div>
                <div className="h-8 w-8 bg-gray-200 rounded-full animate-pulse"></div>
                <div className="h-8 w-8 bg-gray-200 rounded-full animate-pulse"></div>
              </div>
            </div>
          </div>
        </section>
      ) : (
        <section className="w-full">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-medium text-gray-800">Event</h2>
            <div className="flex sm:gap-2 gap-1 relative">
              <button
                className="sm:flex block items-center text-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-gray-300 cursor-not-allowed  font-medium"
                // onClick={() => setIsModalOpen(true)}
              >
                <Image
                  className="sm:mr-2 mx-auto"
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/filter.svg`}
                  width={20}
                  height={20}
                />
                Filter
              </button>
              {activeTab === "Event" && (
                <button
                  className="flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-gray-300 cursor-not-allowed  font-medium"
                  // onClick={() => setOpenAddevent(true)}
                >
                  Add Event
                </button>
              )}
              {/* <button
                    className='flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-primary-blue font-medium'
                    onClick={() => setopenEditevent(true)}
                  >
                    Edit event
                </button> */}
            </div>
          </div>

          <div className="w-full mt-5 bg-white shadow-4xl rounded-2xl">
            <div className="mb-4">
              <ul className="grid md:grid-cols-[repeat(3,_1fr)_1fr] grid-cols-2 font-normal text-xs sm:gap-4 gap-2">
                {tabs.map((item) => (
                  <li
                    key={item.id}
                    className={`flex items-center sm:gap-10 lg:gap-4 gap-2 sm:p-4 p-2 relative border rounded-lg border-[#D0D3D9] ${
                      activeTab === item.id ? "bg-black" : ""
                    } cursor-pointer`}
                    onClick={() => setActiveTab(item.id)}
                  >
                    <div>
                      <a
                        className={`font-medium ${
                          activeTab === item.id
                            ? "text-[#ffffff]"
                            : "text-[#000000]"
                        } cursor-pointer text-left xl:text-lg text-sm`}
                        href="#"
                      >
                        {item.label}
                      </a>
                      <h5
                        className={`text-base text-[#00000080] font-semibold mt-3 ${
                          activeTab === item.id
                            ? "text-[#FFFFFF80]"
                            : "text-[#00000080]"
                        } cursor-pointer`}
                      >
                        <span
                          className={`lg:text-[26px] text-lg ${
                            activeTab === item.id
                              ? "text-[#40E0D0]"
                              : "text-[#000000]"
                          } cursor-pointer`}
                        >
                          {" "}
                          {EventList?.length || 0}
                        </span>
                      </h5>
                    </div>
                    {activeTab === item.id ? (
                      <Image
                        src={item.icon}
                        width={40}
                        height={40}
                        alt="BgImg"
                        className="ml-auto xl:w-[40px] xl:h-[40px] lg:w-[30px] lg:h-[30px] w-[25px] h-[25px]"
                        loading="lazy"
                      />
                    ) : (
                      <Image
                        src={item.icon1}
                        width={40}
                        height={40}
                        alt="BgImg"
                        className="ml-auto xl:w-[40px] xl:h-[40px] lg:w-[30px] lg:h-[30px] w-[25px] h-[25px]"
                        loading="lazy"
                      />
                    )}
                  </li>
                ))}
              </ul>
              {/* <div className='ml-auto flex gap-4 items-center'>
                  <div className='relative'>
                    <button
                      type='button'
                      className='absolute text-black top-1/2 left-2.5 transform -translate-y-1/2'
                    >
                      <Search size={16} />
                    </button>
                    <input
                      type='text'
                      className='px-8 py-3 rounded-md w-80 outline-none border text-sm font-light'
                      placeholder='Search Arrivals'
                    />
                    <button
                      className='absolute text-black top-1/2 right-2.5 transform -translate-y-1/2'
                      type='button'
                      onClick={handleOpenFilter}
                    >
                      <SlidersHorizontal size={16} />
                    </button>
                  </div>
                  <button
                    className='bg-blue-500 text-white px-4 py-3 text-sm rounded'
                    onClick={() => setOpenAddEvent(true)}
                  >
                    Add Event
                  </button>
                </div> */}
            </div>
            {activeTab === "Event" ? (
              <>
                <div className="overflow-x-auto mt-4 rounded-md ">
                  <table className="min-w-full border">
                    <thead>
                      <tr className="bg-gray-100 border-b text-nowrap">
                        {headers.map((header, index) => (
                          <th
                            key={index}
                            className="p-4 text-xs font-bold text-[#000000] text-left"
                          >
                            {header}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {EventList?.map((item, index) => (
                        <tr className="border-b" key={index}>
                          <td className="p-3 text-xs text-[#000]">
                            {item?._id}
                          </td>
                          <td className="p-3 text-xs text-[#000]">
                            {item?.title}
                          </td>
                          <td className="p-3 text-xs text-[#000]">
                            {getCurrencySymbol(item?.currency)} {item?.price}
                          </td>
                          <td className="p-3 text-xs text-[#000]">
                            {item?.duration}
                          </td>
                          <td className="p-3 text-xs text-[#000]">
                            {item?.address}
                          </td>
                          <td className="p-3 text-xs text-[#000]">
                            {item?.hours}h : {item?.minutes}m
                          </td>
                          <td className="p-3 text-xs text-[#000] text-wrap">
                            {item?.description}
                          </td>
                          <td className="p-3 text-xs text-[#000]">
                            <span
                              className={`inline-block px-2 py-1 text-xs whitespace-nowrap rounded-full ${
                                item?.status === "Upcoming"
                                  ? "bg-[#FEEDD8] text-[#F9A63A]"
                                  : item?.status === "Ongoing"
                                  ? "bg-[#41C58833] text-[#41C588]"
                                  : item?.status === "Completed"
                                  ? "bg-[#F3696033] text-[#F36960]"
                                  : ""
                              }`}
                            >
                              {item?.status}
                            </span>
                          </td>

                          {/* <td className="p-3 text-xs text-[#000]">
                        {item.balance}
                      </td> */}
                          <td className="p-3 text-xs text-[#000]">
                            <div className="flex flex-col gap-2">
                              {item?.tags?.map((data, index) => (
                                <span
                                  key={index}
                                  className="inline-block px-3 py-1 text-center text-xs whitespace-nowrap rounded-full bg-[#EEEEEE] text-[#448DF2]"
                                >
                                  {data}
                                </span>
                              ))}
                            </div>
                          </td>

                          <td className="p-4 relative">
                            <Menu
                              as="div"
                              className="relative inline-block text-left"
                            >
                              <div>
                                <MenuButton>
                                  <MoreVertical
                                    aria-hidden="true"
                                    size={16}
                                  ></MoreVertical>
                                </MenuButton>
                              </div>

                              <MenuItems
                                // transition
                                // ref={(el) => {
                                //   if (el) {
                                //     const rect = el.getBoundingClientRect();
                                //     const windowHeight = window.innerHeight;
                                //     if (rect.bottom > windowHeight) {
                                //       el.classList.add("bottom-full", "mb-2"); // Open upwards
                                //       el.classList.remove("mt-2"); // Remove default margin if needed
                                //     } else {
                                //       el.classList.remove("bottom-full", "mb-2"); // Default downwards
                                //       el.classList.add("mt-2"); // Maintain default spacing
                                //     }
                                //   }
                                // }}
                                transition
                                ref={(el) => {
                                  if (el) {
                                    const rect = el.getBoundingClientRect();
                                    const windowHeight = window.innerHeight;
                                    const isLastItem =
                                      index === EventList.length - 1;
                                    const isOnlyItem = EventList.length === 1;

                                    // Clear any previously applied classes
                                    el.classList.remove(
                                      "bottom-full",
                                      "mb-2",
                                      "mt-2",
                                      "top-1/2",
                                      "-translate-y-1/2"
                                    );

                                    if (isOnlyItem) {
                                      // Center the dropdown vertically in the viewport
                                      el.classList.add(
                                        "top-1/2",
                                        "-translate-y-1/2"
                                      );
                                    } else if (
                                      isLastItem ||
                                      rect.bottom > windowHeight
                                    ) {
                                      el.classList.add("bottom-full", "mb-2");
                                    } else {
                                      el.classList.add("mt-2");
                                    }
                                  }
                                }}
                                className="absolute right-0 z-10 mt-2 w-max origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
                              >
                                <div>
                                  <MenuItem>
                                    <button
                                      href="#"
                                      className="px-4 py-2 text-sm w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-t-md"
                                      onClick={() => {
                                        setopenViewevent(true);
                                        setEditId(item?._id);
                                      }}
                                    >
                                      <Eye size={16}></Eye>
                                      View
                                    </button>
                                  </MenuItem>
                                  <MenuItem>
                                    <button
                                      href="#"
                                      className="px-4 py-2 text-sm w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-t-md"
                                      onClick={() => {
                                        setopenEditevent(true);
                                        setEditId(item?._id);
                                      }}
                                    >
                                      <Pencil size={16}></Pencil>
                                      Edit
                                    </button>
                                  </MenuItem>
                                  <MenuItem>
                                    <button
                                      onClick={() => {
                                        handleDelete(item?._id);
                                      }}
                                      className="px-4 py-2 text-sm text-red-600 w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-b-md"
                                    >
                                      <Trash
                                        size={16}
                                        className="text-red-600"
                                      />{" "}
                                      <span>Delete</span>
                                    </button>
                                  </MenuItem>
                                </div>
                              </MenuItems>
                            </Menu>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                {/* {totalPages > 1 && (
                    <div className='flex justify-center mt-4'>
                      <CustomPagination
                        currentPage={currentPage}
                        total={totalPages}
                        onPageChange={handlePageChange}
                      />
                    </div>
                  )} */}

                {EventList?.length > 0 && (
                  <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    totalItems={totalData || 0}
                    itemsPerPage={itemsPerPage}
                    onPageChange={handlePageChange}
                    onItemsPerPageChange={handleItemsPerPageChange}
                  />
                )}
              </>
            ) : (
              <ViewMember />
            )}
          </div>
        </section>
      )}

      {isModalOpen && (
        <Dialog
          open={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          className="relative z-50"
        >
          <DialogBackdrop
            transition
            className="fixed inset-0 bg-[#000000B2] transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
          />

          <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
            <div className="flex min-h-full justify-center p-4 text-center items-center sm:p-0">
              <DialogPanel
                transition
                className="relative transform overflow-hidden rounded-2xl bg-white text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-full sm:max-w-[490px] max-w-full w-full data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95"
              >
                <div className="bg-white sm:px-7 sm:pb-7 pt-3 p-3 pb-3">
                  <DialogTitle>
                    <h3 className="text-center text-black font-bold sm:text-lg text-sm">
                      Filter
                    </h3>
                  </DialogTitle>
                  <Filter closefilterModal={closefilterModal} />
                </div>
              </DialogPanel>
            </div>
          </div>
        </Dialog>
      )}

      <Transition show={openAddevent} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closeeventModal}>
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <h2 className="page-title">Add event</h2>
                    <button
                      onClick={closeeventModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <Event
                      closeeventModal={closeeventModal}
                      updateEventList={updateEventList}
                    ></Event>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      <Transition show={openEditevent} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-50"
          onClose={closeeventeditModal}
        >
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <h2 className="page-title">Edit event</h2>
                    <button
                      onClick={closeeventeditModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <EditEvent
                      closeeventeditModal={closeeventeditModal}
                      updateEventList={updateEventList}
                      editId={editId}
                    ></EditEvent>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      <Transition show={openViewevent} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-50"
          onClose={closeeventviewModal}
        >
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <h2 className="page-title">View event</h2>
                    <button
                      onClick={closeeventviewModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <ViewEvent
                      updateEventList={updateEventList}
                      editId={editId}
                    ></ViewEvent>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
};

export default Page;
