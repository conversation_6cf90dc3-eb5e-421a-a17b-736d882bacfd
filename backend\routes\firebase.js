import express from 'express';
import { saveFcmToken,removeFcmToken } from '../controller/firebase.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();

// Route to save FCM token
router.post('/save-fcm-token',saveFcmToken);
router.post('/remove-fcm-token', removeFcmToken);

export default router;
/**
 * @swagger
 * tags:
 *   name: Tokens
 *   description: API endpoints for managing FCM tokens
 */

/**
 * @swagger
 * /api/save-fcm-token:
 *   post:
 *     summary: Save or update FCM token
 *     tags: [Tokens]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               token:
 *                 type: string
 *                 description: The FCM token to be saved.
 *               userId:
 *                 type: string
 *                 description: The ID of the user associated with the token.
 *     responses:
 *       200:
 *         description: FCM token saved successfully
 *       400:
 *         description: Bad request (e.g., missing token or userId)
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /api/remove-fcm-token:
 *   post:
 *     summary: Remove FCM token
 *     tags: [Tokens]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               token:
 *                 type: string
 *                 description: The FCM token to be saved.
 *               userId:
 *                 type: string
 *                 description: The ID of the user associated with the token.
 *     responses:
 *       200:
 *         description: FCM token remove successfully
 *       400:
 *         description: Bad request (e.g., missing token or userId)
 *       500:
 *         description: Internal server error
 */