"use client";
import { Plus } from "lucide-react";
import Link from "next/link";
import React from "react";
import { FiEye } from "react-icons/fi";
import { MdOutlineKeyboardArrowLeft, MdOutlineKeyboardArrowRight } from "react-icons/md";
import { TfiPencilAlt } from "react-icons/tfi";

const Support = () => {
  const supportData = [
    {
      id: 1,
      name: "what is Mixdorm",
      
      feedback:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      
    },
    {
      id: 2,
      name: "what is Mixdorm",
      hostelname: `88 Backpackers`,
      feedback:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: 4.5,
    },
    {
      id: 3,
      name: "what is Mixdorm",
      hostelname: `88 Backpackers`,
      feedback:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: 4.5,
    },
    {
      id: 4,
      name: "what is Mixdorm",
      hostelname: `88 Backpackers`,
      feedback:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: 4.5,
    },
    {
      id: 5,
      name: "what is Mixdorm",
      hostelname: `88 Backpackers`,
      feedback:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: 4.5,
    },
    {
      id: 6,
      name: "what is Mixdorm",
      hostelname: `88 Backpackers`,
      feedback:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: 4.5,
    },
    {
      id: 7,
      name: "what is Mixdorm",
      hostelname: `88 Backpackers`,
      feedback:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: 4.5,
    },
    {
      id: 8,
      name: "what is Mixdorm",
      hostelname: `88 Backpackers`,
      feedback:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: 4.5,
    },
    {
      id: 9,
      name: "what is Mixdorm",
      hostelname: `88 Backpackers`,
      feedback:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: 4.5,
    },
    {
      id: 10,
      name: "what is Mixdorm",
      hostelname: `88 Backpackers`,
      feedback:
        "Amet minim mollit non deserunt ullamco est sit aliqua dolor do amet sint.",
      time: "03:50",
      date: "04/02/24",
      status: 4.5,
    },
  ];

  const splitMessage = (feedback) => {
    const words = feedback.split(" ");
    const midpoint = Math.ceil(words.length / 2);
    const firstPart = words.slice(0, midpoint).join(" ");
    const secondPart = words.slice(midpoint).join(" ");
    return { firstPart, secondPart };
  };
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <div className="flex items-center justify-between w-full ">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Support
        </h2>
        <div className="w-[50%] gap-x-5 flex justify-end items-center">
          <Link href={"/superadmin/dashboard/support-add"}
            className={` px-4 py-2 text-sm font-medium  font-poppins text-white rounded relative flex justify-center items-center bg-sky-blue-650 `}
            type="button"
            
          >
            <Plus size={18} className="mr-1" /> Support
          </Link>
        </div>
      </div>
      <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border dark:border-none">
        <table className="w-full divide-y bg-white rounded-xl divide-gray-200 dark:bg-black">
          <thead>
            <tr className="">
              <th className="px-5 py-6 bg-white text-center text-sm font-semibold font-poppins text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                Questions
              </th>

              <th className="pr-4 pl-2 py-6 bg-white lg:text-center text-sm font-semibold text-center font-poppins text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                ANSWER
              </th>

              <th className="py-6 pl-0   bg-white  text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6] text-center">
                ACTION
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 border-x border-y dark:border-x-0 text-black/70">
            {supportData.map((supportItem) => {
              const { firstPart, secondPart } = splitMessage(
                supportItem.feedback
              );
              return (
                <tr key={supportItem.id} className="">
                  <td className="whitespace-nowrap  px-5 pl-7 text-gray-500 text-sm font-medium font-poppins dark:text-[#757575] text-center">{supportItem.name}</td>

                  <td className="whitespace-nowrap text-sm font-medium font-poppins  break-words text-gray-500 max-w-md py-2 pl-4 lg:pl-0 lg:py-0 dark:text-[#757575] text-center pr-2 ">
                    <div>
                      <span>{firstPart}</span>
                      <br />
                      <span>{secondPart}</span>
                    </div>
                  </td>
                

                  <td className="py-8 lg:py-5  px-6 flex justify-center">
                    <Link href={"/superadmin/dashboard/support-details"} className="border py-1 px-2 lg:py-2 lg:px-3 rounded-l-lg text-black/75 hover:text-blue-700 dark:text-[#757575] hover:hover:text-blue-700">
                      <FiEye />
                    </Link>
                    <Link href={"/superadmin/dashboard/support-edit"} className=" border py-1 px-2 lg:py-2 lg:px-3 rounded-r-lg text-black/75 hover:text-yellow-400 dark:text-[#757575] dark:hover:text-yellow-400">
                      <TfiPencilAlt />
                    </Link>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
            <div className="flex justify-between items-center mt-5">
              <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
              <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
                <a
                  href="#"
                  className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                >
                  <span className="sr-only">Next Page</span>
                  <MdOutlineKeyboardArrowLeft />
                </a>
      
                <a
                  href="#"
                  className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                >
                  <span className="sr-only">Next Page</span>
                  <MdOutlineKeyboardArrowRight />
                </a>
              </div>
            </div>
    </div>
  );
};

export default Support;
