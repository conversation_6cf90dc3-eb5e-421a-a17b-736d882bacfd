 
import Image from "next/image";
import Link from "next/link";
import { ChevronDown, Globe } from "lucide-react";
import { useNavbar } from "../home/<USER>";

const UserNavbar = () => {
  const { token,role } = useNavbar();

  return (
    <>
      <section
        className={`w-full duration-300 bg-black ease-in-out sticky top-0 z-50 `}
      >
        <div className="relative z-50 flex items-center justify-between px-5 py-4 mx-auto bg-black xl:px-12 xl:container xl:max-w-screen-xl">
          <div className="w-[14%]">
            <Link href={token && role !=="user" ? "/owner/hostel-login" : "/"} rel="canonical" prefetch={false}>
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mixdrom-dark.svg`}
                width={150}
                height={40}
                alt="Mixdorm"
                title="Mixdorm"
                className="max-w-[110px] md:max-w-[155px] md:max-h-24 relative z-50 w-fit object-contain bg-blend-color-burn  cursor-pointer hover:scale-95  duration-500 ease-in-out"
                loading="lazy"
              />
            </Link>
          </div>
          <div className="w-[40%] flex justify-end  items-center ">
            <ul className="flex items-center justify-center gap-x-5 ">
              <li
                className={`text-sm font-manrope flex justify-center items-center  font-bold  cursor-pointer  text-white duration-300 ease-in-out`}
              >
                <Link
                  href="#"
                  rel="canonical"
                  className="flex items-center justify-start gap-x-2"
                  prefetch={false}
                >
                  <Globe size={20} />
                  English
                  <ChevronDown size={18} />
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </section>
    </>
  );
};

export default UserNavbar;
