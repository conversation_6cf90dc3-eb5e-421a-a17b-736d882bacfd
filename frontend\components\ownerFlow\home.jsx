/* eslint-disable no-irregular-whitespace */
import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { GoogleMap, Marker, InfoWindow } from "@react-google-maps/api";
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
} from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { getOwnerDashboardDataApi } from "@/services/ownerflowServices";
import { getItemLocalStorage } from "@/utils/browserSetting";
import countries from "world-countries";
import Loader from "../loader/loader";
import ReactStars from "react-rating-stars-component";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/navigation";
import { CircleChevronLeft, CircleChevronRight } from "lucide-react";
const GOOGLE_MAPS_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

const Home = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [dashboardData, setDashboardData] = useState([]);
  const [flags, setFlags] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isApiLoaded, setIsApiLoaded] = useState(false);
  const [selectedMarker, setSelectedMarker] = useState(null);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageFailed, setImageFailed] = useState(false);
  
    const [imagePreviewModal, setImagePreviewModal] = useState(false);
  const [previewIndex, setPreviewIndex] = useState(0);
  const [activeIndex, setActiveIndex] = useState(0);

  const mainSwiperRef = useRef(null);
  const mainThumbRef = useRef(null);

  const images = dashboardData?.counts?.propertyDeatails?.images || [];

  const isFirstRender = useRef(null);

  const storedId = getItemLocalStorage("hopid");

  const amenitiesList = [
    {
      key: "lockers",
      label: "Lockers",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/lockers.svg`,
    },
    {
      key: "hot_water",
      label: "Hot water",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/hot_water.svg`,
    },
    {
      key: "laundry_services",
      label: "Laundry Services (Extra)",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/laundry_services.svg`,
    },
    {
      key: "FREEWIFI",
      label: "Free Wi-Fi",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/free_wi_fi.svg`,
    },
    {
      key: "card_payment_accepted",
      label: "Card Payment Accepted",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/card_payment_accepted.svg`,
    },
    {
      key: "common_television",
      label: "Common Television",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/common_television.svg`,
    },
    {
      key: "water_dispenser",
      label: "Water Dispenser",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/water_dispenser.svg`,
    },
    {
      key: "air_conditioning",
      label: "Air-conditioning",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/air_conditioning.svg`,
    },
    {
      key: "reception",
      label: "24/7 Reception",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/24_7_reception.svg`,
    },
    {
      key: "common_hangout_area",
      label: "Common hangout area",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/common_hangout_area.svg`,
    },
    {
      key: "cafe",
      label: "Cafe",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/cafe.svg`,
    },
    {
      key: "in_house_activities",
      label: "In-house Activities",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/in_house_activities.svg`,
    },
    {
      key: "bedside_lamps",
      label: "Bedside Lamps",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/bedside_lamps.svg`,
    },
    {
      key: "BREAKFASTINCLUDED",
      label: "Breakfast (Extra)",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/breakfast.svg`,
    },
    {
      key: "storage_facility",
      label: "Storage Facility",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/storage_facility.svg`,
    },
    {
      key: "TOWELSINCLUDED",
      label: "Towels on rent",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/towels_on_rent.svg`,
    },
    {
      key: "LINENINCLUDED",
      label: "Linen Included",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/linen_included.svg`,
    },
    {
      key: "upi_payment_accepted",
      label: "UPI Payment Accepted",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/upi_payment_accepted.svg`,
    },
    {
      key: "shower",
      label: "Shower",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/shower.svg`,
    },
    {
      key: "FREEPARKING",
      label: "Parking (public)",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/parking.svg`,
    },
  ];

  console.log("amenitiesData", dashboardData);

  const mapContainerStyle = {
    width: "100%",
    height: "146px",
  };

  const fullAddress = [
    dashboardData?.counts?.propertyDeatails?.address?.lineOne,
    dashboardData?.counts?.propertyDeatails?.address?.city,
    dashboardData?.counts?.propertyDeatails?.address?.state,
    dashboardData?.counts?.propertyDeatails?.address?.country,
  ]
    .filter(Boolean)
    .join(", ");

  const tabs = [
    {
      id: "overview",
      label: "Overview",
      content: dashboardData?.counts?.propertyDeatails?.aboutUs,
    },
    {
      id: "rules",
      label: "Rules",
      content: (
        <div className='flex flex-col justify-center'>
          {/* Check In */}
          <div className='flex items-center gap-3 mb-4'>
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/checkin-b.svg`}
              width={22.68}
              height={22.68}
              className='h-5 w-5'
            />
            <span>
              {" "}
              <span className='font-medium text-sm font-inter'>Check In</span>
              <span className='font-bold text-xs font-inter ml-12'>
                14:00 - 23:00
              </span>
            </span>
          </div>

          {/* Check Out */}
          <div className='flex items-center gap-3 mb-4'>
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/checkout-b.svg`}
              width={22.68}
              height={22.68}
              className='h-5 w-5'
            />
            <span>
              <span className='font-medium text-sm font-inter'>Check Out</span>
              <span className='font-bold text-xs font-inter ml-[38px]'>
                14:00 - 23:00
              </span>
            </span>
          </div>

          {/* Cancellation */}
          <div className='flex gap-3 mb-4'>
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/info-b.svg`}
              width={22.68}
              height={22.68}
              className='h-5 w-5 mt-1'
            />
            <div className='flex'>
              <span className='font-medium text-sm font-inter'>
                Cancellation/ <br /> Prepayment
              </span>
              <p className='text-xs font-inter ml-5'>
                Cancellation and prepayment policies vary according to
                accommodation type. Please enter the dates of your stay and
                check the conditions of your required room.
              </p>
            </div>
          </div>

          {/* Children and Beds */}
          <div className='flex gap-3 mb-4'>
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/child-b.svg`}
              width={22.68}
              height={22.68}
              className='h-5 w-5'
            />
            <span className='font-medium text-sm font-inter whitespace-nowrap'>
              Children and <br /> beds
            </span>
            <div>
              <span className=''>
                {" "}
                <p className='text-xs font-semibold  ml-3'>Child Policies</p>
                <p className='text-xs ml-3'>Children are not allowed.</p>
                <p className='text-xs font-semibold ml-3'>
                  Cot and extra bed policies
                </p>
                <p className='text-xs ml-3'>
                  Cots and extra beds are not available at this property.
                </p>
              </span>
            </div>
          </div>

          {/* Age Restriction */}
          <div className='flex gap-3 mb-4'>
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/age-b.svg`}
              width={22.68}
              height={22.68}
              className='h-5 w-5'
            />
            <span className='font-medium text-sm font-inter whitespace-nowrap'>
              Age restriction
            </span>
            <div>
              <span className='text-xs font-inter '>
                Check-in is only possible for guests aged between 18 and 45.
              </span>
            </div>
          </div>

          {/* Pets */}
          <div className='flex gap-3'>
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/pets-b.svg`}
              width={22.68}
              height={22.68}
              className='h-5 w-5'
            />
            <span className='font-medium font-inter text-sm'>Pets</span>
            <div>
              <p className='text-xs font-inter ml-[70px] mt-1'>
                Pets are not allowed.
              </p>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: "amenities",
      label: "Amenities",
      content: (
        <div className='grid grid-cols-2 gap-5 text-black'>
          {amenitiesList
            .filter((amenity) =>
              dashboardData?.counts?.propertyDeatails?.freeFacilities?.some(
                (facility) => facility.id === amenity.key
              )
            )
            .map(({ label, icon }) => (
              <div key={label} className='flex items-center space-x-2'>
                <span className='text-lg'>
                  <Image
                    src={icon}
                    width={22.68}
                    height={22.68}
                    className='h-5 w-5'
                  />
                </span>
                <span className='font-medium font-manrope'>{label}</span>
              </div>
            ))}
        </div>
      ),
    },
  ];

  const card1Ref = useRef(null); // First div
  const card2Ref = useRef(null); // Second div

  useEffect(() => {
    function matchHeights() {
      if (card1Ref.current && card2Ref.current) {
        const height1 = card1Ref.current.offsetHeight;
        const height2 = card2Ref.current.offsetHeight;
        const maxHeight = Math.max(height1, height2);
        card1Ref.current.style.height = `${maxHeight}px`;
        card2Ref.current.style.height = `${maxHeight}px`;
      }
    }

    matchHeights();

    window.addEventListener("resize", matchHeights);
    return () => window.removeEventListener("resize", matchHeights);
  }, []);

  // const [matchedHeight, setMatchedHeight] = useState("auto");

  // useEffect(() => {
  //   const syncHeights = () => {
  //     if (card1Ref.current && card2Ref.current) {
  //       const h1 = card1Ref.current.offsetHeight;
  //       const h2 = card2Ref.current.offsetHeight;
  //       const maxHeight = Math.max(h1, h2);
  //       setMatchedHeight(`${maxHeight}px`);
  //     }
  //   };

  //   syncHeights();
  //   window.addEventListener("resize", syncHeights);

  //   return () => {
  //     window.removeEventListener("resize", syncHeights);
  //   };
  // }, []);

  //for calendar
  const [selectedId, setSelectedId] = useState(null);
  // eslint-disable-next-line no-unused-vars
  const [data, setData] = useState(null);

  useEffect(() => {
    // Retrieve selectedId from localStorage
    const storedId = localStorage.getItem("selectedId");
    if (storedId) {
      setSelectedId(storedId);
    }
    // fetchProperties();
  }, []);

  useEffect(() => {
    // Update localStorage when selectedId changes
    if (selectedId) {
      localStorage.setItem("selectedId", selectedId);
      // fetchPropertiesById(selectedId);
    }
  }, [selectedId]);

  const fetchDashboardData = async (id) => {
    setLoading(true);
    try {
      const response = await getOwnerDashboardDataApi(id);
      setDashboardData(response?.data?.data);
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!isFirstRender.current) {
      fetchDashboardData(storedId);
    } else {
      isFirstRender.current = false;
    }
  }, [storedId]);

  const loadGoogleMapsApi = () => {
    const script = document.createElement("script");
    script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&libraries=places`;
    script.async = true;
    script.onload = () => setIsApiLoaded(true);
    document.head.appendChild(script);
  };

  useEffect(() => {
    if (window.google && window.google.maps) {
      setIsApiLoaded(true);
    } else {
      loadGoogleMapsApi();
    }
  }, []);

  useEffect(() => {
    const fetchFlags = () => {
      try {
        const filteredFlags = countries
          .filter((country) => {
            const commonName = country.name.common;
            return dashboardData?.counts?.propertyDeatails?.address?.country?.includes(
              commonName
            );
          })
          .map((country) => ({
            id: country.cca3,
            img:
              // eslint-disable-next-line no-constant-binary-expression
              `https://flagcdn.com/w320/${country.cca2.toLowerCase()}.png` ||
              "https://via.placeholder.com/30x25",
            name: country.name.common,
          }));

        setFlags(filteredFlags);
      } catch (error) {
        console.error("Error processing flags:", error);
      }
    };

    if (dashboardData?.counts?.propertyDeatails?.address?.country) {
      fetchFlags();
    }
  }, [dashboardData?.counts?.propertyDeatails?.address?.country]);

  const [isModalOpen, setIsModalOpen] = useState(false);

  if (!isApiLoaded) {
    return <Loader open={true} />;
  }

  return (
    <>
      <Loader open={loading} />
      { loading ? ( 
        <div>
  <h1 className='page-title'>
    <div className='h-8 w-32 bg-gray-200 rounded animate-pulse'></div>
  </h1>

  <div className='main-content-wrap rounded-lg md:p-6 p-2 mt-7'>
    <div className='grid lg:grid-cols-3 grid-cols-1 gap-4 items-stretch'>
      {/* First Column */}
      <div className='h-full'>
        <div className='min-h-[130px] flex px-4 py-4 border border-slate-200 rounded-lg mb-4 gap-x-4'>
          <div className='relative flex justify-center items-center'>
            <div className='h-20 w-20 rounded-full bg-gray-200 animate-pulse'></div>
          </div>
          <div className='flex flex-col justify-center space-y-2'>
            <div className='h-6 w-40 bg-gray-200 rounded animate-pulse'></div>
            <div className='h-8 w-44 bg-gray-200 rounded-lg animate-pulse'></div>
          </div>
        </div>
        
        <div className='md:px-4 md:py-6 px-2 py-3 border border-slate-200 border-1 rounded-lg'>
          <div className='flex items-center justify-between mb-4'>
            <div className='h-5 w-32 bg-gray-200 rounded animate-pulse'></div>
            <div className='h-5 w-16 bg-gray-200 rounded animate-pulse'></div>
          </div>
          {[1, 2].map((item) => (
            <div key={item} className='py-2 px-3 border border-slate-200 border-1 rounded-lg mb-3 flex gap-2'>
              <div className='h-16 w-16 bg-gray-200 rounded-md animate-pulse'></div>
              <div className='space-y-2 flex-1'>
                <div className='h-4 w-3/4 bg-gray-200 rounded animate-pulse'></div>
                <div className='h-3 w-1/2 bg-gray-200 rounded animate-pulse'></div>
                <div className='h-3 w-1/3 bg-gray-200 rounded animate-pulse'></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Second Column */}
      <div className='h-full'>
        <div className='min-h-[130px] flex justify-between px-4 py-4 border border-slate-200 rounded-lg mb-4'>
          <div className='flex gap-4 items-center'>
            <div className='h-20 w-20 bg-gray-200 rounded animate-pulse'></div>
            <div className='space-y-2'>
              <div className='h-6 w-32 bg-gray-200 rounded animate-pulse'></div>
              <div className='h-4 w-24 bg-gray-200 rounded animate-pulse'></div>
            </div>
          </div>
          <div className='h-4 w-4 bg-gray-200 rounded animate-pulse'></div>
        </div>
        
        <div className='md:px-4 md:py-6 px-2 py-3 border border-slate-200 border-1 rounded-lg'>
          <div className='flex items-center justify-between mb-4'>
            <div className='h-5 w-32 bg-gray-200 rounded animate-pulse'></div>
            <div className='h-5 w-16 bg-gray-200 rounded animate-pulse'></div>
          </div>
          {[1, 2].map((item) => (
            <div key={item} className='p-4 bg-no-repeat bg-full mb-5'>
              <div className='flex gap-3 items-center mb-2'>
                <div className='h-10 w-10 bg-gray-200 rounded-full animate-pulse'></div>
                <div className='space-y-2 flex-1'>
                  <div className='h-4 w-3/4 bg-gray-200 rounded animate-pulse'></div>
                  <div className='h-3 w-1/2 bg-gray-200 rounded animate-pulse'></div>
                </div>
                <div className='h-4 w-20 bg-gray-200 rounded animate-pulse'></div>
              </div>
              <div className='h-3 w-full bg-gray-200 rounded animate-pulse'></div>
              <div className='h-3 w-4/5 bg-gray-200 rounded animate-pulse mt-1'></div>
            </div>
          ))}
        </div>
      </div>

      {/* Third Column */}
      <div className='md:px-4 md:py-6 px-2 py-4 border border-slate-200 border-1 rounded-lg bg-[#40E0D033] h-auto self-start'>
        <div className='flex items-center justify-between mb-4'>
          <div className='h-6 w-32 bg-gray-200 rounded animate-pulse'></div>
          <div className='h-5 w-16 bg-gray-200 rounded animate-pulse'></div>
        </div>
        
        <div className='grid grid-cols-2 mb-5 space-x-0 lg:space-x-3 xl:space-x-0'>
          {[1, 2].map((item) => (
            <div key={item} className='flex gap-2 items-center'>
              <div className='h-10 w-10 bg-gray-200 rounded animate-pulse'></div>
              <div className='space-y-1'>
                <div className='h-3 w-16 bg-gray-200 rounded animate-pulse'></div>
                <div className='h-3 w-12 bg-gray-200 rounded animate-pulse'></div>
              </div>
            </div>
          ))}
        </div>
        
        <div className='grid md:grid-cols-2 grid-cols-1 gap-2 mb-3.5'>
          <div className='h-44 w-full bg-gray-200 rounded-lg animate-pulse'></div>
          <div className='grid grid-cols-2 gap-2'>
            {[1, 2, 3, 4].map((item) => (
              <div key={item} className='h-20 w-full bg-gray-200 rounded-lg animate-pulse'></div>
            ))}
          </div>
        </div>
        
        <div className='mb-4'>
          <div className='tabs flex gap-7 xl:gap-7 lg:gap-5'>
            {[1, 2, 3].map((item) => (
              <div key={item} className='h-6 w-16 bg-gray-200 rounded animate-pulse'></div>
            ))}
          </div>
          
          <div className='mt-3.5'>
            <div className='h-3 w-full bg-gray-200 rounded animate-pulse'></div>
            <div className='h-3 w-4/5 bg-gray-200 rounded animate-pulse mt-1'></div>
            <div className='h-3 w-3/4 bg-gray-200 rounded animate-pulse mt-1'></div>
          </div>
        </div>
        
        <div className='flex gap-2 items-center mb-4'>
          <div className='h-10 w-10 bg-gray-200 rounded animate-pulse'></div>
          <div className='space-y-1'>
            <div className='h-3 w-16 bg-gray-200 rounded animate-pulse'></div>
            <div className='h-3 w-24 bg-gray-200 rounded animate-pulse'></div>
          </div>
        </div>
        
        <div className='h-36 w-full bg-gray-200 rounded-lg animate-pulse'></div>
      </div>
    </div>
  </div>
        </div> 
        ) :
       (
         <div>
        <h1 className='page-title'>Dashboard</h1>

        <div className='main-content-wrap rounded-lg md:p-6 p-2 mt-7'>
          <div className='grid lg:grid-cols-3 grid-cols-1 gap-4 items-stretch'>
            <div className='h-full '>
              <div
                className='min-h-[130px] flex px-4 py-4 border border-slate-200 rounded-lg mb-4 gap-x-4'
                ref={card1Ref}
                // style={{ height: matchedHeight }}
              >
                <div className='relative flex justify-center items-center'>
                  {/* <div className='hotel-img-wrap relative w-max '>
                    <Image
                      // src={`${
                      //   dashboardData?.counts?.propertyDeatails?.photos?.[0]?.url?.startsWith(
                      //     "https"
                      //   )
                      //     ? ""
                      //     : "https://"
                      // }${
                      //   dashboardData?.counts?.propertyDeatails?.photos?.[0]
                      //     ?.url
                      // }`}
                      src={
                        dashboardData?.counts?.propertyDeatails?.images?.[0]
                          ?.objectUrl
                      }
                      width={73.08}
                      height={73.08}
                      className='h-20 w-20 rounded-full border border-black'
                    />
                    {flags?.[0]?.img && (
                      <Image
                        className='absolute bottom-1 -right-1 h-5 w-5 rounded-full z-10'
                        src={`${flags?.[0]?.img}`}
                        width={16.42}
                        height={16.42}
                      ></Image>
                    )}
                  </div> */}

                  {dashboardData?.counts?.propertyDeatails?.images?.[0]?.objectUrl ? (
                       <div className='relative'>
                         {(!imageLoaded || imageFailed) && (
                           <div className='h-20 w-20 rounded-full border border-gray-400 bg-gray-200 flex justify-center items-center animate-pulse'>
                             <span className='text-gray-400 font-bold'>MixDorm</span>
                           </div>
                         )}
                         {!imageFailed && (
                          <div className='hotel-img-wrap relative w-max '>
                           <Image
                             src={dashboardData.counts.propertyDeatails.images[0].objectUrl}
                             width={80}
                             height={80}
                             className={`w-20 h-20 rounded-full border  object-cover ${
                               !imageLoaded ? 'opacity-0' : 'opacity-100'
                             }`}
                             onLoadingComplete={() => setImageLoaded(true)}
                             onError={() => setImageFailed(true)}
                           />
                             {flags?.[0]?.img && (
                       <Image
                         className='absolute bottom-1 -right-1 h-5 w-5 rounded-full z-10'
                         src={`${flags?.[0]?.img}`}
                         width={16.42}
                         height={16.42}
                       />
                     )}
                            </div>
                         )}
                       </div>
                     ) : (
                       <div className='h-20 w-20 rounded-full border border-gray-400 bg-gray-200 flex justify-center items-center'>
                         <span className='text-gray-400 font-bold'>MixDorm</span>
                       </div>
                     )}
                   

                  {imageLoaded && !imageFailed && (
    <div className='absolute w-[90px] h-[90px] border-[3px] border-t-0 border-l-0 border-r-primary-blue border-b-primary-blue rounded-full -right-1 -rotate-45'></div>
  )}
                  {/* <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2 w-24 h-12 border-t-4 border-blue-500 rounded-t-full"></div> */}
                </div>
                <div className='flex flex-col justify-center'>
                  <h3 className='font-bold text-xl md:text-lg xl:text-xl text-black'>
                    {dashboardData?.counts?.propertyDeatails?.name}
                  </h3>
                  {/* <div className='bg-slate-200 rounded-lg p-2 gap-2 flex items-center max-w-44'>
                    <Image
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/profile-views.svg`}
                      width={22.68}
                      height={22.68}
                    />
                    <p className='mb-0 font-normal text-sm text-black'>
                      52k Profile Views
                    </p>
                  </div> */}
                </div>
              </div>
              <div className='md:px-4 md:py-6 px-2 py-3 border border-slate-200 border-1 rounded-lg'>
                <div className='flex items-center justify-between mb-4'>
                  <p className='mb-0 text-base text-black font-semibold'>
                    Recent Bookings
                  </p>
                  <Link href='/owner/dashboard/booking'>
                    <p className='mb-0 text-base text-black font-bold'>
                      See all
                    </p>
                  </Link>
                </div>
                {dashboardData?.counts?.totalBookings?.length > 0
                  ? dashboardData?.counts?.totalBookings?.map((booking) => (
                      // eslint-disable-next-line react/jsx-key
                      <Link href='/owner/dashboard/booking'>
                        <div className='py-2 px-3 border border-slate-200 border-1 rounded-lg mb-3 flex gap-2'>
                          <Image
                            className='rounded-md h-16 w-16'
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/usericon.png`}
                            width={70.44}
                            height={65}
                          ></Image>
                          <div>
                            <p className='text-sm font-bold mb-1'>
                              {booking?.guestDetails?.name}
                            </p>
                            <p className='text-xs font-medium flex mb-2 items-center'>
                              <span className='mr-1'>
                                <Image
                                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/calendar.svg`}
                                  width={14}
                                  height={14}
                                ></Image>
                              </span>
                              <span>
                                {new Date(
                                  booking?.checkInDate
                                ).toLocaleDateString("en-US", {
                                  month: "short",
                                  day: "2-digit",
                                  year: "numeric",
                                })}
                                , 
                                {Math.max(
                                  (new Date(booking?.checkOutDate) -
                                    new Date(booking?.checkInDate)) /
                                    (1000 * 60 * 60 * 24),
                                  1
                                )}{" "}
                                 nights
                              </span>
                            </p>
                            <p className='text-xs font-medium flex mb-2 items-center'>
                              <span className='mr-1'>
                                <Image
                                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/guests.svg`}
                                  width={14}
                                  height={14}
                                ></Image>
                              </span>
                              <span>3 Guests</span>
                            </p>
                          </div>
                        </div>
                      </Link>
                    ))
                  : "No Booking Found"}
              </div>
            </div>
            <div className='h-full'>
              {/* <div className='md:px-4 md:py-6 px-2 py-3 border border-slate-200 border-1 rounded-lg flex gap-4 mb-4 items-center'>
                <div className='hotel-img-wrap relative w-max'>
                  <Image
                    src='https://www.ezeeabsolute.com/images/ezee-logo.svg'
                    width={73.08}
                    height={73.08}
                  />
                  <span className='w-[16.42px] h-[16.42px] rounded-full bg-green-500 absolute bottom-0 right-1'></span>
                </div>
                <div className='flex flex-col justify-between'>
                  <h3 className='font-bold text-xl text-black mb-2'>
                  Ezee 
                  </h3>
                  <p className='mb-0 font-normal text-base text-black/50'>
                    Connected
                  </p>
                </div>
              </div> */}
              <div
                className='min-h-[130px] flex  justify-between px-4 py-4 border border-slate-200 rounded-lg mb-4 cursor-pointer'
                // onClick={() => setIsModalOpen(true)}
                ref={card2Ref}
                // style={{ height: matchedHeight }}
              >
                <div className='flex gap-4 items-center'>
                  <div className='hotel-img-wrap relative w-max'>
                    <Image
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/partnership-Channel.png`}
                      width={80.08}
                      height={80.08}
                      className=''
                    />
                    <span className='w-[16.42px] h-[16.42px] rounded-full bg-red-500 absolute bottom-1 right-1'></span>
                  </div>
                  <div className='flex flex-col justify-between'>
                    <h3 className='font-bold text-xl text-black mb-2'>
                      Channel Partner{" "}
                    </h3>
                    <p className='mb-0 font-normal text-base text-black/50'>
                      Select partner
                    </p>
                  </div>
                </div>
                <Image
                  className='float-right'
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/right-arrow.svg`}
                  width={13}
                  height={13}
                ></Image>
              </div>
              <div className='md:px-4 md:py-6 px-2 py-3 border border-slate-200 border-1 rounded-lg'>
                <div className='flex items-center justify-between mb-4'>
                  <p className='mb-0 text-base text-black font-semibold'>
                    Guest Reviews
                  </p>
                  <Link href='/owner/dashboard/reviews'>
                    <p className='mb-0 text-base text-black font-bold'>
                      See all
                    </p>
                  </Link>
                </div>
                {dashboardData?.counts?.propertyDeatails?.googleReviews
                  ?.length > 0
                  ? dashboardData?.counts?.propertyDeatails?.googleReviews?.map(
                      (review) => (
                        // eslint-disable-next-line react/jsx-key
                        <div className='p-4  bg-no-repeat bg-full mb-5'>
                          <div className='flex gap-3 items-center mb-2 '>
                            <Image
                              className='rounded-full h-[39.55px] '
                              src={review?.profile_photo_url}
                              width={39.55}
                              height={39.55}
                            ></Image>
                            <div>
                              <p className='text-sm font-bold mb-1'>
                                {review?.author_name}
                              </p>
                              {/* <p className='text-xs font-normal flex mb-2 items-center text-slate-400'>
                                Marketing Manager
                              </p> */}
                            </div>
                            <div className='flex gap-0.5'>
                              <ReactStars
                                key={review?.rating}
                                count={5}
                                size={15}
                                activeColor='#ffd700'
                                value={review?.rating}
                                edit={false}
                                isHalf={true}
                              />
                            </div>
                          </div>
                          <p className='text-xs text-normal'>{review?.text}</p>
                        </div>
                      )
                    )
                  : "No Reviews Found"}
              </div>
            </div>
            <div className='md:px-4 md:py-6 px-2 py-4 border border-slate-200 border-1 rounded-lg bg-[#40E0D033] h-auto self-start'>
              <div className='flex items-center justify-between mb-4'>
                <p className='mb-0 text-lg lg:text-sm xl:text-lg text-black font-medium'>
                  {dashboardData?.counts?.propertyDeatails?.name}
                </p>
                <Link href='/owner/dashboard/propertyprofile'>
                  <p className='mb-0 text-sm lg:text-xs xl:text-sm  text-black font-bold whitespace-nowrap'>
                    View Property
                  </p>
                </Link>
              </div>
              <div className='grid grid-cols-2 mb-5 space-x-0 lg:space-x-3 xl:space-x-0'>
                <div className='flex gap-2 items-center'>
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/hotel-type.png`}
                    width={39.37}
                    height={39.37}
                  ></Image>
                  <div>
                    <p className='font-bold text-xs text-dark mb-1'>
                      Hostel Type
                    </p>
                    <p className='font-normal text-xs' color='#00000080'>
                      {dashboardData?.counts?.propertyDeatails?.type}
                    </p>
                  </div>
                </div>
                <div className='flex gap-2 items-center'>
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/rating.png`}
                    width={39.37}
                    height={39.37}
                  ></Image>
                  <div>
                    <p className='font-bold text-xs text-dark mb-1'>Rating</p>
                    <p className='font-normal text-xs' color='#00000080'>
                      {dashboardData?.counts?.propertyDeatails?.starRating} Star
                    </p>
                  </div>
                </div>
              </div>
              {/* {dashboardData?.counts?.propertyDeatails?.images?.length > 0 ? (
                <div className='grid md:grid-cols-2 grid-cols-1 gap-2 mb-3.5 '>
                  <div>
                    <Image
                      className='h-[173.65px] w-full rounded-lg'
                      src={`${
                        dashboardData?.counts?.propertyDeatails?.images?.[0]?.objectUrl?.startsWith(
                          "https"
                        )
                          ? ""
                          : "https://"
                      }${
                        dashboardData?.counts?.propertyDeatails?.images?.[0]
                          ?.objectUrl
                      }`}
                      width={173.65}
                      height={173.65}
                    ></Image>
                  </div>

                  <div className='grid grid-cols-2 gap-2'>
                    {dashboardData?.counts?.propertyDeatails?.images
                      ?.slice(1, 5)
                      .map((photo, index) => {
                        const currentPhoto =
                          dashboardData?.counts?.propertyDeatails?.images?.[
                            index + 1
                          ];
                        const imageUrl = `${
                          currentPhoto?.objectUrl?.startsWith("https")
                            ? ""
                            : "https://"
                        }${currentPhoto?.objectUrl}`;

                        return (
                          <div
                            key={index}
                            className='relative rounded-lg h-[82.15px] w-full overflow-hidden'
                          >
                            <Image
                              className='h-full w-full object-cover'
                              src={imageUrl}
                              width={98.66}
                              height={82.15}
                            />
                            {index === 3 && (
                              <div className='absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center text-white text-sm font-medium rounded-lg'>
                                +8
                              </div>
                            )}
                          </div>
                        );
                      })}
                  
                  </div>
                </div>
              ) : (
                <div className='mb-4 text-red-500'>
                  No Property Images Added
                </div>
              )} */}
          {dashboardData?.counts?.propertyDeatails?.images?.length > 0 ? (
                        <div className='grid md:grid-cols-2 grid-cols-1 gap-2 mb-3.5'>
                         
                          <div className='relative h-[173.65px] w-full rounded-lg bg-gray-100 overflow-hidden '
                           onClick={() => {
                              setPreviewIndex(0);
                              setActiveIndex(0);
                              setImagePreviewModal(true);
                            }}
                          >
                            {dashboardData?.counts?.propertyDeatails?.images?.[0]?.objectUrl ? (
                              <Image
                                className='h-full w-full object-cover rounded-lg transition-transform duration-1000 ease-in-out group-hover:scale-110'
                                src={`${
                                  dashboardData.counts.propertyDeatails.images[0].objectUrl.startsWith("https")
                                    ? ""
                                    : "https://"
                                }${dashboardData.counts.propertyDeatails.images[0].objectUrl}`}
                                width={173.65}
                                height={173.65}
                                alt="Property main image"
                                onError={(e) => {
                                  e.target.onerror = null;
                                  e.target.style.display = 'none';
                                }}
                              />
                            ) : null}
                            <div className='absolute inset-0 flex justify-center items-center pointer-events-none animate-pulse'>
                              {!dashboardData?.counts?.propertyDeatails?.images?.[0]?.objectUrl && (
                                <span className='text-gray-400 font-bold'>MixDorm</span>
                              )}
                            </div>
                          </div>
                      
                       
                          <div className='grid grid-cols-2 gap-2'>
                            {Array.from({ length: 4 }).map((_, index) => {
                              const photoIndex = index + 1;
                              const photo = dashboardData?.counts?.propertyDeatails?.images?.[photoIndex];
                              const hasImage = !!photo?.objectUrl;
                              const isLastInGrid = index === 3;
                              const remainingImages = Math.max(
                                0,
                                (dashboardData?.counts?.propertyDeatails?.images?.length || 0) - 5
                              );
                      
                              return (
                        <div
                          key={index}
                          className='relative rounded-lg h-[82.15px] w-full overflow-hidden bg-gray-100'
                           onClick={() => {
                           setPreviewIndex(photoIndex);
                           setActiveIndex(photoIndex);
                           setImagePreviewModal(true);
                         }}
                        >
                          {hasImage ? (
                            <Image
                              className='h-full w-full object-cover transition-transform duration-1000 ease-in-out group-hover:scale-110'
                              src={`${photo.objectUrl.startsWith("https") ? "" : "https://"}${photo.objectUrl}`}
                              width={98.66}
                              height={82.15}
                              alt={`Property image ${photoIndex}`}
                              onError={(e) => {
                                e.target.onerror = null;
                                e.target.style.display = 'none';
                              }}
                            />
                          ) : null}
                          
                         
                          <div className='absolute inset-0 flex justify-center items-center pointer-events-none animate-pulse'>
                            {!hasImage && (
                              <span className='text-gray-400 text-xs font-bold'>MixDorm</span>
                            )}
                          </div>
              
                          {isLastInGrid && remainingImages > 0 && (
                            <div className='absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center text-white text-sm font-medium rounded-lg'>
                              +{remainingImages}
                            </div>
                          )}
                        </div>
                            );
                           })}
                         </div>
                       </div>
                     ) : (
                       <div className='mb-4 text-red-500'>
                         No Property Images Added
                       </div>
                     )}
                     
        
   
              <div className='mb-4'>
                <div className='tabs flex gap-7 xl:gap-7 lg:gap-5'>
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      className={`tab-button text-sm lg:text-xs xl:text-sm font-bold pb-1 border-b-2 ${
                        activeTab === tab.id
                          ? "active border-black"
                          : "border-transparent "
                      }`}
                      onClick={() => setActiveTab(tab.id)}
                    >
                      {tab.label}
                    </button>
                  ))}
                </div>

                {/* Tab Content */}
                <div className='tab-content mt-3.5'>
                  {tabs.map((tab) =>
                    activeTab === tab.id ? (
                      <div className='text-xs font-normal' key={tab.id}>
                        {tab.content}
                      </div>
                    ) : null
                  )}
                </div>
              </div>

              {activeTab === "overview" && (
                <div className='flex gap-2 items-center mb-4'>
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/location.png`}
                    width={40}
                    height={40}
                  ></Image>
                  <div>
                    <p className='font-bold text-xs text-dark mb-1'>Location</p>
                    <p className='font-normal text-xs' color='#00000080'>
                      {fullAddress}
                    </p>
                  </div>
                </div>
              )}

              {activeTab === "overview" && (
                <div style={{ width: "100%", height: "146px" }}>
                  {/* <iframe
                className='rounded-lg'
                src='https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3151.8354345095996!2d144.96305781531692!3d-37.8162791797517!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6ad642af0f11fd81%3A0xf57772f61fdd8a5a!2sMelbourne%20VIC%2C%20Australia!5e0!3m2!1sen!2sus!4v1689799781505!5m2!1sen!2sus'
                width='100%'
                height='100%'
                style={{ border: 0 }}
                allowFullScreen=''
                loading='lazy'
                referrerPolicy='no-referrer-when-downgrade'
              ></iframe> */}
                  {isApiLoaded ? (
                    <GoogleMap
                      mapContainerStyle={mapContainerStyle}
                      center={{
                        lat: Number(
                          dashboardData?.counts?.propertyDeatails?.location
                            ?.coordinates?.[1]
                        ),
                        lng: Number(
                          dashboardData?.counts?.propertyDeatails?.location
                            ?.coordinates?.[0]
                        ),
                      }}
                      zoom={14}
                    >
                      <Marker
                        position={{
                          lat: Number(
                            dashboardData?.counts?.propertyDeatails?.location
                              ?.coordinates?.[1]
                          ),
                          lng: Number(
                            dashboardData?.counts?.propertyDeatails?.location
                              ?.coordinates?.[0]
                          ),
                        }}
                        onClick={() =>
                          setSelectedMarker({
                            lat: Number(
                              dashboardData?.counts?.propertyDeatails?.location
                                ?.coordinates?.[1]
                            ),
                            lng: Number(
                              dashboardData?.counts?.propertyDeatails?.location
                                ?.coordinates?.[0]
                            ),
                          })
                        }
                      />
                      {selectedMarker && (
                        <InfoWindow
                          position={selectedMarker}
                          onCloseClick={() => setSelectedMarker(null)}
                        >
                          <div>
                            <h2>
                              {dashboardData?.counts?.propertyDeatails?.name}
                            </h2>
                          </div>
                        </InfoWindow>
                      )}
                    </GoogleMap>
                  ) : (
                    <p>Loading Google Maps API...</p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Channel partner Modal */}
        {isModalOpen && (
          <Dialog
            open={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            className='relative z-50'
          >
            <DialogBackdrop
              transition
              className='fixed inset-0 bg-[#000000B2] transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in'
            />

            <div className='fixed inset-0 z-10 w-screen overflow-y-auto'>
              <div className='flex min-h-full justify-center p-4 text-center items-center sm:p-0'>
                <DialogPanel
                  transition
                  className='relative transform overflow-hidden rounded-2xl bg-white text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-full sm:max-w-md max-w-full w-full data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95'
                >
                  <button
                    onClick={() => setIsModalOpen(false)}
                    className='absolute top-4 right-4 text-gray-500 hover:text-gray-900'
                  >
                    <XMarkIcon className='h-6 w-6 text-black font-bold hover:text-slate-400	' />
                  </button>

                  <div className='bg-white sm:px-14 sm:pb-7 pt-5 p-3 pb-3'>
                    <div className='mt-4'>
                      <Image
                        className='mx-auto mb-7'
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/channel-selected.svg`}
                        width={198.73}
                        height={146}
                      ></Image>
                      <div className='mt-3 text-center sm:mt-0 sm:text-left'>
                        <DialogTitle
                          as='h3'
                          className='text-center font-semibold text-black sm:text-2xl text-lg'
                        >
                          Oops! No Channel Selected
                        </DialogTitle>
                        <div className='mt-3.5 text-center'>
                          <p
                            className='text-sm font-normal mb-5'
                            style={{ color: "#888888" }}
                          >
                            Select a channel to use{" "}
                            <span className='font-bold text-black'>
                              MixDorm
                            </span>{" "}
                            <span style={{ color: "#40E0D0" }}>AI</span> <br />{" "}
                            <span className='font-bold text-black'>
                              technology
                            </span>{" "}
                            to boost your bookings
                          </p>
                          <Link href='/owner/dashboard/channelpartner'>
                            <button className='h-10 rounded-full text-center bg-[#EEEEEE] w-full text-sm font-semibold'>
                              Select Channel Partner
                            </button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </DialogPanel>
              </div>
            </div>
          </Dialog>
        )}

        {/*------------------------- old code -----------------------------*/}

        {/* <h1 className=" text-xl font-medium mb-5">
        {propertyData?.name || ""}, {currentDate}
      </h1>

      <div className=" w-full bg-white shadow-md my-5 px-5 pt-5 pb-20 rounded-lg">
        <div className="flex align-items-center sm:mb-6 mb-4">
          <h3 className="text-xl mb-0">Milestones</h3>
          <Link
            href=""
            className="ml-auto py-3 px-5 bg-blue-600 text-white hover:bg-primary-blue rounded-md text-sm"
            prefetch={false}
          >
            Add Millstones
          </Link>
        </div>

        <ol className="flex items-center w-full justify-center max-w-[1100px] mx-auto ">
          <li className="relative cursor-pointer flex w-full items-center text-black  after:content-[''] after:w-full after:h-1 after:border-b after:border-[#D9D9D9] after:border-1 after:inline-block  text-center">
            <span className="flex items-center justify-center w-14 h-14 bg-[#D9D9D9] rounded-full  shrink-0">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/channel_manager.svg`}
                width={27}
                height={27}
                alt=""
                loading="lazy"
              />
            </span>
            <h6 className="absolute -bottom-11 -left-20 text-lg text-center ">
              Connect to Channel Manager
            </h6>
          </li>
          <li className="relative cursor-pointer flex w-full items-center text-black  after:content-[''] after:w-full after:h-1 after:border-b after:border-[#D9D9D9] after:border-1 after:inline-block  text-center">
            <h6 className="absolute -top-6 text-base">0%</h6>
            <span className="flex items-center justify-center w-9 h-9 bg-[#D9D9D9] rounded-full  shrink-0">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/channel_manager.svg`}
                width={18}
                height={18}
                alt=""
                loading="lazy"
              />
            </span>
          </li>
          <li className="relative cursor-pointer flex w-full items-center after:content-[''] after:w-full after:h-1 after:border-b after:border-gray-100 after:border-1 after:inline-block">
            <span className="flex items-center justify-center w-20 h-20 bg-[#D9D9D9] rounded-full shrink-0">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/property_profile.svg`}
                width={35}
                height={35}
                alt=""
                loading="lazy"
              />
            </span>
            <h6 className="absolute -bottom-11 -left-24 text-lg">
              Complete Your Property Profile
            </h6>
          </li>
          <li className="relative  cursor-pointer  flex w-full items-center text-black  after:content-[''] after:w-full after:h-1 after:border-b after:border-[#D9D9D9] after:border-1 after:inline-block  text-center">
            <h6 className="absolute -top-6 text-base">0%</h6>
            <span className="flex items-center justify-center w-9 h-9 bg-[#D9D9D9] rounded-full  shrink-0">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/property_profile.svg`}
                width={18}
                height={18}
                alt=""
                loading="lazy"
              />
            </span>
          </li>
          <li className="relative cursor-pointer flex items-center">
            <span className="flex items-center justify-center w-14 h-14 bg-[#D9D9D9] rounded-full  shrink-0">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/featured.svg`}
                width={27}
                height={27}
                alt=""
                loading="lazy"
              />
            </span>
            <h6 className="absolute -bottom-11 -right-20 text-lg  w-60 text-center">
              Get Featured and Socialize
            </h6>
          </li>
        </ol>
      </div>

      <div className=" w-full bg-white shadow-md my-5 p-5 rounded-lg">
        <h3 className="text-xl mb-5">Unlock Feature</h3>
        <div className="flex flex-wrap gap-5">
          {Menu.map((item, id) => (
            <Link
              href={item?.link}
              target={item?.link === "/faqs" ? "_blank" : undefined} 
              className="border border-[#D0D3D9] rounded-md pt-5 px-4 pb-4 text-center w-32"
              key={id}
              prefetch={false}
            >
              <span className=" h-12 text-center block">
                <Image
                  src={item.icon}
                  width={32}
                  height={32}
                  alt=""
                  className="block mx-auto max-h-8"
                  loading="lazy"
                />
              </span>
              <h6 className="text-base font-semibold text-black ">
                {item.name}
              </h6>
            </Link>
          ))}
        </div>
      </div> */}

        {/* 
      <Listing /> */}

        {/* <div className=" grid grid-cols-4 gap-x-5">
        {stats.map((item, index) => (
          <>
            <Statscard
              key={index}
              heading={item.heading}
              num={0}
              icon={item.icon}
            />
          </>
        ))}
      </div> */}

        {/* <div className=" w-full bg-white shadow-md my-5 p-5 rounded-lg">
        <h3 className="text-xl ">Rooms Detail</h3>
        <div className=" grid grid-cols-5 gap-x-5 mt-5">
          {roomdetails.map((item, index) => (
            <div
              key={index}
              className=" flex p-2 border border-[#D0D3D9] rounded-md"
            >
              <span className="w-[65%] p-1">
                <h3 className="">{item.heading}</h3>
                <h2 className=" text-blue-500 text-[26px]">
                  {item.num}
                  <span className=" text-gray-500 text-[16px] font-medium">
                    /day
                  </span>
                </h2>
              </span>
              <span className="w-[35%] flex justify-end pt-5 pr-3">
                <Image src={item.icon} className="h-[40px] w-[40px]" />
              </span>
            </div>
          ))}
        </div>
      </div> */}

        {/* <div className=" flex gap-x-5">
        <div className={` w-[50%] ${css1}`}>
          <Chartbar />
        </div>
        <div className={`w-[30%] ${css1}`}>
          <Calender />
        </div>

        <div className={` w-[20%] ${css1} `}>
          <Review />
        </div>
      </div> */}
       </div> 
       )}
         {imagePreviewModal && (
        <div className="fixed inset-0 bg-black bg-opacity-90 z-[999] flex flex-col items-center justify-center px-4">
          {/* Close Button */}
          <button
            onClick={() => setImagePreviewModal(false)}
            className="absolute top-4 right-4 text-white text-3xl font-bold z-50"
          >
            &times;
          </button>

          {/* Main Swiper */}
          <div className="relative w-full max-w-4xl h-[70vh] mb-4">
            <Swiper
              slidesPerView={1}
              centeredSlides={true}
              onSlideChange={(swiper) => {
                setActiveIndex(swiper.realIndex);
                if (mainThumbRef.current?.swiper) {
                  mainThumbRef.current.swiper.slideTo(swiper.realIndex);
                }
              }}
              className="h-full"
              ref={mainSwiperRef}
              spaceBetween={10}
              initialSlide={previewIndex}
            >
              {images.map((img, idx) => (
                <SwiperSlide key={idx}>
                  <div className="w-full h-full relative">
                    <Image
                      src={`${img.objectUrl.startsWith("https") ? "" : "https://"}${img.objectUrl}`}
                      alt={`Preview Image ${idx + 1}`}
                      layout="fill"
                      className="rounded-xl object-contain"
                    />
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>

            {/* Arrows */}
            <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 z-10 flex space-x-16">
              <div
                className="flex items-center justify-center w-8 h-8 shadow-md cursor-pointer"
                onClick={() => mainSwiperRef.current.swiper.slidePrev()}
              >
                <CircleChevronLeft size={24} color="#fff" />
              </div>
              <div
                className="flex items-center justify-center w-8 h-8 shadow-md cursor-pointer"
                onClick={() => mainSwiperRef.current.swiper.slideNext()}
              >
                <CircleChevronRight size={24} color="#fff" />
              </div>
            </div>
          </div>

          {/* Thumbnail Swiper */}
          <div className="w-full max-w-5xl mt-4 relative">
            <Swiper
              slidesPerView={5}
              spaceBetween={10}
              centeredSlides={true}
              className="h-full"
              ref={mainThumbRef}
              initialSlide={previewIndex}
            >
              {images.map((img, idx) => (
                <SwiperSlide key={idx}>
                  <div
                    className={`relative w-full h-32 rounded-md cursor-pointer border ${
                      idx === activeIndex ? "border-white" : "border-transparent"
                    }`}
                    onClick={() => mainSwiperRef.current.swiper.slideTo(idx)}
                  >
                    <Image
                      src={`${img.objectUrl.startsWith("https") ? "" : "https://"}${img.objectUrl}`}
                      alt={`Thumb ${idx + 1}`}
                      layout="fill"
                      className="rounded-md object-cover"
                    />
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        </div>
      )}
    </>
  );
};

export default Home;
