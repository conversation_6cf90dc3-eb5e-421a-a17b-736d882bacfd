import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/router";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Navigation, Autoplay } from "swiper/modules";
import { FaCarSide, FaHeart, FaStar, FaWifi } from "react-icons/fa";
import { PiMapPinLineFill } from "react-icons/pi";
import { RxMix } from "react-icons/rx";

// Import Swiper styles
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/navigation";
import { MdDelete, MdOutlinePool } from "react-icons/md";
import { FaKitchenSet } from "react-icons/fa6";
import { TbAirConditioning } from "react-icons/tb";

const WishlistPage = () => {
  const router = useRouter();
  const [wishlistItems, setWishlistItems] = useState([]);
  const [imagesLoaded, setImagesLoaded] = useState({});
  const [loading, setLoading] = useState(true);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const popupRef = useRef(null);


useEffect(() => {
  const handleClickOutside = (event) => {
    if (popupRef.current && !popupRef.current.contains(event.target)) {
      handleCancel();
    }
  };

  // Add when the popup is shown
  if (showConfirmation) {
    document.addEventListener('mousedown', handleClickOutside);
  } else {
    document.removeEventListener('mousedown', handleClickOutside);
  }

  // Clean up
  return () => {
    document.removeEventListener('mousedown', handleClickOutside);
  };
}, [showConfirmation]);

  useEffect(() => {
    const fetchWishlist = async () => {
      try {
        // Mock data
        const mockData = [
          {
            _id: "1",
            name: "Luxury Apartment in Downtown",
            distanceFromCityCenter: "1.2",
            starRating: "4.8",
            overallRating: { numberOfRatings: 124 },
            lowestAveragePricePerNight: { value: 120, currency: "USD" },
            images: [
              { objectUrl: "/property1.jpg" },
              { objectUrl: "/property2.jpg" },
            ],
            tag: "apartment",
            liked: true,
            facilities: [
              { name: "wifi" },
              { name: "parking" },
              { name: "pool" },
            ],
          },
          {
            _id: "2",
            name: "Cozy Studio Near Beach",
            distanceFromCityCenter: "0.5",
            starRating: "4.5",
            overallRating: { numberOfRatings: 89 },
            lowestPricePerNight: { value: 95, currency: "USD" },
            images: [
              { objectUrl: "/property3.jpg" },
              { objectUrl: "/property4.jpg" },
            ],
            tag: "studio",
            liked: true,
            facilities: [{ name: "wifi" }, { name: "kitchen" }, { name: "ac" }],
          },
          {
            _id: "3",
            name: "Luxury Apartment in Downtown",
            distanceFromCityCenter: "1.2",
            starRating: "4.8",
            overallRating: { numberOfRatings: 124 },
            lowestAveragePricePerNight: { value: 120, currency: "USD" },
            images: [
              { objectUrl: "/property1.jpg" },
              { objectUrl: "/property2.jpg" },
            ],
            tag: "apartment",
            liked: true,
            facilities: [
              { name: "wifi" },
              { name: "parking" },
              { name: "pool" },
            ],
          },
        ];

        setWishlistItems(mockData);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching wishlist:", error);
        setLoading(false);
      }
    };

    fetchWishlist();
  }, []);

  const handleDeleteClick = () => {
    setShowConfirmation(true);
  };

  const handleConfirm = () => {
    setShowConfirmation(false);
  };

  const handleCancel = () => {
    setShowConfirmation(false);
  };

  const handleImageLoad = (itemId, imgIndex) => {
    setImagesLoaded((prev) => ({
      ...prev,
      [`${itemId}-${imgIndex}`]: true,
    }));
  };

  const HandleLike = (itemId, isLiked) => {
    setWishlistItems((prevItems) =>
      prevItems.map((item) =>
        item._id === itemId ? { ...item, liked: !isLiked } : item
      )
    );
  };

  const handleShowMap = (item) => {
    // Implement map showing functionality
    console.log("Show map for:", item.name);
  };

  const getCurrencySymbol = (currency) => {
    const symbols = {
      USD: "$",
      EUR: "€",
      GBP: "£",
      INR: "₹",
    };
    return symbols[currency] || currency;
  };

  const tagDisplayNames = {
    apartment: "Apartment",
    studio: "Studio",
    hostel: "Hostel",
    hotel: "Hotel",
  };

  const formatTag = (tag) => {
    return tag ? tag.charAt(0).toUpperCase() + tag.slice(1) : "";
  };

  const freeFacilities = (item) => {
    return item.facilities || [];
  };

  const Faciicons = {
    wifi: () => (
      <span>
        <FaWifi className="text-lg" />
      </span>
    ),
    parking: () => (
      <span>
        <FaCarSide className="text-lg" />
      </span>
    ),
    pool: () => (
      <span>
        <MdOutlinePool className="text-lg" />
      </span>
    ),
    kitchen: () => (
      <span>
        <FaKitchenSet className="text-lg" />
      </span>
    ),
    ac: () => (
      <span>
        <TbAirConditioning className="text-lg" />
      </span>
    ),
  };

  const normalizeFaciName = (name) => {
    return name.toLowerCase().replace(/\s+/g, "");
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-blue"></div>
      </div>
    );
  }

  return (
    <div className="pb-8">
      <h1 className="text-4xl font-bold  mb-4 font-mashiny text-primary-blue">
        Your Wishlist
      </h1>

      {wishlistItems.length === 0 ? (
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold mb-4">Your wishlist is empty!!</h2>
          <p className="text-gray-600 mb-6">
            Start saving your favorite properties to see them here
          </p>
          <button
            onClick={() => router.push("/")}
            className="bg-primary-blue text-black px-6 py-2 rounded-lg hover:bg-teal-400 transition-colors"
          >
            Browse Properties
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 py-4 px-2 md:px-0">
          {wishlistItems.map((item, index) => (
            <div key={index} className="my-4 w-full">
              <div className="border border-[#EEEEEE] rounded-2xl flex flex-col bg-white shadow-sm transition-all duration-300 hover:shadow-md">
                {/* Property Image */}
                <div className="flex relative w-full">
                  {item?.images?.[0]?.objectUrl ? (
                    <Swiper
                      pagination={{
                        dynamicBullets: true,
                        clickable: true,
                      }}
                      navigation={true}
                      modules={[Pagination, Navigation, Autoplay]}
                      slidesPerView={1}
                      loop
                      speed={1000}
                      spaceBetween={0}
                      className="mySwiper myCustomSwiper m-0 w-full"
                    >
                      {item?.images?.map((itemImg, imgIndex) => {
                        const isLoaded =
                          imagesLoaded[`${item._id}-${imgIndex}`];

                        return (
                          <SwiperSlide
                            key={imgIndex}
                            className="w-full bg-transparent"
                          >
                            <div className="relative">
                              {!isLoaded && (
                                <div className="w-full h-[180px] sm:h-[215px] bg-gray-200 animate-pulse rounded-t-2xl absolute flex items-center justify-center">
                                  <h1 className="text-white font-bold font-manrope text-4xl">
                                    Mixdorm
                                  </h1>
                                </div>
                              )}

                              <Image
                                src={itemImg?.objectUrl}
                                className={`rounded-t-2xl object-cover w-full h-[180px] sm:h-[215px] ${
                                  !isLoaded ? "opacity-0" : "opacity-100"
                                }`}
                                alt="Property"
                                width={320}
                                height={300}
                                loading={imgIndex === 0 ? "eager" : "lazy"}
                                priority={imgIndex === 0}
                                onLoadingComplete={() =>
                                  handleImageLoad(item._id, imgIndex)
                                }
                              />

                              <div className="absolute flex items-center justify-center px-3 py-1 text-xs font-semibold text-white bg-black rounded-4xl top-5 left-4 font-manrope">
                                {tagDisplayNames[item?.tag]?.toUpperCase() ||
                                  formatTag(item?.tag) ||
                                  item?.type}
                              </div>
                              <button
                                type="button"
                                onClick={() =>
                                  HandleLike(item?._id, item?.liked)
                                }
                                className={`absolute flex items-center justify-center p-1 rounded-full w-7 h-7 top-5 right-5 font-manrope ${
                                  item?.liked
                                    ? "text-red-600 bg-white"
                                    : "text-black bg-white"
                                } hover:bg-white-600 hover:text-red-600`}
                              >
                                <FaHeart size={16} />
                              </button>
                            </div>
                          </SwiperSlide>
                        );
                      })}
                    </Swiper>
                  ) : (
                    <div className="w-full h-[180px] sm:h-[215px] bg-gray-200 animate-pulse rounded-t-2xl">
                      <div className="h-full w-full bg-gray-300 animate-pulse rounded-t-2xl flex items-center justify-center">
                        <h1 className="text-white font-bold font-manrope text-4xl">
                          Mixdorm
                        </h1>
                      </div>
                    </div>
                  )}
                </div>

                {/* Property Details */}
                <div className="w-full p-4 flex flex-col relative">
                  <div className="min-h-[60px]">
                    <h3
                      onClick={() => {
                        router.push(`/hostels-detail/${item?._id}`);
                      }}
                      className="cursor-pointer text-base md:text-lg font-bold font-manrope mb-1 hover:text-primary-blue"
                    >
                      {item.name}
                    </h3>
                    <p
                      className="text-[#888888] text-xs sm:text-sm flex items-center font-manrope cursor-pointer hover:text-primary-blue"
                      onClick={() => handleShowMap(item)}
                    >
                      <PiMapPinLineFill className="text-primary-blue mr-1.5" />
                      {`${item?.distanceFromCityCenter}`} KM away from city
                      center
                    </p>
                  </div>

                  <div className="absolute -top-4 z-10">
                    <span className="text-xs font-bold flex items-center bg-gray-100 px-3 py-1.5 rounded-3xl shadow-lg border">
                      <FaStar className="text-yellow-400 mr-1" />
                      {item?.starRating || "5"}{" "}
                      <span className="text-[#888888] font-normal ml-1">
                        ({item?.overallRating?.numberOfRatings || 0} Reviews)
                      </span>
                    </span>
                  </div>

                  <div className="flex flex-wrap gap-2 font-medium text-xs mt-2 items-center">
                    <span className="text-sm font-manrope font-medium text-gray">
                      Key Features:
                    </span>
                    {freeFacilities(item).map((faci, id) => {
                      const key = normalizeFaciName(faci?.name);
                      const Icon = Faciicons[key] || RxMix;

                      return (
                        <div
                          key={id}
                          className="relative group w-5 h-5 flex items-center justify-center"
                        >
                          <Icon className="text-lg text-black" />
                          <span className="absolute bottom-6 left-[-100%] transform text-xs text-white bg-black px-2 py-1 rounded opacity-0 group-hover:opacity-100 duration-200 whitespace-nowrap">
                            {faci.name}
                          </span>
                        </div>
                      );
                    })}
                  </div>

                  <div className="flex justify-between items-center mt-4 gap-3">
                    {(item?.lowestAveragePricePerNight?.value ||
                      item?.lowestPricePerNight?.value) && (
                      <div className="text-black font-bold text-sm sm:text-base">
                        <span
                          onClick={() => {
                            router.push(`/hostels-detail/${item?._id}`);
                          }}
                          className="hover:text-primary-blue cursor-pointer"
                        >
                          {" "}
                          {getCurrencySymbol(
                            item?.lowestAveragePricePerNight?.currency ||
                              item?.lowestPricePerNight?.currency
                          )}{" "}
                          {item?.lowestAveragePricePerNight?.value ||
                            item?.lowestPricePerNight?.value}{" "}
                        </span>
                        <span className="text-xs text-[#737373] font-normal">
                          / Night
                        </span>
                      </div>
                    )}
                    <button
                      className="w-8 h-8 bg-red-600 text-white rounded-full flex items-center justify-center
                   "
                      onClick={handleDeleteClick}
                    >
                      <MdDelete className="text-lg" />
                    </button>
                    {showConfirmation && (
                      <>
                        <div className="fixed inset-0 bg-black bg-opacity-50 z-40"></div>

                        {/* Confirmation popup */}
                        <div
                          
                          className="fixed inset-0 flex items-center justify-center z-50"
                        >
                          <div className="bg-white p-6 rounded-lg shadow-lg max-w-sm w-full mx-4" ref={popupRef}>
                            <h3 className="text-lg font-medium mb-4">
                              Are you sure you want to remove this?
                            </h3>
                            <div className="flex justify-end space-x-3">
                              <button
                                onClick={handleCancel}
                                className="px-2 py-1 border border-gray-300 rounded-lg text-gray-700 text-sm"
                              >
                                Cancel
                              </button>
                              <button
                                onClick={handleConfirm}
                                className="px-2 py-1 bg-red-600 text-white rounded-lg hover:bg-red-700 text-sm"
                              >
                                Delete
                              </button>
                            </div>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default WishlistPage;
