import Response from "../utills/response.js";
import {
    addBooking, updateBookingById,
    getBookingById, listAllBookingsByUser, deleteBookingById, listBookings, checkRoomBookingInRange, checkBookingByDateRange, getUserTravelingDetails
} from "../services/booking.js";
import roomModel from "../models/room.js";
import { getConversionRates } from "../services/otaProperties.js"
import ARI from "../models/ARI.js";
import RatePlan from "../models/roomRatePlans.js";
import mongoose from "mongoose";
const addBookingController = async (req, res) => {
    try {
        const bookingData = { ...req.body };

        const newBooking = await addBooking(bookingData);
        return Response.Created(res, newBooking, 'Booking Added Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const updateBookingController = async (req, res) => {
    try {
        const { id } = req.params;
        const updatedBooking = await updateBookingById(id, req.body, req.user._id);
        return Response.OK(res, null, 'Booking Updated Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const getBookingByIdController = async (req, res) => {
    try {
        const { id } = req.params;
        const booking = await getBookingById(id);
        return Response.OK(res, booking, 'Booking Retrieved Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const listAllBookingsByUserController = async (req, res) => {
    try {
        let { page, limit, id, ...filter } = req.query;

        // Optional: Add filter for booking id
        if (id) {
            filter._id = new mongoose.Types.ObjectId(id); // Apply filter to subBookingId field
        }

        page = parseInt(page);
        limit = parseInt(limit);

        if (isNaN(page) || page <= 0) page = 1;
        if (isNaN(limit) || limit <= 0) limit = 10;

        const { subBookings, total } = await listAllBookingsByUser(req.user._id, filter, page, limit);

        const totalPages = Math.ceil(total / limit);
        const pagination = {
            page,
            limit,
            totalPages,
            totalBookings: total
        };

        return Response.OK(res, { bookings: subBookings, pagination }, 'Bookings Retrieved Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};


const deleteBookingController = async (req, res) => {
    try {
        const { id } = req.params;
        const result = await deleteBookingById(id);
        return Response.OK(res, 'Booking Deleted Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const listAllBookingsController = async (req, res) => {
    try {
        let { page, limit, category, property, startDate, endDate, status } = req.query;

        // Convert page and limit to integers
        page = parseInt(page);
        limit = parseInt(limit);

        // If page or limit are not valid numbers, default them
        if (isNaN(page) || page <= 0) {
            page = 1;
        }
        if (isNaN(limit) || limit <= 0) {
            limit = 10;
        }

        const { bookings, pagination, counts } = await listBookings(page, limit, category, property, startDate, endDate, status);

        return Response.OK(res, { bookings, pagination, counts }, 'Bookings Retrieved Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
}

const checkAvailabilityController = async (req, res) => {
    try {
        if (!req.body.propertyId || !req.body.roomId || !req.body.checkIn || !req.body.checkOut) {
            return Response.NotFound(res, null, 'Property , room, checkIn , checkOut are mandatory!');
        }
        const result = await checkRoomBookingInRange(req.body);
        return Response.OK(res, result, "Booking Retrive");
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
}

const checkBookingByDateRangeController = async (req, res) => {
    try {
        const { rooms } = req.body; // Expecting an array of room objects

        if (!rooms || !Array.isArray(rooms) || rooms.length === 0) {
            return res.status(400).json({ message: 'Rooms array is required.' });
        }

        // Validate and process each room's data
        const results = await Promise.all(
            rooms.map(async (room) => {
                const { roomId, startDate, endDate } = room;

                if (!roomId || !startDate || !endDate) {
                    return { roomId, status: 'error', message: 'roomId, startDate, and endDate are required.' };
                }

                const start = new Date(startDate);
                const end = new Date(endDate);

                if (start > end) {
                    return { roomId, status: 'error', message: 'startDate must be before endDate.' };
                }
                try {
                    const bookingStatus = await checkBookingByDateRange(roomId, start, end);
                    return { roomId, status: 'success', bookingStatus };
                } catch (error) {
                    console.error(`Error checking booking for roomId ${roomId}:`, error);
                    return { roomId, status: 'error', message: 'Failed to check booking status.' };
                }
            })
        );

        // Return the results for all rooms
        return Response.OK(res, results);
    } catch (error) {
        console.error(error);
        return res.status(500).json({ message: 'Internal Server Error' });
    }
};



const getUserTravelingDetailsController = async (req, res) => {
    try {

        const userId = req.user._id;
        const travelingDetails = await getUserTravelingDetails(userId);
        return Response.OK(res, travelingDetails);
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};
export const checkout = async (req, res) => {
    try {
        let checkIn = new Date(req.body.checkIn);
        let checkOut = new Date(req.body.checkOut);
        const roomSelections = req.body.roomSelections;
        const currency = req.body.currency;
        const noOfGuests = req.body.noOfGuests;
        checkIn.setHours(0, 0, 0, 0);
        checkOut.setHours(0, 0, 0, 0);

        if (!currency) {
            return Response.BadRequest(res, null, 'Currency is required');
        }

        if (isNaN(checkIn) || isNaN(checkOut)) {
            return Response.BadRequest(res, null, 'Invalid check-in or check-out date');
        }

        const days = (checkOut - checkIn) / (1000 * 60 * 60 * 24);

        const firstRoomData = await roomModel.findOne({ _id: roomSelections[0].roomId }).populate('property', 'images name address');
        if (!firstRoomData) {
            return Response.NotFound(res, null, 'Property not found');
        }

        const propertyData = firstRoomData.property;
        const baseCurrency = "INR";
        const conversionRate = await getConversionRates(currency);

        if (!conversionRate) {
            return Response.InternalServerError(res, null, 'Failed to fetch currency conversion rate');
        }

        const roomsDetails = [];
        let totalPayableAmount = 0;
        let lowerBedSurchargeTotal = 0;
        let allRoomTotalPrice = 0;

        for (const roomSelection of roomSelections) {
            const { roomId, beds, bedType, RateTypeId } = roomSelection;

            const roomData = await roomModel.findOne({ _id: roomId }).populate('property', 'images');
            if (!roomData) {
                return Response.NotFound(res, null, `Room with ID ${roomId} not found`);
            }

            if (typeof beds !== 'number' || beds < 1) {
                return Response.BadRequest(res, null, 'Number of beds should be a positive integer');
            }

            const rateData = await ARI.findOne({
                roomId: roomData.roomId || roomData._id,
                ratePlan: RateTypeId,
                date: {
                    $gte: new Date(checkIn.setHours(0, 0, 0, 0)),
                    $lte: new Date(checkIn.setHours(23, 59, 59, 999))
                }
            });

            if (!rateData || !rateData.rate) {
                return Response.NotFound(res, null, `Rate not found for room ${roomId} on ${req.body.checkIn}`);
            }

            const roomRate = rateData.rate;
            const perDayRoomPrice = roomRate;
            const roomTotalPrice = perDayRoomPrice * days * beds;

            let lowerBedSurcharge = 0;
            if (bedType === 'lower') {
                lowerBedSurcharge = roomTotalPrice * 0.15;
                lowerBedSurchargeTotal += lowerBedSurcharge;
            }
            const ratePlanData = await RatePlan.findOne({ otaRateId: RateTypeId }); // Adjust query if you use _id or another key
            const ratePlanName = ratePlanData ? ratePlanData.name : 'Standard Plan';

            roomsDetails.push({
                id: roomData._id,
                roomName: roomData.name,
                bedType: bedType || 'standard',
                perDayRoomPrice: parseFloat(perDayRoomPrice.toFixed(2)),
                roomTotalPrice: parseFloat(roomTotalPrice.toFixed(2)),
                lowerBedSurcharge: parseFloat(lowerBedSurcharge.toFixed(2)),
                totalBeds: beds,
                ratePlanName,
                RateTypeId
            });

            totalPayableAmount += roomTotalPrice;
            allRoomTotalPrice += roomTotalPrice;
        }

        const totalAmountWithSurcharge = parseFloat((allRoomTotalPrice + lowerBedSurchargeTotal).toFixed(2));
        const baseAdvanceAmount = allRoomTotalPrice * 0.15;
        const payableNow = parseFloat((baseAdvanceAmount * 1.18 * 1.03) + lowerBedSurchargeTotal).toFixed(2);
        const payableOnArrival = parseFloat((totalAmountWithSurcharge - baseAdvanceAmount).toFixed(2));

        // Taxes & Fees
        const taxAmount = baseAdvanceAmount * 0.18;   // 18% GST
        const serviceFee = baseAdvanceAmount * 0.03;  // 3% convenience fee
        const taxesAndFees = taxAmount + serviceFee;

        const responseData = {
            propertyData,
            checkIn: req.body.checkIn,
            checkOut: req.body.checkOut,
            noOfDays: days,
            noOfGuests: noOfGuests,
            roomsData: roomsDetails,
            payableOnArrival: payableOnArrival,
            payableNow: payableNow,
            totalAmount: (Number(payableOnArrival) + Number(payableNow)).toFixed(2),
            currency: currency,
            taxesAndFees
        };

        return Response.Created(res, responseData, 'Checkout details retrieved successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};
export const cancelBookingController = async (req, res) => {
    try {
        const { id } = req.body;
        const updatedBooking = await updateBookingById(id, { isCancel: true }, req.user._id);
        return Response.OK(res, null, 'Booking Cancelled Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

export { addBookingController, updateBookingController, getBookingByIdController, listAllBookingsByUserController, deleteBookingController, listAllBookingsController, checkAvailabilityController, checkBookingByDateRangeController, getUserTravelingDetailsController };