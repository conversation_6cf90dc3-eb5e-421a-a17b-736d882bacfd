 
"use client";
import { useEffect, useRef } from "react";
import React from "react";
import { RxCross2 } from "react-icons/rx";

const HostelPopup = ({ onClose }) => {
  const popupRef = useRef(null);
 
useEffect(() => {
    function handleClickOutside(event) {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        onClose(); // Close the popup
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [onClose]);


  return (
    <div className="fixed inset-0 px-4 md:px-0 lg:px-0  bg-black bg-opacity-50 flex justify-center items-center z-50 p-2">
      <div className="bg-white rounded-xl w-[633px] h-[276px] md:w-[400px] md:h-[276px] lg:w-[633px] lg:h-[276px] dark:bg-[#171616]" ref={popupRef}>
        <h2 className="text-2xl text-center font-bold my-6 dark:text-gray-100">User Action</h2>
                <button
                  className="sticky bottom-[90%] left-[90%] z-20 p-2 rounded-md "
                  onClick={onClose}
                  
                >
                <RxCross2 className="bg-transparent text-black dark:text-[#B6B6B6]" />
                </button>
        <hr />
        <div className="flex flex-col mx-6 my-4">
          <label className=" text-black/50 text-center text-sm font-semibold dark:text-[#B6B6B6]">Hostel</label>
        </div>
        <div className="flex justify-between gap-4 mx-8 pt-10">
          <button
            className="border w-[50%] px-6 py-2 rounded text-sm dark:text-[#B6B6B6]"
            
          >
            Delete
          </button>
          <button
            className="bg-sky-blue-650  w-[50%] text-white text-sm px-6 py-2 rounded"
            
          >
            Hide
          </button>
        </div>
      </div>
    </div>
  );
};

export default HostelPopup;
