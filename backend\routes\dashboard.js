// dashboardRoutes.js
import { checkAuth } from '../middleware/auth.js';

import express from "express";
import {getDashboardData,getCalendarData} from "../controller/hostel-owner/dashboard.js"

const router = express.Router();
router.get('/calendar', checkAuth(''),getCalendarData);
router.get('/:propertyId', getDashboardData);

export default router;
/**
 * @swagger
 * tags:
 *   name: Dashboard
 *   description: Hostel Owner dashboard APIs
 */
/**
 * @swagger
 * /dashboard/{propertyId}:
 *   get:
 *     summary: Get property dashboard data
 *     tags: [Dashboard]
 *     description: Returns total bookings, total revenue, total reviews, and new bookings filtered by date range.
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         required: true
 *         description: The ID of the property to get dashboard data for.
 *         schema:
 *           type: string
 *       - in: query
 *         name: startDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date to filter bookings (required).
 *       - in: query
 *         name: endDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: End date to filter bookings (required).
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 totalBookings:
 *                   type: integer
 *                   example: 50
 *                 totalRevenue:
 *                   type: number
 *                   example: 10000.50
 *                 totalReviews:
 *                   type: integer
 *                   example: 25
 *                 newBookings:
 *                   type: integer
 *                   example: 10
 *       400:
 *         description: Bad Request - Missing required parameters
 *       500:
 *         description: Internal Server Error
 */
/**
 * @swagger
 * /dashboard/calendar:
 *   get:
 *     summary: Get room rate data grouped by type and room type
 *     tags:
 *       - Room Rates
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: startDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: The start date for filtering room rates (YYYY-MM-DD)
 *       - in: query
 *         name: endDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: The end date for filtering room rates (YYYY-MM-DD)
 *     responses:
 *       200:
 *         description: Successfully retrieved room rate data grouped by type and room type
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         description: Room type category (e.g., dormitory, private)
 *                         example: "dormitory"
 *                       roomTypes:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             roomTypeId:
 *                               type: string
 *                               description: Room type ID
 *                               example: "rm9"
 *                             roomTypeName:
 *                               type: string
 *                               description: Room type name
 *                               example: "6 Bed Male Dormitory"
 *                             roomRates:
 *                               type: array
 *                               items:
 *                                 type: object
 *                                 properties:
 *                                   roomId:
 *                                     type: string
 *                                     description: Room ID
 *                                     example: "65a9876543210fedcba54321"
 *                                   roomName:
 *                                     type: string
 *                                     description: Room name
 *                                     example: "Deluxe King Room"
 *                                   date:
 *                                     type: string
 *                                     format: date
 *                                     description: The date for the rate
 *                                     example: "2025-04-01"
 *                                   rate:
 *                                     type: object
 *                                     properties:
 *                                       weekdayRate:
 *                                         type: object
 *                                         properties:
 *                                           value:
 *                                             type: number
 *                                             description: The weekday rate value
 *                                             example: 1200
 *                                           updatedAt:
 *                                             type: string
 *                                             format: date-time
 *                                             example: "2025-03-30T10:00:00Z"
 *                                       weekendRate:
 *                                         type: object
 *                                         properties:
 *                                           value:
 *                                             type: number
 *                                             description: The weekend rate value
 *                                             example: 1500
 *                                           updatedAt:
 *                                             type: string
 *                                             format: date-time
 *                                             example: "2025-03-30T10:00:00Z"
 *                                       averagePrice:
 *                                         type: object
 *                                         properties:
 *                                           value:
 *                                             type: number
 *                                             description: The average price
 *                                             example: 1300
 *                                           updatedAt:
 *                                             type: string
 *                                             format: date-time
 *                                             example: "2025-03-30T10:00:00Z"
 *                                       basePrice:
 *                                         type: number
 *                                         description: The base price of the room
 *                                         example: 1000
 *                                   currency:
 *                                     type: string
 *                                     description: The currency of the rate
 *                                     example: "INR"
 *       400:
 *         description: Missing or invalid startDate and endDate
 *       401:
 *         description: Unauthorized access
 *       500:
 *         description: Server error
 */
