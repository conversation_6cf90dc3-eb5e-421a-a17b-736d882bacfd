import cron from 'node-cron';
import userModel from "../models/auth.js";  // Import your user model
import moment from 'moment';  // For date handling

const activateAccount = async () => {
    try {
        // Get all users who have a deactivation period set
        const usersToReactivate = await userModel.find({
            isActive: false,
            deactivate_period: { $gt: 0 },
            deactivatedAt: { $exists: true }, // Ensure deactivated date is present
        });

        if (usersToReactivate.length === 0) {
            console.log('No users to reactivate');
            return;
        }

        // Loop through all users who need to be reactivated
        for (let user of usersToReactivate) {
            // Calculate the reactivation date
            const reactivationDate = moment(user.deactivatedAt).add(user.deactivate_period, 'days');

            // Check if the reactivation date has passed
            if (moment().isSameOrAfter(reactivationDate)) {
                await userModel.updateOne(
                    { _id: user._id },
                    {
                        $set: { isActive: true, deactivate_period: 0 },
                        $unset: { deactivatedAt: "" }, // Remove the deactivatedAt field
                    }
                );
                console.log(`User ${user._id} has been reactivated.`);
            }
        }
    } catch (error) {
        console.error('Error during account activation process:', error.message);
    }
};

// Schedule the cron job to run every day at midnight (00:00)
cron.schedule('0 0 * * *', () => {
    console.log('Running cron job to reactivate accounts...');
    activateAccount();
});

export default activateAccount;
