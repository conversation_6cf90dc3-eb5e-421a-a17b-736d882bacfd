 
import React, { useState } from "react";
import Image from "next/image";
import {
  getItemLocalStorage,
  setItemLocalStorage,
} from "@/utils/browserSetting";
import { useRouter } from "next/router";
import { AddPropertyApi } from "@/services/ownerflowServices";
import toast from "react-hot-toast";
import { useNavbar } from "../home/<USER>";

const Onboarding = ({  data }) => {
  // eslint-disable-next-line no-unused-vars
  const [isLoading, setIsLoading] = useState(false);
  const { updateHopId } = useNavbar();

  const name = getItemLocalStorage("name");



  const router = useRouter();

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      const payload = {
        type: data?.step1?.type,
        name: data?.step1?.name,
        address: {
          lineOne: data?.step1?.address.lineOne,
          city: data?.step1?.city,
          state: data?.step1?.stateName,
          country: data?.step1?.countryName,
          zipcode: data?.step1?.postCode,
        },
        latitude: data?.step1?.latitude,
        longitude: data?.step1?.longitude,
        photos: Array.isArray(data?.step2?.propertyPhoto)
          ? data?.step2?.propertyPhoto.map((photoUrl) => ({
              title: "propertyPhoto",
              url: photoUrl,
              action: "add",
            }))
          : [],
        licenceProof: Array.isArray(data?.step2?.propertyLicense)
          ? data?.step2?.propertyLicense.map((licenseUrl) => ({
              title: "propertyLicense",
              url: licenseUrl,
            }))
          : [],
        addressProof: Array.isArray(data?.step2?.propertyAddress)
          ? data?.step2?.propertyAddress.map((addressUrl) => ({
              title: "addressProof",
              url: addressUrl,
            }))
          : [],
      };

      const response = await AddPropertyApi(payload);
      if (response?.data?.status) {
        toast.success(
          response?.data?.message || "Property added successfully!"
        );
        router?.push("/owner/dashboard");
        setItemLocalStorage("hopid", response.data.data._id);
        updateHopId(response.data.data._id);
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Failed to submit property details.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {/* {!selectOwner ? ( */}
      <div className='min-h-screen flex flex-col items-center bg-[#F7F7F7] sm:pb-[10rem] pb-10'>
        <div className='bg-white rounded-3xl shadow-md w-full max-w-3xl p-6 md:p-14 mb-10'>
          <h2 className='text-2xl font-semibold py-4'>
            👋Hi, <span className='text-primary-blue'>{name}</span> Verification pending
          </h2>
          <p className="font-manrope font-normal text-black mb-10 sm:text-base text-sm">It may takes upto 24 hours to verify </p>
          <div className='flex justify-center mb-8'>
            <Image
              className=""
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/verify.svg`}
              alt='Verification Illustration'
              width={349}
              height={222}
              loading='lazy'
            />
          </div>
          <h3 className='text-2xl font-bold text-center mb-14 py-6'>
          Please verify your detail
          </h3>
          <div className='flex flex-col items-center space-y-4'>
            <button
              onClick={handleSubmit}
              className='w-full max-w-xl bg-[#40E0D0] text-black font-semibold py-4 rounded-4xl hover:bg-teal-400 transition duration-200'
            >
              Onboard
            </button>
            {/* <button className={`w-full max-w-xl bg-[#EEEEEE] text-black font-semibold py-4 rounded-4xl hover:bg-teal-400 transition duration-200`}>
              Verify
            </button> */}
          </div>
        </div>
      </div>
      {/* ) : (
        <SelectOwner  propertyId={propertyId}/>
      )} */}
    </>
  );
};

export default Onboarding;
