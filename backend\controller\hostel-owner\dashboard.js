import Response from "../../utills/response.js";
import Booking from "../../models/bookings.js"
import Review from "../../models/reviews.js"
import Room from "../../models/room.js"
import mongoose from "mongoose";
import property from "../../models/properties.js";
import roomRatesModel from "../../models/rates.js";
import roomModel from "../../models/room.js";
import RoomInventory from '../../models/ARI.js'
import avability from "../../models/avability.js";
import ARI from "../../models/ARI.js";
const getDashboardData = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    const { propertyId } = req.params;

    // // Check for mandatory date fields
    // if (!startDate || !endDate) {
    //   return Response.BadRequest(res, null, 'Both startDate and endDate are required.');
    // }

    // Main filter for property and date range
    const filter = {
      property: new mongoose.Types.ObjectId(propertyId)
      // checkInDate: { $gte: new Date(startDate), $lte: new Date(endDate) }
    };
    console.log("Filter:", filter);
    const propertyDeatails = await property.findOne({ _id: new mongoose.Types.ObjectId(propertyId) })
    // Fetch total bookings
    const totalBookings = await Booking.find({ property: new mongoose.Types.ObjectId(propertyId) });
    console.log("totalBookings", totalBookings)
    // Calculate total revenue
    const totalRevenue = await Booking.aggregate([
      { $match: filter },
      { $group: { _id: null, total: { $sum: "$totalAmount" } } }
    ]);

    // Fetch total reviews (assuming reviews are linked to property)
    const totalReviews = await Review.find({ property: propertyId });

    // Fetch new bookings within date range
    const newBookings = await Booking.find(filter);
    console.log("new", newBookings)
    // Room-related details
    const totalCheckIn = await Booking.countDocuments({ checkInDate: { $exists: true, $ne: null }, ...filter });
    const totalCheckOut = await Booking.countDocuments({ checkOutDate: { $exists: true, $ne: null }, ...filter });
    const totalOccupiedRoom = await Booking.countDocuments({ status: 'confirmed', isCancel: false, ...filter });
    const totalVacantRoom = await Room.countDocuments({ status: 'vacant', property: propertyId });
    const totalInHouseGuests = await Booking.countDocuments({
      checkInDate: { $lte: new Date() },
      checkOutDate: { $gte: new Date() },
      ...filter
    });

    const roomDetails = {
      totalCheckIn,
      totalCheckOut,
      totalOccupiedRoom,
      totalVacantRoom,
      totalInHouseGuests,
    };

    // Fetch chart data for this week
    const startOfWeek = new Date();
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay() + 1); // Set to Monday
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(endOfWeek.getDate() + 6); // Set to Sunday

    const chartData = await Booking.aggregate([
      {
        $match: {
          checkInDate: { $gte: startOfWeek, $lte: endOfWeek },
          property: new mongoose.Types.ObjectId(propertyId) // Ensure filtering by property
        }
      },
      {
        $group: {
          _id: { $dayOfWeek: "$checkInDate" },
          occupied: { $sum: { $cond: [{ $eq: ["$status", "confirmed"] }, 1, 0] } },
          vacant: { $sum: { $cond: [{ $eq: ["$status", "vacant"] }, 1, 0] } },
          reserved: { $sum: { $cond: [{ $eq: ["$status", "reserved"] }, 1, 0] } }
        }
      },
      {
        $sort: { _id: 1 } // Sort by day of the week
      }
    ]);

    // Prepare counts object for response
    const counts = {
      totalBookings,
      totalRevenue: totalRevenue[0] ? totalRevenue[0].total : 0,
      totalReviews,
      newBookings,
      roomDetails,
      chartData, // Include the chart data in the response,
      propertyDeatails
    };

    return Response.OK(res, { counts }, 'Dashboard data retrieved successfully');
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    return Response.InternalServerError(res, null, error.message);
  }
};
const getCalendarData = async (req, res) => {
  try {
    const { property, startDate, endDate } = req.query;

    if (!property || !startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: "property, startDate, and endDate are required"
      });
    }

    const parsedStartDate = new Date(startDate);
    const parsedEndDate = new Date(endDate);

    // 1️⃣ Get all active rooms of this property
    const rooms = await roomModel.find({
      property,
      isActive: true,
      isDeleted: false
    }).lean();

    const matchIds = rooms
      .map(r => String(r.roomId))
      .filter(id => !isNaN(Number(id))); // Only numeric roomIds

    // 2️⃣ Get rate data from ARI Inventory
    const ariData = await ARI.find({
      roomId: { $in: matchIds },
      date: {
        $gte: parsedStartDate,
        $lte: parsedEndDate
      }
    }).lean();

    // 3️⃣ Get available room data from Availability collection
    const availabilityData = await avability.find({
      roomId: { $in: matchIds },
      date: {
        $gte: parsedStartDate,
        $lte: parsedEndDate
      }
    }).lean();

    // 4️⃣ Create maps for fast lookup
    const rateMap = new Map();
    for (const entry of ariData) {
      const key = `${entry.roomId}_${entry.date.toISOString().split("T")[0]}`;
      rateMap.set(key, entry);
    }

    const availabilityMap = new Map();
    for (const entry of availabilityData) {
      const key = `${entry.roomId}_${entry.date.toISOString().split("T")[0]}`;
      availabilityMap.set(key, entry);
    }

    // 5️⃣ Group by dormitory/private
    const grouped = {
      dormitory: [],
      private: []
    };

    for (const room of rooms) {
      if (isNaN(Number(room.roomId))) continue;

      const calendar = [];
      const dateCursor = new Date(parsedStartDate);
      while (dateCursor <= parsedEndDate) {
        const dayStr = dateCursor.toISOString().split("T")[0];
        const key = `${room.roomId}_${dayStr}`;

        const rateEntry = rateMap.get(key);
        const availabilityEntry = availabilityMap.get(key);

        calendar.push({
          date: new Date(dayStr),
          weekdayRate: rateEntry?.rate || 0,
          weekendRate: rateEntry?.rate || 0,
          averagePrice: rateEntry?.rate || 0,
          basePrice: rateEntry?.rate || 0,
          availableRooms: availabilityEntry?.availableUnits || 0,
          bookings: [],
          currency: rateEntry?.currency || "INR"
        });

        dateCursor.setDate(dateCursor.getDate() + 1);
      }

      const roomType = {
        roomTypeId: room.otaRoomId,
        roomTypeName: room.name,
        roomRates: calendar
      };

      if (room.dormitory) {
        grouped.dormitory.push(roomType);
      } else {
        grouped.private.push(roomType);
      }
    }

    const responseData = [
      {
        _id: "dormitory",
        roomTypes: grouped.dormitory
      },
      {
        _id: "private",
        roomTypes: grouped.private
      }
    ];

    return res.status(200).json({
      success: true,
      data: responseData
    });

  } catch (error) {
    console.error("getCalendarData error", error);
    return res.status(500).json({
      success: false,
      message: error.message
    });
  }
};


// const getCalendarData = async (req, res) => {
//   try {
//     const { property, startDate, endDate } = req.query;

//     if (!property || !startDate || !endDate) {
//       return res.status(400).json({
//         success: false,
//         message: "property, startDate, and endDate are required"
//       });
//     }

//     const parsedStartDate = new Date(startDate);
//     const parsedEndDate = new Date(endDate);

//     // 1️⃣ get all active rooms of this property
//     const rooms = await roomModel.find({
//       property: property,
//       isActive: true,
//       isDeleted: false
//     }).lean();
//     // const otaRoomIds = rooms.map(r => r.roomId);
//     const matchIds = [
//   ...rooms.map(r => r.otaRoomId),                      // OTA id
//   ...rooms.map(r => String(r.roomId)),                  // numeric id stringified
//   ...rooms.map(r => r._id.toString())                   // Mongo _id string
// ].filter(Boolean);
//     console.log("otaRoomIds",matchIds)
//     console.log("parsedStartDate",parsedStartDate)
//     console.log("parsedEndDate,parsedEndDate",parsedEndDate )
//     // 2️⃣ get all ARI inventory for the date range
//     const ariInventory = await RoomInventory.find({
//       roomId: { $in: matchIds  },

//       // date: {
//       //   $gte: parsedStartDate,
//       //   $lte: parsedEndDate
//       // }
//     }).lean();
//     console.log("ariInventory", ariInventory)

//     // 3️⃣ build a nested Map (roomId -> date -> inventory)
//     const ariMap = new Map();
//     for (const inv of ariInventory) {
//       const dayStr = inv.date.toISOString().split("T")[0];
//       if (!ariMap.has(inv.roomId)) {
//         ariMap.set(inv.roomId, new Map());
//       }
//       ariMap.get(inv.roomId).set(dayStr, inv);
//     }

//     // 4️⃣ group rooms by dormitory vs private
//     const grouped = {
//       dormitory: [],
//       private: []
//     };

//     for (const room of rooms) {
//       const calendar = [];

//       const roomAriMap = ariMap.get(room.roomId);
//       if (roomAriMap) {
//         for (const [dayStr, ari] of roomAriMap.entries()) {
//           calendar.push({
//             date: ari.date,
//             weekdayRate: ari.rate,
//             weekendRate: ari.rate,
//             averagePrice: ari.rate,
//             basePrice: ari.rate,
//             availableRooms: ari.availableUnits,
//             bookings: []
//           });
//         }
//       }

//       const roomType = {
//         roomTypeId: room.roomId,
//         roomTypeName: room.name,
//         roomRates: calendar
//       };

//       if (room.dormitory) {
//         grouped.dormitory.push(roomType);
//       } else {
//         grouped.private.push(roomType);
//       }
//     }

//     // 5️⃣ convert to array in requested structure
//     const responseData = [
//       {
//         _id: "dormitory",
//         roomTypes: grouped.dormitory
//       },
//       {
//         _id: "private",
//         roomTypes: grouped.private
//       }
//     ];

//     return res.status(200).json({
//       success: true,
//       data: responseData
//     });
//   } catch (error) {
//     console.error("getCalendarData error", error);
//     return res.status(500).json({
//       success: false,
//       message: error.message
//     });
//   }
// };


export { getDashboardData, getCalendarData };
