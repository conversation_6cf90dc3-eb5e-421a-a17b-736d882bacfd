 
import React, { useEffect } from "react";
import Image from "next/image";

const Loader = ({ open }) => {
  useEffect(() => {
    // Move style injection to useEffect
    const stylesTag = document.createElement("style");
    stylesTag.innerHTML = `
      @keyframes bounce {
        0%, 100% {
           transform: translateY(0);
      box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
        }
        50%  {
         transform: translateY(-15px);
      box-shadow: 0 15px 15px rgba(0, 255, 255, 0.2);
        }
      }
    `;
    document.head.appendChild(stylesTag);

    // Cleanup function to remove styles when component unmounts
    return () => {
      stylesTag.remove();
    };
  }, []); // Empty dependency array means this runs once on mount

  if (!open) return null;

  return (
    <div style={styles.overlay}>
      <div style={styles.loaderContainer}>
        <div style={styles.imageWrapper}>
          <Image src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/loader.jpg`} className="object-contain" alt='Loading' style={styles.flippingImage} width={40} height={40} />
        </div>
      </div>
    </div>
  );
};

// **Inline Styles**
const styles = {
  overlay: {
    position: "fixed",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    zIndex: 1000,
  },
  loaderContainer: {
    position: "relative",
  },
  imageWrapper: {
    width: "70px",
    height: "70px",
    overflow: "hidden",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    borderRadius: "50%",
    animation: "bounce 0.5s infinite ease-in-out",
    boxShadow: "0 0 15px rgba(0, 255, 255, 0.3)",
    backgroundColor: "#000000",
    padding: "2px",
  },
  flippingImage: {
    width: "90%",
    height: "90%",
    borderRadius: "50%",
    filter: "brightness(1.2)",
  },
};

export default Loader;



//Rotate

// import React, { useEffect } from "react";

// const Loader = ({ open }) => {
//   useEffect(() => {
//     // Move style injection to useEffect
//     const stylesTag = document.createElement("style");
//     stylesTag.innerHTML = `
//       @keyframes rotate {
//         from {
//           transform: rotate(0deg);
//         }
//         to {
//           transform: rotate(360deg);
//         }
//       }
//     `;
//     document.head.appendChild(stylesTag);

//     // Cleanup function to remove styles when component unmounts
//     return () => {
//       stylesTag.remove();
//     };
//   }, []); // Empty dependency array means this runs once on mount

//   if (!open) return null;

//   return (
//     <div style={styles.overlay}>
//       <div style={styles.loaderContainer}>
//         <div style={styles.imageWrapper}>
//           <Image src="/loader.jpg" alt="Loading" style={styles.flippingImage} />
//         </div>
//       </div>
//     </div>
//   );
// };

// // **Inline Styles**
// const styles = {
//   overlay: {
//     position: "fixed",
//     top: 0,
//     left: 0,
//     width: "100%",
//     height: "100%",
//     backgroundColor: "rgba(0, 0, 0, 0.7)",
//     display: "flex",
//     alignItems: "center",
//     justifyContent: "center",
//     zIndex: 1000,
//   },
//   loaderContainer: {
//     position: "relative",
//   },
//   imageWrapper: {
//     width: "60px",
//     height: "60px",
//     overflow: "hidden",
//     display: "flex",
//     alignItems: "center",
//     justifyContent: "center",
//     borderRadius: "50%",
//     boxShadow: "0 0 15px rgba(0, 255, 255, 0.3)",
//     backgroundColor: "#000000",
//     padding: "4px",
//   },
//   flippingImage: {
//     width: "85%",
//     height: "85%",
//     objectFit: "contain",
//     borderRadius: "50%",
//     filter: "brightness(1.2)",
//     animation: "rotate 2s linear infinite",
//   },
// };

// export default Loader;



// Flip

// import React, { useEffect } from "react";

// const Loader = ({ open }) => {
//   useEffect(() => {
//     // Move style injection to useEffect
//     const stylesTag = document.createElement("style");
//     stylesTag.innerHTML = `
//       @keyframes flip  {
//        0% { transform: rotateY(0deg); }
//     100% { transform: rotateY(360deg); }
//       }
//     `;
//     document.head.appendChild(stylesTag);

//     // Cleanup function to remove styles when component unmounts
//     return () => {
//       stylesTag.remove();
//     };
//   }, []); // Empty dependency array means this runs once on mount
//   if (!open) return null;

//   return (
//     <div style={styles.overlay}>
//       <div style={styles.loaderContainer}>
//         <div style={styles.imageWrapper}>
//           <Image src="/loader.jpg" alt="Loading" style={styles.flippingImage} />
//         </div>
//       </div>
//     </div>
//   );
// };

// // **Inline Styles**
// const styles = {
//   overlay: {
//     position: "fixed",
//     top: 0,
//     left: 0,
//     width: "100%",
//     height: "100%",
//     backgroundColor: "rgba(0, 0, 0, 0.7)",
//     display: "flex",
//     alignItems: "center",
//     justifyContent: "center",
//     zIndex: 1000,
//   },
//   loaderContainer: {
//     position: "relative",
//   },
//   imageWrapper: {
//     width: "100px",
//     height: "100px",
//     perspective: "1000px",
//     display: "flex",
//     alignItems: "center",
//     justifyContent: "center",
//     borderRadius: "12px",
//   },
//   flippingImage: {
//     width: "80%",
//     height: "80%",
//     objectFit: "contain",
//     transformStyle: "preserve-3d",
//     backfaceVisibility: "visible",
//     animation: "flip 1s infinite linear",
//     backgroundColor: "#000000",
//     borderRadius: "18px",
//   },
// };



// export default Loader;
