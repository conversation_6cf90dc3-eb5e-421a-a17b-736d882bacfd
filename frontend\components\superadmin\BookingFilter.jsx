 
 
// "use client";
// import Image from "next/image";
// import { useState } from "react";
// import { FaRedo, FaChevronDown, FaChevronUp } from "react-icons/fa";

// const Dropdown = ({ label, options, width = "w-48" }) => {
//   const [isOpen, setIsOpen] = useState(false);
//   const [selectedOption, setSelectedOption] = useState(label);

//   const toggleDropdown = () => setIsOpen(!isOpen);

//   const handleOptionClick = (option) => {
//     setSelectedOption(option);
//     setIsOpen(false);
//   };

//   return (
//     <div className={`relative ${width}`}>
//       <button
//         onClick={toggleDropdown}
//         className="bg-transparent focus:outline-none sm:text-sm w-full flex items-center justify-center"
//       >
//         {selectedOption}
//         {isOpen ? (
//           <FaChevronUp className="ml-2 text-black" size={12} />
//         ) : (
//           <FaChevronDown className="ml-2 text-black" size={12} />
//         )}
//       </button>
//       {isOpen && (
//         <ul className="absolute z-10 bg-white border rounded-lg shadow-md mt-2 w-full">
//           {options.map((option, index) => (
//             <li
//               key={index}
//               className="p-2 hover:bg-gray-100 cursor-pointer"
//               onClick={() => handleOptionClick(option)}
//             >
//               {option}
//             </li>
//           ))}
//         </ul>
//       )}
//     </div>
//   );
// };

// const BookingFilter = () => {
//   return (
//     <div className="flex flex-wrap">
//       <button className="flex items-center justify-center border h-[50px] px-3 rounded-l-xl bg-white dark:bg-gray-500 dark:border-0.5">
//         <Image
//           src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/filter.png`}
//           width={22}
//           height={25}
//           alt=""
//           loading="lazy"
//         />
//       </button>
//       <div className="text-sm font-poppins bg-white flex items-center border h-[50px] justify-center text-black font-semibold px-4 dark:bg-black dark:border-0.5 dark:text-[#B6B6B6]">
//         Filter By
//       </div>
//       <div className="flex items-center justify-center text-black font-semibold font-poppins text-sm space-x-4 border h-[50px] bg-white">
//         <Dropdown label="Country" options={["India", "UK"]} width="w-40" />
//       </div>
//       <div className="flex text-black font-semibold font-poppins text-sm items-center justify-center space-x-4 border h-[50px] bg-white">
//         <Dropdown
//           label="Payment Status"
//           options={["40000", "50000", "80000"]}
//           width="w-48"
//         />
//       </div>
//       <div className="flex text-black font-semibold font-poppins text-sm items-center justify-center space-x-4 border h-[50px] bg-white">
//         <Dropdown
//           label="Status"
//           options={["Booked", "Cancelled", "Processing", "On Hold"]}
//           width="w-40"
//         />
//       </div>
//       <button className="text-red-600 bg-white text-sm font-semibold h-[50px] border rounded-r-xl flex items-center justify-center px-6">
//         <FaRedo className="mr-1" size={16} />
//         Reset Filter
//       </button>
//     </div>
//   );
// };

// export default BookingFilter;


"use client";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { FaRedo, FaChevronDown, FaChevronUp } from "react-icons/fa";

const Dropdown = ({ label, options, width = "w-48" }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState(label);
  const dropdownRef = useRef(null);

  const toggleDropdown = () => setIsOpen(!isOpen);

  const handleOptionClick = (option) => {
    setSelectedOption(option);
    setIsOpen(false);
  };
    // Function to handle click outside
    useEffect(() => {
      function handleClickOutside(event) {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
          setIsOpen(false);
        }
      }
      document.addEventListener("mousedown", handleClickOutside);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }, []);

  return (
    <div className={`relative ${width}` } ref={dropdownRef}>
      <button
        onClick={toggleDropdown}
        className="bg-transparent focus:outline-none sm:text-sm w-full flex items-center justify-center "
      >
        {selectedOption}
        {isOpen ? (
          <FaChevronUp className="ml-2 text-black dark:text-[#B6B6B6]" size={12} />
        ) : (
          <FaChevronDown className="ml-2 text-black dark:text-[#B6B6B6]" size={12} />
        )}
      </button>
      {isOpen && (
        <ul className="absolute z-10 bg-white border rounded-lg shadow-md mt-2 w-full dark:bg-[#171616] dark:border-none">
          {options.map((option, index) => (
            <li
              key={index}
              className="p-2 hover:bg-gray-100 cursor-pointer text-sm font-semibold dark:hover:bg-[#393939b7]"
              onClick={() => handleOptionClick(option)}
            >
              {option}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

const BookingFilter = () => {
  return (
    <div className="flex flex-wrap">
      <button className="flex h-[50px] items-center justify-center border px-3 rounded-l-xl dark:border-0.5">
        <Image
          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/filter.png`}
          width={22}
          height={25}
          loading="lazy"
          alt=""
        />
      </button>
      <div className="text-sm font-poppins bg-white flex items-center border h-[50px] justify-center text-black font-semibold px-6 dark:bg-black dark:text-[#B6B6B6] dark:border-0.5 dark:border-gray-200">
        Filter By
      </div>
      <div className="flex text-sm font-poppins text-black font-semibold items-center justify-center space-x-4 border px-2 h-[50px] bg-white dark:bg-black dark:text-[#B6B6B6] dark:border-0.5 dark:border-gray-200">
        <Dropdown label="Country" options={["India", "UK"]} width="w-32" />
      </div>
      <div className="flex text-sm font-poppins items-center justify-center text-black font-semibold space-x-4 border px-2 h-[50px] bg-white dark:bg-black dark:text-[#B6B6B6] dark:border-0.5 dark:border-gray-200">
        <Dropdown
          label="Payment Status"
          options={["paid", "unpaid"]}
          width="w-40"
          
            
        />
      </div>
      <div className="flex text-sm font-poppins text-black font-semibold items-center justify-center space-x-4 border px-2 h-[50px] bg-white dark:bg-black dark:text-[#B6B6B6] dark:border-0.5 dark:border-gray-200">
        <Dropdown
          label="Status"
          options={["Inprocess", "Booked", "Cancelled",]}
          width="w-36"
          
        />
      </div>
      
      <button className="text-red-600 bg-white text-sm font-poppins font-semibold border px-6 h-[50px] rounded-r-xl flex items-center justify-center dark:bg-black  dark:border-0.5 dark:border-gray-200">
        <FaRedo className="mr-1" size={16} />
        Reset Filter
      </button>
    </div>
  );
};

export default BookingFilter;
