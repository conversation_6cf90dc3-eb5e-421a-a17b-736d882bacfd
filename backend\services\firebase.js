import userModel from '../models/auth.js';
const saveFcmTokenService = async (token, userId) => {
    try {
        const result = await userModel.findOneAndUpdate(
            { _id: userId, fcmTokens: { $ne: token } },
            { $addToSet: { fcmTokens: token } },
            { new: true, upsert: false }
        );
    } catch (error) {
        throw error;
    }
};
const removeFcmTokenService = async (token, userId) => {
    try {
        const result = await userModel.findOneAndUpdate(
            { _id: userId },
            { $pull: { fcmTokens: token } },
        );
        return result;
    } catch (error) {
        throw error;
    }
};

export { saveFcmTokenService ,removeFcmTokenService};
