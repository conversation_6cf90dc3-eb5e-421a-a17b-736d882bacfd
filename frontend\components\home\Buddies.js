/* eslint-disable react/no-unescaped-entities */
import React from "react";
import Image from "next/image";
import Link from "next/link";

const Buddies = () => {
  return (
    <>
      <section className="w-full bg-white md:pb-16 pb-10 lg:px-6 hidden sm:block ">
        <div className="container">
          <h2 className="font-semibold text-black font-manrope md:text-3xl sm:text-2xl text-xl">
            Meet Buddies, Split Bills, Share Rides, and{" "}
            <br className="hidden sm:block" />
            Explore with AI-powered
            <span className="text-primary-blue"> Ease</span>!"
          </h2>
          <p className="block mt-3 text-base font-medium text-[#737373] font-manrope mb-6 text-[15px]">
            Traveling just got easier, more affordable, and more fun with
            Mixdorm's exclusive features! Whether you're on a solo adventure or
            traveling with friends, our app helps you meet new people, manage
            your expenses, and explore destinations hassle-free. Let's break
            down how these features will transform your travel experience:
          </p>

          <div className="grid grid-cols-2 md:grid-cols-3 sm:grid-cols-2 justify-center lg:gap-14 md:gap-8 gap-4">
            <div className="relative">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/buddies1.png`}
                width={400}
                height={637}
                alt=""
                className="w-full"
                loading="lazy"
              />
              <Link
                href=""
                className="max-w-64 w-full absolute left-1/2 -translate-x-1/2 bg-[#FFC22A] sm:text-base text-xs text-black rounded-3xl sm:p-4 p-3 font-medium text-sm text-center sm:bottom-[50px] bottom-[15px]"
                prefetch={false}
              >
                Get started
              </Link>
            </div>
            <div className="relative">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/buddies2.png`}
                alt=""
                className="w-full"
                width={400}
                height={637}
                loading="lazy"
              />
              <Link
                href=""
                className="max-w-64 w-full absolute left-1/2 -translate-x-1/2 bg-[#EA4080] sm:text-base text-xs text-black rounded-3xl sm:p-4 p-3 font-medium text-sm text-center sm:bottom-[50px] bottom-[15px]"
                prefetch={false}
              >
                Get started
              </Link>
            </div>
            <div className="relative">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/buddies3.png`}
                alt=""
                className="w-full"
                width={400}
                height={637}
                loading="lazy"
              />
              <Link
                href="#"
                className="max-w-64 w-full absolute left-1/2 -translate-x-1/2 bg-[#4C3CB9] sm:text-base text-xs text-white rounded-3xl sm:p-4 p-3 font-medium text-sm text-center sm:bottom-[50px] bottom-[15px]"
                prefetch={false}
              >
                Get started
              </Link>
            </div>
          </div>
          <div className="text-center mt-10">
            <Link
              href="/meetbuddies"
              className="text-sm font-semibold text-black bg-primary-blue rounded-4xl py-4 px-12 hover:bg-sky-blue-750"
              prefetch={false}
            >
              Learn More
            </Link>
          </div>
        </div>
      </section>
    </>
  );
};

export default Buddies;
