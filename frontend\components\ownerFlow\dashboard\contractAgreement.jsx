/* eslint-disable react/no-unescaped-entities */
import React, { useState } from "react";
import Image from "next/image";
import { useHeaderOwner } from "../headerContex";
import CustomSelect from "@/components/common/CustomDropdown";

const ContractAgreement = () => {

    const [activeSection, setActiveSection] = useState("default");
    const { profileData } = useHeaderOwner();
    
    const handleSecondSectionClick = () => {
        setActiveSection("requestchannel");
    };

    return (

        <>
        {activeSection === "default" && (
            <div>
                <h1 className="page-title">
                    Hostel Mixdorm Private Limited E-Contract Agreement
                </h1>
                <div className="w-full bg-white border border-slate-200 border-1 my-5 sm:px-5 px-3 pt-5 pb-10 rounded-lg">
                    <Image className="text-center mx-auto mb-10" src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/contract.svg`} width={226} height={209}/>
                    <p className="text-[#888888] text-sm font-normal">This agreement ("Agreement") is entered into as of the date of electronic signing
                    ("<span className="text-[#DE0000]">Effective Date</span>") by and between</p>

                    <div className='relative my-3'>
                        <label className='block text-black sm:text-sm text-xs font-semibold mb-1.5'>
                            Date
                        </label>
                        <div className='datepicker relative'>
                            <Image
                            className='text-xl absolute left-[10px] top-[12px] sm:w-[20px] sm:h-[20px] w-[15px] h-[15px]'
                            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/calendar2.svg`}
                            width='20'
                            height='20'
                            ></Image>  
                            <input
                                type='text'
                                name='checkInDate'
                                value="21 December 2024"
                                className='block border border-black/50 rounded sm:px-4 px-2 sm:py-2.5 py-2 sm:pl-10 pl-7 shadow-sm appearance-none cursor-pointer focus:outline-none focus:ring-1 focus:ring-teal-500 sm:text-sm text-xs text-[#00000080] font-semibold'
                            />                      
                            <div className='absolute top-3 right-3 cursor-pointer w-5 h-5 bg-white'></div>
                        </div>
                    </div>

                    <p className="text-[#888888] text-sm font-normal">
                        Hostel Mixdorm Private Limited ("MixDorm"), having its registered office at 47/7
                        Baskheda Main Road Kareli 487221 India, Madhya Pradesh (hereinafter referred to
                        as "Service Provider"), and
                        [<span className="text-[#DE0000]">Hostel Name</span>] ("Hostel Partner"), having its registered address at [<span className="text-[#DE0000]">Hostel Address</span>]
                        (hereinafter referred to as "Partner")
                    </p>

                    <div className="sm:flex block my-3 gap-2">
                        <div>
                            <label className='block text-black sm:text-sm text-xs font-semibold mb-1.5'>
                            Hostel Name
                            </label>
                            <input
                            type='text'
                            name='name'
                            value="Hostel Mama"
                            className='sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded focus:outline-none focus:ring-1 focus:ring-teal-500 sm:text-sm text-xs text-[#00000080] font-semibold'
                            />
                        </div>
                        <div>
                            <label className='block text-black sm:text-sm text-xs font-semibold mb-1.5'>
                            Hostel Address
                            </label>
                            <input
                            type='text'
                            name='name'
                            value="22 Lake Pichola, Gali 2 Udaipur, India 776677"
                            className='w-max sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded focus:outline-none focus:ring-1 focus:ring-teal-500 sm:text-sm text-xs text-[#00000080] font-semibold'
                            />
                        </div>
                    </div>

                    <p className="block text-black sm:text-sm text-xs font-semibold mb-3">Terms and Conditions</p>
                    <p className="block text-black sm:text-sm text-xs font-semibold mb-1">1. Objective </p>
                    <p className="block text-[#888888] sm:text-sm text-xs font-normal mb-1.5">This Agreement sets forth the terms and conditions under which the Partner will utilize
                    MixDorm's platform and services to facilitate online booking of its hostel accommodations.</p>
                    <p className="block text-black sm:text-sm text-xs font-semibold mb-1">2. Commission Structure </p>
                    <p className="block text-black sm:text-sm text-xs font-semibold mb-1 pl-3">1. Standard Commission: </p>
                    <ul className="list-disc sm:pl-8 pl-5">
                        <li className="text-[#888888] sm:text-sm text-xs font-normal mb-1.5">MixDorm shall retain a commission of 15% on the total booking value for all
                        bookings facilitated through the MixDorm platform.</li>
                    </ul>
                    <p className="block text-black sm:text-sm text-xs font-semibold mb-1 pl-3">2. Exclusive Top Featured Property Commission: </p>
                    <ul className="list-disc sm:pl-8 pl-5">
                        <li className="text-[#888888] sm:text-sm text-xs font-normal mb-1.5">For properties opting to be exclusively featured in top search results and
                        promoted listings, a commission of 20% shall be applicable on the total
                        booking value.</li>
                    </ul>
                    <p className="block text-black sm:text-sm text-xs font-semibold mb-1 pl-3">3. Payment Terms </p>
                    <ol className="list-decimal sm:pl-8 pl-5">
                        <li className="text-[#888888] sm:text-sm text-xs font-normal mb-1.5">Commissions will be automatically deducted from the booking amount before the
                        payout is transferred to the Partner.</li>
                        <li className="text-[#888888] sm:text-sm text-xs font-normal mb-1.5">Payments to the Partner will be disbursed on a weekly basis, subject to the deduction
                        of applicable commissions and taxes.</li>
                        <li className="text-[#888888] sm:text-sm text-xs font-normal mb-1.5">Commissions will be automatically deducted from the booking amount before the
                        payout is transferred to the Partner.</li>
                    </ol>
                    <p className="block text-black sm:text-sm text-xs font-semibold mb-1 pl-3">4. Partner Obligations </p>
                    <ol className="list-decimal sm:pl-8 pl-5">
                        <li className="text-[#888888] sm:text-sm text-xs font-normal mb-1.5">Ensure accurate and up-to-date property information, including availability, pricing,
                        and images, on the MixDorm platform.</li>
                        <li className="text-[#888888] sm:text-sm text-xs font-normal mb-1.5">Honor all bookings made through MixDorm and provide quality service to customers.
                        </li>
                        <li className="text-[#888888] sm:text-sm text-xs font-normal mb-1.5">Notify MixDorm immediately of any discrepancies or cancellations.</li>
                    </ol>
                    <p className="block text-black sm:text-sm text-xs font-semibold mb-1 pl-3">5. Term and Termination</p>
                    <ol className="list-decimal sm:pl-8 pl-5">
                        <li className="text-[#888888] sm:text-sm text-xs font-normal mb-1.5">This Agreement shall remain in effect unless terminated by either party with a 30-day
                        prior written notice.
                        </li>
                        <li className="text-[#888888] sm:text-sm text-xs font-normal mb-1.5">MixDorm may terminate this Agreement immediately in case of breach of terms or
                        fraudulent activity by the Partner.
                        </li>
                    </ol>
                    <p className="block text-black sm:text-sm text-xs font-semibold mb-1 pl-3">6. Dispute Resolution</p>
                    <p className="text-[#888888] sm:text-sm text-xs font-normal mb-1.5 pl-3">In the event of any dispute arising under this Agreement, the parties agree to resolve the
                    dispute through mutual negotiation. If unresolved, the dispute shall be referred to arbitration
                    under the Indian Arbitration and Conciliation Act, 1996.</p>
                    <p className="block text-black sm:text-sm text-xs font-semibold mb-1 pl-3">7. Liability and Indemnity</p>
                    <ol className="list-decimal sm:pl-8 pl-5">
                        <li className="text-[#888888] sm:text-sm text-xs font-normal mb-1.5">MixDorm is not liable for any direct, indirect, or consequential damages resulting from
                        the Partner’s failure to fulfill bookings. </li>
                        <li className="text-[#888888] sm:text-sm text-xs font-normal mb-1.5">The Partner agrees to indemnify and hold MixDorm harmless from any claims arising
                        out of the Partner's non-compliance with laws or regulations.
                        </li>
                    </ol>
                    <p className="block text-black sm:text-sm text-xs font-semibold mb-1 pl-3">8. Confidentiality</p>
                    <p className="text-[#888888] sm:text-sm text-xs font-normal mb-1.5 pl-3">Both parties agree to maintain confidentiality regarding sensitive business and customer
                    information and shall not disclose such information without prior consent.</p>
                    <p className="block text-black sm:text-sm text-xs font-semibold mb-1 pl-3">9. Governing Law</p>
                    <p className="text-[#888888] sm:text-sm text-xs font-normal mb-1.5 pl-3">This Agreement shall be governed by and construed in accordance with the laws of India,
                    Madhya Pradesh.</p>
                    <p className="block text-black sm:text-sm text-xs font-semibold mb-1 pl-3">Agreement and Acceptance</p>
                    <p className="text-[#888888] sm:text-sm text-xs font-normal mb-1.5 pl-3">This Agreement shall be governed by and construed in accordance with the laws of India,
                    Madhya Pradesh.</p>
                    <p className="block text-black sm:text-sm text-xs font-semibold mb-1 pl-3">E-Signature Options</p>
                    <div className="mb-3">
                        <label className='block text-black sm:text-sm text-xs font-semibold mb-1.5'>
                        Type your name
                        </label>
                        <input
                        type='text'
                        name='name'
                        value="Enter Name"
                        className='sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded focus:outline-none focus:ring-1 focus:ring-teal-500 sm:text-sm text-xs text-[#00000080] font-semibold'
                        />
                    </div>
                    <div className="sm:w-[70%] ">
                        <label className='flex gap-1 items-center text-black sm:text-sm text-xs font-semibold mb-1.5'>
                        <span>Draw your signature</span> <span><Image src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/upload-icon.svg`} width={28} height={28}></Image></span>
                        </label>
                        <input
                            type='file'
                            name='signature'
                            multiple
                            accept='.jpg, .jpeg, .png'
                            className='z-10 cursor-pointer block w-full p-2 px-4 text-sm bg-transparent border rounded-lg opacity-0 border-gray-220 focus:outline-none text-slate-320 placeholder:text-gray-320 absolute top-0 left-0 right-0 bottom-0'
                        />
                        <div className='px-3 py-4 rounded focus:outline-none focus:ring-1 focus:ring-teal-500 text-[#5D6679] text-sm placeholder:text-gray-500 flex items-center justify-center border border-[#B9BDC7] cursor-pointer h-[113px] text-center'>
                            <Image 
                                src=""
                                className="object-cover rounded-sm w-full h-[100px]"
                                />
                        </div>
                        <div className='flex justify-between gap-3 py-4'>
                            <button
                            type='button'
                            className='hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 sm:px-10 px-4 border-black rounded-lg w-auto text-sm xs:mb-0 mb-2'
                            >
                            Clear
                            </button>
                            <button
                            type='submit'
                            className='bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 sm:px-10 px-4  border-black rounded-lg w-auto text-sm'
                            >
                            Save
                            </button>
                        </div>
                    </div>
                    <p className="block text-black sm:text-sm text-xs font-semibold mb-3">Contact Information</p>
                    <p className="block text-black sm:text-sm text-xs font-semibold mb-1">MixDorm Support Team</p>
                    <p className="text-[#888888] sm:text-sm text-xs font-normal mb-1.5">Email: <EMAIL>
                    </p>
                    <p className="text-[#888888] sm:text-sm text-xs font-normal mb-1.5">Phone: +919244770117
                    </p>
                    <div className='flex justify-center mt-8'>
                        <button
                        type='submit'
                        className='bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 sm:px-10 px-4  border-black rounded-lg w-auto text-sm'
                        onClick={handleSecondSectionClick}
                        >
                        Submit
                        </button>
                    </div>
                </div>
            </div>
        )}

        {activeSection === "requestchannel" && (
            <div>
                <h1 className="text-xl font-semibold mb-5">
                Request Channel Manager Integration
                </h1>
                <div className="w-full bg-white border border-slate-200 border-1 my-5 px-4 pt-5 pb-10 rounded-lg">
                    <div className="flex justify-between">
                        <div>
                            <Image
                                src={profileData?.profileImage?.objectURL}
                                width={40}
                                height={40}
                                alt="Avatar"
                                title="Avatar"
                                className="w-10 h-10 cursor-pointer"
                                loading="lazy"
                            />
                            <h2 className="mb-4 text-2xl font-bold text-black-800 text-center mt-2">MixDorm</h2>
                        </div>
                        <a href="/owner/dashboard">
                            <button
                                className='hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 sm:px-10 px-4 border-black rounded-lg w-full text-sm xs:mb-0 mb-2'
                                >
                                Back to Dashboard
                            </button>
                        </a>
                    </div>
                    <p className="sm:text-base text-sm text-black font-inter font-semibold mb-3">Confirmation Message</p>
                    <p className="text-sm text-black font-inter font-bold">"Your Agreement with <span className="text-[#40E0D0]">Mix</span>Dorm is Complete!"</p>
                    <p className="text-sm text-[#888888] font-inter font-normal mb-3">Thank you for signing the agreement. The next step is to request your channel manager to MixDorm for seamless inventory and rate sharing.</p>
                    <p className="text-sm text-black font-inter font-bold mb-3">Email Pre-fill Template</p>
                    <div className="lg:w-[50%] md:w-[30%] w-full font-semibold text-sm !text-[#00000080]">
                        <CustomSelect
                            name='email'
                            options={[
                                { value: "", label: "Email" },
                                { value: "zostelmumbai@gmail .com", label: "zostelmumbai@gmail .com" },
                                { value: "<EMAIL>", label: "<EMAIL>" },
                            ]}
                            value="zostelmumbai@gmail .com"
                            placeholder='Select Email'
                        />
                    </div>
                    <div className="my-3 lg:w-[50%] md:w-[30%] w-full">
                        <label className='block text-black sm:text-sm text-xs font-semibold mb-1.5'>
                        Cc :
                        </label>
                        <input
                        type='text'
                        name='Cc'
                        value="support@mixdorm .com"
                        className='sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded focus:outline-none focus:ring-1 focus:ring-teal-500 sm:text-sm text-xs text-[#00000080] font-semibold w-full'
                        />
                    </div>
                    <div className="my-3 lg:w-[50%] md:w-[30%] w-full">
                        <label className='block text-black sm:text-sm text-xs font-semibold mb-1.5'>
                        Subject
                        </label>
                        <input
                        type='text'
                        name='name'
                        value="Channel Manager OTA Integration"
                        className='sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded focus:outline-none focus:ring-1 focus:ring-teal-500 sm:text-sm text-xs text-[#00000080] font-semibold w-full'
                        />
                    </div>
                    <textarea className="border border-black/50 rounded w-full" rows={5}></textarea>
                    <p className="text-sm text-[#888888] font-inter font-normal mb-3">Request to Establish Connection Between MixDorm
                    and Channel Manager</p>
                    <div className="lg:w-[50%] md:w-[30%] w-full font-semibold text-sm !text-[#00000080]">
                        <label className='block text-black sm:text-sm text-xs font-semibold mb-1.5'>
                        Select channel manager
                        </label>
                        <CustomSelect
                            name='email'
                            options={[
                                { value: "", label: "Email" },
                                { value: "zostelmumbai@gmail .com", label: "zostelmumbai@gmail .com" },
                                { value: "<EMAIL>", label: "<EMAIL>" },
                            ]}
                            value="zostelmumbai@gmail .com"
                            placeholder='Select Email'
                        />
                        <div className="flex justify-end">
                            <button
                                type='submit'
                                className='bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4 mt-2 mb-3 border-black rounded-lg w-auto text-sm'
                                >
                                Edit Contact Email
                            </button>
                        </div>
                    </div>
                    <p className="text-sm text-black font-inter font-bold">Support Note:</p>
                    <p className="text-sm text-[#888888] font-inter font-normal mb-3">"Need help? Contact <NAME_EMAIL> or call
                    +91-9244770117."</p>
                    <div className="flex justify-center sm:mb-10 mb-5 sm:mt-20 mt-10">
                        <button
                            type='submit'
                            className='bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4 border-black rounded-lg sm:w-[30%] text-sm'
                            >
                            Send Email
                        </button>
                    </div>
                </div>
            </div>
        )}

        </>
    );
};

export default ContractAgreement;
