"use client";
import React from "react";
import { FiEye } from "react-icons/fi";
import { TfiPencilAlt } from "react-icons/tfi";
import { FaRegTrashCan } from "react-icons/fa6";
import Image from "next/image";
import { Plus } from "lucide-react";
import Link from "next/link";

const OfferBanner = () => {
  const aboutusData = [
    {
      id: 1,
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Offer1.png`,
    },
    {
      id: 2,
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Offer2.png`,
    },
  ];
  return (
    <div className="w-full p-7 bg-sky-blue-20 lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px]  float-end  overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <div className="flex items-center justify-between w-full ">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Offer Banner
        </h2>
        <div className="w-[50%] gap-x-5 flex justify-end items-center">
          <Link
          href={"/superadmin/dashboard/offer-banner-add"}
            className={`px-4 py-2 text-sm font-poppins font-medium text-white rounded relative flex justify-center items-center bg-sky-blue-650`}
            type="button"
           
          >
            <Plus size={18} className="mr-1" /> Offer
          </Link>
        </div>
      </div>
      <div className="bg-white border-b border-l border-r rounded-xl dark:bg-black dark:border-none">
        <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border-t dark:border-none">
          <table className="min-w-full divide-y bg-white rounded-xl divide-gray-200 dark:bg-black">
            <thead>
              <tr>
                <th className="pl-12 md:pl-24  py-6 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  OFFER BANNER
                </th>
                <th className="pr-10 py-6 bg-white text-right text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  ACTION
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 border-x border-y dark:border-x-0 text-black/70">
              {aboutusData.map((aboutus) => (
                <tr key={aboutus.id}>
                  <td className="text-left pl-2 md:pl-4 lg:pl-6">
                    <button className="font-medium py-4 rounded">
                      <Image
                        src={aboutus.image}
                        alt="Image"
                        className="w-48 h-32 md:w-72 md:h-44 lg:w-72 lg:h-44 "
                        width={100}
                        height={70}
                      />
                    </button>
                  </td>
                  <td className="py-5 pr-4 flex justify-end ">
                    <Link href={"/superadmin/dashboard/offer-banner-details"} className="border p-2 rounded-l-lg text-black/75 hover:text-blue-800 dark:text-[#757575] dark:hover:text-blue-800">
                      <FiEye />
                    </Link>
                    <Link href={"/superadmin/dashboard/offer-banner-edit"} className="border p-2 text-black/75 hover:text-yellow-400 dark:text-[#757575] dark:hover:text-yellow-400">
                      <TfiPencilAlt />
                    </Link>
                    <button className="p-2 border rounded-r-lg text-red-600">
                      <FaRegTrashCan />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default OfferBanner;
