 
"use client";
import { useState } from "react";
import { Plus } from "lucide-react";
import Link from "next/link";
import { FaRegTrashCan } from "react-icons/fa6";
import { FiEye } from "react-icons/fi";
import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight,
} from "react-icons/md";
import { TfiPencilAlt } from "react-icons/tfi";

// Function to split text into lines with a specific number of words per line
const splitTextByWords = (text, wordsPerLine) => {
  if (typeof text !== "string") return [text]; // Wrap non-string content in an array
  const words = text.split(" ");

  // If no words are found, return an empty array to avoid `.map` errors
  if (words.length === 0) return [];

  return words
    .reduce((acc, word, index) => {
      if (index % wordsPerLine === 0) acc.push([]);
      acc[acc.length - 1].push(word);
      return acc;
    }, [])
    .map((line) => line.join(" "));
};



const BookingGuarantee = () => {
  // eslint-disable-next-line no-unused-vars
  const [wordsPerLine, setWordsPerLine] = useState(13);

  const guranteeData = [
    {
      id: 1,
      name: (
        <div className="">
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            Introduction
          </span>
        </div>
      ),
      feedback:
        "At MixDorm, we understand the importance of a hassle-free booking experience. OurBooking Guarantee ensures that your reservations are secure and your stay meets your expectations.",
    },
    {
      id: 2,
      name: (
        <div className="">
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            Our Guarantee
          </span>
        </div>
      ),
      feedback: (
        <div>
          <h1 className="text-base text-black font-bold dark:text-[#B6B6B6]">
            1. Secure Reservation
          </h1>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            • When you book with MixDorm, your reservation is guaranteed. We use
            secure and <br /> trusted payment gateways to protect your personal
            and payment information.
          </p>

          <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
            2. Best Price Assurance
          </h1>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            • We assure you that the price you see on our website is the best
            available rate. If you <br /> find a lower price elsewhere, let us
            know and we will match it.
          </p>

          <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
            3. Confirmed Bookings
          </h1>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            • Once your booking is confirmed, you will receive an email with all
            the details of your <br /> reservation. This confirmation guarantees
            your spot at the chosen MixDorm <br /> property
          </p>

          <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
            4. Flexible Policies
          </h1>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            • We offer flexible booking policies to accommodate changes in your
            travel plans. <br /> Please review our Cancellation Policy for more
            details on how to modify or cancel <br /> your reservation.
          </p>
        </div>
      ),
    },
    {
      id: 3,
      name: (
        <div>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            What to expect
          </span>
        </div>
      ),
      feedback: (
        <div>
          <h1 className="text-base text-black font-bold ">What to Expect</h1>
          <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
            1. Quality Accomodations
          </h1>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            • MixDorm ensures that all our properties meet high standards of
            cleanliness, safety, <br/> and comfort. We regularly inspect our hostels
            to maintain these standards.
          </p>
          <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
            2. 24/7 Customer Support
          </h1>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            • Our customer support team is available 24/7 to
            assist you with any queries or <br/> concerns. Whether you need help with
            your booking or have questions about your <br/> stay, we are here to help.
          </p>
          <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
            3. No Hidden Feses
          </h1>
          <p className="text-sm text-gray-500 font-semibold dark:text-[#757575]">
            • The price you see at the time of booking is the
            price you pay. We believe in <br/> transparent pricing with no hidden
            charges or surprise fees.

          </p>
        </div>
      ),
    },
  ];

  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <div className="flex items-center justify-between w-full">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Booking Guarantee
        </h2>
        <div className="w-[50%] gap-x-5 flex justify-end items-center">
          <Link
            href={"/superadmin/dashboard/booking-guarantee-add"}
            className="px-4 py-2 text-sm font-medium font-poppins text-white rounded flex items-center bg-sky-blue-650"
          >
            <Plus size={18} className="mr-1" /> Booking Guarantee
          </Link>
        </div>
      </div>

      <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border dark:border-none">
        <table className="w-full divide-y bg-white rounded-xl divide-gray-200 dark:bg-black">
          <thead>
            <tr className="w-full items-center justify-between">
              <th className="pr-8 py-6 bg-white text-center text-sm font-semibold font-poppins text-black uppercase dark:bg-black dark:text-[#B6B6B6]">
                TITLE TEXT
              </th>
              <th className="py-6 pr-10 md:pr-8 lg:pr-10 bg-white text-sm font-semibold text-center font-poppins text-black uppercase dark:bg-black dark:text-[#B6B6B6]">
                SUB TEXT
              </th>
              <th className="py-6  bg-white text-center text-sm font-poppins font-semibold text-black uppercase dark:bg-black dark:text-[#B6B6B6]">
                ACTION
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 border-x border-y dark:border-x-0 text-black/70 dark:text-[#757575]">
            {guranteeData.map((guranteeItem) => (
              <tr key={guranteeItem.id} className="w-full">
                <td className="whitespace-nowrap text-gray-500 text-sm font-medium font-poppins px-6 py-8 dark:text-[#757575]">
                  {guranteeItem.name}
                </td>
                <td className="pl-6 lg:pl-32 px-5 text-sm text-gray-500 font-medium font-poppins py-4 dark:text-[#757575]">
                  {splitTextByWords(guranteeItem.feedback, wordsPerLine)?.map(
                    (line, index) => (
                      <div key={index} className="block">
                        {line}
                      </div>
                    )
                  )}
                </td>
                <td className="py-6 md:py-6 lg:py-10 flex justify-end px-6">
                  <Link
                    href={"/superadmin/dashboard/booking-guarantee-details"}
                    className="border p-2 rounded-l-lg text-black/75 hover:text-blue-700 dark:text-[#757575] dark:hover:text-blue-700 "
                  >
                    <FiEye />
                  </Link>
                  <Link
                    href={"/superadmin/dashboard/booking-guarantee-edit"}
                    className="border p-2 text-black/75 hover:text-yellow-400 dark:text-[#757575] dark:hover:text-yellow-400"
                  >
                    <TfiPencilAlt />
                  </Link>
                  <button className="p-2 border rounded-r-lg text-red-600">
                    <FaRegTrashCan />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

       <div className="flex justify-between items-center mt-5">
                    <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
                    <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
                      <a
                        href="#"
                        className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                      >
                        <span className="sr-only">Next Page</span>
                        <MdOutlineKeyboardArrowLeft />
                      </a>
            
                      <a
                        href="#"
                        className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                      >
                        <span className="sr-only">Next Page</span>
                        <MdOutlineKeyboardArrowRight />
                      </a>
                    </div>
                  </div>
    </div>
  );
};

export default BookingGuarantee;
