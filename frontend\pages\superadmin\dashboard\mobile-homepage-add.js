"use client";
import Link from "next/link";
import React from "react";

const addHostel = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth h-screen dark:bg-[#171616]">
          <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
            Add Mobile Homepage
          </h2>
          <div className="bg-white border flex justify-center mt-5  rounded-xl h-auto px-4 md:px-5 lg:px-1 py-7 md:py-16 lg:py-8 dark:bg-black dark:border-none">
            <div className="bg-white w-full max-w-3xl dark:bg-black">
              <form>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div className="relative">
                    <label className="block font-semibold font-poppins text-sm text-black/40 dark:text-[#B6B6B6]">
                      Hostel Name
                    </label>
                    <input
                      type="text"
                      className="mt-1 font-poppins text-sm font-medium p-2 w-full  rounded-md bg-[#EEF9FF] dark:bg-transparent dark:placeholder:text-[#B6B6B6]"
                      placeholder="Enter Hostel Name"
                    />
                    {/* <button
                      type="button"
                      className="absolute right-3  top-9 text-black transform"
                    >
                      <RiArrowDropDownLine className="text-2xl" />
                    </button> */}
                  </div>
                  <div className="relative">
                    <label className="block font-semibold font-poppins text-sm text-black/40 dark:text-[#B6B6B6]">
                      Hostel Location
                    </label>
                    <input
                      type="text"
                      className="mt-1 font-poppins text-sm font-medium p-2 w-full  rounded-md bg-[#EEF9FF] dark:bg-transparent dark:placeholder:text-[#B6B6B6]"
                      placeholder="Ente Hostel Loaction"
                    />
                  </div>
    
                  <div className="relative">
                    <label className="block font-semibold font-poppins text-sm text-black/40 dark:text-[#B6B6B6]">
                      Hostel Image
                    </label>
                    <input
                      type="text"
                      className="mt-1 p-2 w-full font-poppins text-sm font-medium rounded-md bg-[#EEF9FF] dark:bg-transparent dark:placeholder:text-[#B6B6B6]"
                      placeholder="Upload"
                    />
                    {/* <button
                      type="button"
                      className="absolute right-3  top-9 text-black transform"
                    >
                      <RiArrowDropDownLine className="text-2xl" />
                    </button> */}
                  </div>
                  <div className="relative">
                    <label className="block font-semibold font-poppins text-sm text-black/40 dark:text-[#B6B6B6] dark:bg-transparent dark:placeholder:text-[#B6B6B6]">
                      Flag Image
                    </label>
                    <input
                      type="text"
                      className="mt-1 p-2 w-full font-poppins text-sm font-medium rounded-md bg-[#EEF9FF] dark:bg-transparent dark:placeholder:text-[#B6B6B6]"
                      placeholder="Upload"
                    />
                    {/* <button
                      type="button"
                      className="absolute right-3  top-9 text-black transform"
                    >
                      <RiArrowDropDownLine className="text-2xl" />
                    </button> */}
                  </div>
                  <div className="relative">
                    <label className="block font-semibold font-poppins text-sm text-black/40 dark:text-[#B6B6B6]">
                      Price
                    </label>
                    <input
                      type="text"
                      className="mt-1 p-2 w-full font-poppins text-sm font-medium  rounded-md bg-[#EEF9FF] text-black dark:bg-transparent dark:placeholder:text-[#B6B6B6]"
                      placeholder="Price"
                    />
                  </div>
                </div>
    
                {/* Buttons */}
                <div className="flex flex-wrap items-center justify-center space-x-3 my-8">
                  <Link href={"/superadmin/dashboard/mobile-homepage"}
                    type="button"
                    className="py-2 flex items-center justify-center border w-56 border-gray-300 text-black font-poppins text-sm font-medium rounded-md mt-2 dark:text-gray-100"
                  >
                    Cancel
                  </Link>
                  <button
                    type="submit"
                    className="px-6 py-2 w-56 bg-sky-blue-650 text-white font-poppins text-sm font-medium rounded-md mt-2"
                  >
                    Submit
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
  );
};

export default addHostel;
