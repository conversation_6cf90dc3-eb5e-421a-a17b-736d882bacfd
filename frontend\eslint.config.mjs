import globals from "globals";
import pluginJs from "@eslint/js";
import pluginReact from "eslint-plugin-react";

/** @type {import('eslint').Linter.Config[]} */
export default [
  { files: ["**/*.{js,mjs,cjs,jsx}"] },
  {
    ignores: [".next/", "node_modules/"]
  },
  {
    languageOptions: {
      globals: { ...globals.browser, ...globals.node, process: "readonly" },
    },
  },
  pluginJs.configs.recommended,
  pluginReact.configs.flat.recommended,
  { 
    rules: { 
      "react/prop-types": "off" ,
      "react/react-in-jsx-scope": "off"
    } 
  }
];
