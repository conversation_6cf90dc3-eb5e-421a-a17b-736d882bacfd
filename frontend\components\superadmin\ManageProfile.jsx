"use client";
import React, { useState } from "react";
import { addSubAdmin, updateSubAdminDetails } from "@/services/adminflowServices";
import toast from "react-hot-toast";

const ProfileForm = ({ profile, onSave, onCancel, onDelete }) => {
  const [formData, setFormData] = useState({
    name: profile.name || "",
    email: profile.email || "",
    phoneNumber: profile.phoneNumber || "",
    role: profile.role || "CEO",
    oldPassword: "",
    newPassword: "",
    confirmPassword: ""
  });

  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }

    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = "Phone number is required";
    } else if (!/^\d{10}$/.test(formData.phoneNumber.replace(/\D/g, ''))) {
      newErrors.phoneNumber = "Phone number must be 10 digits";
    }

    if (profile.isNew) {
      if (!formData.newPassword) {
        newErrors.newPassword = "Password is required";
      } else if (formData.newPassword.length < 8) {
        newErrors.newPassword = "Password must be at least 8 characters";
      }

      if (formData.newPassword !== formData.confirmPassword) {
        newErrors.confirmPassword = "Passwords do not match";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const handleSaveClick = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      if (profile.isNew) {
        const payload = {
          name: formData.name,
          email: formData.email,
          phone: formData.phoneNumber,
          password: formData.newPassword
        };

        const response = await addSubAdmin(payload);
        if (response?.data?.status === true) {
          toast.success("User created successfully");
          onSave({
            ...profile,
            ...formData,
            _id: response.data.data._id,
            isNew: true
          });
        } else {
          toast.error(response?.data?.message || "Failed to create user");
        }
      } else {
        // Prepare update payload
        const updatePayload = {
          name: {
            prefix: "",
            first: formData.name,
            middle: "",
            last: "",
            disp_name: formData.name
          },
          email: formData.email,
          phone: formData.phoneNumber,
          role: formData.role,
          password: formData.newPassword || undefined, // Only include if new password is provided
          isActive: true
        };

        const response = await updateSubAdminDetails(profile._id, updatePayload);
        if (response?.data?.status === true) {
          toast.success("Profile updated successfully");
          onSave({
            ...profile,
            ...formData,
            isNew: false
          });
        } else {
          toast.error(response?.message || "Failed to update profile");
        }
      }
    } catch (error) {
      toast.error(error?.message || "Something went wrong!");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClick = () => {
    if (profile._id) {
      onDelete(profile._id);
    }
  };

  return (
    <div className="w-full p-8 bg-white rounded-lg border dark:bg-black dark:border-none mt-4">
      <div className="flex justify-center">
        <div className={`w-20 h-20 rounded-full ${profile.color} flex items-center justify-center text-white text-3xl font--normal font-poppins`}>
          {!profile.isNew ? formData.name[0] : "?"}
        </div>
      </div>
      <div className="text-center flex items-center justify-center gap-2 mt-4">
        <h3 className="text-xl font-medium font-poppins dark:text-gray-200">
          {!profile.isNew && formData.name}
        </h3>
      </div>
      <div className="bg-white flex items-center mt-4 justify-center dark:bg-black">
        <div className="w-full max-w-3xl px-4 sm:px-2">
          <form className="mt-6" onSubmit={(e) => e.preventDefault()}>
            <div className="grid sm:grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex flex-col">
                <label className="font-semibold text-black/45 text-sm font-poppins dark:text-[#B6B6B6]">Name</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className={`p-3 mt-1 text-sm font-medium font-poppins border rounded bg-[#EEF9FF] w-full dark:bg-transparent dark:text-[#757575] ${errors.name ? 'border-red-500' : ''}`}
                />
                {errors.name && <span className="text-red-500 text-sm mt-1">{errors.name}</span>}
              </div>
              <div className="flex flex-col">
                <label className="font-semibold text-black/45 text-sm font-poppins dark:text-[#B6B6B6]">Email</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`p-3 mt-1 border text-sm font-medium font-poppins rounded bg-[#EEF9FF] w-full dark:bg-transparent dark:text-[#757575] ${errors.email ? 'border-red-500' : ''}`}
                />
                {errors.email && <span className="text-red-500 text-sm mt-1">{errors.email}</span>}
              </div>
              <div className="flex flex-col">
                <label className="font-semibold text-black/45 text-sm font-poppins dark:text-[#B6B6B6]">Phone Number</label>
                <input
                  type="text"
                  name="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={handleInputChange}
                  className={`p-3 mt-1 font-medium font-poppins border text-sm rounded bg-[#EEF9FF] w-full dark:bg-transparent dark:text-[#757575] ${errors.phoneNumber ? 'border-red-500' : ''}`}
                />
                {errors.phoneNumber && <span className="text-red-500 text-sm mt-1">{errors.phoneNumber}</span>}
              </div>
              <div className="flex flex-col">
                <label className="font-semibold text-black/45 text-sm font-poppins dark:text-[#B6B6B6]">User Type</label>
                <select
                  name="role"
                  value={formData.role}
                  onChange={handleInputChange}
                  className="p-3 mt-1 border text-sm rounded bg-[#EEF9FF] w-full dark:text-[#757575] dark:bg-black"
                >
                  <option className="text-sm font-medium font-poppins dark:text-[#757575]">Super Admin</option>
                  <option className="text-sm font-medium font-poppins dark:text-[#757575]">CEO</option>
                  <option className="text-sm font-medium font-poppins dark:text-[#757575]">Manager</option>
                </select>
              </div>
            </div>
            <div className="mt-4">
              <label className="font-medium text-lg font-poppins dark:text-[#B6B6B6]">
                {profile.isNew ? "Set Password" : "Change Password"}
              </label>
              <div className="grid sm:grid-cols-1 md:grid-cols-2 gap-4 mt-5">
                {!profile.isNew && (
                  <div className="flex flex-col">
                    <label className="font-semibold text-black/45 text-sm font-poppins dark:text-[#B6B6B6]">Old Password</label>
                    <input
                      type="password"
                      name="oldPassword"
                      value={formData.oldPassword}
                      onChange={handleInputChange}
                      placeholder="********"
                      className={`p-3 mt-1 bg-[#EEF9FF] placeholder:text-black border rounded w-full text-sm font-medium font-poppins dark:bg-transparent dark:placeholder:text-[#757575] ${errors.oldPassword ? 'border-red-500' : ''}`}
                    />
                    {errors.oldPassword && <span className="text-red-500 text-sm mt-1">{errors.oldPassword}</span>}
                  </div>
                )}
                <div className="flex flex-col">
                  <label className="font-semibold text-black/45 text-sm font-poppins dark:text-[#B6B6B6]">
                    {profile.isNew ? "Password" : "New Password"}
                  </label>
                  <input
                    type="password"
                    name="newPassword"
                    value={formData.newPassword}
                    onChange={handleInputChange}
                    placeholder="********"
                    className={`p-3 mt-1 bg-[#EEF9FF] placeholder:text-black border rounded w-full text-sm font-medium font-poppins dark:bg-transparent dark:placeholder:text-[#757575] ${errors.newPassword ? 'border-red-500' : ''}`}
                  />
                  {errors.newPassword && <span className="text-red-500 text-sm mt-1">{errors.newPassword}</span>}
                </div>
                <div className="flex flex-col">
                  <label className="font-semibold text-black/45 text-sm font-poppins dark:text-[#B6B6B6]">Confirm Password</label>
                  <input
                    type="password"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    placeholder="********"
                    className={`p-3 mt-1 bg-[#EEF9FF] placeholder:text-black border rounded w-full text-sm font-medium font-poppins dark:bg-transparent dark:placeholder:text-[#757575] ${errors.confirmPassword ? 'border-red-500' : ''}`}
                  />
                  {errors.confirmPassword && <span className="text-red-500 text-sm mt-1">{errors.confirmPassword}</span>}
                </div>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row items-center mt-6 gap-3">
              <button
                type="button"
                className="text-black border px-8 md:px-6 lg:px-8 py-2 rounded w-full sm:w-auto text-sm font-medium font-poppins dark:text-gray-100"
                onClick={onCancel}
                disabled={loading}
              >
                Cancel
              </button>
              {!profile.isNew && (
                <button
                  type="button"
                  className="text-red-500 border border-red-500 px-8 md:px-6 lg:px-8 py-2 rounded w-full sm:w-auto text-sm font-medium font-poppins"
                  onClick={handleDeleteClick}
                  disabled={loading}
                >
                  Delete
                </button>
              )}
              <button
                type="button"
                className="bg-sky-blue-650 text-white px-10 md:px-6 lg:px-10 py-2 rounded w-full sm:w-auto text-sm font-medium font-poppins disabled:opacity-50"
                onClick={handleSaveClick}
                disabled={loading}
              >
                {loading ? "Saving..." : "Save"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ProfileForm;
