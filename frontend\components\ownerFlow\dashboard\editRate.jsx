/* eslint-disable no-constant-binary-expression */
import { RoomListApi } from "@/services/hostemproviderservices";
import { Minus, Plus, ChevronDown  } from "lucide-react";
import React, { useState, useEffect, useRef } from "react";
import axios from "axios";
import toast, { Toaster } from "react-hot-toast";
import { getItemLocalStorage } from "@/utils/browserSetting";
import Image from "next/image";
import Loader from "@/components/loader/loader";
import countries from "world-countries";

const EditRate = ({ closeeditModal }) => {
  const [addroom, setAddRoom] = useState({
    name: "",
    type: "",
    rate: "",
    weekdayRate:"",
    currency: "",
  });

  const [properties, setProperties] = useState([]);
  const [currencies, setCurrencies] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [isLoading, setIsLoading] = useState(false);

  const [selectedOption, setSelectedOption] = useState("amount");

  const [percentage, setPercentage] = useState(10); // Default percentage value

  const handleIncrease = () => {
    if (percentage < 100) {
      setPercentage((prev) => prev + 1); // Increase percentage by 1
    }
  };

  const handleDecrease = () => {
    if (percentage > 0) {
      setPercentage((prev) => prev - 1); // Decrease percentage by 1
    }
  };

  const isCurrenciesFetched = useRef(null);
  const isFirstRender = useRef(null);
  const id = getItemLocalStorage("hopid");

  useEffect(() => {
    if (id && !isFirstRender.current) {
      fetchList(id);
      fetchCurrencies();
    } else {
      isFirstRender.current = false;
    }
  }, [id]);

  const fetchCurrencies = async () => {
      try {
        const response = await axios.get(
          "https://api.exchangerate-api.com/v4/latest/USD"
        );
        if (response.data && response.data.rates) {
          // Convert rates to array of currency codes
          const currencyList = Object.keys(response.data.rates);
          setCurrencies(currencyList);
        }
      } catch (error) {
        console.error("Error fetching currencies:", error);
        toast.error("Failed to fetch currencies");
      }
  };

  useEffect(() => {
    if (!isCurrenciesFetched.current) {
      const fetchCurrenciesWithFlags = async () => {
        try {
          // Fetch currency rates
          const exchangeRateResponse = await axios.get(
            "https://api.exchangerate-api.com/v4/latest/USD"
          );
  
          if (exchangeRateResponse.data) {
            const currencyRates = exchangeRateResponse.data.rates;
  
            // Map currency codes to country data using the world-countries package
            const currencyList = Object.keys(currencyRates).map((currencyCode) => {
              const country = countries.find((country) =>
                country.currencies
                  ? Object.keys(country.currencies).includes(currencyCode)
                  : false
              );
  
              return {
                code: currencyCode,
                name:
                  country?.currencies?.[currencyCode]?.name || currencyCode, // Fallback to the currency code if no name is found
                symbol: country?.currencies?.[currencyCode]?.symbol || "", // Fallback to an empty string if no symbol is found
                flag: `https://flagcdn.com/w320/${country?.cca2?.toLowerCase()}.png` ||
              "https://via.placeholder.com/30x25", // Fallback to a placeholder if no flag is found
              };
            });
  
            setCurrencies(currencyList);
          }
        } catch (error) {
          console.error("Error fetching currencies or rates:", error);
        }
      };
  
      fetchCurrenciesWithFlags();
      isCurrenciesFetched.current = true; // Ensure the fetch happens only once
    }
  }, []);

  const fetchList = async (id) => {
    try {
      const response = await RoomListApi(id, 1, 100);
      if (response.status === 200) {
        setProperties(response.data.data.rooms);
      } else {
        // Handle error response
        toast.error("Failed to fetch rooms");
      }
    } catch (error) {
      console.log("Error fetching rooms:", error);
      toast.error("Error fetching rooms");
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setAddRoom({
      ...addroom,
      [name]: value,
    });
    const selectedRoomName = e.target.value;
    const room = properties.find((r) => r.name === selectedRoomName);
    setAddRoom(room || {});
  };
    
  const [isOpen, setIsOpen] = useState({ name: false, type: false });
  const [selectedValues, setSelectedValues] = useState({
    name: addroom?.name || "",
    type: addroom?.type || "",
  });

  // Function to auto-select the room type based on room name
  const handleSelect = (field, value) => {
    let updatedValues = { ...selectedValues, [field]: value };

    if (field === "name") {
      // Find the selected property and set its type
      const selectedRoom = properties.find((room) => room.name === value);
      if (selectedRoom) {
        updatedValues.type = selectedRoom.type;
        handleChange({ target: { name: "type", value: selectedRoom.type } });
      }
    }

    setSelectedValues(updatedValues);
    handleChange({ target: { name: field, value } });
    setIsOpen((prev) => ({ ...prev, [field]: false }));
  };

  return (
    <>
      <Loader open={isLoading} />
      <div className='max-w-[780px] mx-auto pb-8'>
        <Toaster />
        <div className='grid grid-cols-2 sm:gap-5 gap-3'>
          <div className="sm:col-span-1 col-span-2 relative">
            <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
              {selectedValues.name ? "Room Name" : "Select Name"}
            </label>
            <div
              className={`w-full sm:px-4 px-2 sm:py-2.5 py-2 text-sm border border-black/50 rounded-lg cursor-pointer ${
                !selectedValues.name ? "text-gray-500" : "text-black"
              }`}
              onClick={() => setIsOpen({ ...isOpen, name: !isOpen.name })}
            >
              {selectedValues.name || "Room"}
              <div className="absolute right-3 top-1/2 pointer-events-none">
                <ChevronDown />
              </div>
            </div>
            {isOpen.name && (
              <ul className="absolute w-full bg-white border border-black/50 rounded-lg mt-1 shadow-lg z-20 max-h-[300px] overflow-y-auto">
                {properties.map((val, index) => (
                  <li
                    key={index}
                    className={`px-4 py-1 cursor-pointer hover:bg-[#40E0D0] text-sm first:rounded-t-lg last:rounded-b-lg ${
                      selectedValues.name === val.name ? "bg-[#40E0D0] text-white" : ""
                    }`}
                    onClick={() => handleSelect("name", val.name)}
                  >
                    {val.name}
                  </li>
                ))}
              </ul>
            )}
          </div>

          {/* Room Type Dropdown */}
          <div className="sm:col-span-1 col-span-2 relative">
            <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
              Choose Room Type
            </label>
            <div
              className={`w-full sm:px-4 px-2 sm:py-2.5 py-2 text-sm border border-black/50 rounded-lg cursor-pointer ${
                !selectedValues.type ? "text-gray-500" : "text-black"
              }`}
              onClick={() => setIsOpen({ ...isOpen, type: !isOpen.type })}
            >
              {selectedValues.type || "Room Type"}
              <div className="absolute right-3 top-1/2 pointer-events-none">
                <ChevronDown />
              </div>
            </div>
            {isOpen.type && (
              <ul className="absolute w-full bg-white border border-black/50 rounded-lg mt-1 shadow-lg z-20 max-h-[300px] overflow-y-auto">
                {["Private", "Dbl Private", "Mixed Dorm", "Female Dorm"].map((option, index) => (
                  <li
                    key={index}
                    className={`px-4 py-1 cursor-pointer hover:bg-[#40E0D0] text-sm first:rounded-t-lg last:rounded-b-lg ${
                      selectedValues.type === option.toLowerCase() ? "bg-[#40E0D0] text-white" : ""
                    }`}
                    onClick={() => handleSelect("type", option.toLowerCase())}
                  >
                    {option}
                  </li>
                ))}
              </ul>
            )}
          </div>
          <div className="col-span-2">
            <label className='block text-black sm:text-sm text-xs font-medium mb-1.5'>
              Set the rate values
            </label>
            <div className="grid sm:grid-cols-2  gap-4">
              <div>
                <div onClick={() => setSelectedOption("amount")} className={`w-full flex justify-between sm:px-4 px-2 sm:py-2.5 py-2 border rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs cursor-pointer ${ selectedOption === "amount" ? "text-black bg-[#40E0D0] border-[#40E0D0]" : "text-black/50 bg-white border-black/50" }`}
                >
                  <input
                    type="radio"
                    value="amount"
                    checked={selectedOption === "amount"}
                    onChange={() => setSelectedOption("amount")}
                    style={{ display: "none" }}
                  />
                  <span>
                    Amount
                  </span>
                  <Image className={`${ selectedOption === "amount" ? "block" : "hidden" }`} src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/checked.svg`} width={20} height={20}/>
                </div>
              </div>
              <div>
                <div
                  onClick={() => setSelectedOption("percentage")} className={`w-full flex justify-between sm:px-4 px-2 sm:py-2.5 py-2 border rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs cursor-pointer ${ selectedOption === "percentage" ? "text-black bg-[#40E0D0] border-[#40E0D0]" : "text-black/50 bg-white border-black/50" }`}
                >
                  <input
                    type="radio"
                    value="percentage"
                    checked={selectedOption === "percentage"}
                    onChange={() => setSelectedOption("percentage")}
                    style={{ display: "none" }}
                  />
                  <span>
                    Percentage
                  </span>
                  <Image className={`${ selectedOption === "percentage" ? "block" : "hidden" }`} src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/checked.svg`} width={20} height={20}/>
                </div>

                {selectedOption === "percentage" && (
                  <div className="sm:mt-5 mt-3">
                    <label className='block text-black sm:text-sm text-xs font-medium mb-1.5'>
                      Percentage discount off the standard rate
                    </label>
                    <div
                      className='w-full flex justify-between sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500'
                    >
                      <button
                        onClick={handleDecrease} style={{ cursor: "pointer", }} disabled={percentage <= 0}
                      >
                        <Minus color="#00000080" className="w-[20px] h-[20px]"/>
                      </button>
                      <input className="text-center w-full"
                        type="text"
                        value={`${percentage}%`}
                        readOnly
                      />

                      <button
                        // eslint-disable-next-line react/no-unknown-property
                        onClick={handleIncrease} style={{ cursor: "pointer", }} isabled={percentage >= 100}
                      >
                       <Plus color="#00000080" className="w-[20px] h-[20px]"/>
                      </button>
                    </div>
                    <div className="grid grid-cols-3 font-medium text-sm text-center mt-6">
                      <div>
                          <p className="font-medium sm:text-sm text-xs">Thu</p>
                          <p className="font-medium sm:text-sm text-xs">May 04</p>
                      </div>
                      <div>
                          <p className="font-medium sm:text-sm text-xs">Fri</p>
                          <p className="font-medium sm:text-sm text-xs">May 05</p>
                      </div>
                      <div>
                          <p className="font-medium sm:text-sm text-xs">Sat</p>
                          <p className="font-medium sm:text-sm text-xs">May 06</p>
                      </div>
                    </div>
                    <div className="grid grid-cols-3 font-medium text-sm text-center sm:mt-6 mt-4 gap-1.5">
                      <label className='block text-black sm:text-sm text-xs font-medium mb-1.5 col-span-3 text-left'>
                        Percentage discount off the standard rate
                      </label>
                      <input
                        type='text'
                        name=''
                        className='w-full sm:px-2.5 px-1.5 sm:py-2.5 py-2 border border-black/50 font-normal rounded-lg focus:outline-none focus:bg-[#40E0D033] focus:text-black cursor-pointer  text-black sm:text-sm text-xs placeholder:text-gray-500'
                        placeholder='10000.00' readOnly
                      />
                      <input
                        type='text'
                        name=''
                        className='w-full sm:px-2.5 px-1.5 sm:py-2.5 py-2 border border-black/50 font-normal rounded-lg focus:outline-none focus:bg-[#40E0D033] focus:text-black cursor-pointer  text-black sm:text-sm text-xs placeholder:text-gray-500'
                        placeholder='15000.00' readOnly
                      />
                      <input
                        type='text'
                        name=''
                        className='w-full sm:px-2.5 px-1.5 sm:py-2.5 py-2 border border-black/50 font-normal rounded-lg focus:outline-none focus:bg-[#40E0D033] focus:text-black cursor-pointer  text-black sm:text-sm text-xs placeholder:text-gray-500'
                        placeholder='20000.00' readOnly
                      />
                    </div>
                    <div className="grid grid-cols-3 font-medium text-sm text-center sm:mt-6 mt-4 gap-1.5">
                      <label className='block text-black sm:text-sm text-xs font-medium mb-1.5 col-span-3 text-left'>
                        Non- refundable Rate
                      </label>
                      <input
                        type='text'
                        name=''
                        className='w-full sm:px-2.5 px-1.5 sm:py-2.5 py-2 border border-black/50 font-normal rounded-lg focus:outline-none focus:bg-[#40E0D033] focus:text-black cursor-pointer  text-black sm:text-sm text-xs placeholder:text-gray-500'
                        placeholder='900.00' readOnly
                      />
                      <input
                        type='text'
                        name=''
                        className='w-full sm:px-2.5 px-1.5 sm:py-2.5 py-2 border border-black/50 font-normal rounded-lg focus:outline-none focus:bg-[#40E0D033] focus:text-black cursor-pointer  text-black sm:text-sm text-xs placeholder:text-gray-500'
                        placeholder='13500.00' readOnly
                      />
                      <input
                        type='text'
                        name=''
                        className='w-full sm:px-2.5 px-1.5 sm:py-2.5 py-2 border border-black/50 font-normal rounded-lg focus:outline-none focus:bg-[#40E0D033] focus:text-black cursor-pointer  text-black sm:text-sm text-xs placeholder:text-gray-500'
                        placeholder='13500.00' readOnly
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div>
            <label
              className="block text-black sm:text-sm text-xs font-medium mb-1.5"
              htmlFor="weekday-rate"
            >
              Weekday rate
            </label>
            <div className="flex items-center w-full sm:px-3 px-2 py-2 border border-black/50 rounded-lg focus-within:ring-1 focus-within:ring-teal-500">
              <select
                name="currency"
                value={addroom.currency}
                onChange={handleChange}
                className="sm:w-20 w-16 focus:outline-none text-black sm:text-sm text-xs placeholder:text-gray-500"
              >
                <option disabled value="">
                  EUR
                </option>
                {currencies.map((currency) => (
                  <option key={currency.code} value={currency.code}>
                    {currency?.code}
                  </option>
                ))}
              </select>
              <span className="mr-2 text-gray-300">|</span>
              <input
                type="text"
                name=""
                placeholder="Rate"
                value=""
                className="w-full bg-transparent focus:outline-none text-black sm:text-sm text-xs placeholder:text-gray-500"
              />
            </div>
          </div>

          <div>
            <label
              className="block text-black sm:text-sm text-xs font-medium mb-1.5"
              htmlFor="weekday-rate"
            >
              Weekend rate
            </label>
            <div className="flex items-center w-full sm:px-3 px-2 py-2 border border-black/50 rounded-lg focus-within:ring-1 focus-within:ring-teal-500">
              <select
                name="currency"
                value={addroom.currency}
                onChange={handleChange}
                className="sm:w-20 w-16 focus:outline-none text-black sm:text-sm text-xs placeholder:text-gray-500"
              >
                <option disabled value="">
                  EUR
                </option>
                {currencies.map((currency) => (
                  <option key={currency.code} value={currency.code}>
                    {currency?.code}
                  </option>
                ))}
              </select>
              <span className="mr-2 text-gray-300">|</span>
              <input
                type="text"
                name="Rate"
                placeholder="Rate"
                value={addroom.weekendRate}
                onChange={handleChange}
                className="w-full bg-transparent focus:outline-none text-black sm:text-sm text-xs placeholder:text-gray-500"
              />
            </div>
          </div>

        </div>

        <div className='flex items-center justify-between w-full sm:my-14 my-7 gap-4 col-span-2 py-4 bg-white/60 sticky bottom-0 backdrop-blur-sm'>
          <button
            className='hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm'
            onClick={closeeditModal}
          >
            Cancel
          </button>
          <button
            className='bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm'
          >
            Save Changes
          </button>
        </div>
      </div>
    </>
  );
};

export default EditRate;
