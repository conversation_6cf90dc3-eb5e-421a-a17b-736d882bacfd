// import React, { useEffect } from "react";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import dynamic from "next/dynamic";
import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";
// import ReactApex<PERSON>hart from 'react-apexcharts';
import DonutChart from "@/components/DonutChart";
import AboutSkeleton from "@/components/AboutSkeleton";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay } from "swiper/modules";
import {
  TbGridDots,
  TbPhotoAi,
  TbMailFilled,
  TbCircleDashed,
} from "react-icons/tb";
import {
  FaUsersGear,
  FaIdCard,
  FaLocationDot,
  FaWhatsapp,
} from "react-icons/fa6";
import { FaCar, FaFacebook, FaLinkedin } from "react-icons/fa";
import { LuTvMinimalPlay } from "react-icons/lu";
import {
  BsFillCalendarE<PERSON><PERSON>ill,
  BsInstagram,
  BsTwitterX,
} from "react-icons/bs";
import { HiDocumentCheck } from "react-icons/hi2";
import { FiUser } from "react-icons/fi";
import Head from "next/head";
import { getContentSchemaOtherPages } from "@/lib/schema/contentSchemaOtherPages";

const ReactApexChart = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});

const MotionImage = motion(Image);

const logos = [
  {
    src: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/SiteMinder02.webp`,
    alt: "Siteminder",
    className: "",
  },
  {
    src: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Allocator02.webp`,
    alt: "yanolja cloud solution",
    className: "",
  },
  {
    src: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Ezee01.webp`,
    alt: "Eze",
    className: "",
  },
  {
    src: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Cloudbeds01.webp`,
    alt: "Cloudbeds",
    className: "",
  },
  {
    src: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/./Yanolja01.webp`,
    alt: "yanolja",
    className: "",
  },
];

const keyfetures = [
  {
    title: "MixMate",
    desc: "A social feature for travelers to connect and plan activities together",
    icon: <FaUsersGear color='black' size={24} />,
  },
  {
    title: "MixSplit",
    desc: "Splitting bills for bookings, food, and transport made easy",
    icon: <FaIdCard color='black' size={24} />,
  },
  {
    title: "MixRide",
    desc: "A feature that enables ride-sharing between travelers.",
    icon: <FaCar color='black' size={24} />,
  },
  {
    title: "MixCreators",
    desc: "Collaborate with influencers to promote your hostel.",
    icon: <LuTvMinimalPlay color='black' size={24} />,
  },
  {
    title: "Events",
    desc: "Host events and attract guests by promoting city or hostel-specific activities.",
    icon: <BsFillCalendarEventFill color='black' size={24} />,
  },
  {
    title: "AI Noticeboard",
    desc: "Automated updates and notifications to guests about events or announcements.",
    icon: <TbPhotoAi color='black' size={24} />,
  },
  {
    title: "Web Check-in",
    desc: "Simplify your guests arrival process by allowing them to check in online before they arrive, ensuring a smooth and hassle-free experience for  guests and staff.",
    icon: <HiDocumentCheck color='black' size={24} />,
  },
];

const socialMedia = [
  {
    id: 1,
    name: "Instagram",
    icon: <BsInstagram className='text-black h-6 w-6' />,
    url: "https://www.instagram.com/mixdorms/",
    disc: "Instagram - MixDorms",
  },
  {
    id: 2,
    name: "Twitter",
    icon: <BsTwitterX className='text-black h-6 w-6' />,
    url: "https://x.com/mixdorm",
    disc: "Twitter - MixDorm",
  },

  {
    id: 3,
    name: "Facebook",
    icon: <FaFacebook className='text-black h-6 w-6' />,
    url: "https://www.facebook.com/profile.php?id=61572989814393",
    disc: "Facebook - MixDorm",
  },
  {
    id: 4,
    name: "Linkedin",
    icon: <FaLinkedin className='text-black h-6 w-6' />,
    url: "https://www.linkedin.com/company/mixdorm/",
    disc: "Linkedin - MixDorm",
  },
  {
    id: 5,
    name: "Whatsapp",
    icon: <FaWhatsapp className='text-black h-6 w-6' />,
    url: "",
    disc: "+91-8491933663",
  },
  {
    id: 6,
    name: "Location",
    icon: <FaLocationDot className='text-black h-6 w-6' />,
    url: "",
    disc: "Indore, India",
  },
  {
    id: 7,
    name: "Mail",
    icon: <TbMailFilled className='text-black h-6 w-6' />,
    url: "",
    disc: "<EMAIL>",
  },
];

const containerVariants = {
  visible: {
    transition: {
      staggerChildren: 0.25, // gap between donut animations
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, x: -50 },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut",
    },
  },
};

const initialData = [
  { label: "India", value: 21, users: 10000 },
  { label: "Spain", value: 27, users: 15000 },
  { label: "Brazil", value: 11, users: 12000 },
  { label: "Thailand", value: 31, users: 11000 },
];

const Page = () => {
  const series = [20, 15, 10, 9, 8, 7, 6, 5, 4, 16]; // Sample data per country

  const [openIndex, setOpenIndex] = useState(0);

  const handleToggle = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  const options = {
    chart: {
      type: "pie",
      toolbar: { show: false },
      animations: {
        enabled: true,
        easing: "easeinout",
        speed: 800,
        animateGradually: {
          enabled: true,
          delay: 150,
        },
        dynamicAnimation: {
          enabled: true,
          speed: 350,
        },
      },
      dropShadow: {
        enabled: true,
        top: 2,
        left: 2,
        blur: 6,
        color: "#000",
        opacity: 0.25,
      },
    },
    labels: [
      "India",
      "Thailand",
      "Indonesia",
      "Spain",
      "Mexico",
      "Italy",
      "Brazil",
      "Colombia",
      "Vietnam",
      "Rest of World",
    ],
    colors: [
      "#0D3C3E",
      "#14524D",
      "#1C6758",
      "#238D65",
      "#27A277",
      "#32B68C",
      "#47D7B4",
      "#5DE6C1",
      "#7DEFD2",
      "#A3F6E4",
    ],
    fill: {
      type: "gradient",
    },
    stroke: {
      show: true,
      width: 2,
      colors: ["#fff"],
    },
    legend: {
      position: "right",
      fontSize: "12px",
      labels: {
        colors: "#fff",
      },
      markers: {
        width: 12,
        height: 12,
        radius: 2,
      },
    },
    dataLabels: {
      style: {
        colors: ["#fff"],
      },
    },
    responsive: [
      {
        breakpoint: 1024,
        options: {
          chart: { width: 450 },
          legend: { position: "bottom" },
        },
      },
      {
        breakpoint: 640,
        options: {
          chart: { width: 400 },
          legend: {
            position: "bottom",
            fontSize: "10px",
          },
        },
      },
      {
        breakpoint: 420,
        options: {
          chart: { width: 350 },
        },
      },
    ],
  };

  const [chartData, setChartData] = useState(initialData);

  useEffect(() => {
    const today = new Date().toISOString().split("T")[0]; // Format: YYYY-MM-DD
    const lastUpdatedDate = localStorage.getItem("chartLastUpdatedDate");

    let storedData = localStorage.getItem("dailyChartData");

    if (!storedData || lastUpdatedDate !== today) {
      // New day or no data — generate new chart values
      const updated = initialData.map((item) => {
        const increment = Math.floor(Math.random() * 2) + 1; // 1 or 2%
        let newValue = item.value + increment;
        if (newValue > 100) newValue = 100;
        return { ...item, value: newValue };
      });

      setChartData(updated);
      localStorage.setItem("dailyChartData", JSON.stringify(updated));
      localStorage.setItem("chartLastUpdatedDate", today);
    } else {
      // Use saved data for today
      setChartData(JSON.parse(storedData));
    }
  }, []);

  const steps = [
    {
      title: "1. Email Us to Request :",
      content: "We will provide you OTA Property Credentials.",
      highlight: "<EMAIL>",
    },
    {
      title: "2. Add Subject Line :",
      content: "Subject: Mixdorm Integration Request – [Your Hostel Name]",
    },
    {
      title: "3. CC Your Channel Manager:",
      content: "Copy the right partner based on your PMS:",
      highlightAI: true,
      list: [
        {
          text: "If using eZee Absolute",
          email: "<EMAIL>",
        },
        { text: "If using Cloudbeds", email: "<EMAIL>" },
        { text: "(Other PMS ? We’ll guide you)" },
      ],
    },
    {
      title: "4. Email Body Template:",
      contentTop:
        "Dear Team,<br><br>We would like to connect our hostel with Mixdorm for booking integration.<br>Below are the details:",
      list: [
        { text: "Hostel Name: [Your Property Name]" },
        { text: "PMS/Hotel Code: [Your Code]" },
        { text: "Location: [City, Country]" },
      ],
      contentBottom:
        "Kindly process the integration.<br><br>Regards,<br>[Your Name / Hostel Owner]",
    },
    {
      title: "5. Await Confirmation:",
      content:
        "Our team or your PMS partner will confirm once connected and rooms are mapped.",
    },
    {
      title: "6. Go Live!:",
      contentTop:
        "- Your hostel will go live on Mixdorm for global travelers to book. <br> - Start receiving backpacker bookings!",
    },
  ];

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 2000); // 2 seconds
    return () => clearTimeout(timer);
  }, []);

  const contentSchema = getContentSchemaOtherPages({
    title: "List Your Hostel - Partner With MixDorm",
    description:
      "Join Mixdorm and Connect with 50K+ Gen Z Travelers. Boost Bookings, Grow Your Brand, and Get Full Platform Access.",
    authorName: "Mixdorm",
    datePublished: "2023-07-17",
  });

  return (
    <>
      <Head>
        <title>List Your Hostel - Partner With MixDorm</title>
        <meta
          name='description'
          content='Join Mixdorm and Connect with 50K+ Gen Z Travelers. Boost Bookings, Grow Your Brand, and Get Full Platform Access.'
        />
        {contentSchema && (
          <script
            type='application/ld+json'
            dangerouslySetInnerHTML={{ __html: JSON.stringify(contentSchema) }}
          />
        )}
      </Head>
      <div className='bg-black rounded-full border-2 border-white p-2'></div>
      <div className='w-full flex z-50 bg-white bg-opacity-10 shadow-md backdrop-blur-sm fixed top-0'>
        <div className='relative z-50 flex items-center justify-between py-4 container'>
          <Link href='/' rel='canonical' prefetch={false}>
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Mixdorm-white-D.svg`}
              width={155}
              height={40}
              alt='Mixdorm'
              title='Mixdorm'
              className='max-w-[155px] md:max-w-[155px] md:max-h-24 relative z-50 w-fit object-contain bg-blend-color-burn cursor-pointer hover:scale-95 duration-500 ease-in-out'
              loading='lazy'
            />
          </Link>
          <Link
            href='/owner/login'
            rel='canonical'
            prefetch={false}
            target='_blank'
          >
            <button className='bg-primary-blue text-black flex gap-2 text-sm font-semibold py-2.5 px-5 rounded-full hover:bg-white uppercase'>
              {" "}
              <FiUser size={20} /> Hostel Login{" "}
            </button>
          </Link>
        </div>
      </div>
      <div className='relative bg-cover flex items-center justify-center h-screen overflow-hidden'>
        <Image
          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/home-page-new2.webp`}
          alt='Initial Slide'
          fill
          priority
          fetchPriority='high'
          quality={75}
          sizes='100vw'
          className='object-cover absolute top-0 left-0 w-full h-screen z-0 animate-zoomBounce'
        />
        <div
          className='absolute inset-0 bg-black/85 w-full h-screen'
          style={{ zIndex: 2 }}
        />
        <div className='z-20'>
          <motion.h1
            initial={{ y: 100, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className='text-white text-center font-roboto mb-5 font-semibold'
          >
            &quot; The Future of{" "}
            <span className='text-primary-blue font-mashiny font-normal'>
              Hostel
            </span>{" "}
            Booking <br />
            Intelligent. Social. Seamless. &quot;
          </motion.h1>
          <motion.p
            initial={{ y: 100, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
            className='text-white text-sm text-center mb-8'
          >
            Tap into the growing community of 50,000+ young solo travelers
            worldwide
          </motion.p>
          <motion.div
            initial={{ y: 100, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, ease: "easeOut", delay: 0.5 }}
            className='flex items-center justify-center gap-5'
          >
            <a href='mailto:<EMAIL>'>
              <button className='bg-primary-blue text-black font-semibold text-base py-3 px-6 rounded-full border border-primary-blue capitalize hover:bg-transparent hover:text-primary-blue'>
                {" "}
                Contact Us{" "}
              </button>
            </a>
            <Link href='#get-started' scroll={true}>
              <button className='hover:bg-white hover:text-black font-semibold text-base py-3 px-6 rounded-full border border-white capitalize text-white bg-transparent'>
                {" "}
                List Your Property{" "}
              </button>
            </Link>
          </motion.div>
        </div>
      </div>
      {loading ? (
        <AboutSkeleton /> // Full skeleton (including hero, about, stats, etc.)
      ) : (
        <>
          <section className='relative bg-[#282828] z-30'>
            {/* Wave Shape at Top */}
            <div className='absolute -top-32 w-full overflow-hidden leading-none rotate-180'>
              <svg
                className='w-full h-32 -rotate-180'
                viewBox='0 0 1440 320'
                preserveAspectRatio='none'
                xmlns='http://www.w3.org/2000/svg'
              >
                <path
                  d='M0,96L80,122.7C160,149,320,203,480,202.7C640,203,800,149,960,144C1120,139,1280,181,1360,202.7L1440,224'
                  fill='none'
                  stroke='#40e0d0'
                  strokeWidth='5'
                />

                <path
                  d='M0,96L80,122.7C160,149,320,203,480,202.7C640,203,800,149,960,144C1120,139,1280,181,1360,202.7L1440,224L1440,320L0,320Z'
                  fill='#282828'
                />
              </svg>
            </div>
            {/* Your Section Content */}
            <div className='container relative pt-8 pb-10 overflow-x-hidden'>
              <div className='grid grid-cols-2 absolute top-10 rotate-45 opacity-10'>
                <TbGridDots size={45} color='#40e0d0' />
                <TbGridDots className='-ml-1' size={45} color='#40e0d0' />
                <TbGridDots className='-mt-1' size={45} color='#40e0d0' />
                <TbGridDots className='-mt-1 -ml-1' size={45} color='#40e0d0' />
              </div>
              <div className='grid grid-cols-3 absolute bottom-10 right-0 opacity-10'>
                <TbGridDots size={45} color='#40e0d0' />
                <TbGridDots className='-ml-1' size={45} color='#40e0d0' />
                <TbGridDots className='-ml-2' size={45} color='#40e0d0' />
                <TbGridDots className='-mt-1' size={45} color='#40e0d0' />
                <TbGridDots className='-mt-1 -ml-1' size={45} color='#40e0d0' />
                <TbGridDots className='-mt-1 -ml-2' size={45} color='#40e0d0' />
              </div>
              <motion.h2
                initial={{ y: 60, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
                className='sm:text-4xl text-3xl font-bold text-center text-white font-manrope'
              >
                Welcome to <span className='text-primary-blue'>Mix</span>Dorm
              </motion.h2>
              <motion.p
                initial={{ y: 60, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
                className='text-center text-white/50 mt-4 text-sm md:w-[70%] mx-auto'
              >
                Discover why thousands of young solo travelers choose Mixdorm
                for their adventures around the world.
              </motion.p>
              <div className='grid lg:grid-cols-2 items-center lg:gap-10 gap-4 md:pb-12 pb-8 sm:pt-10 pt-5'>
                <motion.div
                  className='grid grid-cols-2 sm:gap-4 gap-3 justify-items-center'
                  initial='hidden'
                  whileInView='visible'
                  viewport={{ once: false }}
                  variants={containerVariants}
                >
                  {chartData.map((item, index) => (
                    <motion.div
                      key={index}
                      variants={itemVariants}
                      initial='hidden'
                      whileInView='visible'
                      viewport={{ once: false }}
                    >
                      <DonutChart
                        label={item.label}
                        value={item.value}
                        users={item.users}
                      />
                    </motion.div>
                  ))}
                </motion.div>
                <motion.div
                  className='w-full max-w-[550px] mx-auto lg:mt-0 mt-10 text-center about-mixdorm'
                  initial={{ opacity: 0, x: 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 1, ease: "easeOut" }}
                  viewport={{ once: false }}
                >
                  <ReactApexChart
                    options={options}
                    series={series}
                    type='pie'
                    width='100%'
                  />
                </motion.div>
              </div>
            </div>
          </section>
          <section className='relative bg-[#000] pt-12 pb-8 overflow-x-hidden'>
            <div className='container relative'>
              <motion.h2
                initial={{ y: 60, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
                className='sm:text-4xl text-3xl font-bold text-center text-white font-manrope'
              >
                <span className='text-primary-blue'>Who</span> we are ?
              </motion.h2>
              <motion.p
                initial={{ y: 60, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
                className='text-center text-white/50 mt-4 text-sm md:w-[70%] mx-auto'
              >
                MixDorm is a platform where hostels meet socialdriven travel. We
                combine the best features of OTAs with the vibrancy of a social
                app. Think of us as the place where bookings meet community, and
                where hostels become more than just a place to stay.
              </motion.p>
              <div className='grid lg:grid-cols-3 md:grid-cols-2 grid-cols-1 items-center lg:gap-12 gap-4 mt-10'>
                <motion.ol
                  initial={{ x: -60, opacity: 0 }}
                  whileInView={{ x: 0, opacity: 1 }}
                  transition={{ duration: 0.8, ease: "easeOut" }}
                  className='text-white/80 list-decimal lg:col-span-2 pl-5 pr-3'
                >
                  <li className='lg:mb-4 mb-2 text-justify lg:text-base text-sm'>
                    <p className='font-semibold lg:text-lg text-base'>
                      A Next-Gen OTA Built for Shared Stays
                    </p>
                    MixDorm is a booking platform exclusively designed for
                    hostels, dorms, and homestays - not just hotels. We
                    understand your business model, guest type, and challenges.
                  </li>
                  <li className='lg:mb-4 mb-2 text-justify lg:text-base text-sm'>
                    <p className='font-semibold lg:text-lg text-base'>
                      {" "}
                      Focused on Gen Z & Solo Travelers
                    </p>
                    Our app is tailored for Gen Z backpackers, digital nomads,
                    and budget explorers - the core audience that fills your
                    beds and energizes your community.
                  </li>
                  <li className='lg:mb-4 mb-2 text-justify lg:text-base text-sm'>
                    <p className='font-semibold lg:text-lg text-base'>
                      {" "}
                      Revenue-First Policies for Hostel Owners
                    </p>
                    Unlike traditional OTAs, we work for your profit - with 100%
                    advance payments, strict no-show rules, and zero tolerance
                    for revenue leakage.
                  </li>
                  <li className='lg:mb-4 mb-2 text-justify lg:text-base text-sm'>
                    <p className='font-semibold lg:text-lg text-base'>
                      {" "}
                      Global Platform, Local Partnership
                    </p>
                    We&apos;re live in over 5,700+ properties across 15+
                    countries, yet we support every hostel personally like a
                    local partner - not just a listing.
                  </li>
                  <li className='lg:mb-4 mb-2 text-justify lg:text-base text-sm'>
                    <p className='font-semibold lg:text-lg text-base'>
                      {" "}
                      More Than a Booking Site{" "}
                    </p>
                    A Growth Tool From events and ride-sharing to digital
                    marketing and Al-powered visibility, MixDorm is your
                    one-stop solution to grow bookings, build brand value, and
                    create community.
                  </li>
                </motion.ol>
                <div className='md:flex sm:hidden flex items-center justify-center'>
                  <MotionImage
                    initial={{ x: 60, opacity: 0 }}
                    whileInView={{ x: 0, opacity: 1 }}
                    transition={{ duration: 0.8, ease: "easeOut" }}
                    src={"/about-mixdorm.webp"}
                    width={591}
                    height={681}
                    alt='BgImg'
                    className='rounded-4xl h-full sm:w-full xs:w-[70%] w-full'
                    loading='lazy'
                  />
                </div>
              </div>
            </div>
          </section>
          <section className='relative bg-[#282828]' id='get-started'>
            {/* Wave Shape at Top */}
            <div className='w-full overflow-hidden leading-none'>
              <svg
                className='w-full h-32 -rotate-180'
                viewBox='0 0 1440 320'
                preserveAspectRatio='none'
                xmlns='http://www.w3.org/2000/svg'
              >
                <path
                  d='M0,96L80,122.7C160,149,320,203,480,202.7C640,203,800,149,960,144C1120,139,1280,181,1360,202.7L1440,224'
                  fill='none'
                  stroke='#40e0d0'
                  strokeWidth='5'
                />
                <path
                  d='M0,96L80,122.7C160,149,320,203,480,202.7C640,203,800,149,960,144C1120,139,1280,181,1360,202.7L1440,224L1440,320L0,320Z'
                  fill='#000'
                />
              </svg>
            </div>
            <div className='container pb-10'>
              <motion.h2
                initial={{ y: 60, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
                className='sm:text-4xl text-3xl text-center font-bold text-white font-manrope'
              >
                {" "}
                Get Started <span className='text-primary-blue'>
                  Today
                </span>{" "}
              </motion.h2>
              <motion.p
                initial={{ y: 60, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
                className='text-center text-white/50 mt-4 text-sm md:w-[70%] mx-auto'
              >
                Ready to join MixDorm and take your hostel to the next level?
              </motion.p>
              <div className='mt-10'>
                <motion.div
                  className='text-white w-full grid md:grid-cols-2 md:gap-4 gap-2'
                  initial={{ y: 60, opacity: 0 }}
                  whileInView={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.8, ease: "easeOut" }}
                >
                  {[0, 1].map((col) => (
                    <div key={col} className='space-y-4'>
                      {steps.slice(col * 3, col * 3 + 3).map((step, i) => {
                        const globalIndex = col * 3 + i;
                        return (
                          <motion.div
                            key={globalIndex}
                            className='bg-black/60 rounded-lg md:p-4 p-3 border border-white/80 text-white shadow-md hover:shadow:sm transition'
                          >
                            <button
                              onClick={() => handleToggle(globalIndex)}
                              className='w-full text-left font-bold text-primary-blue md:text-lg text-base flex justify-between items-center'
                            >
                              <span>
                                {step.title}{" "}
                                {step.highlight && (
                                  <Link
                                    href='mailto:<EMAIL>'
                                    className='hover:text-white'
                                  >
                                    {" "}
                                    <EMAIL>{" "}
                                  </Link>
                                )}
                              </span>
                              <span className='ml-2'>
                                {openIndex === globalIndex ? "−" : "+"}
                              </span>
                            </button>

                            {/* Accordion Body */}
                            <AnimatePresence initial={false}>
                              {openIndex === globalIndex && (
                                <motion.div
                                  key='content'
                                  initial={{ height: 0, opacity: 0 }}
                                  animate={{ height: "auto", opacity: 1 }}
                                  exit={{ height: 0, opacity: 0 }}
                                  transition={{
                                    duration: 0.4,
                                    ease: "easeInOut",
                                  }}
                                  className='overflow-hidden px-4'
                                >
                                  {step.contentTop && (
                                    <p
                                      className='text-sm mt-1'
                                      dangerouslySetInnerHTML={{
                                        __html: step.contentTop,
                                      }}
                                    />
                                  )}

                                  {step.content && (
                                    <p className='text-sm mt-2'>
                                      {step.content
                                        .split("AI")
                                        .map((part, i, arr) =>
                                          i < arr.length - 1 ? (
                                            <span key={i}>
                                              {part}
                                              <span className='text-primary-blue'>
                                                AI
                                              </span>
                                            </span>
                                          ) : (
                                            part
                                          )
                                        )}
                                    </p>
                                  )}

                                  {step.list && (
                                    <ul className='text-sm mt-2 list-disc pl-5 space-y-1'>
                                      {step.list.map((item, i) => (
                                        <li className='text-sm' key={i}>
                                          {item.email ? (
                                            <>
                                              {item.text} →{" "}
                                              <a
                                                href={`mailto:${item.email}`}
                                                className='text-primary-blue underline hover:text-teal-600'
                                              >
                                                {item.email}
                                              </a>
                                            </>
                                          ) : (
                                            item.text
                                          )}
                                        </li>
                                      ))}
                                    </ul>
                                  )}

                                  {step.contentBottom && (
                                    <p
                                      className='text-sm mt-2'
                                      dangerouslySetInnerHTML={{
                                        __html: step.contentBottom,
                                      }}
                                    />
                                  )}
                                </motion.div>
                              )}
                            </AnimatePresence>
                          </motion.div>
                        );
                      })}
                    </div>
                  ))}
                </motion.div>
                <div className='pt-12'>
                  <motion.h2
                    initial={{ y: 60, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.8, ease: "easeOut" }}
                    className='text-2xl font-bold text-center text-white font-manrope mb-8'
                  >
                    Supported Channel Managers
                  </motion.h2>
                  <motion.div
                    initial={{ y: 60, opacity: 0 }}
                    whileInView={{ y: 0, opacity: 1 }}
                    transition={{ duration: 0.8, ease: "easeOut" }}
                  >
                    <Swiper
                      modules={[Autoplay]}
                      slidesPerView={2} // default for <640px
                      spaceBetween={8} // optional default space
                      breakpoints={{
                        640: { slidesPerView: 3, spaceBetween: 10 },
                        768: { slidesPerView: 4, spaceBetween: 15 },
                        1024: { slidesPerView: 5, spaceBetween: 30 },
                      }}
                      autoplay={{ delay: 2000, disableOnInteraction: false }}
                      loop={true}
                      className='w-full'
                    >
                      {logos.map((logo, index) => (
                        <SwiperSlide key={index}>
                          <div className='flex items-center justify-center bg-white border-4 border-white hover:border-primary-blue rounded-md md:p-4 p-2'>
                            <Image
                              src={logo.src}
                              alt={`Partner ${index + 1}`}
                              height={50}
                              width={150}
                              className='h-20 w-auto object-contain'
                            />
                          </div>
                        </SwiperSlide>
                      ))}
                    </Swiper>
                  </motion.div>
                </div>
                <div className='justify-center hidden'>
                  <MotionImage
                    initial={{ x: 60, opacity: 0 }}
                    whileInView={{ x: 0, opacity: 1 }}
                    transition={{ duration: 0.8, ease: "easeOut" }}
                    loading='lazy'
                    className='z-10 md:pt-20 pt-10 md:w-[280px] w-[180px]'
                    src={"/phn-mockup.webp"}
                    alt='Phone Mockup'
                    width={280}
                    height={320}
                  />
                  <MotionImage
                    initial={{ x: 60, opacity: 0 }}
                    whileInView={{ x: 0, opacity: 1 }}
                    transition={{ duration: 0.5, ease: "easeOut", delay: 0.3 }}
                    loading='lazy'
                    className='md:-ml-14 -ml-9 h-[80%] md:w-[280px] w-[180px]'
                    src={"/phn-mockup.webp"}
                    alt='Phone Mockup'
                    width={280}
                    height={320}
                  />
                </div>
              </div>
            </div>
          </section>
          <section className='relative bg-[#000] pt-12 pb-10'>
            <TbCircleDashed
              className='absolute -top-20 -left-14 text-white/10 z-20'
              size={150}
            />
            <div className='container pt-6'>
              <motion.h2
                initial={{ y: 60, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
                className='sm:text-4xl text-3xl font-bold text-center text-white font-manrope'
              >
                <span className='text-primary-blue'> Why</span> MixDorm Stands
                Out?
              </motion.h2>
              <div className='overflow-x-auto pt-10 pb-2'>
                <table className='min-w-[700px] bg-[#e8fffa] w-full text-left border border-black'>
                  <thead>
                    <tr className='bg-primary-blue text-black text-sm sm:text-lg'>
                      <th className='p-3 font-semibold'>Feature</th>
                      <th className='p-3 font-semibold'>Mixdorm</th>
                      <th className='p-3 font-semibold'>Hostelworld</th>
                      <th className='p-3 font-semibold'>Booking.com</th>
                    </tr>
                  </thead>
                  <tbody className='text-sm sm:text-base text-[#333]'>
                    <tr className='border border-primary-blue'>
                      <td className='p-3 font-semibold'>Social Features</td>
                      <td className='p-3'>
                        <strong>Yes</strong> – AI-powered social connections
                        through Mix Mate; traveler matching
                      </td>
                      <td className='p-3'>
                        Limited – Basic profile and reviews
                      </td>
                      <td className='p-3'>
                        Limited – Primarily focused on bookings
                      </td>
                    </tr>
                    <tr className='border border-primary-blue'>
                      <td className='p-3 font-semibold'>Event Promotion</td>
                      <td className='p-3'>
                        <strong>Yes</strong> – Curated hostel and city events;
                        join through the platform
                      </td>
                      <td className='p-3'>
                        Limited – Some hostel-specific events
                      </td>
                      <td className='p-3'>No event features</td>
                    </tr>
                    <tr className='border border-primary-blue'>
                      <td className='p-3 font-semibold'>
                        Community Engagement
                      </td>
                      <td className='p-3'>
                        <strong>Yes</strong> – In-app social activities, travel
                        buddy matching
                      </td>
                      <td className='p-3'>
                        Basic – Review-based user interaction
                      </td>
                      <td className='p-3'>No dedicated community features</td>
                    </tr>
                    <tr className='border border-primary-blue'>
                      <td className='p-3 font-semibold'>
                        AI-Driven Personalization
                      </td>
                      <td className='p-3'>
                        <strong>Yes</strong> – Customized hostel and activity
                        suggestions
                      </td>
                      <td className='p-3 font-bold'>No</td>
                      <td className='p-3 font-bold'>No</td>
                    </tr>
                    <tr className='border border-primary-blue'>
                      <td className='p-3 font-semibold'>Split Bill Feature</td>
                      <td className='p-3'>
                        <strong>Yes</strong> – Split bills with friends via Mix
                        Split Bill
                      </td>
                      <td className='p-3 font-bold'>No</td>
                      <td className='p-3 font-bold'>No</td>
                    </tr>
                    <tr className='border border-primary-blue'>
                      <td className='p-3 font-semibold'>Ride Sharing</td>
                      <td className='p-3'>
                        <strong>Yes</strong> – Connect with others for local
                        rides
                      </td>
                      <td className='p-3 font-bold'>No</td>
                      <td className='p-3 font-bold'>No</td>
                    </tr>
                    <tr className='border border-primary-blue'>
                      <td className='p-3 font-semibold'>
                        Diverse Hostel Listings
                      </td>
                      <td className='p-3'>
                        <strong>Yes</strong> – Extensive hostel network with
                        real-time updates
                      </td>
                      <td className='p-3'>
                        <strong>Yes</strong> – Focused on hostels and hotels
                      </td>
                      <td className='p-3'>
                        <strong>Yes</strong> – Includes hotels and apartments
                      </td>
                    </tr>
                    <tr className='border border-primary-blue'>
                      <td className='p-3 font-semibold'>
                        User-Friendly Interface
                      </td>
                      <td className='p-3'>
                        <strong>Yes</strong> – Traveler-centric with intuitive
                        design
                      </td>
                      <td className='p-3 font-bold'>Not focused</td>
                      <td className='p-3'>
                        <strong>Yes</strong> – Broad accommodations
                      </td>
                    </tr>
                    <tr className='border border-primary-blue'>
                      <td className='p-3 font-semibold'>
                        Pricing Transparency
                      </td>
                      <td className='p-3'>
                        <strong>Yes</strong> – No hidden fees, allin-one pricing
                      </td>
                      <td className='p-3'>
                        <strong>No</strong> – High Commission
                      </td>
                      <td className='p-3'>
                        <strong>Yes</strong> – But may have hidden costs
                      </td>
                    </tr>
                    <tr className='border border-primary-blue'>
                      <td className='p-3 font-semibold'>Web Check-in</td>
                      <td className='p-3'>
                        <strong>Yes</strong> – Hostel check-in from anywhere
                      </td>
                      <td className='p-3'>
                        <strong>No</strong>
                      </td>
                      <td className='p-3'>
                        <strong>No</strong>
                      </td>
                    </tr>
                    <tr className='border border-primary-blue'>
                      <td className='p-3 font-semibold'>Loyalty and Rewards</td>
                      <td className='p-3'>
                        <strong>Yes</strong> – Exclusive offers for loyal users
                      </td>
                      <td className='p-3'>
                        <strong>No</strong> – Limited rewards program{" "}
                      </td>
                      <td className='p-3'>
                        <strong>Yes</strong> – Reward program for frequent users
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div className='container hidden'>
              <motion.h2
                initial={{ y: 60, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
                className='sm:text-4xl text-3xl font-bold text-center text-white font-manrope'
              >
                Get Your{" "}
                <span className='text-primary-blue font-mashiny font-normal'>
                  Hostel
                </span>{" "}
                Live on Mixdorm
              </motion.h2>
              <motion.p
                initial={{ y: 60, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
                className='text-center text-white/50 mt-4 text-sm md:w-[70%] mx-auto'
              >
                Seamless integration with your current Channel Manager.
              </motion.p>
              <motion.div
                initial={{ y: 60, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
                className='grid grid-cols-1 md:grid-cols-3 gap-8 pt-10'
              >
                {/* Step 1 */}
                <div className='bg-white rounded-bl-4xl rounded-tr-4xl shadow-sm shadow-white p-6 text-center border-t-8 border-primary-blue transition-all duration-300 transform hover:shadow-md hover:-translate-y-1 hover:border-[#1d8f74]'>
                  <h3 className='text-xl font-bold mb-2'>Step 1</h3>
                  <p className='text-base'>
                    Send an email to
                    <br />
                    <a
                      href='mailto:<EMAIL>'
                      className='text-primary-blue font-semibold underline'
                    >
                      <EMAIL>
                    </a>
                  </p>
                </div>

                {/* Step 2 */}
                <div className='bg-white rounded-bl-4xl rounded-tr-4xl shadow-sm shadow-white p-6 text-center border-t-8 border-primary-blue transition-all duration-300 transform hover:shadow-md hover:-translate-y-1 hover:border-[#1d8f74]'>
                  <h3 className='text-xl font-bold mb-2'>Step 2</h3>
                  <p className='text-base'>
                    Mention the subject line:
                    <br />
                    <span className='italic text-sm'>
                      &quot;Request to Connect with Mixdorm via [Your Channel
                      Manager]&quot;
                    </span>
                  </p>
                </div>

                {/* Step 3 */}
                <div className='bg-white rounded-bl-4xl rounded-tr-4xl shadow-sm shadow-white p-6 text-center border-t-8 border-primary-blue transition-all duration-300 transform hover:shadow-md hover:-translate-y-1 hover:border-[#1d8f74]'>
                  <h3 className='text-xl font-bold mb-2'>Step 3</h3>
                  <p className='text-base'>CC the channel manager provider</p>
                </div>
              </motion.div>
              <div className='pt-12'>
                <motion.h2
                  initial={{ y: 60, opacity: 0 }}
                  whileInView={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.8, ease: "easeOut" }}
                  className='text-2xl font-bold text-center text-white font-manrope mb-8'
                >
                  Supported Channel Managers
                </motion.h2>
                <motion.div
                  initial={{ y: 60, opacity: 0 }}
                  whileInView={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.8, ease: "easeOut" }}
                >
                  <Swiper
                    modules={[Autoplay]}
                    spaceBetween={30}
                    slidesPerView={2}
                    breakpoints={{
                      640: { slidesPerView: 3 },
                      768: { slidesPerView: 4 },
                      1024: { slidesPerView: 5 },
                    }}
                    autoplay={{ delay: 2000, disableOnInteraction: false }}
                    loop={true}
                    className='w-full'
                  >
                    {logos.map((logo, index) => (
                      <SwiperSlide key={index}>
                        <div className='flex items-center justify-center bg-white border-4 border-white hover:border-primary-blue rounded-md p-4'>
                          <Image
                            src={logo.src}
                            alt={`Partner ${index + 1}`}
                            height={50}
                            width={150}
                            className='h-20 w-auto object-contain'
                          />
                        </div>
                      </SwiperSlide>
                    ))}
                  </Swiper>
                </motion.div>
              </div>
            </div>
          </section>
          <section className='bg-[#282828] pb-12 relative overflow-x-hidden'>
            <TbCircleDashed
              className='absolute top-0 -right-14 text-white/10 z-20'
              size={150}
            />
            <div className='w-full overflow-hidden leading-none'>
              <svg
                className='w-full h-32 -rotate-180'
                viewBox='0 0 1440 320'
                preserveAspectRatio='none'
                xmlns='http://www.w3.org/2000/svg'
              >
                <path
                  d='M0,96L80,122.7C160,149,320,203,480,202.7C640,203,800,149,960,144C1120,139,1280,181,1360,202.7L1440,224'
                  fill='none'
                  stroke='#40e0d0'
                  strokeWidth='5'
                />
                <path
                  d='M0,96L80,122.7C160,149,320,203,480,202.7C640,203,800,149,960,144C1120,139,1280,181,1360,202.7L1440,224L1440,320L0,320Z'
                  fill='#000'
                />
              </svg>
            </div>
            <div className='lg:w-[75%] w-[90%] relative mx-auto bg-black/50 text-white rounded-3xl p-6 md:p-10'>
              <motion.h2
                initial={{ y: 60, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
                className='text-2xl md:text-3xl font-bold mb-8 text-white font-manrope'
              >
                Key Features
              </motion.h2>
              <motion.div
                initial={{ y: 60, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
                className='space-y-2'
              >
                {/* Feature Item */}
                {keyfetures.map((item, i) => (
                  <div key={i} className='flex items-center gap-3'>
                    <div className='min-w-[48px] min-h-[48px] bg-primary-blue rounded-bl-md rounded-tr-md flex items-center justify-center p-2'>
                      {item.icon}
                    </div>
                    <div>
                      <h3 className='font-semibold text-white/90 md:text-base text-sm'>
                        {item.title}:
                        <span className='font-normal text-white/90 ml-1'>
                          {item.desc}
                        </span>
                      </h3>
                    </div>
                  </div>
                ))}
              </motion.div>
            </div>
          </section>
          <section className='relative bg-[#000] pt-16 pb-8'>
            <div className='container'>
              <motion.h2
                initial={{ y: 60, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
                className='sm:text-4xl text-3xl font-bold text-center text-white font-manrope'
              >
                Choose Your{" "}
                <span className='font-mashiny text-primary-blue font-normal'>
                  Onboarding
                </span>{" "}
                Plan
              </motion.h2>
              <motion.p
                initial={{ y: 60, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
                className='text-center text-white/50 mt-4 text-sm md:w-[70%] mx-auto'
              >
                Select the pricing model that works best for your business
                needs.
              </motion.p>
              <motion.div
                initial={{ y: 60, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
                className='xs:flex justify-center lg:gap-8 sm:gap-6 gap-3 mt-8'
              >
                {/* Flat Subscription Fee */}
                <div className='bg-white lg:w-[35.5%] xs:w-[50%] w-[90%] xs:mx-0 mx-auto shadow-lg rounded-2xl lg:p-8 p-5 hover:scale-105 transition xs:mb-0 mb-3'>
                  <h3 className='lg:text-2xl text-xl font-semibold text-gray-800 lg:mb-4 mb-2'>
                    Flat Subscription Fee
                  </h3>
                  <p className='text-sm text-gray-600 mb-4'>
                    Perfect for high-volume hosts
                  </p>

                  <ul className='text-gray-700 space-y-3 mb-6 text-base'>
                    <li className='sm:text-base text-sm'>
                      💵 $100 onboarding fee
                    </li>
                    <li className='sm:text-base text-sm'>
                      🆓 0% booking commission
                    </li>
                    <li className='sm:text-base text-sm'>
                      🔓 Full access to platform features
                    </li>
                  </ul>

                  <Link href='mailto:<EMAIL>'>
                    <button className='w-full bg-primary-blue hover:bg-black hover:text-white text-black/80 py-2.5 rounded-lg font-medium transition'>
                      Get Info
                    </button>
                  </Link>
                </div>

                {/* Commission-Based Model */}
                <div className='bg-white lg:w-[35.5%] xs:w-[50%] w-[90%] xs:mx-0 mx-auto shadow-lg rounded-2xl lg:p-8 p-5 hover:scale-105 transition'>
                  <h3 className='lg:text-2xl text-xl font-semibold text-gray-800 lg:mb-4 mb-2'>
                    Commission-Based Model
                  </h3>
                  <p className='text-sm text-gray-600 mb-4'>
                    Great for new or seasonal hosts
                  </p>

                  <ul className='text-gray-700 space-y-3 mb-6 text-base'>
                    <li className='sm:text-base text-sm'>
                      🆓 $0 onboarding fee
                    </li>
                    <li className='sm:text-base text-sm'>
                      💰 15% commission per booking
                    </li>
                    <li className='sm:text-base text-sm'>
                      🔓 Full access to platform features
                    </li>
                  </ul>

                  <Link href='mailto:<EMAIL>'>
                    <button className='w-full bg-primary-blue hover:bg-black hover:text-white text-black/80 py-2.5 rounded-lg font-medium transition'>
                      Get Info
                    </button>
                  </Link>
                </div>
              </motion.div>
            </div>
          </section>
          <section className='bg-[#282828] overflow-x-hidden'>
            <div className='w-full overflow-hidden leading-none'>
              <svg
                className='w-full h-32 -rotate-180'
                viewBox='0 0 1440 320'
                preserveAspectRatio='none'
                xmlns='http://www.w3.org/2000/svg'
              >
                <path
                  d='M0,96L80,122.7C160,149,320,203,480,202.7C640,203,800,149,960,144C1120,139,1280,181,1360,202.7L1440,224'
                  fill='none'
                  stroke='#40e0d0'
                  strokeWidth='5'
                />
                <path
                  d='M0,96L80,122.7C160,149,320,203,480,202.7C640,203,800,149,960,144C1120,139,1280,181,1360,202.7L1440,224L1440,320L0,320Z'
                  fill='#000'
                />
              </svg>
            </div>
          </section>
          <footer className='bg-[#282828] sm:pb-12 pb-10 relative'>
            <div className='grid grid-cols-2 absolute -top-10 rotate-45 opacity-10'>
              <TbGridDots size={45} color='#40e0d0' />
              <TbGridDots className='-ml-1' size={45} color='#40e0d0' />
              <TbGridDots className='-mt-1' size={45} color='#40e0d0' />
              <TbGridDots className='-mt-1 -ml-1' size={45} color='#40e0d0' />
            </div>
            <div className='container'>
              <Link href='/' rel='canonical' prefetch={false}>
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Mixdorm-white-D.svg`}
                  width={155}
                  height={40}
                  alt='Mixdorm'
                  title='Mixdorm'
                  className='sm:w-[250px] w-[200px] relative z-50 object-contain bg-blend-color-burn cursor-pointer hover:scale-95 duration-500 ease-in-out mx-auto text-center'
                  loading='lazy'
                />
              </Link>
              <div className='flex flex-wrap items-center sm:gap-7 gap-3 justify-center sm:mt-8 mt-5'>
                {socialMedia.map((item) => (
                  <div key={item.id}>
                    <Link
                      href={item.url}
                      aria-label={`Visit our ${item.name} page`}
                      target='_blank'
                    >
                      <div className='lg:w-[48px] lg:h-[48px] w-[40px] h-[40px] bg-primary-blue shadow-none hover:shadow-md hover:shadow-primary-blue/50 transition-all rounded-bl-md rounded-tr-md flex items-center justify-center lg:p-2 p-2.5 mx-auto'>
                        {item.icon}
                      </div>
                      <p className='text-white/85 text-sm mt-2 sm:block hidden'>
                        {item.disc}
                      </p>
                    </Link>
                  </div>
                ))}
              </div>
            </div>
          </footer>
        </>
      )}
    </>
  );
};

export default Page;
