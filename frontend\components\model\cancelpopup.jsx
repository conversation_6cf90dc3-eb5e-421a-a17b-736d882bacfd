/* eslint-disable react/no-unknown-property */
import React from "react";
import Modal from "@mui/material/Modal";
import { IoCloseCircleOutline } from "react-icons/io5";
import { cancelBookingApi } from "@/services/webflowServices";
import toast from "react-hot-toast";

const Cancelpopup = ({ openCancelpopup, handleCloseCancelpopup,bookingId,setIsUpdateData,setLoading }) => {
  const style = {
    position: "fixed",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: "100%",
    bgcolor: "background.paper",
    border: "2px solid #000",
    boxShadow: 24,
  };

  const handleCancel = async () => {
    setLoading(true);
    try {
      const payload = {
        id: bookingId,
      };
      const response = await cancelBookingApi(payload);
      console.log("response", response);
      if (response?.data?.status) {
        handleCloseCancelpopup();
        setIsUpdateData((prevState) => !prevState);
        toast.success(response?.data?.message)
      } else {
        toast.error("There is some issue cancel booking...");
        handleCloseCancelpopup();
      }
      setLoading(false);
    } catch (error) {
      console.error("Failed to delete account:", error);
      setLoading(false);
      // Optionally, display an error message to the user here
    }
  };

  return (
    <>
      <Modal
        open={openCancelpopup}
        onClose={handleCloseCancelpopup}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <div sx={style}>
          <div className="bg-white rounded-2xl max-w-[400px] mx-auto left-1/2 absolute w-[95%] top-1/2 -translate-y-1/2 -translate-x-1/2">
            <div className="flex items-center justify-between bg-gray-100 p-4 rounded-t-2xl">
              <h2 className="text-xl text-black font-bold">Cancel</h2>
              <button
                onClick={handleCloseCancelpopup}
                className="text-black text-2xl hover:text-primary-blue"
              >
                <IoCloseCircleOutline />
              </button>
            </div>
            <div className="md:p-6 p-4">
              <div className="relative ml-2 text-lg">
                <div>Are you sure you want to cancel?</div>
              </div>
              <div className="flex gap-1 py-2 justify-end">
              <button
                  className="bg-black text-white px-4 py-2 rounded-full text-sm hover:bg-primary-blue"
                onClick={handleCancel}
                >
                 Yes
                </button>
                <button
                  className="bg-tranparent border border-black text-black px-4 py-2 rounded-full text-sm hover:bg-red-600 hover:text-white hover:border-red-600"
                  onClick={handleCloseCancelpopup}
                >
                 No
                </button>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default Cancelpopup;
