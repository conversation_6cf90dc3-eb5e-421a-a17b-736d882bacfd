"use client";
import { Plus } from "lucide-react";
import React, { useState } from "react";
import { FaRegTrashCan } from "react-icons/fa6";
import { FiEye } from "react-icons/fi";
import { TfiPencilAlt } from "react-icons/tfi";
import Image from "next/image";
import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight,
} from "react-icons/md";
import Link from "next/link";

const FeaturedHostelPage = () => {
  // eslint-disable-next-line no-unused-vars
  const [OpenAddHostel, setOpenAddHostel] = useState("");

  const mobilehomepageData = [
    {
      id: 1,
      activityName: "Explore ruins",
      Feature: (
        <div>
          <p className="text-xs md:text-sm lg-text-sm text-gray-500 leading-6">
            <span className="text-black font-semibold">Key Features:</span>{" "}
            On-Site Bar, <br /> Rooftop Terrace, Dorms & <br /> Private Rooms
          </p>
        </div>
      ),
      amount: "$28/Person",
      status: (
        <p className="text-[#00B69B] bg-[#CCF0EB] text-sm py-2 px-2 md:px-2 lg:px-0 rounded-lg font-semibold text-center">
          Top rated
        </p>
      ),
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/activity.png`,
    },
    {
      id: 2,
      activityName:  "Explore ruins",
      Feature: (
        <div>
          <p className="text-xs md:text-sm lg-text-sm text-gray-500 leading-6">
            <span className="text-black font-semibold">Key Features:</span>{" "}
            On-Site Bar, <br /> Rooftop Terrace, Dorms & <br /> Private Rooms
          </p>
        </div>
      ),
      amount: "$28/Person",
      status: (
        <p className="text-[#00B69B] bg-[#CCF0EB] text-sm py-2 rounded-lg font-semibold text-center px-2 md:px-2 lg:px-0">
          Top rated
        </p>
      ),
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/activity.png`,
    },
    {
      id: 3,
      activityName:  "Explore ruins",
      Feature: (
        <div>
          <p className="text-xs md:text-sm lg-text-sm text-gray-500 leading-6">
            <span className="text-black font-semibold">Key Features:</span>{" "}
            On-Site Bar, <br /> Rooftop Terrace, Dorms & <br /> Private Rooms
          </p>
        </div>
      ),
      amount: "$28/Person",
      status: (
        <p className="text-[#00B69B] bg-[#CCF0EB] text-sm py-2 rounded-lg font-semibold text-center px-2 md:px-2 lg:px-0">
          Top rated
        </p>
      ),
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/activity.png`,
    },
    {
      id: 4,
      activityName:  "Explore ruins",
      Feature: (
        <div>
          <p className="text-xs md:text-sm lg-text-sm text-gray-500 leading-6">
            <span className="text-black font-semibold">Key Features:</span>{" "}
            On-Site Bar, <br /> Rooftop Terrace, Dorms & <br /> Private Rooms
          </p>
        </div>
      ),
      amount: "$28/Person",
      status: (
        <p className="text-[#00B69B] bg-[#CCF0EB] text-sm py-2 rounded-lg font-semibold text-center px-2 md:px-2 lg:px-0">
          Top rated
        </p>
      ),
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/activity.png`,
    },
    {
      id: 5,
      activityName: "Explore ruins",
      Feature: (
        <div>
          <p className="text-xs md:text-sm lg-text-sm text-gray-500 leading-6">
            <span className="text-black font-semibold">Key Features:</span>{" "}
            On-Site Bar, <br /> Rooftop Terrace, Dorms & <br /> Private Rooms
          </p>
        </div>
      ),
      amount: "$28/Person",
      status: (
        <p className="text-[#00B69B] bg-[#CCF0EB] text-sm py-2 rounded-lg font-semibold text-center px-2 md:px-2 lg:px-0">
          Top rated
        </p>
      ),
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/activity.png`,
    },
  ];

  return (
    <div className=" lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <div className="flex items-center justify-between w-full ">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Travel by Activity
        </h2>
        <div className="w-[40%] lg:w-[50%] gap-x-5 flex justify-end items-center">
          <button
            className={` px-2 md:px-4 lg:px-4 py-1.5 md:py-2 lg:py-2 text-sm font-normal text-white rounded relative flex justify-center items-center bg-sky-blue-650 `}
            type="button"
            onClick={() => setOpenAddHostel(true)}
          >
            <Link
              href={"/superadmin/dashboard/travel-activity-add"}
              className="flex text-sm font-medium font-poppins"
            >
              <Plus size={18} className="mr-1" /> Travel Activity
            </Link>
          </button>
        </div>
      </div>
      <div className="bg-white border-b border-l border-r rounded-xl dark:bg-black dark:border-none">
        <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border-b dark:border-none">
          <table className="min-w-full divide-y  bg-white rounded-xl divide-gray-200 dark:bg-black">
            <thead>
              <tr className="flex items-center justify-between w-full px-3">
                <th className="px-4 whitespace-nowrap py-6 bg-white  text-sm font-semibold font-poppins text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  ACTIVITY NAME
                </th>
                <th className="whitespace-nowrap pl-4 lg:pl-0 pr-8 md:pr-2 lg:pr-10  py-6  bg-white text-sm font-semibold font-poppins text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  ACTIVITY PHOTO
                </th>
                <th className="py-6 pr-6 bg-white text-sm font-semibold font-poppins text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  ACTION
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 border-x border-y dark:border-x-0 text-black/70 dark:text-[#757575]">
              {mobilehomepageData.map((mobile) => (
                <tr key={mobile.id} className="flex w-full items-center justify-between px-3">
                  <td className="pl-6 lg:pl-2 font-poppins font-medium text-sm whitespace-nowrap">
                    {mobile.activityName}
                  </td>
                  <td>
                    <button className="py-2 pl-16 md:pl-14 lg:pl-4 flex items-center justify-center rounded ">
                      <Image
                        src={mobile.image}
                        alt="Image"
                        className="w-[60px] h-[60px] md:w-[80px] md:h-[80px]   lg:w-[100px] lg:h-[100px] rounded-full"
                        width={80}
                        height={48}
                      />
                    </button>
                  </td>

                  
                  <td className="flex justify-end pl-10 md:pl-0 ">
                    <Link href={"/superadmin/dashboard/travel-activity-details"} className=" border p-2 rounded-l-lg text-black/75 hover:text-blue-700 dark:text-[#757575] dark:hover:text-blue-700">
                      <FiEye />
                    </Link>
                    <button className=" border p-2 text-black/75 hover:text-yellow-400 dark:text-[#757575] dark:hover:text-yellow-400">
                      <Link href={"/superadmin/dashboard/travel-activity-edit"}>
                        <TfiPencilAlt />
                      </Link>
                    </button>
                    <button className=" p-2 border rounded-r-lg text-red-600">
                      <FaRegTrashCan />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
           <div className="flex justify-between items-center mt-5">
             <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
             <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
               <a
                 href="#"
                 className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
               >
                 <span className="sr-only">Next Page</span>
                 <MdOutlineKeyboardArrowLeft />
               </a>
     
               <a
                 href="#"
                 className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
               >
                 <span className="sr-only">Next Page</span>
                 <MdOutlineKeyboardArrowRight />
               </a>
             </div>
           </div>
    </div>
  );
};

export default FeaturedHostelPage;
