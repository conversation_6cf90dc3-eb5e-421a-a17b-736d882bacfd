/* eslint-disable react/no-unescaped-entities */
import React from "react";
import Link from "next/link";

const MyHelp = () => {
  return (
    <>
      <h2 className="text-black flex gap-1 text-2xl font-bold mb-6">Help</h2>
      <h3 className="font-semibold sm:text-xl xs:text-lg text-base">How Can We Assist You?</h3>
      <p className="sm:text-base text-sm text-black/80 my-3">
        At MixDorm, we're here to make your experience as smooth and enjoyable
        as possible. Whether you need help with a booking, have questions about
        our services, or need support during your stay, our Help page provides
        resources and contact information to get you the assistance you need.
      </p>

      <div className="mb-8">
        <h4 className="font-semibold mb-4 sm:text-xl xs:text-lg text-base">
          Common Issues and Solutions
        </h4>
        <ol className="list-decimal sm:pl-8 xs:pl-6 pl-4">
          <li className="xs:text-lg text-base font-medium mb-4">
            Booking Assistance
            <p className="sm:text-base text-sm text-black/80 my-3">
              <strong className="text-black">Problem:</strong> I need to modify
              or cancel my booking.
            </p>
            <p className="sm:text-base text-sm text-black/80 my-3">
              <strong className="text-black">Solution:</strong> You can modify
              or cancel your booking by logging into your account on our website
              or app. For further assistance, please contact our customer
              support team.
            </p>
          </li>

          <li className="xs:text-lg text-base font-medium mb-4">
            Check-In and Check-Out
            <p className="sm:text-base text-sm text-black/80 my-3">
              <strong>Problem:</strong> I have a question about check-in or
              check-out times.
            </p>
            <p className="sm:text-base text-sm text-black/80 my-3">
              <strong>Solution:</strong> Standard check-in is at 12 noon, and
              check-out is at 10 AM. Early check-in or late check-out may be
              available upon request. Contact the property directly for
              availability.
            </p>
          </li>

          <li className="xs:text-lg text-base font-medium mb-4">
            Lost and Found
            <p className="sm:text-base text-sm text-black/80 my-3">
              <strong>Problem:</strong> I lost something during my stay.
            </p>
            <p className="sm:text-base text-sm text-black/80 my-3">
              <strong>Solution:</strong> Report lost items to our customer
              support team as soon as possible. We will make every effort to
              locate and return lost belongings.
            </p>
          </li>

          <li className="xs:text-lg text-base font-medium mb-4">
            Technical Issues
            <p className="sm:text-base text-sm text-black/80 my-3">
              <strong>Problem:</strong> I'm having trouble using the website or
              app.
            </p>
            <p className="sm:text-base text-sm text-black/80 my-3">
              <strong>Solution:</strong> Try clearing your browser cache or
              updating the app. If the issue persists, contact our technical
              support team for help.
            </p>
          </li>
        </ol>
      </div>

      <h4 className="font-semibold mb-4 sm:text-xl xs:text-lg text-base">Contact Us</h4>

      <p className="sm:text-base text-sm text-black/80 my-3">
        If you have any questions or concerns about this Privacy Policy, please
        contact us at:
      </p>
      <p className="sm:text-base text-sm text-black/80 my-3">
        MixDorm Email: <Link
              className="text-primary-blue hover:underline"
              href="mailto:<EMAIL>"
              prefetch={false}
            >
              <EMAIL>
            </Link>
      </p>
      <h4 className="font-semibold mb-4 sm:text-xl xs:text-lg text-base">
        Support Hours Our customer support team is available:
      </h4>

      <p className="sm:text-base text-sm text-black/80 my-3">
        Monday to Friday: 9 AM - 6 PM
      </p>
      <p className="sm:text-base text-sm text-black/80 my-3">
        Saturday and Sunday: 10 AM - 4 PM
      </p>
      <p className="sm:text-base text-sm text-black/80 my-3">
        For urgent matters outside of these hours, please leave a message or
        send an email, and we will respond as soon as possible.
      </p>
      <h4 className="font-semibold mb-4 sm:text-xl xs:text-lg text-base">
        Feedback and Suggestions
      </h4>

      <p className="sm:text-base text-sm text-black/80 my-3">
        We value your feedback and are always looking for ways to improve. If
        you have any suggestions or comments, please feel free to share them
        with us:
      </p>
      <p className="sm:text-base text-sm text-black/80 my-3">
       Email:  
        <Link
              className="text-primary-blue hover:underline"
              href="mailto:<EMAIL>"
              prefetch={false}
            >
              {" "}
               <EMAIL>
            </Link>
       
      </p>
      <h4 className="font-semibold mb-4 sm:text-xl xs:text-lg text-base">Resources</h4>
      <h4 className="font-semibold mb-4 sm:text-xl xs:text-lg text-base">Help Center</h4>

      <ul className="text-sm list-disc text-black/80 my-3 pl-8">
        <li className="xs:text-base text-sm mb-3">
          Access our detailed guides and troubleshooting tips in the Help
          Center.
        </li>
        <li className="xs:text-base text-sm">
          Join our Community Forum to connect with other travelers and share
          your experiences.
        </li>
      </ul>
      <p className="sm:text-base text-sm text-black/80 my-3">
        Thank you for choosing MixDorm. We're here to ensure you have a great
        stay, so don't hesitate to reach out if you need assistance! Feel free
        to tailor the content and contact details to match MixDorm's specific
        needs and support structure.
      </p>
    </>
  );
};

export default MyHelp;
