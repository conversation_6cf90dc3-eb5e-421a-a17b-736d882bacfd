@tailwind base;
@tailwind components;
@tailwind utilities;

@import "swiper/css";
@import "swiper/css/effect-coverflow";


#nprogress .bar {
  background: #40e0d0 !important;
  height: 4px !important;
}

#nprogress .peg {
  box-shadow: 0 0 10px #40e0d0, 0 0 5px #40e0d0;
}

/* Optionally hide the spinner */
#nprogress .spinner-icon {
  display: none !important;
}

html {
  overflow: unset !important;
  padding-right: unset !important;
  /* overflow-x: hidden !important; */
}

body {
  font-family: var(--font-inter), sans-serif;
  /* overflow-x: hidden !important; */
  /* cursor: none; */
}

::-moz-selection { /* Code for Firefox */
  background: #40E0D0;
}

::selection {
  background: #e5e7eb;
}

h1, h2, h3, h4, h5, h6, p, ul li, Link , .page-title
{
  font-family: var(--font-inter), sans-serif;
}

h1 {
  font-weight: 900;
  font-style: normal;
  font-size: 50px;
  line-height: 74px;
}

h2 {
  font-size: 40px;
  line-height: 64px;
  font-weight: 600;
}

h3 {
  font-weight: 500;
}

h4 {
  font-weight: 400;
}

h5,
h6 {
  font-weight: 500;
}

p,
ul li,
Link {
  font-size: 20px;
  font-weight: 400;
}

/* 
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(242, 245, 250, 0.3);
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  background: #40e0d0;
  border-radius: 8px;
}

::-webkit-scrollbar-thumb:hover {
  background: #40e0d0;
} */

.verify-input input {
  width: 70px !important;
  height: 44px !important;
  text-align: center;
  margin-right: 3% !important;
  /* @apply text-xs font-light bg-transparent rounded-3xl border border-[#EEEEEE] focus:outline-none placeholder:text-gray-50/40 text-gray-50; */
}
.datepicker .react-datepicker-wrapper {
  width: 100%;
}
.myCustomSwiper .swiper-button-next,
.myCustomSwiper .swiper-button-prev {
    color: #fff;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}

.myCustomSwiper .swiper-button-next:hover,
.myCustomSwiper .swiper-button-prev:hover {
    background-color: rgba(0, 0, 0, 0.8);
}

.myCustomSwiper .swiper-button-next:after,
.myCustomSwiper .swiper-button-prev:after {
    font-size: 18px;
    font-weight: bold;
}
.mySwiper .swiper-pagination-bullet {
  border-radius: 4px;
  width: 16px;
  height: 8px;
}
.myCustomSwiper .swiper-pagination-bullet-active {
  border: 2px solid #fff; 
  background-color: #fff;
}
.css-13cymwt-control{
  outline: none;
  border: 1px solid #e5e7eb !important;
  padding: 5px 10px;
  border-radius: 0.75rem !important;
  background-color: transparent;
}
.css-13cymwt-control:focus{
  outline: #eeeeee;
  box-shadow: #eeeeee;
}
.css-t3ipsp-control:hover{
  border: 1px solid #eeeeee;
  padding: 5px 10px;
  border-radius: 25px;
  background-color: transparent;
}
.css-t3ipsp-control{
  border: 1px solid #eeeeee;
  padding: 5px 10px;
  border-radius: 25px;
  background-color: transparent;
  box-shadow: none;
}
.PhoneInputInput{
outline: none;
width: 100%;
/* padding: 1rem 0.75rem; */
appearance: none;
}
.phone-input .PhoneInputInternationalIconPhone{
  display: none;
}
/* .PhoneInputCountry{
  padding-left: 0.75rem;
} */
@media screen and (max-width: 991px) {
  h1 {
    font-size: 36px;
    line-height: 46px;
  }
  h2 {
    font-size: 28px;
    line-height: 40px;
  }
}

@media screen and (max-width: 768px) {
  .banner-screen {
    height: 100%;
    max-height: 100%;
  }
  .responsive-table thead {
    display: none;
  }
  .responsive-table tbody tr {
    display: grid;
    padding-top: 10px;
    padding-bottom: 10px;
  }
  .responsive-table tbody tr th {
    text-align: left;
    padding-left: 155px;
    position: relative;
    font-size: 14px;
    padding-top: 5px;
    padding-bottom: 5px;
  }
  .responsive-table tbody tr td {
    text-align: left;
    padding-left: 155px;
    position: relative;
    font-size: 14px;
    padding-top: 5px;
    padding-bottom: 5px;
  }
  .responsive-table tbody tr td div,
  .responsive-table tbody tr td svg,
  .responsive-table tbody tr td ul {
    margin: 0;
  }
  .responsive-table tbody tr td ul {
    display: flex;
    justify-content: flex-start;
  }
  .responsive-table tbody tr th::after {
    position: absolute;
    content: attr(title);
    top: 7px;
    left: 44px;
    width: 178px;
    font-size: 14px;
    text-transform: uppercase;
  }
  .responsive-table tbody tr td::after {
    position: absolute;
    content: attr(title);
    top: 8px;
    left: 44px;
    width: 178px;
    font-size: 14px;
    text-transform: uppercase;
  }
  .page-title
  {
    font-size: 18px !important;
  }
  h1 {
  font-size: 28px;
  line-height: 40px;
  }
  h2 {
    font-size: 22px;
    line-height: 36px;
  }
  .banner-screen {
    height: 100%;
    max-height: 100%;
  }
}

/* .left-sidebar
{
  background: white;
}

.logo-img 
{
  border-radius: 8px;
} */

.bg-sky
{
  background: #40E0D0;
}

.border-right-bottom
{
  border-radius: 0 0 30px 0;
}

.border-right-top 
{
  border-radius: 0 30px 0 0;
}

.sticky-sidebar {
  position: sticky;
  top: 0;
  height: 100vh;
  overflow-y: overlay; /* Ensures the scrollbar doesn't take space */
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

/* Hide scrollbar by default */
.sticky-sidebar::-webkit-scrollbar {
  width: 0;
  height: 0;
}

/* .sticky-sidebar::-webkit-scrollbar {
  width: 3px; 
  height: 3px;
}

.sticky-sidebar:hover::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.365); 
  border-radius: 4px; 
} */

/* .sticky-sidebar:hover::-webkit-scrollbar-track {
  background: #40E0D0;
} */

.hover-scrollbar::-webkit-scrollbar {
  width: 0;
  height: 0;
}

.hover-scrollbar::-webkit-scrollbar {
  width: 3px; 
  height: 3px;
}

.hover-scrollbar:hover::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.365); 
  border-radius: 4px; 
}

.hover-scrollbar:hover::-webkit-scrollbar-track {
  background: #40E0D0;
}

.page-title 
{
  font-size: 22px;
  font-weight: 500;
  line-height: 33px;
}

.main-content-wrap 
{
  border: 1px solid #EEEEEE;
}

.bg-full 
{
  background-size: 100% 100%;
}

.custom-thumb:hover {
  background: #555;
}

.mobilemenu .mobilemenubox 
{
  display: block !important;
}

.calendar-tooltip
{
  position: relative;
}

.calendar-tooltip::before {
  content: '';
  position: absolute;
  top: -10px; /* Adjust based on tooltip height */
  left: 50%;
  transform: translateX(-50%);
  border-width: 0 10px 10px 10px; /* Triangle dimensions */
  border-style: solid;
  border-color: transparent transparent #EEEEEE transparent; /* Triangle color */
}

@media (max-width: 575px)
{
  .page-title
  {
    font-size: 16px !important;
  }
}

.indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 52px;
  background-color: red ;
  transition: transform 0.3s ease;
}

.date-input input 
{
  border-color: rgb(0 0 0 / 0.5) !important;
}

.inner-currency-wrap .inner-currency
{
  border: none !important;
  padding: 0 !important;
  font-size: 12px !important;
  padding-right: 30px !important;
  height: auto !important;
  width: max-content !important;
}

.inner-currency-wrap .inner-currency svg 
{
  width: 15px !important;
  height: 15px !important;
}

.inner-currency-wrap ul 
{
  width: max-content !important
}

.inner-currency-wrap label 
{
  display: none !important;
}

.header-country .inner-currency 
{
  color: white !important;
}

.header-country.inner-currency-wrap ul
{
  right: 0;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}
.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

@media (max-width: 480px)
{
  .inner-currency-wrap ul li
  {
    font-size: 12px !important;
  }
  .inner-currency-wrap ul
  {
    width: 220px !important 
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

.slider-button-prev , .slider-button-next {
  top: 40px;
  z-index: 20;
  height: 40px;
  width: 40px;
  color: black;
  border-radius: 50%;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.slider-button-prev {
  background-color: #E4E6E8;
}

.slider-button-next {
  background-color: #40E0D0;
}

.type-animation-cursor {
  color: #00ffcc;      /* change color */
  font-weight: bold;   /* make it thicker */
  animation: blink 1s step-end infinite;
}

@keyframes blink {
  50% {
    opacity: 0;
  }
}

.animate-slowbounce
{
  animation: slowbounce 3s ease-in-out infinite
}

@keyframes slowbounce {

  0%
  {
    transform: translateY(-2%)
  }
  50% {
    transform: translateY(2%);
  }
  100%
  {
    transform: translateY(-2%)
  }
}

.scroll-wrapper {
  display: flex;
  width: fit-content;
  animation: scrollLeft 20s linear infinite;
  animation-play-state: running;
}

.scroll-wrapper:hover {
  animation-play-state: paused;
}

@keyframes scrollLeft {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-50%);
  }
}

.bg-highlight {
  background-color: #cceeff;
  border-radius: 0.3em;
}

.react-datepicker__day--keyboard-selected {
  background-color: transparent !important;
  color: inherit !important;
}

.react-datepicker__day--outside-month {
  color: #ccc !important;
  pointer-events: none;
}

@keyframes zoomBounce {
  0% {
    transform: scale(1.2); /* Zoomed in */
  }
  40% {
    transform: scale(1); /* Zoomed out */
  }
  70% {
    transform: scale(1.05); /* Slight zoom in */
  }
  100% {
    transform: scale(1); /* Final scale */
  }
}

.animate-zoomBounce {
  animation: zoomBounce 7s ease-in-out forwards;
}

.apexcharts-legend-series {
  display: flex !important;
  align-items: center;
  gap: 8px; /* Adjust the spacing as needed */
}

.apexcharts-legend.apx-legend-position-right
{
  justify-content: center !important;
}

.about-mixdorm .apexcharts-canvas
{
  margin: auto;
}