import React from "react";
import { Checkbox, FormControlLabel } from "@mui/material";

const Conprofiledelete = () => {
    return (
      <>
        <section className='w-full'>
        <div className="flex items-center justify-center min-h-[500px] bg-transparent">
            <div className="text-center w-[600px]">
              <h1 className="text-lg font-semibold text-black mb-4">
                Confirm Profile Deletion
              </h1>
              <p className="text-gray-600 text-sm mb-3">
              Deleting your profile will permanently remove all data, including bookings, settings, and
              events
              </p>
              <FormControlLabel
                className="text-gray-400 text-sm font-normal"
                control={<Checkbox />}
                label="I understand that this action is irreversible"
              />
              <button className="bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-8 rounded-full shadow-md mb-4">
                Disconnect Channel Manager
              </button>
              <p className="text-gray-600 text-sm mb-3 font-bold">
              Cancellation Option
              </p>
              <h1 className="text-lg font-semibold text-black mb-4">
              Changed Your Mind?
              </h1>
              <p className="text-gray-600 text-sm mb-3">
              You can keep your profile active and explore other features of Mixdorm. If you need
              assistance, contact support
              </p>
              <button className="bg-[#40E0D0] hover:bg-bg-[#40E0D0] text-black font-medium py-3 px-8 rounded-full shadow-md mb-4">
                  Cancel and Return to Dashboard
              </button>
            </div>
          </div> 
        </section>
      </>
    );
};
export default Conprofiledelete;