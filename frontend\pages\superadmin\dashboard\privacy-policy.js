 
"use client";
import { useState } from "react";
import { Plus } from "lucide-react";
import Link from "next/link";
import { FaRegTrashCan } from "react-icons/fa6";
import { FiEye } from "react-icons/fi";
import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight,
} from "react-icons/md";
import { TfiPencilAlt } from "react-icons/tfi";

// Function to split text into lines with a specific number of words per line
const splitTextByWords = (text, wordsPerLine) => {
  if (typeof text !== "string") return [text]; // Wrap non-string content in an array
  const words = text.split(" ");

  // If no words are found, return an empty array to avoid `.map` errors
  if (words.length === 0) return [];

  return words
    .reduce((acc, word, index) => {
      if (index % wordsPerLine === 0) acc.push([]);
      acc[acc.length - 1].push(word);
      return acc;
    }, [])
    .map((line) => line.join(" "));
};

const Privacypolicy = () => {
  // eslint-disable-next-line no-unused-vars
  const [wordsPerLine, setWordsPerLine] = useState(13);

  const privacyData = [
    {
      id: 1,
      name: (
        <div className="">
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            Introduction
          </span>
        </div>
      ),
      feedback:
        "Welcome to MixDorm! Your privacy is important to us. This Privacy Policy explains how we collect, use, and protect your personal information when you visit our website and use our services.",
    },
    {
      id: 2,
      name: (
        <div className="">
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            Information:
          </span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            We collect the
          </span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            following type
          </span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            of information
          </span>
        </div>
      ),
      feedback: (
        <div>
          <h1 className="text-base text-black font-bold dark:text-[#B6B6B6]">
            1. Personal Information
          </h1>
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            This includes your name, email address, phone number, and payment
            information. We <br/> collect this information when you register on our
            site, book <br/> accommodation, or contact us for support.
          </p>

          <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
            2. Non-Personal Information:
          </h1>
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            This includes your browser type, IP address, device information, and
            usage data. We <br/> collect this information to improve our website and <br/>
            services.
          </p>

          <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
            3. Cookies and Tracking Technologies:
          </h1>
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            We use cookies to enhance your experience on our site. Cookies are
            small files stored <br/> on your device that help us understand <br/> your
            preferences and usage patterns.
          </p>
        </div>
      ),
    },
    {
      id: 3,
      name: (
        <div>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">How we use</span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">your</span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            information to
          </span>
        </div>
      ),
      feedback: (
        <div>
          <h1 className="text-base text-black font-bold dark:text-[#B6B6B6]">
            1. Provide Services
          </h1>
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            Process your bookings, manage your account, and provide customer
            support.
          </p>

          <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
            2. Improve Our Services:
          </h1>
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            Analyze usage data to enhance our website, services, and user
            experience.
          </p>

          <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
            3. Marketing and Communication:
          </h1>
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            Send you promotional materials, newsletters, and updates about our
            services. You can <br/>  opt out of these communications at any time.
          </p>

          <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
            4. Legal Compliance:
          </h1>
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            Comply with legal obligations and protect our rights and the rights
            of our users. <br/> Sharing Your Information.
          </p>
        </div>
      ),
    },
    {
      id: 4,
      name: (
        <div>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            We do not sell, trade, or
          </span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            rent your personal
          </span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            information to third
          </span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            parties. We may share
          </span>
          <span className="block text-gray-500 font-semibold dark:text-[#757575]">
            your information with
          </span>
        </div>
      ),
      feedback: (
        <div>
          <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
            1. Service Providers
          </h1>
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            •Trusted third parties that help us operate
            our website, process payments, and <br/> provide customer support.
          </p>
          <h1 className="text-base text-black font-bold mt-1 dark:text-[#B6B6B6]">
            2. Legal Authorities
          </h1>
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • When required by law or to protect our rights and
            the safety of our users.
          </p>
          <p className="text-sm lg:text-base text-gray-500 font-semibold dark:text-[#757575]">
            • Data Security
          </p>
        </div>
      ),
    },
  ];

  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <div className="flex items-center justify-between w-full">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Privacy Policy
        </h2>
        <div className="w-[50%] gap-x-5 flex justify-end items-center">
          <Link
            href={"/superadmin/dashboard/privacy-policy-add"}
            className="px-4 py-2 text-sm font-medium font-poppins text-white rounded flex items-center bg-sky-blue-650"
          >
            <Plus size={18} className="mr-1" /> Privacy Policy
          </Link>
        </div>
      </div>

      <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border dark:border-none">
        <table className="w-full divide-y bg-white rounded-xl divide-gray-200 dark:bg-black ">
          <thead>
            <tr className="w-full items-center justify-between">
              <th className="pr-20 py-6 bg-white text-center text-sm font-semibold font-poppins text-black uppercase dark:bg-black dark:text-[#B6B6B6]">
                TITLE TEXT
              </th>
              <th className="py-6 pr-10 md:pr-8 lg:pr-10 bg-white text-sm font-semibold text-center font-poppins text-black uppercase dark:bg-black dark:text-[#B6B6B6]">
                SUB TEXT
              </th>
              <th className="py-6  bg-white text-center text-sm font-poppins font-semibold text-black uppercase dark:bg-black dark:text-[#B6B6B6]">
                ACTION
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 border-x border-y text-black/70 dark:border-x-0 dark:text-[#757575]">
            {privacyData.map((privacyItem) => (
              <tr key={privacyItem.id} className="w-full">
                <td className="whitespace-nowrap text-gray-500 text-sm font-medium font-poppins px-6 py-8 dark:text-[#757575]">
                  {privacyItem.name}
                </td>
                <td className="pl-6 lg:pl-32 px-5 text-sm lg:text-base text-gray-500 font-medium font-poppins py-4 dark:text-[#757575]">
                  {splitTextByWords(privacyItem.feedback, wordsPerLine)?.map(
                    (line, index) => (
                      <div key={index} className="block">
                        {line}
                      </div>
                    )
                  )}
                </td>
                <td className="py-6 md:py-6 lg:py-10 flex justify-end px-6">
                  <Link
                    href={"/superadmin/dashboard/privacy-policy-details"}
                    className="border p-2 rounded-l-lg text-black/75 hover:text-blue-700 dark:text-[#757575] dark:hover:text-blue-700"
                  >
                    <FiEye />
                  </Link>
                  <Link
                    href={"/superadmin/dashboard/privacy-policy-edit"}
                    className="border p-2 text-black/75 hover:text-yellow-400 dark:text-[#757575] dark:hover:text-yellow-400"
                  >
                    <TfiPencilAlt />
                  </Link>
                  <button className="p-2 border rounded-r-lg text-red-600">
                    <FaRegTrashCan />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
{/* 
      <div className="flex justify-between items-center mt-5">
        <div className="text-black/75 text-sm font-poppins">
          Showing 1-09 of 78
        </div>
        <div className="inline-flex items-center justify-center border rounded-xl bg-white">
          <a
            href="#"
            className="inline-flex size-7 items-center justify-center border border-gray-100 text-black/70"
          >
            <MdOutlineKeyboardArrowLeft />
          </a>
          <a
            href="#"
            className="inline-flex size-7 items-center justify-center border border-gray-100 text-black"
          >
            <MdOutlineKeyboardArrowRight />
          </a>
        </div>
      </div> */}

            <div className="flex justify-between items-center mt-5">
              <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
              <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
                <a
                  href="#"
                  className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                >
                  <span className="sr-only">Next Page</span>
                  <MdOutlineKeyboardArrowLeft />
                </a>
      
                <a
                  href="#"
                  className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                >
                  <span className="sr-only">Next Page</span>
                  <MdOutlineKeyboardArrowRight />
                </a>
              </div>
            </div>
    </div>
  );
};

export default Privacypolicy;
