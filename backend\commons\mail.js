import nodemailer from 'nodemailer';
import EmailTemplate from '../models/emailtemplates.js'; // Adjust the path as necessary
import dotenv from 'dotenv';
dotenv.config();

const sendEmail = async (templateName, data) => {
    try {
        // Fetch the email template from the database
        const template = await EmailTemplate.findOne({ name: templateName });
        if (!template) {
            throw new Error('Email template not found');
        }

        // Replace variables in the template text with the provided data
        let emailText = template.text;
        for (const [key, value] of Object.entries(data)) {
            const regex = new RegExp(`{{${key}}}`, 'g');
            emailText = emailText.replace(regex, value);
        }

        let transporter = nodemailer.createTransport({
            host: 'smtp.zoho.in',
            port: 587,
            secure: false,
            auth: {
                user: process.env.EMAIL_USER,
                pass: process.env.EMAIL_PASS
            }
        });
        console.log("transporter",transporter)
      try {
        let mailOptions = {
            from: process.env.EMAIL_USER,
            to: data.email,
            subject: template.subject,
            html: emailText
        };

        console.log("mailOptions",mailOptions)
        await transporter.sendMail(mailOptions);
        console.log(`Email sent to ${data.email}`);
      } catch (error) {
        console.log("eror",error)
      }
    } catch (error) {
        console.error('Error sending email:', error);
        throw error;
    }
};
const dynamicSendEmail = async (templateName, data) => {
    try {
        // Fetch the email template from the database
        const template = await EmailTemplate.findOne({ name: templateName });
        if (!template) {
            throw new Error('Email template not found');
        }

        // Replace variables in the template text with the provided data
        let emailText = template.text;
        for (const [key, value] of Object.entries(data)) {
            const regex = new RegExp(`{{${key}}}`, 'g');
            emailText = emailText.replace(regex, value);
        }

        let transporter = nodemailer.createTransport({
            host: 'smtp.zoho.in',
            port: 587,
            secure: false,
            auth: {
                user: process.env.EMAIL_USER,
                pass: process.env.EMAIL_PASS
            }
        });
        console.log("transporter",transporter)
      try {
        let mailOptions = {
            from: process.env.EMAIL_USER,
            to: data.email,
            subject: data.subject,
            html: emailText
        };

        console.log("mailOptions",mailOptions)
        await transporter.sendMail(mailOptions);
        console.log(`Email sent to ${data.email}`);
      } catch (error) {
        console.log("eror",error)
      }
    } catch (error) {
        console.error('Error sending email:', error);
        throw error;
    }
};
export default sendEmail;
