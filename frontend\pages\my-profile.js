import React, { useState, useEffect } from "react";
import {
 
  LogOut,
 
  HelpCircle,
 
  ChevronDown,
  ChevronUp,
} from "lucide-react"; // Icons
import { CiCreditCard1 } from "react-icons/ci";
import { useRouter } from "next/router";
import dynamic from "next/dynamic";

// Component Imports
// import SearchNavbar from "@/components/layout/SearchNavbar";

// Services and utils
import { getMyStayApi, getProfileApi, getProfileTravelingApi, getProfileViewsApi } from "@/services/webflowServices";
import { getItemLocalStorage, removeItemLocalStorage } from "@/utils/browserSetting";
import MyProfile from "@/components/myProfile/myProfile";
const EditDetailsPage = dynamic(() => import("@/components/myProfile/editProfilePage"), { ssr: false });
const MembershipPage = dynamic(() => import("@/components/myProfile/membership"), { ssr: false });
const Wishlist = dynamic(() => import("@/components/myProfile/wishlist"), { ssr: false });
const MyTripsPage = dynamic(() => import("@/components/myProfile/myTrip"), { ssr: false });
const MyWalletPage = dynamic(() => import("@/components/myProfile/myWallet"), { ssr: false });
const MyHelp = dynamic(() => import("@/components/myProfile/myHelp"), { ssr: false });
const MyStay = dynamic(() => import("@/components/myProfile/myStay"), { ssr: false });
const MyRide = dynamic(() => import("@/components/myProfile/myRide"), { ssr: false });
const MyEvent = dynamic(() => import("@/components/myProfile/myEvent"), { ssr: false });
// const MyPayment = dynamic(() => import("@/components/myProfile/payment"), { ssr: false });
import { useNavbar } from "@/components/home/<USER>";
import { removeFirebaseToken } from "@/services/ownerflowServices";
import Head from "next/head";
import { MdCardMembership, MdLogout,   MdOutlineTravelExplore, } from "react-icons/md";
import {  FaRegUser } from "react-icons/fa";
import { GrEdit } from "react-icons/gr";
import { IoWalletOutline } from "react-icons/io5";
import { GoHeart } from "react-icons/go";


const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

const MyProfileMain = () => {
  const router = useRouter();
  const { section } = router.query;
  const [isTripsOpen, setIsTripsOpen] = useState(false);
  const [profileData, setProfileData] = useState(null);
  const [profileTravelingData, setProfileTravelingData] = useState(null);
  const [profileViewsData, setProfileViewsData] = useState(null);
  const [stayData, setStayData] = useState([]);
  const [loading, setLoading] = useState(false);

  // Map URL sections to component names
  const sectionMapping = {
    profile: "My Profile",
    edit: "Edit Details",
    membership: "Membership",
    wishlist: "Wishlist",
    trips: "My Trips",
    stay: "My Stay",
    ride: "My Ride",
    event: "My Event",
    wallet: "My Wallet",
    // payment: "My Payment",
    help: "Help"
  };

  // Get current section from URL or default to profile
  const currentSection = section ? sectionMapping[section] || "My Profile" : "My Profile";

  // Section-to-component mapping
  const sectionComponents = {
    "My Profile": <MyProfile data={profileData} loading={loading} setLoading={setLoading} profileTravelingData={profileTravelingData} profileViewsData={profileViewsData} />,
    "Edit Details": <EditDetailsPage setLoading={setLoading} data={profileData} />,
    "Membership": <MembershipPage />,
    "Wishlist": <Wishlist />,
    "My Trips": <MyTripsPage />,
    "My Stay": <MyStay />,
    "My Ride": <MyRide />,
    "My Event": <MyEvent />,
    "My Wallet": <MyWalletPage />,
    // "My Payment": <MyPayment />,
    // "Add Card": <AddCard />,
    Help: <MyHelp />,
  };

  // Fetch profile data on load or when "My Profile" or "Edit Details" is selected
  useEffect(() => {
    const fetchProfile = async () => {
      setLoading(true);
      try {
        const response = await getProfileApi();
        const travelResponse = await getProfileTravelingApi();
        const profileViewsResponse = await getProfileViewsApi();
        const stayResponse = await getMyStayApi(1, 1);
        setProfileViewsData(profileViewsResponse?.data)
        setProfileData(response.data);
        setProfileTravelingData(travelResponse?.data)
        setStayData(stayResponse?.data?.data?.bookings || []);
      } catch (error) {
        console.error("Error fetching profile data:", error);
      } finally {
        setLoading(false);
      }
    };

    if (currentSection === "My Profile" || currentSection === "Edit Details") {
      fetchProfile();
    }
  }, [currentSection]);


  const {  updateUserStatus,updateUserRole } = useNavbar();

  // Handle section navigation
  const handleSectionClick = async(section) => {
    if (section === "My Trips") {
      setIsTripsOpen(!isTripsOpen);
      if(stayData?.length === 0) {
        router.push('/my-profile?section=trips');
      } else {
        router.push('/my-profile?section=stay');
      }
    } else if (["My Stay", "My Ride", "My Event"].includes(section)) {
      setIsTripsOpen(true);
      const sectionMap = {
        "My Stay": "stay",
        "My Ride": "ride",
        "My Event": "event"
      };
      router.push(`/my-profile?section=${sectionMap[section]}`);
    } else if (section === "Logout") {
      removeItemLocalStorage("token");
      updateUserStatus("")
      updateUserRole("")
      removeItemLocalStorage("name");
      removeItemLocalStorage("email");
      removeItemLocalStorage("contact");
      const payload = {
        token: getItemLocalStorage("FCT"), 
        userId: getItemLocalStorage("id"), 
      };
      try {
        await removeFirebaseToken(payload);
        console.log("FCM token removed successfully.");
        removeItemLocalStorage("FCT");
      } catch (error) {
        console.error("Error removing FCM token:", error);
      }
      removeItemLocalStorage("id");
      router.push("/");
    } else {
      const sectionMap = {
        "My Profile": "profile",
        "Edit Details": "edit",
        "Membership": "membership",
        "Wishlist" : "wishlist",
        "My Wallet": "wallet",
        // "My Payment" : "payment",
        "Help": "help"
      };
      router.push(`/my-profile?section=${sectionMap[section]}`);
      setIsTripsOpen(false);
    }
  };
  

  // Update isTripsOpen based on current section
  useEffect(() => {
    if (["stay", "ride", "event"].includes(section)) {
      setIsTripsOpen(true);
    }
  }, [section]);

  return (
    <>
    <Head>
      <title>My Profile | Manage Bookings & Preferences | Mixdorm</title>
    </Head>
      <Loader open={loading} />
      <div className="bg-white pb-4 md:pb-20 sm:pt-12 pt-10">
        <div className="container md:px-8 lg:px-6 xl:px-6">
          <div className="flex items-start md:pb-10 relative">
            {/* Sidebar */}
            <div className="w-[265px] md:flex hidden flex-col items-center justify-start mr-4 sticky top-32 left-0">
              <ul className="w-full">
                {Object.keys(sectionComponents).map((item) => (
                  <React.Fragment key={item}>
                    {item === "My Trips" ? (
                      <div>
                        <li
                          onClick={() => handleSectionClick("My Trips")}
                          className={`flex items-center gap-x-2 cursor-pointer text-base font-medium text-black h-[52px] px-6 py-3 bg-[#D9F9F6] mt-2 rounded-4xl ${
                            currentSection === "My Trips" && "bg-primary-blue "
                          }`}
                        >
                          <MdOutlineTravelExplore size={20} />
                          <span className="flex-grow text-left">My Trips</span>
                          {isTripsOpen ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
                        </li>
                        {isTripsOpen && (
                          <ul className="ml-4 space-y-2">
                            {["My Stay", "My Ride", "My Event"].map((subItem) => (
                              <li
                                key={subItem}
                                onClick={() => handleSectionClick(subItem)}
                                className={`flex items-center gap-x-2 p-2 rounded-full cursor-pointer text-base ${
                                  currentSection === subItem
                                    ? "text-primary-blue font-semibold"
                                    : "text-black/80 hover:text-primary-blue"
                                }`}
                              >
                                <button className="text-left">{subItem}</button>
                              </li>
                            ))}
                          </ul>
                        )}
                      </div>
                    ) : (
                      item !== "My Stay" &&
                      item !== "My Ride" &&
                      item !== "My Event" && (
                        <li
                          key={item}
                          onClick={() => handleSectionClick(item)}
                          className={`flex items-center gap-x-2 cursor-pointer text-base font-medium px-6 py-3 h-[52px] text-black bg-[#D9F9F6] mt-2 rounded-4xl ${
                            currentSection === item && "bg-primary-blue "
                          }`}
                        >
                          {item === "My Profile" && <FaRegUser size={20} className="mt-0" />}
                          {item === "Edit Details" && <GrEdit size={20} />}
                          {item === "Membership" && <MdCardMembership size={20} />}  
                          {item === "Wishlist" && <GoHeart size={20} />}                     
                          {item === "My Wallet" && <IoWalletOutline size={20} />}
                          {/* {item === "My Payment" && <PiHandCoinsFill size={20} />} */}
                          {item === "Add Card" && <CiCreditCard1 size={20} />}
                          {item === "Help" && <HelpCircle size={20} />}
                          {item === "Logout" && <MdLogout size={20} />}
                          <button className="text-left">{item}</button>
                        </li>
                      )
                    )}
                  </React.Fragment>
                ))}
                <li
                  onClick={() => handleSectionClick("Logout")}
                className={`flex items-center gap-x-2 cursor-pointer text-base font-medium px-6 py-3 text-black bg-[#D9F9F6] mt-2 rounded-4xl h-[52px] hover:bg-primary-blue`}
                >
                  <LogOut size={20} />
                  <button className="text-left">Logout</button>
                </li>
              </ul>
            </div>

            {/* Main Content */}
            <div className="md:flex-1 w-full md:border-l border-[#EEEEEE] lg:pl-11 md:pl-4 p-0">
              {sectionComponents[currentSection]}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default MyProfileMain;
