import React, { useState } from "react";
import Stepper from "@/components/ownerFlow/stepper";
import PropertyDetails from "@/components/ownerFlow/propertyDetails";
import dynamic from "next/dynamic";

const UploadPropertyDocument = dynamic(
  () => import("@/components/ownerFlow/uploadPropertyDocuments"),
  {
    ssr: false,
  }
);

const Onboarding = dynamic(() => import("@/components/ownerFlow/onBoarding"), {
  ssr: false,
});

const MainComponent = () => {


  const [currentStep, setCurrentStep] = useState(0);
  const [propertyId, setPropertyId] = useState(null);
  const [propertyDetails, setPropertyDetails] = useState({
    step1: {
      name: "",
      type: "",
      email: "",
      address: { lineOne: "", lineTwo: "" },
      postCode: "",
      country: "", // Country code
      countryName: "", // Full country name
      state: "", // State code
      stateName: "", // Full state name
      city: "",
      termsAccepted: false,
      phoneNumber: "",
      latitude: "",
      longitude: "",
    },
    step2: {
      propertyPhoto: [],
      propertyLicense: [],
      propertyAddress: [],
      termsAccepted: false,
    },
  });

  console.log("data46",propertyDetails)

  const handleNext = (id = null) => {
    if (id) {
      setPropertyId(id);
    }
    setCurrentStep((prevStep) => prevStep + 1);
  };

  const handleViewClick = async (stepIndex) => {
    setCurrentStep(stepIndex);
    // if (!propertyId) {
    //   toast.error("No property ID found");
    //   return;
    // }
    // try {
    //   const response = await propertyDetailsApi(propertyId);
    //   if (response.data.status) {
    //     const data = response.data.data;
    //     const address = data.address || {};
    //     const location = data.location?.coordinates || [];

    //     const countryCode = getCountryCodeFromName(address.country);
    //     const stateCode = getStateCodeFromName(address.state, countryCode);

    //     const updatedFormData = {
    //       name: data.name || "",
    //       type: data.type || "",
    //       email: data.contactUs?.email || "",
    //       address: {
    //         lineOne: address.lineOne || "",
    //         lineTwo: address.lineTwo || "",
    //       },
    //       postCode: address.zipcode || "",
    //       country: countryCode || "",
    //       countryName: address.country || "",
    //       state: stateCode || "",
    //       stateName: address.state || "",
    //       city: address.city || "",
    //       termsAccepted: false,
    //       phoneNumber: data.contactUs?.phoneNumber || "",
    //       latitude: location[1] || "",
    //       longitude: location[0] || "",
    //       id: data?._id || "",
    //     };

    //     setPropertyDetails(updatedFormData);
    //     setCurrentStep(stepIndex);
    //   } else {
    //     toast.error(
    //       response.data.message || "Failed to fetch property details"
    //     );
    //   }
    // } catch (error) {
    //   console.error("Error fetching property details:", error);
    //   toast.error(error?.response?.data?.message || "Something went wrong");
    // }
  };

  const handleEditClick = (stepIndex) => {
    setCurrentStep(stepIndex);
  };

  const updateStepData = (step, data) => {
    setPropertyDetails((prev) => ({
      ...prev,
      [step]: { ...prev[step], ...data },
    }));
  };

  // Validate the current step


  return (
    <div className='bg-[#F7F7F7] py-12'>
      <Stepper
        currentStep={currentStep}
        onViewClick={handleViewClick}
        onEditClick={handleEditClick}
      />
      {currentStep === 0 && (
        <PropertyDetails
          onContinue={handleNext}
          viewFormData={propertyDetails?.step1}
          setPropertyDetails={setPropertyDetails}
          setPropertyId={setPropertyId}
          data={propertyDetails.step1}
          updateData={(data) => updateStepData("step1", data)}
        />
      )}
      {currentStep === 1 && (
        <UploadPropertyDocument
          propertyId={propertyId}
          onContinue={handleNext}
          data={propertyDetails.step2}
          updateData={(data) => updateStepData("step2", data)}
        />
      )}
      {currentStep === 2 && (
        <Onboarding
          propertyId={propertyId}
          data={propertyDetails}
          updateData={(data) => updateStepData("step3", data)}
        />
      )}
    </div>
  );
};

export default MainComponent;
