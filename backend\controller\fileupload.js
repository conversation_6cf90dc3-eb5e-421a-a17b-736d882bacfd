import pkg from 'aws-sdk';
import multer from 'multer';
import multerS3 from 'multer-s3';
import Response from '../utills/response.js';
import dotenv from 'dotenv';
dotenv.config();
const { S3 } = pkg;
import { decrypt } from '../utills/encryptions.js';
// Initialize the S3 client
console.log('Bucket Name:', process.env.S3_BUCKET_NAME);
const s3 = new S3({
  accessKeyId: await decrypt(process.env.AWS_ACCESS_KEY_ID),
  secretAccessKey: await decrypt(process.env.AWS_SECRET_ACCESS_KEY),
  region: process.env.AWS_REGION,
});
// Configure multer to use S3 for file uploads
const upload = multer({
  storage: multerS3({
    s3: s3,
    bucket: await decrypt(process.env.S3_BUCKET_NAME),
    key: function (req, file, cb) {
      const key = `uploads/${Date.now()}-${file.originalname}`;
      cb(null, key);
    }
  }),
  limits: {  fileSize: 4000 * 1024 * 1024 }
}).single('file');
const createUploadMultiple = (bucketName) => {
  const s3 = new S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID, // Use env vars directly (already decrypted by dotenv)
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION,
  });

  return multer({
    storage: multerS3({
      s3,
      bucket: bucketName,
      key: function (req, file, cb) {
        const fileName = `${Date.now()}-${file.originalname}`;
        cb(null, fileName);
      },
    }),
    limits: {
      fileSize: 1024 * 1024 * 1024 * 10, // 10GB per file
    },
  }).array('files');
};


export const generateURLAndUpload = async (req, res) => {
  try {
    uploadMultiple(req, res, async function (err) {
      if (err) {
        console.error('Error uploading file:', err);
        return Response.InternalServerError(res, null, 'Error uploading file');
      }

      const file = req.files;
      if (!file) {
        return Response.BadRequest(res, null, 'File is required');
      }

      // Generate a pre-signed URL for accessing the uploaded file
      const params = {
        Bucket: (process.env.S3_BUCKET_NAME),
        Key: file.key,
        Expires: 60, // URL expiration time in seconds
      };
      const objectURL = `https://${await decrypt(process.env.S3_BUCKET_NAME)}.s3.${process.env.AWS_REGION}.amazonaws.com/${file.key}`;
     // const url = s3.getSignedUrl('getObject', params);

      return Response.OK(res, { filename: file.originalname, path: file.key, objectURL }, 'File uploaded and access URL generated successfully');
    });
  } catch (error) {
    console.error('Error processing request:', error);
    return Response.InternalServerError(res, null, error.message);
  }
};
// import multer from "multer";
// import multerS3 from "multer-s3";
import AWS from "aws-sdk";

export const generateURLAndUploadMultiple = async (req, res) => {
  try {
    const decryptedBucket = await decrypt(process.env.S3_BUCKET_NAME);
    const accessKeyId = await decrypt(process.env.AWS_ACCESS_KEY_ID);
    const secretAccessKey = await decrypt(process.env.AWS_SECRET_ACCESS_KEY);

    const s3 = new AWS.S3({
      accessKeyId,
      secretAccessKey,
      region: process.env.AWS_REGION,
    });

    const upload = multer({
      storage: multerS3({
        s3,
        bucket: decryptedBucket,
        key: (req, file, cb) => {
          cb(null, `${Date.now()}-${file.originalname}`);
        }
      })
    }).array("files");

    upload(req, res, (err) => {
      if (err) {
        console.error("Multer error:", err);
        return res.status(500).json({ status: false, message: "Multer error", error: err.message });
      }

      if (!req.files || req.files.length === 0) {
        return res.status(400).json({ status: false, message: "Atttt least one file is required" });
      }
      
      const uploadedFiles = req.files.map(file => ({
        filename: file.originalname,
        path: file.key,
        objectURL: file.location
      }));

      return res.status(200).json({
        status: true,
        message: "Files uploaded successfully",
        data: uploadedFiles
      });
    });
  } catch (err) {
    console.error("Unexpected error:", err);
    return res.status(500).json({ status: false, message: err.message });
  }
};




/**
 * Generate a pre-signed URL for accessing a file from S3.
 * @param {string} filePath - The path (key) of the file in the S3 bucket.
 * @returns {string} The pre-signed URL.
 */
export const generatePresignedUrl = async(filePath) => {
  const params = {
    Bucket: await decrypt(process.env.S3_BUCKET_NAME),
    Key: filePath,
    Expires: 60, // URL expiration time in seconds
  };
  return s3.getSignedUrl('getObject', params);
};


