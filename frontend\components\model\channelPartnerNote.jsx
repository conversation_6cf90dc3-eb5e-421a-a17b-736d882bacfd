import React from "react";
import { Mo<PERSON>, Fade, Backdrop } from "@mui/material";

const ChannelPartnerNote = ({ close, open }) => {
  const style = {
    position: "fixed",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    maxWidth: 384,
    bgcolor: "background.paper",
    boxShadow: 24,
    borderRadius: "20px",
    p: 4,
  };

  return (
    <Modal
      open={open}
      onClose={close}
      aria-labelledby="child-modal-title"
      aria-describedby="child-modal-description"
      closeAfterTransition
      slots={{ backdrop: Backdrop }}
      slotProps={{
        backdrop: {
          timeout: 500,
        },
      }}
      className="bg-black/70"
    >
      <Fade in={open} sx={style}>
        <div className="bg-white rounded-2xl max-w-[390px] mx-auto left-1/2 absolute w-[95%] top-1/2 -translate-y-1/2 -translate-x-1/2 p-6">
          <h2 className="sm:text-xl text-md leading-none font-bold text-gray-800 mb-4">
            Note:
          </h2>
          <form className="space-y-6 max-w-[830px] mx-auto">
            <p className="text-sm text-[#000000] font-normal font-inter">
              Please make sure your property is bookable again on Mixdorm as
              soon as possible. If your rates, availability or restrictions
              aren’t imported to your new connectivity provider calendar after
              the connection is activated, please contact Channel Partner for
              assistance.
            </p>

            <div className="flex justify-center gap-3">
              <button
                type="button"
                // eslint-disable-next-line react/no-unknown-property
                onClose={close}
                className="px-4 border-[#40E0D0] border text-[#40E0D0] py-3 rounded-lg shadow-sm hover:bg-[#40E0D0] hover:text-white w-full text-sm font-medium"
              >
                Reset
              </button>
              <button
                type="submit"
                className="px-4 bg-[#40E0D0] border border-[#40E0D0] w-full text-black py-3 font-medium rounded-lg shadow-sm hover:bg-[#fff] text-sm"
              >
                Connect Request
              </button>
            </div>
          </form>
        </div>
      </Fade>
    </Modal>
  );
};

export default ChannelPartnerNote;
