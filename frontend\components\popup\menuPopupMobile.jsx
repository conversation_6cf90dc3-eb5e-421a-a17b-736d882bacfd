// import React, { useEffect, useRef, useState } from "react";
// import Link from "next/link";
// import { ChevronDown, ChevronUp } from "lucide-react";
// import toast from "react-hot-toast";
// import { getItemLocalStorage, removeItemLocalStorage } from "@/utils/browserSetting";
// import ContactPopup from "./contactPopup";
// import { getPropertyCountApi } from "@/services/webflowServices";
// import { useRouter } from "next/router";

// const menuPopupMobile = ({ isOpen, toggleMenu, updateTokenState, toggleLoginPopup, updateRoleState }) => {
//     // State to manage the open/close state of each section
//     const [openSections, setOpenSections] = useState({
//         services: false,
//         company: false,
//         help: false,
//         account: false,
//     });
//     const [hasToken, setHasToken] = useState(false);
//     const [isContactPopupOpen, setIsContactPopupOpen] = useState(false);
//     const [role, setRole] = useState(false);
//     const modalRef = useRef(null);
//     const router = useRouter();

//     // Toggle function for each section
//     const toggleSection = (section) => {
//         setOpenSections((prevState) => ({
//             ...prevState,
//             [section]: !prevState[section],
//         }));
//     };

//     useEffect(() => {
//         const token = getItemLocalStorage("token");
//         setHasToken(!!token);
//     }, []);

//     useEffect(() => {
//         const role = getItemLocalStorage("role");
//         setRole(role);
//     }, []);

//     const handleLogout = () => {
//         removeItemLocalStorage("token")
//         removeItemLocalStorage("name")
//         removeItemLocalStorage("id")
//         removeItemLocalStorage("role")
//         toggleMenu();
//         updateTokenState();
//         updateRoleState();
//         toast.success("Logged out successfully");
//     };

//     const openContactPopup = async () => {
//         setIsContactPopupOpen(true);
//     };

//     const closeContactPopup = () => {
//         setIsContactPopupOpen(false);
//     };

//     const handleLoginClick = () => {
//         toggleLoginPopup();
//         toggleMenu();
//     };

//     // Close modal when clicking outside
//     useEffect(() => {
//         const handleOutsideClick = (event) => {
//             if (modalRef.current && !modalRef.current.contains(event.target)) {
//                 toggleMenu();
//             }
//         };

//         if (isOpen) {
//             document.addEventListener("mousedown", handleOutsideClick);
//         }

//         return () => {
//             document.removeEventListener("mousedown", handleOutsideClick);
//         };
//     }, [isOpen, toggleMenu]);

//     if (!isOpen) return null;

//     return (
//       <>
//         <div
//           className={`fixed w-full h-[98%] sm:h-full sm:w-[400px] top-2 sm:top-[120px] right-0 sm:right-[120px] sm:left-auto left-2
//        lg:bottom-0 rounded-tl-2xl rounded-bl-2xl pl-[2px] pb-[2px]   z-50 flex items-start justify-end sm:bg-transparent bg-black
//         bg-opacity-[70%] animated ${
//           isOpen ? "sm:animate-none fadeInRight" : ""
//         }`}
//         >
//           <div
//             ref={modalRef}
//             className='bg-white rounded-tl-2xl rounded-bl-2xl w-[100%] max-w-full p-5 ml-0 mr-0 mt-0 h-full font-manrope'
//           >
//             <div className='flex items-center justify-between mb-6'>
//               <span className='text-[#40E0D0] flex text-2xl font-extrabold'>
//                 Mix<p className='text-black text-2xl font-extrabold '>Dorm</p>
//               </span>
//               <button onClick={toggleMenu} className='text-black'>
//                 ✕
//               </button>
//             </div>
//             <ul className='overflow-y-auto  max-h-[600px] sm:max-h-96 fancy_y_scroll pr-0'>
//               {/* Services */}
//               <li className=''>
//                 <div
//                   className={`flex justify-between items-center mb-5 cursor-pointer border border-solid border-[#EEEEEE]  py-3 px-4 rounded-full ${
//                     openSections.services &&
//                     "bg-[#D9F9F6] text-black !border-[#D9F9F6]"
//                   }`}
//                   onClick={() => toggleSection("services")}
//                 >
//                   <Link
//                     href='/services'
//                     className='sm:text-base text-sm font-[600] text-black'
//                     prefetch={false}
//                   >
//                     Services
//                   </Link>
//                   {openSections.services ? <ChevronUp /> : <ChevronDown />}
//                 </div>
//                 {openSections.services && (
//                   <ul className='text-base font-medium mt-[-20px] mb-3'>
//                     <li>
//                       <button
//                         onClick={async () => {
//                           if (getItemLocalStorage("token") && role === "user") {
//                             const propertyCountResponse =
//                               await getPropertyCountApi();
//                             if (
//                               propertyCountResponse?.data?.data?.totalBooking >
//                               0
//                             ) {
//                               router.push("/noticeboard-detail");
//                             } else if (
//                               propertyCountResponse?.data?.data
//                                 ?.totalBooking === 0
//                             ) {
//                               router.push("/noticeboard-detail");
//                             }
//                           } else {
//                             toast.error("Please Login !", {
//                               subText: "You need to be Logged In",
//                             });
//                           }
//                         }}
//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
//                       >
//                         Noticeboard
//                       </button>
//                     </li>
//                     <li>
//                       <Link
//                         href='/services/mixride'
//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
//                         prefetch={false}
//                       >
//                         Mix Ride
//                       </Link>
//                     </li>
//                     <li>
//                       <Link
//                         href='/services/mixcreators'
//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
//                         prefetch={false}
//                       >
//                         Mix Creators
//                       </Link>
//                     </li>
//                     <li>
//                       <Link
//                         href='/services/mixmate'
//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
//                         prefetch={false}
//                       >
//                         Mix Mate
//                       </Link>
//                     </li>
//                     <li>
//                       <Link
//                         href='/services/events'
//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
//                         prefetch={false}
//                       >
//                         Events
//                       </Link>
//                     </li>
//                   </ul>
//                 )}
//               </li>
//               {/* Company */}
//               <li>
//                 <div
//                   className={`flex justify-between items-center mb-5 cursor-pointer border border-solid border-[#EEEEEE]  py-3 px-4 rounded-full ${
//                     openSections.company &&
//                     "bg-[#D9F9F6] !mb-0 text-black !border-[#D9F9F6]"
//                   }`}
//                   onClick={() => toggleSection("company")}
//                 >
//                   <Link
//                     href='/company'
//                     className='sm:text-base text-sm font-[600] text-black'
//                     prefetch={false}
//                   >
//                     Company
//                   </Link>
//                   {openSections.company ? <ChevronUp /> : <ChevronDown />}
//                 </div>
//                 {openSections.company && (
//                   <ul className='text-base font-medium mt-[-20px] mb-3'>
//                     <li>
//                       <Link
//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
//                         href='/aboutus'
//                         prefetch={false}
//                       >
//                         About Us
//                       </Link>
//                     </li>
//                     <li>
//                       <Link
//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
//                         href='/company/rewards'
//                         prefetch={false}
//                       >
//                         Rewards
//                       </Link>
//                     </li>
//                     <li>
//                       <Link
//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
//                         href='blog'
//                         prefetch={false}
//                       >
//                         Blogs
//                       </Link>
//                     </li>
//                     <li>
//                       <button
//                         onClick={openContactPopup}
//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
//                       >
//                         Contact Us
//                       </button>
//                     </li>
//                   </ul>
//                 )}
//               </li>
//               {/* Help */}
//               <li>
//                 <div
//                   className={`flex justify-between items-center mb-6 cursor-pointer border border-solid border-[#EEEEEE]  py-3 px-4 rounded-full ${
//                     openSections.help &&
//                     "bg-[#D9F9F6] text-black !border-[#D9F9F6]"
//                   }`}
//                   onClick={() => toggleSection("help")}
//                 >
//                   <Link
//                     href='/help'
//                     className='sm:text-base text-sm font-[600] text-black'
//                     prefetch={false}
//                   >
//                     Help
//                   </Link>
//                   {openSections.help ? <ChevronUp /> : <ChevronDown />}
//                 </div>
//                 {openSections.help && (
//                   <ul className='text-base font-medium mt-[-20px] mb-3'>
//                     <li>
//                       <Link
//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
//                         href='faqs'
//                         prefetch={false}
//                       >
//                         FAQs
//                       </Link>
//                     </li>
//                     <li>
//                       <Link
//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
//                         href='privacypolicy'
//                         prefetch={false}
//                       >
//                         Privacy Policy
//                       </Link>
//                     </li>
//                     <li>
//                       <Link
//                         className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
//                         href='terms-condition'
//                         prefetch={false}
//                       >
//                         Terms and Conditions
//                       </Link>
//                     </li>
//                   </ul>
//                 )}
//               </li>
//               {/* My Account */}
//               <li>
//                 <div
//                   className={`flex justify-between items-center mb-5 cursor-pointer border border-solid border-[#EEEEEE]  py-3 px-4 rounded-full ${
//                     openSections.account &&
//                     "bg-[#D9F9F6] text-black !border-[#D9F9F6]"
//                   }`}
//                   onClick={() => toggleSection("account")}
//                 >
//                   <Link
//                     href='/account'
//                     className='sm:text-base text-sm font-[600] text-black'
//                     prefetch={false}
//                   >
//                     My Account
//                   </Link>
//                   {openSections.account ? <ChevronUp /> : <ChevronDown />}
//                 </div>
//                 {openSections.account &&
//                   (!hasToken ? (
//                     <ul className='text-base font-medium mt-[-20px] mb-3'>
//                       <li>
//                         <button
//                           className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
//                           onClick={handleLoginClick}
//                         >
//                           Login
//                         </button>
//                       </li>
//                       <li>
//                         <button
//                           onClick={handleLoginClick}
//                           className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
//                         >
//                           Signup
//                         </button>
//                       </li>
//                     </ul>
//                   ) : (
//                     <ul className='text-base font-medium mt-[-20px] mb-3'>
//                       <li>
//                         <Link
//                           className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
//                           href={
//                             role === "user"
//                               ? "/my-profile"
//                               : "/owner/dashboard/profile"
//                           }
//                           prefetch={false}
//                         >
//                           Profile
//                         </Link>
//                       </li>
//                       <li>
//                         <button
//                           onClick={handleLogout}
//                           className='sm:text-[16px] text-sm font-[600] block  text-black hover:text-primary-blue justify-between items-center cursor-pointer py-3 px-4'
//                         >
//                           Logout
//                         </button>
//                       </li>
//                     </ul>
//                   ))}
//               </li>
//             </ul>
//           </div>
//         </div>
//         {isContactPopupOpen && (
//           <ContactPopup
//             isOpen={isContactPopupOpen}
//             onClose={closeContactPopup}
//           />
//         )}
//       </>
//     );
// };

// export default menuPopupMobile;

import React, { useEffect, useRef, useState } from "react";
import Link from "next/link";
import {
  getItemLocalStorage,
  removeItemLocalStorage,
} from "@/utils/browserSetting";
import ContactPopup from "./contactPopup";

import { FaRegUser } from "react-icons/fa";
import {
  MdCardMembership,
  MdLogout,
  MdOutlineTravelExplore,
} from "react-icons/md";
import { GrEdit } from "react-icons/gr";
import { IoWalletOutline } from "react-icons/io5";
import { HiOutlineQuestionMarkCircle } from "react-icons/hi";
import { GoHeart } from "react-icons/go";
import toast from "react-hot-toast";
import { useNavbar } from "../home/<USER>";
import { removeFirebaseToken } from "@/services/ownerflowServices";
import { useRouter } from "next/router";

const menuPopupMobile = ({ isOpen, toggleMenu }) => {
  // eslint-disable-next-line no-unused-vars
  const [hasToken, setHasToken] = useState(false);
  const [isContactPopupOpen, setIsContactPopupOpen] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [role, setRole] = useState(false);
  const modalRef = useRef(null);
  const router = useRouter();

  const menuItems = [
    {
      icon: <FaRegUser className='text-xl' />,
      label: "My Profile",
      href: "/my-profile?section=profile",
    },
    { icon: <GoHeart className='text-xl' />, label: "Wishlist", href: "#" },
    {
      icon: <GrEdit className='text-xl' />,
      label: "Edit Details",
      href: "/my-profile?section=edit",
    },
    {
      icon: <MdCardMembership className='text-xl' />,
      label: "Membership",
      href: "/my-profile?section=membership",
    },
    {
      icon: <MdOutlineTravelExplore className='text-xl' />,
      label: "My Trips",
      href: "/my-profile?section=stay",
    },
    {
      icon: <IoWalletOutline className='text-xl' />,
      label: "My Wallet",
      href: "/my-profile?section=wallet",
    },
    {
      icon: <HiOutlineQuestionMarkCircle className='text-xl' />,
      label: "Help",
      href: "/my-profile?section=help",
    },
  ];

  useEffect(() => {
    const token = getItemLocalStorage("token");
    setHasToken(!!token);
  }, []);

  useEffect(() => {
    const role = getItemLocalStorage("role");
    setRole(role);
  }, []);

  const closeContactPopup = () => {
    setIsContactPopupOpen(false);
  };

  const [isClosing, setIsClosing] = useState(false);

  // Close modal when clicking outside
  useEffect(() => {
    const handleOutsideClick = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        setIsClosing(true);
        setTimeout(() => {
          setIsClosing(false);
          toggleMenu(); // this sets isOpen to false
        }, 300);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleOutsideClick);
    }

    return () => {
      document.removeEventListener("mousedown", handleOutsideClick);
    };
  }, [isOpen, toggleMenu]);

  if (!isOpen) return null;

  const closeWithAnimation = () => {
    setIsClosing(true);
    setTimeout(() => {
      setIsClosing(false);
      toggleMenu(); // this sets isOpen to false
    }, 300); // match this with your animation duration
  };

  const { updateUserStatus, updateUserRole } = useNavbar();

  const handleLogout = async () => {
    removeItemLocalStorage("token");
    updateUserStatus("");
    updateUserRole("");
    removeItemLocalStorage("name");
    removeItemLocalStorage("role");
    removeItemLocalStorage("email");
    removeItemLocalStorage("contact");
    toggleMenu();

    toast.success("Logged out successfully");
    const payload = {
      token: getItemLocalStorage("FCT"),
      userId: getItemLocalStorage("id"),
    };
    try {
      await removeFirebaseToken(payload);
      console.log("FCM token removed successfully.");
      removeItemLocalStorage("FCT");
    } catch (error) {
      console.error("Error removing FCM token:", error);
    }
    removeItemLocalStorage("id");
    router.push("/");
  };

  return (
    <>
      {isOpen && (
        <div
          className={`fixed inset-0 bg-black bg-opacity-70 z-50 backdrop-blur-sm transition-all duration-300 ${
            isClosing ? "animate-slideBackdropOut" : "animate-slideBackdropIn"
          }`}
        ></div>
      )}

      <div
        className={`fixed xs:w-[60%] w-[80%] h-[98%] sm:h-full sm:w-[400px] top-2 sm:top-[120px] right-0 sm:right-[120px] sm:left-auto 
            lg:bottom-0 rounded-tl-2xl rounded-bl-2xl pl-[2px] pb-[2px] z-50 flex items-start justify-end sm:bg-transparent bg-black
            bg-opacity-[70%] transition-all duration-300 animated ${
              isClosing ? "animate-fadeOutRight" : isOpen ? "fadeInRight" : ""
            }`}
      >
        <div
          ref={modalRef}
          className='bg-white rounded-tl-2xl rounded-bl-2xl w-[100%] max-w-full xs:p-5 p-3 ml-0 mr-0 mt-0 h-full font-manrope'
        >
          <div className='flex items-center justify-between mb-6'>
            <span className='text-[#40E0D0] flex text-2xl font-extrabold'>
              Mix<p className='text-black text-2xl font-extrabold '>Dorm</p>
            </span>
            <button onClick={closeWithAnimation} className='text-black'>
              ✕
            </button>
          </div>
          {menuItems.map((item, idx) => (
            <Link
              key={idx}
              href={item.href}
              className='flex items-center xs:gap-3 gap-2 bg-[#D9F9F6] hover:bg-primary-blue transition-colors xs:p-3 py-2 px-3 rounded-full text-sm font-medium text-gray-800 mt-2'
            >
              <span className='xs:text-lg text-sm'>{item.icon}</span>
              {item.label}
            </Link>
          ))}
          <button
            onClick={handleLogout}
            className='flex items-center sm:gap-3 gap-2 bg-[#D9F9F6] hover:bg-primary-blue transition-colors p-3 rounded-full text-sm font-medium text-gray-800 sm:mt-2 mt-1 w-full'
          >
            <span className='sm:text-lg text-base'>
              <MdLogout className='text-xl' />
            </span>
            Logout
          </button>
        </div>
      </div>
      {isContactPopupOpen && (
        <ContactPopup isOpen={isContactPopupOpen} onClose={closeContactPopup} />
      )}
    </>
  );
};

export default menuPopupMobile;
