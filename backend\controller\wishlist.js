// controller/propertyLikeController.js
import property from '../models/properties.js';
import PropertyLike from '../models/wishLists.js';
import Response from '../utills/response.js'

/**
 * Like or Unlike a property based on the isLike flag
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */

export const likeOrUnlikeProperty = async (req, res) => {
  const { propertyId } = req.params;
  const { isLike } = req.body;
  const userId = req.user._id;

  try {
    const propertyData = await property.findById(propertyId).lean(); // Fetch property data as plain object
    if (!propertyData) {
      return res.status(404).json({ message: 'Property not found' });
    }

    const existingLike = await PropertyLike.findOne({ user: userId, property: propertyId });

    if (isLike) {
      if (!existingLike) {
        await PropertyLike.create({ user: userId, property: propertyId });
        return Response.Created(res, {
          ...propertyData,
          liked: true
        }, 'Property liked successfully.');
      } else {
        return res.status(400).json({ message: 'Property already liked' });
      }
    } else {
      if (existingLike) {
        await PropertyLike.deleteOne({ user: userId, property: propertyId });
        return res.status(200).json({
          ...propertyData,
          liked: false,
          message: 'Property unliked successfully'
        });
      } else {
        return res.status(400).json({ message: 'Property not liked yet' });
      }
    }

  } catch (error) {
    console.error(error);
    return res.status(500).json({ message: 'Internal Server Error' });
  }
};
/**
 * Get all wishlist properties for the logged-in user
 * @param {object} req - Express request object
 * @param {object} res - Express response object
 */
export const getUserWishlistProperties = async (req, res) => {
  const userId = req.user?._id;

  try {
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Step 1: Get all property IDs liked by the user
    const wishlistItems = await PropertyLike.find({ user: userId }).select('property');

    const propertyIds = wishlistItems.map(item => item.property);

    // Step 2: Fetch full property details
    const properties = await property.find({ _id: { $in: propertyIds } })
      .lean();

    // Step 3: Add `liked: true` flag to each property
    const responseProperties = properties.map(p => ({
      ...p,
      liked: true
    }));

    return Response.OK(res, responseProperties, 'User wishlist properties fetched successfully.');
  } catch (error) {
    console.error(error);
    return Response.InternalServerError(res, null, error.message);
  }
};
