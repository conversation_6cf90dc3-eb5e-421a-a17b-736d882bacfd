import Link from "next/link";
import React from "react";

const NotificationDetails = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616] h-screen">
      <div className="flex items-center justify-between w-full ">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Notification Details
        </h2>
      </div>
      <div className="bg-white border rounded-xl mt-5 h-auto dark:bg-black dark:border-none">
        <div className="p-8 flex flex-col gap-y-6">
          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">
              Notification Information
            </h1>
            <div className="flex flex-wrap items-center my-6 gap-6">
              <div className="flex w-full sm:w-[45%] md:w-[50%] lg:w-[25%] flex-col mb-0  lg:mb-5 ">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  Title
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  River Rafting in rishikesh
                </p>
              </div>
              <div className="flex sm:w-[90%] md:w-[100%] lg:w-[55%] flex-col mb-0 md:mb-3 lg:mb-4">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  Message
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  Amet minim mollit non deserunt ullamco est sit aliqua dolor do
                  amet sint
                </p>
              </div>
              <div className="flex w-full sm:w-[45%] md:w-[45%] lg:w-[25%] flex-col mb-0  lg:mb-5 ">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  Time
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  03:50
                </p>
              </div>
              <div className="flex w-full sm:w-[45%] md:w-[45%] lg:w-[25%] flex-col mb-0  lg:mb-5 ">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  Date
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  04/06/2024
                </p>
              </div>
            </div>
            <div>
              <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">
                Status Information
              </h1>
              <div className="flex items-center gap-x-20 mt-6">
                <div className="flex w-[15%] flex-col">
                  <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                    Status
                  </strong>
                  <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                    Sent
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-start justify-start">
            <Link
              href={"/superadmin/dashboard/notifications"}
              className="text-white py-2 w-32 rounded bg-sky-blue-650 flex items-center justify-center"
            >
              Cancel
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationDetails;
