import React, { useState, Fragment } from "react";
import dynamic from "next/dynamic";
import Image from "next/image";
import "react-datepicker/dist/react-datepicker.css";
import { <PERSON>u, MenuButton, MenuItem, MenuItems } from "@headlessui/react";
import { Trash, MoreVertical, Penci<PERSON>, Eye } from "lucide-react";

import { Dialog, Transition } from "@headlessui/react";

const Editcreator = dynamic(
  () => import("../../../components/ownerFlow/dashboard/editcreator"),
  {
    ssr: false,
  }
);

const Creatordetails = dynamic(
  () => import("../../../components/ownerFlow/dashboard/creatordetails"),
  {
    ssr: false,
  }
);

const Creator = ({ setActiveTab }) => {
  const [openEditcreator, setopenEditcreator] = useState(false);
  const closecreatoreditModal = () => setopenEditcreator(false);
  const [openViewcreator, setopenViewcreator] = useState(false);
  const closecreatorviewModal = () => setopenViewcreator(false);

  return (
    <>
      <div className="overflow-x-auto  mt-5 rounded-md">
        <table className="w-full border rounded-t-lg border-gray-340">
          <thead>
            <tr>
              <th className="px-2 py-2.5 bg-[#EEEEEE] font-medium text-xs text-black text-left whitespace-nowrap">
                Offer ID
              </th>
              <th className="bg-[#EEEEEE] font-medium text-xs text-black text-left px-2 py-2.5 whitespace-nowrap">
                Name
              </th>
              <th className="bg-[#EEEEEE] font-medium text-xs text-black text-left px-2 py-2.5 whitespace-nowrap">
                Profile
              </th>
              <th className="bg-[#EEEEEE] font-medium text-xs text-black text-left px-2 py-2.5 whitespace-nowrap">
                Follower
              </th>
              <th className="bg-[#EEEEEE] font-medium text-xs text-black text-left px-2 py-2.5 whitespace-nowrap">
                Average Likes
              </th>
              <th className="bg-[#EEEEEE] font-medium text-xs text-black text-left px-2 py-2.5 whitespace-nowrap">
                Post
              </th>
              <th className="bg-[#EEEEEE] font-medium text-xs text-black text-left px-2 py-2.5 whitespace-nowrap">
                Engagement rate
              </th>
              <th className="bg-[#EEEEEE] font-medium text-xs text-black text-left px-2 py-2.5 whitespace-nowrap">
                Profile
              </th>
              <th className="bg-[#EEEEEE] font-medium text-xs text-black text-center px-2 py-2.5 whitespace-nowrap">
                Send Offer
              </th>
              <th className="bg-[#EEEEEE] font-medium text-xs text-black text-center px-2 py-2.5"></th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="text-xs font-semibold p-2 border-b">#01</td>
              <td className="text-xs font-normal p-2 border-b">Alexander</td>
              <td className="text-xs font-normal p-2 border-b">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mix-profile.png`}
                  alt="profile"
                  title="profile"
                  width={32}
                  height={32}
                  className="object-cover"
                  loading="lazy"
                />
              </td>
              <td className="text-xs font-semibold p-2 border-b">158.k</td>
              <td className="text-xs font-semibold p-2 border-b">1158.5k</td>
              <td className="text-xs font-semibold p-2 border-b">52525</td>
              <td className="text-xs font-semibold p-2 border-b">50%</td>
              <td className="text-xs p-2 border-b">
                <div className="inline-flex relative w-24">
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mix-profile.png`}
                    alt="profile"
                    title="profile"
                    width={32}
                    height={32}
                    className="object-cover z-0"
                    loading="lazy"
                  />
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mix-profile.png`}
                    alt="profile"
                    title="profile"
                    width={32}
                    height={32}
                    className="object-cover absolute right-9 z-10"
                    loading="lazy"
                  />
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mix-profile.png`}
                    alt="profile"
                    title="profile"
                    width={32}
                    height={32}
                    className="object-cover absolute right-2 z-20"
                    loading="lazy"
                  />
                </div>
              </td>
              <td className=" p-2 border-b text-center">
                <button
                  className="text-xs font-inter font-normal text-[#1570ef] bg-[#C8DAF4] rounded-2xl p-2 px-3 whitespace-nowrap"
                  onClick={() => setActiveTab(2)}
                >
                  Offer Send
                </button>
              </td>
              <td className=" p-2 border-b text-center">
                <Menu as="div" className="relative inline-block text-left">
                  <div>
                    <MenuButton>
                      <MoreVertical aria-hidden="true" size={16}></MoreVertical>
                    </MenuButton>
                  </div>

                  <MenuItems
                    // transition
                    // ref={(el) => {
                    //   if (el) {
                    //     const rect = el.getBoundingClientRect();
                    //     const windowHeight = window.innerHeight;
                    //     const isLastItem = index === properties.length - 1;
                    //     const isSecondLastItem =
                    //       index === properties.length - 2;
                    //     const isOnlyItem = properties.length === 1;
                    //     const hasMoreThanTwoItems = properties.length > 2;

                    //     // Clear previous classes
                    //     el.classList.remove(
                    //       "bottom-full",
                    //       "mb-2",
                    //       "mt-2",
                    //       "top-1/2",
                    //       "-translate-y-1/2"
                    //     );

                    //     if (isOnlyItem) {
                    //       el.classList.add("top-1/2", "-translate-y-1/2");
                    //     } else if (
                    //       isLastItem ||
                    //       (hasMoreThanTwoItems && isSecondLastItem) ||
                    //       rect.bottom > windowHeight
                    //     ) {
                    //       el.classList.add("bottom-full", "mb-2");
                    //     } else {
                    //       el.classList.add("mt-2");
                    //     }
                    //   }
                    // }}
                    
                    ref={(el) => {
                      if (el) {
                        const rect = el.getBoundingClientRect();
                        const windowHeight = window.innerHeight;
                        if (rect.bottom > windowHeight) {
                          el.classList.add("bottom-full", "mb-2"); // Open upwards
                        } else {
                          el.classList.remove("bottom-full", "mb-2"); // Default downwards
                        }
                      }
                    }}
                    className="absolute right-0 z-10 w-max origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 transition transform focus:outline-none data-[closed]:scale-95 data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
                  >
                    <div>
                      <MenuItem>
                        <button
                          className="px-4 py-2 text-sm w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-t-md"
                          onClick={() => {
                            setopenViewcreator(true);
                          }}
                        >
                          <Eye size={16}></Eye>
                          View
                        </button>
                      </MenuItem>
                      <MenuItem>
                        <button
                          className="px-4 py-2 text-sm w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-t-md"
                          onClick={() => {
                            setopenEditcreator(true);
                          }}
                        >
                          <Pencil size={16}></Pencil>
                          Edit
                        </button>
                      </MenuItem>
                      <MenuItem>
                        <button className="px-4 py-2 text-sm text-red-600 w-full flex items-center gap-1.5 data-[focus]:bg-gray-100 data-[focus]:outline-none rounded-b-md">
                          <Trash size={16} className="text-red-600" />{" "}
                          <span>Delete</span>
                        </button>
                      </MenuItem>
                    </div>
                  </MenuItems>
                </Menu>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <Transition show={openEditcreator} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-50"
          onClose={closecreatoreditModal}
        >
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <h2 className="page-title">Edit Creator</h2>
                    <button
                      onClick={closecreatoreditModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <Editcreator
                      closecreatoreditModal={closecreatoreditModal}
                    ></Editcreator>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      <Transition show={openViewcreator} as={Fragment}>
        <Dialog
          as="div"
          className="relative z-50"
          onClose={closecreatorviewModal}
        >
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[41%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <h2 className="page-title">View Creator</h2>
                    <button
                      onClick={closecreatorviewModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <Creatordetails
                      closecreatorviewModal={closecreatorviewModal}
                    ></Creatordetails>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  );
};

export default Creator;
