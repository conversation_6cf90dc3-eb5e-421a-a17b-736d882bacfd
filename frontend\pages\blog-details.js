import React from "react";
import Image from "next/image";
import { FaPaperPlane } from "react-icons/fa";
import { MdOutlineCalendarMonth } from "react-icons/md";
import {
  getBlogDetailsApi,
  newsLetterSubscribeApi,
} from "@/services/webflowServices";
import { useEffect, useRef, useState } from "react";
import { useRouter } from "next/router";
import dynamic from "next/dynamic";
import toast from "react-hot-toast";
import Head from "next/head";
import { getContentSchema } from "@/lib/schema/contentSchema";
// import Link from "next/link";
// import { CalendarDays } from "lucide-react";
// import { Button } from "@mui/material";

const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

export async function getServerSideProps(context) {
  const { id } = context.query;
  let blogDetailsData = {};
  if (id) {
    try {
      const response = await getBlogDetailsApi(id);
      blogDetailsData = response?.data?.data || {};
    } catch (e) {
      console.error("Error fetching blog details:", e);
      // handle error, maybe redirect or show 404
    }
  }

  // Build the full URL
  const protocol = context.req.headers["x-forwarded-proto"] || "https";
  const host = context.req.headers.host;
  const url = `${protocol}://${host}/blog-details?id=${id}`;

  return {
    props: {
      blogDetailsData,
      fullUrl: url,
    },
  };
}

const BlogDetails = ({ blogDetailsData, fullUrl }) => {
  const [loading, setLoading] = useState(true);
  const [email, setEmail] = useState("");
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageFailed, setImageFailed] = useState(false);

  const isFirstRender = useRef(null);
  const router = useRouter();

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubscribe = async () => {
    if (!email) {
      toast.error("Email is required");
      return;
    }
    if (!validateEmail(email)) {
      toast.error("Please enter a valid email address");
      return;
    }

    setLoading(true);
    try {
      const response = await newsLetterSubscribeApi({ email });
      if (response?.data?.status) {
        toast.success(response?.data?.message);
        setEmail("");
      } else {
        toast.error(response?.data?.message);
      }
      console.log("Subscribed successfully:", response.data);
    } catch (error) {
      console.error("Subscription failed:", error);
    }
    setLoading(false);
  };

  useEffect(() => {
    const fetchBlogDetailsData = async () => {
      setLoading(true);
      try {
        const { id } = router.query;
        if (id) {
          // const response = await getBlogDetailsApi(id);
          // setBlogDetailsData(response?.data?.data || []); // This line is removed as blogDetailsData is now passed as a prop
        }
      } catch (error) {
        console.error("Error fetching stay data:", error);
      } finally {
        setLoading(false);
      }
    };
    if (!isFirstRender.current) {
      fetchBlogDetailsData();
    } else {
      isFirstRender.current = false;
    }
  }, [router]);

  const contentSchema = blogDetailsData?.title
    ? getContentSchema({
        title: blogDetailsData.title,
        description: blogDetailsData.description,
        authorName: blogDetailsData.authorName || "Aayush",
        datePublished: blogDetailsData.createdAt,
        dateModified: blogDetailsData.updatedAt,
        imageUrl: blogDetailsData.images?.[0] || "",
        url: fullUrl,
      })
    : null;

  return (
    <>
      <Head>
        <title>{blogDetailsData?.title} | Mixdorm Blog</title>
        {contentSchema && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(contentSchema) }}
          />
        )}
      </Head>
      <Loader open={loading} />
      {/* <SearchNavbar /> */}
      {loading ? (
        <div className="bg-white md:pb-16 font-manrope md:pt-12 py-8">
          <div className="container md:px-8 lg:px-12 xl:px-16 flex flex-col md:flex-row items-start gap-x-5 justify-between">
            <div className="md:w-[70%] w-full md:mb-0 mb-5">
              {/* Title Skeleton */}
              <div className="h-8 w-3/4 bg-gray-200 rounded mb-4 animate-pulse"></div>

              {/* Date/Info Skeleton */}
              <div className="flex space-x-4 pb-7">
                <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-16 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
              </div>

              {/* Main Image Skeleton */}
              <div className="w-full h-96 bg-gray-200 rounded-3xl mb-4 animate-pulse"></div>

              {/* Content Paragraphs Skeleton */}
              <div className="flex flex-col gap-4">
                <div className="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-5/6 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-4/5 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-3/4 bg-gray-200 rounded animate-pulse"></div>
              </div>

              {/* Newsletter Skeleton */}
              <div className="flex justify-center items-center bg-gray-200 rounded-3xl shadow-lg mb-6 animate-pulse">
                <div className="py-12 px-2 max-w-md w-full text-center">
                  <div className="h-6 w-32 bg-gray-300 rounded mx-auto mb-4"></div>
                  <div className="h-4 w-48 bg-gray-300 rounded mx-auto mb-6"></div>
                  <div className="relative flex items-center bg-white rounded-full p-3 shadow-inner">
                    <div className="flex-grow h-8 bg-gray-200 rounded-full"></div>
                    <div className="absolute right-0 h-10 w-10 bg-gray-400 rounded-full"></div>
                  </div>
                </div>
              </div>

              {/* Additional Content Skeleton */}
              <div className="flex flex-col gap-4 mt-4">
                <div className="h-6 w-1/2 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-full bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 w-5/6 bg-gray-200 rounded animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-white md:pb-16 font-manrope md:pt-12 py-8">
          <div className="container md:px-8 lg:px-12 xl:px-16 flex flex-col md:flex-row items-start gap-x-5  justify-between ">
            <div className="md:w-[70%] w-full md:mb-0 mb-5 ">
              <h2 className="md:text-2xl sm:text-xl text-lg mb-4 ">
                {blogDetailsData?.title}
              </h2>
              <div className="flex space-x-4 pb-7 ">
                <div className="flex items-center space-x-1">
                  <MdOutlineCalendarMonth className="text-teal-400" />
                  <h3 className="text-black/50 xs:text-base text-sm">
                    {new Date(blogDetailsData?.createdAt).toLocaleDateString(
                      "en-US",
                      {
                        year: "numeric",
                        month: "short",
                        day: "numeric",
                      }
                    )}
                  </h3>
                </div>
                {/* <div className="flex items-center space-x-1">
                <FaRegClock className="text-teal-400" />
                <h3 className="text-black/50 xs:text-base text-sm">6 mins</h3>
              </div> */}
                {/* <div className="flex items-center space-x-1">
                <BsChatDots className="text-teal-400" />
                <h3 className="text-black/50 xs:text-base text-sm">
                  38 comments
                </h3>
              </div> */}
              </div>
              <div className="w-full rounded-3xl mb-4">
                {blogDetailsData?.images?.[0] ? (
                  <div className="relative">
                    {(!imageLoaded || imageFailed) && (
                      <div className="w-full h-96 bg-gray-200 flex justify-center items-center rounded-3xl animate-pulse">
                        <h1 className="text-gray-400 font-bold font-manrope text-4xl">
                          MixDorm
                        </h1>
                      </div>
                    )}
                    {!imageFailed && (
                      <Image
                        src={blogDetailsData.images[0]}
                        alt="Main Post"
                        className={`w-full rounded-3xl ${
                          !imageLoaded ? "opacity-0" : "opacity-100"
                        }`}
                        width={830}
                        height={320}
                        loading="lazy"
                        onLoadingComplete={() => setImageLoaded(true)}
                        onError={() => setImageFailed(true)}
                      />
                    )}
                  </div>
                ) : (
                  <div className="w-full h-96 bg-gray-200 flex justify-center items-center rounded-3xl">
                    <h1 className="text-gray-400 font-bold font-manrope text-4xl">
                      MixDorm
                    </h1>
                  </div>
                )}
              </div>
              {/* <p className="text-center mb-4 text-black text-sm font-medium">
              Nayza's Figma builder — Design your next ecommerce project
              instantly
            </p> */}
              <div className="flex flex-col gap-4 txet-base leading-7">
                <p className="text-gray-500 sm:text-lg text-base">
                  {blogDetailsData?.description}
                </p>
                {/* <p className="text-gray-500 sm:text-lg text-base">
                But I must explain to you how all this mistaken idea of
                denouncing pleasure and praising pain was born and I will give
                you a complete account of the system, and expound the actual
                teachings of the great explorer of the truth, the master-builder
                of human happiness. No one rejects, dislikes, or avoids pleasure
                itself
              </p>
              <p className="text-gray-500 sm:text-lg text-base">
                On the other hand, we denounce with righteous indignation and
                dislike men who are so beguiled and demoralized by the charms of
                pleasure of the moment, so blinded by desire, that they cannot
                foresee the pain and trouble that are bound to ensue; and equal
                blame belongs to those who fail in their duty through weakness
                of will, which is the same as saying through.
              </p> */}
                {/* <div className="flex justify-center items-center bg-teal-400 rounded-3xl shadow-lg mb-6">
                  <div className=" py-12 px-2 max-w-md w-full text-center">
                    <h2 className="text-xl font-bold mb-2">Newsletter</h2>
                    <p className="text-sm text-gray-700 mb-4">
                      Subscribe to our weekly Newsletter and receive updates via
                      email.
                    </p>
                    <div className="relative flex items-center bg-white rounded-full p-3 shadow-inner">
                      <input
                        type="email"
                        placeholder="Email*"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="flex-grow bg-transparent border-none outline-none px-4 pr-16"
                      />
                      <button
                        type="submit"
                        className="absolute sm:-right-2 right-0 bg-black text-white p-4 rounded-full flex items-center justify-center"
                        onClick={handleSubscribe}
                      >
                        <FaPaperPlane />
                      </button>
                    </div>
                  </div>
                </div> */}
                {/* <h3 className="font-semibold md:text-2xl text-lg">
                Keep your everyday on trend
              </h3> */}
                {/* <p className="text-gray-500 sm:text-lg text-base">
                Nayzak, everyone in my team works towards the samegoal. This
                enabled our teams to ship new ideas and feel more capable.
                Podcasting operational — change management inside of workflows.
                Completely synergize.
              </p>
              <p className="text-gray-500 sm:text-lg text-base">
                But I must explain to you how all this mistaken idea of
                denouncing pleasure and praising pain was born and I will give
                you a complete account of the system, and expound the actual
                teachings of the great explorer of the truth, the master-builder
                of human happiness. No one rejects, dislikes, or avoids pleasure
                itself
              </p> */}
                {/* <div>
                {" "}
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/blog-main.svg`}
                  alt="Main Post"
                  className="w-full rounded-3xl mb-4"
                  width={830}
                  height={320}
                  loading="lazy"
                />
                <p className="text-center mb-4 text-black text-sm font-medium">
                  Nayza's Figma builder — Design your next ecommerce project
                  instantly
                </p>
                <div div className="flex flex-col gap-4">
                  <p className="text-gray-500 sm:text-lg text-base">
                    Nayzak, everyone in my team works towards the samegoal. This
                    enabled our teams to ship new ideas and feel more capable.
                    Podcasting operational — change management inside of
                    workflows. Completely synergize.
                  </p>
                  <p className="text-gray-500 sm:text-lg text-base">
                    But I must explain to you how all this mistaken idea of
                    denouncing pleasure and praising pain was born and I will
                    give you a complete account of the system, and expound the
                    actual teachings of the great explorer of the truth, the
                    master-builder of human happiness. No one rejects, dislikes,
                    or avoids pleasure itself
                  </p>
                </div>
              </div> */}
              </div>
            </div>
            {/* <div className="md:w-[30%] w-full flex flex-col gap-4 items-center justify-center">
            <div className="relative">
              <Image
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/single_blog_sidebar1.png`}
                alt="Mix-suffle"
                width={357}
                height={213}
                loading="lazy"
              />
              <Button className="absolute top-0 right-0 text-white text-2xl">
                <IoCloseCircleOutline />
              </Button>
              <div className="absolute px-5 left-0 w-full bottom-1">
                <div className="text-center bg-white bg-opacity-5 rounded-[30px] xl:pb-5 py-3 px-3 backdrop-blur-lg">
                  <h2 class="font-bold text-center text-white font-manrope mb-0  xl:text-4xl lg:text-2xl text-xl">
                    Mix <span class="text-primary-blue">Shuffle</span>
                  </h2>
                  <p className="text-white lg:text-sm text-xs">
                    Stay More than Six days and get{" "}
                    <br className="xl:block hidden" /> Premium membership{" "}
                  </p>
                </div>
              </div>
            </div>
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Mix-ride.png`}
              width={357}
              height={465}
              alt=""
            />
          </div> */}
            <div className="md:w-[30%] w-full flex flex-col  items-center justify-center ">
              <div className="grid grid-cols-1 xl:gap-6 gap-3 w-full px-6 mt-2 md:mt-[100px] ">
                {/* {blogData?.slice(1).map((item, index) => (
                  <div
                    key={index}
                    className="bg-white border-2 border-[#EEEEEE] rounded-3xl overflow-hidden shadow-sm flex flex-col max-h-[300px]"
                  >
                    <div className="relative">
                    
                      {item?.images?.[0] ? (
                        <div className="relative">
                          {(!imageLoaded || imageFailedMap[item._id]) && (
                            <div className="w-full h-36 bg-gray-200 flex justify-center items-center rounded-t-3xl animate-pulse">
                              <h1 className="text-gray-400 font-bold font-manrope text-4xl">
                                MixDorm
                              </h1>
                            </div>
                          )}
                          {!imageFailedMap[item._id] && (
                            <Image
                              src={item.images[0]}
                              alt={item?.title}
                              className={`w-full h-36 object-cover rounded-t-3xl ${
                                !imageLoaded ? "opacity-0" : "opacity-100"
                              }`}
                              width={500}
                              height={192}
                              onLoadingComplete={() => setImageLoaded(true)}
                              onError={() =>
                                setImageFailedMap((prev) => ({
                                  ...prev,
                                  [item._id]: true,
                                }))
                              }
                            />
                          )}
                        </div>
                      ) : (
                        <div className="w-full xs:h-[212px] h-[150px] bg-slate-200 flex justify-center items-center rounded-t-3xl">
                          <h1 className="text-white font-bold font-manrope">
                            MixDorm
                          </h1>
                        </div>
                      )}

                      <div className="absolute top-4 -left-1 right-0 flex items-center justify-between px-2">
                        <Link
                          href=""
                          className="bg-white px-3 py-1 rounded-full text-xs font-semibold "
                          prefetch={false}
                        >
                          {item?.categoryId?.title}
                        </Link>
                        <Button
                          className={`w-7 h-7 p-0 min-w-0 text-center rounded-full flex items-center justify-center 
                                      ${
                                        item?.liked
                                          ? "text-red-600"
                                          : "bg-white text-black hover:text-red-600 "
                                      }
                                    `}
                          onClick={() => HandleLike(item?.liked, item?._id)}
                        >
                          <FaHeart className="text-base" />
                        </Button>
                      </div>
                    </div>
                    <div className="px-3 pt-3 flex-grow flex flex-col ">
                      <div className="mb-1.5 xl:flex items-center lg:block flex">
                        <p className="flex items-center font-medium text-black/50  text-xs mb-1 xl:mb-0">
                          {" "}
                          <span>
                            <CalendarDays size={13} />
                          </span>
                          <span className="ml-1 mr-4">
                            {new Date(item?.createdAt).toLocaleDateString(
                              "en-US",
                              {
                                year: "numeric",
                                month: "short",
                                day: "numeric",
                              }
                            )}
                          </span>
                        </p>
                       
                      </div>
                      <h3>
                        <Link
                          href={`/blog-details?id=${item?._id}`}
                          className="text-sm lg:text-xs xl:text-sm font-bold hover:text-primary-blue block"
                          prefetch={false}
                        >
                          {item?.title}
                        </Link>
                      </h3>
                      <div className="xl:flex lg:block flex items-center mt-4 lg:mt-3 2xl:mt-1">
                        <Link
                          href={`/blog-details?id=${item?._id}`}
                          className="ml-auto bg-black text-white px-2 py-2 rounded-3xl text-[10px] font-semibold hover:bg-primary-blue transition-all text-center hover:text-black"
                          prefetch={false}
                        >
                          Keep Reading
                        </Link>
                      </div>
                    </div>
                  </div>
                ))} */}
                {Array(2)
                  .fill(0)
                  .map((_, index) => (
                    <div
                      key={index}
                      className="bg-white border-2 border-[#EEEEEE] rounded-3xl overflow-hidden shadow-sm flex flex-col max-h-[300px]"
                    >
                      <div className="relative">
                        <div className="relative">
                          <div className="w-full h-36 bg-gray-200 flex justify-center items-center rounded-t-3xl">
                            <h1 className="text-gray-400 font-bold font-manrope text-4xl">
                              MixDorm
                            </h1>
                          </div>
                        </div>

                        <div className="absolute top-4 -left-1 right-0 flex items-center justify-between px-2">
                          <div className="bg-white px-3 py-1 rounded-full text-xs font-semibold">
                            Category {index + 1}
                          </div>
                          <button className="w-7 h-7 p-0 min-w-0 text-center rounded-full flex items-center justify-center bg-white text-black hover:text-red-600">
                            <span className="text-base">❤</span>
                          </button>
                        </div>
                      </div>
                      <div className="px-3 pt-3 flex-grow flex flex-col">
                        <div className="mb-1.5 xl:flex items-center lg:block flex">
                          <p className="flex items-center font-medium text-black/50 text-xs mb-1 xl:mb-0">
                            <span className="mr-1">📅</span>
                            <span className="ml-1 mr-4">
                              {new Date().toLocaleDateString("en-US", {
                                year: "numeric",
                                month: "short",
                                day: "numeric",
                              })}
                            </span>
                          </p>
                        </div>
                        <h3>
                          <a
                            href="#"
                            className="text-sm lg:text-xs xl:text-sm font-bold hover:text-primary-blue block"
                          >
                            Solo traveling can be empowering and exciting {index + 1}
                          </a>
                        </h3>
                        <div className="xl:flex lg:block flex items-center mt-4 lg:mt-3 2xl:mt-1 my-3">
                          <a
                            href="#"
                            className="ml-auto bg-black text-white px-2 py-2 rounded-3xl text-[10px] font-semibold hover:bg-primary-blue transition-all text-center hover:text-black"
                          >
                            Keep Reading
                          </a>
                        </div>
                      </div>
                    </div>
                  ))}
                   <div className="flex justify-center items-center bg-primary-blue rounded-3xl shadow-lg mb-6">
                  <div className=" py-12 px-2 max-w-md w-full text-center">
                    <h2 className="text-xl font-bold mb-2">Newsletter</h2>
                    <p className="text-sm text-gray-700 mb-4">
                      Subscribe to our weekly Newsletter and receive updates via
                      email.
                    </p>
                    <div className="relative flex items-center bg-white rounded-full p-3 shadow-inner mx-1">
                      <input
                        type="email"
                        placeholder="Email*"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="flex-grow bg-transparent border-none outline-none px-4 pr-16"
                      />
                      <button
                        type="submit"
                        className="absolute sm:-right-2 right-0 bg-black text-white p-4 rounded-full flex items-center justify-center"
                        onClick={handleSubscribe}
                      >
                        <FaPaperPlane />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default BlogDetails;
