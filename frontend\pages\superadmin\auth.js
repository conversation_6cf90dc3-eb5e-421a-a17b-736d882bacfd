"use client";
import React, { useState } from "react";
import Image from "next/image";
import Button from "@/components/superadmin/Button";
import VerificationCode from "@/components/superadmin/VerificationCode";
import Input from "@/components/superadmin/Input";
import { verifyOtpAdmin, sendOtpAdminApi } from "@/services/adminflowServices";
import toast from "react-hot-toast";
import { useRouter } from "next/router";

const AuthenticationCode = () => {
  const [email, setEmail] = useState("");
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleLogin = async (e) => {
    e.preventDefault();
    if (!email) {
      toast.error("Please enter your email.");
      return;
    }
    if (otp.some((d) => d === "")) {
      toast.error("Please enter the 6-digit code.");
      return;
    }
    setLoading(true);
    try {
      const payload = { email, otp: otp.join("") };
      const response = await verifyOtpAdmin(payload);
      if (response?.data?.status) {
        toast.success(response?.data?.message || "Login successful!");
        router.push("/superadmin/dashboard");
      } else {
        toast.error(response?.data?.message || "Invalid code or email.");
      }
    } catch (error) {
      toast.error(
        error?.response?.data?.message || error.message || "Login failed."
      );
    } finally {
      setLoading(false);
    }
  };

  const handleResend = async () => {
    if (!email) {
      toast.error("Please enter your email to resend code.");
      return;
    }
    setLoading(true);
    try {
      const response = await sendOtpAdminApi({ email });
      if (response?.data?.status) {
        toast.success(
          response?.data?.message || `Verification code sent to ${email}`
        );
        setOtp(["", "", "", "", "", ""]);
      } else {
        toast.error(response?.data?.message || "Failed to resend code.");
      }
    } catch (error) {
      toast.error(
        error?.response?.data?.message || error.message || "Failed to resend code."
      );
    } finally {
      setLoading(false);
    }
  };

  const isOtpValid = otp.every((d) => d !== "");
  const isEmailValid = email && /.+@.+\..+/.test(email);

  return (
    <section className="flex flex-col md:flex-row w-full h-screen bg-[#50C2FF] md:bg-white lg:bg-white py-24 lg:py-0 md:py-0">
      {/* Left Section */}
      <div className="w-full md:w-1/2 flex items-center justify-center bg-[#50C2FF] md:bg-white lg:bg-white shadow-2xl ">
        <div className="flex flex-col w-[90%] md:w-[68%] px-8 py-10 md:px-6 md:py-16 bg-white border shadow">
          <Image
            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/logo.svg`}
            width={155}
            height={40}
            alt="Mixdorm"
            title="Mixdorm"
            className="object-contain w-fit h-fit max-w-[186px] max-h-11"
            loading="lazy"
          />
          <h2 className="mt-10 font-medium text-black text-xl md:text-xl lg:text-2xl">
            Enter Authentication Code
          </h2>
          <p className="mt-3 text-sm font-normal text-gray-500">
            Two-Factor Authentication (2FA)
          </p>
          <form onSubmit={handleLogin}>
            <Input
              type="email"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="mb-2 mt-6 text-black bg-[#EEF9FF] border border-gray-300"
              name="email"
            />
            <VerificationCode otp={otp} setOtp={setOtp} onResend={handleResend} />
            <Button
              text={
                loading ? (
                  <div className="absolute loader-button">
                    <div className="loader"></div>
                  </div>
                ) : (
                  "Login"
                )
              }
              type="submit"
              className="mt-2"
              disabled={loading || !isEmailValid || !isOtpValid}
            />
          </form>
        </div>
      </div>

      {/* Right Section */}
      <div className="hidden md:flex w-1/2 bg-[#50C2FF] items-end justify-start pt-10 pl-10">
        <Image
          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/superadmin_login_img.png`}
          width={720}
          height={1024}
          loading="lazy"
          alt="Admin dash"
          className="h-full w-full object-cover object-left-top"
        />
      </div>
    </section>
  );
};

export default AuthenticationCode;
