import mongoose from 'mongoose';

const eCheckInSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Types.ObjectId,
      ref: 'users',
    //  required: true,
    },
    booking: {
      type: mongoose.Types.ObjectId,
      ref: 'bookings',
      // required: true,
    },
    property: {
      type: mongoose.Types.ObjectId,
      ref: 'properties',
      required: true,
    },
    room :{
        type: mongoose.Types.ObjectId,
        ref: 'rooms',
    },
    checkIn: {
      type: Date,
      required: true,
    },
    checkOut: {
        type: Date,
        required: true,
      },
    guestDetails: {
      name: {
        type: String,
        required: true,
      },
      email: {
        type: String,
      //  required: true,
      },
      phone:{
        code:{
          type:String
        },
        number:{
          type:Number
        }
      },
      identification: [{
        url:{
            type:String
        }
      }],
      idNumber: {
        type: String,
      //  required: true,
      },
    },
    uploadedDocuments: {
      type: [{
        url:String
      }],
    },
    signature:{
      type:Object
    },
    status: {
      type: String,
      enum: ['pending', 'completed', 'failed'],
      default: 'pending',
    },
    fromLocation:{
      type:String
    },
    toLocation:{
      type:String
    },
    refNumber:{
      type:String,
      required:true
    },
    isDeleted:{
      type:Boolean,
      default:false
    }
  },
  {
    timestamps: true,
  }
);

const ECheckIn = mongoose.model('eCheckIn', eCheckInSchema);
export default ECheckIn;
