import mongoose from "mongoose";

// MongoDB connection URI
const MONGO_URI = "mongodb+srv://mixdormhostel:<EMAIL>/mixdorm";

// Connect to MongoDB
mongoose.connect(MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true });

const PropertySchema = new mongoose.Schema({}, { strict: false });
const Property = mongoose.model("properties", PropertySchema, "properties");

// Function to get the latest hostelId
const getLatestHostelId = async () => {
  const latestHostel = await Property.findOne({ hostelId: { $exists: true } })
    .sort({ hostelId: -1 })
    .select("hostelId")
    .lean();

  return latestHostel ? parseInt(latestHostel.hostelId, 10) + 1 : 1009;
};

const updateHostelProperties = async () => {
  try {
    let nextHostelId = await getLatestHostelId();

    // Fetch all hostels that don't have a hostelId
    const hostels = await Property.find({ type: "HOSTEL" }).select("_id name");

    console.log(`Found ${hostels.length} hostels to update`);

    if (hostels.length === 0) {
      console.log("No hostels require updating.");
      return;
    }

    const bulkOps = hostels.map((hostel) => ({
      updateOne: {
        filter: { _id: hostel._id },
        update: { $set: { hostelId: nextHostelId++ } }
      }
    }));

    await Property.bulkWrite(bulkOps);

    console.log(`Successfully updated ${hostels.length} hostels`);
  } catch (error) {
    console.error("Error updating properties:", error);
  } finally {
    mongoose.connection.close();
  }
};

updateHostelProperties();
