import { savePageData } from '../services/page.js';
import RecentSearch from "../models/recentSearches.js"
import Response from "../utills/response.js";
import pageModel from '../models/page.js';
import Property from "../models/properties.js"
import mongoose from 'mongoose';
import { getConversionRates } from "../services/otaProperties.js"
import { convertCurrency } from "../services/otaProperties.js"
import lodash from 'lodash';
import NewsletterSubscriber from "../models/NewsletterSubscriber.js"
import sendEmail from "../commons/mail.js";
import jwt from 'jsonwebtoken';
import wishlists from '../models/wishLists.js';
import roomModel from '../models/room.js';
const savePageDataController = async (req, res) => {
  const { page, data } = req.body;

  if (!page || !data) {
    return res.status(400).json({ success: false, message: 'Page and data are required' });
  }

  const result = await savePageData(page, data);

  if (result.success) {
    return res.status(200).json(result);
  } else {
    return res.status(500).json(result);
  }
};
const listRecentSearchesController = async (req, res) => {
  try {
    const recentSearches = await RecentSearch.aggregate([
      {
        $match: { user: new mongoose.Types.ObjectId(req.user._id) }
      },
      {
        $sort: { createdAt: -1 }
      },
      {
        $addFields: {
          createdDateOnly: {
            $dateToString: { format: "%Y-%m-%d", date: "$createdAt" }
          }
        }
      },
      {
        $group: {
          _id: {
            property: "$property",
            date: "$createdDateOnly"
          },
          doc: { $first: "$$ROOT" } // most recent per property + date
        }
      },
      {
        $replaceRoot: { newRoot: "$doc" }
      },
      {
        $sort: { createdAt: -1 }
      },
      {
        $limit: 3
      }
    ]);

    return Response.OK(res, { recentSearches }, 'Recent searches listed');
  } catch (error) {
    console.error("Error fetching recent searches:", error.message);
    return Response.InternalServerError(res, null, error.message);
  }
};

export const listTopProperties = async (req, res) => {
  try {
    // Find the page data based on the specified page name
    const pageData = await pageModel.findOne({ page: 'top_hostels' });

    if (!pageData) {
      return Response.NotFound(res, null, 'Page not found');
    }
    const propertyIds = pageData.data.properties;
    const hostels = await Property.aggregate([
      {
        $match: {
          _id: { $in: propertyIds.map(id => new mongoose.Types.ObjectId(id)) } // Convert string IDs to ObjectId
        }
      },
      {
        $lookup: {
          from: 'properties', // The name of the collection to join with
          let: { hostelId: '$_id' }, // Define a variable for the local field
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$propertyId', '$$hostelId'] // Match on the propertyId field
                }
              }
            },
            {
              $project: {
                name: 1,
                address: 1,
                images: 1,
                overallRating: 1,
                starRating: 1,
                photos: 0
              }
            }
          ],
          as: 'Hostels'
        }
      }, {
        $project: {
          name: 1,
          address: 1,
          images: 1,
          overallRating: 1,
          starRating: 1
        }
      }
    ]);

    return Response.OK(res, hostels, 'Recent searches listed');
  } catch (error) {
    console.error("Error fetching recent searches:", error.message);
    return Response.InternalServerError(res, null, error.message);
  }
};
export const listTopHostelsController = async (req, res) => {
  try {
    let userId
    if (req?.headers?.authorization) {
      const token = req.headers.authorization?.split(' ')[1];
      const decodedToken = jwt.verify(token, process.env.JWT_SECRET);
      userId = decodedToken.userId
    }
    const topHostels = await pageModel.findOne({ page: "featured_hostels" })
    const propertyIds = topHostels?.data?.properties || [];
    let properties = await Property.find({
      _id: { $in: propertyIds }
    }, 'name address location aboutUs freeFacilities images lowestAveragePricePerNight googleReviews starRating')
    // Ensure the lowestAveragePricePerNight is in the correct format
    properties = properties.map(originalProperty => {
      return {
        ...originalProperty.toObject(), // Convert Mongoose document to plain object
        lowestAveragePricePerNight: {
          value: originalProperty.lowestAveragePricePerNight.value,
          currency: originalProperty.lowestAveragePricePerNight.currency
        },
        // Add any other fields you need
      };
    });

    let currency = req.query.currency || 'INR';
    if (currency) {
      const conversionRates = await getConversionRates(currency);

      // Apply currency conversion
      properties = properties.map(originalProperty => {
        const convertedPrice = convertCurrency(
          { lowestAveragePricePerNight: originalProperty.lowestAveragePricePerNight },
          currency,
          conversionRates
        );

        return {
          ...originalProperty,
          lowestAveragePricePerNight: convertedPrice.lowestAveragePricePerNight, // Use converted value
        };
      });
    }
    if (properties && properties.length) {
      if (userId) {
        for (const property of properties) {
          const liked = await wishlists.findOne({
            user: userId,
            property: property._id
          });
          property.liked = !!liked;
        }
      }
    }


    return Response.OK(res, { properties, about: topHostels.about }, 'Top Featured Hostels');
  } catch (error) {
    return Response.InternalServerError(res, null, error.message);
  }
};
export const listTopHostelsByCountry = async (req, res) => {
  try {
    const { country } = req.params;
    const { city, currency = 'INR', page = 1, limit = 10 } = req.query;

    let userId;
    if (req?.headers?.authorization) {
      const token = req.headers.authorization?.split(' ')[1];
      const decodedToken = jwt.verify(token, process.env.JWT_SECRET);
      userId = decodedToken.userId;
    }

    const query = {
      "address.country": country,
    };
    if (country == "India") {
      query["isPropertyLive"] = true
    }
    if (city) {
          if (country == "India") {
            query["address.city"] = city;
          } else{
            query["address.state"] = city;
          }
    }

    // Fetch the top hostels document for the requested country
    let topHostels = await pageModel.findOne({ page: "top_hostels", country });

    // Get total property count for pagination
    const totalCount = await Property.countDocuments(query);
    const totalPages = Math.ceil(totalCount / limit);
    const currentPage = parseInt(page);

    // Fetch paginated properties (excluding photos for optimization)
    let properties = await Property.find(query, { photos: 0 })
      .skip((currentPage - 1) * limit)
      .limit(parseInt(limit))
      .lean();

    // Currency Conversion
    if (currency) {
      const conversionRates = await getConversionRates(currency);
      properties = properties.map(originalProperty => {
        const convertedPrice = convertCurrency(
          { lowestAveragePricePerNight: originalProperty.lowestAveragePricePerNight },
          currency,
          conversionRates
        );
        return {
          ...originalProperty,
          lowestAveragePricePerNight: convertedPrice.lowestAveragePricePerNight,
        };
      });
    }

    // Liked Properties
    if (properties.length && userId) {
      if (Array.isArray(properties) && properties.length && userId) {
        for (const property of properties) {
          const liked = await wishlists.findOne({
            user: userId,
            property: property._id
          });
          property.liked = !!liked;
        }
      }
    }

    return Response.OK(res, {
      properties,
      about: topHostels?.about || null,
      locations: topHostels?.data?.locations || []
    }, `Top Featured Hostels in ${country}`);
  } catch (error) {
    return Response.InternalServerError(res, null, error.message);
  }
};
export const getTravelActivities = async (req, res) => {
  try {
    const { category } = req.query;
    let travel_activities = await pageModel.findOne({ page: "travel_activities", category });
    return Response.OK(res, { activities: travel_activities.data.activities }, `Activities `);
  } catch (error) {
    return Response.InternalServerError(res, null, error.message);
  }
};


// export const getTravelActivities = async (req, res) => {
//   try {
//     const travelDocs = await pageModel.find({
//       page: "travel_activities",
//       "data.activities.hostel": { $exists: true }
//     });

//     if (!travelDocs || travelDocs.length === 0) {
//       return res.status(404).json({ status: false, message: "No travel activity documents found." });
//     }

//     const notFoundHostels = [];

//     for (const doc of travelDocs) {
//       const updatedActivities = await Promise.all(
//         doc.data.activities.map(async (activity) => {
//           const property = await Property.findOne({
//             name: { $regex: new RegExp(`^${activity.hostel.trim()}$`, 'i') } // case-insensitive exact match
//           });

//           if (property) {
//             return { ...activity, hostelId: property._id };
//           } else {
//             notFoundHostels.push(activity.hostel);
//             return activity;
//           }
//         })
//       );
//       console.log("updatedActivities", updatedActivities)
//       doc.data.activities = updatedActivities;
//       await doc.save();
//     }

//     console.log("✅ All travel_activities documents updated.");
//     if (notFoundHostels.length > 0) {
//       console.log("⚠️ Hostels not found in properties:");
//       console.table(notFoundHostels);
//     }

//     return res.status(200).json({
//       status: true,
//       message: "Documents updated with hostelIds where found.",
//     });

//   } catch (err) {
//     console.error("❌ Error:", err);
//     return res.status(500).json({ status: false, message: "Server error", error: err.message });
//   }
// };


export const subscribeNewsletterController = async (req, res) => {
  const { email } = req.body;

  try {
    const existingSubscriber = await NewsletterSubscriber.findOne({ email });
    if (existingSubscriber) {
      return Response.Conflict(res, undefined, 'Email already subscribed.');
    }

    const newSubscriber = new NewsletterSubscriber({ email });
    await newSubscriber.save();

    // Logic to send confirmation email can be added here
    sendEmail("newsletter-subscribe", { email: req.body.email });
    return Response.OK(res, null, `Subscription successful `);
  } catch (error) {
    return Response.InternalServerError(res, null, error.message);
  }
};
export const autoCompleteSearch = async (req, res) => {
  try {
    const { search } = req.query;

    if (!search) {
      return res.status(400).json({ error: "Search is required." });
    }

    const searchPattern = new RegExp(search, "i");
    const MAX_RECORDS = 10;

    const properties = await Property.aggregate([
      {
        $match: {
          type: "HOSTEL",
          $or: [
            { "address.state": { $regex: searchPattern } },
            { "address.country": { $regex: searchPattern } },
            { "address.city": { $regex: searchPattern } },   // ✅ already included
            { name: { $regex: searchPattern } }
          ]
        }
      },
      {
        $lookup: {
          from: "rooms",
          localField: "_id",
          foreignField: "property",
          as: "rooms"
        }
      },
      {
        $addFields: {
          category: {
            $cond: [
              { $regexMatch: { input: "$name", regex: searchPattern } },
              "Property",
              {
                $cond: [
                  { $regexMatch: { input: "$address.city", regex: searchPattern } },
                  "City",   // ✅ added City category
                  {
                    $cond: [
                      { $regexMatch: { input: "$address.state", regex: searchPattern } },
                      "State",
                      {
                        $cond: [
                          { $regexMatch: { input: "$address.country", regex: searchPattern } },
                          "Country",
                          null
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        }
      },
      {
        $match: {
          $or: [
            { category: { $ne: "Property" } },
            {
              $and: [
                { category: "Property" },
                {
                  $or: [
                    { "address.country": { $ne: "India" } },
                    {
                      $and: [
                        { "address.country": "India" },
                        { isPropertyLive: true }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      },
      { $match: { category: { $ne: null } } },
      {
        $project: {
          id: "$_id",
          label: {
            $switch: {
              branches: [
                {
                  case: { $eq: ["$category", "Property"] },
                  then: {
                    $concat: [
                      "$name",
                      { $cond: [{ $ifNull: ["$address.city", false] }, { $concat: [", ", "$address.city"] }, ""] },
                      { $cond: [{ $ifNull: ["$address.state", false] }, { $concat: [", ", "$address.state"] }, ""] },
                      { $cond: [{ $ifNull: ["$address.country", false] }, { $concat: [", ", "$address.country"] }, ""] }
                    ]
                  }
                },
                {
                  case: { $eq: ["$category", "City"] },
                  then: {
                    $concat: [
                      "$address.city",
                      { $cond: [{ $ifNull: ["$address.state", false] }, { $concat: [", ", "$address.state"] }, ""] },
                      { $cond: [{ $ifNull: ["$address.country", false] }, { $concat: [", ", "$address.country"] }, ""] }
                    ]
                  }
                },
                {
                  case: { $eq: ["$category", "State"] },
                  then: {
                    $concat: [
                      "$address.state",
                      { $cond: [{ $ifNull: ["$address.country", false] }, { $concat: [", ", "$address.country"] }, ""] }
                    ]
                  }
                },
                {
                  case: { $eq: ["$category", "Country"] },
                  then: "$address.country"
                }
              ],
              default: "Unknown"
            }
          },
          category: "$category",
          type: {
            $switch: {
              branches: [
                { case: { $eq: ["$category", "Property"] }, then: "property" },
                { case: { $eq: ["$category", "City"] }, then: "city" },      // ✅ fixed City type
                { case: { $eq: ["$category", "State"] }, then: "state" },
                { case: { $eq: ["$category", "Country"] }, then: "country" }
              ],
              default: "unknown"
            }
          }
        }
      },
      {
        $group: {
          _id: { category: "$category", label: "$label" },
          items: { $addToSet: { id: "$id", label: "$label", type: "$type" } }
        }
      },
      {
        $group: {
          _id: "$_id.category",
          items: {
            $push: {
              label: "$_id.label",
              type: { $first: "$items.type" },
              id: { $first: "$items.id" }
            }
          }
        }
      },
      {
        $project: {
          _id: 0,
          category: "$_id",
          items: { $slice: ["$items", MAX_RECORDS] }
        }
      }
    ]);

    return Response.OK(res, properties);
  } catch (error) {
    return Response.InternalServerError(res, null, error.message);
  }
};



export const getPropertiesCounts = async (req, res) => {
  try {
    const { countries } = req.body;

    if (!countries || !Array.isArray(countries) || countries.length === 0) {
      return Response.BadRequest(res, null, 'Invalid countries array provided');
    }

    // Fetch property counts grouped by country
    const properties = await Property.aggregate([
      { $match: { 'address.country': { $in: countries } } },
      {
        $group: {
          _id: '$address.country',
          count: { $sum: 1 },
        },
      },
    ]);

    // Transform the result to a more readable format
    const formattedResult = properties.reduce((acc, item) => {
      acc[item._id] = item.count;
      return acc;
    }, {});

    return Response.OK(res, { propertyCounts: formattedResult }, 'Property counts fetched successfully');
  } catch (error) {
    console.error('Error fetching property counts:', error.message);
    return Response.InternalServerError(res, null, 'Error fetching property counts');
  }
};
export const getFilterdProperty = async (req, res) => {
  try {
    let userId
    if (req?.headers?.authorization) {
      const token = req.headers.authorization?.split(' ')[1];
      const decodedToken = jwt.verify(token, process.env.JWT_SECRET);
      userId = decodedToken.userId
    }
    const topHostels = await pageModel.findOne({ page: req.query.type })
    const propertyIds = topHostels?.data?.properties || [];
    let properties = await Property.find({
      _id: { $in: propertyIds }
    }, 'name address photos location aboutUs freeFacilities lowestAveragePricePerNight googleReviews starRating')
    // Ensure the lowestAveragePricePerNight is in the correct format
    properties = properties.map(originalProperty => {
      return {
        ...originalProperty.toObject(), // Convert Mongoose document to plain object
        lowestAveragePricePerNight: {
          value: originalProperty.lowestAveragePricePerNight.value,
          currency: originalProperty.lowestAveragePricePerNight.currency
        },
        // Add any other fields you need
      };
    });

    let currency = req.query.currency || 'INR';
    if (currency) {
      const conversionRates = await getConversionRates(currency);

      // Apply currency conversion
      properties = properties.map(originalProperty => {
        const convertedPrice = convertCurrency(
          { lowestAveragePricePerNight: originalProperty.lowestAveragePricePerNight },
          currency,
          conversionRates
        );

        return {
          ...originalProperty,
          lowestAveragePricePerNight: convertedPrice.lowestAveragePricePerNight, // Use converted value
        };
      });
    }
    if (properties && properties.length) {
      if (userId) {
        for (const property of properties) {
          const liked = await wishlists.findOne({
            user: userId,
            property: property._id
          });
          property.liked = !!liked;
        }
      }
    }


    return Response.OK(res, { properties, about: topHostels.about }, 'Top Featured Hostels');
  } catch (error) {
    return Response.InternalServerError(res, null, error.message);
  }
};
// GET /categories-hostels?search=goa,india&tag=partyHostels

export const getCategoryWiseProperty = async (req, res) => {
  try {
    const { search = "", tag } = req.query;
    const searchTerms = search.split(",").map(term => term.trim()).filter(Boolean);

    const searchConditions = searchTerms.map(term => ({
      $or: [
        { "address.state": { $regex: term, $options: "i" } },
        { "address.country": { $regex: term, $options: "i" } },
        { name: { $regex: term, $options: "i" } }
      ]
    }));

    // Get properties that match the search and tag
    const matchedWithTag = await Property.find({
      $and: [
        ...(searchConditions.length ? [{ $or: searchConditions }] : []),
        { tag: tag }
      ]
    })

    // Get properties that match search but do not match the tag
    const matchedOutsideTag = await Property.find({
      $and: [
        ...(searchConditions.length ? [{ $or: searchConditions }] : []),
        // { $or: [{ tag: { $exists: false } }] }
        { $or: [{ tag: { $ne: tag } }] }

      ]
    })
    return Response.OK(res, { matchedWithTag, withoutTag: matchedOutsideTag }, 'Top Featured Hostels');
  } catch (error) {
    console.error("Error in getCategoryWiseProperty:", error);
    res.status(500).json({ success: false, message: "Internal server error" });
  }
};

export const listStatesByCountry = async (req, res) => {
  try {
    const { country } = req.query;

    if (!country) {
      return res.status(400).json({
        success: false,
        message: 'Country query parameter is required.'
      });
    }

    let match = {
      'address.country': country,
    }
    let cities

    if (country == 'India') {
      match['isPropertyLive'] = true
       cities = await Property.aggregate([
        {
          $match: match
        },
        {
          $group: {
            _id: '$address.city',
            propertyCount: { $sum: 1 }
          }
        },
        {
          $sort: {
            propertyCount: -1
          }
        },
      ]);

    } else{
        cities = await Property.aggregate([
        {
          $match: match
        },
        {
          $group: {
            _id: '$address.state',
            propertyCount: { $sum: 1 }
          }
        },
        {
          $sort: {
            propertyCount: -1
          }
        },
      ]);
    }

    console.log("cities", cities)
    const cityNames = cities
      .map(city => city._id)
      .filter(Boolean); // removes null/undefined cities

    return res.status(200).json({
      success: true,
      data: cityNames,
      message: `Unique states found in ${country}`
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: error.message
    });
  }
};

export const listTopHostels = async (req, res) => {
  try {
    const { city, currency = "INR", countries } = req.query;

    let userId;
    if (req?.headers?.authorization) {
      const token = req.headers.authorization?.split(" ")[1];
      const decodedToken = jwt.verify(token, process.env.JWT_SECRET);
      userId = decodedToken.userId;
    }

    // Build match filter
    const matchStage = {
      isTopHostel: true,
      isOTA: true
    };

    if (city) {
      matchStage["address.state"] = city;
    }

    if (countries) {
      const countryArray = countries?.split(",").map(c => c.trim());
      matchStage["address.country"] = { $in: countryArray };
    }
    let grouped = await Property.collection.aggregate([
      { $match: matchStage },
      {
        $group: {
          _id: "$address.country",
          hostels: { $push: "$$ROOT" }
        }
      },
      {
        $project: {
          _id: 0,
          country: "$_id",
          hostels: { $slice: ["$hostels", 5] }
        }
      }
    ], { allowDiskUse: true }).toArray();

    // Currency conversion
    const conversionRates = await getConversionRates(currency);

    // Map hostels with converted currency and fetch `about` content
    grouped = await Promise.all(grouped.map(async (group) => {
      const hostels = group.hostels.map(h => {
        const converted = convertCurrency(
          { lowestAveragePricePerNight: h.lowestAveragePricePerNight },
          currency,
          conversionRates
        );
        return {
          ...h,
          lowestAveragePricePerNight: converted.lowestAveragePricePerNight,
        };
      });

      // Get about/description from pageModel
      const topHostelsPage = await pageModel.findOne({
        page: "top_hostels",
        country: group.country
      });

      return {
        country: group.country,
        about: topHostelsPage?.about || null,
        hostels,
        locations: topHostelsPage?.about || null,
      };
    }));

    // Mark liked properties
    if (userId) {
      for (const group of grouped) {
        for (const hostel of group.hostels) {
          const liked = await wishlists.findOne({
            user: userId,
            property: hostel._id
          });
          hostel.liked = !!liked;
        }
      }
    }

    return Response.OK(res, grouped, "Top Featured Hostels Grouped by Country");
  } catch (error) {
    console.log(error);
    return Response.InternalServerError(res, null, error.message);
  }
};

export { savePageDataController, listRecentSearchesController };