"use client";
import React from "react";
import { FiEye } from "react-icons/fi";
import { TfiPencilAlt } from "react-icons/tfi";
import { FaRegTrashCan } from "react-icons/fa6";
import Image from "next/image";
import { Plus } from "lucide-react";
import Link from "next/link";
import { MdOutlineKeyboardArrowLeft, MdOutlineKeyboardArrowRight } from "react-icons/md";

// Helper function to split text into chunks of 5 words
const splitTextByWords = (text, wordsPerLine) => {
  const words = text.split(" ");
  const lines = [];
  for (let i = 0; i < words.length; i += wordsPerLine) {
    lines.push(words.slice(i, i + wordsPerLine).join(" "));
  }
  return lines;
};

const Event = () => {
  const eventData = [
    {
      id: 1,
      titleText: "Club House",
      subText:
        "Whether it's splitting a cab fare, sharing a rental, or hopping on public transport together, Mix Ride helps you cut costs and make the most of your journey. ",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/event-s.png`,
    },
    {
      id: 2,
      titleText: "Club House",
      subText:
        "Whether it's splitting a cab fare, sharing a rental, or hopping on public transport together, Mix Ride helps you cut costs and make the most of your journey. ",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/event-s.png`,
    },
    {
      id: 3,
      titleText: "Club House",
      subText:
        "Whether it's splitting a cab fare, sharing a rental, or hopping on public transport together, Mix Ride helps you cut costs and make the most of your journey. ",
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/event-s.png`,
    },
  ];
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <div className="flex items-center justify-between w-full ">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Events
        </h2>
        <div className="w-[50%] gap-x-5 flex justify-end items-center">
          <Link href={"/superadmin/dashboard/event-add"}
            className={` px-3 lg:px-6 py-2 text-sm font-normal text-white rounded relative flex justify-center items-center bg-sky-blue-650 `}
            type="button"
            
          >
            <Plus size={18} className="mr-1" />Events
          </Link>
        </div>
      </div>
      <div className="bg-white border-b border-l border-r rounded-xl dark:bg-black dark:border-none">
        <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border-t dark:border-none">
          <table className="min-w-full divide-y bg-white rounded-xl divide-gray-200 dark:bg-black">
            <thead>
              <tr>
                <th className="px-6 py-6 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider whitespace-nowrap dark:bg-black dark:text-[#B6B6B6]">
                  TITLE TEXT
                </th>
                <th className="pl-20  py-6 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  MESSAGE TEXT
                </th>
                <th className="pr-12 lg:pr-6 pl-14 md:pl-16 lg:pl-16 py-6 bg-white text-left text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  IMAGE
                </th>

                <th className="pr-8 py-6 bg-white text-end text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  ACTION
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 border-x border-y dark:border-x-0">
              {eventData.map((event) => (
                <tr key={event.id}>
                  <td className="whitespace-nowrap text-sm px-5 text-gray-500 font-medium font-poppins  pb-32 dark:text-[#757575]">
                    {event.titleText}
                  </td>
                  <td className="whitespace-nowrap py-6 px-5 pr-8 pl-12">
                    {splitTextByWords(event.subText, 5).map((line, index) => (
                      <p key={index} className="text-sm leading-7 text-gray-500 font-medium font-poppins dark:text-[#757575]">
                        {line}
                      </p>
                    ))}
                  </td>
                  <td className="pl-10 lg:pl-0 pr-6 ">
                    
                      <Image
                        src={event.image}
                        alt="Image"
                        className="w-56 h-40 lg:h-32 lg:w-52 rounded-lg"
                        height={144}
                        width={144}
                      />
                    
                  </td>

                  <td className="py-10 pl-16 md:pl-14 lg:pl-0 pr-3 flex justify-end">
                    <Link href={"/superadmin/dashboard/event-details"} className="border hover:text-blue-700 p-2 rounded-l-lg text-black/75 dark:text-[#757575] dark:hover:text-blue-700">
                      <FiEye />
                    </Link>
                    <Link href={"/superadmin/dashboard/event-edit"} className="border p-2 text-black/75 hover:text-yellow-400 dark:text-[#757575] dark:hover:text-yellow-400">
                      <TfiPencilAlt />
                    </Link>
                    <button className="p-2 border rounded-r-lg text-red-600">
                      <FaRegTrashCan />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
            <div className="flex justify-between items-center mt-5">
              <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
              <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
                <a
                  href="#"
                  className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                >
                  <span className="sr-only">Next Page</span>
                  <MdOutlineKeyboardArrowLeft />
                </a>
      
                <a
                  href="#"
                  className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                >
                  <span className="sr-only">Next Page</span>
                  <MdOutlineKeyboardArrowRight />
                </a>
              </div>
            </div>
    </div>
  );
};

export default Event;