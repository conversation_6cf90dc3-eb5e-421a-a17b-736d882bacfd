import express from 'express';
import { getChannelManagers ,contractSign,contractSignEmail} from '../controller/chanelManagers.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();

// Route to get channel managers with optional search
router.get('/', checkAuth('get_chanel_managers_list'), getChannelManagers);
router.post('/contract-agreement', checkAuth('get_chanel_managers_list'), contractSign);
router.post('/contract-email', checkAuth('get_chanel_managers_list'), contractSignEmail);

export default router;

/**
 * @swagger
 * tags:
 *   name: ChannelManagers
 *   description: API endpoints for managing channel managers
 */

/**
 * @swagger
 * /channel-managers:
 *   get:
 *     summary: Retrieve a list of channel managers, optionally filtering by name
 *     tags: [ChannelManagers]
 *     parameters:
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         description: Name of the channel manager to search for
 *     responses:
 *       200:
 *         description: Successfully retrieved channel managers
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /channel-managers/contract-agreement:
 *   post:
 *     summary: Sign a contract for a channel manager
 *     tags: [ChannelManagers]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - date
 *               - hostelName
 *               - hostelAddress
 *               - name
 *             properties:
 *               date:
 *                 type: string
 *                 format: date
 *                 description: The date of the contract signing
 *               hostelName:
 *                 type: string
 *                 description: Name of the hostel
 *               hostelAddress:
 *                 type: string
 *                 description: Address of the hostel
 *               name:
 *                 type: string
 *                 description: Name of the person signing the contract
 *     responses:
 *       200:
 *         description: Contract signed successfully
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /channel-managers/contract-email:
 *   post:
 *     summary: Send an email for contract signing
 *     tags: [ChannelManagers]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - cc
 *               - subject
 *               - body
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: The recipient's email address
 *               cc:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: email
 *                 description: List of CC email addresses
 *               subject:
 *                 type: string
 *                 description: Subject of the email
 *               body:
 *                 type: string
 *                 description: Body content of the email
 *     responses:
 *       200:
 *         description: Email sent successfully
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */
