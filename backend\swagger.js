import swaggerJSDoc from 'swagger-jsdoc';
const swaggerOptions = {
    definition: {
        openapi: '3.0.0',
        info: {
            title: 'MixDorm',
            version: '1.0.0',
            description: 'API documentation for Mixdorm',
        },
        servers: [
            {
               url: `http://localhost:${process.env.PORT}`,
               description: 'Local server',
            },
            {
                url: `/`,
                description: 'Live server',
             }
        ],
        components: {
            securitySchemes: {
                bearerAuth: {
                    type: 'http',
                    scheme: 'bearer',
                    bearerFormat: 'JWT',
                },
            },
        },
        security: [
            {
                bearerAuth: [], 
            },
        ],
    },
    apis: ['./routes/*.js', './routes/front/*.js'],
};


const swaggerSpec = swaggerJSDoc(swaggerOptions);

export default swaggerSpec;
