// import NavbarOwner from "@/components/ownerFlow/navbar";
import { Inter, Roboto, Manrope } from "next/font/google";
import "../styles/globals.css";
import "../styles/main.css";
import NProgress from "nprogress";
import "nprogress/nprogress.css";
import Router, { useRouter } from "next/router";
import { Toaster } from "react-hot-toast";
import { NavbarProvider } from "@/components/home/<USER>";
import { HeaderProvider } from "@/components/ownerFlow/headerContex";
import { useEffect, useState } from "react";
import axios from "axios";
import dynamic from "next/dynamic";
import {
  getItemLocalStorage,
  setItemLocalStorage,
} from "@/utils/browserSetting";
import countries from "world-countries";
import Script from "next/script";
import Head from "next/head";
import { organizationSchema } from "@/lib/schema/organizationSchema";


const inter = Inter({ subsets: ["latin"],display: "swap", variable: "--font-inter" });
const roboto = Roboto({ subsets: ["latin"],display: "swap", variable: "--font-roboto" });
const manrope = Manrope({ subsets: ["latin"],display: "swap", variable: "--font-manrope" });

const Footer = dynamic(() => import("@/components/footer/footer"));
const Navbar = dynamic(() => import("@/components/navbar/navbar"));
const Header = dynamic(() => import("@/components/ownerFlow/header"));
const UserNavbar = dynamic(() => import("@/components/ownerFlow/userNavbar"));
const SuperLayout = dynamic(() => import("@/components/superadmin/SuperLayout"));



const MobileModal = dynamic(
  () => import("./../components/ownerFlow/mobileModel"),
  { ssr: false }
);

NProgress.configure({
  minimum: 0.6,
  easing: "ease",
  speed: 800,
  showSpinner: false,
});

Router.events.on("routeChangeStart", () => NProgress.start());
Router.events.on("routeChangeComplete", () => NProgress.done());
Router.events.on("routeChangeError", () => NProgress.done());

function AppWrapper({ Component, pageProps }) {
  const [countryToCurrency, setCountryToCurrency] = useState({});
  // eslint-disable-next-line no-unused-vars
  const [currency, setCurrency] = useState("USD");
  // eslint-disable-next-line no-unused-vars
  const [error, setError] = useState(null);
  // eslint-disable-next-line no-unused-vars
  const [coordinates, setCoordinates] = useState(null);
  // eslint-disable-next-line no-unused-vars
  const [loading, setLoading] = useState(true); // Loading state
  const router = useRouter();
  const path = router.pathname;

  // Conditionally choose layout based on the path
  const isOwnerRoute = path.startsWith("/owner");
  const isOwnerDashboard = path.startsWith("/owner/dashboard");
  const isSuperAdminRoute = path.startsWith("/superadmin/dashboard");
  const isSuperAdminRouteMeta = router.pathname.startsWith("/superadmin");

  // Define routes that require UserNavbar
  const userNavbarRoutes = [
    "/owner/login",
    "/owner/signup",
    "/owner/verifyotpowner",
    "/owner/forgot-password",
    "/owner/reset-password",
    "/owner/list",
    "/owner/add-property",
  ];

  // Define routes that require Header
  const headerRoutes = [
    "/owner/login",
    "/owner/hostel-login",
    "/owner/signup",
    "/owner/verifyotpowner",
    "/owner/forgot-password",
    "/owner/reset-password",
    "/owner/list",
    "/owner/add-property",
    "/owner/list-your-hostel",
  ];

  const mobileRoutes = [
    "/owner/login",
    "/owner/hostel-login",
    "/owner/signup",
    "/owner/verifyotpowner",
    "/owner/forgot-password",
    "/owner/reset-password",
    "/owner/list",
    "/owner/add-property",
    "/owner/list-your-hostel",
  ];

  // eslint-disable-next-line no-unused-vars
  const [isMobileRoute, setIsMobileRoute] = useState(false);

  useEffect(() => {
    if (typeof window !== "undefined") {
      setIsMobileRoute(mobileRoutes.includes(window.location.pathname));
    }
  }, []);

  useEffect(() => {
    const fetchCountryCurrencyCodes = async () => {
      try {
        const countryData = countries.map((country) => {
          // Safely extract currency code and symbol, falling back to defaults
          const currencyCode =
            country?.currencies && Object.keys(country?.currencies)[0]
              ? Object.keys(country?.currencies)[0]
              : "N/A";
          const currencySymbol =
            country?.currencies && country?.currencies[currencyCode]?.symbol
              ? country?.currencies[currencyCode]?.symbol
              : "â¬";

          // Get the flag code (ISO 3166-1 alpha-2) for the flag
          const flagCode = country.cca2 ? country.cca2.toLowerCase() : "xx"; // Default to 'xx' if cca2 is missing

          // Construct the flag image URL or use a placeholder
          const flag =
            // eslint-disable-next-line no-constant-binary-expression
            `https://flagcdn.com/w320/${flagCode}.png` ||
            "https://via.placeholder.com/30x25";

          return {
            country: country?.name?.common || "Unknown", // Country name, fallback to "Unknown" if not found
            code: currencyCode, // Currency code
            symbol: currencySymbol, // Currency symbol
            flag: flag, // Flag image URL
          };
        });

        setCountryToCurrency(countryData); // Store the country data
      } catch (error) {
        console.error("Error fetching country data:", error);
        setError("Could not load country data.");
      } finally {
        setLoading(false);
      }
    };

    fetchCountryCurrencyCodes();
  }, []);

  useEffect(() => {
    // Fetch user location based on IP
    const fetchLocationFromIP = async () => {
      try {
        const response = await axios.get("https://ipapi.co/json/");
        const { country_name, country_code } = response.data;

        if (country_name && country_code) {
          const currencyObject = countryToCurrency?.find(
            (item) => item?.country === country_name
          );

          const userCurrency = currencyObject ? currencyObject.code : "USD";
          setCurrency(userCurrency);

          setItemLocalStorage("selectedCountry", country_name);
          setItemLocalStorage("selectedCurrencyCode", userCurrency);
          setItemLocalStorage("selectedCountryFlag", currencyObject?.flag);
          setItemLocalStorage("selectedCurrencySymbol", currencyObject?.symbol);
          setItemLocalStorage("selectedCurrencyCodeOwner", userCurrency);
          setItemLocalStorage("selectedOwnerCountry", userCurrency);
        } else {
          throw new Error("Could not retrieve location from IP.");
        }
      } catch (error) {
        console.error("Error fetching location from IP:", error);
        setError("Could not determine location.");
        // Fallback to default currency
        setCurrency("USD");
      }
    };

    if (
      (!getItemLocalStorage("selectedCountry") &&
        !getItemLocalStorage("selectedCurrencyCode") &&
        !getItemLocalStorage("selectedCountryFlag") &&
        !getItemLocalStorage("selectedCurrencySymbol")) ||
      (!getItemLocalStorage("selectedCurrencyCodeOwner") &&
        !getItemLocalStorage("selectedOwnerCountry"))
    ) {
      fetchLocationFromIP();
    }
  }, [countryToCurrency]);

  // Fetch currency based on coordinates
  useEffect(() => {
    const fetchCurrency = async (latitude, longitude) => {
      const apiKey = "AIzaSyBv_hPcDOPcrTfHnLrFNduHgJWDwv1pjfU"; // Replace with your actual Google API key
      try {
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${apiKey}`
        );
        const data = await response.json();

        if (data.status === "OK") {
          const addressComponents = data.results[0].address_components;
          const countryComponent = addressComponents.find((component) =>
            component.types.includes("country")
          );

          if (countryComponent && countryToCurrency) {
            const countryCode = countryComponent.short_name;
            const currencyObject = countryToCurrency.find(
              (item) => item.country === countryComponent?.long_name
            );

            const userCurrency = currencyObject ? currencyObject.code : "USD";
            console.log(
              "countryCode",
              userCurrency,
              countryCode,
              currencyObject,
              countryComponent
            );

            setCurrency(userCurrency);
            setItemLocalStorage("selectedCountry", currencyObject?.country);
            setItemLocalStorage("selectedCurrencyCode", currencyObject?.code);
            setItemLocalStorage("selectedCountryFlag", currencyObject?.flag);
            setItemLocalStorage(
              "selectedCurrencySymbol",
              currencyObject?.symbol
            );
            setItemLocalStorage("selectedRoomsData", null);
            setItemLocalStorage("selectedOwnerCountry", currencyObject?.code);
            setItemLocalStorage(
              "selectedCurrencyCodeOwner",
              currencyObject?.code
            );

            // updateCountry2(currencyObject?.flag,currencyObject?.code)

            // setSearchTerm("");
            // updateCountry();
          } else {
            console.error(
              "Country component not found or countryToCurrency is not defined."
            );
          }
        } else {
          throw new Error("Unable to retrieve location data.");
        }
      } catch (error) {
        console.error("Error fetching currency:", error);
        setError("Could not determine currency.");
      }
    };

    if (
      (coordinates &&
        !getItemLocalStorage("selectedCountry") &&
        !getItemLocalStorage("selectedCurrencyCode") &&
        !getItemLocalStorage("selectedCountryFlag") &&
        !getItemLocalStorage("selectedCurrencySymbol")) ||
      (!getItemLocalStorage("selectedOwnerCountry") &&
        !getItemLocalStorage("selectedCurrencyCodeOwner"))
    ) {
      fetchCurrency(coordinates?.latitude, coordinates?.longitude);
    }
  }, [coordinates, countryToCurrency]);

  const [collapsed, setCollapsed] = useState(false);

  // useEffect(() => {
  // // Sidebar starts normal
  //   const collapseTimer = setTimeout(() => {
  //     setCollapsed(true); // Collapse after 2s
  //   }, 2000); // Adjust time before collapse

  //   const expandTimer = setTimeout(() => {
  //     setCollapsed(false); // Expand back after another 1s
  //   }, 3000); // 2s + 1s = total 3s delay for expansion

  //   return () => {
  //     clearTimeout(collapseTimer);
  //     clearTimeout(expandTimer);
  //   };
  // }, []);

  const BASE_URL = "https://mixdorm.com" ;

  const getCanonicalUrl = () => {
    const path = router.asPath;
    return `${BASE_URL}${path}`;
  };

  return (
    <>
      <Head>
        {/* <link rel='canonical' href='https://mixdorm.com/' /> */}
        {router.asPath != "/" &&
        <link rel="canonical" href={getCanonicalUrl()} />
        }
        {isSuperAdminRouteMeta ? (
          <meta name='robots' content='noindex, nofollow' />
        ) : (
          <meta name='robots' content='index,follow' />
        )}

        <title>Mixdorm | Best Affordable Hostel Booking Worldwide</title>
        <meta
          name="description"
          content="Explore the world with Mixdorm! Big savings on hostels, dorms & shared stays. Hostel booking made easyâstay cheap, meet people, travel smarter!"
        />
        <meta
          name='keywords'
          content='Top Hostel Booking, Dormitories, Hotel booking, Budget Hotels, Solo Backpacker, Travel Booking, Hostels For Travellers, Cheapest Accommodation, Hostel Stay, Online Booking, Backpackers Hostel, Hostel Booking Near Me, Youth hostels, Hostel Listing'
        />
         <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(organizationSchema),
          }}
        />
      </Head>
      {/* <Head>
        <title>Mixdorm</title>
        <meta
          name="description"
          content="ð¥ Sleep Cheap, Live Big! From beach huts to city digs, 2.5M+ spots in 85K+ places to call home. Your next epic stay is waiting! ð"
        />
        <meta name="keywords" content="Hostels,Dorms,Hotels" />
        <meta property="og:title" content="Mixdorm" />
        <meta
          property="og:description"
          content="ð¥ Sleep Cheap, Live Big! From beach huts to city digs, 2.5M+ spots in 85K+ places to call home. Your next epic stay is waiting! ð"
        />
        <meta property="og:image" content="/path/to/image.jpg" />
        <meta property="og:url" content="www.mixdorm.com" />
      </Head> */}
      {/* <SocketProvider> */}

      <HeaderProvider>
        <NavbarProvider>
          {isOwnerRoute && !mobileRoutes.includes(path) && (
            <MobileModal collapsed={collapsed}  />
          )}
          {/* w-[calc(100% - 218px)] right-content float-right w-full md:ml-[436px] */}
          <div
            className={`transition-all duration-300 
              ${
                collapsed
                  ? "ml-[80px] w-[calc(100%-80px)]"
                  : isOwnerRoute && !mobileRoutes.includes(path)
                  ? "md:ml-[250px] md:w-[calc(100%-250px)] w-full"
                  : "w-full"
              } 
            `}
          >
            <Toaster position='top-center' />

            {isOwnerRoute && userNavbarRoutes.includes(path) && <UserNavbar />}

            {isOwnerRoute && !headerRoutes.includes(path) && (
              <Header collapsed={collapsed} setCollapsed={setCollapsed} />
            )}

            <main
              className={`w-full h-full ${inter.variable} ${roboto.variable} ${manrope.variable} ${
                isOwnerDashboard && "px-3 py-5 lg:px-8"
              }`}
            >
              {!isOwnerRoute &&
                !isSuperAdminRoute &&
                path !== "/superadmin/signup" &&
                path !== "/superadmin/signin" &&
                path !== "/superadmin/auth" &&
                path !== "/superadmin/select-user" &&
                path !== "/superadmin/profile" && <Navbar />}

              {isSuperAdminRoute && (
                <>
                  <SuperLayout />
                </>
              )}
              {/* <!-- Google tag (gtag.js) --> */}
              <Script
                src='https://www.googletagmanager.com/gtag/js?id=G-C4JQ9ECXS5'
                strategy='afterInteractive'
              />

              <Script id='google-analytics' strategy='afterInteractive'>
                {`
                  window.dataLayer = window.dataLayer || [];
                  function gtag(){dataLayer.push(arguments);}
                  gtag('js', new Date());
                  gtag('config', 'G-C4JQ9ECXS5');
                `}
              </Script>

              {/* <!-- Google tag (gtag.js) -->
                <script async src="https://www.googletagmanager.com/gtag/js?id=G-C4JQ9ECXS5"></script>
                <script>
                  window.dataLayer = window.dataLayer || [];
                  function gtag(){dataLayer.push(arguments);}
                  gtag('js', new Date());

                  gtag('config', 'G-C4JQ9ECXS5');
                </script> */}

              <Component {...pageProps} />
            </main>
            {path !== "/owner/hostel-login" &&
              path !== "/superadmin/signup" &&
              path !== "/superadmin/signin" &&
              path !== "/superadmin/auth" &&
              path !== "/superadmin/select-user" &&
              path !== "/superadmin/profile" &&
              !isOwnerRoute &&
              !isSuperAdminRoute && (
                <>
                  <Footer />
                </>
            )}
          </div>
        </NavbarProvider>
      </HeaderProvider>
    
      {/* </SocketProvider> */}
    </>
  );
}

export default AppWrapper;
