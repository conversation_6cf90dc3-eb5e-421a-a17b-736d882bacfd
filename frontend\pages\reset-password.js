import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import toast, { Toaster } from "react-hot-toast";
import { resetPassApi } from "@/services/webflowServices";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import VisibilityOffOutlinedIcon from "@mui/icons-material/VisibilityOffOutlined";

const ResetPassword = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [isClient, setIsClient] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();

  useEffect(() => {
    setIsClient(true);
  }, []);

  const validatePassword = (password) => {
    const passwordRegex =
      /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*#?&])[A-Za-z\d@$!%*#?&]{8,25}$/;
    return passwordRegex.test(password);
  };

  const handleResetPassword = async (e) => {
    e.preventDefault();

    if (email === "" || password === "" || confirmPassword === "") {
      setError("Please fill in all fields.");
      return;
    }

    if (password !== confirmPassword) {
      setError("Passwords do not match.");
      return;
    }

    if (!validatePassword(password)) {
      setError(
        "Password must be at least 8 characters long, include at least one uppercase letter, one number, and one special character, and be at most 25 characters."
      );
      return;
    }

    setError("");
    setLoading(true);
    const { id } = router.query;

    try {
      const response = await resetPassApi({ email, newPassword: password, token: id });

      if (response?.data?.status) {
        toast.success(
          response?.data?.message || "Password reset successfully!"
        );
        router.push("/"); 
      } else {
        toast.error(response?.data?.message || "Something went wrong.");
      }
    } catch (error) {
      console.error("Error during password reset request:", error);

      if (error?.response?.status === 404) {
        toast.error(error?.response?.data?.message || "Email not found.");
      } else {
        toast.error("An unexpected error occurred. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Toaster position="top-center" />
      <div className="min-h-screen flex items-center justify-center bg-[#F7F7F7] w-full pb-[10rem] pt-[3rem]">
        <div className="w-full max-w-3xl bg-white shadow-md p-14 rounded-3xl md:max-w-3xl">
          <form onSubmit={handleResetPassword}>
            <h2 className="mb-6 text-xl font-bold text-center text-gray-800">
              👋 Reset Password
            </h2>
            <div className="mb-4">
              {/* <label htmlFor="email" className="block text-base font-semibold text-gray-700">
                Email
              </label> */}
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
                placeholder="Enter your email"
                required
              />
            </div>
            <div className="relative mb-4">
              {/* <label
                htmlFor="password"
                className="block font-semibold text-gray-700"
              >
                New Password
              </label> */}
              <input
                type={showPassword ? "text" : "password"}
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
                placeholder="Enter new password"
                required
              />
              {isClient && (
                <button
                  type="button"
                  className="absolute right-4 top-[14px] text-gray-300 transform"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <VisibilityOutlinedIcon />
                  ) : (
                    <VisibilityOffOutlinedIcon />
                  )}
                </button>
              )}
            </div>
            <div className="relative mb-4">
              {/* <label
                htmlFor="confirmPassword"
                className="block text-base font-semibold text-gray-700"
              >
                Confirm Password
              </label> */}
              <input
                type="text"
                id="confirmPassword"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
                placeholder="Confirm your password"
                required
              />
            </div>
            <button
              type="submit"
              className="w-full mb-4 bg-primary-blue font-semibold text-white py-4 rounded-2xl hover:bg-sky-blue-750 hover:text-white transition duration-200"
              disabled={loading}
            >
              {loading ? "Resetting..." : "Reset Password"}
            </button>
            {error && (
              <div className="mb-4 text-center text-red-600">{error}</div>
            )}
          </form>
        </div>
      </div>
    </>
  );
};

export default ResetPassword;
