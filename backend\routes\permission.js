import express from 'express';
import { createPer<PERSON><PERSON><PERSON>roller, updatePermissionController, getAllPermissionsController, getPermissionByIdController, deletePermissionController } from '../controller/permission.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();

router.post('/', checkAuth("add_permission"), createPermissionController);
router.put('/:id', checkAuth("update_permission") , updatePermissionController);
router.get('/', checkAuth("get_all_permissions") , getAllPermissionsController);
router.get('/:id', checkAuth("get_permission") , getPermissionByIdController);
router.delete('/:id', checkAuth("delete_permission") , deletePermissionController);

export default router;

/**
 * @swagger
 * tags:
 *   name: Permissions
 *   description: API for managing user permissions
 */

/**
 * @swagger
 * /permissions:
 *   post:
 *     summary: Create a new permission
 *     tags: [Permissions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Permission'
 *     responses:
 *       201:
 *         description: The permission was successfully created
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /permissions/{id}:
 *   put:
 *     summary: Update a permission by ID
 *     tags: [Permissions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The permission ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Permission'
 *     responses:
 *       200:
 *         description: The permission was successfully updated
 *       404:
 *         description: Permission not found
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /permissions:
 *   get:
 *     summary: Get all permissions
 *     tags: [Permissions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Permission'
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /permissions/{id}:
 *   get:
 *     summary: Get a permission by ID
 *     tags: [Permissions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The permission ID
 *     responses:
 *       200:
 *         description: Permission details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Permission'
 *       404:
 *         description: Permission not found
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /permissions/{id}:
 *   delete:
 *     summary: Delete a permission by ID
 *     tags: [Permissions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: The permission ID
 *     responses:
 *       200:
 *         description: The permission was successfully deleted
 *       404:
 *         description: Permission not found
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Permission:
 *       type: object
 *       required:
 *         - user
 *         - view
 *       properties:
 *         id:
 *           type: string
 *           description: Auto-generated ID of the permission
 *         user:
 *           type: string
 *           description: The ID of the user
 *         view:
 *           type: string
 *           description: View permission (e.g., "allow", "deny")
 *         add:
 *           type: string
 *           description: Add permission (e.g., "allow", "deny")
 *         update:
 *           type: string
 *           description: Update permission (e.g., "allow", "deny")
 *         delete:
 *           type: string
 *           description: Delete permission (e.g., "allow", "deny")
 *         status:
 *           type: number
 *           description: Describes status
 *         isActive:
 *           type: boolean
 *           description: Is the booking active
 *         isDeleted:
 *           type: boolean
 *           description: Is the booking deleted
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The creation date of the permission
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The last update date of the permission
*/
