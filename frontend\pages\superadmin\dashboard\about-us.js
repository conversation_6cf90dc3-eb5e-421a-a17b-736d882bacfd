"use client";
import { Plus } from "lucide-react";
import <PERSON> from "next/link";
import React from "react";
import { FaRegTrashCan } from "react-icons/fa6";
import { FiEye } from "react-icons/fi";
import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight,
} from "react-icons/md";
import { TfiPencilAlt } from "react-icons/tfi";

const splitTextByWords = (text, wordsPerLine) => {
  const words = text.split(" ");
  const lines = [];
  for (let i = 0; i < words.length; i += wordsPerLine) {
    lines.push(words.slice(i, i + wordsPerLine).join(" "));
  }
  return lines;
};
const About = () => {
  const aboutData = [
    {
      id: 1,
      name: "Welcome to Mixdorm",

      feedback:
        "At MixDorm, we believe that travel is more than just a journey; it's an experience that connects people, cultures, and stories. Founded with a vision to revolutionize the hostel experience, MixDorm is not just a place to stay but a community where travelers from around the world come together to create unforgettable memories",
    },
    {
      id: 2,
      name: "Welcome to Mixdorm",

      feedback:
        "At MixDorm, we believe that travel is more than just a journey; it's an experience that connects people, cultures, and stories. Founded with a vision to revolutionize the hostel experience, MixDorm is not just a place to stay but a community where travelers from around the world come together to create unforgettable memories",
    },
    {
      id: 3,
      name: "Welcome to Mixdorm",

      feedback:
        "At MixDorm, we believe that travel is more than just a journey; it's an experience that connects people, cultures, and stories. Founded with a vision to revolutionize the hostel experience, MixDorm is not just a place to stay but a community where travelers from around the world come together to create unforgettable memories",
    },
    {
      id: 4,
      name: "Welcome to Mixdorm",

      feedback:
        "At MixDorm, we believe that travel is more than just a journey; it's an experience that connects people, cultures, and stories. Founded with a vision to revolutionize the hostel experience, MixDorm is not just a place to stay but a community where travelers from around the world come together to create unforgettable memories",
    },
    {
      id: 5,
      name: "Welcome to Mixdorm",

      feedback:
        "At MixDorm, we believe that travel is more than just a journey; it's an experience that connects people, cultures, and stories. Founded with a vision to revolutionize the hostel experience, MixDorm is not just a place to stay but a community where travelers from around the world come together to create unforgettable memories",
    },
    {
      id: 6,
      name: "Welcome to Mixdorm",

      feedback:
        "At MixDorm, we believe that travel is more than just a journey; it's an experience that connects people, cultures, and stories. Founded with a vision to revolutionize the hostel experience, MixDorm is not just a place to stay but a community where travelers from around the world come together to create unforgettable memories",
    },
    {
      id: 7,
      name: "Welcome to Mixdorm",

      feedback:
        "At MixDorm, we believe that travel is more than just a journey; it's an experience that connects people, cultures, and stories. Founded with a vision to revolutionize the hostel experience, MixDorm is not just a place to stay but a community where travelers from around the world come together to create unforgettable memories",
    },
    {
      id: 8,
      name: "Welcome to Mixdorm",

      feedback:
        "At MixDorm, we believe that travel is more than just a journey; it's an experience that connects people, cultures, and stories. Founded with a vision to revolutionize the hostel experience, MixDorm is not just a place to stay but a community where travelers from around the world come together to create unforgettable memories",
    },
    {
      id: 9,
      name: "Welcome to Mixdorm",

      feedback:
        "At MixDorm, we believe that travel is more than just a journey; it's an experience that connects people, cultures, and stories. Founded with a vision to revolutionize the hostel experience, MixDorm is not just a place to stay but a community where travelers from around the world come together to create unforgettable memories",
    },
  ];

  // const splitMessage = (feedback) => {
  //   const words = feedback.split(" ");
  //   const midpoint = Math.ceil(words.length / 4);
  //   const firstPart = words.slice(0, midpoint).join(" ");
  //   const secondPart = words.slice(midpoint).join(" ");
  //   return { firstPart, secondPart };
  // };

  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 dark:bg-[#171616] overflow-y-auto scroll-smooth max-w-full">
      <div className="flex items-center justify-between w-full ">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          About Us
        </h2>
        <div className="w-[50%] gap-x-5 flex justify-end items-center">
          <Link
            href={"/superadmin/dashboard/about-us-add"}
            className={` px-4 py-2 text-sm font-medium  font-poppins text-white rounded relative flex justify-center items-center bg-sky-blue-650 `}
            type="button"
          >
            <Plus size={18} className="mr-1" /> About Us
          </Link>
        </div>
      </div>
      <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border dark:border-none">
        <table className="w-full max-w-full divide-y bg-white rounded-xl divide-gray-200 dark:bg-black">
          <thead>
            <tr className="">
              <th className="px-5 py-6 bg-white text-left pl-10 text-sm font-semibold font-poppins text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                TITLE TEXT
              </th>

              <th className="pr-4 pl-0 py-6 bg-white  text-sm font-semibold text-center font-poppins text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                SUB TEXT
              </th>

              <th className="py-6 pl-8 pr-2   bg-white text-center text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                ACTION
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 border-x border-y dark:border-x-0 text-black/70 dark:text-[#757575]">
            {aboutData.map((aboutItem) => {
              // const { firstPart, secondPart } = splitMessage(
              //   aboutItem.feedback
              // );
              return (
                <tr key={aboutItem.id} className="">
                  <td className=" whitespace-nowrap  px-5 text-gray-500 text-sm font-medium font-poppins dark:text-[#757575]">
                    {aboutItem.name}
                  </td>

                  <td className="table-cell sm:table-cell lg:hidden whitespace-nowrap px-5 pl-10 lg:pl-28 py-4">
                    {splitTextByWords(aboutItem.feedback, 6).map(
                      (line, index) => (
                        <p
                          key={index}
                          className="text-sm leading-7 text-gray-500 font-medium font-poppins dark:text-[#757575]"
                        >
                          {line}
                        </p>
                      )
                    )}
                  </td>

                  <td className="hidden lg:table-cell whitespace-nowrap px-5 pl-10 lg:pl-28 py-4">
                    {splitTextByWords(aboutItem.feedback, 13).map(
                      (line, index) => (
                        <p
                          key={index}
                          className="text-sm leading-7 text-gray-500 font-medium font-poppins dark:text-[#757575]"
                        >
                          {line}
                        </p>
                      )
                    )}
                  </td>

                  <td className="py-28 md:py-28 lg:py-14 pl-8 lg:pl-8 px-2 flex justify-center  ">
                    <Link
                      href={"/superadmin/dashboard/about-us-details"}
                      className=" border p-2 rounded-l-lg text-black/75 hover:text-blue-700 dark:text-[#757575] dark:hover:text-blue-700"
                    >
                      <FiEye />
                    </Link>

                    <Link
                      href={"/superadmin/dashboard/about-us-edit"}
                      className=" border p-2 text-black/75 hover:text-yellow-400 dark:text-[#757575] dark:hover:text-yellow-400"
                    >
                      <TfiPencilAlt />
                    </Link>

                    <button className=" p-2 border rounded-r-lg text-red-600">
                      <FaRegTrashCan />
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
      {/* <div className="flex justify-between items-center mt-5">
        <div className="text-black/75">Showing 1-09 of 78</div>
        <div className="inline-flex items-center justify-center border rounded-xl bg-white">
          <a
            href="#"
            className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180"
          >
            <span className="sr-only">Next Page</span>
            <MdOutlineKeyboardArrowLeft />
          </a>

          <a
            href="#"
            className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180"
          >
            <span className="sr-only">Next Page</span>
            <MdOutlineKeyboardArrowRight />
          </a>
        </div>
      </div> */}
       <div className="flex justify-between items-center mt-5">
                    <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
                    <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
                      <a
                        href="#"
                        className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                      >
                        <span className="sr-only">Next Page</span>
                        <MdOutlineKeyboardArrowLeft />
                      </a>
            
                      <a
                        href="#"
                        className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                      >
                        <span className="sr-only">Next Page</span>
                        <MdOutlineKeyboardArrowRight />
                      </a>
                    </div>
                  </div>
    </div>
  );
};

export default About;
