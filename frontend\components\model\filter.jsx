import React from "react";
// import { format } from "date-fns";

const Filter = ({ closefilterModal }) => {
  // const [showCalendar, setShowCalendar] = useState(false);
  // const [selectedDate, setSelectedDate] = useState("");
  // const [currentMonth, setCurrentMonth] = useState(new Date());
  // const calendarRef = useRef(null);
  // const inputRef = useRef(null);

  // const handlePrevMonth = () => {
  //   setCurrentMonth(
  //     (prev) => new Date(prev.getFullYear(), prev.getMonth() - 1, 1)
  //   );
  // };

  // const handleNextMonth = () => {
  //   setCurrentMonth(
  //     (prev) => new Date(prev.getFullYear(), prev.getMonth() + 1, 1)
  //   );
  // };

  // // Generate days for current month
  // function generateDays(month) {
  //   const daysInMonth = new Date(
  //     month.getFullYear(),
  //     month.getMonth() + 1,
  //     0
  //   ).getDate();
  //   const firstDayIndex = new Date(
  //     month.getFullYear(),
  //     month.getMonth(),
  //     1
  //   ).getDay();

  //   const days = [];

  //   // Add blank spaces for days of previous month
  //   for (let i = 0; i < firstDayIndex; i++) {
  //     days.push(null);
  //   }

  //   // Add days of the current month
  //   for (let i = 1; i <= daysInMonth; i++) {
  //     days.push(new Date(month.getFullYear(), month.getMonth(), i));
  //   }

  //   return days;
  // }

  // const handleDateClick = (day) => {
  //   const formattedDate = format(day, "MM-dd-yyyy"); // format the selected date
  //   setSelectedDate(formattedDate);
  //   setShowCalendar(false); // close calendar after selecting
  // };

  // // Close calendar if clicked outside
  // useEffect(() => {
  //   function handleClickOutside(event) {
  //     if (
  //       calendarRef.current &&
  //       !calendarRef.current.contains(event.target) &&
  //       !inputRef.current.contains(event.target)
  //     ) {
  //       setShowCalendar(false);
  //     }
  //   }
  //   document.addEventListener("mousedown", handleClickOutside);
  //   return () => {
  //     document.removeEventListener("mousedown", handleClickOutside);
  //   };
  // }, []);
  return (
    <>
      <form className="space-y-6 max-w-[830px] mx-auto">
        <div className="relative">
            <label className="block text-sm font-medium text-black">
              Date
            </label>
            <input
              type="date"
              name="title"
              className="mt-1 block w-full border border-gray-300 rounded-lg p-2 pl-10 shadow-sm appearance-none cursor-pointer"
              placeholder="10 Sep"
              style={{ outline: "none" }}
              onClick={(e) => e.target.showPicker()} // Opens the date picker on input click
            />
            
            <div
              className="absolute top-5 left-3 cursor-pointer mt-4 "
              onClick={(e) => {
                e.target.previousSibling.showPicker(); // Opens the date picker on icon click
              }}
            >
              
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M8 7V3m8 4V3m-9 8h10m-12 8h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
            </div>
            <div className="absolute top-5 right-3 cursor-pointer mt-4 w-5 h-5 bg-white"></div>
          </div>
        {/* <div className="relative w-full ">
          <label
            className="block text-black sm:text-sm text-xs font-medium mb-1.5"
            htmlFor="toDate"
          >
            Date
          </label>

          <input
            type="text"
            name="toDate"
            id="toDate"
            ref={inputRef}
            value={selectedDate}
            placeholder="mm/dd/yyyy"
            readOnly
            onClick={() => setShowCalendar(!showCalendar)}
            className="block w-full p-2 px-4 py-3 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-800 placeholder:text-gray-400 cursor-pointer"
          />

          {showCalendar && (
            <div
              ref={calendarRef}
              className="absolute  left-0 bg-white border border-black/50 rounded-lg shadow-lg px-4 py-2 w-full mt-0.5 z-50"
            >
           
              <div className="flex items-center justify-between mb-4">
                <button
                  onClick={handlePrevMonth}
                  className="text-lg font-bold px-2"
                >
                  &#8592;
                </button>

                <span className="text-base font-semibold">
                  {format(currentMonth, "MMMM yyyy")}
                </span>

                <button
                  onClick={handleNextMonth}
                  className="text-lg font-bold px-2"
                >
                  &#8594;
                </button>
              </div>

      
              <div className="grid grid-cols-7 gap-2 text-center text-sm font-semibold text-gray-600">
                <div>Su</div>
                <div>Mo</div>
                <div>Tu</div>
                <div>We</div>
                <div>Th</div>
                <div>Fr</div>
                <div>Sa</div>
              </div>

          
              <div className="grid grid-cols-7 gap-1 mt-2 text-center text-sm">
                {generateDays(currentMonth).map((day, index) =>
                  day ? (
                    <button
                      key={index}
                      className="hover:bg-primary-blue hover:text-white rounded-full p-2"
                      onClick={() => handleDateClick(day)}
                    >
                      {day.getDate()}
                    </button>
                  ) : (
                    <div key={index} />
                  )
                )}
              </div>
            </div>
          )}
        </div> */}
        <div>
          <label className="block text-sm font-medium text-black mb-1">
            Method
          </label>
          <select
            name="type"
            className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
          >
            <option disabled value="">
              Visa
            </option>
            <option value="Visa">Visa</option>
          </select>
        </div>

        {/* <div>
            <label className="block text-sm font-medium text-gray-700">
              Country
            </label>
            <select
              name="price"
              className="mt-1 block w-full placeholder:text-sm border border-gray-300 rounded-lg p-3 shadow-sm"
              style={{ outline: "none" }}
            >
              <option>1</option>
              <option>2</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Rating
            </label>
            <select
              name="price"
              className="mt-1 block w-full placeholder:text-sm border border-gray-300 rounded-lg p-3 shadow-sm"
              style={{ outline: "none" }}
            >
              <option>1</option>
              <option>2</option>
            </select>
          </div> */}

        <div className="flex justify-center gap-3 mx-9">
          <button
            type="button"
            onClick={closefilterModal}
            className="hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm"
          >
            Apply
          </button>
        </div>
      </form>
    </>
  );
};

export default Filter;
