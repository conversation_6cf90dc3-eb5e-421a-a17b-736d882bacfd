import React, { useState, useEffect } from "react";
import {
  editProfile<PERSON><PERSON>,
  getProfile<PERSON>pi,
} from "@/services/webflowServices";
import toast from "react-hot-toast";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import VisibilityOffOutlinedIcon from "@mui/icons-material/VisibilityOffOutlined";
import Select from 'react-select';
import { getCode, getNames } from 'country-list';
import countryToCurrency from "country-to-currency";
import { Checkbox, FormControlLabel } from "@mui/material";
import { BASE_URL } from "@/utils/api";
import Link from "next/link";
import Image from "next/image";

const EditDetailsPage = ({ data, setLoading }) => {
  const [isChangePasswordChecked, setIsChangePasswordChecked] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState(null);
  const [selectedCurrency, setSelectedCurrency] = useState("");  // State for currency
  const [formData, setFormData] = useState({
    firstName: data?.data?.name?.first || "",
    lastName: data?.data?.name?.last || "",
    email: data?.data?.email || "",
    contact: data?.data?.contact || "",
    address: data?.data?.address || "",
    newPassword: "",
    confirmPassword: "",
    profilePic: data?.data?.profileImage || "",
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [countries, setCountries] = useState([]);

  useEffect(() => {
    const fetchCountries = async () => {
      const countryOptions = getNames().map(country => ({
        label: country,
        value: getCode(country),
      }));
      setCountries(countryOptions);
    };

    fetchCountries();
  }, []);

  const countriesFromPackage = getNames().map(country => ({
    label: country,
    value: getCode(country),
  }));
  

  useEffect(() => {
    if (data?.data?.countryShortName) {
      const countryCode = data?.data?.countryShortName;
      const country = countriesFromPackage.find(option => option.value === countryCode);
      setSelectedCountry(country || null);
      setSelectedCurrency(countryToCurrency[countryCode]);
    }
    setFormData({
      firstName: data?.data?.name?.first || "",
      lastName: data?.data?.name?.last || "",
      email: data?.data?.email || "",
      contact: data?.data?.contact || "",
      address: data?.data?.address || "",
      newPassword: "",
      confirmPassword: "",
      profilePic: data?.data?.profileImage || "",
    });
  }, [data]);

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleFileChange = async (e) => {
    const selectedFile = e.target.files[0];

    const { files } = e.target;

    if (files.length === 0) {
      toast.error("At least one file is required");
      return;
    }
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];

    // Check if all selected files are of allowed types
    const invalidFiles = Array.from(files).filter(file => !allowedTypes.includes(file.type));
    
    if (invalidFiles.length > 0) {
      toast.error("Only JPG, JPEG, and PNG files are allowed.");
      return;
    }
    if (selectedFile) {
      setUploading(true);
      setLoading(true);
      setErrors({ ...errors, file: null });

      try {
        const formData = new FormData();
        formData.append("files", selectedFile);

        const presignedUrlResponse = await fetch(
          `${BASE_URL}/fileUpload/generate-presigned-url`,
          {
            method: "POST",
            body: formData,
          }
        );

        if (!presignedUrlResponse.ok) {
          throw new Error("Failed to get presigned URL");
        }

        const presignedUrlData = await presignedUrlResponse.json();
        const objectURL = Array.isArray(presignedUrlData.data) && presignedUrlData.data[0]?.path;

        setFormData((prevData) => ({
          ...prevData,
          profilePic: objectURL,
        }));

        toast.success("Profile picture uploaded successfully!");
      } catch (error) {
        console.error("Error uploading profile picture", error);
        toast.error("Error uploading profile picture");
        setErrors({
          ...errors,
          file: "Error uploading file. Please try again.",
        });
      } finally {
        setUploading(false);
        setLoading(false);
      }
    } else {
      setErrors({ ...errors, file: "File is required" });
    }
  };

  const validatePassword = (password) => {
    const passwordRegex =
      /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*#?&])[A-Za-z\d@$!%*#?&]{8,25}$/;
    return passwordRegex.test(password);
  };

  const validateForm = () => {
    let newErrors = {};

    if (!formData.firstName) newErrors.firstName = "First name is required";
    if (!formData.lastName) newErrors.lastName = "Last name is required";
    if (!formData.email) newErrors.email = "Email is required";
    if (!formData.contact) newErrors.contact = "Contact number is required";
    if (!formData.address) newErrors.address = "Address is required"; // Address validation
    if (!selectedCountry) newErrors.country = "Country is required";

    if (isChangePasswordChecked) {
      if (!formData.newPassword) {
        newErrors.newPassword = "New password is required";
      } else if (!validatePassword(formData.newPassword)) {
        newErrors.newPassword =
          "Password must be at least 8 characters long, include at least one letter, one number, and one special character, and be at most 25 characters.";
      }
      if (formData.newPassword !== formData.confirmPassword) {
        newErrors.confirmPassword = "Passwords do not match";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    const payload = {
      name: {
        first: formData.firstName,
        last: formData.lastName,
      },
      email: formData.email,
      password: isChangePasswordChecked ? formData.newPassword : undefined,
      contact: formData.contact,
      address: formData.address,
      profileImage: formData.profilePic,
      countryShortName: selectedCountry?.value,
      country: selectedCountry?.label,
      currency: selectedCurrency
    };

    setIsSubmitting(true);

    try {
      const response = await editProfileApi(payload);
      toast.success(response?.data?.message || "Profile updated successfully!");

      const updatedProfileResponse = await getProfileApi();
      const updatedProfile = updatedProfileResponse?.data;

      setFormData({
        firstName: updatedProfile?.data?.name?.first || "",
        lastName: updatedProfile?.data?.name?.last || "",
        email: updatedProfile?.data?.email || "",
        contact: updatedProfile?.data?.contact || "",
        address: updatedProfile?.data?.address || "",
        newPassword: "",
        confirmPassword: "",
        profilePic: updatedProfile?.data?.profileImage || "",
      });

      setIsChangePasswordChecked(false);
    } catch (error) {
      console.error("Error updating profile", error);
      toast.error("Error updating profile");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCountryChange = selectedOption => {
    setSelectedCountry(selectedOption);
    setSelectedCurrency(countryToCurrency[selectedOption?.value]);

  };

  const [aboutLoading, setAboutLoading] = useState(true);
  const [imgLoadedAbout, setImgLoadedAbout] = useState(false);
  const [imgFailedAbout, setImgFailedAbout] = useState(false);

  // Reset states on image URL change
  useEffect(() => {
    if (formData?.profilePic) {
      const img = new window.Image();
      img.src = `${process.env.NEXT_PUBLIC_S3_URL_FE}/${formData.profilePic}`;
      img.onload = () => {
        setImgLoadedAbout(true);
        setImgFailedAbout(false);
      };
      img.onerror = () => {
        setImgLoadedAbout(false);
        setImgFailedAbout(true);
      };
    } else {
      setImgLoadedAbout(false);
      setImgFailedAbout(false);
    }
  }, [formData?.profilePic]);

  // Simulate section data loading (adjust based on actual data fetching logic)
  useEffect(() => {
    if (formData) {
      setAboutLoading(false);
    }
  }, [formData]);

  const AboutSkeleton = () => (
    <div className="animate-pulse">
      <div className="h-8 w-40 bg-gray-300 rounded mb-6" />
      <div className="flex items-center mb-6 gap-4">
        <div className="w-16 h-16 rounded-full bg-gray-300" />
        <div>
          <div className="h-4 w-32 bg-gray-300 rounded mb-2" />
          <div className="h-3 w-24 bg-gray-300 rounded" />
        </div>
      </div>
      {[...Array(6)].map((_, i) => (
        <div key={i} className="mb-4">
          <div className="h-3 w-24 bg-gray-300 rounded mb-1" />
          <div className="h-10 w-full bg-gray-200 rounded-xl" />
        </div>
      ))}
    </div>
  );


  return (
    <>
    {aboutLoading ? (
      <AboutSkeleton />
    ) : (
      <div className="md:max-w-xl sm:mb-10 mb-4">
        <h2 className="text-[#40E0D0] flex gap-1 text-2xl font-bold mb-6">
          About
          <span className="text-black ml-0.5"> Me</span>
        </h2>

        <div className="flex items-center mb-6">
          <div className="w-16 h-16 bg-primary-blue text-black flex justify-center items-center rounded-full text-2xl font-semibold">
            {formData?.profilePic && !imgFailedAbout ? (
              <>
                {!imgLoadedAbout && (
                  <div className="absolute w-full h-full rounded-full bg-gray-300 animate-pulse z-10" />
                )}
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/${formData.profilePic}`}
                  width={64}
                  height={64}
                  alt="Profile"
                  className="w-full h-full rounded-full object-cover"
                />
              </>
            ) : (
              <span>{formData?.firstName?.[0]?.toUpperCase() || "M"}</span>
            )}
          </div>
          <div className="ml-4">
            <h3 className="font-semibold text-lg">{`${formData.firstName} ${formData.lastName}`}</h3>
            <label className="text-primary-blue text-sm cursor-pointer">
              Change Profile Photo
              <input type="file" onChange={handleFileChange} accept='.jpg, .jpeg, .png' className="hidden" />
            </label>
            {uploading && <p className="text-primary-blue text-sm">Uploading...</p>}
          </div>
        </div>

        <div className="sm:mb-5 mb-3">
          <label className="block text-gray-400 font-normal text-sm mb-1">
            First Name
          </label>
          <input
            type="text"
            name="firstName"
            className="w-full px-3 sm:py-4 py-3 border rounded-xl focus:outline-none focus:ring-1 focus:ring-primary-blue text-black text-sm placeholder:text-gray-500"
            placeholder="First Name"
            value={formData.firstName}
            onChange={handleInputChange}
          />
          {errors.firstName && (
            <p className="text-red-600 text-sm">{errors.firstName}</p>
          )}
        </div>

        <div className="sm:mb-5 mb-3">
          <label className="block text-gray-400 font-normal text-sm mb-1">
            Last Name
          </label>
          <input
            type="text"
            name="lastName"
            className="w-full px-3 sm:py-4 py-3 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
            placeholder="Last Name"
            value={formData.lastName}
            onChange={handleInputChange}
          />
          {errors.lastName && (
            <p className="text-red-600 text-sm">{errors.lastName}</p>
          )}
        </div>

        <div className="sm:mb-5 mb-3">
          <label className="block text-gray-400 font-normal text-sm mb-1">
            Email Address
          </label>
          <input
            type="email"
            name="email"
            className="w-full px-3 sm:py-4 py-3 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
            placeholder="Email Address"
            value={formData.email}
            onChange={handleInputChange}
          />
          {errors.email && <p className="text-red-600 text-sm">{errors.email}</p>}
        </div>

        <div className="sm:mb-5 mb-3">
          <label className="block text-gray-400 font-normal text-sm mb-1">
            Contact Number
          </label>
          <input
            type="text"
            name="contact"
            className="w-full px-3 sm:py-4 py-3 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
            placeholder="Your Contact Number"
            value={formData.contact}
            onChange={handleInputChange}
          />
          {errors.contact && (
            <p className="text-red-600 text-sm">{errors.contact}</p>
          )}
        </div>

        <div className="sm:mb-4 mb-3">
          <label className="block text-gray-400 font-normal text-sm mb-1">
            Address
          </label>
          <textarea
            name="address"
            className="w-full px-3 sm:py-4 py-3 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
            placeholder="Address"
            value={formData.address}
            onChange={handleInputChange}
          />
          {errors.address && (
            <p className="text-red-600 text-sm">{errors.address}</p>
          )}
        </div>

        <div className="sm:mb-4 mb-3">
          <label className="block text-gray-700 font-medium mb-2">Country:</label>
          <Select
            options={countries}
            value={selectedCountry}
            onChange={handleCountryChange}
            placeholder="Select Country"
            className="rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500"
          />
          {errors.country && (
            <p className="text-red-500 text-sm">{errors.country}</p>
          )}
        </div>

        <div className="sm:mb-6 mb-4 flex">
          <FormControlLabel
            className="m-0"
            control={
              <Checkbox
                checked={isChangePasswordChecked}
                onChange={() =>
                  setIsChangePasswordChecked(!isChangePasswordChecked)
                }
              />
            }
            label="Change Password"
          />
          
            <Link className="text-red-600 ml-auto pt-3 font-semibold" href="/accountdelete" target="__blank" prefetch={false}>
              Delete your account
            </Link>
          
        </div>

        <div className="flex gap-3">
          {isChangePasswordChecked && (
            <>
              {/* <div className="mb-4">
                <label className="block text-gray-700 font-medium mb-2">Current Password</label>
                <div className="relative">
                  <input
                    type={showCurrentPassword ? "text" : "password"}
                    name="currentPassword"
                    className="w-full px-4 py-2 border rounded-full focus:outline-none focus:ring-2 focus:ring-teal-500"
                    placeholder="Current Password"
                    value={formData.currentPassword}
                    onChange={handleInputChange}
                  />
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2 cursor-pointer">
                    {showCurrentPassword ? (
                      <VisibilityOutlinedIcon onClick={() => setShowCurrentPassword(false)} />
                    ) : (
                      <VisibilityOffOutlinedIcon onClick={() => setShowCurrentPassword(true)} />
                    )}
                  </div>
                </div>
                {errors.currentPassword && <p className="text-red-600 text-sm">{errors.currentPassword}</p>}
              </div> */}

              <div className="sm:mb-5 mb-3 w-full">
                <label className="block text-gray-400 font-normal text-sm mb-1">
                  New Password
                </label>
                <div className="relative">
                  <input
                    type={showNewPassword ? "text" : "password"}
                    name="newPassword"
                    className="w-full px-3 sm:py-4 py-3 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
                    placeholder="New Password"
                    value={formData.newPassword}
                    onChange={handleInputChange}
                  />
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2 cursor-pointer text-black/30">
                    {showNewPassword ? (
                      <VisibilityOutlinedIcon
                        onClick={() => setShowNewPassword(false)}
                      />
                    ) : (
                      <VisibilityOffOutlinedIcon
                        onClick={() => setShowNewPassword(true)}
                      />
                    )}
                  </div>
                </div>
                {errors.newPassword && (
                  <p className="text-red-600 text-sm">{errors.newPassword}</p>
                )}
              </div>

              <div className="mb-6 w-full">
                <label className="block text-gray-400 font-normal text-sm mb-1">
                  Confirm Password
                </label>
                <input
                  type="password"
                  name="confirmPassword"
                  className="w-full px-3 sm:py-4 py-3 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500"
                  placeholder="Confirm Password"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                />
                {errors.confirmPassword && (
                  <p className="text-red-600 text-sm">{errors.confirmPassword}</p>
                )}
              </div>
            </>
          )}
        </div>

        <div className="flex items-center px-5 gap-5">
          <button
            type="button"
            className="w-full sm:py-4 py-3 transition-all font-semibold px-4 rounded-full hover:text-white hover:bg-red-600 text-red-600 border-2 border-red-600"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className={`w-full sm:py-4 py-3 transition-all hover:bg-sky-blue-750 hover:text-white bg-primary-blue px-4 text-black font-semibold rounded-full ${
              isSubmitting && "bg-sky-blue-750"
            }`}
          >
            {isSubmitting ? "Submitting..." : "Save Changes"}
          </button>
        </div>
      </div>
      )}
    </>
    
  );
};

export default EditDetailsPage;
