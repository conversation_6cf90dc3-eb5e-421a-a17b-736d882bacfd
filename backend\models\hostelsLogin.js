import mongoose from 'mongoose';
import bcrypt from 'bcrypt';
const property_login_schema = mongoose.Schema(
  {
    name: {
      type:String
    },
    email: {
      type: String,
      trim: true,
      lowercase: true,
    },
    password: {
      type: String,
    },
    address: {
      type: String,
    },
    location: {
      type: { type: String },
      coordinates: { type: Array, default: [] },
    },
    contact: {
      type: String,
      minlength: 10,
    },
    role: {
      type: String,
      enum: ["admin", "sub_admin", "user", "hostel_owner"],
      default: 'user',
    },
   
    isEmailVerified: {
      type: Boolean,
      default: false,
    },
    isDeleted: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
    profileImage: {
      filename: {
        type: String
      },
      path: {
        type: String
      },
      objectURL: {
        type: String
      }
    },
    privacy: {
      email: {
        type: Boolean,
        default: false,
      },
      phone: {
        type: Boolean,
        default: false,
      },
    },
    subRole: {
      type: mongoose.SchemaTypes.ObjectId,
    },
    aboutMe: {
      type: String,
    },
    otp: {
      type: Number
    },
    otpExpires: {
      type: Date
    },
    isArchived: {
      type: Boolean,
      default: false
    },
    isActive: {
      type: Boolean,
      default: true
    },
    lastAccess: {
      type: Date,
      default: Date.now
    },
    countryCode: {
      type: Number
    },
    country: {
      type: String
    },
    currency: {
      type: String
    },
    countryShortName: {
      type: String
    },
    fcmTokens: [{ type: String }],
    isPremiumUser: {
      type: Boolean,
      default: false
    },
    lastSeen: {
      type: String
    },
    otaId:{
        type:Number
    }
  },
  {
    timestamps: true,
  },
);

/**
  * Check if password matches the user's password
  * @param {string} password
  * @returns {Promise<boolean>}
  */
// userSchema.methods.isPasswordMatch = async function (password) {
//   const user = this;
//   console.log(password)
//   return await bcrypt.compare(password, user.password);
// };

// userSchema.pre('save', async function (next) {
//   // eslint-disable-next-line no-invalid-this
//   const user = this;
//   if (user.isModified('password')) {
//     user.password = await bcrypt.hash(user.password, 8);
//   }
//   next();
// });

/**
 * @typedef User
 */
const hostelLoginModel = mongoose.model("HostelsLogin", property_login_schema ,"HostelsLogin");

export default hostelLoginModel;
