"use client";
import { Plus } from "lucide-react";
import Link from "next/link";
import React from "react";
import { FaRegTrashCan } from "react-icons/fa6";
import { FiEye } from "react-icons/fi";
import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight,
} from "react-icons/md";
import { TfiPencilAlt } from "react-icons/tfi";

const splitTextByWords = (text, wordsPerLine) => {
  const words = text.split(" ");
  const lines = [];
  for (let i = 0; i < words.length; i += wordsPerLine) {
    lines.push(words.slice(i, i + wordsPerLine).join(" "));
  }
  return lines;
};
const Faqs = () => {
  const faqsData = [
    {
      id: 1,
      questions: (
        <div>
          <span className="block text-gray-500 font-semibold">What is</span>
          <span className="block text-gray-500 font-semibold">mixdorm?</span>
          
        </div>
      ),

      answers:
        "MixDorm is a vibrant community-focused hostel network designed for young travelers and backpackers. We offer modern accommodations, unique features like Mix Ride and Mix Creators, and a range of activities to enhance your travel experience.",
    },
    {
      id: 2,
      questions: (
        <div>
          <span className="block text-gray-500 font-semibold">How can i</span>
          <span className="block text-gray-500 font-semibold">Book a stay at</span>
          <span className="block text-gray-500 font-semibold">Mixdorm?</span>
        </div>
      ),

      answers:
        "You must be at least 18 years of age to use our services and make a booking. By using our services, you represent and warrant that you meet the age requirement and have the legal capacity to enter into these Terms",
    },
    {
      id: 3,
      questions: (
        <div>
          <span className="block text-gray-500 font-semibold">What is</span>
          <span className="block text-gray-500 font-semibold">included in the</span>
          <span className="block text-gray-500 font-semibold">room rate?</span>
        </div>
      ),

      answers:
        "The room rate includes accommodation in our well-maintained dorms or private rooms. Additional amenities such as breakfast, tours, and activities may be available for an extra charge",
    },
    {
      id: 4,
      questions: (
        <div>
          <span className="block text-gray-500 font-semibold">What</span>
          <span className="block text-gray-500 font-semibold">payment</span>
          <span className="block text-gray-500 font-semibold">methods do</span>
          <span className="block text-gray-500 font-semibold">you accept?</span>
        </div>
      ),

      answers:
        "We accept major credit cards, debit cards, and other online payment methods. Payment is processed securely through our payment gateway.",
    },
    {
      id: 5,
      questions: (
        <div>
          <span className="block text-gray-500 font-semibold">Is my booking</span>
          <span className="block text-gray-500 font-semibold">guarantee?</span>
        </div>
      ),

      answers:
        "Yes, once you receive a confirmation email, your booking is guaranteed. We use secure payment methods to ensure your reservation is safe",
    },
    {
      id: 6,
      questions: (
        <div>
          <span className="block text-gray-500 font-semibold">Can i modify</span>
          <span className="block text-gray-500 font-semibold">or cancel my</span>
          <span className="block text-gray-500 font-semibold">booking?</span>
        </div>
      ),

      answers:
        "Yes, you can modify or cancel your booking based on our Cancellation Policy. Please visit our website or contact our customer support team for assistance with changes.",
    },

  ];

  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <div className="flex items-center justify-between w-full ">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Faqs
        </h2>
        <div className="w-[50%] gap-x-5 flex justify-end items-center">
          <Link
            href={"/superadmin/dashboard/faqs-add"}
            className={` px-4 py-2 text-sm font-medium  font-poppins text-white rounded relative flex justify-center items-center bg-sky-blue-650 `}
            type="button"
          >
            <Plus size={18} className="mr-1" /> Faqs
          </Link>
        </div>
      </div>
      <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border dark:border-none">
        <table className="w-full divide-y bg-white rounded-xl divide-gray-200 dark:bg-black">
          <thead>
            <tr className="">
              <th className="py-6 bg-white text-left pl-4 text-sm font-semibold font-poppins text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                QUESTIONS
              </th>

              <th className=" py-6 bg-white  text-sm font-semibold text-center font-poppins text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                ANSWERS
              </th>

              <th className="py-6 pl-6  bg-white text-center text-sm font-poppins font-semibold text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                ACTION
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 border-x border-y dark:border-x-0 text-black/70 dark:text-[#757575]">
            {faqsData.map((faqsItem) => {
              // const { firstPart, secondPart } = splitMessage(
              //   aboutItem.answers
              // );
              return (
                <tr key={faqsItem.id} className="">
                  <td className=" whitespace-nowrap  px-5 text-gray-500 text-sm font-medium font-poppins dark:text-[#757575]">
                    {faqsItem.questions}
                  </td>

                  <td className="table-cell sm:table-cell lg:hidden whitespace-nowrap px-5 pl-10 lg:pl-28 py-4">
                    {splitTextByWords(faqsItem.answers, 6).map(
                      (line, index) => (
                        <p
                          key={index}
                          className="text-base leading-7 text-gray-500 font-medium font-poppins dark:text-[#757575]"
                        >
                          {line}
                        </p>
                      )
                    )}
                  </td>

                  <td className="hidden lg:table-cell whitespace-nowrap px-5 pl-10 lg:pl-28 py-4">
                    {splitTextByWords(faqsItem.answers, 12).map(
                      (line, index) => (
                        <p
                          key={index}
                          className="text-sm leading-7 text-gray-500 font-medium font-poppins dark:text-[#757575]"
                        >
                          {line}
                        </p>
                      )
                    )}
                  </td>

                  <td className="py-28 md:py-28 lg:py-14 pl-8 lg:pl-8 px-2 flex justify-end">
                    <Link
                      href={"/superadmin/dashboard/faqs-details"}
                      className=" border p-2 rounded-l-lg text-black/75 hover:text-blue-700 dark:text-[#757575] dark:hover:text-blue-700"
                    >
                      <FiEye />
                    </Link>

                    <Link
                      href={"/superadmin/dashboard/faqs-edit"}
                      className=" border p-2 text-black/75 hover:text-yellow-400 dark:text-[#757575] dark:hover:text-yellow-400"
                    >
                      <TfiPencilAlt />
                    </Link>

                    <button className=" p-2 border rounded-r-lg text-red-600">
                      <FaRegTrashCan />
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
       <div className="flex justify-between items-center mt-5">
                    <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
                    <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
                      <a
                        href="#"
                        className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                      >
                        <span className="sr-only">Next Page</span>
                        <MdOutlineKeyboardArrowLeft />
                      </a>
            
                      <a
                        href="#"
                        className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                      >
                        <span className="sr-only">Next Page</span>
                        <MdOutlineKeyboardArrowRight />
                      </a>
                    </div>
                  </div>
    </div>
  );
};

export default Faqs;
