// controller/propertyLikeController.js
import Response from '../utills/response.js'
import Wallets from '../models/wallets.js';
import WalletHistory from '../models/walletHistory.js';
export const balanceAddInWallet = async (req, res) => {
    try {
        const { orderId, paymentId, signature, amount, currency } = req.body;
        // Generate HMAC using Razorpay key secret
        const hmac = crypto.createHmac('sha256', process.env.RAZORPAY_KEY_SECRET);
        hmac.update(`${orderId}|${paymentId}`);
        const generated_signature = hmac.digest('hex');
        if (generated_signature === signature) {
            try {
                if (amount || !currency ) {
                    return Response.BadRequest(res, null, 'Missing required fields.');
                }
                let wallet = await Wallets.findOne({ user: userId });
                if (wallet) {
                    // Update wallet balance
                    wallet.amount += amount;
                    await wallet.save();
                } else {
                    // Create a new wallet for the user
                    wallet = new Wallets({
                        user: userId,
                        amount,
                        currency,
                    });
                    await wallet.save();
                }
                const walletHistory = new WalletHistory({
                    user: userId,
                    amount,
                    currency,
                    type: 'credit',
                    paymentId,
                    paymentStatus:'paid',
                });
                await walletHistory.save();
                return Response.OK(res, { wallet, transaction: walletHistory }, 'Wallet updated successfully.');

            } catch (error) {
                res.status(500).json({ status: 'Error saving payment information', error: error.message });
            }
        }

    } catch (error) {
        console.error(error);
        return res.status(500).json({ message: 'Internal Server Error' });
    }
}
export const getUserBalance = async (req, res) => {
  try {
    const { userId } = req.params;

    // Check if a wallet exists for the user
    const wallet = await Wallets.findOne({ user: userId });

    if (!wallet) {
      return Response.NotFound(res, null, 'Wallet not found for the user');
    }

    return Response.OK(res, { balance: wallet.amount, currency: wallet.currency }, 'Wallet balance retrieved successfully');
  } catch (error) {
    console.error('Error fetching user wallet balance:', error.message);
    return Response.InternalServerError(res, null, 'Error fetching wallet balance');
  }
};
