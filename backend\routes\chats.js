import express from 'express';
import { sendMessage, getChatHistory, markAsRead, getChatUsers,
    deleteAllMessages,markAllChatsAsDeleted ,sendInvitation,respondToInvitation,getReceivedInvitations,
    blockUser,
    unblockUser,
    getBlockedUsers} from '../controller/chats.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();

// Route to send a new message
router.post('/send-message', checkAuth('send_message'), sendMessage);

router.post('/send-invitation', checkAuth('send_message'), sendInvitation);

router.put('/respond-invitation/:invitationId', checkAuth('send_message'), respondToInvitation);

// Route to get chat history between two users
router.get('/chat-history/:receiverId', checkAuth('get_chats'), getChatHistory);
router.get("/invitations-received", checkAuth('get_chats'), getReceivedInvitations);

// Get Chat Users
router.get('/chat-users', checkAuth('get_chats'), getChatUsers);

// Route to mark a message as read
router.patch('/mark-as-read/:receiverId', checkAuth(''), markAsRead);

// Route to delete a message
router.delete('/delete-message/:messageId', checkAuth, deleteAllMessages);

// Route to mark all messages as deleted for the logged-in user
router.patch('/clear-chats', checkAuth('delete_chats'), markAllChatsAsDeleted);


router.post('/block-user', checkAuth(''), blockUser);
router.post('/unblock-user', checkAuth(''), unblockUser);
router.get('/blocked-users', checkAuth(''), getBlockedUsers);

export default router;

/**
 * @swagger
 * tags:
 *   name: Chat
 *   description: API endpoints for chat functionality
 */

/**
 * @swagger
 * /chats/send-message:
 *   post:
 *     summary: Send a new message to a user
 *     tags: [Chat]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               receiver:
 *                 type: string
 *                 description: The ID of the user receiving the message.
 *               message:
 *                 type: string
 *                 description: The message content.
 *               type:
 *                 type: string
 *                 description: The type of the message (e.g., text, image).
 *               attachments:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: List of file attachments.
 *     responses:
 *       200:
 *         description: Message sent successfully
 *       400:
 *         description: Bad request (e.g., missing parameters)
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /chats/chat-history/{receiverId}:
 *   get:
 *     summary: Retrieve chat history between authenticated user and another user
 *     tags: [Chat]
 *     parameters:
 *       - in: path
 *         name: receiverId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the user to get the chat history with.
 *     responses:
 *       200:
 *         description: Successfully retrieved chat history
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /chats/mark-as-read/{messageId}:
 *   patch:
 *     summary: Mark a message as read
 *     tags: [Chat]
 *     parameters:
 *       - in: path
 *         name: messageId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the message to mark as read.
 *     responses:
 *       200:
 *         description: Message marked as read successfully
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /chats/delete-message/{messageId}:
 *   delete:
 *     summary: Delete a message
 *     tags: [Chat]
 *     parameters:
 *       - in: path
 *         name: messageId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the message to delete.
 *     responses:
 *       200:
 *         description: Message deleted successfully
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /chats/chat-users:
 *   get:
 *     summary: Retrieve a list of users the authenticated user has chatted with, including unread message counts and user names
 *     tags: [Chat]
 *     responses:
 *       200:
 *         description: Successfully retrieved chat users list with unread counts
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 users:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                         description: The ID of the user.
 *                       name:
 *                         type: string
 *                         description: The name of the user.
 *                       unreadCount:
 *                         type: integer
 *                         description: The number of unread messages from this user.
 *       401:
 *         description: Unauthorized (user not authenticated)
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /chats/clear-chats:
 *   patch:
 *     summary: Mark all chats with a specific user as deleted
 *     tags: [Chat]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *                 description: The ID of the user whose chat messages should be marked as deleted
 *             required:
 *               - userId
 *     responses:
 *       200:
 *         description: Successfully marked chats as deleted
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Success message
 *                 modifiedCount:
 *                   type: integer
 *                   description: Number of chat messages marked as deleted
 *       401:
 *         description: Unauthorized (user not authenticated)
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /chats/respond-invitation/{invitationId}:
 *   put:
 *     summary: Respond to a chat invitation by accepting or rejecting it
 *     tags: [Chat]
 *     parameters:
 *       - in: path
 *         name: invitationId
 *         required: true
 *         description: ID of the chat invitation
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: string
 *                 enum:
 *                   - accepted
 *                   - rejected
 *                 description: Response to the invitation
 *                 example: "accepted"
 *     responses:
 *       200:
 *         description: Successfully responded to the invitation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Response message
 *                   example: "Invitation successfully responded to."
 *                 invitation:
 *                   type: object
 *                   description: Updated invitation details
 *                   properties:
 *                     status:
 *                       type: string
 *                       description: Updated status of the invitation
 *                       example: "accepted"
 *                     sender:
 *                       type: string
 *                       description: ID of the user who sent the invitation
 *                       example: "user123"
 *                     receiver:
 *                       type: string
 *                       description: ID of the user who received the invitation
 *                       example: "user456"
 *                     _id:
 *                       type: string
 *                       description: ID of the invitation
 *                       example: "64b3e0d4c12345678e1e1e1e"
 *                 chatRoom:
 *                   type: object
 *                   description: Details of the chat room (if created or found)
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: ID of the chat room
 *                       example: "chatroom789"
 *                     participants:
 *                       type: array
 *                       description: IDs of the chat room participants
 *                       items:
 *                         type: string
 *                         example: "user123"
 *       404:
 *         description: Invitation not found
 *       403:
 *         description: User is not authorized to respond to this invitation
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /chats/send-invitation:
 *   post:
 *     summary: Send a chat invitation to another user
 *     tags: [Chat]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               senderId:
 *                 type: string
 *                 description: ID of the user sending the invitation
 *                 example: "user123"
 *               receiverId:
 *                 type: string
 *                 description: ID of the user receiving the invitation
 *                 example: "user456"
 *               message:
 *                 type: string
 *                 description: Optional message included with the invitation
 *                 example: "Let's start a chat!"
 *     responses:
 *       201:
 *         description: Invitation sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Success message
 *                   example: "Invitation sent successfully."
 *                 invitation:
 *                   type: object
 *                   description: Details of the created invitation
 *                   properties:
 *                     _id:
 *                       type: string
 *                       description: ID of the created invitation
 *                       example: "invitation123"
 *                     senderId:
 *                       type: string
 *                       description: ID of the sender
 *                       example: "user123"
 *                     receiverId:
 *                       type: string
 *                       description: ID of the receiver
 *                       example: "user456"
 *                     status:
 *                       type: string
 *                       description: Current status of the invitation
 *                       example: "pending"
 *       400:
 *         description: Bad request (e.g., missing required fields)
 *       403:
 *         description: User is not authorized to send an invitation
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /chats/invitations-received:
 *   get:
 *     summary: Get invitations received by the logged-in user
 *     tags: [Chat]
 *     security:
 *       - bearerAuth: [] # Ensure the API requires authentication
 *     responses:
 *       200:
 *         description: List of received invitations
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Success message
 *                   example: "Received invitations retrieved successfully."
 *                 invitations:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         description: Invitation ID
 *                         example: "63e4b7fbe52b7a00123e4cda"
 *                       senderId:
 *                         type: string
 *                         description: ID of the sender
 *                         example: "user123"
 *                       receiverId:
 *                         type: string
 *                         description: ID of the receiver
 *                         example: "user456"
 *                       status:
 *                         type: string
 *                         description: Status of the invitation
 *                         example: "pending"
 *       401:
 *         description: Unauthorized (user not authenticated)
 *       404:
 *         description: No invitations found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /chats/block-user:
 *   post:
 *     summary: Block a user to disable chat and messages from them
 *     tags: [Chat]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               blockedUserId:
 *                 type: string
 *                 description: The ID of the user to block.
 *             required:
 *               - blockedUserId
 *     responses:
 *       200:
 *         description: User blocked successfully
 *       400:
 *         description: Bad request (e.g., blocking yourself or already blocked)
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /chats/unblock-user:
 *   post:
 *     summary: Unblock a previously blocked user to restore chat functionality
 *     tags: [Chat]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               blockedUserId:
 *                 type: string
 *                 description: The ID of the user to unblock.
 *             required:
 *               - blockedUserId
 *     responses:
 *       200:
 *         description: User unblocked successfully
 *       404:
 *         description: User was not blocked
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /chats/blocked-users:
 *   get:
 *     summary: Retrieve a list of users the authenticated user has chatted with, including unread message counts and user names
 *     tags: [Chat]
 *     responses:
 *       200:
 *         description: Successfully retrieved chat users list with unread counts
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 users:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                         description: The ID of the user.
 *                       name:
 *                         type: string
 *                         description: The name of the user.
 *                       unreadCount:
 *                         type: integer
 *                         description: The number of unread messages from this user.
 *       401:
 *         description: Unauthorized (user not authenticated)
 *       500:
 *         description: Internal server error
 */
