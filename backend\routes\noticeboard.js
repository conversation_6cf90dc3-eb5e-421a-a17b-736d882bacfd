import express from 'express';
import { listNoticeboardController, getUnreadCount, updateRead, addSocialInteractionComment, addSocialInteraction, getSocialInteractionLikes, addSocialInteractionLike, likeSocialInteractionComment, replyToSocialInteractionComment, getAllCommentsWithReplies } from '../controller/noticeboard.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();

router.get('/', checkAuth('view_notifications'), listNoticeboardController);
router.put("/", checkAuth('view_notifications'), updateRead);
router.get("/counts", checkAuth('view_notifications'), getUnreadCount);
router.post("/social-interactions", checkAuth('view_notifications'), addSocialInteraction);
router.post("/social-interactions-comment", checkAuth('view_notifications'), addSocialInteractionComment);
router.get(
    "/social-interactions-comment/:socialIntrectionId",
    checkAuth('view_notifications'),
    getAllCommentsWithReplies
  );
  router.post(
    "/social-interactions-like",
    checkAuth('view_notifications'),
    addSocialInteractionLike
  );
  
  router.get(
    "/social-interactions-like/:socialIntrectionId",
    checkAuth('view_notifications'),
    getSocialInteractionLikes
  );
  
router.post("/social-interactions-comment-like", checkAuth('view_notifications'), likeSocialInteractionComment);
router.post("/social-interactions-comment-reply", checkAuth('view_notifications'), replyToSocialInteractionComment);
router.get("/social-interactions-comments", checkAuth('view_notifications'), replyToSocialInteractionComment);

export default router;
/**
 * @swagger
 * /noticeboard:
 *   get:
 *     summary: Get noticeboard (notifications)
 *     description: Retrieves all notifications for the authenticated user, sorted by creation date. Supports filtering by type.
 *     tags:
 *       - Notifications
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         required: false
 *         description: Type of notification to filter by (e.g., "city", "notice")
 *     responses:
 *       200:
 *         description: Successfully retrieved notifications
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         example: "61bc8f859e0a5b0012345678"
 *                       userId:
 *                         type: string
 *                         example: "5fbc8f859e0a5b0012345678"
 *                       title:
 *                         type: string
 *                         example: "Noticeboard Update"
 *                       body:
 *                         type: string
 *                         example: "A new policy update has been posted."
 *                       data:
 *                         type: object
 *                         additionalProperties: true
 *                         example: { "key": "value" }
 *                       fcmToken:
 *                         type: array
 *                         items:
 *                           type: string
 *                         example: ["fcmToken1", "fcmToken2"]
 *                       status:
 *                         type: string
 *                         example: "sent"
 *                       error:
 *                         type: string
 *                         example: null
 *                       type:
 *                         type: string
 *                         example: "notice"
 *                       business:
 *                         type: string
 *                         example: "5fa7b5c9a2e8b40012245c91"
 *                       isRead:
 *                         type: boolean
 *                         example: false
 *                       shift:
 *                         type: string
 *                         example: "shiftObjectId"
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                         example: "2024-10-10T10:30:00Z"
 *       404:
 *         description: No notifications found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "No notifications found."
 *       500:
 *         description: Error retrieving notifications
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Error retrieving notifications."
 */
/**
 * @swagger
 * /noticeboard:
 *   put:
 *     summary: Mark notifications as read
 *     tags: [Notifications]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               notificationIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of notification IDs to be marked as read
 *             example:
 *               notificationIds: ["64a8b96f789c8b4b4402c1a3", "64a8b96f789c8b4b4402c1a4"]
 *     responses:
 *       200:
 *         description: Notifications marked as read successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: SUCCESS
 *       400:
 *         description: Bad request, no notification IDs provided
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: No notification IDs provided
 *       404:
 *         description: Notification not found
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /noticeboard/counts:
 *   get:
 *     summary: Get the count of unread notifications
 *     tags: [Notifications]
 *     responses:
 *       200:
 *         description: The count of unread notifications retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 count:
 *                   type: integer
 *                   description: The total number of unread notifications
 *                 message:
 *                   type: string
 *                   example: SUCCESS
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */
/**
 * @swagger
 * /noticeboard/social-interactions:
 *   post:
 *     summary: Add a new noticeboard entry
 *     description: Adds a new notice to the noticeboard with social interactions.
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               comment:
 *                 type: string
 *                 example: "Excited for this event!"
 *               videos:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["video1.mp4", "video2.mp4"]
 *               photos:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["photo1.jpg", "photo2.jpg"]
 *               experience:
 *                 type: string
 *                 example: "Had an amazing time!"
 *     responses:
 *       201:
 *         description: Noticeboard entry created successfully
 *       400:
 *         description: Bad request, missing required fields
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /noticeboard/social-interactions:
 *   post:
 *     summary: Add a new noticeboard entry
 *     description: Adds a new notice to the noticeboard with social interactions.
 *     tags: [Notifications]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               comment:
 *                 type: string
 *                 example: "Excited for this event!"
 *               videos:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["video1.mp4", "video2.mp4"]
 *               photos:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["photo1.jpg", "photo2.jpg"]
 *               experience:
 *                 type: string
 *                 example: "Had an amazing time!"
 *     responses:
 *       201:
 *         description: Noticeboard entry created successfully
 *       400:
 *         description: Bad request, missing required fields
 *       500:
 *         description: Internal server error
 */
/**
 * @swagger
 * /noticeboard/social-interactions-comment:
 *   post:
 *     summary: Add a comment to a social interaction
 *     description: Allows an authenticated user to post a comment (with optional rating and images) on a social interaction (e.g., a ride).
 *     tags:
 *       - Social Interactions
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               socialIntrection:
 *                 type: string
 *                 example: "6624f84b34a4ef5ab2d7e621"
 *               comment:
 *                 type: string
 *                 example: "Great experience with this ride!"
 *               rating:
 *                 type: number
 *                 minimum: 0
 *                 maximum: 5
 *                 example: 4.5
 *               images:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     url:
 *                       type: string
 *                       example: "https://example.com/image1.jpg"
 *                     title:
 *                       type: string
 *                       example: "At the start of the ride"
 *     responses:
 *       201:
 *         description: Successfully added comment
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Social interaction comment added successfully."
 *                 data:
 *                   $ref: '#/components/schemas/SocialInteractionComment'
 *       400:
 *         description: Missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "At least one of comment, image, or social interaction is required."
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error."
 */
/**
 * @swagger
 * /noticeboard/social-interactions-comment/{socialIntrectionId}:
 *   get:
 *     summary: Get all comments for a social interaction
 *     description: Retrieves all comments related to a specific social interaction, sorted by latest.
 *     tags:
 *       - Social Interactions
 *     parameters:
 *       - in: path
 *         name: socialIntrectionId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the social interaction (e.g., ride)
 *     responses:
 *       200:
 *         description: Successfully retrieved comments
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                   example: Comments fetched successfully.
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/SocialInteractionComment'
 *       404:
 *         description: No comments found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /noticeboard/social-interactions-like/{socialIntrectionId}:
 *   get:
 *     summary: Get all likes for a social interaction
 *     description: Retrieves all likes related to a specific social interaction.
 *     tags:
 *       - Social Interactions
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: socialIntrectionId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the social interaction (e.g., ride)
 *     responses:
 *       200:
 *         description: Successfully retrieved likes
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                   example: Likes fetched successfully.
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                       user:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                           profileImage:
 *                             type: string
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /noticeboard/social-interactions-like:
 *   post:
 *     summary: Add a like to a social interaction
 *     description: Allows an authenticated user to like a specific social interaction.
 *     tags:
 *       - Social Interactions
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               socialIntrection:
 *                 type: string
 *                 example: "6543f0abc123de4567fab999"
 *     responses:
 *       201:
 *         description: Like added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Like added successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     _id:
 *                       type: string
 *                     user:
 *                       type: string
 *                     socialIntrection:
 *                       type: string
 *       400:
 *         description: Already liked
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /noticeboard/social-interactions-comment-like:
 *   post:
 *     summary: Add a like to a social interaction
 *     description: Allows an authenticated user to like a specific social interaction.
 *     tags:
 *       - Social Interactions
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               commentId:
 *                 type: string
 *                 example: "6543f0abc123de4567fab999"
 *     responses:
 *       201:
 *         description: Like added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Like added successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     _id:
 *                       type: string
 *                     user:
 *                       type: string
 *                     socialIntrection:
 *                       type: string
 *       400:
 *         description: Already liked
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /noticeboard/social-interactions-comment-reply:
 *   post:
 *     summary: Add a like to a social interaction
 *     description: Allows an authenticated user to like a specific social interaction.
 *     tags:
 *       - Social Interactions
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               commentId:
 *                 type: string
 *                 example: "6543f0abc123de4567fab999"
 *               comment:
 *                 type: string
 *                 example: "wow"
 *     responses:
 *       201:
 *         description: Like added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Like added successfully.
 *                 data:
 *                   type: object
 *                   properties:
 *                     _id:
 *                       type: string
 *                     user:
 *                       type: string
 *                     socialIntrection:
 *                       type: string
 *       400:
 *         description: Already liked
 *       500:
 *         description: Internal server error
 */
