import Response from "../utills/response.js";
import { addEvent, updateEventById, getEventById, 
    listPropertyEvents, listAllEvents, deleteEventById,viewMembers } from "../services/event.js";
import reviewModel from "../models/eventReviews.js"; 
import {sendPushNotification} from "../utills/firebase.js"
import {noticeboardAdd} from "../controller/noticeboard.js"
import eventBookingModel from "../models/eventBooking.js";
const addEventController = async (req, res) => {
    try {
      const createdBy = req.user._id; 
      const eventData = { ...req.body, createdBy }; 
            //   const propertyNumber = await generateP();
            //   data.number = propertyNumber; // Assign the generated property number
      
      const newEvent = await addEvent(eventData);
      const eventMessage = `${eventData.title} Event Hosted on date ${eventData.startDate}`
      await noticeboardAdd(req.user._id,eventMessage,'events','Event Created',true)
      await sendPushNotification(true,[], "Event Created", 'created',{event:newEvent._id},'event','Event Host by',req.user._id)
      return Response.Created(res, newEvent, 'Event Added Successfully');
    } catch (error) {
      return Response.InternalServerError(res, null, error.message);
    }
  };

const updateEventController = async (req, res) => {
    try {
        const { id } = req.params;
        const updatedEvent = await updateEventById(id, req.body);
        return Response.OK(res, updatedEvent, 'Event Updated Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const getEventByIdController = async (req, res) => {
    try {
        const { id } = req.params;
        const event = await getEventById(id);
        return Response.OK(res, event, 'Event Retrieved Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const listPropertyEventsController = async (req, res) => {
    try {
        const { propertyId } = req.params;
        let { page, limit, ...filter } = req.query;

        // Convert page and limit to integers
        page = parseInt(page);
        limit = parseInt(limit);

        // If page or limit are not valid numbers, default them
        if (isNaN(page) || page <= 0) {
            page = 1;
        }
        if (isNaN(limit) || limit <= 0) {
            limit = 10;
        }

        const eventsData = await listPropertyEvents(propertyId, filter, page, limit);

        const totalPages = Math.ceil(eventsData.totalEvents / parseInt(limit));
        const pagination = {
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages,
            totalEvents: eventsData.totalEvents
        };

        return Response.OK(res, { events: eventsData.events, pagination }, 'Events Retrieved Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const listAllEventsController = async (req, res) => {
    try {
        let { page, limit, ...filter } = req.query;

        // Convert page and limit to integers
        page = parseInt(page);
        limit = parseInt(limit);

        // If page or limit are not valid numbers, default them
        if (isNaN(page) || page <= 0) {
            page = 1;
        }
        if (isNaN(limit) || limit <= 0) {
            limit = 10;
        }

        const eventsData = await listAllEvents(filter, page, limit);

        const totalPages = Math.ceil(eventsData.totalEvents / parseInt(limit));
        const pagination = {
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages,
            totalEvents: eventsData.totalEvents
        };

        return Response.OK(res, { events: eventsData.events, pagination }, 'Events Retrieved Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const deleteEventController = async (req, res) => {
    try {
        const { id } = req.params;
        const result = await deleteEventById(id);
        return Response.OK(res, result, 'Event Deleted Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};
export const eventCheckout = async (req, res) => {
    try {
        const person = new Date(req.body.person);

        if (isNaN(person)) {
            return Response.BadRequest(res, null, 'Invalid person Number');
        }
        const eventData = await getEventById.findOne({ _id: req.params.eventId });

        if (!eventData) {
            return Response.NotFound(res, null, 'Event not found');
        }

        const { title, startDate, endDate,duration,price,address,location,description } = eventData;
        const totalPrice = price*person;

        const responseData = {
            title, startDate, endDate,duration,price,address,location,description,totalPrice
        };

        return Response.Created(res, responseData, 'Event Checkout details retrieved successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};

const addEventReviews = async (req, res) => {
  try {
    const userId = req.user._id; 
    const { eventId } = req.params;
    const { rating, comment, images ,likes,dislikes} = req.body;
    const reviewData = {
      user: userId,
      event: eventId,
      rating,
      comment,
      images,
      likes,
      dislikes
    };

    const newReview = await reviewModel.create(reviewData);
    return Response.Created(res, newReview, 'Review Added Successfully');
  } catch (error) {
    return Response.InternalServerError(res, null, error.message);
  }
};
export const getEvenetsMembers = async (req, res) => {
    try {
        let { page, limit, ...filter } = req.query;

        // Convert page and limit to integers
        page = parseInt(page);
        limit = parseInt(limit);

        // If page or limit are not valid numbers, default them
        if (isNaN(page) || page <= 0) {
            page = 1;
        }
        if (isNaN(limit) || limit <= 0) {
            limit = 10;
        }

        const eventsMembers = await viewMembers(req.user._id, filter, page, limit);

        const totalPages = await eventBookingModel.countDocuments({createdBy: req.user._id })
        const pagination = {
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages,
        };

        return Response.OK(res, { eventsMembers, pagination }, 'Events Retrieved Successfully');
    } catch (error) {
        return Response.InternalServerError(res, null, error.message);
    }
};
export { addEventController, updateEventController, getEventByIdController,
     listPropertyEventsController, listAllEventsController, deleteEventController ,
     addEventReviews};