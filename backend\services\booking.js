import bookingModel from '../models/bookings.js';
import roomModel from '../models/room.js';
import subBookingModel from '../models/subBookings.js';
import { generateBookingReferenceNumber } from '../utills/helper.js';
import mongoose from 'mongoose';
// Function to add a new booking
const addBooking = async (data) => {
  try {
    const room = await roomModel.findById({ _id: data.room }, 'property rate currency').populate({
      path: 'property',
      select: 'address.country'
    });
    const country = room.property.address['country'];
    if (!country) {
      throw new Error('Country not found');
    }

    const bookingReferenceNumber = await generateBookingReferenceNumber(country);
    data.referenceNumber = bookingReferenceNumber;

    // if (!data.rate) {
    //     const date = new Date();
    //     const day = date.getDay();
    //     if (day === 0 || day === 6) {
    //         data.rate = room.rate['weekendRate'];
    //     }
    //     else {
    //         data.rate = room.rate['weekdayRate'];
    //     }
    // }

    if (!data.currency) {
      data.currency = room.currency;
    }
    const newBooking = await bookingModel.create(data);
    return newBooking;
  } catch (error) {
    console.error('Error creating property:', error.message);
    throw error;
  }
};

// Function to update a booking by ID
const updateBookingById = async (bookingId, newData, userId) => {
  try {
    const booking = await subBookingModel.findById({ _id: bookingId });
    if (!booking) {
      throw new Error('Booking Not Found');
    }

    if (newData.isCancel) {
      newData.cancelledDate = Date.now();
      newData.cancelledBy = userId
      newData.isCancel = true
    }

    const updatedBooking = await subBookingModel.findByIdAndUpdate(bookingId, { ...newData }, { new: true });
    return updatedBooking;
  } catch (error) {
    console.error("Error updating booking:", error.message);
    throw error;
  }
};

// Function to get a booking by ID
const getBookingById = async (bookingId) => {
  try {
    const booking = await subBookingModel.findById({ _id: bookingId }).populate('user', 'name email');;
    if (!booking) {
      throw new Error('Booking Not Found');
    }
    return booking;
  } catch (error) {
    console.error("Error getting booking:", error.message);
    throw error;
  }
};

// Function to list all bookings by user with pagination and filter
export const listAllBookingsByUser = async (userId, filter = {}, page = 1, limit = 10) => {
  const skip = (page - 1) * limit;

  const pipeline = [
    {
      $match: {
        user: new mongoose.Types.ObjectId(userId),
        isDeleted: false,
        ...filter, // This will now include { subBookingId: "some-id" } if passed
      },
    },
    {
      $lookup: {
        from: 'properties',
        let: { propertyId: '$property' },
        pipeline: [
          {
            $match: {
              $expr: { $eq: ['$_id', '$$propertyId'] },
              isDeleted: false, // optional filter
            },
          },
          {
            $project: {
              _id: 1,
              name: 1,
              location: 1,
              images: 1,
              address: 1
              // Add any specific fields you want from the property
            },
          },
        ],
        as: 'property',
      },
    },
    {
      $lookup: {
        from: 'rooms',
        let: { roomId: '$room' },
        pipeline: [
          {
            $match: {
              $expr: { $eq: ['$_id', '$$roomId'] },
              isDeleted: false, // optional filter
            },
          },
          {
            $project: {
              _id: 1,
              name: 1,
            },
          },
        ],
        as: 'room',
      },
    },
    {
      $addFields: {
        room: { $arrayElemAt: ['$room', 0] },
        property: { $arrayElemAt: ['$property', 0] },
      },
    },
    {
      $project: {
        bookingId: '$subBookingId',
        checkInDate: 1,
        checkOutDate: 1,
        roomName: '$room.name',
        totalAmount: 1,
        status: 1,
        currency: 1,
        guests: 1,
        rate: 1,
        payableNow: 1,
        payableOnArrival: 1,
        baseAdvance: 1,
        gst: 1,
        paymentGatewayCharge: 1,
        property: 1,
        isCancel: 1,
        cancelledDate: 1,
        ratePlan:1,
        room:1,
        nights:1
      },
    },
    { $sort: { createdAt: -1 } },
    { $skip: skip },
    { $limit: limit },
  ];

  const subBookings = await subBookingModel.aggregate(pipeline);

  const totalCount = await subBookingModel.countDocuments({
    user: new mongoose.Types.ObjectId(userId),
    isDeleted: false,
    ...filter,
  });

  return {
    subBookings,
    total: totalCount,
  };
};

// Function to soft delete a booking by ID
const deleteBookingById = async (bookingId) => {
  try {
    const booking = await bookingModel.findById({ _id: bookingId });
    if (!booking) {
      throw new Error('Booking Not Found');
    }
    await bookingModel.findByIdAndUpdate(bookingId, { $set: { isDeleted: true, isActive: false } });
    return { message: 'Deleted Successfully' };
  } catch (error) {
    console.error("Error deleting booking:", error.message);
    throw error;
  }
};

// list all bookings to property owner according to the 
/**
 * reservation tab :- all bookings in ascending order of refrence number
 * arrivals tab :- all bookings that are upcoming from today's date
 * departure tab :- will inform later, we can do that today who all will check out
 * in house :- in between check in & check out :- sort => check in => descending order
 */


const listBookings = async (page, limit, category, property, startDate, endDate, status) => {
  const skip = (page - 1) * limit;
  let date;

  // Base match condition for the aggregation pipeline
  let matchCondition = {};

  switch (category) {
    case 'reservations':
      matchCondition = {}; // No additional conditions for reservations
      break;

    case 'arrivals':
      date = new Date();
      matchCondition = { checkInDate: { $gte: date } };
      break;

    case 'departures':
      date = new Date();
      matchCondition = { checkOutDate: { $gte: date } };
      break;

    case 'inhouse':
      date = new Date();
      matchCondition = {
        checkInDate: { $lte: date },
        checkOutDate: { $gte: date },
      };
      break;

    default:
      throw new Error("Invalid category");
  }

  if (property) {
    matchCondition.property = new mongoose.Types.ObjectId(property);
  }
  if (status) {
    matchCondition.staus = status
  }
  // Apply date filter if startDate and endDate are provided
  if (startDate && endDate) {
    matchCondition.checkInDate = { $gte: new Date(startDate) };
    matchCondition.checkOutDate = { $lte: new Date(endDate) };
  } else if (startDate) {
    matchCondition.checkInDate = { $gte: new Date(startDate) };
  } else if (endDate) {
    matchCondition.checkOutDate = { $lte: new Date(endDate) };
  }

  // Aggregation pipeline
  const pipeline = [
    { $match: matchCondition },
    {
      $lookup: {
        from: 'users',
        localField: 'user',
        foreignField: '_id',
        as: 'userDetails',
      },
    },
    { $unwind: { path: '$userDetails', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: 'rooms',
        localField: 'rooms', // Update to match the field in the booking document
        foreignField: '_id',
        as: 'roomDetails',
      },
    },
    {
      $addFields: {
        roomDetails: {
          $map: {
            input: '$roomDetails',
            as: 'room',
            in: {
              name: '$$room.name',
              type: '$$room.type',
              roomNumber: '$$room.roomNumber',
            },
          },
        },
      },
    },
    {
      $sort: category === 'reservations'
        ? { referenceNumber: 1 }
        : { [category === 'arrivals' ? 'checkInDate' : 'checkOutDate']: 1 },
    },
    { $skip: skip },
    { $limit: limit },
    {
      $project: {
        _id: 1,
        referenceNumber: 1,
        checkInDate: 1,
        checkOutDate: 1,
        totalAmount: 1,
        status: 1,
        guestDetails: 1,
        userDetails: { name: 1 },
        roomDetails: 1,
        currency: 1 // Includes mapped room details
      },
    },
  ];

  // Execute the aggregation
  const bookings = await bookingModel.aggregate(pipeline);

  // Total count for pagination
  const totalBookings = await bookingModel.countDocuments(matchCondition);
  const totalPages = totalBookings > 0 ? Math.ceil(totalBookings / limit) : 1;

  // Counts for each category
  const categoryCounts = await bookingModel.aggregate([
    {
      $facet: {
        reservations: [{ $match: {} }, { $count: "count" }],
        arrivals: [{ $match: { checkInDate: { $gte: date } } }, { $count: "count" }],
        departures: [{ $match: { checkOutDate: { $gte: date } } }, { $count: "count" }],
        inhouse: [
          {
            $match: {
              checkInDate: { $lte: date },
              checkOutDate: { $gte: date },
            },
          },
          { $count: "count" }
        ]
      }
    }
  ]);

  // Extracting counts safely
  const counts = {
    reservations: categoryCounts[0].reservations[0]?.count || 0,
    arrivals: categoryCounts[0].arrivals[0]?.count || 0,
    departures: categoryCounts[0].departures[0]?.count || 0,
    inhouse: categoryCounts[0].inhouse[0]?.count || 0,
  };

  const pagination = {
    page,
    limit,
    totalPages,
    totalBookings,
  };

  // Return bookings with pagination
  return { bookings, pagination, counts };
};
export const checkRoomBookingInRange = async (data) => {
  try {
    const { propertyId, roomId, checkIn, checkOut } = data;

    // Parse the dates
    const start = new Date(checkIn);
    const end = new Date(checkOut);

    // Check if the dates are valid
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      throw new Error("Invalid Date provided for booking range.");
    }

    // Find if there are any bookings in the given range
    const existingBooking = await bookingModel.findOne({
      property: new mongoose.Types.ObjectId(propertyId),
      room: new mongoose.Types.ObjectId(roomId),
      isCancel: false,
      $or: [
        {
          checkInDate: { $lt: end },
          checkOutDate: { $gt: start },
        }
      ]
    });

    return existingBooking;

  } catch (error) {
    console.error("Error in checkRoomBookingInRange:", error.message || error);
    return { error: error.message || "An error occurred while checking bookings" };
  }
};

const checkBookingByDateRange = async (roomId, startDate, endDate) => {
  const start = new Date(startDate);
  const end = new Date(endDate);

  // Ensure roomId is treated as an ObjectId
  const roomObjectId = new mongoose.Types.ObjectId(roomId);

  // Query bookings for the specified room that overlap with the date range
  const bookings = await bookingModel.find({
    room: roomObjectId,
    isCancel: false,
    $or: [
      { checkInDate: { $lt: end }, checkOutDate: { $gt: start } },
      { checkInDate: { $gte: start, $lte: end } },
      { checkOutDate: { $gte: start, $lte: end } },
    ],
  }).select('checkInDate checkOutDate');

  // Build a set of booked dates for fast lookup
  const bookedDates = new Set();
  bookings.forEach(booking => {
    let checkIn = new Date(booking.checkInDate);
    let checkOut = new Date(booking.checkOutDate);

    while (checkIn <= checkOut) {
      bookedDates.add(checkIn.toISOString().split('T')[0]);
      checkIn.setDate(checkIn.getDate() + 1);
    }
  });

  // Create an array of dates within the range that are booked
  const bookedAvailability = [];
  let currentDate = start;

  while (currentDate <= end) {
    const dateStr = currentDate.toISOString().split('T')[0];
    if (bookedDates.has(dateStr)) {
      bookedAvailability.push({ date: dateStr, isBooked: true });
    }
    currentDate.setDate(currentDate.getDate() + 1);
  }

  // Organize the booked dates by month
  const monthFilter = {};
  bookedAvailability.forEach(({ date }) => {
    const month = date.slice(0, 7); // YYYY-MM format
    if (!monthFilter[month]) monthFilter[month] = [];
    monthFilter[month].push({ date, isBooked: true });
  });

  return monthFilter;
};

// const checkBookingByDateRange = async (startDate, endDate) => {
//   // Convert dates to ISO strings
//   const start = new Date(startDate).toISOString().split('T')[0];
//   const end = new Date(endDate).toISOString().split('T')[0];

//   const bookings = await bookingModel.aggregate([
//     {
//       $match: {
//         $or: [
//           { checkInDate: { $lte: end }, checkOutDate: { $gte: start } },
//           { checkInDate: { $gte: start, $lte: end } },
//           { checkOutDate: { $gte: start, $lte: end } },
//         ],
//       },
//     },
//     {
//       $project: {
//         _id: 0,
//         dates: {
//           $map: {
//             input: {
//               $range: [
//                 { $toLong: { $dateFromString: { dateString: { $dateToString: { format: "%Y-%m-%d", date: "$checkInDate" } } } } },
//                 { $add: [1, { $toLong: { $dateFromString: { dateString: { $dateToString: { format: "%Y-%m-%d", date: "$checkOutDate" } } } } }] }
//               ],
//             },
//             as: "date",
//             in: { $dateToString: { format: "%Y-%m-%d", date: { $toDate: "$$date" } } },
//           },
//         },
//       },
//     },
//     {
//       $unwind: "$dates",
//     },
//     {
//       $group: {
//         _id: "$dates",
//       },
//     },
//     {
//       $project: {
//         _id: 0,
//         date: "$_id",
//       },
//     },
//   ]);

//   const bookedDatesSet = new Set(bookings.map(b => b.date));

//   const dateRange = [];
//   let currentDate = new Date(startDate);

//   while (currentDate <= endDate) {
//     const dateStr = currentDate.toISOString().split('T')[0];
//     dateRange.push({
//       date: dateStr,
//       isBooked: bookedDatesSet.has(dateStr),
//     });
//     currentDate.setDate(currentDate.getDate() + 1);
//   }

//   return dateRange;
// };

const getUserTravelingDetails = async (userId) => {
  const bookings = await bookingModel.aggregate([
    {
      $match: { user: userId }, // Filter by user ID
    },
    {
      $lookup: {
        from: 'properties', // The name of the collection you want to join with
        localField: 'property', // The field in the booking document
        foreignField: '_id', // The field in the property document
        as: 'propertyDetails', // Alias for the joined field
      },
    },
    {
      $unwind: '$propertyDetails', // Deconstruct the array returned by $lookup
    },
    {
      $group: {
        _id: '$propertyDetails.address.country', // Group by country
        properties: {
          $push: {
            name: '$propertyDetails.name',
            address: { $concat: ['$propertyDetails.address.lineOne', ' ', '$propertyDetails.address.lineTwo'] },
            city: '$propertyDetails.address.city',
            state: '$propertyDetails.address.state',
            country: '$propertyDetails.address.country',
            photos: '$propertyDetails.photos',
            _id: '$propertyDetails._id'
          },
        },
      },
    },
    {
      $project: {
        country: '$_id',
        properties: 1, // Include the properties field
      },
    },
  ]);

  return bookings;
};
export const saveBooking = async (bookingData) => {
  // Get the current highest otaId (as a number)
  const lastBooking = await bookingModel.findOne({})
    .sort({ bookingId: -1 })
    .lean();

  let nextNumber = 10000;

  if (lastBooking && lastBooking.bookingId) {
    const currentNumber = parseInt(lastBooking.bookingId);
    if (!isNaN(currentNumber) && currentNumber >= 10000) {
      nextNumber = currentNumber + 1;
    }
  }

  const bookingId = `${nextNumber}`;

  const booking = new bookingModel({ ...bookingData, bookingId });
  console.log("booking", booking);
  await booking.save();
  return booking;
};

export const getNextSubBookingId = async () => {
  const lastSubBooking = await subBookingModel.findOne().sort({ subBookingId: -1 }).lean();
  return lastSubBooking ? lastSubBooking.subBookingId + 1 : 1;
};



export { addBooking, updateBookingById, getBookingById, deleteBookingById, listBookings, checkBookingByDateRange, getUserTravelingDetails };