import express from 'express';
import { createOrder, paymentVerification,
handlePaymentWebhook,AddCashPayments,GetPaymentsByProperty,EditCashPayment,DeleteCashPayment } from '../controller/payment.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();
router.post('/createOrder', checkAuth(''),createOrder);
router.post('/paymentVerification', paymentVerification);
// router.post('/paypal/createPayPalOrder', createPayPalOrder);
// router.post('/paypal/capturePayPalPayment',checkAuth('add_page'), capturePayPalPayment);
router.post('/razorpay/webhook', handlePaymentWebhook);
router.post('/cash-payments', AddCashPayments);
router.get('/:propertyId', GetPaymentsByProperty);
router.put('/:paymentId/editCashPayment', EditCashPayment); 
router.delete('/:paymentId/deleteCashPayment', DeleteCashPayment);
// router.get('/',getAllPayment);

export default router;

/**
 * @swagger
 * tags:
 *   name: Payment
 *   description: Payment endpoints
 */

/**
 * @swagger
 * /api/payment/createOrder:
 *   post:
 *     summary: Create a new order for payment
 *     tags: [Payment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               amount:
 *                 type: number
 *                 description: Amount to be paid in smallest currency unit (e.g., paise)
 *               currency:
 *                 type: string
 *                 description: Currency of the payment (default is INR)
 *               receipt:
 *                 type: string
 *                 description: Receipt identifier for the order
 *             required:
 *               - amount
 *     responses:
 *       '201':
 *         description: Order created successfully
 *         content:
 *           application/json:
 *             example:
 *               id: order_9A33XWu170gUtm
 *               entity: order
 *               amount: 50000
 *               currency: INR
 *               receipt: receipt_1
 *               status: created
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               error: 'Internal server error'
 */

/**
 * @swagger
 * /api/payment/paymentVerification:
 *   post:
 *     summary: Verify the payment after transaction
 *     tags: [Payment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               order_id:
 *                 type: string
 *                 description: ID of the created order
 *               payment_id:
 *                 type: string
 *                 description: ID of the payment
 *               signature:
 *                 type: string
 *                 description: Signature to verify the payment
 *             required:
 *               - order_id
 *               - payment_id
 *               - signature
 *     responses:
 *       '200':
 *         description: Payment verification successful
 *         content:
 *           application/json:
 *             example:
 *               status: 'Payment successful'
 *       '400':
 *         description: Payment verification failed
 *         content:
 *           application/json:
 *             example:
 *               status: 'Payment failed'
 */
/**
 * @swagger
 * /api/payment/paypal/createPayPalOrder:
 *   post:
 *     summary: Create a new PayPal order for payment
 *     tags: [Payment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               amount:
 *                 type: number
 *                 description: Amount to be paid
 *               currency:
 *                 type: string
 *                 description: Currency of the payment (default is USD)
 *             required:
 *               - amount
 *     responses:
 *       '201':
 *         description: PayPal Order created successfully
 *         content:
 *           application/json:
 *             example:
 *               id: "5O190127TN364715T"
 *               status: "CREATED"
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               error: 'Internal server error'
 */

/**
 * @swagger
 * /api/payment/paypal/capturePayPalPayment:
 *   post:
 *     summary: Capture PayPal payment after approval
 *     tags: [Payment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               orderId:
 *                 type: string
 *                 description: ID of the PayPal order
 *             required:
 *               - orderId
 *     responses:
 *       '200':
 *         description: Payment capture successful
 *         content:
 *           application/json:
 *             example:
 *               status: 'Payment successful'
 *               capture: {...}
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               error: 'Internal server error'
 */
/**
 * @swagger
 * tags:
 *   name: Payment
 *   description: Payment endpoints
 */

/**
 * @swagger
 * /api/payment/cash-payments:
 *   post:
 *     summary: Record a cash payment
 *     tags: [Payment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userName:
 *                 type: string
 *                 description: Name of the user making the payment
 *               date:
 *                 type: string
 *                 format: date
 *                 description: Date of the payment (YYYY-MM-DD)
 *               room:
 *                 type: string
 *                 description: Room identifier related to the payment
 *               amount:
 *                 type: number
 *                 description: Amount paid in smallest currency unit (e.g., paise)
 *               property:
 *                 type: string
 *                 description: Identifier for the property
 *             required:
 *               - userName
 *               - date
 *               - amount
 *               - property
 *     responses:
 *       '201':
 *         description: Cash payment recorded successfully
 *         content:
 *           application/json:
 *             example:
 *               id: 643d2f5b9f1e2c123abc4567
 *               gusetUser: John Doe
 *               paymentFor: Property
 *               property: property_123
 *               amount: 5000
 *               currency: INR
 *               status: Paid
 *               paymentType: Cash
 *               paymentDate: 2024-12-01
 *               room: room_45
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               error: 'Internal server error'
 */

/**
 * @swagger
 * /api/payment/{propertyId}:
 *   get:
 *     summary: Get payment data for a specific property
 *     tags: [Payment]
 *     parameters:
 *       - in: path
 *         name: propertyId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the property
 *     responses:
 *       '200':
 *         description: List of payments for the property
 *         content:
 *           application/json:
 *             example:
 *               success: true
 *               data:
 *                 - id: 643d2f5b9f1e2c123abc4567
 *                   gusetUser: John Doe
 *                   paymentFor: Property
 *                   property: property_123
 *                   amount: 5000
 *                   currency: INR
 *                   status: Paid
 *                   paymentType: Cash
 *                   paymentDate: 2024-12-01
 *                   room: room_45
 *       '404':
 *         description: No payments found for the given property ID
 *         content:
 *           application/json:
 *             example:
 *               message: 'No payments found for the given property ID'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               message: 'Internal server error'
 *               error: 'Error details'
 */

/**
 * @swagger
 * /api/payment/{paymentId}/editCashPayment:
 *   put:
 *     summary: Edit a cash payment
 *     tags: [Payment]
 *     parameters:
 *       - in: path
 *         name: paymentId
 *         required: true
 *         description: ID of the payment to edit
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userName:
 *                 type: string
 *                 description: Updated name of the user
 *               date:
 *                 type: string
 *                 format: date
 *                 description: Updated payment date (YYYY-MM-DD)
 *               room:
 *                 type: string
 *                 description: Updated room identifier
 *               amount:
 *                 type: number
 *                 description: Updated amount paid
 *               property:
 *                 type: string
 *                 description: Updated property identifier
 *               currency:
 *                 type: string
 *                 description: Updated currency (e.g., INR)
 *     responses:
 *       '200':
 *         description: Cash payment updated successfully
 *         content:
 *           application/json:
 *             example:
 *               id: 643d2f5b9f1e2c123abc4567
 *               gusetUser: John Doe
 *               paymentFor: Property
 *               property: property_123
 *               amount: 7000
 *               currency: INR
 *               status: Paid
 *               paymentType: Cash
 *               paymentDate: 2024-12-02
 *               room: room_45
 *       '400':
 *         description: Only cash payments can be edited
 *         content:
 *           application/json:
 *             example:
 *               error: 'Only cash payments can be edited'
 *       '404':
 *         description: Payment not found
 *         content:
 *           application/json:
 *             example:
 *               error: 'Payment not found'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               error: 'Internal server error'
 */

/**
 * @swagger
 * /api/payment/{paymentId}/deleteCashPayment:
 *   delete:
 *     summary: Soft delete a cash payment
 *     tags: [Payment]
 *     parameters:
 *       - in: path
 *         name: paymentId
 *         required: true
 *         description: ID of the payment to delete
 *         schema:
 *           type: string
 *     responses:
 *       '200':
 *         description: Cash payment deleted successfully
 *         content:
 *           application/json:
 *             example:
 *               message: 'Payment deleted successfully'
 *       '400':
 *         description: Only cash payments can be deleted
 *         content:
 *           application/json:
 *             example:
 *               error: 'Only cash payments can be deleted'
 *       '404':
 *         description: Payment not found
 *         content:
 *           application/json:
 *             example:
 *               error: 'Payment not found'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             example:
 *               error: 'Internal server error'
 */

/**
 * @swagger
 * /api/payment:
 *   get:
 *     summary: Retrieve payments with pagination and filters
 *     description: Fetch a paginated list of payments with optional filters for payment type, payment purpose, date, and property. This endpoint is accessible only to admin users.
 *     tags: [Payment]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         required: false
 *         description: Page number for pagination (default is 1).
 *         schema:
 *           type: integer
 *           example: 1
 *       - in: query
 *         name: limit
 *         required: false
 *         description: Number of payments per page (default is 10).
 *         schema:
 *           type: integer
 *           example: 10
 *       - in: query
 *         name: paymentType
 *         required: false
 *         description: Filter by payment type (e.g., razorPay, cash).
 *         schema:
 *           type: string
 *           example: razorPay
 *       - in: query
 *         name: paymentFor
 *         required: false
 *         description: Filter by the purpose of payment (e.g., event, property).
 *         schema:
 *           type: string
 *           example: property
 *       - in: query
 *         name: paymentDate
 *         required: false
 *         description: Filter payments by a specific date (YYYY-MM-DD). Filters payments for the specified day.
 *         schema:
 *           type: string
 *           format: date
 *           example: 2024-08-17
 *       - in: query
 *         name: propertyId
 *         required: false
 *         description: Filter payments by property ID.
 *         schema:
 *           type: string
 *           example: 66c08f8e68c9e4dd0aa2868c
 *     responses:
 *       '200':
 *         description: Payments retrieved successfully.
 *         content:
 *           application/json:
 *             example:
 *               status: true
 *               data:
 *                 payments:
 *                   - _id: 66c0a0e49f3755162ad449a4
 *                     guestUser: John Doe
 *                     paymentFor: Property
 *                     property: 66c08f8e68c9e4dd0aa2868c
 *                     amount: 22.74
 *                     currency: EUR
 *                     status: Payment successful
 *                     paymentType: razorPay
 *                     paymentDate: "2024-08-17T13:08:52.306+00:00"
 *                     room: room_45
 *                 pagination:
 *                   page: 1
 *                   limit: 10
 *                   totalPages: 1
 *                   totalPayments: 1
 *               message: Payments retrieved successfully.
 *       '400':
 *         description: Invalid input data, including invalid property ID format.
 *         content:
 *           application/json:
 *             example:
 *               status: false
 *               data: null
 *               message: Invalid property ID format.
 *       '403':
 *         description: Access denied. Admin role required.
 *         content:
 *           application/json:
 *             example:
 *               status: false
 *               data: null
 *               message: Access denied. Admin role required.
 *       '500':
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             example:
 *               status: false
 *               data: null
 *               message: Failed to retrieve payments.
 */







