/* eslint-disable no-constant-binary-expression */
"use client";

import { CloudUpload, ChevronDown } from "lucide-react";
import React, { useState, useEffect, useRef } from "react";
import toast, { Toaster } from "react-hot-toast";
import { AddRoomApi } from "@/services/ownerflowServices";
import { getItemLocalStorage } from "@/utils/browserSetting";
import { BASE_URL } from "@/utils/api";
import Loader from "@/components/loader/loader";
import countries from "world-countries";
import CustomSelect from "@/components/common/CustomDropdown2";
import Image from "next/image";
// import CustomSelect1 from "@/components/common/CustomDropdown";

const AddRoom = ({ setIsUpdate, closeModal }) => {
  const [addroom, setAddRoom] = useState({
    name: "",
    type: "",
    beds: 1,
    ensuite: "",
    rate: "",
    weekdayRate: "",
    weekendRate: "",
    currency: "",
    images: [],
    description: "",
    bedAndBreakfast: false,
    nonRefundable: false,
    freeCancellation: false,
  });

  const [currencies, setCurrencies] = useState([]);
  // const [ensuiteOption, setensuiteOption] = useState([Yes, No]);
  // eslint-disable-next-line no-unused-vars
  const [bedOptions, setBedOptions] = useState([1, 2, 3, 4, 5, 6]);
  // eslint-disable-next-line no-unused-vars
  const [selectedFile, setSelectedFile] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const isCurrenciesFetched = useRef(null);

  useEffect(() => {
    if (!isCurrenciesFetched.current) {
      const fetchCountryCurrencyCodes = async () => {
        try {
          const countryData = countries.map((country) => {
            // Safely extract currency code and symbol, falling back to defaults
            const currencyCode =
              country?.currencies && Object.keys(country?.currencies)[0]
                ? Object.keys(country?.currencies)[0]
                : "N/A";
            const currencySymbol =
              country?.currencies && country?.currencies[currencyCode]?.symbol
                ? country?.currencies[currencyCode]?.symbol
                : "€";

            // Get the flag code (ISO 3166-1 alpha-2) for the flag
            const flagCode = country.cca2 ? country.cca2.toLowerCase() : "xx"; // Default to 'xx' if cca2 is missing

            // Construct the flag image URL or use a placeholder
            const flag =
              `https://flagcdn.com/w320/${flagCode}.png` ||
              "https://via.placeholder.com/30x25";

            return {
              country: country?.name?.common || "Unknown", // Country name, fallback to "Unknown" if not found
              code: currencyCode, // Currency code
              symbol: currencySymbol, // Currency symbol
              flag: flag, // Flag image URL
            };
          });

          setCurrencies(countryData); // Store the country data
        } catch (error) {
          console.error("Error fetching country data:", error);
        }
      };
      fetchCountryCurrencyCodes();
      isCurrenciesFetched.current = true;
    }
  }, []);

  // const decrement = () => {
  //   setQuantity((prev) => Math.max(1, prev - 1));
  // };

  const increment = () => {
    setAddRoom((prev) => ({
      ...prev,
      beds: Number(prev.beds) + 1,
    }));
  };

  const decrement = () => {
    setAddRoom((prev) => ({
      ...prev,
      beds: Math.max(1, Number(prev.beds) - 1), // prevent going below 1
    }));
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setAddRoom({
      ...addroom,
      [name]: value,
    });
  };

  // const handlePhotoChange = async (e) => {
  //   const { files } = e.target;
  //   const file = files[0];

  //   if (file) {
  //     setIsLoading(true);
  //     try {
  //       const formData = new FormData();
  //       formData.append("file", file);

  //       // Get presigned URL for file upload
  //       const presignedUrlResponse = await fetch(
  //         `${BASE_URL}/fileUpload/generate-presigned-url`,
  //         {
  //           method: "POST",
  //           body: formData,
  //         }
  //       );

  //       if (!presignedUrlResponse.ok) {
  //         throw new Error("Failed to get presigned URL");
  //       }

  //       const presignedUrlData = await presignedUrlResponse.json();
  //       const { objectURL } = presignedUrlData.data;

  //       setSelectedFile(file);
  //       setPresignedUrl(objectURL);
  //       setAddRoom((prevState) => ({
  //         ...prevState,
  //         images: [...prevState.images, objectURL],
  //       }));

  //       toast.success("File uploaded successfully.");
  //     } catch (error) {
  //       console.error("Error uploading file:", error);
  //       toast.error("Error uploading file.");
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   } else {
  //     toast.error("File is required");
  //   }
  // };

  const handlePhotoChange = async (e) => {
    const { files } = e.target;

    if (files.length === 0) {
      toast.error("At least one file is required");
      return;
    }
    const allowedTypes = ["image/jpeg", "image/png", "image/jpg"];

    // Check if all selected files are of allowed types
    const invalidFiles = Array.from(files).filter(
      (file) => !allowedTypes.includes(file.type)
    );

    if (invalidFiles.length > 0) {
      toast.error("Only JPG, JPEG, and PNG files are allowed.");
      return;
    }

    setIsLoading(true);

    try {
      // Loop through all selected files
      const uploadedImages = await Promise.all(
        Array.from(files).map(async (file) => {
          const formData = new FormData();
          formData.append("files", file);

          // Get presigned URL for each file
          const presignedUrlResponse = await fetch(
            `${BASE_URL}/fileUpload/generate-presigned-url`,
            {
              method: "POST",
              body: formData,
            }
          );

          if (!presignedUrlResponse.ok) {
            throw new Error("Failed to get presigned URL");
          }

          const presignedUrlData = await presignedUrlResponse.json();
          // const { objectURL } = presignedUrlData.data;
           const objectURL = Array.isArray(presignedUrlData.data) && presignedUrlData.data[0]?.path;

          return { title: "Room view", url: objectURL };
        })
      );

      // Update state with new uploaded image URLs
      setAddRoom((prevState) => ({
        ...prevState,
        images: [...(prevState?.images || []), ...uploadedImages],
      }));

      toast.success("Files uploaded successfully.");
    } catch (error) {
      console.error("Error uploading files:", error);
      toast.error("Error uploading files.");
    } finally {
      setIsLoading(false);
    }
  };

  const removeImage = (indexToRemove) => {
    setAddRoom((prevState) => ({
      ...prevState,
      images: prevState.images.filter((_, index) => index !== indexToRemove),
    }));
  };

  const handleSubmit = async () => {
    const storedId = getItemLocalStorage("hopid");

    const {
      name,
      type,
      beds,
      ensuite,
      rate,
      currency,
      images,
      weekdayRate,
      weekendRate,
    } = addroom;

    console.log("Data", addroom);
    if (
      !name ||
      !type ||
      !beds ||
      !rate ||
      !currency ||
      !weekdayRate ||
      !weekendRate
    ) {
      toast.error("Please fill all required fields");
      return;
    }

    try {
      const payload = {
        name,
        type,
        property: storedId,
        beds: parseInt(beds),
        rate: {
          weekdayRate: {
            value: parseFloat(addroom?.weekdayRate),
          },
          weekendRate: {
            value: parseFloat(addroom?.weekendRate),
          },
        },
        currency: addroom?.currency?.value,
        // images: images.map((url) => ({
        //   title: "Room view",
        //   url,
        // })),
        images,
        description: addroom.description,
        bedAndBreakfast: addroom.bedAndBreakfast,
        nonRefundable: addroom.nonRefundable,
        freeCancellation: addroom.freeCancellation,
        ensuite: ensuite,
        weekdayRate,
      };

      const response = await AddRoomApi(payload);

      if (response.status === 201) {
        toast.success("Room added successfully");
        setAddRoom({
          name: "",
          type: "",
          beds: "",
          ensuite: "",
          rate: "",
          weekdayRate: "",
          currency: "",
          images: [],
          description: "",
          bedAndBreakfast: false,
          nonRefundable: false,
          freeCancellation: false,
        });
        setIsUpdate((prevState) => !prevState);
        closeModal();
      }
    } catch (error) {
      console.error("Error adding room:", error);
      toast.error("Failed to add room");
    }
  };

  const [isOpen, setIsOpen] = useState({ beds: false, ensuite: false });
  const [selectedValues, setSelectedValues] = useState({
    beds: addroom.beds || "",
    ensuite: addroom.ensuite || "Ensuite",
    currency: addroom.currency || "",
  });

  const handleSelect = (name, value) => {
    setSelectedValues((prev) => ({ ...prev, [name]: value }));
    handleChange({ target: { name, value } });
    setIsOpen({ beds: false, ensuite: false, currency: false });
  };

  return (
    <>
      <Loader open={isLoading} />
      <div className='max-w-[780px] mx-auto pb-8'>
        <Toaster />
        <div className='grid grid-cols-2 sm:gap-5 gap-2'>
          <div>
            <label
              className='block text-black sm:text-sm text-xs font-medium mb-1.5 '
              htmlFor='username'
            >
              Room Name <span className='text-red-500'>*</span>
            </label>
            <input
              type='text'
              name='name'
              value={addroom.name}
              onChange={handleChange}
              className='w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500'
              placeholder='Enter Room Name'
            />
          </div>
          <div>
            <label
              className='block text-black sm:text-sm text-xs font-medium mb-1.5 '
              htmlFor='username'
            >
              Room Type <span className='text-red-500'>*</span>
            </label>

            {/* <select
              name='type'
              value={addroom.type}
              onChange={handleChange}
              className='w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500'
            >
              <option disabled value=''>
                Room Type
              </option>
              <option value='private'>Private</option>
              <option value='dbl private'>Dbl Private</option>
              <option value='mixed dorm'>Mixed Dorm</option>
              <option value='female dorm'>Female Dorm</option>
            </select> */}
            <CustomSelect
              name='type'
              options={[
                { value: "", label: "Room Type" },
                { value: "private", label: "Private" },
                { value: "dbl", label: "Dbl Private" },
                { value: "mixed dorm", label: "Mixed Dorm" },
                { value: "female dorm", label: "Female Dorm" },
              ]}
              value={addroom.type}
              onChange={(selectedOption) =>
                handleChange({
                  target: { name: "type", value: selectedOption?.value },
                })
              }
              placeholder='Select Room Type'
            />
          </div>
          <div>
            <label
              className='block text-black sm:text-sm text-xs font-medium mb-1.5 '
              htmlFor='username'
            >
              Select Room Category <span className='text-red-500'>*</span>
            </label>

            <CustomSelect
              name='type'
              options={[
                { value: "", label: "Room Category" },
                { value: "private", label: "Private" },
                { value: "dbl", label: "Dbl Private" },
                { value: "mixed dorm", label: "Mixed Dorm" },
                { value: "female dorm", label: "Female Dorm" },
              ]}
              value={addroom.type}
              onChange={(selectedOption) =>
                handleChange({
                  target: { name: "type", value: selectedOption?.value },
                })
              }
              placeholder='Select Room Category'
            />
          </div>
          <div>
            <label
              className='block text-black sm:text-sm text-xs font-medium mb-1.5 '
              htmlFor='username'
            >
              Room Number<span className='text-red-500'>*</span>
            </label>
            <input
              type='text'
              name='name'
              className='w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500'
              placeholder='Enter Room Name'
            />
          </div>
          <div className='relative'>
            <label className='block text-black sm:text-sm text-xs font-medium mb-1.5'>
              Total Beds <span className='text-red-500'>*</span>
            </label>

            <div className='flex items-center border border-gray-300 rounded-lg overflow-hidden w-full max-w-md'>
              <input
                type='number'
                name='beds'
                min='1'
                step='1'
                value={addroom.beds}
                onChange={(e) => {
                  const value = e.target.value;
                  if (/^\d+$/.test(value) && parseInt(value) > 0) {
                    handleChange(e);
                  } else if (value === "") {
                    handleChange(e); // Allow clearing the input
                  }
                }}
                className='w-full px-4 py-2 focus:outline-none'
              />
              <button
                className='h-6 w-2 rounded-full border-2 border-primary-blue  px-4 py-2 transition-colors text-primary-blue font-bold flex items-center justify-center'
                onClick={decrement}
              >
                -
              </button>
              <button
                className='h-6 w-4 rounded-full border-2 border-primary-blue  px-4 py-2 transition-colors text-primary-blue font-bold flex items-center justify-center mx-2'
                onClick={increment}
              >
                +
              </button>
            </div>
          </div>

          {/* Ensuite Dropdown */}
          <div className='relative'>
            <label className='block text-black sm:text-sm text-xs font-medium mb-1.5'>
              Ensuite
            </label>

            {/* <select
              name='ensuite'
              className='w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500'
              value={addroom.ensuite || ""}
              onChange={handleChange}
            >
              <option disabled value=''>
                Ensuit
              </option>
              <option value='true'>Yes</option>
              <option value='false'>No</option>
            </select> */}
            <CustomSelect
              name='ensuite'
              options={[
                { value: "", label: "Ensuit" },
                { value: "true", label: "Yes" },
                { value: "false", label: "No" },
              ]}
              value={addroom.ensuite}
              onChange={(selectedOption) =>
                handleChange({
                  target: { name: "ensuite", value: selectedOption?.value },
                })
              }
              placeholder='Select Ensuite'
            />
          </div>
          <div>
            <label
              className='block text-black sm:text-sm text-xs font-medium mb-1.5 '
              htmlFor='username'
            >
              Rate <span className='text-red-500'>*</span>
            </label>
            <input
              type='number'
              name='rate'
              value={addroom.rate}
              onChange={handleChange}
              className='w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500'
              placeholder='Price per room, Tax included'
            />
          </div>

          <div className='relative hidden'>
            <label className='block text-black sm:text-sm text-xs font-medium mb-1.5'>
              Currency <span className='text-red-500'>*</span>
            </label>

            {/* Selected Currency Display */}
            <div
              className={`w-full sm:px-4 px-2 sm:py-2.5 py-2 sm:text-sm text-xs border border-black/50 rounded-lg cursor-pointer relative ${
                !selectedValues.currency ? "text-gray-500" : "text-black"
              }`}
              onClick={() =>
                setIsOpen({
                  beds: false,
                  ensuite: false,
                  currency: !isOpen.currency,
                })
              }
            >
              {/* Show selected currency flag, code, and symbol */}
              {selectedValues.currency ? (
                <span className='flex items-center'>
                  {selectedValues.currency.flag && (
                    <Image 
                      src={selectedValues.currency.flag}
                      alt=''
                      className='inline-block sm:w-5 sm:h-5 w-4 h-3 mr-2'
                      width={30}
                      height={25}
                    />
                  )}
                  {selectedValues.currency.code} (
                  {selectedValues.currency.symbol})
                </span>
              ) : (
                "Currency"
              )}

              <div className='absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none'>
                <ChevronDown />
              </div>
            </div>

            {/* Currency Dropdown */}
            {isOpen.currency && (
              <ul className='absolute w-full bg-white border border-black/50 rounded-lg mt-1 shadow-lg z-20 h-[300px] overflow-y-auto modalscroll'>
                <li
                  className={`px-4 py-1 cursor-pointer hover:bg-[#40E0D0] sm:text-sm text-xs first:rounded-t-lg last:rounded-b-lg ${
                    !selectedValues.currency ? "bg-[#40E0D0] text-white" : ""
                  }`}
                  onClick={() => handleSelect("currency", "")}
                >
                  Currency
                </li>

                {currencies.map((currency) => (
                  <li
                    key={currency.code}
                    className={`px-4 py-1 cursor-pointer hover:bg-[#40E0D0] text-sm first:rounded-t-lg last:rounded-b-lg ${
                      selectedValues.currency?.code === currency.code
                        ? "bg-[#40E0D0] text-white"
                        : ""
                    }`}
                    onClick={() => handleSelect("currency", currency)} // Pass entire currency object
                  >
                    <span className='flex items-center'>
                      <Image 
                        src={currency.flag}
                        alt=''
                        className='inline-block sm:w-5 sm:h-5 w-4 h-3 mr-2'
                        width={30}
                        height={25}
                      />
                      {currency.code} ({currency.symbol})
                    </span>
                  </li>
                ))}
              </ul>
            )}
          </div>

          <div className='relative'>
            <label className='block text-black sm:text-sm text-xs font-medium mb-1.5'>
              Currency <span className='text-red-500'>*</span>
            </label>

            <CustomSelect
              options={currencies.map((currency) => ({
                value: currency.code,
                label: (
                  <span className='flex items-center'>
                    <Image 
                      src={currency.flag || "/placeholder.svg"}
                      alt={`${currency.code} Flag`}
                      className='inline-block w-4 h-3 mr-2'
                      width={20}
                      height={15}
                    />
                    {currency.code} ({currency.symbol})
                  </span>
                ),
              }))}
              value={addroom.currency}
              onChange={(selectedOption) =>
                handleChange({
                  target: { name: "currency", value: selectedOption },
                })
              }
              placeholder='Select Currency'
            />
          </div>

          <div className='relative col-span-2'>
            <label
              className='block text-black sm:text-sm text-xs font-medium mb-1.5'
              htmlFor='username'
            >
              Room Photo
            </label>

            <input
              type='file'
              name='images'
              onChange={handlePhotoChange}
              multiple
              accept='.jpg, .jpeg, .png'
              className='z-10 cursor-pointer block w-full p-2 px-4 text-sm bg-transparent border rounded-lg opacity-0 border-gray-220 focus:outline-none text-slate-320 placeholder:text-gray-320 absolute top-0 left-0 right-0 bottom-0'
              placeholder='Upload Photos'
            />
            <div className='w-full px-4 py-2 border border-[#40E0D0] rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500 flex items-center justify-between border-dashed bg-[#40E0D01A] cursor-pointer'>
              {!selectedFile
                ? "Upload Photos"
                : selectedFile?.name || "Upload file"}{" "}
              <CloudUpload className='text-[#40E0D0]' size={22} />
            </div>
          </div>

          {addroom?.images?.length > 0 && (
            <div className='col-span-2'>
              <div className='grid grid-cols-3 sm:gap-4 gap-3'>
                {addroom?.images?.map((image, index) => (
                  <div
                    key={index}
                    className='flex items-center justify-between sm:px-4 px-2 sm:py-2.5 py-2 border border-[#40E0D0] border-dashed bg-[#40E0D01A] rounded-lg'
                  >
                    <Image 
                      // src={image?.url}
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/${image?.url}`}
                      alt={`Existing Image ${index + 1}`}
                      className='w-[54px] h-[36px] object-cover rounded-sm'
                      width={54}
                      height={36}
                    />
                    <span
                      className='text-black hover:text-red-500 font-black cursor-pointer text-xl'
                      onClick={() => removeImage(index)}
                    >
                      &#10005;
                    </span>
                    {/* <XCircle
                      className='absolute top-1 right-1 text-red-500 cursor-pointer'
                      size={24}
                      onClick={() => removeImage(index)}
                    /> */}
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className='inner-currency-wrap'>
            <label
              className='block text-black sm:text-sm text-xs font-medium mb-1.5'
              htmlFor='weekday-rate'
            >
              Weekday Rate <span className='text-red-500'>*</span>
            </label>
            <div className='flex items-center w-full sm:px-1 px-2 py-1 border border-black/50 rounded-lg focus-within:ring-1 focus-within:ring-teal-500'>
              {/* <select
                name='currency'
                value={addroom.currency.value}
                onChange={handleChange}
                className='sm:w-20 w-16 focus:outline-none text-black sm:text-sm text-xs placeholder:text-gray-500'
              >
                <option disabled value=''>
                  EUR
                </option>
                {currencies.map((currency) => (
                  <option key={currency.code} value={currency.code}>
                    {currency?.code}
                  </option>
                ))}
              </select> */}

              <CustomSelect
                className='w-[150%]'
                options={currencies.map((currency) => ({
                  value: currency.code,
                  label: (
                    <span className='flex items-center'>
                      <Image 
                        src={currency.flag || "/placeholder.svg"}
                        alt={`${currency.code} Flag`}
                        className='inline-block w-4 h-3 mr-2 '
                        width={20}
                        height={15}
                      />
                      {currency.code} ({currency.symbol})
                    </span>
                  ),
                }))}
                value={addroom.currency}
                onChange={(selectedOption) =>
                  handleChange({
                    target: { name: "currency", value: selectedOption },
                  })
                }
                placeholder='Currency'
              />
              <span className='mr-2 text-gray-300'>|</span>
              <input
                type='number'
                name='weekdayRate'
                placeholder='Weekday Rate'
                value={addroom.rate?.weekdayRate}
                onChange={handleChange}
                className='w-full bg-transparent focus:outline-none text-black sm:text-sm text-xs placeholder:text-gray-500 pl-9'
              />
            </div>
          </div>

          <div className='inner-currency-wrap'>
            <label
              className='block text-black sm:text-sm text-xs font-medium mb-1.5'
              htmlFor='weekday-rate'
            >
              Weekend Rate <span className='text-red-500'>*</span>
            </label>
            <div className='flex items-center w-full sm:px-1 px-2 py-1 border border-black/50 rounded-lg focus-within:ring-1 focus-within:ring-teal-500'>
              {/* <select
                name='currency'
                value={addroom.currency.code}
                onChange={handleChange}
                className='sm:w-20 w-16 focus:outline-none text-black sm:text-sm text-xs placeholder:text-gray-500'
              >
                <option disabled value=''>
                  EUR
                </option>
                {currencies.map((currency) => (
                  <option key={currency.code} value={currency.code}>
                    {currency?.code}
                  </option>
                ))}
              </select> */}

              <CustomSelect
                className='w-[150%]'
                options={currencies.map((currency) => ({
                  value: currency.code,
                  label: (
                    <span className='flex items-center'>
                      <Image 
                        src={currency.flag || "/placeholder.svg"}
                        alt={`${currency.code} Flag`}
                        className='inline-block w-4 h-3 mr-2'
                        width={20}
                        height={15}
                      />
                      {currency.code} ({currency.symbol})
                    </span>
                  ),
                }))}
                value={addroom.currency}
                onChange={(selectedOption) =>
                  handleChange({
                    target: { name: "currency", value: selectedOption },
                  })
                }
                placeholder='Currency'
              />
              <span className='mr-2 text-gray-300'>|</span>
              <input
                type='number'
                name='weekendRate'
                placeholder='Weekend Rate'
                value={addroom.weekendRate}
                onChange={handleChange}
                className='w-full bg-transparent focus:outline-none text-black sm:text-sm text-xs placeholder:text-gray-500 pl-9'
              />
            </div>
          </div>

          <div className='col-span-2'>
            <label
              className='block text-black sm:text-sm text-xs font-medium mb-1.5 '
              htmlFor='username'
            >
              Room Description
            </label>

            <textarea
              name='description'
              value={addroom.description}
              onChange={handleChange}
              rows={4}
              className='w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500'
              placeholder='Room Description'
            />
          </div>

          {/* <div className='col-span-2 flex items-center'>
            <input
              type='checkbox'
              name='ensuite'
              checked={addroom.ensuite}
              onChange={handleCheckboxChange}
              className='mr-2'
            />
            <label htmlFor='ensuite' className='text-black text-sm'>
              Ensuite Bathroom
            </label>
          </div> */}
        </div>

        <div className='flex items-center justify-start mt-4 sm:gap-x-3 gap-2 flex-wrap'>
          <div
            className={`border border-[#B9BDC7] rounded-full p-2 font-manrope font-semibold sm:text-sm text-xs text-[#202224] cursor-pointer ${
              addroom.bedAndBreakfast
                ? "bg-[#40e0d033] border-[#40e0d033]"
                : "bg-transparent"
            }`}
            onClick={() =>
              setAddRoom({
                ...addroom,
                bedAndBreakfast: !addroom.bedAndBreakfast,
                nonRefundable: false,
                freeCancellation: false,
              })
            }
          >
            Bed and Breakfast
          </div>
          <div
            className={`border border-[#B9BDC7] rounded-full p-2 font-manrope font-semibold sm:text-sm text-xs text-[#202224] cursor-pointer ${
              addroom.nonRefundable
                ? "bg-[#40e0d033] border-[#40e0d033]"
                : "bg-transparent"
            }`}
            onClick={() =>
              setAddRoom({
                ...addroom,
                nonRefundable: !addroom.nonRefundable,
                bedAndBreakfast: false,
                freeCancellation: false,
              })
            }
          >
            Non Refundable
          </div>
          <div
            className={`border border-[#B9BDC7] rounded-full p-2 font-manrope font-semibold sm:text-sm text-xs text-[#202224] cursor-pointer ${
              addroom.freeCancellation
                ? "bg-[#40e0d033] border-[#40e0d033]"
                : "bg-transparent"
            }`}
            onClick={() =>
              setAddRoom({
                ...addroom,
                freeCancellation: !addroom.freeCancellation,
                bedAndBreakfast: false,
                nonRefundable: false,
              })
            }
          >
            Free Cancellation
          </div>
        </div>

        <div className='flex items-center justify-center w-full sm:my-14 my-7 py-4 bg-white/60 sticky bottom-0 backdrop-blur-sm'>
          <button
            onClick={handleSubmit}
            className='bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg sm:w-[290px] w-auto text-sm'
          >
            Create New Room
          </button>
          {/* <button
            onClick={() => setActiveTab(1)}
            className='bg-red-500 hover:bg-red-700 text-white font-semibold py-2 px-4 border border-none rounded  '
          >
            Cancel
          </button> */}
        </div>
      </div>
    </>
  );
};

export default AddRoom;
