// "use client";
// import Link from "next/link";
// import React, { useEffect, useRef, useState } from "react";
// import { FaChevronDown, FaChevronUp } from "react-icons/fa";

// const CreateNotification = () => {
//   const [notificationType, setNotificationType] = useState("");
//   const [selectedNotification, setSelectedNotification] = useState(
//     "Select Notification"
//   );

//   const [recipients, setRecipients] = useState("");
//   const [selectedRecipients, setSelectedRecipients] =
//     useState("Select Recipients");
  

//   const [isIcon, setIsIcon] = useState(false);
//   const [selectedIcon, setSelectedIcon] = useState("Select Icon");
//   const [isIconColor, setIsIconColor] = useState(false);
//   const [selectedIconColor, setSelectedIconColor] = useState("Select Color");

//   const notificationRef = useRef(null);
//   const recipientsRef = useRef(null);
//   const iconRef = useRef(null);
//   const colorRef = useRef(null);

//   // Function to close all dropdowns
//   const closeAllDropdowns = () => {
//     setNotificationType(false);
//     setRecipients(false);
//     setIsIcon(false);
//     setIsIconColor(false);
//   };
//   // Effect to handle outside click
//   useEffect(() => {
//     const handleClickOutside = (event) => {
//       if (
//         notificationRef.current &&
//         !notificationRef.current.contains(event.target) &&
//         recipientsRef.current &&
//         !recipientsRef.current.contains(event.target) &&
//         iconRef.current &&
//         !iconRef.current.contains(event.target) &&
//         colorRef.current &&
//         !colorRef.current.contains(event.target)
//       ) {
//         closeAllDropdowns();
//       }
//     };

//     document.addEventListener("click", handleClickOutside);
//     return () => {
//       document.removeEventListener("click", handleClickOutside);
//     };
//   }, []);

//   const notificationOptions = ["Type 1", "Type 2", "Type 3"];
//   const recipientOptions = ["User", "Admin", "Hostel Owner"];
//   const iconOptions = ["Icon 1", "Icon 2", "Icon 3"]; // Example options for icons
//   const iconColorOptions = ["Red", "Blue", "Green"]; // Example options for colors


//       // Toggle dropdown functions
//       const toggleNotificationDropdown = () => {
//         closeAllDropdowns();
//         setNotificationType(!notificationType);
//       };
  
      
//       const toggleRecipentsDropdown = () => {
//         closeAllDropdowns();
//         setRecipients(!recipients);
//       };
    
//       const toggleIconDropdown = () => {
//         closeAllDropdowns();
//         setIsIcon(!isIcon);
//       };


//       const toggleColorDropdown = () => {
//         closeAllDropdowns();
//         setIsIconColor(!isIconColor);
//       };



//       const handleNotificationSelect = (option) => {
//         setNotificationType(option);
//         closeAllDropdowns();
        
//       };


//   const handleRecipientsSelect = (option) => {
//     setRecipients(option);
//     closeAllDropdowns();
    
//   };

//   const handleIconSelect = (option) => {
//     // Add state handling for icon selection
//     setIsIcon(false);
//     closeAllDropdowns();
//   };

//   const handleIconColorSelect = (option) => {
//     // Add state handling for icon color selection
//     setIsIconColor(false);
//     closeAllDropdowns();
//   };

//   return (
//     <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
//       <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
//         Create Notification
//       </h2>

//       <div className="bg-white border mt-5 p-6 md:p-10 rounded-xl h-auto dark:bg-black dark:border-none">
//         <div className="bg-white w-full max-w-3xl dark:bg-black">
//           <form>
//             {/* <h2 className="text-xl font-bold font-poppins mb-5">Create Notification</h2> */}

//             {/* Adjusted for responsive layout */}
//             <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//               {/* Notification Type Dropdown */}
//               <div className="relative" ref={notificationRef}>
//                 <label className="text-sm font-semibold text-black/40 mb-2 block dark:text-[#757575]">
//                   Notification Type
//                 </label>
//                 <button
//                   type="button"
//                   onClick={toggleNotificationDropdown}
//                   className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-3 flex justify-between items-center rounded-md text-sm font-poppins font-medium dark:text-[#757575] dark:bg-transparent focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
//                 >
//                   {notificationType || "Select notification type"}
//                   {notificationType ? (
//                     <FaChevronUp />
//                   ) : (
//                     <FaChevronDown />
//                   )}
//                 </button>
//                 {notificationType && (
//                   <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg dark:bg-black">
//                     {notificationOptions.map((option, index) => (
//                       <li
//                         key={index}
//                         className="px-4 py-3 cursor-pointer hover:bg-[#EEF9FF] font-poppins font-medium text-sm dark:text-[#757575] dark:hover:bg-[#393939b7]"
//                         onClick={() => handleNotificationSelect(option)}
//                       >
//                         {option}
//                       </li>
//                     ))}
//                   </ul>
//                 )}
//               </div>

//               {/* Recipients Dropdown */}
//               <div className="relative" ref={recipientsRef}>
//                 <label className="text-sm font-poppins font-semibold text-black/40 mb-2 block dark:text-[#757575]">
//                   Recipients
//                 </label>
//                 <button
//                   type="button"
//                   onClick={toggleRecipentsDropdown}
//                   className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-3 flex justify-between items-center rounded-md text-sm font-poppins font-medium dark:text-[#757575] dark:bg-transparent focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
//                 >
//                   {selectedRecipients}
//                   {recipients ? (
//                     <FaChevronUp />
//                   ) : (
//                     <FaChevronDown />
//                   )}
//                 </button>
//                 {recipients && (
//                   <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg dark:bg-black">
//                     {recipientOptions.map((option, index) => (
//                       <li
//                         key={index}
//                         className="px-4 py-3 cursor-pointer hover:bg-[#EEF9FF] font-poppins font-medium text-sm dark:text-[#757575] dark:hover:bg-[#393939b7]"
//                         onClick={() => handleRecipientsSelect(option)}
//                       >
//                         {option}
//                       </li>
//                     ))}
//                   </ul>
//                 )}
//               </div>
//             </div>

//             <div className="grid grid-cols-1 gap-6 mt-4">
//               <div>
//                 <label className="text-sm font-poppins font-semibold text-black/40 mb-2 block dark:text-[#757575]">
//                   Title
//                 </label>
//                 <input
//                   type="text"
//                   placeholder="4.5"
//                   className="w-[100%] md:w-[48%] bg-[#EEF9FF] border px-4 py-3 placeholder:text-black rounded-md text-sm font-poppins font-medium dark:placeholder:text-[#757575] dark:bg-transparent dark:border-white focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
//                 />
//               </div>
//               <div>
//                 <label className="text-sm font-poppins font-semibold text-black/40 mb-2 dark:text-[#757575] block">
//                   Message
//                 </label>
//                 <textarea
//                   rows={4}
//                   placeholder="45"
//                   className="w-full bg-[#EEF9FF] border dark:border-white px-4 py-3 rounded-md placeholder:text-black text-sm font-poppins font-medium dark:placeholder:text-[#757575] dark:bg-transparent focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
//                 />
//               </div>
//             </div>

//             {/* Updated grid layout for responsiveness */}
//             <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
//               {/* Icon Dropdown */}
//               <div className="relative" ref={iconRef}>
//                 <label className="text-sm font-poppins font-semibold text-black/40 mb-2 block dark:text-[#757575]">
//                   Icon
//                 </label>
//                 <button
//                   type="button"
//                   onClick={toggleIconDropdown}
//                   className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-3 flex justify-between items-center rounded-md placeholder:text-black text-sm font-poppins font-medium dark:text-[#757575] dark:bg-transparent focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
//                 >
//                   {selectedIcon}
//                   {isIcon ? <FaChevronUp /> : <FaChevronDown />}
//                 </button>
//                 {isIcon && (
//                   <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg dark:bg-black">
//                     {iconOptions.map((option, index) => (
//                       <li
//                         key={index}
//                         className="px-4 py-3 cursor-pointer hover:bg-[#EEF9FF] font-poppins font-medium text-sm dark:text-[#757575] dark:hover:bg-[#393939b7]"
//                         onClick={() => handleIconSelect(option)}
//                       >
//                         {option}
//                       </li>
//                     ))}
//                   </ul>
//                 )}
//               </div>

//               {/* Icon Color Dropdown */}
//               <div className="relative" ref={colorRef}>
//                 <label className="text-sm font-poppins font-semibold text-black/40 mb-2 block dark:text-[#757575]">
//                   Select Icon Color
//                 </label>
//                 <button
//                   type="button"
//                   onClick={toggleColorDropdown}
//                   className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-3 flex justify-between items-center rounded-md text-sm placeholder:text-black font-poppins font-medium dark:text-[#757575] dark:bg-transparent focus:outline-none focus:ring-1 focus:ring-blue-800 focus:border-blue-800"
//                 >
//                   {selectedIconColor}
//                   {isIconColor ? (
//                     <FaChevronUp />
//                   ) : (
//                     <FaChevronDown />
//                   )}
//                 </button>
//                 {isIconColor && (
//                   <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg dark:bg-black">
//                     {iconColorOptions.map((option, index) => (
//                       <li
//                         key={index}
//                         className="px-4 py-3 cursor-pointer hover:bg-[#EEF9FF] font-poppins font-medium text-sm dark:text-[#757575] dark:hover:bg-[#393939b7]"
//                         onClick={() => handleIconColorSelect(option)}
//                       >
//                         {option}
//                       </li>
//                     ))}
//                   </ul>
//                 )}
//               </div>
//             </div>


//             <div className="flex items-center justify-center gap-4 mt-10">
//               <Link
//                 href={"/superadmin/dashboard/notifications"}
//                 className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-24 py-2 text-sm font-poppins font-semibold  border  rounded  border-gray-200 text-black-100 dark:text-gray-100`}
//               >
//                 Cancel
//               </Link>
//               <button
//                 type="button"
//                 className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-24  py-2 text-sm font-poppins font-semibold  border  rounded  bg-sky-blue-650 text-white `}
//               >
//                 Create
//               </button>
//             </div>
//           </form>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default CreateNotification;




"use client";
import Link from "next/link";
import React, { useEffect, useRef, useState } from "react";
import { FaChevronDown, FaChevronUp } from "react-icons/fa6";


const CreateNotification = () => {
  const [selectedNotification, setSelectedNotification] = useState(
    "Select Notification"
  );
  const [selectedRecipients, setSelectedRecipients] =
    useState("Select Recipients");
  const [selectedIcon, setSelectedIcon] = useState(
    "Select Icon"
  );
  const [selectedIconColor, setSelectedIconColor] = useState("Select Color");

  const [isDropdownOpen, setIsDropdownOpen] = useState({
    notificationType: false,
    recipients: false,
    icon: false,
    iconColor: false,
  });

  const notificationRef = useRef(null);
  const recipientsRef = useRef(null);
  const iconRef = useRef(null);
  const iconColorRef = useRef(null);

  const notificationOptions = [
    "Notification Type 1",
    "Notification Type 2",
    "Notification Type 3",
  ];
  const recipientOptions = ["User", "Admin", "Hostel Owner"];
  const iconOptions = ["x", "y", "z",];
  const iconColorOptions = ["Blue", "Red", "Green", "Yellow",];

  // Function to close all dropdowns
  const closeAllDropdowns = () => {
    setIsDropdownOpen({
      notificationType: false,
      recipients: false,
      icon: false,
      iconColor: false,
    });
  };

  // Effect to handle outside clicks
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        !notificationRef.current?.contains(event.target) &&
        !recipientsRef.current?.contains(event.target) &&
        !iconRef.current?.contains(event.target) &&
        !iconColorRef.current?.contains(event.target)
      ) {
        closeAllDropdowns();
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => document.removeEventListener("click", handleClickOutside);
  }, []);

  // Toggle dropdown functions
  const toggleDropdown = (key) => {
    setIsDropdownOpen((prev) => ({
      notificationType:
        key === "notificationType" ? !prev.notificationType : false,
      recipients: key === "recipients" ? !prev.recipients : false,
      icon:
        key === "icon" ? !prev.icon : false,
      iconColor: key === "iconColor" ? !prev.iconColor : false,
    }));
  };

  // Handle selection
  const handleSelect = (key, option) => {
    if (key === "notificationType") setSelectedNotification(option);
    if (key === "recipients") setSelectedRecipients(option);
    if (key === "icon") setSelectedIcon(option);
    if (key === "iconColor") setSelectedIconColor(option);

    closeAllDropdowns();
  };

  return (

    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 dark:bg-[#171616] overflow-y-auto scroll-smooth h-screen">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        Create Notification
      </h2>

      <div className="bg-white border mt-5 p-6 md:p-10 rounded-xl dark:bg-black dark:border-none">
        <div className="bg-white w-full max-w-3xl dark:bg-black">
          <form>
            {/* <h2 className="text-xl font-bold font-poppins mb-5 dark:text-gray-100">
              Announcement Notification
            </h2> */}

            {/* Adjusted for small & medium screen responsiveness */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Notification Type Dropdown */}
              <div className="relative" ref={notificationRef}>
                <label className="text-sm font-poppins text-black/40 font-semibold mb-2 block dark:text-[#B6B6B6]">
                  Notification type
                </label>

                <button
                type="button"
                onClick={() => toggleDropdown("notificationType")}
                className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-medium dark:bg-transparent dark:text-[#757575] focus:outline-none focus:ring-2 focus:ring-blue-800 focus:border-blue-800"
              >
                {selectedNotification}
                {isDropdownOpen.notificationType ? (
                  <FaChevronUp />
                ) : (
                  <FaChevronDown />
                )}
              </button>
              {isDropdownOpen.notificationType && (
                <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg text-sm dark:bg-[#171616] dark:border-none">
                  {notificationOptions.map((option, index) => (
                    <li
                      key={index}
                      className="px-4 py-2 cursor-pointer hover:bg-[#EEF9FF] dark:hover:bg-[#393939b7] dark:text-[#757575] text-sm"
                      onClick={() => handleSelect("notificationType", option)}
                    >
                      {option}
                    </li>
                  ))}
                </ul>
              )}
              </div>

              {/* Recipients Dropdown */}
              <div className="relative" ref={recipientsRef}>
                <label className="text-sm font-poppins text-black/40 font-semibold mb-2 block dark:text-[#B6B6B6]">
                  Recipients
                </label>

                <button
                type="button"
                onClick={() => toggleDropdown("recipients")}
                className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-medium dark:bg-transparent dark:text-[#757575] focus:outline-none focus:ring-2 focus:ring-blue-800 focus:border-blue-800"
              >
                {selectedRecipients}
                {isDropdownOpen.recipients ? (
                  <FaChevronUp />
                ) : (
                  <FaChevronDown />
                )}
              </button>
              {isDropdownOpen.recipients && (
                <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg dark:bg-[#171616] dark:border-none">
                  {recipientOptions.map((option, index) => (
                    <li
                      key={index}
                      className="px-4 py-2 cursor-pointer hover:bg-[#EEF9FF] dark:hover:bg-[#393939b7] dark:text-[#757575] text-sm"
                      onClick={() => handleSelect("recipients", option)}
                    >
                      {option}
                    </li>
                  ))}
                </ul>
              )}
              </div>
            </div>

            {/* Title */}
            <div className="grid grid-cols-1 gap-6 mt-4">
              <div>
                <label className="text-sm font-poppins text-black/40 font-semibold mb-2 block dark:text-[#B6B6B6]">
                  Title
                </label>
                <input
                  type="text"
                  placeholder="Title"
                  className=" bg-[#EEF9FF] border  px-4 py-2 rounded-md text-sm font-poppins font-medium dark:bg-transparent dark:text-[#757575] w-[49%] dark:border-white focus:outline-none focus:border-none focus:ring-2 focus:ring-blue-800 focus:border-blue-800"
                  value="4.5"
                />
              </div>
            </div>

                        {/* Message */}
                        <div className="grid grid-cols-1 gap-6 mt-4">
              <div>
                <label className="text-sm text-black/40 font-semibold font-poppins mb-2 block dark:text-[#B6B6B6]">
                  Message
                </label>
                <textarea
                  rows="3"
                  placeholder="Message"
                  className="w-full bg-[#EEF9FF] border px-4 py-2 rounded-md font-poppins font-medium text-sm dark:bg-transparent dark:text-[#757575] dark:border-white focus:border-none focus:outline-none focus:ring-2 focus:ring-blue-800 focus:border-blue-800"
                  value="45"
                />
              </div>
            </div>


            {/* Announcement & Schedule */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
              {/* Announcement Visibility */}
              <div className="relative" ref={iconRef}>
                <label className="text-sm font-poppins text-black/40 font-semibold mb-2 block dark:text-[#B6B6B6]">
                  Icon
                </label>


<button
                type="button"
                onClick={() => toggleDropdown("icon")}
                className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-medium dark:bg-transparent dark:text-[#757575] focus:outline-none focus:ring-2 focus:ring-blue-800 focus:border-blue-800"
              >
                {selectedIcon}
                {isDropdownOpen.icon ? (
                  <FaChevronUp />
                ) : (
                  <FaChevronDown />
                )}
              </button>
              {isDropdownOpen.icon && (
                <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg dark:bg-[#171616] dark:border-none ">
                  {iconOptions.map((option, index) => (
                    <li
                      key={index}
                      className="px-4 py-2 cursor-pointer hover:bg-[#EEF9FF] dark:hover:bg-[#393939b7] dark:text-[#757575] text-sm"
                      onClick={() => handleSelect("icon", option)}
                    >
                      {option}
                    </li>
                  ))}
                </ul>
              )}
              </div>

              {/* Schedule Announcement */}
              <div className="relative" ref={iconColorRef}>
                <label className="text-sm font-poppins text-black/40 font-semibold mb-2 mt-[0px] md:mt-[19px] lg:mt-[0px] block dark:text-[#B6B6B6]">
                  Icon Color
                </label>

                                <button
                type="button"
                onClick={() => toggleDropdown("iconColor")}
                className="w-full bg-[#EEF9FF] border text-black text-left px-4 py-2 flex justify-between items-center rounded-md text-sm font-medium dark:bg-transparent dark:text-[#757575] focus:outline-none focus:ring-2 focus:ring-blue-800 focus:border-blue-800"
              >
                {selectedIconColor}
                {isDropdownOpen.iconColor ? (
                  <FaChevronUp />
                ) : (
                  <FaChevronDown />
                )}
              </button>
              {isDropdownOpen.iconColor && (
                <ul className="absolute z-10 w-full bg-white border border-gray-200 rounded-md mt-1 shadow-lg dark:bg-[#171616] dark:border-none">
                  {iconColorOptions.map((option, index) => (
                    <li
                      key={index}
                      className="px-4 py-2 cursor-pointer hover:bg-[#EEF9FF] dark:hover:bg-[#393939b7] dark:text-[#757575] text-sm"
                      onClick={() => handleSelect("iconColor", option)}
                    >
                      {option}
                    </li>
                  ))}
                </ul>
              )}
              </div>
            </div>


            {/* Buttons */}
            <div className="flex items-center justify-center gap-4 mt-10">
              <Link
                href={"/superadmin/dashboard/notifications"}
                className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-24 py-2 text-sm font-semibold  font-poppins border  rounded  border-gray-200 text-black-100 dark:text-gray-100`}
              >
                Cancel
              </Link>
              <button
                type="button"
                className={`relative flex  items-center justify-center h-10 px-12 md:px-16 lg:px-24  py-2 text-sm font-semibold font-poppins   rounded  bg-sky-blue-650 text-white `}
              >
                Create
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default CreateNotification;
