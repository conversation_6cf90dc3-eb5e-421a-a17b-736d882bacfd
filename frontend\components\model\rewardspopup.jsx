/* eslint-disable react/no-unknown-property */
import React from "react";
import Modal from "@mui/material/Modal";
import { IoCloseCircleOutline } from "react-icons/io5";

const Rewardspopup = ({ openRewardspopup, handleCloseRewardspopup }) => {
  const style = {
    position: "fixed",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: "100%",
    bgcolor: "background.paper",
    border: "2px solid #000",
    boxShadow: 24,
  };

  return (
    <>
      <Modal
        open={openRewardspopup}
        onClose={handleCloseRewardspopup}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <div sx={style}>
          <div className="bg-white rounded-2xl max-w-[500px] mx-auto left-1/2 absolute w-[95%] top-1/2 -translate-y-1/2 -translate-x-1/2">
            <div className="flex items-center p-2 justify-end">
              <button
                onClick={handleCloseRewardspopup}
                className="text-black text-2xl hover:text-primary-blue"
              >
                <IoCloseCircleOutline />
              </button>
            </div>
            <div className="md:p-6 p-4">
                <div>
                <h2 className="text-2xl font-bold text-black mb-4 text-center">
                  👋 Funds Added <span className="text-primary-blue">Successfully</span>
                </h2>
                </div>
              <div className="relative py-7 text-center">
                <div className="text-3xl text-black font-bold">INR5000</div>
                <div className="text-lg text-black font-bold">Credited to your wallet</div>
              </div>
              <div className="flex gap-1 py-2 justify-center">
              <button
                  className="bg-[#EEEEEE] text-black px-6 py-2 rounded-full text-[15px] font-bold"
                >
                 Back to my wallet 
                </button>
                <button
                  className="bg-primary-blue text-black px-6 py-2 rounded-full text-[15px] font-bold"
                >
                 View transaction 
                </button>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default Rewardspopup;
