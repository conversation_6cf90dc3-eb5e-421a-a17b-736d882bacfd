import React from 'react';
import Select from 'react-select';
import { ChevronDown } from 'lucide-react';


const customStyles = {
  control: (provided) => ({
    ...provided,
    
    height: '41.2px',
    padding: '0 0.5rem',
    fontSize: '0.75rem',
    borderColor: 'rgba(0, 0, 0, 0.5)',
    boxShadow: 'none',
    borderRadius: '0.5rem',
    scrollbarColor: '#3b82f6 #e5e7eb',
    cursor: 'pointer',
    '&:hover': {
      borderColor: 'rgba(0, 0, 0, 0.7)',
    },
  }),
  placeholder: (provided) => ({
    ...provided,
    color: '#6B7280',
  }),
  singleValue: (provided) => ({
    ...provided,
    color: '#000',
  }),
  menu: (provided) => ({
    ...provided,
    zIndex: 20,
  }),
  option: (provided, state) => ({
    ...provided,
    padding: '8px 12px',
    fontSize: '0.875rem',
    backgroundColor: state.isSelected ? '#40E0D0' : state.isFocused ? '#40E0D0' : 'white',
    color: state.isSelected || state.isFocused ? '#fff' : '#000',
    cursor: 'pointer',
  }),
};

const DropdownIndicator = () => (
  <div className="pr-3 text-gray-600">
    <ChevronDown className="w-5 h-5" />
  </div>
);

const CustomSelect = ({ options, value, onChange, placeholder, label, className }) => {
  return (
    <div className="relative">
      <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
        {label}
      </label>
      <Select
        options={options}
        value={options.find((opt) => opt.value === (typeof value === 'string' ? value : value?.value))}
        onChange={onChange}
        className={className}
        placeholder={placeholder}
        styles={customStyles}
        components={{ DropdownIndicator }}
        isSearchable
        classNamePrefix="react-select" 
      />
    </div>
  );
};

export default CustomSelect;
