/* eslint-disable react/no-unescaped-entities */
import React, { useState, useEffect } from "react";
import dynamic from "next/dynamic";
import axios from "axios";
import { useRouter } from "next/router";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import VisibilityOffOutlinedIcon from "@mui/icons-material/VisibilityOffOutlined";
import toast, { Toaster } from "react-hot-toast";
import {  Checkbox, FormControlLabel } from "@mui/material";
import { getPropertyCountApi, resendOtpApi } from "@/services/webflowServices";
import { removeItemLocalStorage,setItemLocalStorage,
  setToken } from "@/utils/browserSetting";
import { BASE_URL } from "@/utils/api";
import Link from "next/link";
import { requestForFCMToken } from "@/utils/firebaseConfig";
import { saveFirebaseToken } from "@/services/ownerflowServices";
import { useNavbar } from "@/components/home/<USER>";


const DividerAndSocialIcons = dynamic(() => import('@/components/ownerFlow/dividerAndSocialIcons'), {
  ssr: false
});
const OtpForm = dynamic(() => import("@/components/ownerFlow/otpForm"), { ssr: false });


const LoginForm = ({
  email,
  // setEmail,
  password,
  setPassword,
  loading,
  handleLogin,
  error,
  showPassword,
  setShowPassword,
  setOtaId,
  otaId
}) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <form onSubmit={handleLogin}>
      {/* <div className="mb-4">
        <label className='block text-black sm:text-base text-sm font-semibold mb-1.5'>
            Email
        </label>
        <input
          type="email"
          id="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="w-full px-3 py-4 border border-[#EEEEEE] rounded-full focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-[#888888] placeholder:font-light"
          placeholder="Email"
        />
      </div> */}
      <div className="mb-4">
        <label className='block text-black sm:text-base text-sm font-semibold mb-1.5'>
         Email / OTA ID
        </label>
        <input
          type="text"
          id="otaId"
          value={otaId}
          onChange={(e) => setOtaId(e.target.value)}
          className="w-full px-3 py-4 border border-[#EEEEEE] rounded-full focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-[#888888] placeholder:font-light"
          placeholder=" Email / OTA ID"
        />
      </div>
      <div className="relative mb-5">
        <label className='block text-black sm:text-base text-sm font-semibold mb-1.5'>
          Password
        </label>
        <input
          type={showPassword ? "text" : "password"}
          id="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          className="w-full px-3 py-4 border border-[#EEEEEE] rounded-full focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-[#888888] placeholder:font-light"
          placeholder="Password"
        />
        {isClient && (
          <button
            type="button"
            className="absolute right-4 top-11 text-gray-300 transform"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? (
              <VisibilityOutlinedIcon />
            ) : (
              <VisibilityOffOutlinedIcon />
            )}
          </button>
        )}
      </div>

      <div className="flex justify-between items-center mb-8">
        <FormControlLabel
          control={
            <Checkbox
              className="text-sm "
              sx={{
                color: "rgba(0,0,0,0.4)", // blue-800 color from Tailwind
                "&.Mui-checked": {
                  color: "#40E0D0", // blue-800 when checked
                },
              }}
            />
          }
          label={<span className="sm:text-base text-sm font-normal text-[#6D6D6D] font-manrope">Remember me</span>}
        />

        <Link
          href="/owner/forgot-password"
          className="text-[#40E0D0] font-bold cursor-pointer sm:text-base text-sm font-manrope"
          prefetch={false}
        >
          Forgot Password?
        </Link>
      </div>
      <button
        type="submit"
        className={`w-full font-semibold text-black py-3 rounded-full transition duration-200 ${
          (!email && !otaId) || !password 
            ? 'bg-gray-300 cursor-not-allowed' 
            : 'bg-[#40E0D0] hover:bg-[#40E0D0]'
        }`}
        disabled={loading || ((!email && !otaId) || !password)}  
      >
        {loading ? "Signing In..." : "Sign In"}
      </button>
      {error && <div className="mb-4 text-center text-red-600">{error}</div>}
    </form>
  );
};

const Login = () => {
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [otaId, setOtaId] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [error, setError] = useState(null);
  const [otp, setOtp] = useState("");
  // eslint-disable-next-line no-unused-vars
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const { updateUserStatus,updateUserRole,updateHopId } = useNavbar();


  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Step 1: Attempt login
      const response = await axios.post(`${BASE_URL}/auth/login`, {
        email,
        id: otaId,
        password,
        role: "hostel_owner"
      }, {
        validateStatus: () => {
          return true;
        },
      });
  
      const { status, data } = response;
      if(status === 403){
        try {
          const response = await resendOtpApi({ email });

          if (response?.data?.status) {
            toast.success(`We've sent a verification code to ${email}. Please enter this code to continue.`);
            
            router.push("/owner/verifyotpowner");
          } else {
            toast.error(
              response.data.message || "Failed to resend verification code."
            );
          }
        } catch (error) {
          toast.error(
            error.response?.data?.message ||
              "An error occurred while resending OTP."
          );
          console.error("Resend OTP error:", error);
          setOtp("");
        }
      }
      else if (status === 200 && data.status) {
        // Store user data in localStorage
        setItemLocalStorage("name", data.data.user.name.first);
        setItemLocalStorage("role", data.data.user.role);
        setItemLocalStorage("uid", data.data.user._id);
        updateUserStatus(data.data.token);
        updateUserRole(data.data.user.role);
        setToken(data.data.token);
  
        toast.success(data.message);
        const fcmToken = await requestForFCMToken();
        if (fcmToken) {
          setItemLocalStorage("FCT", fcmToken);
          await saveFirebaseToken({
            token: fcmToken,
            userId: data.data.user._id,
          });
        }
        // Step 2: Check property count
        try {
          const propertyCountResponse = await getPropertyCountApi();
          const { status: propertyStatus, data: propertyData } = propertyCountResponse;
  
          if (propertyStatus && propertyData?.status) {
            const propertyCount = propertyData.data.counts;
            const firstPropertyId = propertyData.data.properties[0]?._id;
  
            // Step 3: Redirect based on property count
            if (propertyCount >= 1 && firstPropertyId) {
              setItemLocalStorage("hopid", firstPropertyId);
              updateHopId(firstPropertyId)
              router.push("/owner/dashboard");
            } else {
              removeItemLocalStorage("hopid");
              updateHopId("");
              router.push("/owner/list");
            }
          } else {
            throw new Error("Failed to fetch property count");
          }
        } catch (error) {
          toast.error("Error fetching property count: " + error.message);
        }
      } else {
        // Handle login failure
        toast.error("Login failed: " + data.message);
      }
    } catch (error) {
      // Handle any login-related errors
      toast.error(error.response?.data?.message || error.message);
    } finally {
      setLoading(false);
    }
  };
  

  const handleVerifyOtp = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      const response = await axios.post(`${BASE_URL}/auth/verify-otp`, {
        email,
        otp,
      });
      if (response.status === 201 && response.data.status) {
        setItemLocalStorage("name", response?.data?.data?.user?.name.first);

        setToken(response?.data?.data?.token);
        toast.success("Login Success");
        router.push("/owner/list");
      } else {
        toast.error("OTP verification failed: ");
      }
    } catch (error) {
      toast.error(
        "Error during OTP verification: " +
          (error.response?.data?.message || error.message)
      );
    } finally {
      setLoading(false);
    }
  };

  const resendOTP = async () => {
    try {
      const response = await axios.post(`${BASE_URL}/auth/verify-email`, {
        email,
      });
      const { status, data } = response;
      if (status === 201 && data.status) {
        toast.success(data.message);
      } else {
        console.log("Failed to resend OTP: " + data.message);
      }
    } catch (error) {
      console.log(error.response?.data.message || error.message);
    }
  };

  return (
    <>
      <Toaster position="top-center" />
      <div className="min-h-screen flex items-center justify-center bg-[#F7F7F7] w-full pb-[10rem] pt-[3rem]">
        <div className="w-full max-w-3xl pb-6 bg-white shadow-md sm:px-14 px-7 sm:pt-14 pt-7 rounded-3xl md:max-w-2xl">
          <h2 className="mb-4 sm:text-[27px] text-lg font-bold text-gray-800">
            👋 Welcome To <span className="text-[#40E0D0]">Mix</span>Dorm
          </h2>
          <h4 className="sm:text-base text-sm text-black my-5 flex flex-col md:flex-row">
            Don't have an account?&nbsp;
            <Link
              href="/owner/signup"
              className="text-primary-blue font-medium cursor-pointer text-sm mt-0 md:mt-0.5 underline underline-offset-2"
              prefetch={false}
            > CREATE PROPERTY ACCOUNT
            </Link>
          </h4>
          {isOtpSent ? (
            <>
              <div className="mb-6 font-semibold">
                <h4 className="block text-black sm:text-base text-sm font-semibold mb-1.5">Enter Authentication Code</h4>
              </div>
              <div className="mb-6 text-[#2B303466]">
                <h4 className="block text-[#2B303466] text-sm font-normal mb-1.5">Two-Factor Authentication (2FA)</h4>
              </div>
              <div className="mb-6">
                <h5 className="block text-black text-xs font-normal mb-1.5">Enter Code</h5>
              </div>
              <OtpForm
                otp={otp}
                setOtp={setOtp}
                loading={loading}
                handleVerifyOtp={handleVerifyOtp}
                resendOTP={resendOTP}
              />
            </>
          ) : (
            <>
              <LoginForm
                email={email}
                setEmail={setEmail}
                setOtaId={setOtaId}
                otaId={otaId}
                password={password}
                setPassword={setPassword}
                loading={loading}
                setLoading={setLoading}
                handleLogin={handleLogin}
                error={error}
                showPassword={showPassword}
                setShowPassword={setShowPassword}
              />
            </>
          )}

          <DividerAndSocialIcons />
          
        </div>
      </div>
    </>
  );
};

export default Login;
