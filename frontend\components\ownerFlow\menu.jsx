 const Menu = [
  {
    id: 14,
    name: "Dashboard",
    link: "/owner/dashboard",
    icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/home.svg`,
  },
  {
    id: 19,
    name: "Calendar",
    link: "/owner/dashboard/calendar",
    icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/calender.svg`,
  },
  {
    id: 18,
    name: "Room Management",
    link: "/owner/dashboard/roommanagement",
    icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/roommgt.svg`,
  },
  {
    id: 20,
    name: "Analytics & Reports",
    link: "/owner/dashboard/analytics",
    icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/analytics.svg`,
  },
  {
    id: 8,
    name: "Booking Management",
    link: "/owner/dashboard/booking",
    icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/booking.svg`,
  },
  {
    id: 9,
    name: "Payments Management",
    link: "/owner/dashboard/payment",
    icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/payment.svg`,
  },
  // {
  //   id: 2,
  //   name: "Noticeboard",
  //   link: "/owner/dashboard/noticeboard",
  //   icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/noticeboard.svg`,
  // },
  {
    id: 3,
    name: "Review",
    link: "/owner/dashboard/reviews",
    icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/review.svg`,
  },
  // {
  //   id: 4,
  //   name: "Mix Creators",
  //   link: "/owner/dashboard/mixcreator",
  //   icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mixCreator.svg`,
  // },
  // {
  //   id: 5,
  //   name: "Host Ride",
  //   link: "/owner/dashboard/hostride",
  //   icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/hostRide.svg`,
  // },
  // {
  //   id: 6,
  //   name: "Web-E-checking",
  //   link: "/owner/dashboard/webcheckin",
  //   icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/webCheckin.svg`,
  // },
  {
    id: 7,
    name: "Events",
    link: "/owner/dashboard/event",
    icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/events.svg`,
  },
  // {
  //   id: 10,
  //   name: "Availability",
  //   link: "/owner/dashboard/availability",
  //   icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/availablity.svg`,
  // },
  
  // {
  //   id: 12,
  //   name: "Multiple Property",
  //   link: "#",
  //   icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/multipleProperty.svg`,
  // },
 
  // {
  //   id: 1,
  //   name: "Channel Integration",
  //   link: "/owner/dashboard/channelpartner",
  //   icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/channel_partner.svg`,
  // },
  {
    id: 13,
    name: "Property profile",
    link: "/owner/dashboard/propertyprofile",
    icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/property.svg`,
  },
  {
    id: 16,
    name: "",
    link: "",
    icon: ``,
  },
  // {
  //   id: 15,
  //   name: "Noticeboard",
  //   link: "/owner/dashboard/noticeboard",
  //   icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/notifications.svg`,
  // },
  {
    id: 17,
    name: "Settings",
    link: "/owner/dashboard/setting",
    icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/settings.svg`,
  },
  {
    id: 11,
    name: "FAQs",
    link: "/faqs",
    icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/faq.svg`,
  },
  
];
export default Menu;
