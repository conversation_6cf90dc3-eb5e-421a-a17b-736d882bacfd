import React, { useState, Fragment } from "react";
// import { Search, SlidersHorizontal } from "lucide-react";
import dynamic from "next/dynamic";
// import Breadcrumb from "@/components/breadcrumb/breadcrumb";
import Head from "next/head";
import Image from "next/image";
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
  Transition,
} from "@headlessui/react";

const Filter = dynamic(() => import("../../../components/model/filter"), {
  ssr: false,
});

const Creator = dynamic(
  () => import("@/components/ownerFlow/dashboard/creator"),
  {
    ssr: false,
  }
);

const Offersent = dynamic(
  () => import("@/components/ownerFlow/dashboard/offersent"),
  {
    ssr: false,
  }
);

const Addcreator = dynamic(
  () => import("@/components/ownerFlow/dashboard/addcreator"),
  {
    ssr: false,
  }
);

// const Editcreator = dynamic(
//   () => import("../../../components/ownerFlow/dashboard/editcreator"),
//   {
//     ssr: false,
//   }
// );

// const Creatordetails = dynamic(
//   () => import("../../../components/ownerFlow/dashboard/creatordetails"),
//   {
//     ssr: false,
//   }
// );

const Mixcreator = () => {
  const [activeTab, setActiveTab] = useState(1);
  // const [openAddMixCreator, setOpenAddMixCreator] = useState(false);
  // const [editOpen, setEditOpen] = useState(false);
  // const [viewOpen, setViewOpen] = useState(false);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const closefilterModal = () => setIsModalOpen(false);
  const [openAddcreator, setOpenAddcreator] = useState(false);
  const closecreatorModal = () => setOpenAddcreator(false);
  // const [openEditcreator, setopenEditcreator] = useState(false);
  // const closecreatoreditModal = () => setopenEditcreator(false);
  // const [openViewcreator, setopenViewcreator] = useState(false);
  // const closecreatorviewModal = () => setopenViewcreator(false);

  const tabs = [
    {
      id: 1,
      name: "All Creators",
      count: "23,325",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/All_creators.svg`,
      icon1: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/All_creators_b.svg`,
    },
    {
      id: 2,
      name: "Offer Sent",
      count: "250",
      icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Offer_sent.svg`,
      icon1: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Offer_sent_b.svg`,
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 1:
        return <Creator setActiveTab={setActiveTab} />;
      case 2:
        return <Offersent />;

      default:
        return <Creator setActiveTab={setActiveTab} />;
    }
  };

  return (
    <>
      <Head>
        <title>MixCreator Dashboard | Mixdorm</title>
      </Head>

      <section className="w-full">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-medium text-gray-800">Mix Creator</h2>
          <div className="flex sm:gap-2 gap-1 relative">
            <button
              className="flex items-center text-center justify-center  text-xs text-black rounded-lg w-fit bg-primary-blue font-medium px-6 py-2 md:py-0"
              onClick={() => setIsModalOpen(true)}
            >
              <Image
                className="sm:mr-2 mx-auto"
                src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/filter.svg`}
                width={20}
                height={20}
              />
              Filter
            </button>

            <button
              className="flex items-center justify-center md:px-6 sm:px-3 px-2 md:py-3 sm:py-2 py-1.5 md:text-sm text-xs text-black rounded-lg w-fit bg-primary-blue font-medium"
              onClick={() => setOpenAddcreator(true)}
            >
              Send Offer
            </button>
          </div>
        </div>
        <div className="w-full mt-5 bg-white shadow-4xl rounded-2xl">
          <div className="mb-4">
            <ul className="grid lg:grid-cols-[repeat(3,_1fr)_1fr] grid-cols-2 font-normal text-xs sm:gap-4 gap-2">
              {tabs.map((item) => (
                <li
                  key={item.id}
                  className={`flex items-center sm:gap-10 lg:gap-4 gap-2 sm:p-4 p-2 relative border rounded-lg border-[#D0D3D9] ${
                    activeTab === item.id ? "bg-black" : ""
                  } cursor-pointer`}
                  onClick={() => setActiveTab(item.id)}
                >
                  <div>
                    <a
                      className={`font-medium ${
                        activeTab === item.id
                          ? "text-[#ffffff]"
                          : "text-[#000000]"
                      } cursor-pointer text-left xl:text-lg text-sm`}
                      href="#"
                    >
                      {item.name}
                    </a>
                    <h5
                      className={`text-base text-[#00000080] font-semibold mt-3 ${
                        activeTab === item.id
                          ? "text-[#FFFFFF80]"
                          : "text-[#00000080]"
                      } cursor-pointer`}
                    >
                      <span
                        className={`lg:text-[26px] text-lg ${
                          activeTab === item.id
                            ? "text-[#40E0D0]"
                            : "text-[#000000]"
                        } cursor-pointer`}
                      >
                        {" "}
                        {item.count}
                      </span>
                    </h5>
                  </div>
                  {activeTab === item.id ? (
                    <Image
                      src={item.icon}
                      width={40}
                      height={40}
                      alt="BgImg"
                      className="ml-auto xl:w-[40px] xl:h-[40px] lg:w-[30px] lg:h-[30px] w-[25px] h-[25px]"
                      loading="lazy"
                    />
                  ) : (
                    <Image
                      src={item.icon1}
                      width={40}
                      height={40}
                      alt="BgImg"
                      className="ml-auto xl:w-[40px] xl:h-[40px] lg:w-[30px] lg:h-[30px] w-[25px] h-[25px]"
                      loading="lazy"
                    />
                  )}
                </li>
              ))}
            </ul>
          </div>
          <div className="w-full">{renderTabContent()}</div>
        </div>
      </section>

      {/* <div className='fixed inset-0 bg-white/70 justify-center items-center top-20 left-28 hidden lg:flex'>
        <div className='bg-white p-6 rounded-2xl shadow-xl w-[650px] h-[466px] text-center  flex flex-col items-center justify-around z-20'>
          <Image
            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Coming-Soon.png`}
            alt='Coming Soon'
            width={300}
            height={200}
            className='w-[455px] h-[288px]'
          />
          <h2 className='text-lg font-medium mt-4 font-inter px-10'>
            Hang tight! This section is getting a refresh. We’ll be back soon
            with something{" "}
            <span className='text-primary-blue font-serif text-xl font-bold'>
              amazing!
            </span>
          </h2>
        </div>
      </div>
      <div className='lg:hidden fixed inset-0 bg-white/70 flex justify-center items-center md:left-48 p-4'>
        <div className='bg-white p-4 md:p-6 rounded-2xl shadow-xl w-full max-w-[90%] md:max-w-[500px] lg:w-[650px] lg:h-[466px] text-center flex flex-col items-center justify-around z-20'>
          <Image
            src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Coming-Soon.png`}
            alt='Coming Soon'
            width={300}
            height={200}
            className='w-[300px] h-[180px] md:w-[400px] md:h-[250px] lg:w-[455px] lg:h-[288px]'
          />
          <h2 className='text-base md:text-lg font-medium mt-4 font-inter px-4 md:px-10'>
            Hang tight! This section is getting a refresh. We’ll be back soon
            with something{" "}
            <span className='text-primary-blue font-serif text-lg md:text-xl font-bold'>
              amazing!
            </span>
          </h2>
        </div>
      </div> */}
      {isModalOpen && (
        <Dialog
          open={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          className="relative z-50"
        >
          <DialogBackdrop
            transition
            className="fixed inset-0 bg-[#000000B2] transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
          />

          <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
            <div className="flex min-h-full justify-center p-4 text-center items-center sm:p-0">
              <DialogPanel
                transition
                className="relative transform overflow-hidden rounded-2xl bg-white text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-full sm:max-w-[490px] max-w-full w-full data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95"
              >
                <div className="bg-white sm:px-7 sm:pb-7 pt-3 p-3 pb-3">
                  <DialogTitle>
                    <h3 className="text-center text-black font-bold sm:text-lg text-sm">
                      Filter
                    </h3>
                  </DialogTitle>
                  <Filter closefilterModal={closefilterModal} />
                </div>
              </DialogPanel>
            </div>
          </div>
        </Dialog>
      )}

      <Transition show={openAddcreator} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={closecreatorModal}>
          {/* Overlay */}
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/50" />
          </Transition.Child>

          {/* Slide-In Modal */}
          <div className="fixed inset-0 overflow-hidden">
            <div className="absolute inset-0 flex justify-end sm:top-20 top-16">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-out duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in duration-200"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="md:w-[42.5%] sm:w-[60%] w-[90%] h-full bg-white shadow-xl rounded-l-md overflow-y-auto modalscroll">
                  {/* Modal Header */}
                  <div className="sm:p-6 p-4 flex justify-between items-center bg-white/60 sticky top-0 backdrop-blur-sm z-50">
                    <h2 className="page-title">Add Creator</h2>
                    <button
                      onClick={closecreatorModal}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      &#10005; {/* Close icon */}
                    </button>
                  </div>

                  {/* Modal Content */}
                  <div className="sm:px-6 px-4">
                    <Addcreator
                      closecreatorModal = {closecreatorModal}
               
                    ></Addcreator>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

    </>
  );
};

export default Mixcreator;
