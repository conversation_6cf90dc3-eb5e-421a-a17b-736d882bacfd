import express from 'express';
import { addRideController, getAllRidesController, 
  getRideByIdController, cancelRideController, 
  getUserRidesController,addRideReviews,getRideReviews,editRideController,deleteRideController, 
  getRideRecentSearches} from '../controller/ride.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();

router.post('/', checkAuth("add_ride"), addRideController);
router.get('/', checkAuth("get_all_rides"), getAllRidesController);
router.get('/user', checkAuth(""), getUserRidesController);
router.get('/recent-searches',checkAuth(), getRideRecentSearches)
router.get('/:id', checkAuth(""), getRideByIdController);
router.put('/:id/cancel', checkAuth("cancel_ride"), cancelRideController);
router.post('/rideReviews/:rideId',checkAuth(), addRideReviews);
router.get('/getRidesReviews/:rideId',checkAuth(), getRideReviews);
router.put('/:rideId', checkAuth("add_ride"), editRideController);
router.delete('/:rideId', checkAuth("add_ride"), deleteRideController);

export default router;

/**
 * @swagger
 * /rides:
 *   post:
 *     summary: Add a new ride
 *     tags: [Rides]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Ride'
 *     responses:
 *       201:
 *         description: Ride created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Ride'
 *       400:
 *         description: Bad request
*/

/**
 * @swagger
 * /rides:
 *   get:
 *     summary: Get all rides
 *     tags: [Rides]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Successfully retrieved rides
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 rides:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Ride'
 *                 totalRides:
 *                   type: integer
 *       400:
 *         description: Bad request
 */

/**
 * @swagger
 * /rides/{id}:
 *   get:
 *     summary: Get a ride by ID
 *     tags: [Rides]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Ride ID
 *     responses:
 *       200:
 *         description: Ride retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Ride'
 *       404:
 *         description: Ride not found
 */

/**
 * @swagger
 * /rides/{id}/cancel:
 *   put:
 *     summary: Cancel a ride
 *     tags: [Rides]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Ride ID
 *     responses:
 *       200:
 *         description: Ride cancelled successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Ride'
 *       400:
 *         description: Bad request
 */

/**
 * @swagger
 * /rides/user:
 *   get:
 *     summary: Get all rides for the logged-in user
 *     tags: [Rides]
 *     responses:
 *       200:
 *         description: Successfully retrieved user's rides
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Ride'
 *       400:
 *         description: Bad request
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Ride:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *         user:
 *           type: string
 *           description: User ID reference
 *         fromAddress:
 *           type: object
 *           properties:
 *             address:
 *               type: string
 *             location:
 *               type: object
 *               properties:
 *                 type:
 *                   type: string
 *                 coordinates:
 *                   type: array
 *                   items:
 *                     type: number
 *         destination:
 *           type: object
 *           properties:
 *             address:
 *               type: string
 *             location:
 *               type: object
 *               properties:
 *                 type:
 *                   type: string
 *                 coordinates:
 *                   type: array
 *                   items:
 *                     type: number
 *         date:
 *           type: string
 *           format: date
 *         time:
 *           type: object
 *           properties:
 *             start:
 *               type: number
 *             end:
 *               type: number
 *         passenger:
 *           type: number
 *         transportMode:
 *           type: string
 *           enum: [bus, private_car, cab, bike]
 *         name:
 *           type: string
 *         model:
 *           type: string
 *         number:
 *           type: string
 *         capacity:
 *           type: number
 *         photos:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               filename:
 *                 type: string
 *               path:
 *                 type: string
 *               url:
 *                 type: string
 *         isCancelled:
 *           type: boolean
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
*/

/**
@swagger
 * /rides/rideReviews/{rideId}:
 *   post:
 *     tags: [Rides]
 *     summary: Add a review for a ride
 *     description: Allows users to add a review for a specific ride.
 *     parameters:
 *       - in: path
 *         name: rideId
 *         required: true
 *         description: ID of the ride to review.
 *         schema:
 *           type: string
 *       - in: requestBody
 *         name: body
 *         required: true
 *         description: Review data for the ride.
 *         schema:
 *           type: object
 *           required:
 *             - rating
 *           properties:
 *             rating:
 *               type: number
 *               description: Rating of the ride (0-5).
 *               example: 4
 *             comment:
 *               type: string
 *               description: Comment about the ride.
 *               example: "Great event!"
 *             images:
 *               type: array
 *               description: Optional images related to the review.
 *               items:
 *                 type: object
 *                 properties:
 *                   url:
 *                     type: string
 *                     description: URL of the image.
 *                     example: "http://example.com/image.jpg"
 *                   title:
 *                     type: string
 *                     description: Title or description of the image.
 *                     example: "Front view"
 *             likes:
 *               type: number
 *               description: Number of likes for the review.
 *               example: 10
 *             dislikes:
 *               type: number
 *               description: Number of dislikes for the review.
 *               example: 2
 *     responses:
 *       201:
 *         description: Review added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 _id:
 *                   type: string
 *                   description: ID of the created review.
 *                 user:
 *                   type: string
 *                   description: ID of the user who created the review.
 *                 event:
 *                   type: string
 *                   description: ID of the event being reviewed.
 *                 rating:
 *                   type: number
 *                 comment:
 *                   type: string
 *                 images:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       url:
 *                         type: string
 *                       title:
 *                         type: string
 *                 likes:
 *                   type: number
 *                 dislikes:
 *                   type: number
 *                 isActive:
 *                   type: boolean
 *                 isDeleted:
 *                   type: boolean
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *                 updatedAt:
 *                   type: string
 *                   format: date-time
 *       400:
 *         description: Bad request, invalid input
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Invalid input provided"
 *       404:
 *         description: Event not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Event not found"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */

/**
   @swagger
 * /rides/getRidesReviews/{rideId}:
 *   get:
  *     summary: Get ride reviews by ride ID with pagination and optional rating filter
 *     tags: [Rides]
 *     parameters:
 *       - in: path
 *         name: rideId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the ride for which reviews are to be fetched
 *       - in: query
 *         name: page
 *         required: false
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         required: false
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of reviews per page
 *       - in: query
 *         name: rating
 *         required: false
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 5
 *         description: Filter reviews by rating (1 to 5)
 *     responses:
 *       200:
 *         description: Ride reviews retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Ride reviews fetched successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     reviews:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           _id:
 *                             type: string
 *                             example: "review123"
 *                           user:
 *                             type: object
 *                             properties:
 *                               name:
 *                                 type: string
 *                                 example: "John Doe"
 *                               avatar:
 *                                 type: string
 *                                 example: "avatar.jpg"
 *                           rating:
 *                             type: integer
 *                             example: 5
 *                           comment:
 *                             type: string
 *                             example: "Great ride!"
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                             example: "2024-11-01T12:00:00Z"
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         currentPage:
 *                           type: integer
 *                           example: 1
 *                         totalPages:
 *                           type: integer
 *                           example: 2
 *                         totalReviews:
 *                           type: integer
 *                           example: 15
 *                         limit:
 *                           type: integer
 *                           example: 10
 *       404:
 *         description: No reviews found for the specified ride
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: No reviews found for this ride
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Internal server error
 */

 /**
 * @swagger
 * /rides/{rideId}:
 *   put:
 *     summary: Edit an existing ride
 *     tags: [Rides]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: rideId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the ride to be updated
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               fromAddress:
 *                 type: object
 *                 properties:
 *                   address:
 *                     type: string
 *                   location:
 *                     type: object
 *                     properties:
 *                       type:
 *                         type: string
 *                       coordinates:
 *                         type: array
 *                         items:
 *                           type: number
 *                 description: Updated starting address and location
 *               destination:
 *                 type: object
 *                 properties:
 *                   address:
 *                     type: string
 *                   location:
 *                     type: object
 *                     properties:
 *                       type:
 *                         type: string
 *                       coordinates:
 *                         type: array
 *                         items:
 *                           type: number
 *                 description: Updated destination address and location
 *               date:
 *                 type: string
 *                 format: date
 *                 description: Updated date of the ride (YYYY-MM-DD)
 *               time:
 *                 type: object
 *                 properties:
 *                   start:
 *                     type: number
 *                   end:
 *                     type: number
 *                 description: Updated time range for the ride
 *               transportMode:
 *                 type: string
 *                 enum: [bus, private_car, cab, bike]
 *                 description: Updated mode of transport
 *               passenger:
 *                 type: number
 *                 description: Updated number of passengers
 *               name:
 *                 type: string
 *                 description: Updated name of the ride
 *               model:
 *                 type: string
 *                 description: Updated vehicle model
 *               number:
 *                 type: string
 *                 description: Updated vehicle number
 *               capacity:
 *                 type: number
 *                 description: Updated vehicle capacity
 *               photos:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     filename:
 *                       type: string
 *                     path:
 *                       type: string
 *                     url:
 *                       type: string
 *                 description: Updated photos of the ride
 *               price:
 *                 type: number
 *                 description: Updated price of the ride
 *               currency:
 *                 type: string
 *                 description: Updated currency code
 *             example:
 *               fromAddress:
 *                 address: "Central Park"
 *                 location:
 *                   type: "Point"
 *                   coordinates: [-73.968285, 40.785091]
 *               destination:
 *                 address: "Wall Street"
 *                 location:
 *                   type: "Point"
 *                   coordinates: [-74.009173, 40.706091]
 *               date: "2024-12-15"
 *               time:
 *                 start: 900
 *                 end: 1000
 *               transportMode: "bike"
 *               name: "Morning Commute"
 *               price: 50
 *               currency: "USD"
 *     responses:
 *       200:
 *         description: Ride updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   description: Details of the updated ride
 *             example:
 *               success: true
 *               data:
 *                 _id: "643d2f5b9f1e2c123abc4567"
 *                 fromAddress:
 *                   address: "Central Park"
 *                   location:
 *                     type: "Point"
 *                     coordinates: [-73.968285, 40.785091]
 *                 destination:
 *                   address: "Wall Street"
 *                   location:
 *                     type: "Point"
 *                     coordinates: [-74.009173, 40.706091]
 *                 date: "2024-12-15"
 *                 time:
 *                   start: 900
 *                   end: 1000
 *                 transportMode: "bike"
 *                 name: "Morning Commute"
 *                 price: 50
 *                 currency: "USD"
 *                 user: "643d2f1b9f1e2c123abc1234"
 *       404:
 *         description: Ride not found or unauthorized
 *         content:
 *           application/json:
 *             example:
 *               success: false
 *               message: "Ride not found or unauthorized"
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             example:
 *               success: false
 *               message: "Invalid data provided"
 */

/**
 * @swagger
 * /rides/{rideId}:
 *   delete:
 *     tags: [Rides]
 *     summary: Delete a ride
 *     description: Deletes a ride by its ID. Only users with the `add_ride` permission can perform this action.
 *     parameters:
 *       - in: path
 *         name: rideId
 *         required: true
 *         description: The ID of the ride to delete.
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Ride deleted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Ride deleted successfully.
 *       401:
 *         description: Unauthorized access.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Unauthorized access.
 *       403:
 *         description: Insufficient permissions.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Insufficient permissions.
 *       404:
 *         description: Ride not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Ride not found.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Something went wrong.
 */




/**
 * @swagger
 * /rides/recent-searches:
 *   get:
 *     summary: Get a ride by ID
 *     tags: [Rides]
 *     responses:
 *       200:
 *         description: Ride retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Ride'
 *       404:
 *         description: Ride not found
 */

