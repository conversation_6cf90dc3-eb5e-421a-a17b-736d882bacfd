"use client";
import { Plus } from "lucide-react";
import React, { useState } from "react";
import { FaRegTrashCan } from "react-icons/fa6";
import { FiEye } from "react-icons/fi";
import { TfiPencilAlt } from "react-icons/tfi";
import Image from "next/image";
import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight,
} from "react-icons/md";
import Link from "next/link";

const MobileHomePage = () => {
  // eslint-disable-next-line no-unused-vars
  const [OpenAddHostel, setOpenAddHostel] = useState("");

  const mobilehomepageData = [
    {
      id: 1,
      hostelName: "Made monkey Ubdud",
      city: "Bali, Indonesia",
      amount: "$256",
      flag: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`,
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`,
    },
    {
      id: 2,
      hostelName: "Made monkey Ubdud",
      city: "Bali, Indonesia",
      amount: "$256",
      flag: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`,
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`,
    },
    {
      id: 3,
      hostelName: "Made monkey Ubdud",
      city: "Bali, Indonesia",
      amount: "$256",
      flag: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`,
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`,
    },
    {
      id: 4,
      hostelName: "Made monkey Ubdud",
      city: "Bali, Indonesia",
      amount: "$256",
      flag: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`,
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`,
    },
    {
      id: 5,
      hostelName: "Made monkey Ubdud",
      city: "Bali, Indonesia",
      amount: "$256",
      flag: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Flag.png`,
      image: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`,
    },
  ];

  return (
    <div className=" lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616] h-screen">
      <div className="flex items-center justify-between w-full ">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Mobile
        </h2>
        <div className="w-[50%] gap-x-5 flex justify-end items-center">
          <button
            className={` px-4 py-2 text-sm font-normal text-white rounded relative flex justify-center items-center bg-sky-blue-650 `}
            type="button"
            onClick={() => setOpenAddHostel(true)}
          >
            <Link
              href={"/superadmin/dashboard/mobile-homepage-add"}
              className="flex text-sm font-medium font-poppins"
            >
              <Plus size={18} className="mr-1" /> Hostel
            </Link>
          </button>
        </div>
      </div>
      <div className="bg-white border-b border-l border-r rounded-xl dark:bg-black dark:border-none pb-2">
        <div className="overflow-x-auto mt-5 mb-10 rounded-t-xl border-b">
          <table className="min-w-full divide-y  bg-white dark:bg-black rounded-xl divide-gray-200">
            <thead>
              <tr className=" ">
                <th className=" py-6 bg-white text-center text-sm font-semibold font-poppins text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  NO.
                </th>

                <th className="py-6 bg-white text-center text-sm font-semibold font-poppins  text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  HOSTEL NAME
                </th>
                <th className=" py-6 bg-white text-center text-sm font-semibold font-poppins  text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  CITY
                </th>
                <th className="px-4 md:px-8 lg:px-4 py-6 bg-white text-left text-sm font-semibold font-poppins text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  FLAG
                </th>
                <th className="py-6 px-8 bg-white text-left text-sm font-semibold font-poppins text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  IMAGE
                </th>
                <th className="pl-8  lg:pl-0  py-6   bg-white text-center text-sm font-semibold font-poppins text-black uppercase tracking-wider dark:bg-black dark:text-[#B6B6B6]">
                  PRICE
                </th>

                <th className="pr-8 py-6 bg-white  text-sm font-semibold font-poppins text-black uppercase tracking-wider text-end dark:bg-black dark:text-[#B6B6B6] ">
                  ACTION
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 border-y text-black/70">
              {mobilehomepageData.map((mobile) => (
                <tr key={mobile.id}>
                  <td className="text-center whitespace-nowrap px-3 text-sm font-medium font-poppins dark:text-[#757575]">{mobile.id}</td>
                  <td className="text-center whitespace-nowrap px-3 text-sm font-medium font-poppins dark:text-[#757575]">
                    {mobile.hostelName}
                  </td>

                  <td className="text-center whitespace-nowrap pl-4 lg:pl-2 px-4 text-sm font-medium font-poppins dark:text-[#757575]">{mobile.city}</td>
                  <td className=" whitespace-nowrap px-2 md:px-6 lg:px-2 pl-4 lg:pl-3">
                    <Image
                      src={mobile.flag}
                      width={48}
                      height={48}
                      alt="Flag"
                      className="w-10 h-10 md:w-12 md:h-12 lg:w-12 lg:h-12 mr-4 md:mr-0 lg:mr-0"
                    />
                  </td>
                  <td className="pl-2 lg:pl-1">
                    <button className="font-medium py-1 px-1 md:px-2 rounded">
                      <Image
                        src={mobile.image}
                        alt="Image"
                        className="w-24 h-14 rounded-xl "
                        width={80}
                        height={48}
                      />
                    </button>
                  </td>
                  <td className="whitespace-nowrap pl-8  lg:pl-0 text-sm font-medium font-poppins text-center dark:text-[#757575]">
                    {mobile.amount}
                  </td>

                  <td className=" py-5 px-2 flex justify-end pl-8 lg:pl-0">
                    <Link href={"/superadmin/dashboard/mobile-homepage-details"} className=" border p-2 rounded-l-lg text-black/75 hover:text-blue-600 dark:text-[#757575] dark:hover:text-blue-600">
                      <FiEye />
                    </Link>
                    <button className=" border p-2 text-black/75 hover:text-yellow-400 dark:text-[#757575] dark:hover:text-yellow-400">
                      <Link href={"/superadmin/dashboard/mobile-homepage-edit"}>
                        <TfiPencilAlt />
                      </Link>
                    </button>
                    <button className=" p-2 border rounded-r-lg text-red-600">
                      <FaRegTrashCan />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      {/* <div className="flex justify-between items-center mt-5">
        <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
        <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:border-none dark:bg-black">
          <a
            href="#"
            className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:border-none dark:text-[#B6B6B6]"
          >
            <span className="sr-only">Next Page</span>
            <MdOutlineKeyboardArrowLeft />
          </a>

          <a
            href="#"
            className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:border-none dark:text-[#B6B6B6]"
          >
            <span className="sr-only">Next Page</span>
            <MdOutlineKeyboardArrowRight />
          </a>
        </div>
      </div> */}

            <div className="flex justify-between items-center mt-5">
              <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">Showing 1-09 of 78</div>
              <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
                <a
                  href="#"
                  className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                >
                  <span className="sr-only">Next Page</span>
                  <MdOutlineKeyboardArrowLeft />
                </a>
      
                <a
                  href="#"
                  className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
                >
                  <span className="sr-only">Next Page</span>
                  <MdOutlineKeyboardArrowRight />
                </a>
              </div>
            </div>
    </div>
  );
};

export default MobileHomePage;
