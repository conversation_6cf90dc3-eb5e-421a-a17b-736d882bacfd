import ECheckIn from '../models/eCheckIns.js';
import Response from "../utills/response.js";
import mongoose from 'mongoose';
export const createCheckIn = async (req, res) => {
  try {
    const { user, booking, property, checkIn, checkOut,guestDetails, uploadedDocuments,toLocation,fromLocation,phone } = req.body;

    // Validate required fields
    if (!property || !checkIn  || !booking) {
      return Response.BadRequest(res, null, 'PropertyId, Check in date booking Id and guset details adre required fields.');
    }
     // Find the latest CheckIn for the same property to generate a new refNumber
     const lastCheckIn = await ECheckIn.findOne({ property }).sort({ createdAt: -1 }).select('refNumber');
     let newRefNumber;
 
     if (lastCheckIn && lastCheckIn.refNumber) {
       // Extract the numeric part from the last refNumber and increment it
       const lastRefNumber = parseInt(lastCheckIn.refNumber.split('-')[1]);
       newRefNumber = `#MD-${lastRefNumber + 1}`;
     } else {
       // If no check-in records exist for this property, start with #MD-1
       newRefNumber = '#MD-1';
     }
 
    const newCheckIn = new ECheckIn({
      user,
      booking,
      property,
      checkIn,
      guestDetails,
      uploadedDocuments,
      checkOut,
      fromLocation,
      toLocation,
      phone,
      refNumber: newRefNumber
    });

    await newCheckIn.save();

    return Response.Created(res, newCheckIn, 'Check-in details submitted successfully.');
  } catch (error) {
    console.error('Error creating e-check-in:', error);
    return Response.InternalServerError(res, null, error.message);
  }
};

export const getCheckInStatus = async (req, res) => {
  try {
    const { id } = req.params;

    const checkInDetails = await ECheckIn.findOne({ _id: id,isDeleted:false });

    if (!checkInDetails) {
      return Response.NotFound(res, null, 'Check-in details not found.');
    }

    return Response.OK(res, checkInDetails, 'Check-in details retrieved successfully.');
  } catch (error) {
    console.error('Error fetching check-in status:', error);
    return Response.InternalServerError(res, null, error.message);
  }
};
export const getCheckInByProperty = async (req, res) => {
  try {
    const { id } = req.params;
    let { page, limit } = req.query;

    // Convert page and limit to integers
    page = parseInt(page, 10);
    limit = parseInt(limit, 10);

    // Default values for invalid page or limit
    if (isNaN(page) || page <= 0) {
      page = 1;
    }
    if (isNaN(limit) || limit <= 0) {
      limit = 10;
    }

    // Calculate skip value
    const skip = (page - 1) * limit;

    // Fetch paginated check-in details
    const checkInDetails = await ECheckIn.find({
      property: new mongoose.Types.ObjectId(id),
      isDeleted: false,
    })
      .skip(skip)
      .limit(limit);

    // Count total documents for pagination metadata
    const totalDocuments = await ECheckIn.countDocuments({
      property: new mongoose.Types.ObjectId(id),
      isDeleted: false,
    });
    const totalPages = Math.ceil(totalDocuments / limit);
    // Prepare pagination metadata
    const pagination = {
      page,
      limit,
      totalPages,
      totalDocuments,
    };

    return Response.OK(res, { checkInDetails, pagination }, 'Check-in details retrieved successfully.');
  } catch (error) {
    console.error('Error fetching check-in status:', error);
    return Response.InternalServerError(res, null, error.message);
  }
};

export const editCheckIn = async (req, res) => {
  try {
    const { id } = req.params; // Check-in ID to update
    const {
      user,
      booking,
      property,
      checkIn,
      checkOut,
      guestDetails,
      uploadedDocuments,
      toLocation,
      fromLocation,
      phone,
    } = req.body;

    // Validate that the Check-In ID exists
    const checkInDetails = await ECheckIn.findById(id);

    if (!checkInDetails) {
      return Response.NotFound(res, null, 'Check-in record not found.');
    }

    // Update the fields if they are provided in the request body
    if (user) checkInDetails.user = user;
    if (booking) checkInDetails.booking = booking;
    if (property) checkInDetails.property = property;
    if (checkIn) checkInDetails.checkIn = checkIn;
    if (checkOut) checkInDetails.checkOut = checkOut;
    if (guestDetails) checkInDetails.guestDetails = guestDetails;
    if (uploadedDocuments) checkInDetails.uploadedDocuments = uploadedDocuments;
    if (toLocation) checkInDetails.toLocation = toLocation;
    if (fromLocation) checkInDetails.fromLocation = fromLocation;
    if (phone) checkInDetails.phone = phone;

    // Save the updated record
    await checkInDetails.save();

    return Response.OK(res, checkInDetails, 'Check-in details updated successfully.');
  } catch (error) {
    console.error('Error updating check-in:', error);
    return Response.InternalServerError(res, null, error.message);
  }
};
export const deleteCheckIn = async (req, res) => {
  try {
    const { id } = req.params; // Check-in ID to update
  
    // Validate that the Check-In ID exists
    const checkInDetails = await ECheckIn.findById(id);

    if (!checkInDetails) {
      return Response.NotFound(res, null, 'Check-in record not found.');
    }
    checkInDetails.isDeleted = true
    // Save the updated record
    await checkInDetails.save();

    return Response.OK(res, checkInDetails, 'Check-in details deleted successfully.');
  } catch (error) {
    console.error('Error updating check-in:', error);
    return Response.InternalServerError(res, null, error.message);
  }
};
