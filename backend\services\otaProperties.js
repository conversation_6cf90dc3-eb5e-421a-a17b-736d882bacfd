import roomModel from '../models/room.js';
import propertyModel from "../models/properties.js"
import mongoose from "mongoose";
import otaPropertiesModel from '../models/otaProperties.js';
import fs from 'fs'
import currencyRates from '../utills/currencyRate.js';
import wishlists from '../models/wishLists.js';
import bookingModel from '../models/bookings.js';
import RoomInventory from '../models/ARI.js'
import { Console } from 'console';
import RatePlan from '../models/roomRatePlans.js';
import ARI from '../models/ARI.js';
import avability from '../models/avability.js';
export const createHostel = async (data) => {
    return await Hostel.create(data);
};
const updateApprovalStatus = async (criteria) => {
    const { country, state, propertyId } = criteria;
    let updateQuery = {};

    if (propertyId) {
        updateQuery._id = propertyId;
    } else if (state) {
        updateQuery.state = state;
    } else if (country) {
        updateQuery.country = country;
    }

    const updateResult = await otaPropertiesModel.updateMany(updateQuery, { isApproved: true });
    return updateResult;
};

const escapeRegex = (text) => {
    return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

const listAllOtaProperties = async (page, limit, sortCondition, filter, userId) => {
    try {
        page = parseInt(page, 10) || 1;
        limit = parseInt(limit, 10) || 10;

        // Sorting
        const sort = {};
        if (sortCondition === 'lowestPrice') {
            sort['lowestAveragePricePerNight'] = 1;
        } else if (sortCondition === 'highestPrice') {
            sort['lowestAveragePricePerNight.value'] = -1;
        } else {
            sort.isPropertyLive = -1;
        }

        // Base filters
        let queryFilter = { type: "HOSTEL" };
        queryFilter.$and = queryFilter.$and || [];

        // --- Search Filter ---
        if (filter.search) {
            const rawSearch = filter.search.trim();
            const safeSearch = escapeRegex(rawSearch);

            const searchTerms = rawSearch.split(/[,\s]+/).map(term => escapeRegex(term));

            const fullMatchCondition = {
                $or: [
                    { 'address.state': { $regex: `^${safeSearch}$`, $options: 'i' } },
                    { 'address.city': { $regex: `^${safeSearch}$`, $options: 'i' } },
                    { 'address.country': { $regex: `^${safeSearch}$`, $options: 'i' } },
                    { 'name': { $regex: `^${safeSearch}$`, $options: 'i' } }
                ]
            };


            const partialMatchConditions = searchTerms.map(term => ({
                $or: [
                    { 'address.state': { $regex: term, $options: 'i' } },
                    { 'address.country': { $regex: term, $options: 'i' } },
                    { 'name': { $regex: term, $options: 'i' } }
                ]
            }));

            // queryFilter.$and.push({
            //     $or: [fullMatchCondition, { $and: partialMatchConditions }]
            // });
            queryFilter.$and.push({
                $or: [fullMatchCondition]
            });
        }

        // --- Type Filter ---
        if (filter.type) {
            queryFilter.type = filter.type;
        }

        // --- Tag Filter ---
        if (filter.tag) {
            queryFilter.$and.push({
                $or: [
                    { tag: filter.tag },
                    { tag: { $exists: false } }
                ]
            });
        }

        // --- Country & Live Filter ---
        queryFilter.$and.push({
            $or: [
                { 'address.country': { $ne: 'India' } },
                {
                    $and: [
                        { 'address.country': 'India' },
                        { isPropertyLive: true }
                    ]
                }
            ]
        });

        // Pipeline
        const pipeline = [
            { $match: queryFilter },
            {
                $lookup: {
                    from: 'rooms',
                    localField: '_id',
                    foreignField: 'property',
                    as: 'rooms'
                }
            },
            {
                $addFields: {
                    tagMatched: {
                        $cond: [{ $eq: ["$tag", filter.tag] }, 1, 0]
                    }
                }
            },
            { $project: { photos: 0, "rooms.ezeeId": 0, "rooms.channelManager": 0, "rooms.cloudbeds": 0 } },
            { $sort: { ...sort, tagMatched: -1 } },
            { $skip: (page - 1) * limit },
            { $limit: limit }
        ];

        const properties = await propertyModel.aggregate(pipeline);

        // Wishlist flag
        if (properties && properties.length && userId) {
            for (const property of properties) {
                const liked = await wishlists.findOne({
                    user: userId,
                    property: property._id
                });
                property.liked = !!liked;
            }
        }

        // --- Currency conversion ---
        try {
            if (filter.currency) {
                const conversionRates = await getConversionRates(filter.currency);
                properties.forEach(property => {
                    convertCurrency(property, filter.currency, conversionRates);
                    property.rooms.forEach(room => {
                        convertCurrency(room, filter.currency, conversionRates);
                    });
                });
            }
        } catch (err) {
            console.log("Error in currency conversion:", err);
        }

        // --- Count total ---
        const countPipeline = [
            { $match: queryFilter },
            { $count: "total" }
        ];
        const totalPropertiesArray = await propertyModel.aggregate(countPipeline);
        const totalProperties = totalPropertiesArray[0]?.total || 0;
        const totalPages = Math.ceil(totalProperties / limit);

        return {
            properties,
            pagination: {
                page,
                limit,
                totalPages,
                totalProperties
            }
        };
    } catch (error) {
        console.error("Error listing properties:", error.message);
        return {
            properties: [],
            pagination: {
                page: 1,
                limit: 0,
                totalPages: 0,
                totalProperties: 0
            }
        };
    }
};


const listOtaPropertyAllRooms = async (id, currencyRatesfilter) => {
    const checkIn = new Date(currencyRatesfilter.checkIn);
    const checkOut = new Date(currencyRatesfilter.checkOut);
    const targetCurrency = currencyRatesfilter.currency || 'INR';

    // Rates map for currency conversion
    const ratesMap = currencyRatesfilter.rates && Object.keys(currencyRatesfilter.rates).length
        ? currencyRatesfilter.rates
        : currencyRates.rates;

    const otaProperty = await propertyModel.findOne({ _id: new mongoose.Types.ObjectId(id) });
    const rooms = await roomModel.find({ property: id, isDeleted: false }).lean();
    const ratePlans = await RatePlan.find({ property: id }).lean();
    const ratePlanMap = new Map(ratePlans.map(rp => [String(rp.ratePlan), rp.name]));

    // Extract only numeric roomIds
    const roomIds = new Set();
    rooms.forEach(r => {
        if (r.roomId) roomIds.add(r.roomId.toString());
    });
    const roomIdArray = Array.from(roomIds).map(id => Number(id));

    const ariInventories = await RoomInventory.find({
        roomId: { $in: roomIdArray },
        date: { $gte: checkIn, $lte: checkOut }
    }).lean();

    // Build ARI Map: roomId → date → array of ratePlan entries
    const ariMap = new Map();
    for (const inv of ariInventories) {
        const dateStr = inv.date.toISOString().split("T")[0];
        const key = inv.roomId.toString();
        if (!ariMap.has(key)) ariMap.set(key, new Map());
        const dateMap = ariMap.get(key);
        if (!dateMap.has(dateStr)) dateMap.set(dateStr, []);
        dateMap.get(dateStr).push(inv);
    }

    const updatedRooms = [];
    for (const room of rooms) {
        const latestAvailability = await avability.find({
            roomId: room?.roomId,
            date: { $gte: checkIn, $lte: checkOut }
        }).sort({ date: -1 });

        let minAvailable = 0;
        if (latestAvailability?.length > 0) {
            minAvailable = Math.min(...latestAvailability.map(r => r.availableUnits));
        }

        const ratePlansById = {};
        const roomKey = String(room.roomId);
        const roomARI = ariMap.get(roomKey);

        if (roomARI) {
            const dateCursor = new Date(checkIn);

            while (dateCursor <= checkOut) {
                const dateStr = dateCursor.toISOString().split("T")[0];
                const dayOfWeek = dateCursor.getDay(); // 0 = Sunday, 6 = Saturday

                if (roomARI.has(dateStr)) {
                    const invList = roomARI.get(dateStr);

                    for (const inv of invList) {
                        let ratePlanKey;
                        let ratePlanName;

                        // ✅ Currency conversion if needed
                        let price = inv.rate || 0;
                        if (inv.currency && inv.currency !== targetCurrency) {
                            const fromRate = ratesMap[inv.currency];
                            const toRate = ratesMap[targetCurrency];
                            if (fromRate && toRate) {
                                // convert base price from inv.currency to targetCurrency
                                price = (price / fromRate) * toRate;
                            }
                        }

                        if (inv.ratePlan && !isNaN(inv.ratePlan)) {
                            ratePlanKey = inv.ratePlan.toString();
                            ratePlanName = ratePlanMap.get(ratePlanKey) || `Plan ${ratePlanKey}`;
                        } else {
                            ratePlanKey = "standard";
                            ratePlanName = "Standard";
                        }

                        if (!ratePlansById[ratePlanKey]) {
                            ratePlansById[ratePlanKey] = {
                                name: ratePlanName,
                                values: [],
                                weekday: [],
                                weekend: [],
                                updatedAt: []
                            };
                        }

                        ratePlansById[ratePlanKey].values.push(price);

                        if (dayOfWeek === 0 || dayOfWeek === 6) {
                            ratePlansById[ratePlanKey].weekend.push({ value: price });
                        } else {
                            ratePlansById[ratePlanKey].weekday.push({ value: price });
                        }

                        if (inv.updatedAt) {
                            ratePlansById[ratePlanKey].updatedAt.push(inv.updatedAt);
                        }

                        if (typeof inv.available === 'number') {
                            minAvailable = Math.min(minAvailable, inv.available);
                        }
                    }
                }

                dateCursor.setDate(dateCursor.getDate() + 1);
            }
        }

        // If property is not live, show 0 availability
        if (process.env.IS_LIVE && !otaProperty?.isPropertyLive) {
            minAvailable = 0;
        }

        // Build rateArray
        const rateArray = await Promise.all(
            Object.entries(ratePlansById).map(async ([ratePlanKey, data]) => {
                const avg = data.values.length
                    ? Math.min(...data.values)
                    : 0;

                if (ratePlanKey === "standard") {
                    return {
                        ratePlanId: null,
                        ratePlanName: data.name,
                        averagePrice: parseFloat(avg.toFixed(2))
                    };
                }

                const parsedRatePlanId = parseInt(ratePlanKey);
                const ratePlanDoc = await RatePlan.findOne({ otaRateId: parsedRatePlanId });

                return {
                    ratePlanId: parsedRatePlanId,
                    ratePlanName: ratePlanDoc?.name || data.name || 'Unnamed Plan',
                    averagePrice: parseFloat(avg.toFixed(2))
                };
            })
        );

        updatedRooms.push({
            ...room,
            currency: targetCurrency,
            maxUnits: minAvailable,
            rates: rateArray.filter(Boolean)
        });
    }

    return {
        property: otaProperty,
        rooms: updatedRooms
    };
};




// const listOtaPropertyAllRooms = async (id, currencyRatesfilter) => {
//   const checkIn = new Date(currencyRatesfilter.checkIn);
//   const checkOut = new Date(currencyRatesfilter.checkOut);

//   const otaProperty = await propertyModel.findOne({ _id: new mongoose.Types.ObjectId(id) });

//   const rooms = await roomModel.find({ property: id, isDeleted: false }).lean();

//   const roomIds = rooms.map(r => r.otaRoomId || r.roomId || r._id.toString());

//   const ariInventories = await RoomInventory.find({
//     roomId: { $in: roomIds },
//     date: { $gte: checkIn, $lt: checkOut }
//   }).lean();

//   // build lookup
//   const ariMap = new Map();
//   for (const inv of ariInventories) {
//     const dateStr = inv.date.toISOString().split("T")[0];
//     if (!ariMap.has(inv.roomId)) {
//       ariMap.set(inv.roomId, new Map());
//     }
//     ariMap.get(inv.roomId).set(dateStr, inv.availableUnits);
//   }

//   const updatedRooms = [];

//   for (const room of rooms) {
//     const roomCapacity = room.units || room.capacity || 0;
//     let minAvailable = roomCapacity;

//     // collect all ARI availableUnits in the range
//     const ariAvailableUnitsList = [];

//     const dateCursor = new Date(checkIn);
//     while (dateCursor < checkOut) {
//       const dateStr = dateCursor.toISOString().split("T")[0];
//       const roomAri = ariMap.get(room.otaRoomId || room.roomId || room._id.toString());
//       if (roomAri && roomAri.has(dateStr)) {
//         ariAvailableUnitsList.push(roomAri.get(dateStr));
//       }
//       dateCursor.setDate(dateCursor.getDate() + 1);
//     }

//     // decide minAvailable
//     if (ariAvailableUnitsList.length > 0) {
//       minAvailable = Math.min(...ariAvailableUnitsList);
//     } else {
//       minAvailable = roomCapacity;
//     }

//     if (process.env.IS_LIVE && !otaProperty?.isPropertyLive) {
//       minAvailable = 0;
//     }

//     updatedRooms.push({
//       ...room,
//       maxUnits: minAvailable
//     });
//   }

//   // currency conversion
//   if (currencyRatesfilter.currency) {
//     const conversionRates = await getConversionRates(currencyRatesfilter.currency);
//     convertCurrencyInPropertyDetails(otaProperty, currencyRatesfilter.currency, conversionRates);
//     updatedRooms.forEach(room => {
//       convertCurrencyInPropertyDetails(room, currencyRatesfilter.currency, conversionRates);
//     });
//   }

//   return {
//     property: otaProperty,
//     rooms: updatedRooms
//   };
// };

const getOtaPropertiesByState = async (search, state, page, limit) => {
    const searchQuery = search ? { name: { $regex: search, $options: 'i' }, state: state } : { state: state };
    const skip = (page - 1) * limit;

    const properties = await otaPropertiesModel.find(searchQuery).skip(skip).limit(limit).exec();
    const totalProperties = await otaPropertiesModel.countDocuments(searchQuery);

    return { properties, totalProperties };
};

const getCountriesAndStates = async () => {
    const countriesAndStates = await otaPropertiesModel.aggregate([
        {
            $group: {
                _id: "$country",
                states: { $addToSet: "$state" }
            }
        },
        {
            $project: {
                _id: 0,
                country: "$_id",
                states: 1
            }
        },
        {
            $sort: { country: 1 }
        }
    ]).allowDiskUse(true); // Handle large aggregation results

    return countriesAndStates;
};
export const getConversionRates = async (targetCurrency = 'INR', baseCurrency = 'EUR') => {
    try {
        // Check if the base currency matches the base currency in your rates data
        if (currencyRates.base !== baseCurrency) {
            throw new Error(`Base currency mismatch. Expected ${currencyRates.base} but got ${baseCurrency}`);
        }

        // Get the rate for the target currency
        const rate = currencyRates.rates[targetCurrency];

        // Check if the target currency rate exists in the JSON data
        if (!rate) {
            throw new Error(`Target currency ${targetCurrency} not found`);
        }

        return rate;

    } catch (error) {
        console.error("Error fetching conversion rates from JSON file:", error.message);
        throw new Error("Could not fetch conversion rates");
    }
};
export const convertCurrency = (entity, targetCurrency, conversionRate) => {

    // Convert lowestAveragePricePerNight
    if (entity.lowestAveragePricePerNight && entity.lowestAveragePricePerNight.value) {
        const sourceCurrency = entity.lowestAveragePricePerNight.currency;
        if (conversionRate && sourceCurrency !== targetCurrency) {
            entity.lowestAveragePricePerNight = {
                value: (parseFloat(entity.lowestAveragePricePerNight.value) * conversionRate).toFixed(2),
                currency: targetCurrency
            };
        }
    }

    // Convert room-level prices (if applicable)
    if (entity.rate) {
        const sourceCurrency = entity.currency;
        if (conversionRate && sourceCurrency !== targetCurrency) {
            if (entity.rate.weekdayRate && entity.rate.weekdayRate.value) {
                entity.rate.weekdayRate.value = (parseFloat(entity.rate.weekdayRate.value) * conversionRate).toFixed(2);
            }
            if (entity.rate.weekendRate && entity.rate.weekendRate.value) {
                entity.rate.weekendRate.value = (parseFloat(entity.rate.weekendRate.value) * conversionRate).toFixed(2);
            }
            entity.currency = targetCurrency;
        }
    }

    return JSON.parse(JSON.stringify(entity)); // Breaks any circular references
};


const convertCurrencyInPropertyDetails = (entity, targetCurrency, conversionRate) => {
    if (!conversionRate) return; // Exit if no conversion rate is provided

    // Convert property-level prices if they exist
    if (entity.rate && entity.rate.value && entity.rate.currency) {
        const sourceCurrency = entity.rate.currency;

        // Convert only if source currency is different from target currency
        if (sourceCurrency !== targetCurrency) {
            entity.rate.value = (parseFloat(entity.rate.value) * conversionRate).toFixed(2);
            entity.rate.currency = targetCurrency;
        }
    }
    // Convert room-level prices if they exist
    if (entity.rate && entity.rate.weekdayRate) {
        if (entity.rate.weekdayRate.value) {
            entity.rate.weekdayRate.value = (
                parseFloat(entity.rate.weekdayRate.value) * conversionRate
            ).toFixed(2);
        }

        // Convert weekendRate
        if (entity.rate.weekendRate.value) {
            entity.rate.weekendRate.value = (
                parseFloat(entity.rate.weekendRate.value) * conversionRate
            ).toFixed(2);
        }
        if (entity.rate.averagePrice) {
            entity.rate.averagePrice.value = (
                parseFloat(entity.rate.averagePrice.value) * conversionRate
            ).toFixed(2);
        }
        entity.currency = targetCurrency;
    } else {
        // Handle case where rate does not exist, if needed
        console.log('Rate field does not exist in the entity.');
    }

};


export const listPropertyNameSearch = async (name) => {
    return await otaPropertiesModel.find({ name: { $regex: name, $options: 'i' } }, { name: 1 })
};

export { updateApprovalStatus, listAllOtaProperties, listOtaPropertyAllRooms, getOtaPropertiesByState, getCountriesAndStates };