import Image from "next/image";
import React, { useState } from "react";
import { motion } from "framer-motion";

const MembershipPage = () => {

  const [imgLoaded, setImgLoaded] = useState({
    mockup1: false,
    mockup2: false,
    membershipAvatar: false,
  });

  const [imgFailed, setImgFailed] = useState({
    mockup1: false,
    mockup2: false,
    membershipAvatar: false,
  });

  const ImgSkeleton = ({ className = '' }) => (
    <div className={`bg-gray-300 animate-pulse rounded ${className}`} />
  );

  const [featureImgLoaded, setFeatureImgLoaded] = useState(Array(4).fill(false));

  return (
    <div className="flex-1 md:px-4 lg:px-6 xl:px-6 py-4">
      <motion.h1
        className="text-2xl font-bold mb-1"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: [20, -10, 0] }}
        transition={{
          duration: 1.5,
          ease: "easeOut",
          bounce: 0.4,
        }}
        viewport={{ amount: 0.5 }} // triggers every time it's half visible
      >
        Unlock the Ultimate Travel Experience with{" "}
        <span className="text-primary-blue">Mix Premium!</span>
      </motion.h1>

      <motion.p
        className="mb-6 text-base"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: [20, -8, 0] }}
        transition={{
          duration: 1.8,
          ease: "easeOut",
          delay: 0.2,
          bounce: 0.4,
        }}
        viewport={{ amount: 0.5 }}
      >
        Access exclusive features and make your journey smarter and smoother.
      </motion.p>

      {/* Promo Banner */}
      <div
        className=" rounded-2xl px-6 flex justify-between items-center mb-8 py-6 lg:py-0  bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url(${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/membership-bg.jpeg)`,
        }}
      >
        <div className="flex flex-col gap-y-0 lg:gap-y-10 xl:gap-y-20">
          <div>
            <motion.h2
              className="text-xl xl:text-3xl font-semibold mb-2"
              initial={{ scale: 0.95, opacity: 0 }}
              whileInView={{ scale: [0.95, 1.05, 1], opacity: 1 }}
              transition={{
                duration: 0.8,
                ease: "easeOut",
                bounce: 0.4,
              }}
              viewport={{ amount: 0.5 }} // removed "once: true"
            >
              Unlock the Real Hostel <br className="hidden lg:flex" />{" "}
              Experience with Mix <br className="hidden lg:flex" /> Membership
            </motion.h2>

            <motion.p
              className="text-gray-500 mb-4 text-sm xl:text-base"
              initial={{ scale: 0.95, opacity: 0 }}
              whileInView={{ scale: [0.95, 1.05, 1], opacity: 1 }}
              transition={{
                duration: 0.8,
                ease: "easeOut",
                delay: 0.2,
                bounce: 0.4,
              }}
              viewport={{ amount: 0.5 }} // triggers every time in view
            >
              Match. Split. Ride. Party. All from your phone.
            </motion.p>
          </div>
          <div className="flex gap-2 flex-wrap">
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/google-play.png`}
              alt="Google Play"
              className="lg:h-14 h-12 w-auto xl:w-44"
              width={120}
              height={40}
            />
            {/* <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/app-store.png`}
              alt="App Store"
              className="lg:h-14 h-12 w-auto xl:w-44"
              width={120}
              height={40}
            /> */}
          </div>
        </div>
        <motion.div
          className="hidden lg:flex w-[52%] xl:w-[48%] relative pt-4 pr-0.5"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 1, ease: "easeOut" }}
          viewport={{ amount: 0.5 }}
        >
          {/* First image: Zoom out then zoom in + slight rotate */}
          <motion.div
            initial={{ scale: 1.2, rotate: -8, opacity: 0 }}
            whileInView={{ scale: 1, rotate: 0, opacity: 1 }}
            transition={{ duration: 1.4, ease: "easeOut" }}
            viewport={{ amount: 0.5 }}
            className="z-20"
          >
            {!imgLoaded.mockup1 && !imgFailed.mockup1 && (
              <ImgSkeleton className="absolute inset-0 h-full w-full rounded-xl" />
            )}
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mockup1.png`}
              alt="App Mockup"
              className={`h-[310px] xl:h-[350px] w-[250px] xl:w-[270px] transition-opacity duration-300 ${
                imgLoaded.mockup1 ? 'opacity-100' : 'opacity-0'
              }`}
              width={230}
              height={370}
              onLoad={(e) => {
                if (e?.target?.complete && e?.target?.naturalWidth > 0) {
                  setImgLoaded(prev => ({ ...prev, mockup1: true }));
                }
              }}
              onError={() => {
                setImgFailed(prev => ({ ...prev, mockup1: true }));
                setImgLoaded(prev => ({ ...prev, mockup1: false }));
              }}
            />
          </motion.div>

          {/* Second image: Zoom out then zoom in  */}
          <motion.div
            initial={{ scale: 1.2, x: 80, opacity: 0 }}
            whileInView={{
              scale: 1,
              x: 0,
              opacity: 1,
            }}
            transition={{ duration: 1.6, ease: "easeOut", delay: 0.3 }}
            viewport={{ amount: 0.5 }}
            className="z-10 absolute left-36 top-[66px]"
          >
            {!imgLoaded.mockup2 && !imgFailed.mockup2 && (
              <ImgSkeleton className="absolute inset-0 h-full w-full rounded-xl" />
            )}
            <Image
              src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mockup2.png`}
              alt="App Mockup"
              className={`h-[260px] xl:h-[300px] w-[250px] xl:w-[280px] transition-opacity duration-300 ${
                imgLoaded.mockup2 ? 'opacity-100' : 'opacity-0'
              }`}
              width={230}
              height={320}
              onLoad={(e) => {
                if (e?.target?.complete && e?.target?.naturalWidth > 0) {
                  setImgLoaded(prev => ({ ...prev, mockup2: true }));
                }
              }}
              onError={() => {
                setImgFailed(prev => ({ ...prev, mockup2: true }));
                setImgLoaded(prev => ({ ...prev, mockup2: false }));
              }}
            />
          </motion.div>
        </motion.div>
      </div>

      {/* Feature List */}
      <h3 className="text-[22px] font-manrope font-bold my-4">Feature List</h3>
      <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 xs:gap-4 gap-3">
        {[
          {
            title: "MixMate (Travel Tinder)",
            desc: "Match with travelers in your hostel",
            tag: "Only On Mobile App",
            color: "bg-pink-100",
            icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/MixMate.png`,
          },
          {
            title: "MixSplit (Split Bills)",
            desc: "Split rides, meals, and rooms with hostel buddies",
            tag: "Mobile Exclusive",
            color: "bg-yellow-100",
            icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/MixSplit.png`,
          },
          {
            title: "MixEvent (Join Hostel Events)",
            desc: "See events in your hostel or city",
            tag: "Download App to View",
            color: "bg-pink-200",
            icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/MixEvent.png`,
          },
          {
            title: "MixRide (Share Rides)",
            desc: "Get or share rides to nearby spots",
            tag: "Unlock in App",
            color: "bg-purple-100",
            icon: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/MixRide.png`,
          },
        ].map((feature, idx) => (
          <div
            key={idx}
            className="border rounded-lg xs:p-4 p-3 bg-white shadow-sm"
          >
            <div>
              {" "}
              <motion.div
                whileHover={{
                  scale: 1.1,
                  rotate: 3,
                  boxShadow: "0px 0px 15px rgba(64, 224 , 208, 1)",
                }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
                className="w-14 h-14 rounded-full"
              >
                {!featureImgLoaded[idx] && (
                  <ImgSkeleton className="w-14 h-14 rounded-full" />
                )}
                <Image
                  src={feature.icon}
                  alt={feature.title}
                  width={50}
                  height={50}
                  className={`w-14 h-14 object-contain ${featureImgLoaded[idx] ? '' : 'hidden'}`}
                  onLoad={() =>
                    setFeatureImgLoaded(prev => {
                      const updated = [...prev];
                      updated[idx] = true;
                      return updated;
                    })
                  }
                />

              </motion.div>
              <h4 className="font-semibold xs:text-base text-sm lg:text-sm xl:text-base font-manrope h-16 py-4">
                {feature.title}
              </h4>
              <p className=" text-gray-600 text-sm lg:text-xs xl:text-sm font-manrope h-16">
                {feature.desc}
              </p>
            </div>
            <div>
              <motion.span
                whileHover={{
                  x: 5,
                  opacity: 0.9,
                }}
                transition={{
                  type: "spring",
                  stiffness: 250,
                  damping: 15,
                }}
                className={`px-1.5 py-1 xs:text-sm text-xs lg:text-xxs xl:text-[15px] sm:h-[50px] h-[42px] border rounded-full flex items-center justify-center whitespace-nowrap
                ${
                  idx < 2
                    ? "bg-white text-black border-black hover:text-white hover:bg-black"
                    : "bg-black text-white border-black hover:text-black hover:bg-white"
                }`}
              >
                {feature.tag}
              </motion.span>
            </div>
          </div>
        ))}
      </div>

      {/* Footer section */}
          {/* <div className="overflow-x-hidden">

      <motion.p
        className="md:text-[22px] text-lg font-manrope font-bold my-6"
        initial={{ x: -60, rotate: -1, opacity: 0.6 }}
        whileInView={{ x: 0, rotate: 0, opacity: 1 }}
        viewport={{ once: false, amount: 0.7 }}
        transition={{ duration: 1.1, ease: [0.6, 0.05, 0.01, 0.99] }}
      >
        Collecting from Hostel Tag
      </motion.p>

  
      <motion.div
          initial={{ x: 60, rotate: 1.5, opacity: 0.6 }}
        whileInView={{ x: 0, rotate: 0, opacity: 1 }}
        whileHover={{ scale: 1.01 }}
        whileTap={{ scale: 0.985 }}
        viewport={{ once: false, amount: 0.6 }}
        transition={{ duration: 1.3, ease: [0.7, 0, 0.3, 1] }}
        className="inline-block"
      >
        <Image
          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/membershipAvatar.png`}
          alt="App Mockup"
          className="sm:w-[420px] sm:h-[288px] w-[300px]"
          width={230}
          height={320}
        />
      </motion.div>
    </div> */}
    </div>
  );
};

export default MembershipPage;
