import React, { useState, useEffect, useRef, useCallback } from "react";
import { AddEventApi } from "@/services/hostemproviderservices";
import toast from "react-hot-toast";
import axios from "axios";
import { Autocomplete } from "@react-google-maps/api";
import dynamic from "next/dynamic";
import Image from "next/image";
import CustomSelect from "@/components/common/CustomDropdown";
import { CloudUpload, Minus, Plus } from "lucide-react";
import { BASE_URL } from "@/utils/api";
import { format, isToday } from "date-fns";

const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

const googleMapsApiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

// const DateInputWithPlaceholder = ({ placeholder, value, onChange }) => {
//   const [focused, setFocused] = useState(false);
//   const handleInputClick = (e) => {
//     if (e.target.type === "date") {
//       e.target.showPicker();
//     }
//   };
//   return (
//     <div className='relative'>
//       <input
//         type='date'
//         className={`block w-full border border-black/50 rounded-lg sm:pr-0 pr-2 sm:py-2.5 py-2 sm:pl-8 pl-5 shadow-sm appearance-none cursor-pointer focus:outline-none focus:ring-1 focus:ring-teal-500 sm:text-sm text-xs`}
//         value={value}
//         onChange={onChange}
//         onFocus={() => setFocused(true)}
//         onBlur={() => setFocused(false)}
//         style={{ color: value ? "inherit" : "transparent", outline: "none" }}
//         onClick={handleInputClick}
//       />
//       {!value && !focused && (
//         <span className='absolute left-10 sm:top-3 top-2 text-sm text-[#00000080] pointer-events-none'>
//           {placeholder}
//         </span>
//       )}
//     </div>
//   );
// };

const Event = ({ closeeventModal, updateEventList }) => {
  const [formData, setFormData] = useState({
    title: "",
    startDate: "",
    duration: "",
    durationType: "days", // Selected duration type
    numberOfUnits: "", // Number of days/weeks
    hours: "", // Selected hours
    minutes: "", // Selected minutes
    price: "",
    address: "",
    location: "",
    attachment: null,
    description: "",
    refundable: false,
    nonRefundable: false,
    cancellation: false,
    currency: "",
    photos: [],
  });
  // eslint-disable-next-line no-unused-vars
  const [currencies, setCurrencies] = useState([]);
  const isFirstRender = useRef(null);
  const [autocomplete, setAutocomplete] = useState(null);
  const [isApiLoaded, setIsApiLoaded] = useState(false);
  const [count, setCount] = useState(1);
  const [count1, setCount1] = useState(1);
  const [isLoading, setIsLoading] = useState(false);

  const [showCalendar, setShowCalendar] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const calendarRef = useRef(null);
  const inputRef = useRef(null);

  const handlePrevMonth = () => {
    setCurrentMonth(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() - 1, 1)
    );
  };

  const handleNextMonth = () => {
    setCurrentMonth(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() + 1, 1)
    );
  };

  // Generate days for current month
  function generateDays(month) {
    const daysInMonth = new Date(
      month.getFullYear(),
      month.getMonth() + 1,
      0
    ).getDate();
    const firstDayIndex = new Date(
      month.getFullYear(),
      month.getMonth(),
      1
    ).getDay();

    const days = [];

    // Add blank spaces for days of previous month
    for (let i = 0; i < firstDayIndex; i++) {
      days.push(null);
    }

    // Add days of the current month
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(new Date(month.getFullYear(), month.getMonth(), i));
    }

    return days;
  }

  // Close calendar if clicked outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target) &&
        !inputRef.current.contains(event.target)
      ) {
        setShowCalendar(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handlePhotoChange = async (e) => {
    const { files } = e.target;

    if (files.length === 0) {
      toast.error("At least one file is required");
      return;
    }
    const allowedTypes = ["image/jpeg", "image/png", "image/jpg"];

    // Check if all selected files are of allowed types
    const invalidFiles = Array.from(files).filter(
      (file) => !allowedTypes.includes(file.type)
    );

    if (invalidFiles.length > 0) {
      toast.error("Only JPG, JPEG, and PNG files are allowed.");
      return;
    }

    setIsLoading(true);

    try {
      // Loop through all selected files
      const uploadedImages = await Promise.all(
        Array.from(files).map(async (file) => {
          const formData = new FormData();
          formData.append("files", file);

          // Get presigned URL for each file
          const presignedUrlResponse = await fetch(
            `${BASE_URL}/fileUpload/generate-presigned-url`,
            {
              method: "POST",
              body: formData,
            }
          );

          if (!presignedUrlResponse.ok) {
            throw new Error("Failed to get presigned URL");
          }

          const presignedUrlData = await presignedUrlResponse.json();
          // const { objectURL } = presignedUrlData.data;
          const objectURL = Array.isArray(presignedUrlData.data) && presignedUrlData.data[0]?.path;

          return { title: "Attachment", url: objectURL };
        })
      );

      // Update state with new uploaded image URLs
      setFormData((prevState) => ({
        ...prevState,
        photos: [...(prevState?.photos || []), ...uploadedImages],
      }));

      toast.success("Files uploaded successfully.");
    } catch (error) {
      console.error("Error uploading files:", error);
      toast.error("Error uploading files.");
    } finally {
      setIsLoading(false);
    }
  };

  const removeAttachmentImage = (indexToRemove) => {
    setFormData((prevState) => ({
      ...prevState,
      photos: prevState.photos.filter((_, index) => index !== indexToRemove),
    }));
  };

  const loadGoogleMapsApi = () => {
    const script = document.createElement("script");
    script.src = `https://maps.googleapis.com/maps/api/js?key=${googleMapsApiKey}&libraries=places`;
    script.async = true;
    script.onload = () => setIsApiLoaded(true);
    script.onerror = () => setIsApiLoaded(false);
    document.head.appendChild(script);
  };

  useEffect(() => {
    if (window.google && window.google.maps) {
      setIsApiLoaded(true);
    } else {
      loadGoogleMapsApi();
    }
  }, []);

  useEffect(() => {
    if (!isFirstRender.current) {
      fetchCurrencies();
    } else {
      isFirstRender.current = false;
    }
  }, []);

  const fetchCurrencies = async () => {
    try {
      const response = await axios.get(
        "https://api.exchangerate-api.com/v4/latest/USD"
      );
      if (response.data?.rates) {
        setCurrencies(Object.keys(response.data.rates));
      }
    } catch (error) {
      console.error("Error fetching currencies:", error);
      toast.error("Failed to fetch currencies");
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked, files } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]:
        type === "checkbox" ? checked : type === "file" ? files[0] : value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const requiredFields = [
      "title",
      "startDate",
      "durationType",
      "hours",
      "minutes",
      "price",
      "address",
      "description",
      "photos",
    ];

    for (const field of requiredFields) {
      if (field === "hours" || field === "minutes") {
        if (formData[field] === undefined || formData[field] === null) {
          toast.error(`${field.replace(/([A-Z])/g, " $1")} is required`);
          return;
        }
      } else {
        if (!formData[field]) {
          toast.error(`${field.replace(/([A-Z])/g, " $1")} is required`);
          return;
        }
      }
    }
    const payload = {
      ...formData,
      location: {
        type: "Point",
        coordinates: [formData.longitude || 0, formData.latitude || 0],
      },
      duration: `${formData?.numberOfUnits} ${formData?.durationType}`,
    };
    const submitData = async () => {
      try {
        const res = await AddEventApi(payload);
        if (res?.status === 201) {
          toast.success(res?.data?.message);
          setFormData({
            title: "",
            startDate: "",
            duration: "",
            hours: 0,
            minutes: 0,
            price: 0,
            address: "",
            location: "",
            attachment: null,
            description: "",
            refundable: false,
            nonRefundable: false,
            cancellation: false,
            currency: "",
          });
          updateEventList();
          // onClose();
        }
      } catch (error) {
        console.error("Error:", error);
        toast.error("Failed to create event");
      }
    };

    if (formData.attachment) {
      const reader = new FileReader();
      reader.onloadend = async () => {
        payload.attachment = reader.result;
        await submitData();
      };
      reader.readAsDataURL(formData.attachment);
    } else {
      await submitData();
    }
  };

  const handleAddressChange = useCallback((place) => {
    if (!place || !place.geometry || !place.address_components) {
      toast.error("Invalid place selected.");
      return;
    }
    // Check if the place is valid
    if (place && place.geometry) {
      setFormData((prevData) => ({
        ...prevData,
        address: place.formatted_address || "", // Fallback to empty string
        latitude: place.geometry.location.lat(),
        longitude: place.geometry.location.lng(),
      }));
    } else {
      console.error("Invalid place selected:", place);
    }
  }, []);

  const handleIncrease = () => {
    setCount(count + 1);
    setFormData((prevState) => ({
      ...prevState,
      hours: count + 1,
    }));
  };
  const handleDecrease = () => {
    count > 1 && setCount(count - 1);
    setFormData((prevState) => ({
      ...prevState,
      hours: count - 1,
    }));
  };

  const handleIncrease1 = () => {
    setCount1(count1 + 1);
    setFormData((prevState) => ({
      ...prevState,
      minutes: count1 + 1,
    }));
  };
  const handleDecrease1 = () => {
    count1 > 1 && setCount1(count1 - 1);
    setFormData((prevState) => ({
      ...prevState,
      minutes: count1 - 1,
    }));
  };

  if (!isApiLoaded) {
    return <Loader open={true} />;
  }

  return (
    <>
      <Loader open={isLoading} />
      <section className="w-full">
        <div className="max-w-[780px] mx-auto pb-8">
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div>
              <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                Title
              </label>
              <input
                type="text"
                name="title"
                className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                placeholder="Enter title"
                value={formData.title}
                onChange={handleChange}
                style={{ outline: "none" }}
              />
            </div>
            <div className="grid lg:grid-cols-4 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Event Dates
                </label>
                <div className="datepicker relative">
                  {/* <Image
                    className="text-xl absolute left-[10px] top-[12px] sm:w-[20px] sm:h-[20px] w-[15px] h-[15px]"
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/calendar2.svg`}
                    width="20"
                    height="20"
                    // onClick={() => setCalendarOpen(true)}
                  ></Image> */}
                  {/* <DateInputWithPlaceholder
                    value={formData.startDate}
                    onChange={(e) =>
                      handleChange({
                        ...e,
                        target: { ...e.target, name: "startDate" },
                      })
                    }
                    placeholder='date'
                    className='pl-8'
                  /> */}
                  <input
                    type="text"
                    name="startDate"
                    id="startDate"
                    ref={inputRef}
                    value={formData?.startDate}
                    placeholder="mm/dd/yyyy"
                    readOnly
                    onClick={() => setShowCalendar(!showCalendar)}
                    className="block w-full p-2 px-4 py-3 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-800 placeholder:text-gray-400 cursor-pointer"
                  />

                  {showCalendar && (
                    <div
                      ref={calendarRef}
                      className="absolute top-full left-0 bg-white border border-black/50 rounded-lg shadow-lg px-4 py-2 z-50 w-[16rem] mt-0.5"
                    >
                      {/* Month navigation */}
                      <div className="flex items-center justify-between mb-4">
                        <button
                          onClick={handlePrevMonth}
                          className="text-lg font-bold px-2"
                        >
                          &#8592;
                        </button>

                        <span className="text-base font-semibold">
                          {format(currentMonth, "MMMM yyyy")}
                        </span>

                        <button
                          onClick={handleNextMonth}
                          className="text-lg font-bold px-2"
                        >
                          &#8594;
                        </button>
                      </div>

                      {/* Weekdays */}
                      <div className="grid grid-cols-7 gap-2 text-center text-sm font-semibold text-gray-600">
                        <div>Su</div>
                        <div>Mo</div>
                        <div>Tu</div>
                        <div>We</div>
                        <div>Th</div>
                        <div>Fr</div>
                        <div>Sa</div>
                      </div>

                      {/* Days */}
                      <div className="grid grid-cols-7 gap-1 mt-2 text-center text-sm">
                        {generateDays(currentMonth).map((day, index) =>
                          day ? (
                            <button
                              key={index}
                              className={`rounded-full p-2 text-sm flex items-center justify-center ${isToday(day) ? "border-2 border-primary-blue" : ""} ${
                                format(day, "MM-dd-yyyy") === formData.date
                                  ? "bg-primary-blue text-white"
                                  : "hover:bg-primary-blue hover:text-white"
                              }`}
                              onClick={() => {
                                handleChange({
                                  target: {
                                    name: "startDate",
                                    value: format(day, "MM-dd-yyyy"),
                                  },
                                });
                                setShowCalendar(false);
                              }}
                            >
                              {day.getDate()}
                            </button>
                          ) : (
                            <div key={index} />
                          )
                        )}
                      </div>
                    </div>
                  )}
                  <div className="absolute top-3.5 right-1 cursor-pointer w-4 h-4 bg-white"></div>
                </div>
              </div>
              <div>
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Duration
                </label>
                <CustomSelect
                  name="durationType"
                  options={[
                    { value: "", label: "Select Type" },
                    { value: "days", label: "Days" },
                    { value: "weeks", label: "Weeks" },
                  ]}
                  value={formData.durationType}
                  onChange={(selectedOption) =>
                    handleChange({
                      target: { name: "durationType", value: selectedOption },
                    })
                  }
                  placeholder="Day/Week"
                />
                {/* <select
                  name='durationType'
                  className='w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500'
                  value={formData.durationType}
                  onChange={handleChange}
                >
                  <option value=''>Select Type</option>
                  <option value='days'>Days</option>
                  <option value='weeks'>Weeks</option>
                </select> */}
              </div>

              {/* {["days", "weeks"].includes(formData.durationType) && (
                <div>
                  <label className='block text-black sm:text-sm text-xs font-medium mb-1.5'>
                    Enter Number of{" "}
                    {formData.durationType === "days" ? "Days" : "Weeks"}
                  </label>
                  <input
                    type='number'
                    name='numberOfUnits'
                    className='block w-full p-2 border border-gray-300 rounded'
                    value={formData.numberOfUnits}
                    onChange={handleChange}
                    min='1'
                  />
                </div>
              )} */}

              <div>
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Hours
                </label>
                <div className="relative col-span-2">
                  <div className="flex items-center w-full sm:px-4 px-2 py-0.5 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500">
                    {/* User Icon + Number */}
                    <div className="flex items-center gap-2 flex-grow">
                      <span className="text-black text-sm">{count}</span>
                    </div>

                    {/* Minus and Plus Buttons */}
                    <div className="flex items-center flex-col">
                      <button
                        onClick={handleIncrease}
                        className="text-gray-500 hover:text-black cursor-pointer"
                      >
                        <Plus size={18} />
                      </button>
                      <button
                        onClick={handleDecrease}
                        className="text-gray-500 hover:text-black cursor-pointer"
                      >
                        <Minus size={18} />
                      </button>
                    </div>
                  </div>
                </div>
                {/* <select
                  name='hours'
                  className='block w-full p-2 border border-gray-300 rounded'
                  value={formData.hours}
                  onChange={handleChange}
                >
                  <option value=''>Select Hours</option>
                  {[...Array(25).keys()].map((hour) => (
                    <option key={hour} value={hour}>
                      {hour}
                    </option>
                  ))}
                </select> */}
              </div>
              <div>
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Minutes
                </label>
                <div className="relative col-span-2">
                  <div className="flex items-center w-full sm:px-4 px-2 py-0.5 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500">
                    {/* User Icon + Number */}
                    <div className="flex items-center gap-2 flex-grow">
                      <span className="text-black text-sm">{count1}</span>
                    </div>

                    {/* Minus and Plus Buttons */}
                    <div className="flex items-center flex-col">
                      <button
                        onClick={handleIncrease1}
                        className="text-gray-500 hover:text-black cursor-pointer"
                      >
                        <Plus size={18} />
                      </button>
                      <button
                        onClick={handleDecrease1}
                        className="text-gray-500 hover:text-black cursor-pointer"
                      >
                        <Minus size={18} />
                      </button>
                    </div>
                  </div>
                </div>
                {/* <select
                  name='minutes'
                  className='block w-full p-2 border border-gray-300 rounded'
                  value={formData.minutes}
                  onChange={handleChange}
                >
                  <option value=''>Select Minutes</option>
                  {[...Array(61).keys()].map((minute) => (
                    <option key={minute} value={minute}>
                      {minute}
                    </option>
                  ))}
                </select> */}
              </div>
            </div>

            {/* <div>
            <label className='block text-black sm:text-sm text-xs font-medium mb-1.5'>
              Currency
            </label>
            <select
              name='currency'
              className='w-full px-3 py-4 border rounded-xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500'
              value={formData.currency}
              onChange={handleChange}
              style={{ outline: "none" }}
            >
              <option disabled value=''>
                Select Currency
              </option>
              {currencies.map((currency, index) => (
                <option key={index} value={currency}>
                  {currency}
                </option>
              ))}
            </select>
          </div> */}
            <div className="grid sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Price
                </label>
                <input
                  type="number"
                  name="price"
                  className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                  placeholder="Price Per Person, tax included"
                  value={formData.price}
                  onChange={handleChange}
                  style={{ outline: "none" }}
                />
              </div>
              <div className="relative">
                <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                  Location
                </label>
                <Autocomplete
                  onLoad={(autocompleteInstance) =>
                    setAutocomplete(autocompleteInstance)
                  }
                  onPlaceChanged={() => {
                    if (autocomplete) {
                      const place = autocomplete.getPlace();
                      if (!place || !place.geometry) {
                        toast.error("Invalid place selected.");
                        return;
                      }
                      handleAddressChange(place); // Call your address handling logic
                    } else {
                      toast.error("Autocomplete is not initialized yet.");
                    }
                  }}
                  className="w-full"
                >
                  <input
                    id="address"
                    name="address"
                    type="text"
                    placeholder="Add Location"
                    value={formData.address}
                    onChange={handleChange}
                    className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                  />
                </Autocomplete>
                <Image
                  className="absolute top-9 right-3"
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/location.svg`}
                  width={20}
                  height={20}
                />
              </div>
            </div>
            <div className="relative col-span-2">
              <label
                className="block text-black sm:text-sm text-xs font-medium mb-1.5 "
                htmlFor="username"
              >
                Add Photo
              </label>

              <input
                type="file"
                name="photos"
                multiple
                accept=".jpg, .jpeg, .png"
                className="z-10 cursor-pointer block w-full p-2 px-4 text-sm bg-transparent border rounded-lg opacity-0 border-gray-220 focus:outline-none text-slate-320 placeholder:text-gray-320 absolute top-0 left-0 right-0 bottom-0"
                onChange={handlePhotoChange}
                placeholder="Upload Photos"
              />
              <div className="w-full px-4 py-2 border border-[#40E0D0] rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500 flex items-center justify-between border-dashed bg-[#40E0D01A] cursor-pointer">
                Upload Photos
                <CloudUpload className="text-[#40E0D0]" size={22} />
              </div>
            </div>

            {formData?.photos?.length > 0 && (
              <div className="col-span-2">
                <div className="grid grid-cols-3 sm:gap-4 gap-3 mt-4">
                  {formData?.photos?.map((image, index) => (
                    <div
                      key={index}
                      className="relative flex items-center justify-between sm:px-4 px-2 sm:py-2.5 py-2 border border-[#40E0D0] border-dashed bg-[#40E0D01A] rounded-lg"
                    >
                      <Image 
                        // src={image?.url}
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/${image?.url}`}
                        alt={`Existing Image ${index + 1}`}
                        className="w-[54px] h-[36px] object-cover rounded-sm"
                        width={54}
                        height={36}
                      />
                      <span
                        className="text-black hover:text-red-500 font-black cursor-pointer text-xl"
                        onClick={() => removeAttachmentImage(index)}
                      >
                        &#10005;
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
            <div>
              <label className="block text-black sm:text-sm text-xs font-medium mb-1.5">
                Description
              </label>
              <textarea
                name="description"
                className="w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500"
                placeholder="Enter Description"
                rows={4}
                value={formData.description}
                onChange={handleChange}
                style={{ outline: "none" }}
              ></textarea>
            </div>
            <div className="flex items-center justify-start mt-4 sm:gap-x-3 gap-2 flex-wrap">
              <div
                className={`border rounded-full py-1.5 px-5 font-manrope font-semibold text-sm text-black-230 cursor-pointer ${
                  formData.refundable
                    ? "bg-[#40e0d033] border-[#40e0d033]"
                    : "bg-transparent"
                }`}
                onClick={() =>
                  setFormData({
                    ...formData,
                    refundable: !formData?.refundable,
                    nonRefundable: false,
                    cancellation: false,
                  })
                }
              >
                Refundable
              </div>
              <div
                className={`border rounded-full py-1.5 px-5 font-manrope font-semibold text-sm text-black-230 cursor-pointer ${
                  formData.nonRefundable
                    ? "bg-[#40e0d033] border-[#40e0d033]"
                    : "bg-transparent"
                }`}
                onClick={() =>
                  setFormData({
                    ...formData,
                    refundable: false,
                    nonRefundable: !formData?.nonRefundable,
                    cancellation: false,
                  })
                }
              >
                Non Refundable
              </div>
              <div
                className={`border rounded-full py-1.5 px-5 font-manrope font-semibold text-sm text-black-230 cursor-pointer ${
                  formData.cancellation
                    ? "bg-[#40e0d033] border-[#40e0d033]"
                    : "bg-transparent"
                }`}
                onClick={() =>
                  setFormData({
                    ...formData,
                    refundable: false,
                    nonRefundable: false,
                    cancellation: !formData?.cancellation,
                  })
                }
              >
                Cancellation
              </div>
            </div>

            <div className="xs:flex block  items-center justify-between w-full sm:my-14 my-7 gap-4 col-span-2 py-4 bg-white/80 sticky bottom-0 backdrop-blur-sm">
              <button
                type="button"
                className="hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 px-4 border-black rounded-lg w-full text-sm xs:mb-0 mb-2"
                onClick={closeeventModal}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm"
              >
                Create New Event
              </button>
            </div>
          </form>
        </div>
      </section>
    </>
  );
};

export default Event;
