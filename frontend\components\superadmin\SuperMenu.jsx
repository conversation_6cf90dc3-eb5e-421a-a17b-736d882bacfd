 
import { FaReg<PERSON><PERSON><PERSON><PERSON><PERSON>, FaRegUser } from "react-icons/fa6";
import { GiKeyCard, GiNotebook } from "react-icons/gi";
import {  PiSquaresFourBold } from "react-icons/pi";
import { FaHeadset } from "react-icons/fa";
import { LuBellPlus, LuCalendarDays } from "react-icons/lu";
import { TbBrandWechat } from "react-icons/tb";
import { MdOutlineRateReview } from "react-icons/md";
;


const SuperMenu = [
  {
    id: 1,
    name: "Dashboard",
    link: "/superadmin/dashboard/about",
    icon: <PiSquaresFourBold className="text-xl"/>,
  },
  {
    id: 2,
    name: "User",
    link: "/superadmin/dashboard/user",
    icon: <FaRegUser className="text-xl" />,
  },
  {
    id: 3,
    name: "Content",
    link: "/superadmin/dashboard/content",
    icon: <GiNotebook className="text-xl"/>,
  },
  {
    id: 4,
    name: "Booking",
    link: "/superadmin/dashboard/booking",
    icon: <LuCalendarDays className="text-xl" />,
  },
  {
    id: 5,
    name: "Hostel",
    link: "/superadmin/dashboard/hostel",
    icon: <GiKeyCard className="text-xl" />,
  },
  {
    id: 6,
    name: "Communication",
    link: "/superadmin/dashboard/communication",
    icon: <TbBrandWechat className="text-xl" />,
  },
  {
    id: 7,
    name: "Notification",
    link: "/superadmin/dashboard/notification",
    icon: <LuBellPlus className="text-xl" />,
  },
  {
    id: 8,
    name: "Feedback",
    link: "/superadmin/dashboard/feedback",
    icon: <MdOutlineRateReview className="text-xl" />,
  },
  {
    id: 9,
    name: "Support",
    link: "/superadmin/dashboard/support",
    icon: <FaHeadset className="text-xl" />,
  },
  {
    id: 10,
    name: "Profile",
    link: "/superadmin/dashboard/profile",
    icon: <FaRegCircleUser className="text-xl" />,
  },
//   {
//     id: 11,
//     name: "Theme",
//     link: "#",
//     icon: <BsFillCalendar2CheckFill className="text-xl" />,
//   },
//   {
//     id: 12,
//     name: "Setting",
//     link: "#",
//     icon: <PiCalendarStarFill className="text-xl" />,
//   },
//   {
//     id: 13,
//     name: "Logout",
//     link: "#",
//     icon: <GrSettingsOption className="text-xl" />,
//   },

];
export default SuperMenu;