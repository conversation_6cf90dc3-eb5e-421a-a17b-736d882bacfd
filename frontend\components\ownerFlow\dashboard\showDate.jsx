import React from "react";

const ShowDates = (props) => {
  // const months = [
  //   "January",
  //   "February",
  //   "March",
  //   "April",
  //   "May",
  //   "June",
  //   "July",
  //   "August",
  //   "September",
  //   "October",
  //   "November",
  //   "December",
  // ];

  // const generateDates = (year, month) => {
  //   const dates = [];
  //   const startDate = new Date(year, month, 1);
  //   const endDate = new Date(year, month + 1, 0);

  //   for (let d = startDate; d <= endDate; d.setDate(d.getDate() + 1)) {
  //     dates.push(new Date(d));
  //   }

  //   return dates;
  // };






  
  // const generateDates = (year, month) => {
  //   const dates = [];
  //   const today = new Date();
  //   const isCurrentMonth =
  //     today.getFullYear() === year && today.getMonth() === month;

  //   const startDate = isCurrentMonth
  //     ? new Date(today) // start from today
  //     : new Date(year, month, 1); // otherwise from 1st

  //   const endDate = new Date(year, month + 1, 0); // end of month

  //   for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
  //     dates.push(new Date(d));
  //   }

  //   return dates;
  // };

  // const allDates = generateDates(props.currentYear, props.currentMonth);

  // const formatDateAndDay = (date) => {
  //   return `${date
  //     .toLocaleDateString("en-US", { weekday: "short" })
  //     .toUpperCase()} ${date.getDate()}`;
  // };

    const generateDates = (year, month) => {
    const dates = [];
    const today = new Date();
    const isCurrentMonth = today.getFullYear() === year && today.getMonth() === month;

    // Start from today if current month, otherwise from 1st of month
    const startDate = isCurrentMonth
      ? new Date(today.getFullYear(), today.getMonth(), today.getDate())
      : new Date(year, month, 1);
    
    const endDate = new Date(year, month + 1, 0); // Last day of month

    // Create a new Date object for iteration to avoid modifying startDate
    let currentDate = new Date(startDate);
    
    while (currentDate <= endDate) {
      dates.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dates;
  };

  const allDates = generateDates(props.currentYear, props.currentMonth);

  const formatDateAndDay = (date) => {
    return `${date
      .toLocaleDateString("en-US", { weekday: "short" })
      .toUpperCase()} ${date.getDate()}`;
  };

  return (
    <div className="py-4 px-2 xxxl:px-1 pl-4 ">
      {/* <h1 className="text-xs font-medium text-black mb-4">
        {months[props.currentMonth]} {props.currentYear}
      </h1> */}

      {/* Render dates dynamically */}
      <div className="flex items-center justify-center sm:gap-6 gap-6 xxxl:gap-[17px] ">
        {allDates.map((date, index) => (
          <div
            key={index}
            className={`p-2 border rounded-lg text-center bg-white min-w-[65px] w-max flex items-center justify-center flex-col cursor-pointer `}>
            {/* <div className="registers py-0.5 px-2.5 text-sm font-medium rounded-xl !bg-gray-300 font-inter ">
              0
            </div> */}
            <span className="text-xs font-medium flex mt-1 font-inter">
              {formatDateAndDay(date)}
            </span>
            {/* <div className="text-xs font-inter font-medium flex items-center justify-center w-full">4%</div> */}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ShowDates;
