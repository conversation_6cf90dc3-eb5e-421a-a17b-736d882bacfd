import express from 'express';
import {
    savePageDataController,
    listRecentSearchesController, listTopProperties, listTopHostelsController,
    listTopHostelsByCountry, getTravelActivities,subscribeNewsletterController,autoCompleteSearch,getPropertiesCounts,
    getFilterdProperty,
    getCategoryWiseProperty,
    listStatesByCountry,
    listTopHostels
} from '../controller/page.js';
import { checkAuth } from '../middleware/auth.js';

const router = express.Router();

router.post('/save', checkAuth('add_page'), savePageDataController);
router.get("/search/autocomplete",autoCompleteSearch);
router.get("/recent-searches", checkAuth('recent_search'), listRecentSearchesController);
router.get("/top-properties", listTopProperties);
router.get("/top-featured-hostels", listTopHostelsController);
router.get("/top-hostels", listTopHostels);
router.get("/top-hostels/:country", listTopHostelsByCountry);
router.get("/travel-activities", getTravelActivities);
router.post('/news-letter/subscribe', subscribeNewsletterController);
router.post('/properties-counts', getPropertiesCounts);
router.get('/filter', getFilterdProperty);
router.get("/categories-hostels",getCategoryWiseProperty);
router.get("/cities-list",listStatesByCountry);

export default router;

/**
 * @swagger
 * tags:
 *   name: Booking
 *   description: Booking Management
*/

/**
 * @swagger
 * /pages/save:
 *   post:
 *     summary: Save or update page data
 *     description: Saves or updates dynamic page data based on the provided page identifier.
 *     tags:
 *       - Pages
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: string
 *                 example: "header"
 *               data:
 *                 type: object
 *                 additionalProperties:
 *                   oneOf:
 *                     - type: string
 *                     - type: number
 *                     - type: boolean
 *                     - type: object
 *                       additionalProperties: true
 *                     - type: array
 *                       items:
 *                         oneOf:
 *                           - type: string
 *                           - type: number
 *                           - type: boolean
 *                           - type: object
 *                             additionalProperties: true
 *                 example: {
 *                   "title": "Welcome to our website",
 *                   "subtitle": "We are glad to have you",
 *                   "nestedObject": {
 *                     "key1": "value1",
 *                     "key2": 2
 *                   },
 *                   "arrayExample": [
 *                     "item1",
 *                     "item2",
 *                     { "nestedKey": "nestedValue" }
 *                   ]
 *                 }
 *     responses:
 *       200:
 *         description: Page data saved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Page data saved successfully."
 *       400:
 *         description: Missing page or data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Missing required fields: page or data."
 *       500:
 *         description: Error saving page data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "An error occurred while saving page data."
 */
/**
 * @swagger
 * /pages/top-properties:
 *   get:
 *     summary: Get top properties
 *     description: Retrieves the top properties based on recent searches.
 *     tags:
 *       - Pages
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Successfully retrieved top properties
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         example: "66bd2f7592a0a6fb036f3458"
 *                       name:
 *                         type: string
 *                         example: "Example Hostel"
 *                       location:
 *                         type: string
 *                         example: "London, UK"
 *                       image:
 *                         type: string
 *                         example: "https://example.com/image.jpg"
 *                       details:
 *                         type: object
 *                         properties:
 *                           description:
 *                             type: string
 *                             example: "A cozy place to stay."
 *                           ratings:
 *                             type: number
 *                             example: 4.5
 *       404:
 *         description: No recent searches found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Page not found."
 *       500:
 *         description: Error retrieving top properties
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "An error occurred while retrieving top properties."
 */
/**
 * @swagger
 * /pages/top-featured-hostels:
 *   get:
 *     summary: Get top properties
 *     description: Retrieves the top properties based on recent searches.
 *     tags:
 *       - Pages
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Successfully retrieved top properties
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         example: "66bd2f7592a0a6fb036f3458"
 *                       name:
 *                         type: string
 *                         example: "Example Hostel"
 *                       location:
 *                         type: string
 *                         example: "London, UK"
 *                       image:
 *                         type: string
 *                         example: "https://example.com/image.jpg"
 *                       details:
 *                         type: object
 *                         properties:
 *                           description:
 *                             type: string
 *                             example: "A cozy place to stay."
 *                           ratings:
 *                             type: number
 *                             example: 4.5
 *       404:
 *         description: No recent searches found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Page not found."
 *       500:
 *         description: Error retrieving top properties
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "An error occurred while retrieving top properties."
 */
/**
 * @swagger
 * /pages/top-hostels/{country}:
 *   get:
 *     summary: Get top hostels by country and optional city
 *     description: Retrieves the top hostels based on the country (required) and optionally city (state) specified in the query.
 *     tags:
 *       - Pages
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: country
 *         required: true
 *         schema:
 *           type: string
 *         description: The country for which to retrieve top hostels
 *         example: "India"
 *       - in: query
 *         name: city
 *         required: false
 *         schema:
 *           type: string
 *         description: The city (or state) within the country to narrow the search
 *         example: "Goa"
 *       - in: query
 *         name: currency
 *         required: false
 *         schema:
 *           type: string
 *         description: Currency code to convert prices to (e.g., USD, EUR)
 *         example: "USD"
 *     responses:
 *       200:
 *         description: Successfully retrieved top hostels
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     properties:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           _id:
 *                             type: string
 *                             example: "66bd2f7592a0a6fb036f3458"
 *                           name:
 *                             type: string
 *                             example: "Example Hostel"
 *                           address:
 *                             type: object
 *                             properties:
 *                               street:
 *                                 type: string
 *                                 example: "123 Hostel Street"
 *                               city:
 *                                 type: string
 *                                 example: "New Delhi"
 *                               country:
 *                                 type: string
 *                                 example: "India"
 *                           photos:
 *                             type: array
 *                             items:
 *                               type: string
 *                               example: "https://example.com/photo1.jpg"
 *                           location:
 *                             type: object
 *                             properties:
 *                               lat:
 *                                 type: number
 *                                 example: 28.6139
 *                               lng:
 *                                 type: number
 *                                 example: 77.2090
 *                           aboutUs:
 *                             type: string
 *                             example: "A cozy hostel in the heart of New Delhi."
 *                           freeFacilities:
 *                             type: array
 *                             items:
 *                               type: string
 *                               example: "Free Wi-Fi"
 *                           lowestAveragePricePerNight:
 *                             type: number
 *                             example: 50
 *                           liked:
 *                             type: boolean
 *                             example: true
 *       404:
 *         description: No hostels found for the specified country and city
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "No hostels found for the specified location."
 *       500:
 *         description: Error retrieving hostels
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "An error occurred while retrieving hostels."
 */
/**
 * @swagger
 * /pages/top-hostels:
 *   get:
 *     summary: Get top hostels grouped by specified countries
 *     description: Retrieves top hostels grouped by country. Accepts a comma-separated list of countries as a query parameter, and optionally filters by city and currency. Returns up to 5 hostels per country.
 *     tags:
 *       - Pages
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: countries
 *         schema:
 *           type: string
 *         required: true
 *         description: Comma-separated list of countries (e.g., "India,Thailand,Spain")
 *       - in: query
 *         name: city
 *         schema:
 *           type: string
 *         required: false
 *         description: Optional city (state) to filter hostels
 *       - in: query
 *         name: currency
 *         schema:
 *           type: string
 *           default: "INR"
 *         required: false
 *         description: Currency code for price conversion (e.g., "USD", "EUR", "INR")
 *     responses:
 *       200:
 *         description: Successfully retrieved top hostels
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   description: Array of countries with top hostels
 *                   items:
 *                     type: object
 *                     properties:
 *                       country:
 *                         type: string
 *                         example: "India"
 *                       hostels:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             _id:
 *                               type: string
 *                               example: "66bd2f7592a0a6fb036f3458"
 *                             name:
 *                               type: string
 *                               example: "Example Hostel"
 *                             address:
 *                               type: object
 *                               properties:
 *                                 street:
 *                                   type: string
 *                                   example: "123 Hostel Street"
 *                                 city:
 *                                   type: string
 *                                   example: "New Delhi"
 *                                 country:
 *                                   type: string
 *                                   example: "India"
 *                             location:
 *                               type: object
 *                               properties:
 *                                 lat:
 *                                   type: number
 *                                   example: 28.6139
 *                                 lng:
 *                                   type: number
 *                                   example: 77.2090
 *                             aboutUs:
 *                               type: string
 *                               example: "A cozy hostel in the heart of New Delhi."
 *                             freeFacilities:
 *                               type: array
 *                               items:
 *                                 type: string
 *                                 example: "Free Wi-Fi"
 *                             lowestAveragePricePerNight:
 *                               type: number
 *                               example: 50
 *                             liked:
 *                               type: boolean
 *                               example: true
 *       404:
 *         description: No hostels found for the specified country and city
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "No hostels found for the specified location."
 *       500:
 *         description: Error retrieving hostels
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "An error occurred while retrieving hostels."
 */

/**
 * @swagger
 * /pages/travel-activities:
 *   get:
 *     summary: Get travel activities by category
 *     description: Retrieves travel activities based on the specified category.
 *     tags:
 *       - Pages
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: category
 *         required: true
 *         schema:
 *           type: string
 *         description: The category of travel activities to retrieve
 *         example: "Explore Ruins"
 *     responses:
 *       200:
 *         description: Successfully retrieved travel activities
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 activities:
 *                   type: object
 *                   properties:
 *                     _id:
 *                       type: string
 *                       example: "60d5f9834f1b2c7a6c8d8b9e"
 *                     category:
 *                       type: string
 *                       example: "Peru-Cusco"
 *                     activities:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                             example: "Visit Machu Picchu"
 *                           description:
 *                             type: string
 *                             example: "Explore the ancient Inca city."
 *                           image:
 *                             type: string
 *                             example: "https://example.com/machu-picchu.jpg"
 *                           rating:
 *                             type: number
 *                             example: 4.9
 *       404:
 *         description: No activities found for the specified category
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "No travel activities found for the specified category."
 *       500:
 *         description: Error retrieving travel activities
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "An error occurred while retrieving travel activities."
 */
/**
 * @swagger
 * /pages/recent-searches:
 *   get:
 *     summary: Get recent searches
 *     description: Retrieves the most recent property searches by users.
 *     tags:
 *       - Pages
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Successfully retrieved recent searches
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         example: "66bd2f7592a0a6fb036f3458"
 *                       name:
 *                         type: string
 *                         example: "Example Hostel"
 *                       location:
 *                         type: string
 *                         example: "New York, USA"
 *                       image:
 *                         type: string
 *                         example: "https://example.com/image.jpg"
 *                       details:
 *                         type: object
 *                         properties:
 *                           description:
 *                             type: string
 *                             example: "A modern hostel located downtown."
 *                           ratings:
 *                             type: number
 *                             example: 4.7
 *                           price:
 *                             type: number
 *                             example: 45
 *                           currency:
 *                             type: string
 *                             example: "USD"
 *       404:
 *         description: No recent searches found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "No recent searches found."
 *       500:
 *         description: Error retrieving recent searches
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "An error occurred while retrieving recent searches."
 */

/**
 * @swagger
 * /pages/news-letter/subscribe:
 *   post:
 *     summary: Subscribe to the newsletter
 *     description: Adds a new subscriber to the newsletter list.
 *     tags:
 *       - Newsletter
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 example: "<EMAIL>"
 *     responses:
 *       200:
 *         description: Successfully subscribed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Subscription successful."
 *       400:
 *         description: Email already subscribed or invalid email
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid or already subscribed."
 *       500:
 *         description: Error subscribing to newsletter
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "An error occurred while subscribing."
 */
/**
 * @swagger
 * /pages/search/autocomplete:
 *   get:
 *     summary: Autocomplete search for properties
 *     description: Search for properties, states, cities, or countries by a search term. Matches will be highlighted in the response.
 *     tags:
 *       - Pages
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         required: true
 *         description: The search term for autocomplete (e.g., "Goa").
 *     responses:
 *       200:
 *         description: Successfully retrieved autocomplete results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         example: "347"
 *                       label:
 *                         type: string
 *                         example: "Goa, India"
 *                       highlighted:
 *                         type: string
 *                         example: "<strong>Go</strong>a, India"
 *                       category:
 *                         type: string
 *                         example: "City"
 *                       type:
 *                         type: string
 *                         example: "city"
 *                       city:
 *                         type: string
 *                         example: "Goa"
 *                       state:
 *                         type: string
 *                         example: "Goa"
 *                       country:
 *                         type: string
 *                         example: "India"
 *       400:
 *         description: Bad request (e.g., missing search term)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Search term is required."
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "An error occurred while processing the request."
 */

/**
 * @swagger
 * /pages/properties-counts:
 *   post:
 *     summary: Get properties count by country
 *     description: Fetch the count of properties for the specified countries.
 *     tags:
 *       - Pages
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               countries:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["Germany", "France", "India"]
 *     responses:
 *       200:
 *         description: Properties count fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Properties count fetched successfully."
 *                 data:
 *                   type: object
 *                   additionalProperties:
 *                     type: number
 *                   example: {
 *                     "Germany": 120,
 *                     "France": 85,
 *                     "India": 50
 *                   }
 *       400:
 *         description: Missing or invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid input data: countries is required."
 *       500:
 *         description: Error fetching properties count
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "An error occurred while fetching properties count."
 */
/**
 * @swagger
 * /pages/categories-hostels:
 *   get:
 *     summary: Get hostels by category with search and tag filters
 *     description: Retrieves hostels filtered by search terms and optional tag category. If tag matches, properties are returned under the tag; unmatched properties are returned separately.
 *     tags:
 *       - Pages
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search keywords to match against property name, state, or country. Multiple terms can be separated by spaces.
 *         example: "Delhi India Backpackers"
 *       - in: query
 *         name: tag
 *         schema:
 *           type: string
 *         description: Category tag to filter properties
 *         example: "party-hostels"
 *     responses:
 *       200:
 *         description: Successfully retrieved category-wise properties
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 tagMatched:
 *                   type: array
 *                   description: List of properties matching the provided tag and search terms
 *                   items:
 *                     $ref: '#/components/schemas/Property'
 *                 others:
 *                   type: array
 *                   description: List of properties matching the search but not the tag
 *                   items:
 *                     $ref: '#/components/schemas/Property'
 *       400:
 *         description: Invalid query parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid query parameters provided."
 *       500:
 *         description: Server error while retrieving properties
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "An error occurred while fetching properties."
 */

/**
 * @swagger
 * /pages/cities-list:
 *   get:
 *     summary: Get all unique state names by country
 *     description: Returns a list of unique state names for a given country.
 *     tags:
 *       - Pages
 *     parameters:
 *       - in: query
 *         name: country
 *         required: true
 *         schema:
 *           type: string
 *         description: The country to filter states by
 *         example: "India"
 *     responses:
 *       200:
 *         description: Successfully retrieved unique states
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: string
 *                     example: "Goa"
 *                 message:
 *                   type: string
 *                   example: "Unique states found in India"
 *       400:
 *         description: Missing country query parameter
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Country query parameter is required."
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "An error occurred while retrieving states."
 */
