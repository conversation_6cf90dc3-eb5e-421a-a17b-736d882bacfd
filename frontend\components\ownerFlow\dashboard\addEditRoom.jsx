/* eslint-disable no-constant-binary-expression */
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, EDitRoomtApi } from "@/services/hostemproviderservices";
import { CloudUpload } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";
import { getItemLocalStorage } from "@/utils/browserSetting";
import { BASE_URL } from "@/utils/api";
import countries from "world-countries";
import Loader from "@/components/loader/loader";
import CustomSelect from "@/components/common/CustomDropdown2";
import Image from "next/image";
import { useHeaderOwner } from "../headerContex";

const AddEditRoom = ({ closeeditModal, roomId, setIsUpdate }) => {
  // eslint-disable-next-line no-unused-vars
  const [properties, setProperties] = useState([]);
  const [selectedRoom, setSelectedRoom] = useState(roomId || null);
  // eslint-disable-next-line no-unused-vars
  const [currencies, setCurrencies] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [selectedFile, setSelectedFile] = useState(null);
  // const [quantity, setQuantity] = useState(1);
  const { propertyData } = useHeaderOwner();

  console.log("propertyData", propertyData);

  const [addroom, setAddRoom] = useState({
    name: selectedRoom?.name || "",
    type: selectedRoom?.type || "",
    beds: selectedRoom?.beds || "",
    // eslint-disable-next-line no-undef
    ensuite: `${selectedRoom?.ensuite}` || No,
    // rate: selectedRoom?.rate?.weekdayRate?.value || "",
    // weekdayRate: selectedRoom?.rate?.weekdayRate?.value || "",
    // weekendRate: selectedRoom?.rate?.weekendRate?.value || "",
    // currency: selectedRoom?.currency || "",
    images: selectedRoom?.images || [],
    description: selectedRoom?.description || "",
    // bedAndBreakfast: selectedRoom?.bedAndBreakfast || false,
    // nonRefundable: selectedRoom?.nonRefundable || false,
    // freeCancellation: selectedRoom?.freeCancellation || false,
  });

  // eslint-disable-next-line no-unused-vars
  const [selectedValues, setSelectedValues] = useState({
    beds: addroom.beds || "",
    ensuite: addroom.ensuite || "Ensuite",
    currency: addroom.currency || "",
  });

  // const decrement = () => {
  //   setQuantity((prev) => Math.max(1, prev - 1));
  // };

  // const increment = () => {
  //   setQuantity((prev) => prev + 1);
  // };

  const increment = () => {
    setAddRoom((prev) => ({
      ...prev,
      beds: Number(prev.beds) + 1,
    }));
  };

  const decrement = () => {
    setAddRoom((prev) => ({
      ...prev,
      beds: Math.max(1, Number(prev.beds) - 1), // prevent going below 1
    }));
  };
  // const handleCount = (e) => {
  //   const value = parseInt(e.target.value);
  //   if (!isNaN(value) && value >= 1) {
  //     setQuantity(value);
  //   } else if (e.target.value === "") {
  //     setQuantity(1);
  //   }
  // };
  const handleChange = (e) => {
    const { name, value } = e.target;
    setAddRoom({
      ...addroom,
      [name]: value,
    });
  };

  const id = getItemLocalStorage("hopid");
  const isFirstRender = useRef(null);

  useEffect(() => {
    if (id && !isFirstRender.current) {
      fetchList(id);
      fetchCurrencies();
    } else {
      isFirstRender.current = false;
    }
  }, [id]);

  // const fetchCurrencies = async () => {
  //   try {
  //     // Fetch currency rates
  //     const exchangeRateResponse = await axios.get(
  //       "https://api.exchangerate-api.com/v4/latest/USD"
  //     );

  //     if (exchangeRateResponse.data) {
  //       const currencyRates = exchangeRateResponse.data.rates;

  //       // Map currency codes to country data using the world-countries package
  //       const currencyList = Object.keys(currencyRates).map((currencyCode) => {
  //         const country = countries.find((country) =>
  //           country.currencies
  //             ? Object.keys(country.currencies).includes(currencyCode)
  //             : false
  //         );

  //         return {
  //           code: currencyCode,
  //           name: country?.currencies?.[currencyCode]?.name || currencyCode, // Fallback to the currency code if no name is found
  //           symbol: country?.currencies?.[currencyCode]?.symbol || "", // Fallback to an empty string if no symbol is found
  //           flag:
  //             `https://flagcdn.com/w320/${country?.cca2?.toLowerCase()}.png` ||
  //             "https://via.placeholder.com/30x25", // Fallback to a placeholder if no flag is found
  //         };
  //       });

  //       setCurrencies(currencyList);
  //     }
  //   } catch (error) {
  //     console.error("Error fetching currencies or rates:", error);
  //   }
  // };

  const fetchCurrencies = async () => {
    try {
      const countryData = countries.map((country) => {
        // Safely extract currency code and symbol, falling back to defaults
        const currencyCode =
          country?.currencies && Object.keys(country?.currencies)[0]
            ? Object.keys(country?.currencies)[0]
            : "N/A";
        const currencySymbol =
          country?.currencies && country?.currencies[currencyCode]?.symbol
            ? country?.currencies[currencyCode]?.symbol
            : "€";

        // Get the flag code (ISO 3166-1 alpha-2) for the flag
        const flagCode = country.cca2 ? country.cca2.toLowerCase() : "xx"; // Default to 'xx' if cca2 is missing

        // Construct the flag image URL or use a placeholder
        const flag =
          `https://flagcdn.com/w320/${flagCode}.png` ||
          "https://via.placeholder.com/30x25";

        return {
          name: country?.name?.common || "Unknown", // Country name, fallback to "Unknown" if not found
          code: currencyCode, // Currency code
          symbol: currencySymbol, // Currency symbol
          flag: flag, // Flag image URL
        };
      });

      setCurrencies(countryData); // Store the country data
    } catch (error) {
      console.error("Error fetching country data:", error);
    }
  };

  const fetchList = async (id) => {
    try {
      const response = await RoomListApi(id, 1, 100);
      if (response.status === 200) {
        setProperties(response.data.data.rooms);
      } else {
        // Handle error response
        toast.error("Failed to fetch rooms");
      }
    } catch (error) {
      console.log("Error fetching rooms:", error);
      toast.error("Error fetching rooms");
    }
  };

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setSelectedValues((prev) => ({
      ...prev,
      [name]: value, // Store entire object, not just code
    }));
  };

  console.log("addroom", addroom);

  const handleEditRoomApi = async () => {
    if (selectedRoom && selectedRoom._id) {
      const storedId = getItemLocalStorage("hopid");
      const {
        name,
        type,
        beds,
        ensuite,
        // rate,
        // currency,
        images,
        // weekdayRate,
        // weekendRate,
      } = addroom;
      if (
        !name ||
        !type ||
        !beds
        // !rate ||
        // !currency ||
        // !weekdayRate ||
        // !weekendRate
      ) {
        toast.error("Please fill all required fields");
        return;
      }
      const payload = {
        name,
        type,
        property: storedId,
        beds: parseInt(beds),
        // rate: {
        //   weekdayRate: {
        //     value: parseFloat(addroom?.weekdayRate),
        //   },
        //   weekendRate: {
        //     value: parseFloat(addroom?.weekendRate),
        //   },
        // },
        // currency: addroom?.currency?.value,
        // images: images.map((url) => ({
        //   title: "Room view",
        //   url,
        // })),
        images,
        description: addroom.description,
        // bedAndBreakfast: addroom.bedAndBreakfast,
        // nonRefundable: addroom.nonRefundable,
        // freeCancellation: addroom.freeCancellation,
        ensuite,
        // weekdayRate,
      };
      try {
        const response = await EDitRoomtApi(selectedRoom._id, payload);
        if (response.status === 200) {
          toast.success("Room updated successfully");
          setSelectedRoom(null); // Clear selected room after successful update
          closeeditModal();
          setIsUpdate((prevState) => !prevState);
        } else {
          toast.error("Failed to update room");
        }
      } catch (error) {
        console.log("Error updating room", error);
        toast.error("Error updating room");
      }
    } else {
      toast.error("No room selected");
    }
  };

  const handlePhotoChange = async (e) => {
    const { files } = e.target;

    if (files.length === 0) {
      toast.error("At least one file is required");
      return;
    }
    const allowedTypes = ["image/jpeg", "image/png", "image/jpg"];

    // Check if all selected files are of allowed types
    const invalidFiles = Array.from(files).filter(
      (file) => !allowedTypes.includes(file.type)
    );

    if (invalidFiles.length > 0) {
      toast.error("Only JPG, JPEG, and PNG files are allowed.");
      return;
    }

    setIsLoading(true);

    try {
      // Loop through all selected files
      const uploadedImages = await Promise.all(
        Array.from(files).map(async (file) => {
          const formData = new FormData();
          formData.append("files", file);

          // Get presigned URL for each file
          const presignedUrlResponse = await fetch(
            `${BASE_URL}/fileUpload/generate-presigned-url`,
            {
              method: "POST",
              body: formData,
            }
          );

          if (!presignedUrlResponse.ok) {
            throw new Error("Failed to get presigned URL");
          }

          const presignedUrlData = await presignedUrlResponse.json();
          const objectURL =
            Array.isArray(presignedUrlData.data) &&
            presignedUrlData.data[0]?.path;

          return { title: "Room view", action: "add", objectUrl: objectURL };
        })
      );

      // Update state with new uploaded image URLs
      setAddRoom((prevState) => ({
        ...prevState,
        images: [...(prevState?.images || []), ...uploadedImages],
      }));

      toast.success("Files uploaded successfully.");
    } catch (error) {
      console.error("Error uploading files:", error);
      toast.error("Error uploading files.");
    } finally {
      setIsLoading(false);
    }
  };

  const removeImage = (indexToRemove) => {
    setAddRoom((prevState) => ({
      ...prevState,
      images: prevState.images.filter((_, index) => index !== indexToRemove),
    }));
  };

  // const removeImage = async (indexToRemove) => {
  //   const imageToRemove = addroom.images[indexToRemove];

  //   // 1. Call API
  //   try {
  //     await EDitRoomtApi(selectedRoom.slug, {
  //       images: [
  //         {
  //           _id: imageToRemove._id,
  //           action: "delete",
  //         },
  //       ],
  //     });

  //     // 2. Update state (mark as deleted but keep rest)
  //     setAddRoom((prevState) => ({
  //       ...prevState,
  //       images: prevState.images.map((img, index) =>
  //         index === indexToRemove ? { _id: img._id, action: "delete" } : img
  //       ),
  //     }));

  //     toast.success("Image deleted successfully");
  //   } catch (error) {
  //     console.error("Failed to delete image", error);
  //     toast.error("Failed to delete image");
  //   }
  // };

  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [selectedImages, setSelectedImages] = useState([]);
  const popupRef = useRef(null);

  // Close popup when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        setIsPopupOpen(false);
      }
    };

    if (isPopupOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isPopupOpen]);

  const toggleImageSelection = (imageId) => {
    setSelectedImages((prev) =>
      prev.includes(imageId)
        ? prev.filter((id) => id !== imageId)
        : [...prev, imageId]
    );
  };

  console.log("Selected images:", selectedImages);

  const handleAddImages = () => {
    if (!Array.isArray(propertyData?.images) || selectedImages.length === 0) {
      setIsPopupOpen(false);
      return;
    }

    const imageIdToImageMap = new Map(
      propertyData.images.map((image) => [image._id, image])
    );

    const normalizeObjectUrl = (url) => {
      if (!url) return "";
      let normalized = String(url).trim().replace(/^@/, "");

      const baseEnv = process.env.NEXT_PUBLIC_S3_URL_FE
        ? process.env.NEXT_PUBLIC_S3_URL_FE.replace(/\/+$/, "")
        : "";
      const cfBase = "https://de2qijr9wfk4u.cloudfront.net";

      const candidates = [baseEnv, cfBase].filter(Boolean);
      for (const base of candidates) {
        const prefix = `${base}/`;
        if (normalized.startsWith(prefix)) {
          normalized = normalized.slice(prefix.length);
          break;
        }
      }

      return normalized.replace(/^\/+/, "");
    };

    const imagesToAppend = selectedImages
      .map((imageId) => imageIdToImageMap.get(imageId))
      .filter(Boolean)
      .map((image) => ({
        title: "Room view",
        action: "add",
        objectUrl: normalizeObjectUrl(image.objectUrl),
      }));

    setAddRoom((prevState) => {
      const existingImages = prevState?.images || [];
      const existingObjectUrls = new Set(
        existingImages.map((img) => img?.objectUrl)
      );

      const uniqueNewImages = imagesToAppend.filter(
        (img) => img?.objectUrl && !existingObjectUrls.has(img.objectUrl)
      );

      return {
        ...prevState,
        images: [...existingImages, ...uniqueNewImages],
      };
    });

    setSelectedImages([]);
    toast.success("Images added from gallery");
    setIsPopupOpen(false);
  };

  return (
    <>
      <Loader open={isLoading} />
      <div className='max-w-[780px] mx-auto'>
        <div className='grid xs:grid-cols-2 grid-cols-1 gap-4'>
          <div className='xs:col-span-1 col-span-2'>
            <label
              className='block text-black sm:text-sm text-xs font-medium mb-1.5'
              htmlFor='username'
            >
              {selectedRoom?.name ? "Room Name" : "Select Name"}{" "}
              <span className='text-red-500'>*</span>
            </label>
            {/* <select
              className='w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500'
              onChange={handleRoomChange}
              value={selectedRoom?.name || ""}
            >
              <option value='' disabled>
                Select Room
              </option>
              {properties.map((val, index) => (
                <option value={val.name} key={index}>
                  {val.name}
                </option>
              ))}
            </select> */}

            <input
              type='text'
              name='name'
              value={selectedRoom?.name}
              onChange={handleInputChange}
              className='w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500'
              placeholder='Enter Room Name'
            />
          </div>

          {selectedRoom && (
            <>
              <div className='xs:col-span-1 col-span-2'>
                <label
                  className='block text-black sm:text-sm text-xs font-medium mb-1.5'
                  htmlFor='username'
                >
                  Room Type <span className='text-red-500'>*</span>
                </label>
                {/* <select
                  name='type'
                  className='w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500'
                  value={addroom?.type || ""}
                  onChange={handleChange}
                >
                  <option disabled value=''>
                    Room Type
                  </option>
                  <option value='private'>Private</option>
                  <option value='dbl private'>Dbl Private</option>
                  <option value='mixed dorm'>Mixed Dorm</option>
                  <option value='female dorm'>Female Dorm</option>
                </select> */}
                <CustomSelect
                  name='type'
                  options={[
                    { value: "", label: "Room Type" },
                    { value: "private", label: "Private" },
                    { value: "dbl", label: "Dbl Private" },
                    { value: "mixed dorm", label: "Mixed Dorm" },
                    { value: "female dorm", label: "Female Dorm" },
                  ]}
                  value={addroom.type}
                  onChange={(selectedOption) =>
                    handleChange({
                      target: { name: "type", value: selectedOption?.value },
                    })
                  }
                  placeholder='Select Room Type'
                />
              </div>
              <div className='relative'>
                <label className='block text-black sm:text-sm text-xs font-medium mb-1.5'>
                  Total Beds <span className='text-red-500'>*</span>
                </label>

                <div className='flex items-center border border-gray-300 rounded-lg overflow-hidden w-full max-w-md'>
                  <input
                    type='number'
                    name='beds'
                    min='1'
                    step='1'
                    value={addroom.beds}
                    onChange={(e) => {
                      const value = e.target.value;
                      if (/^\d+$/.test(value) && parseInt(value) > 0) {
                        handleChange(e);
                      } else if (value === "") {
                        handleChange(e);
                      }
                    }}
                    className='w-full px-4 py-2 focus:outline-none'
                  />

                  <button
                    className='h-6 w-2 rounded-full border-2 border-primary-blue  px-4 py-2 transition-colors text-primary-blue font-bold flex items-center justify-center'
                    onClick={decrement}
                  >
                    -
                  </button>
                  <button
                    className='h-6 w-4 rounded-full border-2 border-primary-blue  px-4 py-2 transition-colors text-primary-blue font-bold flex items-center justify-center mx-2'
                    onClick={increment}
                  >
                    +
                  </button>
                </div>
              </div>

              {/* Ensuite Dropdown */}
              <div className='relative xs:col-span-1 col-span-2'>
                <label className='block text-black sm:text-sm text-xs font-medium mb-1.5'>
                  Ensuite
                </label>

                {/* <select
                  name='ensuite'
                  className='w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500'
                  value={addroom.ensuite || ""}
                  onChange={handleChange}
                >
                  <option disabled value=''>
                    Ensuit
                  </option>
                  <option value='true'>Yes</option>
                  <option value='false'>No</option>
                </select> */}

                <CustomSelect
                  name='ensuite'
                  options={[
                    { value: "", label: "Ensuit" },
                    { value: "true", label: "Yes" },
                    { value: "false", label: "No" },
                  ]}
                  value={addroom.ensuite}
                  onChange={(selectedOption) =>
                    handleChange({
                      target: { name: "ensuite", value: selectedOption?.value },
                    })
                  }
                  placeholder='Select Ensuite'
                />
              </div>
              {/* <div className='xs:col-span-1 col-span-2'>
                <label
                  className='block text-black sm:text-sm text-xs font-medium mb-1.5 '
                  htmlFor='username'
                >
                  Rate <span className='text-red-500'>*</span>
                </label>
                <input
                  type='number'
                  name='rate'
                  value={addroom.rate}
                  onChange={handleChange}
                  className='w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500'
                  placeholder='Price per room, Tax included'
                />
              </div> */}

              {/* <div className='xs:col-span-1 col-span-2'>
                <label
                  className='block text-black sm:text-sm text-xs font-medium mb-1.5'
                  htmlFor='username'
                >
                  Currency <span className='text-red-500'>*</span>
                </label>

                <CustomSelect
                  options={currencies.map((currency) => ({
                    value: currency.code,
                    label: (
                      <span className='flex items-center'>
                        <Image 
                          src={currency.flag || "/placeholder.svg"}
                          alt={`${currency.code} Flag`}
                          className='inline-block w-4 h-3 mr-2'
                          width={20}
                          height={15}
                        />
                        {currency.code} ({currency.symbol})
                      </span>
                    ),
                  }))}
                  value={addroom.currency}
                  onChange={(selectedOption) =>
                    handleChange({
                      target: { name: "currency", value: selectedOption },
                    })
                  }
                  placeholder='Select Currency'
                />
              </div> */}

              <div className='relative  '>
                <label
                  className='block text-black sm:text-sm text-xs font-medium mb-1.5'
                  htmlFor='username'
                >
                  Room Photo
                </label>

                <input
                  type='file'
                  name='images'
                  onChange={handlePhotoChange}
                  multiple
                  accept='.jpg, .jpeg, .png'
                  className='z-10 cursor-pointer block w-full p-2 px-4 text-sm bg-transparent border rounded-lg  opacity-0 border-gray-220 focus:outline-none text-slate-320 placeholder:text-gray-320 absolute top-0 left-0 right-0 bottom-0'
                  placeholder='Upload Photos'
                />
                <div className='px-4 py-2 border border-[#40E0D0] rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500 flex items-center justify-between border-dashed bg-[#40E0D01A] cursor-pointer'>
                  {!selectedFile
                    ? "Upload Photos"
                    : selectedFile?.name || "Upload file"}{" "}
                  <CloudUpload className='text-[#40E0D0]' size={22} />
                </div>
              </div>
              <div className='relative  '>
                <label
                  className='block text-black sm:text-sm text-xs font-medium mb-1.5'
                  htmlFor='username'
                >
                  Select Photo
                </label>

                <input
                  type='file'
                  name='images'
                  multiple
                  className='z-10 cursor-pointer block w-full p-2 px-4 text-sm bg-transparent border rounded-lg  opacity-0 border-gray-220 focus:outline-none text-slate-320 placeholder:text-gray-320 absolute top-0 left-0 right-0 bottom-0'
                  placeholder='Upload Photos'
                  onClick={(e) => {
                    e.preventDefault();
                    if (propertyData?.images?.length > 0) {
                      setIsPopupOpen(true);
                    } else {
                      toast.error("No property image found");
                    }
                  }}
                />
                <div
                  className='px-4 py-[9px] border border-[#40E0D0] rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500 flex items-center justify-between border-dashed bg-[#40E0D01A] cursor-pointer'
                  onClick={() => {
                    if (propertyData?.images?.length > 0) {
                      setIsPopupOpen(false);
                    } else {
                      toast.error("No property image found");
                    }
                  }}
                >
                  Select Photo From Gallery
                </div>
              </div>
              {addroom?.images?.length > 0 && (
                <div className='col-span-2'>
                  <div className='grid xs:grid-cols-6 grid-cols-2 sm:gap-4 gap-3'>
                    {addroom?.images?.map((image, index) => (
                      <div
                        key={index}
                        className='flex items-center justify-between sm:px-4 px-2 sm:py-2.5 py-2 border border-[#40E0D0] border-dashed bg-[#40E0D01A] rounded-lg relative'
                      >
                        <Image
                          // src={image?.objectURL}
                          src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/${image?.objectUrl}`}
                          alt={`Existing Image ${index + 1}`}
                          className='w-[54px] h-[36px] object-cover rounded-sm'
                          width={54}
                          height={36}
                        />
                        <span
                          className='text-black hover:text-red-600 font-black cursor-pointer text-xxs absolute top-0 right-1'
                          onClick={() => removeImage(index)}
                        >
                          &#10005;
                        </span>
                        {/* <XCircle
                        className='absolute top-1 right-1 text-red-500 cursor-pointer'
                        size={24}
                        onClick={() => removeImage(index)}
                      /> */}
                      </div>
                    ))}
                  </div>
                </div>
              )}
              {isPopupOpen && (
                <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
                  <div
                    ref={popupRef}
                    className='bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto'
                  >
                    <div className='flex justify-between items-center mb-4'>
                      <h3 className='text-lg font-semibold'>Select Photos</h3>
                      <button
                        onClick={() => setIsPopupOpen(false)}
                        className='text-gray-500 hover:text-gray-700'
                      >
                        <svg
                          xmlns='http://www.w3.org/2000/svg'
                          className='h-6 w-6'
                          fill='none'
                          viewBox='0 0 24 24'
                          stroke='currentColor'
                        >
                          <path
                            strokeLinecap='round'
                            strokeLinejoin='round'
                            strokeWidth={2}
                            d='M6 18L18 6M6 6l12 12'
                          />
                        </svg>
                      </button>
                    </div>

                    <div className='grid grid-cols-4 gap-4'>
                      {propertyData?.images?.map((image) => (
                        <div key={image._id} className='relative'>
                          <img
                            src={image.objectUrl}
                            alt={`Gallery ${image._id}`}
                            className='w-full h-40 object-cover rounded'
                          />
                          <input
                            type='checkbox'
                            checked={selectedImages.includes(image._id)}
                            onChange={() => toggleImageSelection(image._id)}
                            className='absolute top-2 right-2 h-5 w-5 rounded border-gray-300 text-teal-600 focus:ring-teal-500'
                          />
                        </div>
                      ))}
                    </div>

                    <div className='mt-6 flex justify-end'>
                      <button
                        onClick={handleAddImages}
                        disabled={selectedImages.length === 0}
                        className={`px-4 py-2 rounded-lg ${
                          selectedImages.length === 0
                            ? "bg-gray-300 cursor-not-allowed"
                            : "bg-[#40E0D0] hover:bg-teal-500"
                        } text-white`}
                      >
                        Add ({selectedImages.length})
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {/* Rates */}

              {/* <div className='inner-currency-wrap'>
                <label
                  className='block text-black sm:text-sm text-xs font-medium mb-1.5'
                  htmlFor='username'
                >
                  Weekday rate <span className='text-red-500'>*</span>
                </label>

                <div className='flex items-center w-full sm:px-1 px-2 py-1 border border-black/50 rounded-lg focus-within:ring-1 focus-within:ring-teal-500'>
                  <CustomSelect
                    className='w-[150%]'
                    options={currencies.map((currency) => ({
                      value: currency.code,
                      label: (
                        <span className='flex items-center'>
                          <Image 
                            src={currency.flag || "/placeholder.svg"}
                            alt={`${currency.code} Flag`}
                            className='inline-block w-4 h-3 mr-2'
                            width={20}
                            height={15}
                          />
                          {currency.code} ({currency.symbol})
                        </span>
                      ),
                    }))}
                    value={addroom.currency}
                    onChange={(selectedOption) =>
                      handleChange({
                        target: { name: "currency", value: selectedOption },
                      })
                    }
                    placeholder='Select Currency'
                  />
                  <span className='mr-2 text-gray-300'>|</span>

                  <input
                    type='number'
                    name='weekdayRate'
                    placeholder='Weekday Rate'
                    className='w-full bg-transparent focus:outline-none text-black sm:text-sm text-xs placeholder:text-gray-500 pl-9'
                    value={addroom.weekdayRate || ""}
                    onChange={handleChange}
                  />
                </div>
              </div>
              <div className='inner-currency-wrap'>
                <label
                  className='block text-black sm:text-sm text-xs font-medium mb-1.5'
                  htmlFor='username'
                >
                  Weekend Rate <span className='text-red-500'>*</span>
                </label>

                <div className='flex items-center w-full sm:px-1 px-2 py-1 border border-black/50 rounded-lg focus-within:ring-1 focus-within:ring-teal-500'>
                  <CustomSelect
                    className='w-[150%]'
                    options={currencies.map((currency) => ({
                      value: currency.code,
                      label: (
                        <span className='flex items-center'>
                          <Image 
                            src={currency.flag || "/placeholder.svg"}
                            alt={`${currency.code} Flag`}
                            className='inline-block w-4 h-3 mr-2'
                            width={20}
                            height={15}
                          />
                          {currency.code} ({currency.symbol})
                        </span>
                      ),
                    }))}
                    value={addroom.currency}
                    onChange={(selectedOption) =>
                      handleChange({
                        target: { name: "currency", value: selectedOption },
                      })
                    }
                    placeholder='Select Currency'
                  />
                  <span className='mr-2 text-gray-300'>|</span>

                  <input
                    type='number'
                    name='weekendRate'
                    placeholder='Weekend Rate'
                    className='w-full bg-transparent focus:outline-none text-black sm:text-sm text-xs placeholder:text-gray-500 pl-9'
                    value={addroom.weekendRate || ""}
                    onChange={handleChange}
                  />
                </div>
              </div> */}

              <div className='col-span-2'>
                <label
                  className='block text-black sm:text-sm text-xs font-medium mb-1.5 '
                  htmlFor='username'
                >
                  Room Description
                </label>

                <textarea
                  name='description'
                  value={addroom?.description || ""}
                  onChange={handleChange}
                  rows={4}
                  className='w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500'
                  placeholder='Room Description'
                />
              </div>

              {/* <div className='col-span-2 flex items-center'>
                <input
                  type='checkbox'
                  name='ensuite'
                  checked={selectedRoom?.ensuite || ""}
                  onChange={handleCheckboxChange}
                  className='mr-2'
                />
                <label htmlFor='ensuite' className='text-black text-sm'>
                  Ensuite Bathroom
                </label>
              </div> */}

              {/* tags */}
              {/* <div className='flex items-center justify-start mt-4 sm:gap-x-3 gap-2 flex-wrap col-span-2'>
                <div
                  className={`border border-[#B9BDC7] rounded-full p-2 font-manrope font-semibold sm:text-sm text-xs text-[#202224] cursor-pointer ${
                    addroom?.bedAndBreakfast
                      ? "bg-[#40e0d033] border-[#40e0d033]"
                      : "bg-transparent"
                  }`}
                  onClick={() =>
                    setAddRoom({
                      ...addroom,
                      bedAndBreakfast: !addroom.bedAndBreakfast,
                      nonRefundable: false,
                      freeCancellation: false,
                    })
                  }
                >
                  Bed and Breakfast
                </div>
                <div
                  className={`border border-[#B9BDC7] rounded-full p-2 font-manrope font-semibold sm:text-sm text-xs text-[#202224] cursor-pointer ${
                    addroom?.nonRefundable
                      ? "bg-[#40e0d033] border-[#40e0d033]"
                      : "bg-transparent"
                  }`}
                  onClick={() =>
                    setAddRoom({
                      ...addroom,
                      nonRefundable: !addroom.nonRefundable,
                      bedAndBreakfast: false,
                      freeCancellation: false,
                    })
                  }
                >
                  Non Refundable
                </div>
                <div
                  className={`border border-[#B9BDC7] rounded-full p-2 font-manrope font-semibold sm:text-sm text-xs text-[#202224] cursor-pointer ${
                    addroom?.freeCancellation
                      ? "bg-[#40e0d033] border-[#40e0d033]"
                      : "bg-transparent"
                  }`}
                  onClick={() =>
                    setAddRoom({
                      ...addroom,
                      freeCancellation: !addroom.freeCancellation,
                      bedAndBreakfast: false,
                      nonRefundable: false,
                    })
                  }
                >
                  Free Cancellation
                </div>
              </div> */}

              <div className='xs:flex block  items-center justify-between w-full sm:my-14 my-7 gap-4 col-span-2 py-4 bg-white/80 sticky bottom-0 backdrop-blur-sm'>
                <button
                  className='hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 px-4 border-black rounded-lg w-full text-sm xs:mb-0 mb-2'
                  onClick={closeeditModal}
                >
                  Cancel
                </button>
                <button
                  className='bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm'
                  onClick={handleEditRoomApi}
                >
                  Save Changes
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default AddEditRoom;
