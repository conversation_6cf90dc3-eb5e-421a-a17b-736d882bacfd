/* eslint-disable react/no-unescaped-entities */
"use client";
import Image from "next/image";
// import { useInView } from "react-intersection-observer";
import { FaArrowRight } from "react-icons/fa6";
import React, { useEffect, useRef, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import Link from "next/link";
import { Autoplay, Navigation } from "swiper/modules";
import "swiper/css/navigation";
import { getHomePagePropertyCountApi } from "@/services/webflowServices";
// import { motion } from "framer-motion";

const ExpandPage = () => {
  const [currentImage, setCurrentImage] = useState(0);
  // eslint-disable-next-line no-unused-vars
  const [manualClick, setManualClick] = useState(false);

  // const [ref1, inView1] = useInView({ threshold: 0.2 });
  // const [ref2, inView2] = useInView({ threshold: 0.1 });
  // const [ref3, inView3] = useInView({ threshold: 0.98 });

  const [popertyCount, setPopertyCount] = useState([]);
  const isFirstRender = useRef(null);
  const [isMobile, setIsMobile] = useState(false);
  const [visibleSlides, setVisibleSlides] = useState([]);

  // eslint-disable-next-line no-unused-vars
  const [direction, setDirection] = useState("right"); // Track slide direction

  const countryImages = {
    India: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/india-new.webp`,
    Thailand: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Thailand-new.webp`,
    Indonesia: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Indonesia-new.webp`,
    Colombia: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/colombiaaa.webp`,
    Spain: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/spainnn.webp`,
    Mexico: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mexicooo.webp`,
    Italy: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/italyyy-new.webp`,
    Portugal: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/portugalll.webp`,
    Brazil: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/brazilll.webp`,
    USA: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/usaaa.webp`,
    Japan: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/japannn.webp`,
    Vietnam: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Vietnam-new.webp`,
    France: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/franceee.webp`,
    Australia: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/australiaaa.webp`,
    Peru: `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/peruuu.webp`,
  };

  const CountryList = [
    "India",
    "Thailand",
    "Indonesia",
    "Colombia",
    "Spain",
    "Mexico",
    "Italy",
    "Portugal",
    "Brazil",
    "USA",
    "Japan",
    "Vietnam",
    "France",
    "Australia",
    "Peru",
  ];

  const images = [
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Bookhostel.webp`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/phone-details-1.2.webp`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/phone-details-2.2.webp`,
  ];

  useEffect(() => {
    const fetchPropertyCount = async () => {
      try {
        const response = await getHomePagePropertyCountApi({
          countries: CountryList,
        });
        setPopertyCount(response?.data?.data?.propertyCounts || []);
      } catch (error) {
        console.error("Error fetching stay data:", error);
      } finally {
        /* empty */
      }
    };
    if (!isFirstRender.current) {
      fetchPropertyCount();
    } else {
      isFirstRender.current = false;
    }
  }, []);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(typeof window !== "undefined" && window.innerWidth <= 768);
    };

    // Initial check
    checkMobile();

    // Add resize listener
    if (typeof window !== "undefined") {
      window.addEventListener("resize", checkMobile);
      return () => window.removeEventListener("resize", checkMobile);
    }
  }, []);
  // useEffect(() => {
  //   if (!manualClick) {
  //     if (inView3) setCurrentImage(2);
  //     else if (inView2) setCurrentImage(1);
  //     else if (inView1) setCurrentImage(0);
  //   }
  // }, [inView1, inView2, inView3, manualClick]);

  const handleClick = (index) => {
    setCurrentImage(index);
    setManualClick(true);
    // setTimeout(() => setManualClick(false), 3500); // allow scroll update after 1.5s
  };

  const [imageLoaded, setImageLoaded] = useState(Array(images.length).fill(false));
  const [qrLoaded, setQrLoaded] = useState(false);

  const handleImageLoad = (index) => {
    setImageLoaded((prev) => {
      const updated = [...prev];
      updated[index] = true;
      return updated;
    });
  };

  // useEffect(() => {
  //   const interval = setInterval(() => {
  //     setCurrentImage((prev) => (prev + 1) % images.length); // Loop through images
  //   }, 5000); // Change image every 3 seconds

  //   return () => clearInterval(interval); // Cleanup interval
  // }, []);

  return (
    <div className="relative container z-0 sm:block hidden">
      <section className="w-full bg-transparent md:pb-16 pb-2 backdrop-blur-sm hidden">
        <div className="w-full  absolute -top-44 lg:-top-60 ">
          {isMobile ? (
            <div>
              <h2 className="font-semibold text-white font-manrope md:text-3xl sm:text-2xl text-xl mb-3">
                Explore the{" "}
                <span className="font-mashiny font-normal text-primary-blue text-4xl md:text-5xl">
                  {" "}
                  World{" "}
                </span>{" "}

              </h2>
              <Swiper
                slidesPerView={"auto"}
                spaceBetween={15}
                autoplay={true}
                loop
                modules={[Autoplay, Navigation]}
                // navigation={true}
                className="mySwiper myCustomSwiper"
                autoHeight={true}
                onSlideChange={(swiper) => {
                  // Get currently visible slides
                  const newVisibleSlides = swiper.slides
                    .slice(swiper.activeIndex, swiper.activeIndex + 4) // Select 4 slides in view
                    .map((slide) => slide.getAttribute("data-index")); // Get index of each slide

                  setVisibleSlides(newVisibleSlides);
                }}
                breakpoints={{
                  0: { slidesPerView: 1.5, spaceBetween: 20 },
                  640: { slidesPerView: 2 },
                  768: { slidesPerView: 3 },
                  1024: { slidesPerView: 4 },
                }}
              >
                {[
                  "India",
                  "Thailand",
                  "Indonesia",
                  "Colombia",
                  "Spain",
                  "Mexico",
                  "Italy",
                  "Portugal",
                  "Brazil",
                  "USA",
                  "Japan",
                  "Vietnam",
                  "France",
                  "Australia",
                  "Peru",
                ].map((country, index) => (
                  <SwiperSlide
                    key={index}
                    data-index={index} // Set index as data attribute
                    className={`${visibleSlides.indexOf(index.toString()) === 1 ||
                      visibleSlides.indexOf(index.toString()) === 3
                      ? "mt-6 pb-10"
                      : "mt-0"
                      }`}
                  >
                    <div className="relative overflow-hidden duration-300 ease-in-out group min-h-[228px] max-h-[268px] shadow-lg bg">
                      <div>
                        <Link
                          href={`/tophostel?country=${country}`}
                          prefetch={false}
                          className="comman-tooltip"
                        >
                          <Image
                            src={countryImages[country]}
                            width={208}
                            height={362}
                            alt={country}
                            className="object-cover w-full h-full duration-300 ease-in-out opacity-100 group-hover:scale-105"
                            loading="lazy"
                          />
                        </Link>
                        <div className="absolute text-white top-[80%] font-manrope left-5 opacity-1">
                          <h3 className="text-sm sm:text-base font-bold">
                            Hostel in {country}
                          </h3>
                          {popertyCount?.[country] && (
                            <p className="mt-1 text-sm font-normal">
                              {popertyCount[country]} Hostels
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>
          ) : (
            <div className="">
              <div className="flex justify-between flex-col md:flex-row">
                <h2 className="font-semibold text-white  md:text-3xl sm:text-2xl text-xl mb-5  relative z-10">
                  Explore the{" "}
                  <span className="font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl text-primary-blue">
                    World{" "}
                  </span>{" "}

                </h2>
                <Link href="/exploreworld" prefetch={false}>
                  <button className="bg-primary-blue hover:bg-teal-400 h-9 w-24 rounded-3xl text-sm font-semibold mt-5">
                    See All
                  </button>
                </Link>
              </div>

              <div className="z-10">


                <Swiper
                  slidesPerView={"auto"}
                  spaceBetween={15}
                  autoplay={true}
                  loop
                  modules={[Autoplay, Navigation]}
                  // navigation={true}
                  className="mySwiper myCustomSwiper"
                  autoHeight={true}
                  onSlideChange={(swiper) => {
                    // Get currently visible slides
                    const newVisibleSlides = swiper.slides
                      .slice(swiper.activeIndex, swiper.activeIndex + 4) // Select 4 slides in view
                      .map((slide) => slide.getAttribute("data-index")); // Get index of each slide

                    setVisibleSlides(newVisibleSlides);
                  }}
                  breakpoints={{
                    0: { slidesPerView: 1.5, spaceBetween: 20 },
                    640: { slidesPerView: 2 },
                    768: { slidesPerView: 3 },
                    1024: { slidesPerView: 4 },
                  }}
                >
                  {[
                    "India",
                    "Thailand",
                    "Indonesia",
                    "Colombia",
                    "Spain",
                    "Mexico",
                    "Italy",
                    "Portugal",
                    "Brazil",
                    "USA",
                    "Japan",
                    "Vietnam",
                    "France",
                    "Australia",
                    "Peru",
                  ].map((country, index) => (
                    <SwiperSlide
                      key={index}
                      data-index={index} // Set index as data attribute
                      className={`${visibleSlides.indexOf(index.toString()) === 1 ||
                        visibleSlides.indexOf(index.toString()) === 3
                        ? "mt-5 py-10"
                        : "mt-0"
                        }`}
                    >
                      <div className="relative overflow-hidden duration-300 ease-in-out group min-h-40 shadow-lg">
                        <div>
                          <Link
                            href={`/tophostel?country=${country}`}
                            prefetch={false}
                            className="comman-tooltip"
                          >
                            <Image
                              src={countryImages[country]}
                              width={208}
                              height={362}
                              alt={country}
                              className="object-cover w-full h-full duration-300 ease-in-out  group-hover:scale-105 bg-white opacity-100"
                              loading="lazy"
                            />
                          </Link>
                          <div className="absolute text-white top-[83%] font-manrope left-5 opacity-1">
                            <h3 className="text-sm 2xl:text-xl font-bold">
                              Hostel in {country}
                            </h3>
                            {popertyCount?.[country] && (
                              <p className="mt-1 text-xs  2xl:text-sm font-normal">
                                {popertyCount[country]} Hostels
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </SwiperSlide>
                  ))}
                </Swiper>
              </div>
            </div>
          )}

          {/* Custom navigation icons */}
        </div>
      </section>
      <div className="bg-white flex flex-col z-0 relative">
        {/* <motion.h1
          initial={{ x: -100, opacity: 0 }}
          whileInView={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          viewport={{ once: false }} */}
        <h1
          className="relative z-10 w-full lg:w-[40%] text-left lg:text-right font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl mb-4 md:mb-0 mt-10"
        >
          <span className="text-primary-blue font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl">
            Expand
          </span>{" "}
          your{" "}
          <span className="font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl ">
            Network
          </span>
        </h1>
        {/* </motion.h1> */}
        <div className="flex  justify-between flex-col-reverse md:flex-row md:justify-around lg:justify-between">
          {/* Left Side Content */}

          {/* Background gradient circles */}
          <div className="absolute top-0 left-[20%] w-40 h-40 bg-cyan-400 rounded-full blur-2xl opacity-30"></div>
          <div className="absolute top-1 left-[33%] w-40 h-40 bg-pink-400 rounded-full blur-2xl opacity-30"></div>
          <div className="absolute top-1 left-[10%] w-40 h-40 bg-yellow-300 rounded-full blur-2xl opacity-40"></div>
          <div className="absolute bottom-1/4 -left-2 w-36 h-36 bg-cyan-400 rounded-full blur-2xl opacity-30"></div>
          <div className="absolute bottom-[48%] left-[15%] w-36 h-36 bg-yellow-300 rounded-full blur-xl opacity-30"></div>
          <div className="absolute bottom-[7%] right-[29%] w-40 h-40 bg-cyan-400 rounded-full blur-2xl opacity-30"></div>
          <div className="absolute bottom-[23%] right-[4%] w-40 h-40 bg-pink-400 rounded-full blur-xl opacity-30"></div>

          <div className="w-full flex flex-wrap justify-end items-start lg:justify-end  lg:items-end md:hidden">

            <div
              // initial={{ x: -100, opacity: 0 }}
              // whileInView={{ x: 0, opacity: 1 }}
              // transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
              // viewport={{ once: false }}
              onClick={() => setCurrentImage(0)}
              className={`relative p-5 rounded-xl w-full max-w-[385px] h-auto min-h-[147px] ${currentImage === 0
                ? "border-2 border-dashed border-primary-blue shadow-xl"
                : "border border-gray-300 shadow-none"
                }`}
            >
              <h1 className="sr-only">Main Page Title</h1>
              <h2 className="sr-only">Section Title</h2>
              <h3 className="mb-1 flex items-center text-lg font-manrope font-extrabold cursor-pointer">
                Book your hostels
              </h3>
              <p className="text-xxs md:text-xs text-gray-600 font-manrope cursor-pointer line-clamp-4">
                Top Hostel Booking Made Easy with Mixdorm
                Explore and book hostels, dormitories, and budget hotels around the world — from solo
                backpacker spots to vibrant youth hostels in top cities. Whether you're looking for the
                cheapest accommodation, a cozy hostel stay, or social backpackers hostels, Mixdorm
                helps you find the perfect match.
                Start your hostel booking in India or across the globe with real traveler reviews, smart
                filters, and seamless online booking.
                From hidden gems to popular picks, discover hostel listings tailored for travelers like you.

              </p>
              {/* <motion.div
                className="absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center"
                animate={currentImage === 0 ? { x: [0, 100, 0] } : { x: 0 }} // animate only if currentImage is 0
                transition={{
                  duration: 2,
                  repeat: currentImage === 0 ? Infinity : 0,
                  ease: "easeInOut"
                }}
              >
                <FaArrowRight className="text-black" />
              </motion.div> */}
                <div
                  className="absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center"
                >
                  <FaArrowRight className="text-black" />
                </div>
            </div>

            {/* Bottom Row Cards */}
            <div className="flex  justify-start items-stretch gap-6 mt-8 w-full">
              {/* Noticeboard Card */}
              <div
                // initial={{ x: -100, opacity: 0 }}
                // whileInView={{ x: 0, opacity: 1 }}
                // transition={{ duration: 0.8, ease: "easeOut", delay: 0.9 }}
                // viewport={{ once: false }}
                onClick={() => setCurrentImage(1)}
                className={`relative p-5 border rounded-xl w-full max-w-[280px]  ${currentImage === 1
                  ? "border-2 border-dashed border-primary-blue shadow-xl"
                  : "border border-gray-300 shadow-none"
                  }`}
              >
                <h3 className="mb-1 flex items-center text-lg font-manrope font-extrabold cursor-pointer">
                  Noticeboard
                </h3>
                <p className="text-xxs md:text-xs text-gray-600 font-manrope cursor-pointer">
                  AI-powered social noticeboard keeps you updated on trending stays, local events, and must-visit spots—helping you connect, explore, and experience more on your journey.
                </p>
                {/* <motion.div
                  className="absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center"
                  animate={currentImage === 1 ? { x: [0, 100, 0] } : { x: 0 }} // animate only if currentImage is 0
                  transition={{
                    duration: 2,
                    repeat: currentImage === 1 ? Infinity : 0,
                    ease: "easeInOut"
                  }}
                >
                  <FaArrowRight className="text-black" />
                </motion.div> */}
                 <div
                  className="absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center"
                >
                  <FaArrowRight className="text-black" />
                </div>
              </div>

              {/* Events Spotlight Card */}
              <div
                // initial={{ x: -100, opacity: 0 }}
                // whileInView={{ x: 0, opacity: 1 }}
                // transition={{ duration: 0.8, ease: "easeOut", delay: 0.6 }}
                // viewport={{ once: false }}
                onClick={() => setCurrentImage(2)}
                className={`relative p-4 border rounded-xl max-w-[217px] ${currentImage === 2
                  ? "border-2 border-dashed border-primary-blue shadow-xl"
                  : "border border-gray-300 shadow-none"
                  }`}
              >
                <h3 className="mb-1 flex items-center text-lg font-manrope font-extrabold cursor-pointer whitespace-nowrap">
                  Events Spotlight
                </h3>
                <p className="text-xxs md:text-xs text-gray-600 font-manrope cursor-pointer line-clamp-5">
                  Explore Top-Rated Hostels Around the World
                  Discover the best hostels and dormitory stays offering fun events, social vibes, and
                  unforgettable travel experiences. From budget-friendly hostels to lively backpackers'
                  stays, find your perfect match for adventure, comfort, and connection.
                </p>
                {/* <motion.div
                  className="absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center"
                  animate={currentImage === 2 ? { x: [0, 100, 0] } : { x: 0 }} // animate only if currentImage is 0
                  transition={{
                    duration: 2,
                    repeat: currentImage === 2 ? Infinity : 0,
                    ease: "easeInOut"
                  }}
                >
                  <FaArrowRight className="text-black" />
                </motion.div> */}
                  <div
                  className="absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center"
                >
                  <FaArrowRight className="text-black" />
                </div>
              </div>
            </div>
          </div>
          <div className="w-full hidden md:flex flex-col justify-end items-start lg:items-end md:w-[90%] lg:w-[45%] mb-16 px-4 lg:px-0">
            {/* Top Card */}
            <div
              // initial={{ x: -100, opacity: 0 }}
              // whileInView={{ x: 0, opacity: 1 }}
              // transition={{ duration: 0.8, ease: "easeOut", delay: 0.3 }}
              // viewport={{ once: false }}
              // ref={ref1}
              onClick={() => handleClick(0)}
              className={`relative p-5 rounded-xl w-full max-w-[385px] h-auto min-h-[147px] ${currentImage === 0
                ? "border-2 border-dashed border-primary-blue shadow-xl"
                : "border border-gray-300 shadow-none"
                }`}
            >
              <h1 className="sr-only">Main Page Title</h1>
              <h2 className="sr-only">Section Title</h2>
              <h3 className="mb-2 flex items-center text-lg font-manrope font-extrabold cursor-pointer">
                Book your hostels
              </h3>
              <p className="text-sm text-gray-600 font-manrope cursor-pointer line-clamp-4">
                Top Hostel Booking Made Easy with <Link href={"/owner/about-mixdorm"} className="text-primary-blue">Mixdorm </Link>
                Explore and book hostels, dormitories, and budget hotels around the world — from solo
                backpacker spots to vibrant youth hostels in top cities. Whether you're looking for the
                cheapest accommodation, a cozy hostel stay, or social backpackers hostels, Mixdorm
                helps you find the perfect match.
                Start your hostel booking in India or across the globe with real traveler reviews, smart
                filters, and seamless online booking.
                From hidden gems to popular picks, discover hostel listings tailored for travelers like you.

              </p>
              {/* <motion.div
                className="absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center"
                animate={currentImage === 0 ? { x: [0, 100, 0] } : { x: 0 }} // animate only if currentImage is 0
                transition={{
                  duration: 2,
                  repeat: currentImage === 0 ? Infinity : 0,
                  ease: "easeInOut"
                }}
              >
                <FaArrowRight className="text-black" />
              </motion.div> */}
                <div
                  className="absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center"
                >
                  <FaArrowRight className="text-black" />
                </div>
            </div>

            {/* Bottom Row Cards */}
            <div className="flex flex-col sm:flex-row justify-start items-stretch gap-6 mt-8 w-full">
              {/* Noticeboard Card */}
              <div
                // initial={{ x: -100, opacity: 0 }}
                // whileInView={{ x: 0, opacity: 1 }}
                // transition={{ duration: 0.8, ease: "easeOut", delay: 0.9 }}
                // viewport={{ once: false }}
                // ref={ref2}
                onClick={() => handleClick(1)}
                className={`relative p-5 border rounded-xl w-full max-w-[330px] ${currentImage === 1
                  ? "border-2 border-dashed border-primary-blue shadow-xl"
                  : "border border-gray-300 shadow-none"
                  }`}
              >
                <h3 className="mb-2 flex items-center text-lg font-manrope font-extrabold cursor-pointer">
                  Noticeboard
                </h3>
                <p className="text-sm text-gray-600 font-manrope cursor-pointer">
                  <Link href={"/meetbuddies"} className="text-primary-blue">AI-powered social noticeboard</Link> keeps you updated on trending stays, local events, and must-visit spots—helping you connect, explore, and experience more on your journey.
                </p>
                {/* <motion.div
                  className="absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center"
                  animate={currentImage === 1 ? { x: [0, 100, 0] } : { x: 0 }} // animate only if currentImage is 0
                  transition={{
                    duration: 2,
                    repeat: currentImage === 1 ? Infinity : 0,
                    ease: "easeInOut"
                  }}
                >
                  <FaArrowRight className="text-black" />
                </motion.div> */}
                  <div
                  className="absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center"
                >
                  <FaArrowRight className="text-black" />
                </div>
              </div>

              {/* Events Spotlight Card */}
              <div
                // initial={{ x: -100, opacity: 0 }}
                // whileInView={{ x: 0, opacity: 1 }}
                // transition={{ duration: 0.8, ease: "easeOut", delay: 0.6 }}
                // viewport={{ once: false }}
                // ref={ref3}
                onClick={() => handleClick(2)}
                className={`relative p-4 border rounded-xl w-full sm:max-w-[217px] ${currentImage === 2
                  ? "border-2 border-dashed border-primary-blue shadow-xl"
                  : "border border-gray-300 shadow-none"
                  }`}
              >
                <h3 className="mb-2 flex items-center text-lg font-manrope font-extrabold cursor-pointer">
                  Events Spotlight
                </h3>
                <p className="text-sm text-gray-600 font-manrope cursor-pointer line-clamp-5">
                  Explore Top-Rated Hostels Around the World
                  Discover the best hostels and dormitory stays offering fun events, social vibes, and
                  unforgettable travel experiences. From budget-friendly hostels to lively backpackers'
                  stays, find your perfect match for adventure, comfort, and connection.
                </p>
                {/* <motion.div
                  className="absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center"
                  animate={currentImage === 2 ? { x: [0, 100, 0] } : { x: 0 }} // animate only if currentImage is 0
                  transition={{
                    duration: 2,
                    repeat: currentImage === 2 ? Infinity : 0,
                    ease: "easeInOut"
                  }}
                >
                  <FaArrowRight className="text-black" />
                </motion.div> */}
                  <div
                  className="absolute -top-4 left-8 h-9 w-9 bg-primary-blue rounded-full flex items-center justify-center"
                >
                  <FaArrowRight className="text-black" />
                </div>
              </div>
            </div>
          </div>


          <div className="flex justify-center w-full sm:w-[90%] md:w-[70%] lg:w-[30%]">
            <div
              // initial={{ y: 80, opacity: 0 }}
              // whileInView={{ y: 0, opacity: 1 }}
              // transition={{
              //   type: "spring",
              //   stiffness: 120,
              //   damping: 12,
              //   duration: 0.8,
              //   delay: 0.2,
              // }}
              // viewport={{ once: false }}
              className="relative w-full max-w-[327px] h-[670px] rounded-3xl p-4"
            >
              <div className="relative">
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Phone-image.webp`}
                  width={327}
                  height={670}
                  alt="Mobile UI Mockup"
                  className="rounded-3xl w-full h-auto"
                />

                <div className="absolute rounded-3xl top-2 left-3 w-[100%] md:w-[90%] max-w-[270px] overflow-hidden">
                  <div
                    className={`relative w-full rounded-3xl h-[660px] flex transition-transform duration-500 ease-in-out ${direction === "right"
                      ? "translate-x-0"
                      : "-translate-x-full"
                      }`}
                  >
                    {images.map((img, index) => (

                      <div key={index} className="overflow-hidden">

                        {!imageLoaded[index] && (
                          <div className="w-full max-h-[560px] h-[560px] bg-slate-200 animate-pulse rounded-3xl z-10 flex items-center justify-center absolute top-0 left-0">
                            <h1 className="text-white font-bold font-manrope text-center">MixDorm</h1>  
                          </div>
                        )}

                        <Image
                          key={index}
                          src={img}
                          width={320}
                          height={660}
                          alt="Mobile UI Mockup"
                          onLoad={() => handleImageLoad(index)}
                          className={`absolute rounded-3xl transition-transform duration-500 ${index === currentImage
                            ? "translate-x-0 opacity-100"
                            : "translate-x-full opacity-0"
                            }`}
                        />
                      </div>
                    ))}
                  </div>
                </div>


              </div>
            </div>
          </div>

          {/* Right Side Mobile UI */}

          <div className="hidden lg:flex justify-start w-[30%]">
            <div className="flex flex-col justify-evenly ">
              <div
                // initial={{ x: 100, opacity: 0 }}
                // whileInView={{ x: 0, opacity: 1 }}
                // transition={{ duration: 0.8, ease: "easeOut" }}
                // viewport={{ once: false }}
                className="pb-56">
                <p className="text-gray-600 text-[15px] font-manrope text-justify" >
                  Find <Link href={"/tophostel?country=India"} className="text-primary-blue" >Top-Rated Hostels</Link> Worldwide for Every Kind of Traveler
                  Explore the best hostel stays across the globe — perfect for solo travelers, backpackers,
                  digital nomads, and budget-conscious explorers. From vibrant social hostels in major cities
                  to peaceful dorms in nature getaways, our curated <Link href={"/search"} className="text-primary-blue" > hostel listings</Link> offer unique stays on every
                  continent.
                  <Link href={"/search"} className="text-primary-blue"> Search hostels</Link> by destination, book instantly, and connect with travelers worldwide.
                  Start Your Hostel Adventure Now
                  
                </p>
                <button className="bg-primary-blue text-black py-2.5 px-5 rounded-full shadow-lg transition text-sm font-manrope font-semibold mt-4" >
                  <Link href={"/exploreworld"} >
                  Explore World </Link>
                </button>
              </div>

              {/* QR Code Section */}
              {/* <motion.div
                initial={{ y: 80, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                transition={{
                  type: "spring",
                  stiffness: 120,
                  damping: 10,
                  duration: 0.8,
                  delay: 0.2,
                }}
                viewport={{ once: false }}
                className="mb-10 text-center flex items-end justify-end w-full"
              >
                <div className="bg-gray-200 animate-bounce shadow-xl shadow-black/40 p-4 rounded-lg inline-block w-[155px] h-[147px] relative">
                  <p className="text-black text-base font-manrope font-extrabold">
                    Scan Here
                  </p>
                  <div className="absolute h-[102px] w-[102px] left-7">
                    {!qrLoaded && (
                      <div className="w-full h-full bg-slate-200 animate-pulse rounded-lg absolute top-0 left-0 z-10" />
                    )}
                    <Image
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/QR.webp`}
                      width={100}
                      height={100}
                      onLoad={() => setQrLoaded(true)}
                      alt="QR Code"
                      className={`rounded-lg transition-opacity duration-300 ${
                        qrLoaded ? "opacity-100" : "opacity-0"
                      }`}
                      style={{ height: "100%" , width: "100%" }}
                    />
                  </div>
                </div>

              </motion.div> */}


               <div
                className="mb-10 text-center flex items-end justify-end w-full"
              >
                <div className="bg-gray-200  shadow-xl shadow-black/40 p-4 rounded-lg inline-block w-[155px] h-[147px] relative bottom-12">
                  <p className="text-black text-base font-manrope font-extrabold">
                    Scan Here
                  </p>
                  <div className="absolute h-[102px] w-[102px] left-7">
                    {!qrLoaded && (
                      <div className="w-full h-full bg-slate-200 animate-pulse rounded-lg absolute top-0 left-0 z-10" />
                    )}
                    <Image
                      src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/QR.webp`}
                      width={100}
                      height={100}
                      onLoad={() => setQrLoaded(true)}
                      alt="QR Code"
                      className={`rounded-lg transition-opacity duration-300 ${
                        qrLoaded ? "opacity-100" : "opacity-0"
                      }`}
                      style={{ height: "100%" , width: "100%" }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>
    </div>
  );
};

export default ExpandPage;
