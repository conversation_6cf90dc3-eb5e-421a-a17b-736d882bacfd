/* eslint-disable react/no-unescaped-entities */
import React, { useEffect, useRef, useState } from "react";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation } from "swiper/modules";
import Link from "next/link";
import { eventApi } from "@/services/webflowServices";
import { useNavbar } from "./navbarContext";
import "swiper/css/navigation";
import countries from "world-countries";
// import { motion } from "framer-motion";
import { ArrowLeft, ArrowRight } from "lucide-react";

const EventSpotlight = () => {
  // eslint-disable-next-line no-unused-vars
  const [selectedImage, setSelectedImage] = useState(1);
  const [activityData, setActivityData] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [currentPage, setCurrentPage] = useState(1);
  // eslint-disable-next-line no-unused-vars
  const [totalPages, setTotalPages] = useState(1);
  const [currencyData, setCurrencyData] = useState({});
  const limit = 10;
  const isFirstRender = useRef(null);
  const { token } = useNavbar();
  const [isLoading, setIsLoading] = useState(true);
  const [imgLoaded, setImgLoaded] = useState(false);
  const [imgError, setImgError] = useState(false);

  // Init arrays if not set (especially after data fetch)
  useEffect(() => {
    if (activityData.length > 0) {
      setImgLoaded(Array(activityData.length).fill(false));
      setImgError(Array(activityData.length).fill(false));
    }
  }, [activityData]);
  // const [isMobile, setIsMobile] = useState(false);

  // useEffect(() => {
  //   const checkMobile = () => {
  //     setIsMobile(typeof window !== 'undefined' && window.innerWidth <= 768);
  //   };

  //   // Initial check
  //   checkMobile();

  //   // Add resize listener
  //   if (typeof window !== 'undefined') {
  //     window.addEventListener('resize', checkMobile);
  //     return () => window.removeEventListener('resize', checkMobile);
  //   }
  // }, []);

  const profileImages = [
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/usericon1.jpeg`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/usericon2.jpeg`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/usericon3.jpeg`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/usericon4.jpeg`,
  ];
  const flagImages = [
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/flag14.png`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/flag15.png`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/flag13.png`,
    `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/flag11.png`,
  ];

  useEffect(() => {
    const fetchActivityData = async () => {
      try {
        const response = await eventApi(currentPage, limit);

        setActivityData(response?.data?.data?.events || []);
        setTotalPages(response?.data?.data?.pagination?.totalPages || 1);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching event data:", error);
      }
    };
    if (!isFirstRender.current) {
      fetchActivityData();
    } else {
      isFirstRender.current = false;
    }
  }, [currentPage, token]);

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);
        setIsLoading(false);

      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);

  const getCurrencySymbol = (currencyCode) => {
    return currencyData[currencyCode] || currencyCode;
  };

  return (
    <div
      className="py-8 md:py-16 px-0 md:px-6 relative bg-repeat-round w-full"
      style={{
        backgroundImage: `url(${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/left-cross-bg.webp)`,
        backgroundSize: "cover",
      }}
    >
      <section className="w-full xs:py-10 py-0">
        <div className="container relative">
          <div className="flex justify-between items-center my-8">
            {/* <motion.h2
              initial={{ x: -100, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              viewport={{ once: false }} */}
            <h2
              className=" text-white font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl"
            >
              <span className="text-primary-blue font-mashiny font-normal xs:text-4xl text-3xl md:text-5xl">
                Events{" "}
              </span>
              Spotlight
            </h2>
            {/* </motion.h2> */}
            {/* <motion.div
              initial={{ x: 100, opacity: 0 }}
              whileInView={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              viewport={{ once: false }} */}
            <div
              className="text-center sm:hidden block"
            >
              <Link
                href="/discover-event"
                aria-label="See all discoverable events"
                className="text-sm font-semibold text-black bg-primary-blue rounded-4xl py-2 px-5 hover:bg-sky-blue-750"
                prefetch={false}
              >
                See All
              </Link>
            </div>
            {/* </motion.div> */}
            <div
              className={`gap-2 ${
                activityData?.length > 4 ? "xl:flex" : "xl:hidden"
              } ${activityData?.length > 3.5 ? "lg:flex" : "lg:hidden"} ${
                activityData?.length > 2.5 ? "md:flex" : "md:hidden"
              } ${activityData?.length > 2 ? "sm:flex" : "sm:hidden"} ${
                activityData?.length > 1.5 ? "hidden" : "hidden"
              }`}
            >
              <div className="slider-button-prev cursor-pointer custom-nav"><ArrowLeft size={18} /></div>
              <div className="slider-button-next cursor-pointer custom-nav"><ArrowRight size={18} /></div>
            </div>
          </div>
          {/* <motion.p
            initial={{ x: -100, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: false }} */}
          <p
            className="hidden sm:block mt-2 text-base font-medium text-white/60 font-manrope mb-2 w-[80%]"
          >
            Discover top-rated hostels across the globe that go beyond just a bed — offering <Link href={"/discover-event"} className="text-primary-blue"> social
            events, </Link> cultural experiences, and unforgettable moments. Whether you're after a lively
            party hostel, a peaceful retreat, or an adventure-packed base, these featured hostels
            combine affordability, comfort, and community vibes.
            Explore our top 8 hostels known for hosting local events, pub crawls, cooking nights, and
            more — all with unique amenities and budget-friendly rates.
            Join the fun, meet fellow solo backpackers, and make every night count — only with
            Mixdorm
          </p>
          {/* </motion.p> */}
          {/* <motion.div
            initial={{ y: 80, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: false }}
          > */}
          <div>
            <Swiper
              modules={[Autoplay, Navigation]}
              // autoplay={{ delay: 1500, pauseOnMouseEnter: true }}
              slidesPerView={18}
              navigation={{
                prevEl: ".slider-button-prev",
                nextEl: ".slider-button-next",
              }}
              loop
              speed={1000}
              spaceBetween={12}
              className="mySwiper overflow-hidden py-4"
              breakpoints={{
                0: {
                  slidesPerView: 1.5,
                  spaceBetween: 10,
                },
                480: {
                  slidesPerView: 1.4,
                  spaceBetween: 10,
                },
                640: {
                  slidesPerView: 2,
                  spaceBetween: 10,
                },

                768: {
                  slidesPerView: 2.5,
                  spaceBetween: 20,
                },

                1024: { slidesPerView: 3.5 },

                1280: { slidesPerView: 4 },
              }}
            >

          {isLoading
            ? Array.from({ length: 4 }).map((_, index) => (
                <SwiperSlide key={`skeleton-${index}`}>
                  <div className="relative w-full animate-pulse bg-gray-300 z-20">
                    <div className="absolute right-[14px] top-[14px] p-2 bg-gray-300 text-transparent rounded-md w-12 h-12"></div>

                    <div className="bg-black/40 bottom-0 absolute left-0 w-full py-4 px-4 z-10">
                      <div className="w-full">
                        <div className="flex gap-3 items-center mb-2">
                          <div className="flex -space-x-1">
                            {[1, 2, 3].map((_, i) => (
                              <div key={i} className="w-[26px] h-[26px] bg-gray-300 rounded-full border border-white" />
                            ))}
                          </div>
                          <p className="text-sm bg-gray-300 text-transparent rounded-md w-20 h-4"></p>
                        </div>
                        <p className="uppercase text-white font-roboto font-normal text-sm my-3">SHOW</p>
                        <div className="w-full flex justify-between items-start">
                          <div className="bg-gray-300 w-[60%] h-6 rounded-md"></div>
                          <div className="bg-gray-300 w-[20%] h-6 rounded-md"></div>
                        </div>
                      </div>
                    </div>

                    <div className="xl:min-h-[410px] lg:min-h-[380px] md:min-h-[340px] xs:min-h-[310px] min-h-[280px] bg-gray-300 border-[6px] border-slate-100"></div>
                  </div>
                </SwiperSlide>
              ))
            : 
              activityData.map((slide, index) => (
                <SwiperSlide
                  key={slide?._id}
                  className=""
                  onClick={() => setSelectedImage(index)}
                >
                  
                  <div className="relative w-full ease-in-out transition-all duration-300 hover:scale-105">
                    <div className="text-black font-thin text-center absolute right-[14px] top-[14px] p-2 bg-[#40E0D0] uppercase text-[13px] sm:w-[60px] sm:h-[60px] w-12 z-10">
                      {new Date(slide?.startDate)
                        .toLocaleString("en-US", { month: "short" })
                        .toUpperCase()}
                      <span className="block sm:text-[26px] text-lg font-bold sm:leading-5 leading-3">
                        {new Date(slide?.startDate).getDate()}
                      </span>
                    </div>
                    <div className="bg-black/40 bottom-0 absolute left-0 w-full py-4 sm:px-6 px-4 z-10">
                      <div className="w-full">
                        <div className="flex gap-3 items-center">
                          <Link href={"/membership"}>
                            <div className="flex -space-x-1">
                              {profileImages.map((profileSrc, i) => (
                                <div key={i} className="relative">
                                  <div className="relative w-[26px] h-[26px] 2xl:w-[32px] 2xl:h-[32px]">
                                    <Image
                                      src={profileSrc}
                                      alt="User"
                                      fill
                                      loading="lazy"
                                      className={`rounded-full border border-white object-cover ${isLoading ? "bg-slate-200 animate-pulse" : "bg-slate-200"}`}
                                      sizes="(min-width: 1536px) 32px, 26px"
                                    />
                                  </div>
                                  <span className="absolute -bottom-1 left-[16px] 2xl:left-[18px] z-10 w-[12px] h-[12px] 2xl:w-[14px] 2xl:h-[14px]">
                                    <Image
                                      src={flagImages[i]}
                                      alt="Flag"
                                      fill
                                      loading="lazy"
                                      className="rounded-full object-cover"
                                      sizes="(min-width: 1536px) 14px, 12px"
                                  />
                                  </span>
                                  {i === 3 && (
                                    <span className="absolute top-0 left-0 w-full h-full rounded-full overflow-hidden z-0 cursor-pointer border">
                                      {/* Background blur layer */}
                                      <span className="absolute inset-0 backdrop-blur-sm z-0" />

                                      {/* Foreground text layer */}
                                      <span className="relative flex items-center justify-center w-full h-full z-10 text-sm text-white font-extrabold">
                                        +4
                                      </span>
                                    </span>
                                  )}
                                </div>
                              ))}
                            </div>
                          </Link>
                          <p className="text-white text-sm font-roboto">20+ Going</p>
                        </div>
                        <p className="uppercase text-white font-roboto font-normal text-sm my-3">
                          SHOW
                        </p>
                        <div className="w-full flex mt-0 justify-between items-start xl:items-start relative">
                          <h2 className="">
                            <div
                              className="comman-tooltip before:!top-[-30px] before:!left-[50%]"
                              data-title={slide?.title}
                            >
                              <span className="md:text-2xl xs:text-lg text-md font-normal font-roboto text-white line-clamp-1">
                                {slide?.title}
                              </span>
                            </div>
                          </h2>
                          <h2 className="text-2xl font-normal font-roboto text-white text-end ">
                            {getCurrencySymbol(slide?.currency || "USD")}
                            {slide?.price}
                          </h2>
                        </div>
                      </div>
                    </div>
                    {(!imgLoaded[index] || imgError[index]) && (
                      <div className="absolute inset-0 bg-slate-200 xl:min-h-[410px] lg:min-h-[380px] md:min-h-[340px] xs:min-h-[310px] min-h-[280px] flex items-center justify-center">
                        <h1 className="text-white font-bold font-manrope">MixDorm</h1>
                      </div>
                    )}
                    <Image
                      src={
                        slide?.attachment?.[0]?.url ||
                        `${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/event_1.jpg`
                      }
                      alt={`Slide ${slide?._id}`}
                      width={402}
                      height={409}
                      quality={90}
                      loading="lazy"
                      sizes="(max-width: 480px) 90vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 402px"
                      className="xl:min-h-[410px] lg:min-h-[380px] md:min-h-[340px] xs:min-h-[310px] min-h-[280px] object-cover border-[6px] border-slate-105 bg-slate-200"
                      onLoad={() =>
                        setImgLoaded((prev) => {
                          const updated = [...prev];
                          updated[index] = true;
                          return updated;
                        })
                      }
                      onError={() => {
                        setImgError((prev) => {
                          const updated = [...prev];
                          updated[index] = true;
                          return updated;
                        });
                        setImgLoaded((prev) => {
                          const updated = [...prev];
                          updated[index] = true;
                          return updated;
                        });
                      }}
                    />
                  </div>
                </SwiperSlide>

              ))
            }

            </Swiper>
          </div>
          {/* </motion.div> */}

          {/* <motion.div
            initial={{ y: 80, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            viewport={{ once: false }} */}
          <div
            className="text-center mt-5 sm:flex hidden justify-center items-center gap-2"
          >
            <Link
              href="/discover-event"
              aria-label="See all discoverable events"
              className="text-sm font-semibold text-black bg-primary-blue rounded-4xl py-4 px-12 hover:bg-sky-blue-750"
              prefetch={false}
            >
              See All
            </Link>
          </div>
          {/* </motion.div> */}
        </div>
      </section>
    </div>
  );
};

export default EventSpotlight;
