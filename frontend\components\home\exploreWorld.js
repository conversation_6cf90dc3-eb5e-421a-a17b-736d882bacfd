import React, { useEffect, useRef, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import Image from "next/image";
import Link from "next/link";
import { Autoplay } from "swiper/modules";
import "swiper/css/navigation";
import { getHomePagePropertyCountApi } from "@/services/webflowServices";
import countries from "world-countries";

// import India from "../../public/image-india.png";

const ExploreWorld = () => {
  const [flags, setFlags] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [popertyCount, setPopertyCount] = useState([]);
  const isFirstRender = useRef(null);
  // eslint-disable-next-line no-unused-vars
  const [isMobile, setIsMobile] = useState(false);

  const CountryList = [
    "India",
    "Thailand",
    "Indonesia",
    "Colombia",
    "Spain",
    "Mexico",
    "Italy",
    "Portugal",
    "Brazil",
    "USA",
    "Japan",
    "Vietnam",
    "France",
    "Australia",
    "Peru",
  ];

  useEffect(() => {
    const fetchPropertyCount = async () => {
      try {
        const response = await getHomePagePropertyCountApi({
          countries: CountryList,
        });
        setPopertyCount(response?.data?.data?.propertyCounts || []);
      } catch (error) {
        console.error("Error fetching stay data:", error);
      } finally {
        /* empty */
      }
    };
    if (!isFirstRender.current) {
      fetchPropertyCount();
    } else {
      isFirstRender.current = false;
    }
  }, []);

  const countriesForFlag = [
    "India",
    "Thailand",
    "Indonesia",
    "Colombia",
    "Spain",
    "Mexico",
    "Italy",
    "Portugal",
    "Brazil",
    "United States",
    "Japan",
    "Vietnam",
    "France",
    "Australia",
    "Peru",
  ];

  useEffect(() => {
    const fetchFlags = () => {
      try {
        const filteredFlags = countries
          .filter((country) => {
            const commonName = country.name.common;
            // Check if commonName is in the list or mapped name is in the list
            return countriesForFlag.includes(commonName);
          })
          .map((country) => ({
            id: country.cca3,
            img: `https://flagcdn.com/w320/${country.cca2.toLowerCase()}.png`, // Using flagcdn for PNG format
            name:
              country.name.common === "United States"
                ? "USA"
                : country.name.common,
          }));

        setFlags(filteredFlags);
      } catch (error) {
        console.error("Error fetching flags:", error);
      }
    };

    fetchFlags();
  }, []);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(typeof window !== "undefined" && window.innerWidth <= 768);
    };

    // Initial check
    checkMobile();

    // Add resize listener
    if (typeof window !== "undefined") {
      window.addEventListener("resize", checkMobile);
      return () => window.removeEventListener("resize", checkMobile);
    }
  }, []);

  return (
    <>
      <section className='w-full bg-transparent md:pb-0 pb-0 relative sm:mt-6 mt-1 z-10'>
        <div className='lg:px-6 sm:mb-2'>
          <div className='w-full container'>
            <div className='w-full sm:pt-4 arrow_top'>
              <Swiper
                autoplay={
                  window.innerWidth < 640
                    ? {
                        delay: 3000,
                        disableOnInteraction: false,
                      }
                    : false
                }
                modules={[Autoplay]}
                loop
                slidesPerView={15}
                speed={1000}
                spaceBetween={10}
                className='mySwiper myCustomSwiper'
                breakpoints={{
                  0: { slidesPerView: 5 },
                  400: { slidesPerView: 6 },
                  640: { slidesPerView: 6 },
                  768: { slidesPerView: 8 },
                  1024: { slidesPerView: 15 },
                }}
              >
                {flags.map((item) => (
                  <SwiperSlide key={item.id} className='py-3'>
                    <Link
                      key={item.id}
                      href={`/tophostel?country=${item.name}`}
                      prefetch={false}
                      className="text-white hover:text-primary-blue"
                    >
                      <Image
                        src={item.img}
                        alt='Explore More Spend Less'
                        width={50}
                        height={50}
                        title='Explore More Spend Less'
                        className='object-cover sm:w-[50px] w-10 h-10 sm:h-[50px] mx-auto duration-300 ease-in-out rounded-full sm:hover:scale-125 hover:scale-150'
                        loading='lazy'
                      />
                      <div className='text-xs sm:text-sm font-medium mt-2 text-center transition-colors mb-0 font-poppins'>
                        {item.name}
                      </div>
                    </Link>
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default ExploreWorld;
