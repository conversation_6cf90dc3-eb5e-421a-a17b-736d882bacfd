import React, { useEffect, useRef, useState } from "react";
import { Checkbox, FormControlLabel } from "@mui/material";
import dynamic from "next/dynamic";
import axios from "axios";
import Loader from "../loader/loader";

const Rewardspopup = dynamic(() => import("../model/rewardspopup"), {
  ssr: false,
});

const AddAmount = () => {
  const [openRewardspopup, setopenRewardspopup] = useState(false);
  const handleOpenRewardspopup = () => setopenRewardspopup(true);
  const handleCloseRewardspopup = () => setopenRewardspopup(false);

  const [currencies, setCurrencies] = useState([]);
  const [formData, setFormData] = useState({
    currency: "",
    amount: "",
    conversionRate: "",
    conversionFee: "",
    agreeToTerms: false,
  });

  const [errors, setErrors] = useState({});
  const [totalAmount, setTotalAmount] = useState(0);
  const [conversionFee, setConversionFee] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const isCurrenciesFetched = useRef(false);

  useEffect(() => {
    if (!isCurrenciesFetched.current) {
      const fetchCurrencies = async () => {
        setIsLoading(true);
        try {
          const response = await axios.get(
            "https://api.exchangerate-api.com/v4/latest/USD"
          );
          if (response.data) {
            const currencyList = Object.keys(response.data.rates);
            setCurrencies(currencyList);
          }
        } catch (error) {
          console.error("Error fetching currencies:", error);
        } finally {
          setIsLoading(false);
        }
      };
      fetchCurrencies();
      isCurrenciesFetched.current = true;
    }
  }, []);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  useEffect(() => {
    if (formData.amount && formData.conversionRate && formData.conversionFee) {
      const amountInINR =
        parseFloat(formData.amount) * parseFloat(formData.conversionRate);
      const fee =
        (amountInINR * parseFloat(formData.conversionFee)) / 100;
      setConversionFee(fee.toFixed(2));
      setTotalAmount((amountInINR + fee).toFixed(2));
    }
  }, [formData.amount, formData.conversionRate, formData.conversionFee]);

  const validate = () => {
    const newErrors = {};
    if (!formData.currency) newErrors.currency = "Currency is required";
    if (!formData.amount) newErrors.amount = "Amount is required";
    if (!formData.conversionRate)
      newErrors.conversionRate = "Conversion rate is required";
    if (!formData.conversionFee)
      newErrors.conversionFee = "Conversion fee is required";
    if (!formData.agreeToTerms)
      newErrors.agreeToTerms = "You must agree to the terms";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validate()) {
      console.log("Form Submitted", formData);
      handleOpenRewardspopup();
    }
  };

  return (
    <>
      <Loader open={isLoading} />
      <div className="w-full">
        <div className="">
          <h2 className="text-[#40E0D0] flex gap-1 text-2xl font-bold">
            Add
            <span className="text-black ml-0.5"> Amount to Wallet</span>
          </h2>
          <p className="mb-5 text-black text-lg font-semibold">
            Securely add funds to your Mixdorm Wallet and use them across India
          </p>
        </div>
        <div className="max-w-[600px]">
          <h3 className="font-extrabold text-black text-xl">
            Currency and Amount Input
          </h3>
          <div className="mt-3">
            <label className="block text-black text-sm font-bold mb-2 mt-4">
              Currency
            </label>
            <select
              name="currency"
              className={`w-full px-3 py-4 border rounded-4xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500 ${
                errors.currency ? "border-red-500" : ""
              }`}
              value={formData.currency}
              onChange={handleChange}
            >
              <option disabled value="">
                Select Currency
              </option>
              {currencies.map((currency) => (
                <option key={currency} value={currency}>
                  {currency}
                </option>
              ))}
            </select>
            {errors.currency && (
              <p className="text-red-500 text-sm">{errors.currency}</p>
            )}
          </div>
          <div className="mt-3">
            <label className="block text-black text-sm font-bold mb-2 mt-4">
              Amount
            </label>
            <input
              type="text"
              name="amount"
              value={formData.amount}
              onChange={handleChange}
              className={`w-full px-3 py-4 border rounded-4xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500 ${
                errors.amount ? "border-red-500" : ""
              }`}
              placeholder="Enter Amount"
            />
            {errors.amount && (
              <p className="text-red-500 text-sm">{errors.amount}</p>
            )}
          </div>
          <div className="mt-3">
            <label className="block text-black text-sm font-bold mb-2 mt-4">
              Conversion Rate
            </label>
            <input
              type="text"
              name="conversionRate"
              value={formData.conversionRate}
              onChange={handleChange}
              className={`w-full px-3 py-4 border rounded-4xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500 ${
                errors.conversionRate ? "border-red-500" : ""
              }`}
              placeholder="Conversion rate: 1 USD = 83.50"
            />
            {errors.conversionRate && (
              <p className="text-red-500 text-sm">{errors.conversionRate}</p>
            )}
          </div>
          <div className="mt-3">
            <label className="block text-black text-sm font-bold mb-2 mt-4">
              Conversion Fee (%)
            </label>
            <input
              type="text"
              name="conversionFee"
              value={formData.conversionFee}
              onChange={handleChange}
              className={`w-full px-3 py-4 border rounded-4xl focus:outline-none focus:ring-1 focus:ring-teal-500 text-black text-sm placeholder:text-gray-500 ${
                errors.conversionFee ? "border-red-500" : ""
              }`}
              placeholder="Enter Conversion Fee"
            />
            {errors.conversionFee && (
              <p className="text-red-500 text-sm">{errors.conversionFee}</p>
            )}
          </div>
          <div className="flex justify-between mt-3">
            <p className="text-lg font-bold text-black">Total Amount in INR</p>
            <p className="text-lg font-normal text-black/80">{totalAmount}</p>
          </div>
          <div className="flex justify-between mt-2">
            <p className="text-lg font-bold text-black">Conversion Fee</p>
            <p className="text-lg font-normal text-black/80">{conversionFee}</p>
          </div>
          <div className="mt-3">
            <FormControlLabel
              className="text-[#6D6D6D] text-sm font-normal"
              control={
                <Checkbox
                  name="agreeToTerms"
                  checked={formData.agreeToTerms}
                  onChange={handleChange}
                />
              }
              label="I agree to the terms and conditions for currency conversion"
            />
            {errors.agreeToTerms && (
              <p className="text-red-500 text-sm">{errors.agreeToTerms}</p>
            )}
          </div>
          <div className="mt-3">
            <button
              className="w-full py-4 transition-all hover:bg-sky-blue-750 hover:text-white bg-[#EEEEEE] px-4 text-black font-semibold rounded-full"
              onClick={handleSubmit}
            >
              Add Amount
            </button>
          </div>
          <div className="mt-3">
            <p className="text-sm font-normal text-[#6D6D6D]">
              <span className="font-bold text-black">Note :</span>Funds will be
              added in INR, usable across all UPI-supported transactions in
              India.
            </p>
          </div>
        </div>
      </div>
      <Rewardspopup
        openRewardspopup={openRewardspopup}
        handleCloseRewardspopup={handleCloseRewardspopup}
      />
    </>
  );
};

export default AddAmount;
