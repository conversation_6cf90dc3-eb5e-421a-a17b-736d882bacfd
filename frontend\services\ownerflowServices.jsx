import httpServices from "./httpServices";


export const getPropertyListApi=(currentPage,limit)=>{
  return httpServices.get(`/property/my-properties?page=${currentPage}&limit=${limit}`)
}

export const editPropertyApi = (id, payload) => {
  return httpServices.put(`/property/${id}`, payload);
};

export const propertyDetailsApi = (id) => {
  return httpServices.get(`/property/${id}`);
};

export const AddPropertyApi = (payload) => {
  return httpServices.post(`/property/`, payload);
};

export const AddRoomApi = (payload) => {
  return httpServices.post(`/room`, payload);
};

export const RoomListApi = (id, currentPage, propertiesPerPage) => {
  return httpServices.get(
    `/room/all/${id}?page=${currentPage}&limit=${propertiesPerPage}`
  );
};

export const DeleteRoomApi = (slug) => {
  return httpServices.delete(`/room/${slug}`);
};

export const EditRoomApi = (slug, payload) => {
  return httpServices.put(`/room/${slug}`, payload);
};

export const BookingListApi = (id,category, currentPage, propertiesPerPage) => {
  return httpServices.get(
    `/booking/all?property=${id}&category=${category}&page=${currentPage}&limit=${propertiesPerPage}`
  );
};

export const AddBookingApi = (payload) => {
  return httpServices.post(`/booking`, payload);
};

export const EventListApi = (currentPage,limit) => {
  return httpServices.get(
    `/events?page=${currentPage}&limit=${limit}`
  );
};

export const DeleteEventApi = (id) => {
  return httpServices.delete(
    `/events/${id}`
  );
};

export const EditEventApi = (id,payload) => {
  return httpServices.put(
    `/events/${id}`,payload
  );
};

export const GetEventApi = (id) => {
  return httpServices.get(
    `/events/${id}`
  );
};

export const getProfileApi = () => {
  return httpServices.get(`/auth/profile`)
}

export const PaymentAccountDataApi = (id) => {
  return httpServices.get(
    `/transactions/properties?propertyId=${id}`
  );
};

export const PaymentListApi = (id,currentPage,paymentPerpage) => {
  return httpServices.get(
    `/api/payment/${id}?page=${currentPage}&limit=${paymentPerpage}`
  );
};

export const editProfileApi = (payload) => {
  return httpServices.put(`/auth/profile/`,payload)
}

export const getTravellersVisitedApi = (id) => {
  return httpServices.get(`/property/travellers-visited/${id}`)
}

export const verifyPropetyApi = (payload) => {
  return httpServices.post(`/property/check-property`, payload);
};

export const getNoticeApi = () => {
  return httpServices.get(`/noticeboard`)
}

export const saveFirebaseToken=(payload)=>{
  return httpServices.post(`/api/save-fcm-token`,payload)
}

export const removeFirebaseToken=(payload)=>{
  return httpServices.post(`/api/remove-fcm-token`,payload)
}

export const GetAnalyticsApi = (id,startDate,endDate) => {
  return httpServices.get(`/dashboard/${id}?startDate=${startDate}&endDate=${endDate}`)
}

export const GetChatUserApi = (type) => {
  return httpServices.get(`/chats/chat-users?type=${type}`)
}

export const GetUserChatContentApi = (id) => {
  return httpServices.get(`/chats/chat-history/${id}`)
}

export const webCheckInApi = (id, currentPage, propertiesPerPage) => {
  return httpServices.get(
    `/check-in/all/${id}?page=${currentPage}&limit=${propertiesPerPage}`
  );
};

export const addPaymentApi = (payload) => {
  return httpServices.post(`/api/payment/cash-payments`, payload);
};

export const channelManagersListApi = (name) => {
  return httpServices.get(
    `/channel-managers?name=${name}`
  );
};

export const hostRideListApi = (currentPage, ridePerpage) => {
  return httpServices.get(
    `/rides?page=${currentPage}&limit=${ridePerpage}`
  );
};

export const addRideApi = (payload) => {
  return httpServices.post(
    `/rides`,payload
  );
};

export const DeletePaymentApi = (id) => {
  return httpServices.delete(
    `/api/payment/${id}/deleteCashPayment`
  );
};

export const EditPaymentListApi = (id) => {
  return httpServices.get(
    `/api/payment/${id}`
  );
};

export const EditPaymentApi = (id,payload) => {
  return httpServices.put(
    `/api/payment/${id}/editCashPayment` ,payload
  );
}

export const ViewMemberListApi = (currentPage, memberPerpage) => {
  return httpServices.get(
    `/events/view-members?page=${currentPage}&limit=${memberPerpage}`
  );
};

export const DeleteBookingApi = (id) => {
  return httpServices.delete(
    `/booking/${id}`
  );
};

export const EditBookingDataApi = (id) => {
  return httpServices.get(
    `/booking/${id}`
  );
};

export const EditBookingApi = (id,payload) => {
  return httpServices.put(
    `/booking/${id}`,payload
  );
};

export const getAllReviewsListApi = (id,currentPage,reviewPerPage) => {
  return httpServices.get(`/reviews/all/${id}?page=${currentPage}&limit=${reviewPerPage}`)
}

export const getHostRideDatabyIdApi = (id) => {
  return httpServices.get(`/rides/${id}`)
}

export const DeleteHostRideApi = (id) => {
  return httpServices.delete(
    `/rides/${id}`
  );
};

export const editRideApi = (id,payload) => {
  return httpServices.put(
    `/rides/${id}`,payload
  );
};

export const addcheckInApi = (payload) => {
  return httpServices.post(
    `/check-in`,payload
  );
};

export const getcheckInApi = (id) => {
  return httpServices.get(
    `/check-in/${id}`
  );
};

export const deletecheckInApi = (id) => {
  return httpServices.delete(
    `/check-in/${id}`
  );
};

export const editcheckInApi = (id,payload) => {
  return httpServices.put(
    `/check-in/${id}`,payload
  );
};

export const addOwnerReviewApi=(payload)=>{
  return httpServices.post(`/reviews`,payload)
}

export const deleteOwnerReviewApi=(id)=>{
  return httpServices.delete(`/reviews/${id}`)
}

export const getOwnerReviewByIdApi=(id)=>{
  return httpServices.get(`/reviews/${id}`)
}

export const updateOwnerReviewByIdApi=(id,payload)=>{
  return httpServices.put(`/reviews/${id}`,payload)
}

export const getOwnerDashboardDataApi=(id)=>{
  return httpServices.get(`/dashboard/${id}`)
}

export const RoomTypeListApi = () => {
  return httpServices.get(
    `/room/types`
  );
};

export const AddRateApi = (payload) => {
  return httpServices.post(
    `/room/room-rates`, payload
  );
};

export const getCalendarApi = (startDate,endDate,storedId) => {
  return httpServices.get(
    `/dashboard/calendar?startDate=${startDate}&endDate=${endDate}&property=${storedId}`,
  );
};

export const getRateByRoomApi = (id) => {
  return httpServices.get(
    `/room/types/rate?room=${id}`,
  );
};