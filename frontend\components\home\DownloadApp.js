import Link from "next/link";
import React , {useState} from "react";
// import { motion } from "framer-motion";
// import { TypeAnimation } from 'react-type-animation';
import Image from "next/image";

const DownloadApp = () => {

  const [isImg1Loaded, setIsImg1Loaded] = useState(false);
  const [isImg2Loaded, setIsImg2Loaded] = useState(false);

  return (
    <div className="mb-[10px] bg-repeat-round w-full"
      style={{
        backgroundImage: `url(${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/left-cross-bg.webp)`,
        backgroundSize: "cover", // Ensures the image covers the entire div
      }}
    >
      <section className="w-full lg:px-6 xs:pt-8 pt-8 md:pb-0 pb-6">
        <div className="container relative">
          <div className="grid grid-cols-12 items-center justify-center">
            <div className="lg:col-span-5 md:col-span-6 col-span-12 xl:col-start-2 lg:mb-0 sm:mb-10 mb-6">
              {/* <motion.div
                initial={{ x: -100, opacity: 0 }}
                whileInView={{ x: 0, opacity: 1 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
                viewport={{ once: false }} */}
              <div
                className="flex items-center md:justify-start justify-center gap-3">
                <Image className="sm:h-[100px] sm:w-[250px] w-[200px] h-[80px]" width={250} height={100} src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Mixdorm-white-D.svg`} alt="Mixdorm" />
              </div>
              {/* </motion.div> */}
              {/* <TypeAnimation
                sequence={[
                  'Download app now !',
                  2000,
                  '',
                  500,
                  'Download app now !',
                ]}
                wrapper="h1"
                cursor={true}
                cursorClassName="type-animation-cursor"
                repeat={Infinity} */}
              <h1
                className="font-manrope sm:mt-5 mt-0 lg:text-[50px] sm:text-[40px] text-[30px] lg:font-extrabold text-semibold text-white leading-none md:text-start text-center">Download app now !</h1>
              {/* /> */}
              <div className="flex items-center md:justify-start justify-center gap-3 sm:mt-8 mt-6">
                <Link href="/">
                  {/* <motion.div
                    initial={{ x: -100, opacity: 0 }}
                    whileInView={{ x: 0, opacity: 1 }}
                    transition={{ duration: 0.8, ease: "easeOut" }}
                    viewport={{ once: false }}
                  > */}
                  <div className="relative sm:w-[190px] w-[150px] sm:aspect-[190/60] aspect-[150/44] ml-auto">
                    <Image className="hover:shaodow-none hover:shadow-transparent shadow-lg transition-all shadow-white/25 rounded-md sm:w-auto ml-auto" fill src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/google-play.png`} alt="Google Play" />
                    </div>
                  {/* </motion.div> */}
                </Link>
                <Link href="/">
                  {/* <motion.div
                    initial={{ x: -70, opacity: 0 }}
                    whileInView={{ x: 0, opacity: 1 }}
                    transition={{ duration: 0.8, ease: "easeOut" }}
                    viewport={{ once: false }} */}
                    <div className="relative sm:w-[190px] w-[150px] sm:aspect-[190/60] aspect-[150/44] ml-auto">
                    <Image className="hover:shaodow-none hover:shadow-transparent shadow-lg transition-all shadow-white/25 rounded-md sm:w-auto me-auto" fill src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/app-store.png`} alt="App store" />
                    </div>
                  {/* </motion.div> */}
                </Link>
              </div>
            </div>
            <div className="xl:col-span-6 lg:col-span-7 md:col-span-6 col-span-12 relative md:block hidden">
              <div className="flex pl-8">
                {/* <motion.div
                  initial={{ x: -50, y: -100, opacity: 0 }}
                  whileInView={{ x: 0, y: 0, opacity: 1 }}
                  transition={{ duration: 1.5, ease: "easeOut", delay: 0.1 }}
                  viewport={{ once: false }} */}
                <div
                  className="relative z-10 md:top-20 sm:top-32 top-16"
                >
                  {!isImg1Loaded && (
                    <div className="animate-pulse bg-gray-300 rounded-lg w-full lg:w-auto md:w-[80%] h-[200px]" />
                  )}
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/download2.png`}
                    alt="download2"
                    width={500}
                    height={500}
                    onLoad={() => setIsImg1Loaded(true)}
                    // className={`animate-slowbounce min-w-full z-40`}
                    className={`min-w-full z-40`}
                  />
                </div>
                {/* </motion.div> */}

                {/* <motion.div
                  initial={{ x: 50, y: -100, opacity: 0 }}
                  whileInView={{ x: 0, y: 0, opacity: 1 }}
                  transition={{ duration: 1.5, ease: "easeOut", delay: 0.4 }}
                  viewport={{ once: false }} */}
                <div
                  className="relative md:top-6 sm:top-20 top-5 -left-28"
                >
                  {!isImg2Loaded && (
                    <div className="animate-pulse bg-gray-300 rounded-lg w-full lg:w-auto md:w-[80%] h-[200px]" />
                  )}
                  <Image
                    src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/download1.png`}
                    alt="download1"
                    width={500}
                    height={500}
                    onLoad={() => setIsImg2Loaded(true)}
                    // className={`animate-slowbounce min-w-full z-40`}
                    className={`min-w-full z-40`}
                  />
                </div>
                {/* </motion.div> */}
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default DownloadApp;
