"use client";
import React, { useEffect, useRef, useState } from "react";
import ProfileForm from "@/components/superadmin/ManageProfile";
import { userProfileListAdmin, deleteSubAdmin } from "@/services/adminflowServices";
import toast from "react-hot-toast";
import Loader from "@/components/loader/loader";

const ManageProfile = () => {
  const [profiles, setProfiles] = useState([]);
  const [editingProfile, setEditingProfile] = useState(null);
  // eslint-disable-next-line no-unused-vars
  const [usersData, setUsersData] = useState([]);
  const [loading, setLoading] = useState(true);
  const isFirstRender = useRef(null);

  useEffect(() => {
    if (!isFirstRender.current) {
      fetchList();
    } else {
      isFirstRender.current = false;
    }
  }, []);

  const fetchList = async () => {
    setLoading(true);

    try {
      const response = await userProfileListAdmin();

      if (response?.data?.status === true) {
        setUsersData(response?.data?.data || []);
        // Transform the API data into the required profile format
        const transformedProfiles = response?.data?.data.map((user, index) => ({
          name: user.name.first,
          email: user.email,
          role: user?.role,
          color: getColorByIndex(index),
          hasEdit: true,
          _id: user._id
        }));

        // Add the "New" profile option at the end
        transformedProfiles.push({
          name: "",
          role: "",
          color: getColorByIndex(transformedProfiles.length),
          hasEdit: true,
          isNew: true
        });

        setProfiles(transformedProfiles);
      } else {
        toast.error(response?.message || "Failed to fetch profiles");
      }
    } catch (error) {
      toast.error(error?.message || "Something went wrong!");
    } finally {
      setLoading(false);
    }
  };

  // Helper function to assign colors to profiles
  const getColorByIndex = (index) => {
    const colors = [
      "bg-blue-400",
      "bg-[#8280FF]",
      "bg-[#FEC53D]",
      "bg-[#4AD991]",
      "bg-[#FF9066]"
    ];
    return colors[index % colors.length];
  };

  const handleEditClick = (profile) => {
    setEditingProfile(profile);
  };

  const handleSaveProfile = (updatedProfile) => {
    if (updatedProfile.isNew) {
      // If it's a new profile, fetch the updated list
      fetchList();
    } else {
      // For existing profiles, just update the local state
      setProfiles(
        profiles.map((profile) =>
          profile._id === updatedProfile._id ? updatedProfile : profile
        )
      );
    }
    setEditingProfile(null);
  };

  const handleCancelEdit = () => {
    setEditingProfile(null);
  };

  const handleDeleteProfile = async (profileId) => {
    if (!profileId) return;
    
    if (window.confirm("Are you sure you want to delete this profile?")) {
      try {
        const response = await deleteSubAdmin(profileId);
        if (response?.data?.status === true) {
          toast.success("Profile deleted successfully");
          // Fetch the updated list after successful deletion
          fetchList();
        } else {
          toast.error(response?.message || "Failed to delete profile");
        }
      } catch (error) {
        toast.error(error?.message || "Something went wrong!");
      }
    }
  };

  return (
    <>
    <Loader open={loading} />
    
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616] h-screen">
      <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
        {editingProfile ? "Profile" : "Manage Profile"}
      </h2>
      {editingProfile ? (
        <ProfileForm
          profile={editingProfile}
          onSave={handleSaveProfile}
          onCancel={handleCancelEdit}
          onDelete={handleDeleteProfile}
        />
      ) : (
        <div className="w-full p-8 bg-white rounded-lg items-center justify-center mt-4 dark:bg-black">
          <div className="grid grid-cols-2 gap-4">
            {profiles.map((profile, index) => (
              <div
                key={index}
                className="flex flex-col items-center justify-center"
              >
                <div
                  className={`w-[74px] h-[74px] rounded-full ${profile.color} flex items-center justify-center text-white text-3xl font-medium font-poppins mt-2 `}
                >
                  {profile.isNew ? "+" : (profile.name ? profile.name[0] : "+")}
                </div>
                <div className="flex items-center py-2 flex-col">
                  <div className="flex text-center flex-col">
                    {profile.name && !profile.isNew && (
                      <div className="text-black text-sm md:text-base lg:text-lg font-medium font-poppins whitespace-nowrap dark:text-[#B6B6B6]">{profile.name}</div>
                    )}
                    {profile.role && !profile.isNew && (
                      <div className="text-sky-blue-650 text-sm md:text-base lg:text-lg font-medium font-poppins whitespace-nowrap">
                        {profile.role}
                      </div>
                    )}
                  </div>
                  {profile.hasEdit && (
                    <div className="flex gap-2">
                      <div
                        className="text-sky-blue-650 cursor-pointer text-sm md:text-base lg:text-lg font-medium font-poppins"
                        onClick={() => handleEditClick(profile)}
                      >
                        {profile.isNew ? "Add New" : "Edit"}
                      </div>
                      {!profile.isNew && (
                        <div
                          className="text-red-500 cursor-pointer text-sm md:text-base lg:text-lg font-medium font-poppins"
                          onClick={() => handleDeleteProfile(profile._id)}
                        >
                          Delete
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
    </>
  );
};

export default ManageProfile;
