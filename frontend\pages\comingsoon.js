/* eslint-disable react/no-unescaped-entities */
 

const Comingsoon = () => {
  return (
    <>
      {/* <section className="w-full">
        <div className="w-full mt-5 bg-white shadow-4xl p-6 rounded-2xl relative h-[200px]">
          <div class="min-h-screen bg-gray-900 flex flex-col items-center justify-center">
            <h1 class="text-5xl text-white font-bold mb-8 animate-pulse">
              Coming Soon
            </h1>
            <p class="text-white text-lg mb-8">
              We're working to bring you something amazing. Stay tuned!
            </p>
          </div>
        </div>
      </section> */}
      <div className="bg-white md:pb-16 font-manrope md:pt-12 py-8">
      <div className="flex flex-col items-center justify-center">
      <h1 className="text-5xl text-gray-600 font-bold mb-8 animate-pulse">
              Coming Soon
            </h1>
            <p className="text-black text-lg mb-8">
              We're working to bring you something amazing. Stay tuned!
            </p>
        </div>
  
      </div>
    </>
  );
};

export default Comingsoon;
