/* eslint-disable no-constant-binary-expression */
import React, { useEffect, useRef, useState } from "react";
import toast, { Toaster } from "react-hot-toast";
import {
  EditBookingApi,
  EditBookingDataApi,
  RoomListApi,
} from "@/services/ownerflowServices";
import { getItemLocalStorage } from "@/utils/browserSetting";
import CustomSelect from "@/components/common/CustomDropdown2";
import countries from "world-countries";
import { format, isToday } from "date-fns";
import Image from "next/image";

const EditBooking = ({ updateRoomList, editId, closebookingeditModal }) => {
  const [bookingData, setBookingData] = useState({
    user: "",
    guestDetails: { name: "", email: "", phone: "" },
    referenceNumber: "",
    property: "",
    room: "",
    roomNumber: "",
    beds: "",
    checkInDate: "",
    checkOutDate: "",
    guests: { adults: 0, children: 0 },
    rate: "",
    currency: "",
    tax: "",
    totalAmount: "",
    balance: "",
    amoutPaid: "",
    isCancel: false,
    cancelledDate: "",
    cancelledBy: "",
    status: "confirmed",
    paymentStatus: "unpaid",
    isActive: true,
    isDeleted: false,
  });

  const id = getItemLocalStorage("hopid");
  const isFirstRender = useRef(null);
  const [currencies, setCurrencies] = useState([]);
  const [roomList, setRoomList] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [weekdayRate, setWeekdayRate] = useState(null);
  const [selectedRoomId, setSelectedRoomId] = useState(null);

  const [showCalendar, setShowCalendar] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const calendarRef = useRef(null);
  const inputRef = useRef(null);
  const [showFromCalendar, setShowFromCalendar] = useState(false);
  const [currentMonthFrom, setCurrentMonthFrom] = useState(new Date());
  const calendarRefFrom = useRef(null);

  const handlePrevMonth = () => {
    setCurrentMonth(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() - 1, 1)
    );
  };

  const handleNextMonth = () => {
    setCurrentMonth(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() + 1, 1)
    );
  };

  // Functions to move months
  const handlePrevMonthFrom = () => {
    setCurrentMonthFrom(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() - 1, 1)
    );
  };

  const handleNextMonthFrom = () => {
    setCurrentMonthFrom(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() + 1, 1)
    );
  };
  // Generate days for current month
  function generateDays(month) {
    const daysInMonth = new Date(
      month.getFullYear(),
      month.getMonth() + 1,
      0
    ).getDate();
    const firstDayIndex = new Date(
      month.getFullYear(),
      month.getMonth(),
      1
    ).getDay();

    const days = [];

    // Add blank spaces for days of previous month
    for (let i = 0; i < firstDayIndex; i++) {
      days.push(null);
    }

    // Add days of the current month
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(new Date(month.getFullYear(), month.getMonth(), i));
    }

    return days;
  }

  // Close calendar if clicked outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target) &&
        !inputRef.current.contains(event.target)
      ) {
        setShowCalendar(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const fetchList = async (id) => {
    try {
      const response = await RoomListApi(id, 1, 100);
      if (response.status === 200) {
        setRoomList(response.data.data.rooms);
      } else {
        // Handle error response
        toast.error("Failed to fetch rooms");
      }
    } catch (error) {
      console.log("Error fetching rooms:", error);
      toast.error("Error fetching rooms");
    }
  };

  useEffect(() => {
    if (id && !isFirstRender.current) {
      fetchList(id);
    } else {
      isFirstRender.current = false;
    }
  }, [id]);

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, "0"); // Add leading zero
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Add leading zero, months are 0-indexed
    const year = date.getFullYear();

    return `${year}-${month}-${day}`;
  };

  const fetchRoomData = async () => {
    try {
      const response = await EditBookingDataApi(editId);
      if (response.status === 200) {
        console.log("response", response?.data?.data);
        setBookingData({
          guestDetails: response?.data?.data?.guestDetails,
          beds: response?.data?.data?.beds,
          room: roomList.find(
            (data) => data?._id === response?.data?.data?.room
          )?.name,
          checkInDate: formatDate(response?.data?.data?.checkInDate),
          checkOutDate: formatDate(response?.data?.data?.checkOutDate),
          currency: response?.data?.data?.currency,
          totalAmount: response?.data?.data?.totalAmount,
        });
        setSelectedRoomId(response?.data?.data?.room);
      } else {
        // Handle error response
        toast.error("Failed to fetch paymentData");
      }
    } catch (error) {
      console.log("Error fetching paymentData:", error);
      toast.error("Error fetching paymentData");
    }
  };

  useEffect(() => {
    if (roomList.length > 0 && editId) {
      fetchRoomData();
    }
  }, [roomList, editId]);

  const getWeekdayRateByName = (roomName) => {
    const room = roomList.find((item) => item.name === roomName?.label);
    if (room && room.rate && room.rate.weekdayRate) {
      setWeekdayRate(room.rate.weekdayRate.value);
      setBookingData({
        ...bookingData,
        totalAmount: room?.rate?.weekdayRate?.value,
        currency: room?.currency,
        room: roomName,
      });
      setSelectedRoomId(room?._id);
    } else {
      console.error("Room or weekdayRate not found.");
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setBookingData({ ...bookingData, [name]: value });
    if (name === "room") {
      getWeekdayRateByName(value);
    }
  };

  const handleGuestDetailsChange = (e) => {
    const { name, value } = e.target;
    setBookingData({
      ...bookingData,
      guestDetails: { ...bookingData.guestDetails, [name]: value },
    });
  };
  const handleSubmit = async () => {
    const {
      guestDetails,
      beds,
      checkInDate,
      checkOutDate,
      currency,
      totalAmount,
      room,
    } = bookingData;

    if (
      !guestDetails.name ||
      !guestDetails.email ||
      !guestDetails.phone ||
      // !roomNumber ||
      !beds ||
      !checkInDate ||
      !checkOutDate ||
      !currency ||
      // !amoutPaid ||
      !totalAmount ||
      // !balance
      !room
    ) {
      toast.error("Please fill all required fields");
      return;
    }

    try {
      //   const imageUrls = images.length > 0 ? await uploadImage(images) : [];
      // const imageUrls = images.length > 0 ? images : [];

      const payload = {
        guestDetails,
        // roomNumber,
        beds: parseInt(beds),
        checkInDate,
        checkOutDate,
        currency: currency?.value ? currency?.value : currency,
        totalAmount: parseFloat(totalAmount),
        // amoutPaid: parseFloat(amoutPaid),
        // balance: parseFloat(balance),
        room: [selectedRoomId],
        property: id,
      };
      //   console.log("payload", payload);

      const response = await EditBookingApi(editId, payload);

      if (response?.data?.status) {
        toast.success("Booking Edit successfully");
        setBookingData({
          guestDetails: { name: "", email: "", phone: "" },
          // roomNumber: "",
          beds: "",
          checkInDate: "",
          checkOutDate: "",
          currency: "",
          totalAmount: "",
          room: "",
          // balance: "",
          // amoutPaid: "",
        });
        updateRoomList();
        closebookingeditModal();
      }
    } catch (error) {
      console.error("Error Edit booking:", error);
      toast.error("Failed to Edit booking");
    }
  };

  const isCurrenciesFetched = useRef(false);

  useEffect(() => {
    if (!isCurrenciesFetched.current) {
      const fetchCountryCurrencyCodes = async () => {
        try {
          const countryData = countries.map((country) => {
            // Safely extract currency code and symbol, falling back to defaults
            const currencyCode =
              country?.currencies && Object.keys(country?.currencies)[0]
                ? Object.keys(country?.currencies)[0]
                : "N/A";
            const currencySymbol =
              country?.currencies && country?.currencies[currencyCode]?.symbol
                ? country?.currencies[currencyCode]?.symbol
                : "€";

            // Get the flag code (ISO 3166-1 alpha-2) for the flag
            const flagCode = country.cca2 ? country.cca2.toLowerCase() : "xx"; // Default to 'xx' if cca2 is missing

            // Construct the flag image URL or use a placeholder
            const flag =
              `https://flagcdn.com/w320/${flagCode}.png` ||
              "https://via.placeholder.com/30x25";

            return {
              name: country?.name?.common || "Unknown", // Country name, fallback to "Unknown" if not found
              code: currencyCode, // Currency code
              symbol: currencySymbol, // Currency symbol
              flag: flag, // Flag image URL
            };
          });

          setCurrencies(countryData); // Store the country data
        } catch (error) {
          console.error("Error fetching country data:", error);
        }
      };
      fetchCountryCurrencyCodes();
      isCurrenciesFetched.current = true;
    }
  }, []);

  return (
    <>
      <div className="w-full bg-white shadow-4xl rounded-2xl">
        <div>
          <Toaster />

          <div className="grid grid-cols-2 sm:gap-5 gap-3 w-full">
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Email
              </label>
              <input
                type="email"
                name="email"
                value={bookingData?.guestDetails?.email}
                onChange={handleGuestDetailsChange}
                className="block w-full p-2 px-4 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-320 placeholder:text-gray-320"
                placeholder="Enter guest email"
              />
            </div>
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Phone
              </label>
              <input
                type="number"
                name="phone"
                value={bookingData?.guestDetails?.phone}
                onChange={handleGuestDetailsChange}
                className="block w-full p-2 px-4 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-320 placeholder:text-gray-320"
                placeholder="Enter guest phone"
              />
            </div>
            <div className="col-span-2">
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Name
              </label>
              <input
                type="text"
                name="name"
                value={bookingData?.guestDetails?.name}
                onChange={handleGuestDetailsChange}
                className="block w-full p-2 px-4 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-320 placeholder:text-gray-320"
                placeholder="Enter guest name"
                required={!bookingData.user}
              />
            </div>

            <div className="relative w-full max-w-xs">
              <label
                className="block text-black sm:text-sm text-xs font-medium mb-1.5"
                htmlFor="toDate"
              >
                Check-In Date
              </label>

              <input
                type="text"
                name="toDate"
                id="toDate"
                ref={inputRef}
                value={
                  bookingData?.checkInDate &&
                  format(bookingData?.checkInDate, "MM-dd-yyyy")
                }
                placeholder="mm/dd/yyyy"
                readOnly
                onClick={() => setShowCalendar(!showCalendar)}
                className="block w-full p-2 px-4 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-800 placeholder:text-gray-400 cursor-pointer"
              />

              {showCalendar && (
                <div
                  ref={calendarRef}
                  className="absolute top-full left-0 bg-white border border-black/50 rounded-lg shadow-lg px-4 py-2 z-50 w-full mt-0.5"
                >
                  {/* Month navigation */}
                  <div className="flex items-center justify-between mb-4">
                    <button
                      onClick={handlePrevMonth}
                      className="text-lg font-bold px-2"
                    >
                      &#8592;
                    </button>

                    <span className="text-base font-semibold">
                      {format(currentMonth, "MMMM yyyy")}
                    </span>

                    <button
                      onClick={handleNextMonth}
                      className="text-lg font-bold px-2"
                    >
                      &#8594;
                    </button>
                  </div>

                  {/* Weekdays */}
                  <div className="grid grid-cols-7 gap-2 text-center text-sm font-semibold text-gray-600">
                    <div>Su</div>
                    <div>Mo</div>
                    <div>Tu</div>
                    <div>We</div>
                    <div>Th</div>
                    <div>Fr</div>
                    <div>Sa</div>
                  </div>

                  {/* Days */}
                  <div className="grid grid-cols-7 gap-1 mt-2 text-center text-sm">
                    {generateDays(currentMonth).map((day, index) => {
                      if (!day) return <div key={index} />;

                      const isSelected =
                        bookingData?.checkInDate &&
                        format(day, "MM-dd-yyyy") ===
                          format(bookingData.checkInDate, "MM-dd-yyyy");

                      return (
                        <button
                          key={index}
                          className={`p-2 rounded-full flex items-center justify-center 
                            ${isToday(day) ? "border-2 border-primary-blue" : ""} ${
                            isSelected
                              ? "bg-primary-blue text-white"
                              : "hover:bg-primary-blue hover:text-white"
                          }`}
                          onClick={() => {
                            handleInputChange({
                              target: {
                                name: "checkInDate",
                                value: format(day, "MM-dd-yyyy"),
                              },
                            });
                            setShowCalendar(false);
                          }}
                        >
                          {day.getDate()}
                        </button>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
            <div className="relative w-full max-w-xs">
              <label
                className="block text-black sm:text-sm text-xs font-medium mb-1.5"
                htmlFor="fromDate"
              >
                Check-Out Date
              </label>

              <input
                type="text"
                name="fromDate"
                id="fromDate"
                value={
                  bookingData?.checkOutDate &&
                  format(bookingData?.checkOutDate, "MM-dd-yyyy")
                }
                placeholder="mm/dd/yyyy"
                readOnly
                onClick={() => setShowFromCalendar(!showFromCalendar)}
                className="block w-full p-2 px-4 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-800 placeholder:text-gray-400 cursor-pointer"
                required
              />

              {showFromCalendar && (
                <div
                  ref={calendarRefFrom}
                  className="absolute top-full left-0 mt-0.5 bg-white border border-black/50 rounded-lg shadow-lg px-4 py-2 z-50 w-full"
                >
                  {/* Month navigation */}
                  <div className="flex items-center justify-between mb-4">
                    <button
                      onClick={handlePrevMonthFrom}
                      className="text-lg font-bold px-2"
                    >
                      &#8592;
                    </button>

                    <span className="text-base font-semibold">
                      {format(currentMonthFrom, "MMMM yyyy")}
                    </span>

                    <button
                      onClick={handleNextMonthFrom}
                      className="text-lg font-bold px-2"
                    >
                      &#8594;
                    </button>
                  </div>

                  {/* Weekdays */}
                  <div className="grid grid-cols-7 gap-2 text-center text-sm font-semibold text-gray-600">
                    <div>Su</div>
                    <div>Mo</div>
                    <div>Tu</div>
                    <div>We</div>
                    <div>Th</div>
                    <div>Fr</div>
                    <div>Sa</div>
                  </div>

                  {/* Days */}
                  {/* <div className="grid grid-cols-7 gap-1 mt-2 text-center text-sm">
                    {generateDays(currentMonthFrom).map((day, index) =>
                      day ? (
                        <button
                          key={index}
                          className="hover:bg-primary-blue hover:text-white rounded-full p-2"
                          onClick={() => {
                            handleInputChange({
                              target: {
                                name: "checkOutDate",
                                value: format(day, "MM-dd-yyyy"),
                              },
                            });
                            setShowFromCalendar(false);
                          }}
                        >
                          {day.getDate()}
                        </button>
                      ) : (
                        <div key={index} />
                      )
                    )}
                  </div> */}
                  <div className="grid grid-cols-7 gap-1 mt-2 text-center text-sm">
                    {generateDays(currentMonth).map((day, index) => {
                      if (!day) return <div key={index} />;

                      const isSelected =
                        bookingData?.checkOutDate &&
                        format(day, "MM-dd-yyyy") ===
                          format(bookingData.checkOutDate, "MM-dd-yyyy");

                      return (
                        <button
                          key={index}
                          className={`p-2 rounded-full flex items-center justify-center 
                            ${isToday(day) ? "border-2 border-primary-blue" : ""} ${
                            isSelected
                              ? "bg-primary-blue text-white"
                              : "hover:bg-primary-blue hover:text-white"
                          }`}
                          onClick={() => {
                            handleInputChange({
                              target: {
                                name: "checkOutDate",
                                value: format(day, "MM-dd-yyyy"),
                              },
                            });
                            setShowFromCalendar(false);
                          }}
                        >
                          {day.getDate()}
                        </button>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>

            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Room No
              </label>

              <CustomSelect
                name="roomNumber"
                options={[
                  { value: "", label: "Enter Room No" },
                  { value: "123", label: "123" },
                ]}
                value={bookingData.roomNumber}
                onChange={(selectedOption) =>
                  handleInputChange({
                    target: {
                      name: "roomNumber",
                      value: selectedOption?.value,
                    },
                  })
                }
                placeholder="Select Room Number"
              />
            </div>
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Room Type
              </label>

              <CustomSelect
                name="room"
                options={roomList.map((roomData) => ({
                  value: roomData?._id,
                  label: roomData?.name,
                }))}
                value={
                  bookingData.room
                    ? {
                        value: bookingData?.room?.value,
                        label: `${bookingData.room?.label}`,
                      }
                    : null
                }
                onChange={(selectedOption) =>
                  handleInputChange({
                    target: { name: "room", value: selectedOption },
                  })
                }
                placeholder="Select Room"
              />
            </div>
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                No of Beds
              </label>
              <input
                type="number"
                name="beds"
                value={bookingData?.beds}
                onChange={handleInputChange}
                className="block w-full p-2 px-4 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-320 placeholder:text-gray-320"
                placeholder="Enter number of beds"
              />
            </div>
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Total Amount
              </label>
              <input
                type="number"
                name="totalAmount"
                value={bookingData?.totalAmount}
                onChange={handleInputChange}
                className="block w-full p-2 px-4 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-320 placeholder:text-gray-320"
                required
              />
            </div>
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Currency
              </label>

              <CustomSelect
                options={currencies.map((currency) => ({
                  value: currency.code,
                  label: (
                    <span className="flex items-center">
                      <Image 
                        src={currency.flag || "/placeholder.svg"}
                        alt={`${currency.code} Flag`}
                        className="inline-block w-4 h-3 mr-2"
                        width={20}
                        height={15}
                      />
                      {currency.code} ({currency.symbol})
                    </span>
                  ),
                }))}
                value={bookingData.currency}
                onChange={(selectedOption) =>
                  handleInputChange({
                    target: { name: "currency", value: selectedOption },
                  })
                }
                placeholder="Select Currency"
              />
            </div>

            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Amount Paid
              </label>
              <input
                type="number"
                name="amoutPaid"
                value={bookingData?.amoutPaid}
                onChange={handleInputChange}
                placeholder="Amount Paid"
                className="block w-full p-2 px-4 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-320 placeholder:text-gray-320"
              />
            </div>
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Balance
              </label>
              <input
                type="number"
                name="balance"
                value={bookingData?.balance}
                onChange={handleInputChange}
                placeholder="Enter Balance"
                className="block w-full p-2 px-4 text-sm bg-transparent border border-black/50 rounded-lg focus:outline-none text-slate-320 placeholder:text-gray-320"
              />
            </div>
          </div>

          <div className="flex justify-end my-10 gap-4 py-4 bg-white/60 sticky bottom-0 backdrop-blur-sm">
            <button
              onClick={closebookingeditModal}
              className="hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 px-4 border-black rounded-lg w-full text-sm"
            >
              Cancel
            </button>
            <button
              onClick={handleSubmit}
              className="bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4 border-black rounded-lg w-full text-sm"
            >
              Edit
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default EditBooking;
