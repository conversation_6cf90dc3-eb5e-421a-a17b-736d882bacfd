import Image from "next/image";
import Link from "next/link";
import React from "react";

const TravelDetails = () => {
  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616] h-screen">
      <div className="flex items-center justify-between w-full ">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Travel Activity Details
        </h2>

      </div>
      <div className="bg-white border rounded-xl mt-5 h-auto dark:bg-black dark:border-none">
        <div className="p-8 flex flex-col gap-y-6">

          <div>
            <h1 className="text-xl font-bold font-poppins dark:text-[#B6B6B6]">
              Activity Information
            </h1>
            <div className="flex flex-wrap items-center my-6 gap-6">
              <div className="flex w-full sm:w-[45%] md:w-[50%] lg:w-[50%] flex-col mb-0  lg:mb-5 ">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  Activity Name
                </strong>
                <p className="mt-2 text-black/65 text-base font-medium font-poppins dark:text-[#B6B6B6]">
                  Explore Ruins
                </p>
              </div>

              <div className="flex w-full sm:w-[45%] md:w-[50%] lg:w-[50%] flex-col">
                <strong className="text-black/75 text-base font-semibold font-poppins dark:text-[#757575]">
                  Image
                </strong>
                <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Place.png`}
                  alt="Image"
                  className="w-32 h-32 mt-2 lg:sm-1 rounded-lg"
                  width={0}
                  height={0}
                />
              </div>
            </div>
          </div>





          <div className="flex items-start justify-start">
            <Link
              href={"/superadmin/dashboard/travel-activity"}
              className="text-white py-2 w-32 rounded bg-sky-blue-650 flex items-center justify-center"
            >
              Cancel
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TravelDetails;

