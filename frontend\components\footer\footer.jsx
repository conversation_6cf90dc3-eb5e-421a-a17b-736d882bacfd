import { Mail, MapPin } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React, { useState } from "react";
import { TbLocationShare } from "react-icons/tb";
import dynamic from "next/dynamic";
import { newsLetterSubscribeApi } from "@/services/webflowServices";
import toast from "react-hot-toast";
import { useNavbar } from "../home/<USER>";
import { FaFacebook, FaLinkedin } from "react-icons/fa";
import { BsInstagram, BsTwitterX } from "react-icons/bs";
// import { FaHotel } from "react-icons/fa6";

import { GoHeartFill } from "react-icons/go";

const ContactPopup = dynamic(() => import("../popup/contactPopup"), {
  ssr: false,
});
const SignInPopup = dynamic(() => import("../popup/signinPopup"), {
  ssr: false,
});
const Loader = dynamic(() => import("@/components/loader/loader"), {
  ssr: false,
});

const Footer = () => {
  //  Contact Modal
  const [openContact, setOpenContact] = useState(false);
  const handleOpenContact = () => setOpenContact(true);
  const handleCloseContact = () => setOpenContact(false);

  const [isSignInPopupOpen, setIsSignInPopupOpen] = useState(false);
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const { isMapOpen } = useNavbar();

  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubscribe = async () => {
    if (!email) {
      toast.error("Email is required");
      return;
    }
    if (!validateEmail(email)) {
      toast.error("Please enter a valid email address");
      return;
    }

    setLoading(true);
    try {
      const response = await newsLetterSubscribeApi({ email });
      if (response?.data?.status) {
        toast.success(response?.data?.message);
        setEmail("");
      } else {
        toast.error(response?.data?.message);
      }
      console.log("Subscribed successfully:", response.data);
    } catch (error) {
      console.error("Subscription failed:", error);
    }
    setLoading(false);
  };

  const handleLoginClosePopup = () => {
    setIsSignInPopupOpen(false);
  };

  const contact = [
    {
      id: 1,
      name: "Mumbai, India, 400001",
      icon: <MapPin size={16} className="mr-2 text-primary-blue" />,
      url: "#",
    },
    // {
    //   id: 2,
    //   name: "+0123-456789",
    //   icon: <Phone size={16} className="mr-2 text-primary-blue" />,
    //   url: "#",
    // },
    {
      id: 2,
      name: "<EMAIL>",
      icon: <Mail size={16} className="mr-2 text-primary-blue" />,
      url: "#",
    },
  ];
  const socialMedia = [
    {
      id: 1,
      name: "Instagram",
      icon: <BsInstagram className="text-white h-6 w-6" />,
      url: "https://www.instagram.com/mixdorms/",
    },
    {
      id: 2,
      name: "Twitter",
      icon: <BsTwitterX className="text-white h-6 w-6" />,
      url: "https://x.com/mixdorm",
    },

    {
      id: 3,
      name: "Facebook",
      icon: <FaFacebook className="text-white h-6 w-6" />,
      url: "https://www.facebook.com/profile.php?id=61572989814393",
    },
    {
      id: 4,
      name: "Linkedin",
      icon: <FaLinkedin className="text-white h-6 w-6" />,
      url: "https://www.linkedin.com/company/mixdorm/",
    },
  ];
  const company = [
    {
      id: 1,
      title: "About us",
      url: "/aboutus",
    },
    {
      id: 2,
      title: "Blog",
      url: "/blog",
    },
    {
      id: 3,
      title: "Awards",
      url: "/awards",
    },
    {
      id: 5,
      title: "FAQ",
      url: "/faqs",
    },
  ];
  const services = [
    // {
    //   id: 1,
    //   title: "Hostels",
    //   url: "#",
    // },
    {
      id: 2,
      title: "Booking Guarantee",
      url: "/bookinggaurantee",
    },
    {
      id: 3,
      title: "List your property",
      url: "/owner/list-your-hostel",
    },
    // {
    //   id: 4,
    //   title: "Mobile App",
    //   url: "#",
    // },
    // {
    //   id: 5,
    //   title: "Premium Membership",
    //   url: "#",
    // },
  ];
  const customerCare = [
    // {
    //   id: 1,
    //   title: "Login",
    //   url: "#",
    //   onClick: handleLoginClick,
    // },
    // {
    //   id: 2,
    //   title: "My account",
    //   url: "/my-profile",
    // },
    {
      id: 3,
      title: "Help",
      url: "/help",
    },
    // {
    //   id: 4,
    //   title: "Add hostels listing",
    //   url: "#",
    // },
    {
      id: 5,
      title: "Claim and Refund",
      url: "/refundpolicy",
    },
    {
      id: 6,
      title: "Contact us",
      url: "",
      onClick: handleOpenContact,
    },
  ];

  const privacyPolicy = [
    {
      id: 1,
      title: "Terms & Conditions",
      url: "/terms-condition",
    },
    {
      id: 2,
      title: "Claim",
      url: "/refundpolicy",
    },
    {
      id: 3,
      title: "Privacy & Policy",
      url: "/privacypolicy",
    },
  ];

  // const paymentLogos = [
  //   "rupay-w.webp",
  //   "upi-w.webp",
  //   "paypal-w.webp",
  //   "visa-w.webp",
  //   "mastercard-logo.webp",
  // ];
  const paymentLogos = [
    `upi-w.webp`,
    `paypal-w.webp`,
    `visa-w.webp`,
    `mastercard-logo.webp`,
    `rupay-w.webp`,
  ];

  // const displayStyles = [
  //   "h-4 mt-3",
  //   "h-4 mt-3",
  //   "h-4 mt-3",
  //   "h-3 mt-3.5",
  //   "h-5 mt-2.5",
  // ];

  if (isMapOpen) {
    return;
  }
  return (
    <>
      <Loader open={loading} />
      <div className="bg-primary-blue xs:w-full lg:w-[878px] md:py-7 py-5 md:px-11 px-5 mx-auto xs:rounded-9xl rounded-2xl mb-6 w-[90%] xs:flex justify-between items-center text-center xs:text-left xs:mb-[-40px] sm:mb-[-60px] relative z-30 hidden md:flex">
        <div className="xs:w-[70%]">
          <p className="lg:text-[28px] sm:text-2xl text-xl text-black font-bold">
            Your Next Adventure Starts Here!
          </p>
          <p className="xs:mt-2 mt-1 text-sm text-black ">
            Find the Perfect Hostel
          </p>
        </div>
        <div className="xs:w-[30%] flex xs:justify-end justify-center items-center mt-3 xs:mt-0">
          <Link
            href="/exploreworld"
            className="h-11 md:min-w-[185px] rounded-9xl text-black bg-white px-4 py-2 flex justify-center items-center text-sm font-semibold "
            prefetch={false}
          >
            Explore Hostels
          </Link>
        </div>
      </div>

      <section className="relative w-full bg-black md:pt-36 xs:pt-20 py-8 z-20 ">
        {/* <div className="flex items-center justify-between bg-teal-400 rounded-full p-4 w-[95%] max-w-xl md:hidden ml-2 absolute -top-16 mt-6 z-20 ">
          <div>
            <h2 className="text-sm font-bold text-black">
              Your Next Adventure Starts Here !
            </h2>
            <p className="text-black text-sm">Find the Perfect Hostel</p>
          </div>
          <button
            aria-label="Search"
            className="bg-white text-black font-semibold w-6 h-6 rounded-full shadow-md flex items-center justify-center"
          >
            <FaSearchLocation />
          </button>
        </div> */}
             {/* <div className="bg-primary-blue xs:w-full lg:w-[878px] md:py-7 py-5 md:px-11 px-5 mx-auto xs:rounded-9xl rounded-2xl  w-[90%] xs:flex justify-between items-center text-center xs:text-left  z-30 hidden md:flex absolute -top-16 left-80 mt-4">
        <div className="xs:w-[70%]">
          <p className="lg:text-[28px] sm:text-2xl text-xl text-black font-bold">
            Your Next Adventure Starts Here!
          </p>
          <p className="xs:mt-2 mt-1 text-sm text-black ">
            Find the Perfect Hostel
          </p>
        </div>
        <div className="xs:w-[30%] flex xs:justify-end justify-center items-center mt-3 xs:mt-0">
          <Link
            href="/exploreworld"
            className="h-11 md:min-w-[185px] rounded-9xl text-black bg-white px-4 py-2 flex justify-center items-center text-sm font-semibold "
            prefetch={false}
          >
            Explore Hostels
          </Link>
        </div>
      </div> */}
        <div className="md:px-8 lg:px-6 xl:px-6 container">
          <div className="grid w-full lg:grid-cols-4 md:grid-cols-3 xl:gap-x-16 lg:gap-5 font-manrope ">
            <div className="md:flex md:flex-col grid sm:grid-cols-3 grid-cols-2 items-center md:items-start mb-5 md:mb-0 gap-5 sm:gap-8">
              <div className="col-span-2 sm:col-span-1 sm:text-left text-center mt-6 md:mt-0">
                {/* <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/mixdrom.png`}
                  width={112}
                  height={112}
                  alt="Mixdorm"
                  title="Mixdorm"
                  className="object-contain w-full max-w-28 m-auto sm:m-0"
                  loading="lazy"
                /> */}
              <Image
                  src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/Mixdorm-B.svg`}
                  width={112}
                  height={112}
                  alt="Mixdorm"
                  title="Mixdorm"
                  className="object-contain w-full max-w-28 m-auto sm:m-0"
                  loading="lazy"
                />
                <p className="mt-5 text-xs text-white">
                  Your hub for affordable stays, shared rides, and unforgettable
                  travel experiences. Explore the world with us.
                </p>
              </div>
              <ul className="hidden sm:block">
                {contact.map((item) => (
                  <li
                    className="flex items-center justify-start mt-2 text-xs first:mt-0"
                    key={item.id}
                  >
                    <Link
                      href={item.url}
                      className="flex items-center justify-start text-white"
                      prefetch={false}
                    >
                      {item.icon}
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
              <ul className="hidden sm:flex items-center justify-start gap-x-2.5">
                {socialMedia.map((item) => (
                  <li
                    className="flex items-center justify-start text-xs"
                    key={item.id}
                  >
                    <Link
                      href={item.url}
                      className="flex items-center justify-start text-white hover:text-primary-blue"
                      prefetch={false}
                      target="_blank"
                      rel="noopener noreferrer"
                      aria-label={`Visit our ${item.name} page`} // 👈 Add a descriptive label
                    >
                      {item.icon}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
            <div className="col-span-2 grid gap-x-2 lg:gap-x-0 grid-cols-3 mb-5 md:mb-0">
              <div>
                <h2 className="text-sm sm:text-base font-bold text-primary-blue font-manrope">
                  Company
                </h2>
                <ul className="mt-4 ">
                  {company.map((item) => (
                    <li
                      className="flex items-center justify-start mt-4 text-xs first:mt-0"
                      key={item.id}
                    >
                      <Link
                        href={item.url}
                        onClick={item.onClick || null}
                        className="flex items-center justify-start text-white hover:text-primary-blue"
                        prefetch={false}
                      >
                        {item.title}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="ml-[-15px] sm:ml-0">
                <h2 className="text-sm sm:text-base font-bold text-primary-blue font-manrope">
                  Services
                </h2>
                <ul className="mt-4 ">
                  {services.map((item) => (
                    <li
                      className="flex items-center justify-start mt-4 text-xs first:mt-0"
                      key={item.id}
                    >
                      <Link
                        href={item.url}
                        className="flex items-center justify-start text-white hover:text-primary-blue"
                        prefetch={false}
                      >
                        {item.title}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
              <div>
                <h2 className="text-sm sm:text-base font-bold text-primary-blue font-manrope">
                  Customer Care
                </h2>
                <ul className="mt-4 ">
                  {customerCare.map((item) => (
                    <li
                      className="flex items-center justify-start mt-4 text-xs first:mt-0"
                      key={item.id}
                      onClick={item?.onClick}
                    >
                      <Link
                        href={item.url}
                        className="flex items-center justify-start text-white hover:text-primary-blue"
                        prefetch={false}
                      >
                        {item.title}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
            <ContactPopup open={openContact} close={handleCloseContact} />
            <SignInPopup
              isOpen={isSignInPopupOpen}
              onClose={handleLoginClosePopup}
            />
            <div className="lg:col-span-1 md:col-span-2 gap-5 lg:gap-0 xs:flex lg:grid">
              <div>
                <h2 className="text-lg md:text-center text-left font-bold text-primary-blue font-manrope">
                  Newsletter
                </h2>
                <p className="mt-5 md:text-center text-left text-xs text-white">
                  Subscribe to our weekly Newsletter and receive updates via
                  email.
                </p>
                <ul className="mt-5 ">
                  <li className="relative mt-3 first:mt-0">
                    <input
                      type="email"
                      placeholder="Email*"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full h-10 py-3 pl-4 text-xs text-gray-500 bg-white outline-none placeholder:text-gray-400/90 rounded-9xl pr-7 "
                    />
                    <div
                      className="absolute top-0 bottom-0 right-0 flex items-center justify-center w-10 h-full text-white rounded-full bg-primary-blue "
                      onClick={handleSubscribe}
                    >
                      <TbLocationShare
                        size={18}
                        className="mx-auto text-white"
                      />
                    </div>
                  </li>
                </ul>
              </div>
              <div>
                <p className="mt-5 text-xs text-white text-center md:text-left">
                  We Accept
                </p>
                <div
                  className="comman-tooltip flex gap-x-2 items-center justify-center md:items-start md:justify-start"
                  // data-title="payment"
                >
                  {paymentLogos.map((file) => (
                    <div key={file} className={`relative w-12 h-8`}>
                      <Image
                        src={`${process.env.NEXT_PUBLIC_S3_URL_FE}/front-images/${file}`}
                        alt={`${
                          file.replace(/[-_]/g, " ").split(".")[0]
                        } payment method`}
                        width={48}
                        height={32}
                        className="object-contain w-12 h-8"
                        sizes="48px"
                        loading="lazy"
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
          <ul className="block sm:hidden mt-5">
            {contact.map((item) => (
              <li
                className="flex items-center justify-center mt-2 text-xs first:mt-0"
                key={item.id}
              >
                <Link
                  href={item.url}
                  className="flex items-center justify-start text-white"
                  prefetch={false}
                >
                  {item.icon}
                  {item.name}
                </Link>
              </li>
            ))}
          </ul>
          <ul className="sm:hidden mt-5 flex items-center justify-center gap-x-2.5">
            {socialMedia.map((item) => (
              <li
                className="flex items-center justify-start text-xs"
                key={item.id}
              >
                <Link
                  href={item.url}
                  className="flex items-center justify-start text-white hover:text-primary-blue"
                  prefetch={false}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={`Visit our ${item.name} page`} // 👈 Add a descriptive label
                >
                  {item.icon}
                </Link>
              </li>
            ))}
          </ul>
          <div className="flex md:flex-row flex-col items-center justify-between w-full sm:mt-14 mt-8">
            {/* <div className="flex-grow"></div> */}
            {/* <div className="relative md:mb-0 mb-2">
              
              <p className="lg:text-2xl md:text-xl sm:text-lg text-base md:text-left text-center font-semibold text-white/90 font-manrope flex md:justify-start justify-center items-center gap-1"> Crafted in Asia <IoMdHeart className="text-red-600 md:w-5 md:h-5" /> </p>
              <p className="lg:text-2xl md:text-xl sm:text-lg text-base md:text-left text-center font-semibold text-white/90 font-manrope"> Inspired by Hostel Life</p>
              <p className="sm:text-sm text-xs md:text-left text-center text-white font-manrope mt-1">Proudly built by backpackers, for backpackers.</p>
              
            </div> */}
            <div className="relative md:mb-0 mb-2 group">
              
              <p className="lg:text-xl md:text-xl sm:text-lg text-base md:text-left text-center font-semibold text-white/90 font-manrope flex md:justify-start justify-center items-center gap-1">
                <span className="relative flex">
                  Crafted in Asia
                  <span className="absolute top-0 md:top-[2px] -right-[26px]">
                    <GoHeartFill className="text-red-600 text-2xl animate-pulse" />
                  </span>
                </span>
              </p>

              <div className="lg:text-2xl md:text-xl sm:text-lg text-base md:text-left text-center font-semibold text-transparent bg-clip-text bg-gradient-to-r from-primary-blue to-white font-manrope h-6 md:h-8 overflow-hidden">
                <div className="typewriter">
                  <span className="text">Inspired by Hostel Life</span>
                </div>
              </div>

              <p className="sm:text-sm text-xs md:text-left text-center text-white font-manrope mt-1 relative inline-block">
                <span className="relative z-10">
                  Proudly built by backpackers, for backpackers
                </span>
                <span className="absolute bottom-0 left-0 w-0 h-[2px] bg-primary-blue group-hover:w-full transition-all duration-300"></span>
              </p>

              <div className="absolute -top-2 md:-top-4 left-2 md:-left-4 w-8 h-8 border-l-2 border-t-2 border-primary-blue opacity-70"></div>
              <div className="absolute -bottom-4 -right-6 md:-right-4  w-8 h-8 border-r-2 border-b-2 border-primary-blue opacity-70"></div>
            </div>
            {/* <div className="relative md:mb-0 mb-6 p-6 rounded-2xl backdrop-blur-md bg-white/5 border border-white/10 shadow-xl overflow-hidden group">
              
              <div className="absolute -top-10 -left-10 w-40 h-40 bg-gradient-to-tr from-primary-blue to-white rounded-full blur-3xl opacity-30 animate-pulse"></div>
              <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-gradient-to-bl from-pink-500 to-primary-blue rounded-full blur-3xl opacity-30 animate-pulse delay-500"></div>

              <div className="absolute inset-0 rounded-2xl border border-transparent group-hover:border-primary-blue/60 transition duration-500 shadow-[0_0_40px_-10px_rgba(59,130,246,0.6)]"></div>

         
              <p className="lg:text-2xl md:text-xl text-lg text-center md:text-left font-bold text-white font-manrope flex items-center gap-2 relative z-10">
                <span className="relative">
                  Crafted in Asia
                  <span className="absolute -right-5 -top-1">
                    <IoMdHeart className="text-pink-500 text-3xl animate-ping" />
                  </span>
                </span>
              </p>

           
              <div className="lg:text-3xl md:text-2xl text-xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-primary-blue via-white to-pink-400 font-manrope mt-2 relative overflow-hidden">
                <span className="inline-block whitespace-nowrap border-r-4 border-primary-blue animate-typing">
                  Inspired by Hostel Life
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer"></div>
              </div>

             
              <p className="sm:text-base text-xs text-center md:text-left text-white font-light mt-3 relative inline-block group-hover:text-primary-blue transition duration-300">
                Proudly built by backpackers, for backpackers
                <span className="absolute bottom-0 left-0 w-0 h-[2px] bg-gradient-to-r from-primary-blue to-pink-400 group-hover:w-full transition-all duration-500"></span>
              </p>
            </div> */}

            <div>
              <p className="text-xs font-normal text-white font-manrope text-center select-none mt-4 md:mt-0">
                © {new Date().getFullYear()} Hostel Mixdorm Private Limited.
                <br /> All rights reserved worldwide.
              </p>
            </div>
            {/* <div className="flex-grow"></div> */}
            <ul className="flex items-center mt-4 md:mt-0">
              {privacyPolicy.map((item) => (
                <li
                  className="ml-3 text-xs font-normal text-white hover:text-primary-blue "
                  key={item.id}
                >
                  <Link
                    href={item.url}
                    prefetch={false}
                    className="whitespace-nowrap"
                  >
                    {item.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </section>
    </>
  );
};

export default Footer;
