import mongoose from 'mongoose';

const NewsletterSubscriberSchema = new mongoose.Schema({
    email: {
        type: String,
        required: true,
    },
    isActive:{
        type:Boolean,
        default:true
    }
},{
    timestamps:true
});
const NewsletterSubscriber = mongoose.model('NewsletterSubscribers', NewsletterSubscriberSchema,'NewsletterSubscribers');
export default NewsletterSubscriber;
