import mongoose from 'mongoose';

const permissionSchema = mongoose.Schema({
    user: {
        type: mongoose.Types.ObjectId,
        ref: "users",
        required: true
    },
    view: {
        type: String,
        required: true
    },
    add: {
        type: String,
    },
    update: {
        type: String
    },
    delete: {
        type: String
    },
    status: {
        type: Number,
        default: 0
    },
    isDeleted: {
        type: Boolean,
        default: false,
    },
    isActive: {
        type: Boolean,
        default: true
    },
},
    {
        timestamps: true
    }
);

const permission = mongoose.model("permissions", permissionSchema);

export default permission;