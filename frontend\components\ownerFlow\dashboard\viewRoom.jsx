import { RoomListApi } from "@/services/hostemproviderservices";
import React, { useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";
import { getItemLocalStorage } from "@/utils/browserSetting";
import countries from "world-countries";
import Loader from "@/components/loader/loader";
import Image from "next/image";

const ViewRoom = ({  roomId }) => {
  // eslint-disable-next-line no-unused-vars
  const [properties, setProperties] = useState([]);
  // eslint-disable-next-line no-unused-vars
  const [selectedRoom, setSelectedRoom] = useState(roomId || null);
  const [currencyData, setCurrencyData] = useState({});
  const [isLoading, setIsLoading] = useState(false);


  // eslint-disable-next-line no-unused-vars
  const [addroom, setAddRoom] = useState({
    name: selectedRoom?.name || "",
    type: selectedRoom?.type || "",
    beds: selectedRoom?.beds || "",
    // eslint-disable-next-line no-undef
    ensuite: `${selectedRoom?.ensuite}` || No,
    rate: selectedRoom?.rate?.weekdayRate?.value || "",
    weekdayRate: selectedRoom?.rate?.weekdayRate?.value || "",
    weekendRate: selectedRoom?.rate?.weekendRate?.value || "",
    currency: selectedRoom?.currency || "",
    images: selectedRoom?.images || [],
    description: selectedRoom?.description || "",
    bedAndBreakfast: selectedRoom?.bedAndBreakfast || false,
    nonRefundable: selectedRoom?.nonRefundable || false,
    freeCancellation: selectedRoom?.freeCancellation || false,
  });



  const id = getItemLocalStorage("hopid");
  const isFirstRender = useRef(null);

  useEffect(() => {
    if (id && !isFirstRender.current) {
      fetchList(id);
    } else {
      isFirstRender.current = false;
    }
  }, [id]);

  useEffect(() => {
    const fetchCurrencyData = () => {
      try {
        const currencyMap = {};

        countries.forEach((country) => {
          if (country.currencies) {
            const currencyCode = Object.keys(country.currencies)[0];
            const currencyInfo = country.currencies[currencyCode];

            if (currencyInfo && currencyInfo.symbol) {
              currencyMap[currencyCode] = currencyInfo.symbol;
            }
          }
        });

        setCurrencyData(currencyMap);
      } catch (error) {
        console.error("Error processing currency data:", error);
      }
    };

    fetchCurrencyData();
  }, []);

  const getCurrencySymbol = (currencyCode) => {
    return currencyData[currencyCode] || currencyCode;
  };


  const fetchList = async (id) => {
    setIsLoading(true);
    try {
      const response = await RoomListApi(id, 1, 100);
      if (response.status === 200) {
        setProperties(response.data.data.rooms);
      } else {
        // Handle error response
        toast.error("Failed to fetch rooms");
      }
    } catch (error) {
      console.log("Error fetching rooms:", error);
      toast.error("Error fetching rooms");
    } finally{
      setIsLoading(false);
    }
  };


  return (
    <>
      <Loader open={isLoading} />
        <div className='max-w-[780px] mx-auto pb-5'>
          <div className="grid sm:grid-cols-[1.4fr_3fr] grid-cols-2 sm:gap-4 gap-3 font-inter">
            <div>
              <p className="font-bold text-black sm:text-base text-xs mb-5">Room Name :</p>
              <p className="font-bold text-black sm:text-base text-xs mb-5">Room Type :</p>
              <p className="font-bold text-black sm:text-base text-xs mb-5">Total Beds :</p>
              <p className="font-bold text-black sm:text-base text-xs mb-5">Ensuite :</p>
              <p className="font-bold text-black sm:text-base text-xs mb-5">Rate:</p>
              <p className="font-bold text-black sm:text-base text-xs mb-5">Currency :</p>
              <p className="font-bold text-black sm:text-base text-xs mb-5">Room Photo :</p>
            </div>
            <div>
              <p className="font-semibold text-[#00000080] sm:text-base text-xs mb-5">
                {selectedRoom.name || " "}
              </p>
              <p className="font-semibold text-[#00000080] sm:text-base text-xs mb-5">
                {addroom.type || " "}
              </p>
              <p className="font-semibold text-[#00000080] sm:text-base text-xs mb-5">
                {addroom.beds || " "}
              </p>
              <p className="font-semibold text-[#00000080] sm:text-base text-xs mb-5">
                {addroom.ensuite === "false" ? "No" :addroom.ensuite === "true" ? "Yes" : ""}
              </p>
              <p className="font-semibold text-[#00000080] sm:text-base text-xs mb-5">
              {getCurrencySymbol(addroom.currency)} {addroom.rate || " "}
              </p>
              <p className="font-semibold text-[#00000080] sm:text-base text-xs mb-5">
                {addroom.currency || " "}
              </p>
              <div className="flex gap-1.5">
                {addroom?.images?.map((image, index) => (
                  // eslint-disable-next-line react/jsx-key
                  <Image className="w-[54px] h-[36px] object-cover rounded-sm" src={image?.url} alt={`Existing Image ${index + 1}`} width={54} height={36}></Image>
                ))}
              </div>
            </div>
            <div>
              <p className="font-bold text-black sm:text-base text-xs mb-5">Weekday rate :</p>
              <p className="font-bold text-black sm:text-base text-xs mb-5">Weekend Rate :</p>
              <p className="font-bold text-black sm:text-base text-xs mb-5">Room Description :</p>
              <p className="font-bold text-black sm:text-base text-xs mb-5">Additional :</p>
            </div>
            <div>
              <p className="font-semibold text-[#00000080] sm:text-base text-xs mb-5">
              {getCurrencySymbol(addroom.currency)} {addroom.weekdayRate || " - "} 
              </p>
              <p className="font-semibold text-[#00000080] sm:text-base text-xs mb-5">
              {getCurrencySymbol(addroom.currency)} {addroom.weekendRate || " - "}
              </p>
              <p className="font-semibold text-[#00000080] sm:text-base text-xs mb-5">
                {addroom?.description || "  "}
              </p>
              <p className="font-semibold text-[#00000080] sm:text-base text-xs mb-5">
                {addroom?.bedAndBreakfast && "Bed and Breakfast"}{addroom?.nonRefundable && "Non Refundable"}{addroom?.freeCancellation && "Free Cancellation"}
              </p>
            </div>
          </div>
        </div>
    </>
  );
};

export default ViewRoom;
