import mongoose from 'mongoose';

const wishListSchema = new mongoose.Schema({
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
    },
    property: {
        type: mongoose.Types.ObjectId,
        ref: 'properties',
        required: true,
    },
    blog:{
        type: mongoose.Types.ObjectId,
        ref: 'blogs',
    }
    },
    {
        timestamps: true,
    },);

const wishlists = mongoose.model('wishlists', wishListSchema);

export default wishlists;

