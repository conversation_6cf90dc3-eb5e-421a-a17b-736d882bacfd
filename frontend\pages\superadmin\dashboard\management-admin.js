"use client";
import React, { useEffect, useRef, useState } from "react";
import { FaPlus, FaChevronDown, FaChevronUp } from "react-icons/fa6";
import {
  MdOutlineKeyboardArrowLeft,
  MdOutlineKeyboardArrowRight,
} from "react-icons/md";

const Dropdown = ({ options, selectedOption, onOptionSelect }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  


  const handleDropdownClick = () => {
    setIsOpen(!isOpen);
    
  };

  const handleOptionClick = (option) => {
    onOptionSelect(option);
    setIsOpen(false);
  };

  // Function to handle click outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative w-full" ref={dropdownRef}>
      <button
        onClick={handleDropdownClick}
        className=" bg-slate-50 border  border-gray-300 text-black/50 py-1.5 rounded w-[100%] lg:w-[90%] flex justify-center gap-x-2 items-center px-8 md:px-6 lg:px-0 dark:bg-transparent dark:text-[#757575]"
      >
        {selectedOption}
        {isOpen ? <FaChevronUp size={12} /> : <FaChevronDown size={12} />}
      </button>
      {isOpen && (
        <ul className="absolute z-10 bg-white border border-gray-300 rounded-md w-[90%] dark:bg-[#171616] dark:text-[#B6B6B6] dark:border-none">
          {options.map((option, index) => (
            <li
              key={index}
              className="py-1 px-2 hover:bg-gray-100 cursor-pointer dark:hover:bg-[#393939b7] text-sm "
              // onClick={() => onOptionSelect(option)}
              onClick={() => handleOptionClick(option)}
            >
              {option}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

const Admin = () => {
  const data = [
    "Content Management",
    "User Management",
    "Booking Management",
    "Communication Management",
    "Hostel Management",
    "Communication Management",
    "Notification Management",
    "Feedback & Support",
    "Payment Management",
  ];

  const dropdownOptions = ["Yes", "No", "Maybe"];

  const [selectedOptions, setSelectedOptions] = useState(
    Array(data.length).fill({
      view: "Yes",
      add: "Select",
      update: "Select",
      delete: "Select",
    })
  );

  // const handleOptionSelect = (index, key, option) => {
  //   const newSelectedOptions = [...selectedOptions];
  //   newSelectedOptions[index] = {
  //     ...newSelectedOptions[index],
  //     [key]: option,
  //   };
  //   setSelectedOptions(newSelectedOptions);
  // };

  const handleOptionSelect = (index, key, option) => {
    setSelectedOptions((prevOptions) => {
      const updatedOptions = [...prevOptions]; 
      updatedOptions[index] = {
        ...updatedOptions[index],
        [key]: option,  // Update the specific key ('delete' in this case)
      };
      return updatedOptions;
    });
  };

  return (
    <div className="lg:pl-[255px] md:pl-[255px] pl-[10px] pr-[10px] w-full float-end p-7 bg-sky-blue-20 overflow-y-auto scroll-smooth dark:bg-[#171616]">
      <div className="flex items-center justify-between w-full">
        <h2 className="text-2xl lg:text-3xl font-bold text-black font-poppins dark:text-gray-100">
          Admin
        </h2>
        <div className="w-[50%] gap-x-5 flex justify-end items-center">
          <button
            className="px-4 py-2 text-sm font-normal text-white rounded relative flex justify-center items-center bg-sky-blue-650"
            type="button"
          >
            <FaPlus size={18} className="mr-1" /> Admin
          </button>
        </div>
      </div>
      <div className="flex gap-x-2 my-4 mt-6">
        <button
          className="px-6 lg:px-12 py-2 text-sm font-semibold font-nunito text-white rounded relative flex justify-center items-center bg-sky-blue-650"
          type="button"
        >
          Admin
        </button>
        <button
          className="px-6 lg:px-12 py-2 text-sm font-semibold font-nunito text-black rounded relative flex justify-center items-center bg-transparent border dark:text-gray-200"
          type="button"
        >
          CEO
        </button>
        <button
          className="px-6 lg:px-12 py-2 text-sm font-semibold font-nunito text-black rounded relative flex justify-center items-center bg-transparent border dark:text-gray-200"
          type="button"
        >
          Yashi
        </button>
        <button
          className="px-6 lg:px-14 py-2 text-sm font-semibold font-nunito text-black rounded relative flex justify-center items-center bg-transparent border dark:text-gray-200"
          type="button"
        >
          HR
        </button>
      </div>

      {/* Permissions Table */}
      <div className="w-full bg-white rounded-lg shadow-md overflow-x-auto dark:bg-black">
        <table className="w-full">
          <thead>
            <tr className="text-left text-black/75 dark:text-[#B6B6B6]">
              <th className="pt-4 pb-8 pl-12 text-sm font-semibold font-poppins">
                MODULES
              </th>
              <th className="pt-4 pb-8 pl-12 md:pl-10 text-sm font-semibold font-poppins">
                VIEW
              </th>
              <th className="pt-4 pb-8 pl-16 md:pl-12 lg:pl-16 text-sm font-semibold font-poppins">
                ADD
              </th>
              <th className="pt-4 pb-8 pl-14 md:pl-10 lg:pl-12 text-sm font-semibold font-poppins">
                UPDATE
              </th>
              <th className="pt-4 pb-8 pl-14 md:pl-10 lg:pl-12 text-sm font-semibold font-poppins">
                DELETE
              </th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            {data.map((module, index) => (
              <tr key={index} className="border-t border-gray-300">
                <td className="whitespace-nowrap p-4 text-sm text-gray-500  font-medium font-poppins dark:text-[#757575]">
                  {module}
                </td>
                <td className=" p-4 text-sm font-medium font-poppins">
                  <Dropdown
                    options={dropdownOptions}
                    selectedOption={selectedOptions[index].view}
                    onOptionSelect={(option) =>
                      handleOptionSelect(index, "view", option)
                    }
                  />
                </td>
                <td className=" p-4 text-sm font-medium font-poppins">
                  <Dropdown
                    options={dropdownOptions}
                    selectedOption={selectedOptions[index].add}
                    onOptionSelect={(option) =>
                      handleOptionSelect(index, "add", option)
                    }
                  />
                </td>
                <td className=" p-4 text-sm font-medium font-poppins">
                  <Dropdown
                    options={dropdownOptions}
                    selectedOption={selectedOptions[index].update}
                    onOptionSelect={(option) =>
                      handleOptionSelect(index, "update", option)
                    }
                  />
                </td>
                <td className=" p-4 text-sm font-medium font-poppins ">
                  <Dropdown
                    options={dropdownOptions}
                    selectedOption={selectedOptions[index].delete}
                    onOptionSelect={(option) =>
                      handleOptionSelect(index, "delete", option)
                    }
                  />
                </td>
                <td className="p-4">
                  <button className="text-gray-500 bg-slate-50 border p-2 rounded-lg font-medium font-poppins dark:bg-transparent dark:text-[#757575]">
                    <FaChevronDown size={12} />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="flex justify-between items-center mt-5">
        <div className="text-black/75 text-sm font-medium font-poppins dark:text-[#B6B6B6]">
          Showing 1-09 of 78
        </div>
        <div className="inline-flex items-center justify-center border rounded-xl bg-white dark:bg-black dark:border-none">
          <a
            href="#"
            className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black/70 rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
          >
            <span className="sr-only">Next Page</span>
            <MdOutlineKeyboardArrowLeft />
          </a>

          <a
            href="#"
            className="inline-flex size-7 items-center justify-center  border border-gray-100  text-black rtl:rotate-180 dark:text-[#B6B6B6] dark:border-none"
          >
            <span className="sr-only">Next Page</span>
            <MdOutlineKeyboardArrowRight />
          </a>
        </div>
      </div>
    </div>
  );
};

export default Admin;
