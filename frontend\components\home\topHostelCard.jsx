import React, { useState } from "react";
import { Building2 } from "lucide-react";
import Image from "next/image";
import { IoStar } from "react-icons/io5";
import Link from "next/link";
import { FaWifi, FaMapMarkedAlt, FaBed, FaHeart } from "react-icons/fa";
import { <PERSON>a<PERSON><PERSON><PERSON><PERSON>, FaGlobe } from "react-icons/fa6";
import { FaCarAlt } from "react-icons/fa";
import { RxMix } from "react-icons/rx";
import { PiForkKnifeBold, PiMapPinLineFill, PiTowel } from "react-icons/pi";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/pagination";
import { Pagination } from "swiper/modules";
import { TbAirConditioning } from "react-icons/tb";
import { MdOutlineElevator, MdPool } from "react-icons/md";

const HostelCard = ({
  tag,
  title,
  images,
  feature,
  price,
  rating,
  review,
  hostelId,
  liked,
  onLikeUnlike,
}) => {
  // const [isLoading, setIsLoading] = useState(true);

  // // const handleImageLoad = () => {
  // //   setIsLoading(false);
  // // };

  // useEffect(() => {
  //   const timer = setTimeout(() => {
  //     setIsLoading(false);
  //   }, 300);

  //   return () => clearTimeout(timer);
  // }, []);
  const getImageUrl = (url) => {
    return url && url.startsWith("http") ? url : `https://${url}`;
  };

  const facilityIcons = {
    FREEWIFI: <FaWifi className="text-gray-800 text-xl h-[16px] w-[16px]" />,
    FREECITYMAPS: (
      <FaMapMarkedAlt className="text-gray-800 text-xl h-[16px] w-[16px]" />
    ),
    LINENINCLUDED: (
      <FaBed className="text-gray-800 text-xl h-[16px] w-[16px]" />
    ),
    FREEINTERNETACCESS: (
      <FaGlobe className="text-gray-800 text-xl h-[16px] w-[16px]" />
    ),
    BREAKFASTINCLUDED: (
      <PiForkKnifeBold className="text-gray-800 text-xl h-[16px] w-[16px]" />
    ),
    FREEPARKING: (
      <FaCarAlt className="text-gray-800 text-xl h-[16px] w-[16px]" />
    ),
    TOWELSINCLUDED: (
      <PiTowel className="text-gray-800 text-xl h-[16px] w-[16px]" />
    ),
    FREECITYTOUR: (
      <Building2 className="text-gray-800 text-xl h-[16px] w-[16px]" />
    ),
    PARKING: <FaCarAlt className="text-gray-800 text-xl h-[16px] w-[16px]" />,
    AIRCONDITIONING: <TbAirConditioning className="text-gray-800 text-xl  " />,
    SWIMMINGPOOL: <MdPool className="text-gray-800 text-xl " />,
    ELEVATOR: <MdOutlineElevator className="text-gray-800 text-xl " />,
    FITNESSCENTRE: <FaDumbbell className="text-gray-800 text-xl " />,
  };

  console.log("images", images);

  return (
    <>
      <div className="rounded-3xl w-full border border-slate-105 font-manrope overflow-hidden">
        {/* {isLoading ? (
          // Full skeleton loading state
          <>
            <div className="w-full h-[200px] bg-gray-200 animate-pulse rounded-t-3xl"></div>
            <div className="p-4 space-y-2">
              <div className="h-5 w-1/4 bg-slate-200 animate-pulse rounded"></div>
              <div className="h-7 w-3/4 bg-slate-200 animate-pulse rounded mt-3"></div>
              <div className="h-4 w-3/5 bg-slate-200 animate-pulse rounded"></div>
              <div className="flex gap-2 mt-3">
                {[...Array(5)].map((_, i) => (
                  <div
                    key={i}
                    className="h-6 w-6 bg-slate-200 animate-pulse rounded-full"
                  ></div>
                ))}
              </div>
              <div className="flex justify-between items-center mt-4">
                <div className="h-8 w-1/3 bg-slate-200 animate-pulse rounded"></div>
                <div className="h-8 w-1/3 bg-slate-200 animate-pulse rounded-full"></div>
              </div>
            </div>
          </>
        ) : ( */}

        <>
          <div className="relative w-full rounded-t-3xl tracking-normal">
            <Link href={`/hostels-detail/${hostelId}`} prefetch={false}>
              <div className="w-full h-[200px] object-cover">
                {/* <Swiper
                    spaceBetween={0}
                    slidesPerView={1}
                    navigation={false}
                    autoplay={false}
                    loop={false}
                    pagination={{
                      dynamicBullets: true,
                      clickable: true,
                    }}
                    modules={[Pagination]}
                    className="mySwiper myCustomSwiper m-0 w-full"
                  >
                    
                    {images && images.length > 0 ? (
                      
                      images.map((img, index) => (
                        
                        <SwiperSlide key={index}>
                          <div className="bg-gray-200">
                            <Image
                              src={getImageUrl(img?.objectUrl)}
                              alt={title}
                              title={title}
                              className="w-full h-[200px] object-cover"
                              width={1920}
                              height={700}
                              priority={index === 0} // Only prioritize first image
                              loading={index === 0 ? undefined : "lazy"}
                            />
                          </div>
                        </SwiperSlide>
                      ))
                    ) : (
                      <SwiperSlide>
                        <div className="w-full h-[200px] bg-gray-200 rounded-t-2xl flex items-center justify-center">
                          <h1 className="text-white font-bold font-manrope text-2xl">
                            {title}
                          </h1>
                        </div>
                      </SwiperSlide>
                    )}
                  </Swiper> */}
                <Swiper
                  spaceBetween={0}
                  slidesPerView={1}
                  navigation={false}
                  autoplay={false}
                  loop={false}
                  pagination={{
                    dynamicBullets: true,
                    clickable: true,
                  }}
                  modules={[Pagination]}
                  className="mySwiper myCustomSwiper m-0 w-full"
                >
                  {images && images.length > 0 ? (
                    images.map((img, index) => {
                      const [isLoaded, setIsLoaded] = useState(false);
                      const [imageFailed, setImageFailed] = useState(false);

                      return (
                        <SwiperSlide key={index}>
                          <div className="relative bg-gray-200 h-[200px]">
                           
                            {(!isLoaded || imageFailed) && (
                              <div className="absolute inset-0 w-full h-full bg-gray-300 animate-pulse z-0 flex items-center justify-center">
                                <h1 className="text-white font-bold font-manrope text-4xl">Mixdorm</h1>
                              </div>
                            )}

                            {/* Image */}
                            <Image
                              src={getImageUrl(img?.objectUrl)}
                              alt={title}
                              title={title}
                              className={`w-full h-full object-cover transition-opacity duration-300 ${
                                isLoaded && !imageFailed ? "opacity-100" : "opacity-0"
                              }`}
                              width={1920}
                              height={700}
                              priority={index === 0}
                              loading={index === 0 ? "eager" : "lazy"}
                              onLoadingComplete={() => setIsLoaded(true)}
                              onError={() => {
                                setIsLoaded(true);
                                setImageFailed(true);
                              }}
                            />
                          </div>
                        </SwiperSlide>
                      );
                    })
                  ) : (
                    <SwiperSlide>
                      <div className="w-full h-[200px] bg-gray-200 rounded-t-2xl flex items-center justify-center">
                        <h1 className="text-white font-bold font-manrope text-2xl">
                          Mixdorm
                        </h1>
                      </div>
                    </SwiperSlide>
                  )}
                </Swiper>
              </div>
            </Link>

            <div className="absolute flex items-center justify-center px-3 py-1 text-xs font-semibold text-black bg-white rounded-4xl top-5 left-5 font-manrope z-10">
              {tag}
            </div>

            <button
              type="button"
              onClick={() => onLikeUnlike && onLikeUnlike(hostelId, liked)}
              aria-label={liked ? "Unlike" : "Like"}
              className={`absolute flex items-center justify-center rounded-full w-7 h-7 top-5 right-5 font-manrope z-10 ${
                liked ? "text-red-600 bg-white" : "text-black bg-white"
              } hover:bg-white-600 hover:text-red-600`}
            >
              <FaHeart size={16} />
            </button>

            <div className="flex items-center text-sm gap-1 font-manrope absolute left-7 bottom-[-10px] bg-white rounded-4xl shadow-lg py-1 px-2 z-10">
              <IoStar className="text-yellow-550" />
              <span className="text-black font-semibold">{rating}</span>
              <span className="text-xs text-[#737373]">({review} reviews)</span>
            </div>
          </div>
          <div className="xl:p-4 px-4 pb-4 pt-4 rounded-b-3xl tracking-normal h-[170px]">
            <div className="h-[100px]">
              <div className="min-h-[80px]">
                {" "}
                <h2 className=" leading-4 mt-1  ">
                  <Link
                    href={`/hostels-detail/${hostelId}`}
                    className=" sm:text-[18px] text-[16px]  font-manrope leading-6 font-bold cursor-pointer text-black duration-300 ease-in-out hover:text-[#40E0D0] line-clamp-2"
                    prefetch={false}
                  >
                    {title}
                  </Link>
                </h2>
                <p className="text-[#888888] text-xs sm:text-sm flex items-center font-manrope cursor-pointer hover:text-primary-blue mt-2">
                  <PiMapPinLineFill className="text-primary-blue mr-1.5" />2 KM
                  away from city center
                </p>
              </div>
              <div className="flex flex-wrap gap-3 font-manrope text-[#737373]">
                {/* <div className="flex items-center text-sm gap-1">
              <CalendarDays size={16} />
              <span>{guest}</span>
            </div>
            <div className="flex items-center text-sm gap-1">
              <Clock size={16} />
              <span>{time} min</span>
            </div> */}
            {feature?.length > 0 && (
              <p className="flex items-center text-sm gap-x-2 ">
                <span className="text-[14px]  font-manrope font-medium cursor-pointer text-gray duration-300 ease-in-out">
                  Key Features:{" "}
                </span>
                <div className="flex items-center justify-start gap-x-1">
                    {feature?.slice(0, 5).map((facility, index) => {
                      const isLastItem = index === feature.length - 1;
                      return (
                        <div
                          key={facility.id}
                          className="relative group w-6 h-6 flex items-center justify-center"
                          // title={facility.name} // For simple tooltips
                        >
                          {facilityIcons[facility.id] || (
                            <RxMix className="text-lg text-gray-800" />
                          )}{" "}
                          {/* Default Icon */}
                          <span
                            className={`absolute bottom-6 ${
                              isLastItem ? "left-[-64px]" : "left-[-100%]"
                            } transform text-xs text-white bg-black px-2 py-1 rounded opacity-0 min-w-[max-content] max-w-[max-content] group-hover:opacity-100 duration-200`}
                          >
                            {facility.name}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </p>
            )}
              </div>
            </div>

            {/* <Link
            href=""
            className="my-3 md:text-xl text-base font-bold  line-clamp-2 "
          >
            {description}
          </Link> */}
            <div className="sm:block items-center justify-between xl:mt-3 mt-3">
              <div className="flex items-center font-bold text-black text-lg gap-1 font-manrope">
                <Link
                  href={`/hostels-detail/${hostelId}`}
                  prefetch={false}
                  className="hover:text-[#40E0D0] group"
                >
                  {price}
                  <span className="text-xs text-[#737373] font-normal ml-1 group-hover:text-[#40E0D0]">
                    / Per Night
                  </span>
                </Link>

                <div className="ml-auto">
                  <Link
                    href={`/hostels-detail/${hostelId}`}
                    prefetch={false}
                    className="bg-black flex items-center hover:bg-[#40E0D0] text-white font-semibold rounded-3xl px-4  font-manrope  h-[30px] text-[10px] leading-[16.5px] "
                  >
                    Book Now
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </>
      </div>
    </>
  );
};

export default HostelCard;
