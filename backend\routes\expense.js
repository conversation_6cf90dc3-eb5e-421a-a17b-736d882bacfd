import express from "express";
import { addExpense,editExpense,deleteExpense } from '../controller/expense.js';

const router = express.Router();

router.post('/addExpenses', addExpense); 
router.put('/:expenseId/editExpenses',editExpense);
router.delete('/:expenseId/removeExpenses',deleteExpense);

export default router;

/**
 * @swagger
 * tags:
 *   name: Expenses
 *   description: API for managing Expenses
 */

/**
 * @swagger
 * /expenses/addExpenses:
 *   post:
 *     summary: Add a new expense
 *     description: Creates a new expense in a group and updates the group balances accordingly.
 *     tags: [Expenses]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - description
 *               - amount
 *               - category
 *               - groupId
 *               - paidBy
 *               - participants
 *             properties:
 *               description:
 *                 type: string
 *                 description: Description of the expense.
 *                 example: Dinner at a restaurant
 *               amount:
 *                 type: number
 *                 description: Total amount of the expense.
 *                 example: 100
 *               category:
 *                 type: string
 *                 description: Category of the expense.
 *                 example: Food & Dining
 *               groupId:
 *                 type: string
 *                 description: ID of the group where the expense is added.
 *                 example: 64c75d7e8f1b2c001f4e95e5
 *               paidBy:
 *                 type: string
 *                 description: ID of the user who paid for the expense.
 *                 example: 64c75d7e8f1b2c001f4e95e6
 *               participants:
 *                 type: array
 *                 description: List of participants in the expense.
 *                 items:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                       description: ID of the participant.
 *                       example: 64c75d7e8f1b2c001f4e95e7
 *     responses:
 *       201:
 *         description: Expense added successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     expense:
 *                       type: object
 *                       description: The created expense details.
 *                 message:
 *                   type: string
 *                   example: Expense added successfully.
 *       400:
 *         description: Invalid request parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: All fields are required, including participants as an array.
 *       404:
 *         description: Group or participant not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Group not found.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Internal server error.
 */



/**
 * @swagger
 * /expenses/{expenseId}/editExpenses:
 *   put:
 *     summary: Update an existing expense
 *     description: Updates details of an existing expense such as description, amount, category, or participants.
 *     tags: [Expenses]
 *     parameters:
 *       - name: expenseId
 *         in: path
 *         required: true
 *         description: The ID of the expense to update.
 *         schema:
 *           type: string
 *           example: 64c75d7e8f1b2c001f4e95e5
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - description
 *               - amount
 *             properties:
 *               description:
 *                 type: string
 *                 description: Updated description of the expense.
 *                 example: Updated travel expenses
 *               amount:
 *                 type: number
 *                 description: Updated amount of the expense.
 *                 example: 250
 *               category:
 *                 type: string
 *                 description: Updated category of the expense.
 *                 example: Travel
 *               participants:
 *                 type: array
 *                 description: List of participants in the expense.
 *                 items:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                       description: The ID of a participant.
 *                       example: 64c75d7e8f1b2c001f4e95e6
 *     responses:
 *       200:
 *         description: Expense updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     expense:
 *                       type: object
 *                       description: The updated expense details.
 *                 message:
 *                   type: string
 *                   example: Expense updated successfully.
 *       400:
 *         description: Invalid request parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Invalid request payload.
 *       404:
 *         description: Expense not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Expense not found.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Internal server error.
 */

/**
 * @swagger
 * /expenses/{expenseId}/removeExpenses:
 *   delete:
 *     summary: Delete an expense
 *     description: Removes an expense and updates the group balances accordingly.
 *     tags: [Expenses]
 *     parameters:
 *       - name: expenseId
 *         in: path
 *         required: true
 *         description: The ID of the expense to delete.
 *         schema:
 *           type: string
 *           example: 64c75d7e8f1b2c001f4e95e5
 *     responses:
 *       200:
 *         description: Expense deleted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Expense deleted successfully.
 *       404:
 *         description: Expense not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Expense not found.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: Internal server error.
 */

