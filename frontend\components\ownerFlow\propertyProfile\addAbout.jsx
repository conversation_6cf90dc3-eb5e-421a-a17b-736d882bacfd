import { editPropertyApi } from "@/services/ownerflowServices";
import { useState } from "react";
import toast from "react-hot-toast";

const AddAbout = ({
  closeaboutModal,
  aboutUsData,
  updatePropertyData,
  setLoading,
  id,
}) => {
  const [aboutUsDataAdd, setAboutUsDataAdd] = useState(aboutUsData || "");

  const handleSubmit = async () => {
    setLoading(true);
    try {
      const payload = {
        aboutUs: aboutUsDataAdd,
      };
      const res = await editPropertyApi(id, payload);
      if (res?.status === 200) {
        toast.success(res?.data?.message);
        updatePropertyData();
        closeaboutModal();
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    setAboutUsDataAdd(e.target.value);
  };

  return (
    <>
      <div>
        <label className='block text-black sm:text-sm text-xs font-medium mb-1.5'>
          Add About Us
        </label>
        <textarea
          name='description'
          className='w-full sm:px-4 px-2 sm:py-2.5 py-2 border border-black/50 rounded-lg focus:outline-none focus:ring-1 focus:ring-teal-500 text-black sm:text-sm text-xs placeholder:text-gray-500'
          placeholder='Enter About Us'
          rows={4}
          value={aboutUsDataAdd}
          onChange={handleInputChange}
          style={{ outline: "none" }}
        ></textarea>
        <div className='xs:flex block  items-center justify-between w-full sm:my-14 my-7 gap-4 col-span-2 py-4 bg-white/80 sticky bottom-0 backdrop-blur-sm'>
          <button
            type='button'
            className='hover:bg-black bg-transparent hover:text-white text-black border-2 font-medium py-2 px-4 border-black rounded-lg w-full text-sm xs:mb-0 mb-2'
            onClick={closeaboutModal}
          >
            Cancel
          </button>
          <button
            type='submit'
            className='bg-black hover:bg-transparent text-white hover:text-black border-2 font-medium py-2 px-4  border-black rounded-lg w-full text-sm'
            onClick={handleSubmit}
          >
            Add
          </button>
        </div>
      </div>
    </>
  );
};

export default AddAbout;
